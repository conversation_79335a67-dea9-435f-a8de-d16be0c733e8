# 🖥️ Mac YouTube Downloader - Usage Guide

## 🚀 Quick Start

### Step 1: Get Your YouTube API Key
1. Go to [Google Cloud Console](https://console.developers.google.com/)
2. Create a new project or select existing
3. Enable **YouTube Data API v3**
4. Create credentials → **API Key**
5. Copy your API key (starts with `<PERSON>za...`)

### Step 2: Set Up Environment
```bash
# Set your API key (replace with your actual key)
export YOUTUBE_API_KEY="AIzaSyBcNcRMc263FHCRvP-ZgzZIreCk7n6ooxw"

# Verify it's set
echo $YOUTUBE_API_KEY
```

### Step 3: Run the Downloader
```bash
# Navigate to the project directory
cd /Users/<USER>/Documents/maxed_out_life

# Download a channel (replace with actual channel URL)
dart simple_mac_youtube_downloader.dart https://www.youtube.com/@channelname
```

## 📋 Supported URL Formats

The downloader supports all YouTube channel URL formats:

```bash
# @username format
dart simple_mac_youtube_downloader.dart https://www.youtube.com/@channelname

# /c/ format  
dart simple_mac_youtube_downloader.dart https://www.youtube.com/c/channelname

# /channel/ format
dart simple_mac_youtube_downloader.dart https://www.youtube.com/channel/UCxxxxx
```

## 📊 What You'll Get

The downloader creates a comprehensive text file with:

- **Channel Information**: Name, ID, total videos
- **Video Metadata**: Titles, descriptions, publish dates, URLs
- **Organized Format**: Easy to read and process
- **Timestamp**: When the download was performed

Example output file: `youtube_ChannelName_1703123456789.txt`

## 📁 File Location

Downloaded files are automatically saved to your **Downloads** folder:
```
/Users/<USER>/Downloads/youtube_ChannelName_[timestamp].txt
```

## 🔧 Troubleshooting

### "API key not found" Error
```bash
# Make sure you've set the environment variable
export YOUTUBE_API_KEY="your_actual_api_key_here"

# Check if it's set correctly
echo $YOUTUBE_API_KEY
```

### "Could not extract channel ID" Error
- Verify the URL is correct and complete
- Try a different URL format for the same channel
- Make sure the channel exists and is public

### "No videos found" Error
- Check if the channel has public videos
- Verify the channel URL is correct
- Some channels may have all videos set to private

### Network/Connection Errors
- Check your internet connection
- Verify YouTube API is accessible
- Try again after a few minutes

## 💡 Tips for Best Results

### API Quota Management
- **Free tier**: 10,000 units per day
- **Small channels** (100 videos): ~200 units
- **Large channels** (1000+ videos): 1000+ units
- Monitor your usage to avoid hitting limits

### Optimal Usage
- **Download during off-peak hours** for better performance
- **Start with smaller channels** to test the system
- **Use specific channel URLs** when possible
- **Avoid downloading the same channel repeatedly**

## 🎯 Integration with MXD App

After downloading:

1. **Copy the content** from the generated text file
2. **Use it to enhance your AI coaches** in the MXD app
3. **The coaches will automatically** integrate this knowledge
4. **No citations needed** - coaches present it as their expertise

## 📈 Example Workflow

```bash
# 1. Set up API key (one time setup)
export YOUTUBE_API_KEY="your_key_here"

# 2. Download a channel
dart simple_mac_youtube_downloader.dart https://www.youtube.com/@examplechannel

# 3. Check the output
ls -la ~/Downloads/youtube_*

# 4. View the content
cat ~/Downloads/youtube_ExampleChannel_*.txt
```

## 🛡️ Privacy & Security

- **All processing is local** - no data leaves your machine
- **API key stays on your Mac** - never transmitted except to YouTube
- **No tracking or analytics** - completely private
- **Files saved locally** - you control all data

## 🆘 Getting Help

If you encounter issues:

1. **Check the error message** for specific guidance
2. **Verify your API key** is set correctly
3. **Test with a small, known channel** first
4. **Check your internet connection**
5. **Ensure YouTube Data API v3 is enabled** in Google Cloud Console

## 🎉 Success Indicators

You'll know it's working when you see:
```
🛡️ SIMPLE YouTube Transcript Downloader - Mac Version
============================================================
🎯 Target Channel: https://www.youtube.com/@channelname

✅ API key found
🔍 Extracting channel ID...
✅ Channel ID: UCxxxxx
📺 Getting channel information...
✅ Channel: Channel Name
📝 Collecting video list...
✅ Found 150 videos
📊 Processing videos...
📝 Processing video 150/150...
✅ Processed 150 videos successfully

✅ SUCCESS! Channel downloaded successfully
📁 File saved: /Users/<USER>/Downloads/youtube_ChannelName_1703123456789.txt
📊 Videos processed: 150
```

---

**🛡️ Your YouTube knowledge expansion system is ready!**
