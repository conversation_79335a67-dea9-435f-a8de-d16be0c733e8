# Maxed Out Life Design System

A comprehensive design system providing consistent styling, components, and patterns across the entire Maxed Out Life application.

## 🎨 Overview

The Maxed Out Life Design System ensures visual consistency, improves development efficiency, and maintains the app's signature neon cyberpunk aesthetic while providing excellent user experience.

### Key Features

- **🎯 Consistent Design Tokens** - Standardized spacing, typography, colors, and animations
- **🧩 Reusable Components** - Pre-built UI components with multiple variants
- **🌈 Neon Aesthetic** - Special support for glow effects and cyberpunk styling
- **♿ Accessibility** - WCAG compliant components with proper touch targets
- **📱 Responsive** - Adaptive sizing for different screen sizes
- **⚡ Performance** - Optimized animations and efficient rendering

## 📐 Design Tokens

### Spacing Scale
```dart
DesignTokens.spaceXs   // 2px  - Tight layouts
DesignTokens.spaceSm   // 4px  - Minimal gaps
DesignTokens.spaceMd   // 8px  - Standard small gap
DesignTokens.spaceLg   // 12px - Standard medium gap
DesignTokens.spaceXl   // 16px - Standard large gap
DesignTokens.space2xl  // 24px - Section spacing
DesignTokens.space3xl  // 32px - Major section spacing
DesignTokens.space4xl  // 48px - Screen padding
DesignTokens.space5xl  // 64px - Large screen sections
```

### Border Radius Scale
```dart
DesignTokens.radiusNone  // 0px   - No radius
DesignTokens.radiusSm    // 4px   - Small elements
DesignTokens.radiusMd    // 8px   - Standard radius
DesignTokens.radiusLg    // 12px  - Cards and containers
DesignTokens.radiusXl    // 16px  - Major containers
DesignTokens.radius2xl   // 24px  - Buttons and prominent elements
DesignTokens.radius3xl   // 32px  - Special containers
DesignTokens.radiusFull  // 9999px - Pills and circular elements
```

### Animation Durations
```dart
DesignTokens.durationInstant  // 0ms    - Instant
DesignTokens.durationFast     // 150ms  - Quick transitions
DesignTokens.durationNormal   // 300ms  - Standard transitions
DesignTokens.durationSlow     // 500ms  - Deliberate transitions
DesignTokens.durationXSlow    // 800ms  - Dramatic effects
DesignTokens.durationLoading  // 1200ms - Loading states
```

## 🔤 Typography System

### Font Families
- **Pirulen** - Primary font for headings and important text
- **BITSUMISHI** - Secondary font for body text and UI elements
- **Digital-7** - Monospace font for code and technical content
- **Orbitron** - Display font for special occasions

### Text Styles
```dart
// Headings
AppTypography.headingXLarge  // 36px - Hero sections
AppTypography.headingLarge   // 32px - Page titles
AppTypography.headingMedium  // 28px - Section titles
AppTypography.headingSmall   // 24px - Subsection titles
AppTypography.headingXSmall  // 20px - Card titles

// Body Text
AppTypography.bodyLarge   // 18px - Important content
AppTypography.bodyMedium  // 16px - Standard body text
AppTypography.bodySmall   // 14px - Secondary content
AppTypography.bodyXSmall  // 12px - Tertiary content

// Labels & Buttons
AppTypography.labelLarge    // 16px - Prominent labels
AppTypography.labelMedium   // 14px - Standard labels
AppTypography.labelSmall    // 12px - Secondary labels
AppTypography.buttonLarge   // 18px - Large buttons
AppTypography.buttonMedium  // 16px - Standard buttons
AppTypography.buttonSmall   // 14px - Small buttons
```

### Typography Utilities
```dart
// Apply color
AppTypography.withColor(AppTypography.headingLarge, MolColors.cyan)

// Apply glow effect
AppTypography.withGlow(AppTypography.bodyMedium, MolColors.cyan)

// Apply shadow
AppTypography.withShadow(AppTypography.labelLarge, Colors.black)
```

## 🎨 Color System

### Primary Colors
```dart
MolColors.black   // #0D0D0D - Primary background
MolColors.cyan    // #00BCD4 - Primary accent
MolColors.blue    // #00BFFF - Secondary accent
```

### Category Colors
```dart
MolColors.blue    // Health category
MolColors.green   // Wealth category  
MolColors.purple  // Purpose category
MolColors.yellow  // Connection category
```

### Extended Palette
```dart
MolColors.red     // #FF1744 - Destructive actions
MolColors.orange  // #FFA500 - Warnings
MolColors.pink    // #E91E63 - Custom categories
MolColors.lime    // #8BC34A - Success states
MolColors.magenta // #EA00FF - Special effects
```

## 🧩 Components

### AppButton

Comprehensive button component with multiple variants and sizes.

```dart
// Primary button
AppButton.primary(
  text: 'Get Started',
  onPressed: () => print('Pressed'),
)

// Secondary button with icon
AppButton.secondary(
  text: 'Cancel',
  icon: Icons.close,
  size: AppButtonSize.small,
  onPressed: () => Navigator.pop(context),
)

// Loading state
AppButton.primary(
  text: 'Saving...',
  isLoading: true,
  onPressed: null,
)

// Full width button
AppButton.primary(
  text: 'Continue',
  fullWidth: true,
  onPressed: () => print('Continue'),
)
```

#### Button Variants
- **Primary** - Filled background, high emphasis
- **Secondary** - Outlined style, medium emphasis  
- **Tertiary** - Text only, low emphasis
- **Destructive** - Red styling for dangerous actions

#### Button Sizes
- **Small** - 32px height, compact spacing
- **Medium** - 40px height, standard spacing
- **Large** - 48px height, generous spacing

### AppCard

Flexible card component with glow effects and sections.

```dart
// Standard card
AppCard(
  child: Text('Card content'),
)

// Elevated card with glow
AppCard.elevated(
  glowColor: MolColors.cyan,
  header: Text('Header'),
  child: Text('Content'),
  footer: AppButton.primary(text: 'Action', onPressed: () {}),
)

// Outlined card
AppCard.outlined(
  glowColor: MolColors.purple,
  child: Column(
    children: [
      Text('Title'),
      Text('Description'),
    ],
  ),
)

// Glow card with intense neon effect
AppCard.glow(
  glowColor: MolColors.cyan,
  child: Text('Neon content'),
)
```

#### Card Variants
- **Standard** - Subtle background, minimal styling
- **Elevated** - Shadow effects, optional glow
- **Outlined** - Border styling, optional glow
- **Glow** - Intense neon effects, cyberpunk aesthetic

### Specialized Cards

#### CategoryCard
```dart
CategoryCard(
  category: 'Health',
  customCategories: user.customCategories,
  child: Text('Health-related content'),
  onTap: () => print('Tapped'),
)
```

#### BountyCard
```dart
BountyCard(
  title: 'Complete 5 Workouts',
  description: 'Finish 5 workout sessions this week',
  isEpic: true,
  isCompleted: false,
  onTap: () => print('Bounty tapped'),
)
```

## 🎭 Design Patterns

### Glow Effects

Create consistent neon glow effects:

```dart
// Using helper function
BoxDecoration(
  boxShadow: DesignSystemHelpers.createGlow(MolColors.cyan),
)

// Custom intensity
BoxDecoration(
  boxShadow: DesignSystemHelpers.createGlow(
    MolColors.cyan, 
    intensity: 1.5,
  ),
)
```

### Elevation

Standard elevation shadows:

```dart
BoxDecoration(
  boxShadow: DesignSystemHelpers.createElevation(4.0),
)
```

### Responsive Sizing

Adaptive sizing for different screen sizes:

```dart
final responsiveSize = DesignSystemHelpers.responsiveSize(
  16.0, // base size
  MediaQuery.of(context).size.width,
);
```

## ♿ Accessibility

### Touch Targets
All interactive elements meet minimum 44px touch target requirements.

### Color Contrast
Use the validator to ensure WCAG compliance:

```dart
final hasValidContrast = DesignSystemValidator.hasValidContrast(
  Colors.white, // foreground
  Colors.black, // background
);
```

### Screen Reader Support
Components include proper semantic markup and labels.

## 🚀 Performance

### Optimized Animations
- Consistent 60fps performance
- Hardware acceleration where possible
- Appropriate duration ranges (100ms - 1000ms)

### Efficient Rendering
- Minimal widget rebuilds
- Optimized shadow and glow effects
- Cached decoration objects

## 📱 Usage Examples

### Complete Screen Example
```dart
class ExampleScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: Padding(
          padding: EdgeInsets.all(DesignTokens.space2xl),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Page title
              Text(
                'Welcome Back',
                style: AppTypography.withGlow(
                  AppTypography.headingLarge,
                  MolColors.cyan,
                ),
              ),
              
              SizedBox(height: DesignTokens.space2xl),
              
              // Category card
              CategoryCard(
                category: 'Health',
                child: Column(
                  children: [
                    Text(
                      'Today\'s Progress',
                      style: AppTypography.headingSmall,
                    ),
                    SizedBox(height: DesignTokens.spaceLg),
                    Text(
                      '3 of 5 habits completed',
                      style: AppTypography.bodyMedium,
                    ),
                  ],
                ),
              ),
              
              SizedBox(height: DesignTokens.space2xl),
              
              // Action buttons
              AppButton.primary(
                text: 'Continue Journey',
                icon: Icons.arrow_forward,
                fullWidth: true,
                onPressed: () => print('Continue'),
              ),
              
              SizedBox(height: DesignTokens.spaceLg),
              
              AppButton.secondary(
                text: 'View Stats',
                fullWidth: true,
                onPressed: () => print('Stats'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
```

## 🔧 Migration Guide

### From Old Components

Replace existing buttons:
```dart
// Old
ElevatedButton(
  style: ElevatedButton.styleFrom(backgroundColor: Colors.blue),
  child: Text('Button'),
  onPressed: () {},
)

// New
AppButton.primary(
  text: 'Button',
  onPressed: () {},
)
```

Replace existing cards:
```dart
// Old
Container(
  decoration: BoxDecoration(
    color: Colors.grey[900],
    borderRadius: BorderRadius.circular(16),
  ),
  child: Text('Content'),
)

// New
AppCard(
  child: Text('Content'),
)
```

## 📚 Resources

- [Design Tokens Reference](lib/design_system/design_tokens.dart)
- [Typography Guide](lib/design_system/typography.dart)
- [Component Library](lib/design_system/components/)
- [Theme Integration](lib/theme/theme_provider.dart)

---

**Design System Version:** 1.0.0  
**Last Updated:** 2025-01-20  
**Maintainer:** Maxed Out Life Development Team
