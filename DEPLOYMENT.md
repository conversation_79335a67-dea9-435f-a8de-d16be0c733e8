# Maxed Out Life Deployment Guide

Comprehensive deployment documentation for the Maxed Out Life application.

## 🚀 Deployment Overview

The Maxed Out Life app supports multiple deployment strategies across various platforms and environments. Our CI/CD pipeline automates building, testing, and deployment processes to ensure reliable and consistent releases.

### Supported Platforms

- **📱 Mobile**: Android (APK/AAB), iOS (App Store/TestFlight)
- **🌐 Web**: Progressive Web App (PWA) with offline support
- **💻 Desktop**: Windows, macOS, Linux native applications
- **🐳 Container**: Docker-based deployment for web hosting

### Deployment Environments

- **Development**: Continuous deployment from develop branch
- **Staging**: Manual deployment for pre-production testing
- **Production**: Approved releases with full quality gates

## 🏗️ CI/CD Pipeline

### GitHub Actions Workflows

#### Continuous Integration (`.github/workflows/ci.yml`)

Triggered on every push and pull request:

```yaml
Triggers:
  - Push to main/develop branches
  - Pull requests to main/develop
  - Scheduled nightly builds (2 AM UTC)

Stages:
  1. Code Analysis (10 min)
  2. Testing (30 min, parallel)
  3. Security Scan (15 min)
  4. Multi-platform Build (45 min, parallel)
  5. Quality Gate Validation
```

**Quality Gates**:
- ✅ Code analysis passes (dart analyze, formatting)
- ✅ All tests pass (unit, widget, integration)
- ✅ Test coverage ≥ 80%
- ✅ Security scan passes
- ✅ All platform builds succeed

#### Release Pipeline (`.github/workflows/release.yml`)

Triggered on version tags or manual dispatch:

```yaml
Triggers:
  - Git tags matching v*.*.*
  - Manual workflow dispatch

Stages:
  1. Prepare Release
  2. Quality Assurance
  3. Multi-platform Builds
  4. GitHub Release Creation
  5. Deployment to Environments
```

### Build Matrix

| Platform | OS | Build Command | Artifacts |
|----------|----|--------------|-----------| 
| **Android** | Ubuntu | `flutter build apk/appbundle` | APK, AAB |
| **iOS** | macOS | `flutter build ios` | IPA (with signing) |
| **Web** | Ubuntu | `flutter build web` | Static files |
| **Windows** | Windows | `flutter build windows` | EXE, MSI |
| **macOS** | macOS | `flutter build macos` | APP, DMG |
| **Linux** | Ubuntu | `flutter build linux` | AppImage, DEB |

## 🔧 Local Development Setup

### Prerequisites

```bash
# Install Flutter
git clone https://github.com/flutter/flutter.git -b stable
export PATH="$PATH:`pwd`/flutter/bin"

# Verify installation
flutter doctor

# Get dependencies
flutter pub get
```

### Build Scripts

#### Unix/Linux/macOS
```bash
# Make script executable
chmod +x scripts/build.sh

# Build for specific platform
./scripts/build.sh --platform android --type release

# Build for all platforms
./scripts/build.sh --platform all --clean

# Build with custom version
./scripts/build.sh --platform web --version 1.0.0 --build-number 100
```

#### Windows
```powershell
# Build for specific platform
.\scripts\build.ps1 -Platform android -BuildType release

# Build for all platforms
.\scripts\build.ps1 -Platform all -Clean

# Build with custom version
.\scripts\build.ps1 -Platform web -Version "1.0.0" -BuildNumber "100"
```

### Docker Development

```bash
# Start development environment
docker-compose --profile development up

# Run tests
docker-compose --profile testing up app-test

# Build production web app
docker-compose --profile production up app-web

# Multi-platform build
docker-compose --profile build up app-builder
```

## 📱 Platform-Specific Deployment

### Android Deployment

#### Google Play Store

1. **Prepare Release**:
   ```bash
   # Build signed App Bundle
   flutter build appbundle --release
   ```

2. **Upload to Play Console**:
   - Navigate to Google Play Console
   - Upload AAB file to Internal Testing
   - Promote through testing tracks (Alpha → Beta → Production)

3. **Automated Deployment** (Future):
   ```yaml
   # Configure in deployment/config.yml
   android:
     play_store:
       enabled: true
       track: "internal"
       package_name: "com.guardiantape.maxedoutlife"
   ```

#### Direct APK Distribution

```bash
# Build release APK
flutter build apk --release --split-per-abi

# Artifacts available in:
# build/app/outputs/flutter-apk/
```

### iOS Deployment

#### App Store Connect

1. **Prepare Release**:
   ```bash
   # Build iOS archive
   flutter build ios --release
   ```

2. **Code Signing**:
   - Configure signing certificates in Xcode
   - Set up provisioning profiles
   - Archive and upload via Xcode or Application Loader

3. **TestFlight Distribution**:
   - Upload build to App Store Connect
   - Add to TestFlight for beta testing
   - Submit for App Store review

### Web Deployment

#### GitHub Pages (Automated)

The web app automatically deploys to GitHub Pages on releases:

```yaml
# Deployed to: https://username.github.io/maxed_out_life
# Custom domain: maxedoutlife.app (if configured)
```

#### Manual Web Deployment

```bash
# Build web app
flutter build web --release --web-renderer canvaskit

# Deploy to any static hosting service
# Artifacts in: build/web/
```

#### Docker Web Deployment

```bash
# Build production image
docker build -t maxed_out_life:latest --target web-production .

# Run container
docker run -p 8080:8080 maxed_out_life:latest

# Or use docker-compose
docker-compose --profile production up
```

### Desktop Deployment

#### Windows

```bash
# Build Windows app
flutter build windows --release

# Create installer (optional)
# Use tools like Inno Setup or WiX Toolset
```

#### macOS

```bash
# Build macOS app
flutter build macos --release

# Create DMG (optional)
# Use tools like create-dmg or DMG Canvas
```

#### Linux

```bash
# Build Linux app
flutter build linux --release

# Create packages (optional)
# AppImage, DEB, or Snap packages
```

## 🔐 Security & Signing

### Android Signing

1. **Generate Keystore**:
   ```bash
   keytool -genkey -v -keystore android/keystore.jks \
     -keyalg RSA -keysize 2048 -validity 10000 \
     -alias maxed_out_life
   ```

2. **Configure Signing**:
   ```properties
   # android/key.properties
   storePassword=<password>
   keyPassword=<password>
   keyAlias=maxed_out_life
   storeFile=keystore.jks
   ```

3. **GitHub Secrets**:
   ```
   ANDROID_KEYSTORE_BASE64: <base64-encoded-keystore>
   ANDROID_KEYSTORE_PASSWORD: <keystore-password>
   ANDROID_KEY_ALIAS: maxed_out_life
   ANDROID_KEY_PASSWORD: <key-password>
   ```

### iOS Signing

1. **Certificates & Profiles**:
   - Distribution Certificate
   - App Store Provisioning Profile
   - Export as base64 for CI/CD

2. **GitHub Secrets**:
   ```
   IOS_CERTIFICATE_BASE64: <base64-encoded-certificate>
   IOS_CERTIFICATE_PASSWORD: <certificate-password>
   IOS_PROVISIONING_PROFILE_BASE64: <base64-encoded-profile>
   ```

## 🌐 Environment Configuration

### Environment Variables

```bash
# Development
FLUTTER_ENV=development
DEBUG_MODE=true
LOG_LEVEL=debug
API_BASE_URL=https://dev-api.maxedoutlife.com

# Staging
FLUTTER_ENV=staging
DEBUG_MODE=false
LOG_LEVEL=info
API_BASE_URL=https://staging-api.maxedoutlife.com

# Production
FLUTTER_ENV=production
DEBUG_MODE=false
LOG_LEVEL=error
API_BASE_URL=https://api.maxedoutlife.com
```

### Build Configuration

```yaml
# deployment/config.yml
environments:
  production:
    build:
      type: "release"
      obfuscation: true
      tree_shake_icons: true
    testing:
      run_unit_tests: true
      run_widget_tests: true
      run_integration_tests: true
      performance_tests: true
      security_scan: true
```

## 📊 Monitoring & Analytics

### Performance Monitoring

```yaml
# Enable in deployment/config.yml
monitoring:
  performance:
    enabled: true
    thresholds:
      app_start_time_ms: 3000
      frame_rate_fps: 55
      memory_usage_mb: 200
```

### Error Tracking

```yaml
# Configure error tracking service
monitoring:
  error_tracking:
    enabled: true
    service: "sentry"
```

### Analytics

```yaml
# Configure analytics service
monitoring:
  analytics:
    enabled: true
    service: "firebase"
```

## 🚨 Troubleshooting

### Common Build Issues

1. **Flutter Version Mismatch**:
   ```bash
   flutter channel stable
   flutter upgrade
   flutter clean
   flutter pub get
   ```

2. **Android Build Failures**:
   ```bash
   # Check Android SDK
   flutter doctor --android-licenses
   
   # Clean build
   cd android && ./gradlew clean
   ```

3. **iOS Build Failures**:
   ```bash
   # Clean iOS build
   cd ios && rm -rf Pods Podfile.lock
   pod install
   ```

4. **Web Build Issues**:
   ```bash
   # Enable web
   flutter config --enable-web
   
   # Clean web build
   flutter clean
   flutter build web
   ```

### CI/CD Debugging

1. **Check Workflow Logs**:
   - Navigate to GitHub Actions tab
   - Review failed job logs
   - Check artifact uploads

2. **Local CI Simulation**:
   ```bash
   # Run CI steps locally
   flutter analyze
   flutter test --coverage
   flutter build web --release
   ```

3. **Docker Issues**:
   ```bash
   # Build specific stage
   docker build --target development .
   
   # Debug container
   docker run -it --entrypoint /bin/bash image_name
   ```

## 📋 Release Checklist

### Pre-Release

- [ ] All tests passing
- [ ] Code coverage ≥ 80%
- [ ] Security scan clean
- [ ] Performance benchmarks met
- [ ] Documentation updated
- [ ] Version number updated

### Release Process

- [ ] Create release tag
- [ ] Trigger release pipeline
- [ ] Verify all builds complete
- [ ] Test release artifacts
- [ ] Deploy to staging
- [ ] Staging validation
- [ ] Deploy to production

### Post-Release

- [ ] Monitor error rates
- [ ] Check performance metrics
- [ ] Verify user analytics
- [ ] Update documentation
- [ ] Plan next release

## 🔗 Resources

### Documentation
- [Flutter Deployment Guide](https://docs.flutter.dev/deployment)
- [GitHub Actions Documentation](https://docs.github.com/en/actions)
- [Docker Documentation](https://docs.docker.com/)

### Tools
- [Flutter](https://flutter.dev/)
- [GitHub Actions](https://github.com/features/actions)
- [Docker](https://www.docker.com/)
- [Nginx](https://nginx.org/)

---

**Deployment Version:** 1.0.0  
**Last Updated:** 2025-01-20  
**Deployment Contact:** <EMAIL>
