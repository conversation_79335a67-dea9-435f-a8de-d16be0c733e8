<!-- Guardian Tape Privacy Policy Section -->
<style>
  /* Font Face Declarations for Pirulen and Orbitron */
  @font-face {
    font-family: 'Pirulen';
    src: url('{{ 'Pirulen.otf' | asset_url }}') format('opentype'),
         url('{{ 'Pirulen.woff2' | asset_url }}') format('woff2'),
         url('{{ 'Pirulen.woff' | asset_url }}') format('woff');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'Orbitron';
    src: url('{{ 'Orbitron.woff2' | asset_url }}') format('woff2'),
         url('{{ 'Orbitron.woff' | asset_url }}') format('woff'),
         url('{{ 'Orbitron.ttf' | asset_url }}') format('truetype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
  }

  /* Privacy Policy Section Styles */
  .guardian-privacy-section {
    background: {{ section.settings.background_color | default: '#000000' }};
    color: {{ section.settings.text_color | default: '#ffffff' }};
    padding: {{ section.settings.section_padding_top | default: 60 }}px 0 {{ section.settings.section_padding_bottom | default: 60 }}px 0;
    position: relative;
    overflow: hidden;
  }

  .guardian-privacy-container {
    max-width: {{ section.settings.container_width | default: 1200 }}px;
    margin: 0 auto;
    padding: 0 20px;
  }

  .guardian-privacy-header {
    text-align: center;
    margin-bottom: 50px;
    padding: 40px 20px;
    background: linear-gradient(135deg, rgba(0, 34, 128, 0.1), rgba(138, 43, 226, 0.1));
    border-radius: 15px;
    border: 2px solid rgba(0, 34, 128, 0.3);
  }

  .guardian-privacy-title {
    font-family: 'Pirulen', 'Arial Black', Impact, sans-serif;
    font-size: {{ section.settings.title_size | default: 48 }}px;
    color: {{ section.settings.title_color | default: '#00BFFF' }};
    margin-bottom: 20px;
    text-transform: uppercase;
    letter-spacing: 2px;
    text-shadow: 0 0 20px rgba(0, 191, 255, 0.5);
  }

  .guardian-privacy-subtitle {
    font-family: 'Orbitron', Arial, sans-serif;
    font-size: {{ section.settings.subtitle_size | default: 18 }}px;
    color: {{ section.settings.subtitle_color | default: '#8A2BE2' }};
    margin-bottom: 10px;
    font-weight: 600;
  }

  .guardian-privacy-meta {
    font-family: 'Orbitron', Arial, sans-serif;
    font-size: 14px;
    color: {{ section.settings.meta_color | default: '#BCC6CC' }};
    opacity: 0.9;
  }

  .guardian-privacy-content {
    background: {{ section.settings.content_background | default: '#ffffff' }};
    color: {{ section.settings.content_text_color | default: '#000000' }};
    border-radius: 15px;
    padding: 40px;
    margin: 30px 0;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(0, 34, 128, 0.1);
  }

  .guardian-privacy-content h2 {
    font-family: 'Pirulen', 'Arial Black', Impact, sans-serif;
    color: {{ section.settings.heading_color | default: '#002280' }};
    font-size: {{ section.settings.heading_size | default: 24 }}px;
    margin: 40px 0 20px 0;
    padding-bottom: 10px;
    border-bottom: 3px solid {{ section.settings.accent_color | default: '#00BFFF' }};
    text-transform: uppercase;
    letter-spacing: 1px;
  }

  .guardian-privacy-content h3 {
    font-family: 'Pirulen', 'Arial Black', Impact, sans-serif;
    color: {{ section.settings.subheading_color | default: '#8A2BE2' }};
    font-size: {{ section.settings.subheading_size | default: 18 }}px;
    margin: 30px 0 15px 0;
    text-transform: uppercase;
  }

  .guardian-privacy-content p {
    font-family: 'Orbitron', Arial, sans-serif;
    line-height: 1.7;
    margin-bottom: 20px;
    font-size: {{ section.settings.body_text_size | default: 16 }}px;
  }

  .guardian-privacy-content ul {
    font-family: 'Orbitron', Arial, sans-serif;
    margin: 15px 0;
    padding-left: 25px;
    line-height: 1.6;
  }

  .guardian-privacy-content li {
    margin: 8px 0;
    font-size: {{ section.settings.body_text_size | default: 16 }}px;
  }

  .guardian-privacy-highlight {
    background: linear-gradient(135deg, rgba(0, 191, 255, 0.1), rgba(138, 43, 226, 0.1));
    padding: 20px;
    border-radius: 10px;
    border-left: 4px solid {{ section.settings.accent_color | default: '#00BFFF' }};
    margin: 25px 0;
    font-weight: 600;
  }

  .guardian-privacy-important {
    background: linear-gradient(135deg, rgba(255, 23, 68, 0.1), rgba(255, 152, 0, 0.1));
    padding: 20px;
    border-radius: 10px;
    border-left: 4px solid #FF1744;
    margin: 25px 0;
    font-weight: 600;
  }

  .guardian-privacy-contact {
    background: linear-gradient(135deg, rgba(0, 34, 128, 0.1), rgba(0, 34, 128, 0.05));
    border-radius: 15px;
    padding: 30px;
    text-align: center;
    margin: 40px 0;
    border: 2px solid rgba(0, 34, 128, 0.4);
  }

  .guardian-privacy-contact h3 {
    color: {{ section.settings.accent_color | default: '#00BFFF' }};
    margin-bottom: 15px;
  }

  .guardian-privacy-email {
    font-family: 'Orbitron', Arial, sans-serif;
    color: {{ section.settings.accent_color | default: '#00BFFF' }};
    font-weight: bold;
    font-size: 18px;
    text-decoration: none;
    transition: all 0.3s ease;
  }

  .guardian-privacy-email:hover {
    color: {{ section.settings.hover_color | default: '#8A2BE2' }};
    text-shadow: 0 0 10px rgba(138, 43, 226, 0.5);
  }

  /* Mobile Optimization */
  @media (max-width: 768px) {
    .guardian-privacy-title {
      font-size: {{ section.settings.mobile_title_size | default: 32 }}px;
    }
    
    .guardian-privacy-content {
      padding: 25px 20px;
      margin: 20px 0;
    }
    
    .guardian-privacy-content h2 {
      font-size: {{ section.settings.mobile_heading_size | default: 20 }}px;
    }
    
    .guardian-privacy-content h3 {
      font-size: {{ section.settings.mobile_subheading_size | default: 16 }}px;
    }
    
    .guardian-privacy-content p,
    .guardian-privacy-content li {
      font-size: {{ section.settings.mobile_body_size | default: 14 }}px;
    }
    
    .guardian-privacy-container {
      padding: 0 15px;
    }
  }

  /* Print Styles */
  @media print {
    .guardian-privacy-section {
      background: white !important;
      color: black !important;
    }
    
    .guardian-privacy-content {
      background: white !important;
      color: black !important;
      box-shadow: none !important;
      border: 1px solid #ccc !important;
    }
  }
</style>

<section class="guardian-privacy-section" id="privacy-policy-{{ section.id }}">
  <div class="guardian-privacy-container">
    
    <!-- Header Section -->
    <div class="guardian-privacy-header">
      <h1 class="guardian-privacy-title">{{ section.settings.page_title | default: 'Privacy Policy' }}</h1>
      <p class="guardian-privacy-subtitle">{{ section.settings.app_name | default: 'MXD (Maxed Out Life) Mobile Application' }}</p>
      <p class="guardian-privacy-meta">
        <strong>{{ section.settings.company_name | default: 'Guardian Tape Pty Ltd' }}</strong><br>
        <strong>Effective Date:</strong> {{ section.settings.effective_date | default: 'January 22, 2025' }}<br>
        <strong>Last Updated:</strong> {{ section.settings.last_updated | default: 'January 22, 2025' }}
      </p>
    </div>

    <!-- Contact Information Highlight -->
    <div class="guardian-privacy-contact">
      <h3>Contact Information</h3>
      <p>
        <strong>Email:</strong> <a href="mailto:{{ section.settings.contact_email | default: '<EMAIL>' }}" class="guardian-privacy-email">{{ section.settings.contact_email | default: '<EMAIL>' }}</a><br>
        <strong>Company:</strong> {{ section.settings.company_name | default: 'Guardian Tape Pty Ltd' }} (Australian Company)<br>
        <strong>App:</strong> {{ section.settings.app_details | default: 'MXD (Bundle ID: com.guardiantape.maxedoutlife)' }}
      </p>
    </div>

    <!-- Privacy Policy Content -->
    <div class="guardian-privacy-content">
      
      <h2>1. Introduction</h2>
      <p>{{ section.settings.company_name | default: 'Guardian Tape Pty Ltd' }} ("we," "our," or "us") is an Australian company that operates the {{ section.settings.app_name | default: 'MXD (Maxed Out Life)' }} mobile application ("App," "Service," or "MXD"). This Privacy Policy explains how we collect, use, disclose, and protect your personal information when you use our App.</p>
      
      <div class="guardian-privacy-highlight">
        <strong>Our Commitment:</strong> We are committed to protecting your privacy and handling your personal information in accordance with the Australian Privacy Act 1988 (Cth), the EU General Data Protection Regulation (GDPR), and other applicable privacy laws.
      </div>

      <h2>2. Information We Collect</h2>
      
      <h3>2.1 Personal Information You Provide</h3>
      <p><strong>Account Registration:</strong></p>
      <ul>
        <li>Email address (required for account creation and verification)</li>
        <li>Username (unique identifier, 3-15 characters)</li>
        <li>Password (securely hashed and encrypted)</li>
        <li>Gender (for personalized coaching assignment)</li>
      </ul>
      
      <p><strong>Profile and Preferences:</strong></p>
      <ul>
        <li>Personal development goals and categories</li>
        <li>Custom category names and preferences</li>
        <li>App settings and display preferences</li>
        <li>Notification preferences</li>
      </ul>

      <h3>2.2 Health and Fitness Data</h3>
      <p><strong>Training and Workout Information:</strong></p>
      <ul>
        <li>Workout duration, type, and frequency</li>
        <li>Training timer data and session logs</li>
        <li>Exercise notes and personal reflections</li>
        <li>Body weight tracking (optional)</li>
        <li>Workout scheduling and calendar data</li>
      </ul>

      <h3>2.3 Content You Create</h3>
      <p><strong>Personal Diary and Reflections:</strong></p>
      <ul>
        <li>Daily diary entries and personal notes</li>
        <li>North Star Quest logs and progress updates</li>
        <li>Personal reflections and goal-setting content</li>
        <li>Achievement descriptions and milestone notes</li>
      </ul>
      
      <p><strong>Photos and Media:</strong></p>
      <ul>
        <li>Progress photos for fitness and lifestyle goals</li>
        <li>Achievement documentation images</li>
        <li>Workout-related photos (stored locally on device)</li>
        <li>Vision board images for goal visualization</li>
      </ul>

      <h3>2.4 AI Coaching Interactions</h3>
      <p><strong>Conversation Data:</strong></p>
      <ul>
        <li>Messages sent to and received from AI coaches</li>
        <li>Coaching category selections and preferences</li>
        <li>Response preferences and feedback</li>
        <li>Conversation history and context</li>
      </ul>
      
      <div class="guardian-privacy-important">
        <strong>Note:</strong> AI coaching conversations are processed through OpenAI's API for response generation. We do not store complete conversation transcripts permanently.
      </div>

      <h3>2.5 Location Data</h3>
      <div class="guardian-privacy-important">
        <strong>We do NOT collect location data.</strong> MXD does not access, store, or process any location information from your device.
      </div>

      <h2>3. How We Use Your Information</h2>
      
      <h3>3.1 Core App Functionality</h3>
      <ul>
        <li><strong>Account Management:</strong> Creating, maintaining, and securing your user account</li>
        <li><strong>Personalization:</strong> Providing personalized coaching, recommendations, and content</li>
        <li><strong>Progress Tracking:</strong> Storing and displaying your fitness and personal development progress</li>
        <li><strong>Goal Achievement:</strong> Helping you set, track, and achieve your personal goals</li>
      </ul>

      <h3>3.2 AI Coaching Services</h3>
      <ul>
        <li><strong>Personalized Guidance:</strong> Generating relevant coaching responses based on your goals and preferences</li>
        <li><strong>Content Adaptation:</strong> Tailoring coaching content to your selected categories and progress</li>
        <li><strong>Conversation Context:</strong> Maintaining conversation flow and relevance in coaching interactions</li>
      </ul>

      <h2>4. Information Sharing and Disclosure</h2>
      
      <h3>4.1 Third-Party Service Providers</h3>
      <p>We share limited information with the following trusted service providers:</p>
      
      <p><strong>Firebase (Google LLC):</strong></p>
      <ul>
        <li><strong>Purpose:</strong> User authentication, data storage, and app analytics</li>
        <li><strong>Data Shared:</strong> Email, username, encrypted user data, usage analytics</li>
        <li><strong>Privacy Policy:</strong> https://policies.google.com/privacy</li>
      </ul>
      
      <p><strong>OpenAI (OpenAI, L.P.):</strong></p>
      <ul>
        <li><strong>Purpose:</strong> AI coaching response generation</li>
        <li><strong>Data Shared:</strong> Coaching conversation context and user messages (temporarily)</li>
        <li><strong>Privacy Policy:</strong> https://openai.com/privacy/</li>
      </ul>
      
      <p><strong>Klaviyo (Klaviyo, Inc.):</strong></p>
      <ul>
        <li><strong>Purpose:</strong> Email marketing and communication</li>
        <li><strong>Data Shared:</strong> Email address, username, subscription preferences</li>
        <li><strong>Privacy Policy:</strong> https://www.klaviyo.com/privacy</li>
      </ul>

      <h3>4.2 What We DON'T Share</h3>
      <div class="guardian-privacy-important">
        <ul>
          <li><strong>We never sell your personal information</strong></li>
          <li><strong>We don't share data with advertising networks</strong></li>
          <li><strong>We don't provide data to data brokers</strong></li>
          <li><strong>We don't share information with business partners beyond those listed above</strong></li>
        </ul>
      </div>

      <h2>5. Data Security and Protection</h2>
      
      <h3>5.1 Security Measures</h3>
      <p><strong>Encryption:</strong></p>
      <ul>
        <li>All data transmitted between your device and our servers uses HTTPS/TLS encryption</li>
        <li>Passwords are securely hashed using industry-standard algorithms</li>
        <li>Sensitive data is encrypted both in transit and at rest</li>
      </ul>

      <h3>5.2 Data Retention</h3>
      <div class="guardian-privacy-highlight">
        <p><strong>Immediate Deletion Policy:</strong></p>
        <ul>
          <li><strong>Immediate Deletion:</strong> All personal data is deleted immediately upon account deletion request</li>
          <li><strong>No Retention:</strong> We do not retain any personal information after account deletion</li>
          <li><strong>Backup Removal:</strong> Data is removed from all backups within 30 days</li>
        </ul>
      </div>

      <h2>6. Your Privacy Rights</h2>
      
      <h3>6.1 GDPR Rights (EU Users)</h3>
      <p>If you are located in the European Union, you have additional rights:</p>
      <ul>
        <li><strong>Right to Access:</strong> Request information about the personal data we hold about you</li>
        <li><strong>Right to Rectification:</strong> Correct inaccurate or incomplete personal data</li>
        <li><strong>Right to Erasure:</strong> Request deletion of your personal data ("right to be forgotten")</li>
        <li><strong>Right to Data Portability:</strong> Receive your data in a portable format</li>
      </ul>

      <h3>6.2 Australian Privacy Rights</h3>
      <p>Under the Australian Privacy Act 1988, you have the right to:</p>
      <ul>
        <li>Access your personal information</li>
        <li>Correct inaccurate or out-of-date information</li>
        <li>Make a complaint about privacy breaches</li>
        <li>Request information about our privacy practices</li>
      </ul>

      <h2>7. Children's Privacy</h2>
      
      <h3>7.1 Age Requirements</h3>
      <ul>
        <li><strong>Minimum Age:</strong> MXD is intended for users aged 13 and older</li>
        <li><strong>Parental Consent:</strong> Users under 18 should obtain parental consent before using the app</li>
        <li><strong>No Intentional Collection:</strong> We do not knowingly collect personal information from children under 13</li>
      </ul>

      <h2>8. Contact Information</h2>
      
      <div class="guardian-privacy-contact">
        <h3>Privacy Inquiries</h3>
        <p>
          <strong>Email:</strong> <a href="mailto:{{ section.settings.contact_email | default: '<EMAIL>' }}" class="guardian-privacy-email">{{ section.settings.contact_email | default: '<EMAIL>' }}</a><br>
          <strong>Subject Line:</strong> "Privacy Policy Inquiry - MXD App"<br>
          <strong>Response Time:</strong> We aim to respond within 48 hours
        </p>
      </div>

      <div class="guardian-privacy-highlight" style="text-align: center; margin-top: 40px;">
        <strong>This Privacy Policy is effective as of {{ section.settings.effective_date | default: 'January 22, 2025' }}, and governs the collection, use, and disclosure of personal information in connection with the {{ section.settings.app_name | default: 'MXD' }} mobile application operated by {{ section.settings.company_name | default: 'Guardian Tape Pty Ltd' }}.</strong>
      </div>

    </div>
  </div>
</section>


