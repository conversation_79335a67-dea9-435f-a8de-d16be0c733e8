      1@(  "C:\Users\<USER>\maxed_out_life\.env                           OC:\Users\<USER>\maxed_out_life\build\ae30582a806dc4248fd237072391b5a0\.filecache                           (C:\Users\<USER>\maxed_out_life\.gitignore                           0C:\Users\<USER>\maxed_out_life\android\.gitignore                           ,C:\Users\<USER>\maxed_out_life\ios\.gitignore                           .C:\Users\<USER>\maxed_out_life\linux\.gitignore                           .C:\Users\<USER>\maxed_out_life\macos\.gitignore                           0C:\Users\<USER>\maxed_out_life\windows\.gitignore                           2C:\Users\<USER>\maxed_out_life\build\.last_build_id                           'C:\Users\<USER>\maxed_out_life\.metadata                           9C:\Users\<USER>\maxed_out_life\lib\screens\7dd_screen.dart                           QC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\screens\7dd_screen.dart                           QC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\screens\7dd_screen.dart                           cC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\clone\screens\active_training_screen.dart                           KC:\Users\<USER>\maxed_out_life\WorkoutHub\active_workout_session_screen.dart                           ;C:\Users\<USER>\maxed_out_life\lib\screens\admin_screen.dart                           SC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\screens\admin_screen.dart                           SC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\screens\admin_screen.dart                           3C:\Users\<USER>\maxed_out_life\analysis_options.yaml                           GC:\Users\<USER>\maxed_out_life\android\app\src\debug\AndroidManifest.xml                           FC:\Users\<USER>\maxed_out_life\android\app\src\main\AndroidManifest.xml                           IC:\Users\<USER>\maxed_out_life\android\app\src\profile\AndroidManifest.xml                           :C:\Users\<USER>\maxed_out_life\ios\Runner\AppDelegate.swift                           <C:\Users\<USER>\maxed_out_life\macos\Runner\AppDelegate.swift                           @C:\Users\<USER>\maxed_out_life\ios\Flutter\AppFrameworkInfo.plist                           CC:\Users\<USER>\maxed_out_life\macos\Runner\Configs\AppInfo.xcconfig                           1C:\Users\<USER>\maxed_out_life\lib\l10n\app_en.arb                           CC:\Users\<USER>\maxed_out_life\windows\runner\resources\app_icon.ico                           _C:\Users\<USER>\maxed_out_life\macos\Runner\Assets.xcassets\AppIcon.appiconset\app_icon_1024.png                           ^C:\Users\<USER>\maxed_out_life\macos\Runner\Assets.xcassets\AppIcon.appiconset\app_icon_128.png                           ]C:\Users\<USER>\maxed_out_life\macos\Runner\Assets.xcassets\AppIcon.appiconset\app_icon_16.png                           ^C:\Users\<USER>\maxed_out_life\macos\Runner\Assets.xcassets\AppIcon.appiconset\app_icon_256.png                           ]C:\Users\<USER>\maxed_out_life\macos\Runner\Assets.xcassets\AppIcon.appiconset\app_icon_32.png                           ^C:\Users\<USER>\maxed_out_life\macos\Runner\Assets.xcassets\AppIcon.appiconset\app_icon_512.png                           ]C:\Users\<USER>\maxed_out_life\macos\Runner\Assets.xcassets\AppIcon.appiconset\app_icon_64.png                           ?C:\Users\<USER>\maxed_out_life\lib\carena\arena_home_screen.dart                           >C:\Users\<USER>\maxed_out_life\lib\utils\audio_popup_utils.dart                           VC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\utils\audio_popup_utils.dart                           VC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\utils\audio_popup_utils.dart                           BC:\Users\<USER>\maxed_out_life\lib\widgets\back_to_home_button.dart                           ZC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\widgets\back_to_home_button.dart                           ZC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\widgets\back_to_home_button.dart                           YC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\mxd\badge_achievement_grid.dart                           YC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\mxd\badge_achievement_grid.dart                           RC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\engine\badge_engine.dart                           RC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\engine\badge_engine.dart                           QC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\models\badge_model.dart                           QC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\models\badge_model.dart                           PC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\mxd\badge_storage.dart                           PC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\mxd\badge_storage.dart                           VC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\mxd\badge_unlock_dialog.dart                           VC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\mxd\badge_unlock_dialog.dart                           9C:\Users\<USER>\maxed_out_life\assets\fonts\BITSUMISHI.TTF                           :C:\Users\<USER>\maxed_out_life\android\app\build.gradle.kts                           6C:\Users\<USER>\maxed_out_life\android\build.gradle.kts                           [C:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\widgets\build_active_session.dart                           [C:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\widgets\build_active_session.dart                           <C:\Users\<USER>\maxed_out_life\lib\widgets\cancel_button.dart                           TC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\widgets\cancel_button.dart                           TC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\widgets\cancel_button.dart                           ?C:\Users\<USER>\maxed_out_life\lib\widgets\category_buttons.dart                           WC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\widgets\category_buttons.dart                           WC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\widgets\category_buttons.dart                           FC:\Users\<USER>\maxed_out_life\lib\screens\category_manager_screen.dart                           ^C:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\screens\category_manager_screen.dart                           ^C:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\screens\category_manager_screen.dart                           <C:\Users\<USER>\maxed_out_life\lib\carena\challenge_card.dart                           EC:\Users\<USER>\maxed_out_life\lib\carena\challenge_detail_screen.dart                           ?C:\Users\<USER>\maxed_out_life\lib\carena\challenge_manager.dart                           =C:\Users\<USER>\maxed_out_life\lib\carena\challenge_model.dart                           UC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\models\challenge_model.dart                           UC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\models\challenge_model.dart                           DC:\Users\<USER>\maxed_out_life\lib\carena\challenge_progress_bar.dart                           WC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\screens\challenge_screen.dart                           WC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\screens\challenge_screen.dart                           YC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\services\challenge_service.dart                           YC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\services\challenge_service.dart                           eC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\clone\screens\clone_add_exercise_modal.dart                           eC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\clone\widgets\clone_add_exercise_popup.dart                           aC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\clone\widgets\clone_add_set_button.dart                           iC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\clone\screens\clone_create_exercise_screen.dart                           gC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\clone\screens\clone_exercise_list_screen.dart                           `C:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\clone\models\clone_exercise_model.dart                           dC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\clone\services\clone_exercise_service.dart                           `C:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\clone\widgets\clone_exercise_tile.dart                           ^C:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\clone\services\clone_exp_engine.dart                           `C:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\clone\services\clone_exp_uploader.dart                           hC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\clone\services\clone_exp_uploader_service.dart                           _C:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\clone\models\clone_history_model.dart                           cC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\clone\services\clone_history_service.dart                           dC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\clone\widgets\clone_rest_timer_widget.dart                           `C:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\clone\widgets\clone_set_input_row.dart                           `C:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\clone\models\clone_template_model.dart                           dC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\clone\services\clone_template_service.dart                           jC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\clone\services\clone_training_history_model.dart                           `C:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\clone\models\clone_training_model.dart                           jC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\clone\screens\clone_training_summary_screen.dart                           2C:\Users\<USER>\maxed_out_life\linux\CMakeLists.txt                           :C:\Users\<USER>\maxed_out_life\linux\flutter\CMakeLists.txt                           9C:\Users\<USER>\maxed_out_life\linux\runner\CMakeLists.txt                           4C:\Users\<USER>\maxed_out_life\windows\CMakeLists.txt                           <C:\Users\<USER>\maxed_out_life\windows\flutter\CMakeLists.txt                           ;C:\Users\<USER>\maxed_out_life\windows\runner\CMakeLists.txt                           =C:\Users\<USER>\maxed_out_life\.vs\CMakeWorkspaceSettings.json                           5C:\Users\<USER>\maxed_out_life\lib\prompts\coachp.dart                           MC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\prompts\coachp.dart                           MC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\prompts\coachp.dart                           @C:\Users\<USER>\maxed_out_life\lib\screens\coach_chat_screen.dart                           XC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\screens\coach_chat_screen.dart                           XC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\screens\coach_chat_screen.dart                           9C:\Users\<USER>\maxed_out_life\lib\data\coach_prompts.dart                           QC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\data\coach_prompts.dart                           QC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\data\coach_prompts.dart                           =C:\Users\<USER>\maxed_out_life\lib\services\coach_service.dart                           UC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\services\coach_service.dart                           UC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\services\coach_service.dart                           3C:\Users\<USER>\maxed_out_life\lib\theme\colors.dart                           KC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\theme\colors.dart                           KC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\theme\colors.dart                           9C:\Users\<USER>\maxed_out_life\lib\carena\coming_soon.dart                           IC:\Users\<USER>\maxed_out_life\lib\carena\completed_challenges_screen.dart                           EC:\Users\<USER>\maxed_out_life\lib\dhabits\complete_habits_widget.dart                           YC:\Users\<USER>\maxed_out_life\ios\Runner\Assets.xcassets\AppIcon.appiconset\Contents.json                           [C:\Users\<USER>\maxed_out_life\ios\Runner\Assets.xcassets\LaunchImage.imageset\Contents.json                           [C:\Users\<USER>\maxed_out_life\macos\Runner\Assets.xcassets\AppIcon.appiconset\Contents.json                           _C:\Users\<USER>\maxed_out_life\ios\Runner.xcodeproj\project.xcworkspace\contents.xcworkspacedata                           MC:\Users\<USER>\maxed_out_life\ios\Runner.xcworkspace\contents.xcworkspacedata                           OC:\Users\<USER>\maxed_out_life\macos\Runner.xcworkspace\contents.xcworkspacedata                           >C:\Users\<USER>\maxed_out_life\lib\screens\countdown_timer.dart                           VC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\screens\countdown_timer.dart                           VC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\widgets\countdown_timer.dart                           VC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\screens\countdown_timer.dart                           VC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\widgets\countdown_timer.dart                           EC:\Users\<USER>\maxed_out_life\lib\carena\create_challenge_screen.dart                           ^C:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\screens\create_challenge_screen.dart                           ^C:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\screens\create_challenge_screen.dart                           XC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\mxd\create_exercise_modal.dart                           KC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\services\csv.dart                           KC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\services\csv.dart                           _C:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\screens\current_challenge_screen.dart                           _C:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\screens\current_challenge_screen.dart                           VC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\mxd\custom_timer_config.dart                           VC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\mxd\custom_timer_config.dart                           BC:\Users\<USER>\maxed_out_life\lib\dhabits\daily_habits_screen.dart                           BC:\Users\<USER>\maxed_out_life\lib\screens\daily_habits_screen.dart                           ZC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\screens\daily_habits_screen.dart                           ZC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\screens\daily_habits_screen.dart                           8C:\Users\<USER>\maxed_out_life\ios\Flutter\Debug.xcconfig                           AC:\Users\<USER>\maxed_out_life\macos\Runner\Configs\Debug.xcconfig                           DC:\Users\<USER>\maxed_out_life\macos\Runner\DebugProfile.entitlements                           6C:\Users\<USER>\maxed_out_life\lib\widgets\dhm_pop.dart                           NC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\widgets\dhm_pop.dart                           NC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\widgets\dhm_pop.dart                           9C:\Users\<USER>\maxed_out_life\lib\widgets\diary_feed.dart                           QC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\widgets\diary_feed.dart                           QC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\widgets\diary_feed.dart                           8C:\Users\<USER>\maxed_out_life\assets\fonts\digital-7.ttf                           WC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\mxd\dismissible_set_card.dart                           WC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\mxd\dismissible_set_card.dart                           2C:\Users\<USER>\maxed_out_life\lib\secrets\env.dart                           JC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\secrets\env.dart                           JC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\secrets\env.dart                           YC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\mxd\exercise_detail_screen.dart                           YC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\mxd\exercise_detail_screen.dart                           UC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\mxd\exercise_log_chart.dart                           UC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\mxd\exercise_log_chart.dart                           TC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\models\exercise_model.dart                           TC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\models\exercise_model.dart                           XC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\mxd\exercise_picker_modal.dart                           UC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\mxd\export_csv_service.dart                           UC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\mxd\export_csv_service.dart                           QC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\mxd\exp_bar_widget.dart                           QC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\mxd\exp_bar_widget.dart                           @C:\Users\<USER>\maxed_out_life\lib\widgets\exp_category_tile.dart                           XC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\widgets\exp_category_tile.dart                           XC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\widgets\exp_category_tile.dart                           /C:\Users\<USER>\maxed_out_life\lib\exp_data.dart                           GC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\exp_data.dart                           GC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\exp_data.dart                           8C:\Users\<USER>\maxed_out_life\lib\screens\exp_entry.dart                           PC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\screens\exp_entry.dart                           PC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\screens\exp_entry.dart                           =C:\Users\<USER>\maxed_out_life\lib\models\exp_entry_model.dart                           UC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\models\exp_entry_model.dart                           UC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\models\exp_entry_model.dart                           FC:\Users\<USER>\maxed_out_life\lib\services\exp_log_secure_service.dart                           ^C:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\services\exp_log_secure_service.dart                           ^C:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\services\exp_log_secure_service.dart                           ?C:\Users\<USER>\maxed_out_life\lib\services\exp_log_service.dart                           WC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\services\exp_log_service.dart                           WC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\services\exp_log_service.dart                           BC:\Users\<USER>\maxed_out_life\lib\widgets\exp_pie_chart_popup.dart                           ZC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\widgets\exp_pie_chart_popup.dart                           ZC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\widgets\exp_pie_chart_popup.dart                           ;C:\Users\<USER>\maxed_out_life\lib\widgets\exp_progress.dart                           SC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\widgets\exp_progress.dart                           SC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\widgets\exp_progress.dart                           8C:\Users\<USER>\maxed_out_life\lib\dhabits\exp_tally.dart                           -C:\Users\<USER>\maxed_out_life\web\favicon.png                           YC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\mxd\first_perk_unlock_stub.dart                           YC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\mxd\first_perk_unlock_stub.dart                           BC:\Users\<USER>\maxed_out_life\macos\Flutter\Flutter-Debug.xcconfig                           DC:\Users\<USER>\maxed_out_life\macos\Flutter\Flutter-Release.xcconfig                           ?C:\Users\<USER>\maxed_out_life\windows\runner\flutter_window.cpp                           =C:\Users\<USER>\maxed_out_life\windows\runner\flutter_window.h                           =C:\Users\<USER>\maxed_out_life\lib\widgets\footer_message.dart                           UC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\widgets\footer_message.dart                           UC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\widgets\footer_message.dart                           @C:\Users\<USER>\maxed_out_life\lib\screens\full_timer_screen.dart                           XC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\screens\full_timer_screen.dart                           XC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\screens\full_timer_screen.dart                           KC:\Users\<USER>\maxed_out_life\macos\Flutter\GeneratedPluginRegistrant.swift                           CC:\Users\<USER>\maxed_out_life\linux\flutter\generated_plugins.cmake                           EC:\Users\<USER>\maxed_out_life\windows\flutter\generated_plugins.cmake                           JC:\Users\<USER>\maxed_out_life\linux\flutter\generated_plugin_registrant.cc                           LC:\Users\<USER>\maxed_out_life\windows\flutter\generated_plugin_registrant.cc                           IC:\Users\<USER>\maxed_out_life\linux\flutter\generated_plugin_registrant.h                           KC:\Users\<USER>\maxed_out_life\windows\flutter\generated_plugin_registrant.h                           \C:\Users\<USER>\maxed_out_life\build\ae30582a806dc4248fd237072391b5a0\gen_localizations.stamp                           PC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\models\goal_model.dart                           PC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\models\goal_model.dart                           VC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\mxd\goal_tracker_widget.dart                           VC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\mxd\goal_tracker_widget.dart                           ;C:\Users\<USER>\maxed_out_life\lib\services\gpt_service.dart                           SC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\services\gpt_service.dart                           SC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\services\gpt_service.dart                           NC:\Users\<USER>\maxed_out_life\android\gradle\wrapper\gradle-wrapper.properties                           7C:\Users\<USER>\maxed_out_life\android\gradle.properties                           DC:\Users\<USER>\maxed_out_life\assets\images\guardian_tape_splash.png                           @C:\Users\<USER>\maxed_out_life\lib\dhabits\habit_card_widget.dart                           <C:\Users\<USER>\maxed_out_life\lib\dhabits\habit_manager.dart                           .C:\Users\<USER>\maxed_out_life\home_screen.dart                           ;C:\Users\<USER>\maxed_out_life\lib\screens\home_screen2.dart                           SC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\screens\home_screen2.dart                           SC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\screens\home_screen2.dart                           7C:\Users\<USER>\maxed_out_life\assets\sounds\HSError.MP3                           8C:\Users\<USER>\maxed_out_life\assets\sounds\HSError2.MP3                           4C:\Users\<USER>\maxed_out_life\web\icons\Icon-192.png                           4C:\Users\<USER>\maxed_out_life\web\icons\Icon-512.png                           eC:\Users\<USER>\maxed_out_life\ios\Runner\Assets.xcassets\AppIcon.appiconset\<EMAIL>                           aC:\Users\<USER>\maxed_out_life\ios\Runner\Assets.xcassets\AppIcon.appiconset\<EMAIL>                           aC:\Users\<USER>\maxed_out_life\ios\Runner\Assets.xcassets\AppIcon.appiconset\<EMAIL>                           aC:\Users\<USER>\maxed_out_life\ios\Runner\Assets.xcassets\AppIcon.appiconset\<EMAIL>                           aC:\Users\<USER>\maxed_out_life\ios\Runner\Assets.xcassets\AppIcon.appiconset\<EMAIL>                           aC:\Users\<USER>\maxed_out_life\ios\Runner\Assets.xcassets\AppIcon.appiconset\<EMAIL>                           aC:\Users\<USER>\maxed_out_life\ios\Runner\Assets.xcassets\AppIcon.appiconset\<EMAIL>                           aC:\Users\<USER>\maxed_out_life\ios\Runner\Assets.xcassets\AppIcon.appiconset\<EMAIL>                           aC:\Users\<USER>\maxed_out_life\ios\Runner\Assets.xcassets\AppIcon.appiconset\<EMAIL>                           aC:\Users\<USER>\maxed_out_life\ios\Runner\Assets.xcassets\AppIcon.appiconset\<EMAIL>                           aC:\Users\<USER>\maxed_out_life\ios\Runner\Assets.xcassets\AppIcon.appiconset\<EMAIL>                           aC:\Users\<USER>\maxed_out_life\ios\Runner\Assets.xcassets\AppIcon.appiconset\<EMAIL>                           aC:\Users\<USER>\maxed_out_life\ios\Runner\Assets.xcassets\AppIcon.appiconset\<EMAIL>                           aC:\Users\<USER>\maxed_out_life\ios\Runner\Assets.xcassets\AppIcon.appiconset\<EMAIL>                           eC:\Users\<USER>\maxed_out_life\ios\Runner\Assets.xcassets\AppIcon.appiconset\<EMAIL>                           =C:\Users\<USER>\maxed_out_life\web\icons\Icon-maskable-192.png                           =C:\Users\<USER>\maxed_out_life\web\icons\Icon-maskable-512.png                           RC:\Users\<USER>\maxed_out_life\android\app\src\main\res\mipmap-hdpi\ic_launcher.png                           RC:\Users\<USER>\maxed_out_life\android\app\src\main\res\mipmap-mdpi\ic_launcher.png                           SC:\Users\<USER>\maxed_out_life\android\app\src\main\res\mipmap-xhdpi\ic_launcher.png                           TC:\Users\<USER>\maxed_out_life\android\app\src\main\res\mipmap-xxhdpi\ic_launcher.png                           UC:\Users\<USER>\maxed_out_life\android\app\src\main\res\mipmap-xxxhdpi\ic_launcher.png                           lC:\Users\<USER>\maxed_out_life\ios\Runner.xcodeproj\project.xcworkspace\xcshareddata\IDEWorkspaceChecks.plist                           ZC:\Users\<USER>\maxed_out_life\ios\Runner.xcworkspace\xcshareddata\IDEWorkspaceChecks.plist                           nC:\Users\<USER>\maxed_out_life\macos\Runner.xcodeproj\project.xcworkspace\xcshareddata\IDEWorkspaceChecks.plist                           \C:\Users\<USER>\maxed_out_life\macos\Runner.xcworkspace\xcshareddata\IDEWorkspaceChecks.plist                           ;C:\Users\<USER>\maxed_out_life\lib\widgets\image_picker.dart                           ,C:\Users\<USER>\maxed_out_life\web\index.html                           3C:\Users\<USER>\maxed_out_life\ios\Runner\Info.plist                           5C:\Users\<USER>\maxed_out_life\macos\Runner\Info.plist                           `C:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\- AUS Fight Nerds\killer_instinct.dart                           `C:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\- AUS Fight Nerds\killer_instinct.dart                           ]C:\Users\<USER>\maxed_out_life\ios\Runner\Assets.xcassets\LaunchImage.imageset\LaunchImage.png                           `C:\Users\<USER>\maxed_out_life\ios\Runner\Assets.xcassets\LaunchImage.imageset\<EMAIL>                           `C:\Users\<USER>\maxed_out_life\ios\Runner\Assets.xcassets\LaunchImage.imageset\<EMAIL>                           KC:\Users\<USER>\maxed_out_life\ios\Runner\Base.lproj\LaunchScreen.storyboard                           YC:\Users\<USER>\maxed_out_life\android\app\src\main\res\drawable-v21\launch_background.xml                           UC:\Users\<USER>\maxed_out_life\android\app\src\main\res\drawable\launch_background.xml                           >C:\Users\<USER>\maxed_out_life\Old Zip Files\lib Fri2 09.35.zip                           >C:\Users\<USER>\maxed_out_life\Old Zip Files\lib Fri2 12.28.zip                           ?C:\Users\<USER>\maxed_out_life\Old Zip Files\lib Fri25 12.48.zip                           ?C:\Users\<USER>\maxed_out_life\Old Zip Files\lib Fri25 14.28.zip                           ?C:\Users\<USER>\maxed_out_life\Old Zip Files\lib Fri25 20.42.zip                           BC:\Users\<USER>\maxed_out_life\lib Fri9.5.25 CArena Coming Soon.zip                           @C:\Users\<USER>\maxed_out_life\lib Fri9.5.25 No Errors CArena.zip                           /C:\Users\<USER>\maxed_out_life\lib Fri9.5.25.zip                           =C:\Users\<USER>\maxed_out_life\Old Zip Files\lib Mon 08.53.zip                           =C:\Users\<USER>\maxed_out_life\Old Zip Files\lib Mon 11.59.zip                           IC:\Users\<USER>\maxed_out_life\lib Mon 12.5.25 Working Challenge Arena.zip                           =C:\Users\<USER>\maxed_out_life\Old Zip Files\lib Mon 16.48.zip                           =C:\Users\<USER>\maxed_out_life\Old Zip Files\lib Mon 18.27.zip                           =C:\Users\<USER>\maxed_out_life\Old Zip Files\lib Mon 19.42.zip                           ?C:\Users\<USER>\maxed_out_life\Old Zip Files\lib Mon28 09.41.zip                           AC:\Users\<USER>\maxed_out_life\Old Zip Files\lib Mon30.4 10.24.zip                           CC:\Users\<USER>\maxed_out_life\Old Zip Files\lib Mon5.5.25 11.19.zip                           >C:\Users\<USER>\maxed_out_life\Old Zip Files\lib Sat 16.35 .zip                           =C:\Users\<USER>\maxed_out_life\Old Zip Files\lib Sat 18.13.zip                           TC:\Users\<USER>\maxed_out_life\Old Zip Files\lib Sat 3.5.25 11.30 (Pre-Overmatch).zip                           ?C:\Users\<USER>\maxed_out_life\Old Zip Files\lib Sat26 10.43.zip                           ?C:\Users\<USER>\maxed_out_life\Old Zip Files\lib Sat26 14.11.zip                           NC:\Users\<USER>\maxed_out_life\Old Zip Files\lib Sat3.5.25 11.54 (No bueno).zip                           CC:\Users\<USER>\maxed_out_life\Old Zip Files\lib Sat3.5.25 17.53.zip                           @C:\Users\<USER>\maxed_out_life\Old Zip Files\lib Sun 27 10.34.zip                           ?C:\Users\<USER>\maxed_out_life\Old Zip Files\lib Sun27 11.24.zip                           ?C:\Users\<USER>\maxed_out_life\Old Zip Files\lib Sun27 13.44.zip                           ?C:\Users\<USER>\maxed_out_life\Old Zip Files\lib Sun27 21.57.zip                           CC:\Users\<USER>\maxed_out_life\Old Zip Files\lib Sun4.5.25 13.42.zip                           CC:\Users\<USER>\maxed_out_life\Old Zip Files\lib Sun4.5.25 15.37.zip                           ?C:\Users\<USER>\maxed_out_life\Old Zip Files\lib Thurs 08.12.zip                           ?C:\Users\<USER>\maxed_out_life\Old Zip Files\lib Thurs 08.24.zip                           ?C:\Users\<USER>\maxed_out_life\Old Zip Files\lib Thurs 08.40.zip                           ?C:\Users\<USER>\maxed_out_life\Old Zip Files\lib Thurs 11.28.zip                           ?C:\Users\<USER>\maxed_out_life\Old Zip Files\lib Thurs 12.03.zip                           EC:\Users\<USER>\maxed_out_life\Old Zip Files\lib Thurs1.5.25 12.52.zip                           EC:\Users\<USER>\maxed_out_life\Old Zip Files\lib Thurs1.5.25 14.26.zip                           EC:\Users\<USER>\maxed_out_life\Old Zip Files\lib Thurs1.5.25 15.26.zip                           EC:\Users\<USER>\maxed_out_life\Old Zip Files\lib Thurs1.5.25 15.43.zip                           7C:\Users\<USER>\maxed_out_life\lib Thurs8.5.25 13.22.zip                           AC:\Users\<USER>\maxed_out_life\lib Thurs8.5.25 After EXP Reset.zip                           aC:\Users\<USER>\maxed_out_life\Old Zip Files\lib Thurs8.5.25 Challnege Model + Spanish 10.01am.zip                           >C:\Users\<USER>\maxed_out_life\Old Zip Files\lib Tues 21.28.zip                           AC:\Users\<USER>\maxed_out_life\lib Tues13.5.25 NS Quests Begin.zip                           IC:\Users\<USER>\maxed_out_life\lib Tues13.5.25 todayKey DHabits OMatch.zip                           AC:\Users\<USER>\maxed_out_life\lib Tues13.5.25 Working DHabits.zip                           DC:\Users\<USER>\maxed_out_life\Old Zip Files\lib Tues6.5.25 14.48.zip                           =C:\Users\<USER>\maxed_out_life\Old Zip Files\lib Wed 08.05.zip                           =C:\Users\<USER>\maxed_out_life\Old Zip Files\lib Wed 08.36.zip                           =C:\Users\<USER>\maxed_out_life\Old Zip Files\lib Wed 09.10.zip                           =C:\Users\<USER>\maxed_out_life\Old Zip Files\lib Wed 10.25.zip                           =C:\Users\<USER>\maxed_out_life\Old Zip Files\lib Wed 11.07.zip                           =C:\Users\<USER>\maxed_out_life\Old Zip Files\lib Wed 15.39.zip                           =C:\Users\<USER>\maxed_out_life\Old Zip Files\lib Wed 16.04.zip                           =C:\Users\<USER>\maxed_out_life\Old Zip Files\lib Wed 16.24.zip                           =C:\Users\<USER>\maxed_out_life\Old Zip Files\lib Wed 17.38.zip                           =C:\Users\<USER>\maxed_out_life\Old Zip Files\lib Wed 20.17.zip                           >C:\Users\<USER>\maxed_out_life\Old Zip Files\lib Wed2 09.38.zip                           >C:\Users\<USER>\maxed_out_life\Old Zip Files\lib Wed2 12.03.zip                           >C:\Users\<USER>\maxed_out_life\Old Zip Files\lib Wed2 17.11.zip                           DC:\Users\<USER>\maxed_out_life\Old Zip Files\lib Wed30.4.25 13.43.zip                           DC:\Users\<USER>\maxed_out_life\Old Zip Files\lib Wed30.4.25 15.25.zip                           DC:\Users\<USER>\maxed_out_life\Old Zip Files\lib Wed30.4.25 16.40.zip                           DC:\Users\<USER>\maxed_out_life\Old Zip Files\lib Wed30.4.25 17.16.zip                           DC:\Users\<USER>\maxed_out_life\Old Zip Files\lib Wed30.4.25 17.49.zip                           DC:\Users\<USER>\maxed_out_life\Old Zip Files\lib Wed30.4.25 19.24.zip                           DC:\Users\<USER>\maxed_out_life\Old Zip Files\lib Wed30.4.25 19.41.zip                           DC:\Users\<USER>\maxed_out_life\Old Zip Files\lib Wed30.4.25 20.03.zip                           SC:\Users\<USER>\maxed_out_life\Old Zip Files\lib Wed7.5.25 Challenge Arena Begin.zip                           @C:\Users\<USER>\maxed_out_life\lib\screens\life_coach_screen.dart                           XC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\screens\life_coach_screen.dart                           XC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\screens\life_coach_screen.dart                           :C:\Users\<USER>\maxed_out_life\lib\widgets\lockdisplay.dart                           RC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\widgets\lockdisplay.dart                           RC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\widgets\lockdisplay.dart                           gC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\.Kotlin+Swift Files\LockWidgetExtension.swift                           cC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\.Kotlin+Swift Files\LockWidgetProvider.kt                           :C:\Users\<USER>\maxed_out_life\lib\widgets\lock_widget.dart                           RC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\widgets\lock_widget.dart                           RC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\widgets\lock_widget.dart                           dC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\.Kotlin+Swift Files\lock_widget_layout.xml                           ;C:\Users\<USER>\maxed_out_life\lib\screens\login_screen.dart                           SC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\screens\login_screen.dart                           SC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\screens\login_screen.dart                           2C:\Users\<USER>\maxed_out_life\linux\runner\main.cc                           5C:\Users\<USER>\maxed_out_life\windows\runner\main.cpp                           +C:\Users\<USER>\maxed_out_life\lib\main.dart                           CC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\main.dart                           CC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\main.dart                           CC:\Users\<USER>\maxed_out_life\ios\Runner\Base.lproj\Main.storyboard                           dC:\Users\<USER>\maxed_out_life\android\app\src\main\kotlin\com\example\maxed_out_life\MainActivity.kt                           BC:\Users\<USER>\maxed_out_life\macos\Runner\MainFlutterWindow.swift                           BC:\Users\<USER>\maxed_out_life\macos\Runner\Base.lproj\MainMenu.xib                           /C:\Users\<USER>\maxed_out_life\web\manifest.json                           @C:\Users\<USER>\maxed_out_life\assets\images\MaxedOutLifeIcon.png                           >C:\Users\<USER>\maxed_out_life\lib\widgets\maxedout_appbar.dart                           VC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\widgets\maxedout_appbar.dart                           VC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\widgets\maxedout_appbar.dart                           @C:\Users\<USER>\maxed_out_life\WOStrong\maxed_active_session.dart                           DC:\Users\<USER>\maxed_out_life\WOStrong\maxed_workout_hub_screen.dart                           HC:\Users\<USER>\maxed_out_life\WOStrong\maxed_wo_progress_track_dash.dart                           CC:\Users\<USER>\maxed_out_life\WOStrong\maxed_wo_summary_screen.dart                           DC:\Users\<USER>\maxed_out_life\WOStrong\maxed_wo_temp_man_screen.dart                           [C:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\mxd\mock_workout_demo_screen.dart                           [C:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\mxd\mock_workout_demo_screen.dart                           CC:\Users\<USER>\maxed_out_life\Old Zip Files\mxd Sat3.5.25 14.21.zip                           eC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\clone\screens\MXDC_training_hub_screen.dart                           _C:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\mxd\mxd_active_wo_session_screen.dart                           YC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\mxd\mxd_history_entry_tile.dart                           YC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\mxd\mxd_history_entry_tile.dart                           VC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\mxd\mxd_settings_screen.dart                           VC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\mxd\mxd_settings_screen.dart                           XC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\mxd\mxd_wo_history_screen.dart                           TC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\mxd\mxd_wo_hub_screen.dart                           YC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\mxd\mxd_wo_prog_track_dash.dart                           YC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\mxd\mxd_wo_prog_track_dash.dart                           XC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\mxd\mxd_wo_summary_screen.dart                           XC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\mxd\mxd_wo_summary_screen.dart                           YC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\mxd\mxd_wo_temp_man_screen.dart                           SC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\mxd\mxd_wo_temp_tile.dart                           SC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\mxd\mxd_wo_temp_tile.dart                           <C:\Users\<USER>\maxed_out_life\linux\runner\my_application.cc                           ;C:\Users\<USER>\maxed_out_life\linux\runner\my_application.h                           >C:\Users\<USER>\maxed_out_life\lib\quests\north_star_model.dart                           :C:\Users\<USER>\maxed_out_life\lib\quests\ns_dashboard.dart                           <C:\Users\<USER>\maxed_out_life\lib\quests\ns_exp_tracker.dart                           AC:\Users\<USER>\maxed_out_life\lib\quests\ns_glow_card_widget.dart                           8C:\Users\<USER>\maxed_out_life\lib\quests\ns_journal.dart                           ?C:\Users\<USER>\maxed_out_life\lib\quests\ns_progress_meter.dart                           =C:\Users\<USER>\maxed_out_life\lib\quests\ns_setup_widget.dart                           ]C:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\mxd\onboarding_tutorial_screen.dart                           ]C:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\mxd\onboarding_tutorial_screen.dart                           0C:\Users\<USER>\maxed_out_life\OPENAI_API_KEY.env                           7C:\Users\<USER>\maxed_out_life\assets\fonts\Orbitron.ttf                           QC:\Users\<USER>\maxed_out_life\build\ae30582a806dc4248fd237072391b5a0\outputs.json                           ]C:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\screens\past_challenges_screen.dart                           ]C:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\screens\past_challenges_screen.dart                           8C:\Users\<USER>\maxed_out_life\lib\widgets\perk_card.dart                           PC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\widgets\perk_card.dart                           PC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\widgets\perk_card.dart                           9C:\Users\<USER>\maxed_out_life\lib\engine\perk_engine.dart                           QC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\engine\perk_engine.dart                           QC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\engine\perk_engine.dart                           :C:\Users\<USER>\maxed_out_life\lib\engine\perk_engineX.dart                           CC:\Users\<USER>\maxed_out_life\lib\services\perk_export_service.dart                           [C:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\services\perk_export_service.dart                           [C:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\services\perk_export_service.dart                           AC:\Users\<USER>\maxed_out_life\lib\services\perk_icon_service.dart                           YC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\services\perk_icon_service.dart                           YC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\services\perk_icon_service.dart                           8C:\Users\<USER>\maxed_out_life\lib\models\perk_model.dart                           PC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\models\perk_model.dart                           PC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\models\perk_model.dart                           9C:\Users\<USER>\maxed_out_life\lib\widgets\perk_popup.dart                           QC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\widgets\perk_popup.dart                           QC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\widgets\perk_popup.dart                           <C:\Users\<USER>\maxed_out_life\lib\services\perk_storage.dart                           TC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\services\perk_storage.dart                           TC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\services\perk_storage.dart                           UC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\mxd\perk_unlock_dialog.dart                           UC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\mxd\perk_unlock_dialog.dart                           AC:\Users\<USER>\maxed_out_life\lib\screens\perk_viewer_screen.dart                           YC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\screens\perk_viewer_screen.dart                           YC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\screens\perk_viewer_screen.dart                           6C:\Users\<USER>\maxed_out_life\assets\fonts\Pirulen.otf                           VC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\mxd\progress_graph_card.dart                           VC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\mxd\progress_graph_card.dart                           BC:\Users\<USER>\maxed_out_life\ios\Runner.xcodeproj\project.pbxproj                           DC:\Users\<USER>\maxed_out_life\macos\Runner.xcodeproj\project.pbxproj                           6C:\Users\<USER>\maxed_out_life\.vs\ProjectSettings.json                           RC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\.vs\ProjectSettings.json                           *C:\Users\<USER>\maxed_out_life\pubspec.lock                           *C:\Users\<USER>\maxed_out_life\pubspec.yaml                           WC:\Users\<USER>\maxed_out_life\ios\Runner\Assets.xcassets\LaunchImage.imageset\README.md                           'C:\Users\<USER>\maxed_out_life\README.md                           ?C:\Users\<USER>\maxed_out_life\macos\Runner\Release.entitlements                           :C:\Users\<USER>\maxed_out_life\ios\Flutter\Release.xcconfig                           CC:\Users\<USER>\maxed_out_life\macos\Runner\Configs\Release.xcconfig                           7C:\Users\<USER>\maxed_out_life\windows\runner\resource.h                           TC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\mxd\rest_timer_widget.dart                           TC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\mxd\rest_timer_widget.dart                           AC:\Users\<USER>\maxed_out_life\ios\Runner\Runner-Bridging-Header.h                           @C:\Users\<USER>\maxed_out_life\windows\runner\runner.exe.manifest                           6C:\Users\<USER>\maxed_out_life\windows\runner\Runner.rc                           YC:\Users\<USER>\maxed_out_life\ios\Runner.xcodeproj\xcshareddata\xcschemes\Runner.xcscheme                           [C:\Users\<USER>\maxed_out_life\macos\Runner.xcodeproj\xcshareddata\xcschemes\Runner.xcscheme                           ?C:\Users\<USER>\maxed_out_life\ios\RunnerTests\RunnerTests.swift                           AC:\Users\<USER>\maxed_out_life\macos\RunnerTests\RunnerTests.swift                           XC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\mxd\session_detail_dialog.dart                           XC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\mxd\session_detail_dialog.dart                           9C:\Users\<USER>\maxed_out_life\android\settings.gradle.kts                           PC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\mxd\set_input_row.dart                           PC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\mxd\set_input_row.dart                           5C:\Users\<USER>\maxed_out_life\assets\fonts\SF-Pro.ttf                           9C:\Users\<USER>\maxed_out_life\lib\widgets\shp_button.dart                           MC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\mxd\shp_button.dart                           MC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\mxd\shp_button.dart                           BC:\Users\<USER>\maxed_out_life\lib\screens\skills_perks_screen.dart                           ZC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\screens\skills_perks_screen.dart                           ZC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\screens\skills_perks_screen.dart                           ?C:\Users\<USER>\maxed_out_life\lib\carena\solo_arena_screen.dart                           AC:\Users\<USER>\maxed_out_life\lib\spanish\spanish_controller.dart                           =C:\Users\<USER>\maxed_out_life\lib\widgets\speech_to_text.dart                           <C:\Users\<USER>\maxed_out_life\lib\screens\splash_screen.dart                           TC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\screens\splash_screen.dart                           TC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\screens\splash_screen.dart                           gC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\clone\widgets\start_from_template_button.dart                           fC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\clone\widgets\start_new_training_button.dart                           XC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\mxd\streak_counter_widget.dart                           XC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\mxd\streak_counter_widget.dart                           BC:\Users\<USER>\maxed_out_life\lib\dhabits\streak_meter_widget.dart                           NC:\Users\<USER>\maxed_out_life\android\app\src\main\res\values-night\styles.xml                           HC:\Users\<USER>\maxed_out_life\android\app\src\main\res\values\styles.xml                           7C:\Users\<USER>\maxed_out_life\assets\sounds\Success.MP3                           OC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\mxd\sync_service.dart                           OC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\mxd\sync_service.dart                           :C:\Users\<USER>\maxed_out_life\lib\widgets\top_summary.dart                           RC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\widgets\top_summary.dart                           RC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\widgets\top_summary.dart                           `C:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\clone\services\training_data_bulk.dart                           aC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\clone\services\training_data_entry.dart                           fC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\clone\services\training_session_manager.dart                           =C:\Users\<USER>\maxed_out_life\lib\screens\userlog_screen.dart                           UC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\screens\userlog_screen.dart                           UC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\screens\userlog_screen.dart                           8C:\Users\<USER>\maxed_out_life\lib\models\user_model.dart                           PC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\models\user_model.dart                           PC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\models\user_model.dart                           <C:\Users\<USER>\maxed_out_life\lib\services\user_service.dart                           TC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\services\user_service.dart                           TC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\services\user_service.dart                           6C:\Users\<USER>\maxed_out_life\windows\runner\utils.cpp                           4C:\Users\<USER>\maxed_out_life\windows\runner\utils.h                           SC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\.vs\VSWorkspaceState.json                           DC:\Users\<USER>\maxed_out_life\macos\Runner\Configs\Warnings.xcconfig                           DC:\Users\<USER>\maxed_out_life\lib\dhabits\weekly_habit_overview.dart                           DC:\Users\<USER>\maxed_out_life\lib\widgets\weekly_rituals_screen.dart                           \C:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\screens\weekly_rituals_screen.dart                           \C:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\widgets\weekly_rituals_screen.dart                           \C:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\screens\weekly_rituals_screen.dart                           \C:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\widgets\weekly_rituals_screen.dart                           3C:\Users\<USER>\maxed_out_life\test\widget_test.dart                           =C:\Users\<USER>\maxed_out_life\windows\runner\win32_window.cpp                           ;C:\Users\<USER>\maxed_out_life\windows\runner\win32_window.h                           _C:\Users\<USER>\maxed_out_life\Old Zip Files\- MOL Old Workout Darts\workout_builder_screen.dart                           FC:\Users\<USER>\maxed_out_life\WorkoutHub\workout_completion_logic.dart                           [C:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\models\workout_history_model.dart                           [C:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\models\workout_history_model.dart                           DC:\Users\<USER>\maxed_out_life\WorkoutHub\workout_history_screen.dart                           _C:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\services\workout_history_service.dart                           _C:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\services\workout_history_service.dart                           [C:\Users\<USER>\maxed_out_life\Old Zip Files\- MOL Old Workout Darts\workout_hub_screen.dart                           @C:\Users\<USER>\maxed_out_life\WorkoutHub\workout_hub_screen.dart                           SC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\models\workout_model.dart                           SC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\models\workout_model.dart                           YC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\engine\workout_perk_engine.dart                           YC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\engine\workout_perk_engine.dart                           YC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\mxd\workout_profile_screen.dart                           YC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\mxd\workout_profile_screen.dart                           VC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\workout_session_manager.dart                           [C:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\models\workout_session_model.dart                           WC:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\models\workout_set_model.dart                           WC:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\models\workout_set_model.dart                           DC:\Users\<USER>\maxed_out_life\WorkoutHub\workout_summary_dialog.dart                           \C:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\models\workout_template_model.dart                           \C:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\models\workout_template_model.dart                           iC:\Users\<USER>\maxed_out_life\Old Zip Files\- MOL Old Workout Darts\workout_template_selector_screen.dart                           `C:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\services\workout_template_service.dart                           `C:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\services\workout_template_service.dart                           _C:\Users\<USER>\maxed_out_life\Old Zip Files\- MOL Old Workout Darts\workout_tracker_screen.dart                           pC:\Users\<USER>\maxed_out_life\ios\Runner.xcodeproj\project.xcworkspace\xcshareddata\WorkspaceSettings.xcsettings                           ^C:\Users\<USER>\maxed_out_life\ios\Runner.xcworkspace\xcshareddata\WorkspaceSettings.xcsettings                           TC:\Users\<USER>\maxed_out_life\Old Zip Files\- MOL Old Workout Darts\wotm_screen.dart                           [C:\Users\<USER>\maxed_out_life\Old Zip Files\lib 3(Broken)\services\wo_perk_gen_service.dart                           [C:\Users\<USER>\maxed_out_life\Old Zip Files\lib2 (Broken)\services\wo_perk_gen_service.dart                           