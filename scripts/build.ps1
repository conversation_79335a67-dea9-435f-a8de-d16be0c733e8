# Maxed Out Life - Windows Build Script
# PowerShell script for building the app on Windows

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("android", "ios", "web", "windows", "macos", "linux", "all")]
    [string]$Platform = "all",

    [Parameter(Mandatory=$false)]
    [ValidateSet("debug", "profile", "release")]
    [string]$BuildType = "debug",

    [Parameter(Mandatory=$false)]
    [switch]$Clean,

    [Parameter(Mandatory=$false)]
    [switch]$VerboseOutput,

    [Parameter(Mandatory=$false)]
    [string]$Version,

    [Parameter(Mandatory=$false)]
    [string]$BuildNumber,

    [Parameter(Mandatory=$false)]
    [switch]$Help
)

# Colors for output
$Red = "Red"
$Green = "Green"
$Yellow = "Yellow"
$Blue = "Blue"

# Configuration
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$ProjectRoot = Split-Path -Parent $ScriptDir
$BuildDir = Join-Path $ProjectRoot "build"
$ReleaseDir = Join-Path $ProjectRoot "release"

function Write-Info {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor $Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor $Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor $Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor $Red
}

function Show-Help {
    Write-Host @"
Maxed Out Life Windows Build Script

Usage: .\build.ps1 [OPTIONS]

OPTIONS:
    -Platform PLATFORM      Platform to build (android|ios|web|windows|macos|linux|all)
    -BuildType TYPE         Build type (debug|profile|release)
    -Clean                  Clean build (remove previous build artifacts)
    -VerboseOutput          Verbose output
    -Version VERSION        Set version number
    -BuildNumber NUMBER     Set build number
    -Help                   Show this help message

EXAMPLES:
    .\build.ps1 -Platform android -BuildType release
    .\build.ps1 -Platform all -Clean
    .\build.ps1 -Platform web -BuildType release -Version "1.0.0"

PLATFORMS:
    android     Build Android APK and App Bundle
    ios         Build iOS app (requires macOS)
    web         Build Progressive Web App
    windows     Build Windows desktop app
    macos       Build macOS desktop app (requires macOS)
    linux       Build Linux desktop app (requires Linux)
    all         Build for all available platforms

"@
}

function Test-Flutter {
    if (-not (Get-Command flutter -ErrorAction SilentlyContinue)) {
        Write-Error "Flutter is not installed or not in PATH"
        exit 1
    }
    
    $flutterVersion = flutter --version | Select-Object -First 1
    Write-Info "Flutter version: $flutterVersion"
}

function Update-Dependencies {
    Write-Info "Checking dependencies..."
    
    Set-Location $ProjectRoot
    flutter pub get
    
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Failed to get dependencies"
        exit 1
    }
    
    Write-Success "Dependencies updated"
}

function Clear-BuildArtifacts {
    if ($Clean) {
        Write-Info "Cleaning previous build artifacts..."
        
        Set-Location $ProjectRoot
        flutter clean
        
        if (Test-Path $BuildDir) {
            Remove-Item $BuildDir -Recurse -Force
        }
        
        if (Test-Path $ReleaseDir) {
            Remove-Item $ReleaseDir -Recurse -Force
        }
        
        Write-Success "Build artifacts cleaned"
    }
}

function Update-Version {
    if ($Version -or $BuildNumber) {
        Write-Info "Updating version information..."
        
        Set-Location $ProjectRoot
        $pubspecPath = "pubspec.yaml"
        $content = Get-Content $pubspecPath
        
        if ($Version -and $BuildNumber) {
            $content = $content -replace "version: .*", "version: $Version+$BuildNumber"
        } elseif ($Version) {
            $content = $content -replace "version: ([^+]*)\+.*", "version: $Version+`$1"
        } elseif ($BuildNumber) {
            $content = $content -replace "version: ([^+]*)\+.*", "version: `$1+$BuildNumber"
        }
        
        $content | Set-Content $pubspecPath
        
        $newVersion = ($content | Where-Object { $_ -match "version:" }) -replace "version: ", ""
        Write-Success "Version updated to $newVersion"
    }
}

function Build-Android {
    Write-Info "Building Android app..."
    
    Set-Location $ProjectRoot
    
    # Check if Android SDK is available
    $androidCheck = flutter doctor | Select-String "Android toolchain"
    if (-not $androidCheck) {
        Write-Warning "Android toolchain not properly configured"
        return $false
    }
    
    # Build APK
    Write-Info "Building APK..."
    if ($BuildType -eq "release") {
        flutter build apk --release --split-per-abi
    } else {
        flutter build apk --$BuildType
    }
    
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Android APK build failed"
        return $false
    }
    
    # Build App Bundle for release
    if ($BuildType -eq "release") {
        Write-Info "Building App Bundle..."
        flutter build appbundle --release
        
        if ($LASTEXITCODE -ne 0) {
            Write-Error "Android App Bundle build failed"
            return $false
        }
    }
    
    # Copy artifacts to release directory
    $androidReleaseDir = Join-Path $ReleaseDir "android"
    New-Item -ItemType Directory -Force -Path $androidReleaseDir | Out-Null
    
    $apkPath = Join-Path $ProjectRoot "build\app\outputs\flutter-apk"
    if (Test-Path $apkPath) {
        Copy-Item "$apkPath\*" $androidReleaseDir -Recurse -Force
    }
    
    $bundlePath = Join-Path $ProjectRoot "build\app\outputs\bundle\release"
    if (Test-Path $bundlePath) {
        Copy-Item "$bundlePath\*" $androidReleaseDir -Recurse -Force
    }
    
    Write-Success "Android build completed"
    return $true
}

function Build-Web {
    Write-Info "Building Web app..."
    
    Set-Location $ProjectRoot
    
    # Build web app
    if ($BuildType -eq "release") {
        flutter build web --release
    } else {
        flutter build web --$BuildType
    }
    
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Web build failed"
        return $false
    }
    
    # Copy artifacts to release directory
    $webReleaseDir = Join-Path $ReleaseDir "web"
    New-Item -ItemType Directory -Force -Path $webReleaseDir | Out-Null
    
    $webBuildPath = Join-Path $ProjectRoot "build\web"
    if (Test-Path $webBuildPath) {
        Copy-Item "$webBuildPath\*" $webReleaseDir -Recurse -Force
    }
    
    Write-Success "Web build completed"
    return $true
}

function Build-Windows {
    Write-Info "Building Windows app..."
    
    Set-Location $ProjectRoot
    
    # Enable Windows desktop
    flutter config --enable-windows-desktop
    
    # Build Windows app
    if ($BuildType -eq "release") {
        flutter build windows --release
    } else {
        flutter build windows --$BuildType
    }
    
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Windows build failed"
        return $false
    }
    
    # Copy artifacts to release directory
    $windowsReleaseDir = Join-Path $ReleaseDir "windows"
    New-Item -ItemType Directory -Force -Path $windowsReleaseDir | Out-Null
    
    $windowsBuildPath = Join-Path $ProjectRoot "build\windows\x64\runner\Release"
    if (Test-Path $windowsBuildPath) {
        Copy-Item "$windowsBuildPath\*" $windowsReleaseDir -Recurse -Force
    } else {
        $windowsBuildPath = Join-Path $ProjectRoot "build\windows\x64\runner\Debug"
        if (Test-Path $windowsBuildPath) {
            Copy-Item "$windowsBuildPath\*" $windowsReleaseDir -Recurse -Force
        }
    }
    
    Write-Success "Windows build completed"
    return $true
}

function Build-All {
    Write-Info "Building for all available platforms..."
    
    $failedBuilds = @()
    
    # Try to build for each platform
    if (-not (Build-Android)) { $failedBuilds += "android" }
    if (-not (Build-Web)) { $failedBuilds += "web" }
    if (-not (Build-Windows)) { $failedBuilds += "windows" }
    
    if ($failedBuilds.Count -eq 0) {
        Write-Success "All builds completed successfully"
        return $true
    } else {
        Write-Warning "Some builds failed: $($failedBuilds -join ', ')"
        return $false
    }
}

# Main execution
if ($Help) {
    Show-Help
    exit 0
}

Write-Info "Starting Maxed Out Life build process..."
Write-Info "Platform: $Platform"
Write-Info "Build type: $BuildType"

Test-Flutter
Update-Dependencies
Clear-BuildArtifacts
Update-Version

# Build based on platform
$buildSuccess = $false

switch ($Platform) {
    "android" { $buildSuccess = Build-Android }
    "web" { $buildSuccess = Build-Web }
    "windows" { $buildSuccess = Build-Windows }
    "all" { $buildSuccess = Build-All }
    default {
        Write-Error "Platform '$Platform' is not supported on Windows"
        Write-Info "Supported platforms: android, web, windows, all"
        exit 1
    }
}

if ($buildSuccess) {
    Write-Success "Build process completed!"
    Write-Info "Build artifacts are available in: $ReleaseDir"
} else {
    Write-Error "Build process failed!"
    exit 1
}
