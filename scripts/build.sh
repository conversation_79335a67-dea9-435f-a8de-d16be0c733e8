#!/bin/bash

# Maxed Out Life - Build Script
# Comprehensive build script for all platforms

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
BUILD_DIR="$PROJECT_ROOT/build"
RELEASE_DIR="$PROJECT_ROOT/release"

# Default values
PLATFORM="all"
BUILD_TYPE="debug"
CLEAN_BUILD=false
VERBOSE=false
VERSION=""
BUILD_NUMBER=""

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

show_help() {
    cat << EOF
Maxed Out Life Build Script

Usage: $0 [OPTIONS]

OPTIONS:
    -p, --platform PLATFORM    Platform to build (android|ios|web|windows|macos|linux|all)
    -t, --type TYPE            Build type (debug|profile|release)
    -c, --clean                Clean build (remove previous build artifacts)
    -v, --verbose              Verbose output
    -V, --version VERSION      Set version number
    -b, --build-number NUMBER  Set build number
    -h, --help                 Show this help message

EXAMPLES:
    $0 --platform android --type release
    $0 --platform all --clean
    $0 --platform web --type release --version 1.0.0

PLATFORMS:
    android     Build Android APK and App Bundle
    ios         Build iOS app (requires macOS)
    web         Build Progressive Web App
    windows     Build Windows desktop app (requires Windows)
    macos       Build macOS desktop app (requires macOS)
    linux       Build Linux desktop app (requires Linux)
    all         Build for all available platforms

EOF
}

check_flutter() {
    if ! command -v flutter &> /dev/null; then
        log_error "Flutter is not installed or not in PATH"
        exit 1
    fi
    
    log_info "Flutter version: $(flutter --version | head -n 1)"
}

check_dependencies() {
    log_info "Checking dependencies..."
    
    cd "$PROJECT_ROOT"
    flutter pub get
    
    if [ $? -ne 0 ]; then
        log_error "Failed to get dependencies"
        exit 1
    fi
    
    log_success "Dependencies updated"
}

clean_build() {
    if [ "$CLEAN_BUILD" = true ]; then
        log_info "Cleaning previous build artifacts..."
        
        cd "$PROJECT_ROOT"
        flutter clean
        
        if [ -d "$BUILD_DIR" ]; then
            rm -rf "$BUILD_DIR"
        fi
        
        if [ -d "$RELEASE_DIR" ]; then
            rm -rf "$RELEASE_DIR"
        fi
        
        log_success "Build artifacts cleaned"
    fi
}

update_version() {
    if [ -n "$VERSION" ] || [ -n "$BUILD_NUMBER" ]; then
        log_info "Updating version information..."
        
        cd "$PROJECT_ROOT"
        
        if [ -n "$VERSION" ] && [ -n "$BUILD_NUMBER" ]; then
            sed -i.bak "s/version: .*/version: $VERSION+$BUILD_NUMBER/" pubspec.yaml
        elif [ -n "$VERSION" ]; then
            sed -i.bak "s/version: \([^+]*\).*/version: $VERSION+\1/" pubspec.yaml
        elif [ -n "$BUILD_NUMBER" ]; then
            sed -i.bak "s/version: \([^+]*\)+.*/version: \1+$BUILD_NUMBER/" pubspec.yaml
        fi
        
        log_success "Version updated to $(grep 'version:' pubspec.yaml | cut -d' ' -f2)"
    fi
}

build_android() {
    log_info "Building Android app..."
    
    cd "$PROJECT_ROOT"
    
    # Check if Android SDK is available
    if ! flutter doctor | grep -q "Android toolchain"; then
        log_warning "Android toolchain not properly configured"
        return 1
    fi
    
    # Build APK
    log_info "Building APK..."
    if [ "$BUILD_TYPE" = "release" ]; then
        flutter build apk --release --split-per-abi
    else
        flutter build apk --$BUILD_TYPE
    fi
    
    # Build App Bundle for release
    if [ "$BUILD_TYPE" = "release" ]; then
        log_info "Building App Bundle..."
        flutter build appbundle --release
    fi
    
    # Copy artifacts to release directory
    mkdir -p "$RELEASE_DIR/android"
    cp -r build/app/outputs/flutter-apk/* "$RELEASE_DIR/android/" 2>/dev/null || true
    cp -r build/app/outputs/bundle/release/* "$RELEASE_DIR/android/" 2>/dev/null || true
    
    log_success "Android build completed"
}

build_ios() {
    log_info "Building iOS app..."
    
    if [[ "$OSTYPE" != "darwin"* ]]; then
        log_warning "iOS builds require macOS"
        return 1
    fi
    
    cd "$PROJECT_ROOT"
    
    # Check if iOS toolchain is available
    if ! flutter doctor | grep -q "Xcode"; then
        log_warning "Xcode not properly configured"
        return 1
    fi
    
    # Build iOS app
    if [ "$BUILD_TYPE" = "release" ]; then
        flutter build ios --release --no-codesign
    else
        flutter build ios --$BUILD_TYPE --no-codesign
    fi
    
    # Copy artifacts to release directory
    mkdir -p "$RELEASE_DIR/ios"
    cp -r build/ios/iphoneos/Runner.app "$RELEASE_DIR/ios/" 2>/dev/null || true
    
    log_success "iOS build completed"
}

build_web() {
    log_info "Building Web app..."
    
    cd "$PROJECT_ROOT"
    
    # Build web app
    if [ "$BUILD_TYPE" = "release" ]; then
        flutter build web --release
    else
        flutter build web --$BUILD_TYPE
    fi
    
    # Copy artifacts to release directory
    mkdir -p "$RELEASE_DIR/web"
    cp -r build/web/* "$RELEASE_DIR/web/"
    
    log_success "Web build completed"
}

build_windows() {
    log_info "Building Windows app..."
    
    if [[ "$OSTYPE" != "msys" && "$OSTYPE" != "cygwin" ]]; then
        log_warning "Windows builds require Windows"
        return 1
    fi
    
    cd "$PROJECT_ROOT"
    
    # Enable Windows desktop
    flutter config --enable-windows-desktop
    
    # Build Windows app
    if [ "$BUILD_TYPE" = "release" ]; then
        flutter build windows --release
    else
        flutter build windows --$BUILD_TYPE
    fi
    
    # Copy artifacts to release directory
    mkdir -p "$RELEASE_DIR/windows"
    cp -r build/windows/x64/runner/Release/* "$RELEASE_DIR/windows/" 2>/dev/null || true
    cp -r build/windows/x64/runner/Debug/* "$RELEASE_DIR/windows/" 2>/dev/null || true
    
    log_success "Windows build completed"
}

build_macos() {
    log_info "Building macOS app..."
    
    if [[ "$OSTYPE" != "darwin"* ]]; then
        log_warning "macOS builds require macOS"
        return 1
    fi
    
    cd "$PROJECT_ROOT"
    
    # Enable macOS desktop
    flutter config --enable-macos-desktop
    
    # Build macOS app
    if [ "$BUILD_TYPE" = "release" ]; then
        flutter build macos --release
    else
        flutter build macos --$BUILD_TYPE
    fi
    
    # Copy artifacts to release directory
    mkdir -p "$RELEASE_DIR/macos"
    cp -r build/macos/Build/Products/Release/maxed_out_life.app "$RELEASE_DIR/macos/" 2>/dev/null || true
    cp -r build/macos/Build/Products/Debug/maxed_out_life.app "$RELEASE_DIR/macos/" 2>/dev/null || true
    
    log_success "macOS build completed"
}

build_linux() {
    log_info "Building Linux app..."
    
    if [[ "$OSTYPE" != "linux-gnu"* ]]; then
        log_warning "Linux builds require Linux"
        return 1
    fi
    
    cd "$PROJECT_ROOT"
    
    # Check for required dependencies
    if ! command -v cmake &> /dev/null; then
        log_error "cmake is required for Linux builds"
        return 1
    fi
    
    # Enable Linux desktop
    flutter config --enable-linux-desktop
    
    # Build Linux app
    if [ "$BUILD_TYPE" = "release" ]; then
        flutter build linux --release
    else
        flutter build linux --$BUILD_TYPE
    fi
    
    # Copy artifacts to release directory
    mkdir -p "$RELEASE_DIR/linux"
    cp -r build/linux/x64/release/bundle/* "$RELEASE_DIR/linux/" 2>/dev/null || true
    
    log_success "Linux build completed"
}

build_all() {
    log_info "Building for all available platforms..."
    
    local failed_builds=()
    
    # Try to build for each platform
    build_android || failed_builds+=("android")
    build_ios || failed_builds+=("ios")
    build_web || failed_builds+=("web")
    build_windows || failed_builds+=("windows")
    build_macos || failed_builds+=("macos")
    build_linux || failed_builds+=("linux")
    
    if [ ${#failed_builds[@]} -eq 0 ]; then
        log_success "All builds completed successfully"
    else
        log_warning "Some builds failed: ${failed_builds[*]}"
    fi
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -p|--platform)
            PLATFORM="$2"
            shift 2
            ;;
        -t|--type)
            BUILD_TYPE="$2"
            shift 2
            ;;
        -c|--clean)
            CLEAN_BUILD=true
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -V|--version)
            VERSION="$2"
            shift 2
            ;;
        -b|--build-number)
            BUILD_NUMBER="$2"
            shift 2
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Validate build type
if [[ ! "$BUILD_TYPE" =~ ^(debug|profile|release)$ ]]; then
    log_error "Invalid build type: $BUILD_TYPE"
    exit 1
fi

# Main execution
log_info "Starting Maxed Out Life build process..."
log_info "Platform: $PLATFORM"
log_info "Build type: $BUILD_TYPE"

check_flutter
check_dependencies
clean_build
update_version

# Build based on platform
case $PLATFORM in
    android)
        build_android
        ;;
    ios)
        build_ios
        ;;
    web)
        build_web
        ;;
    windows)
        build_windows
        ;;
    macos)
        build_macos
        ;;
    linux)
        build_linux
        ;;
    all)
        build_all
        ;;
    *)
        log_error "Invalid platform: $PLATFORM"
        show_help
        exit 1
        ;;
esac

log_success "Build process completed!"
log_info "Build artifacts are available in: $RELEASE_DIR"
