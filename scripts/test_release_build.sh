#!/bin/bash

# 📁 scripts/test_release_build.sh
# Script to test release build and validate no debug elements are visible

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Get script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

echo "🚀 MXD Release Build Validation"
echo "================================"
echo ""

# Change to project directory
cd "$PROJECT_ROOT"

# Check if Flutter is available
if ! command -v flutter &> /dev/null; then
    log_error "Flutter is not installed or not in PATH"
    exit 1
fi

log_info "Flutter version:"
flutter --version
echo ""

# Clean previous builds
log_info "Cleaning previous builds..."
flutter clean
flutter pub get

# Build iOS release
log_info "Building iOS release..."
if flutter build ios --release --no-codesign; then
    log_success "iOS release build completed successfully"
else
    log_error "iOS release build failed"
    exit 1
fi

# Validate release configuration
log_info "Validating release configuration..."

# Create a temporary test file to run validation
cat > temp_validation_test.dart << 'EOF'
import 'lib/debug/release_validation_test.dart';

void main() async {
  final result = await ReleaseValidationTest.validateReleaseConfiguration();
  ReleaseValidationTest.printValidationResults(result);
  
  if (!result.isValid) {
    exit(1);
  }
}
EOF

# Run validation test
if dart temp_validation_test.dart; then
    log_success "Release validation passed"
else
    log_error "Release validation failed"
    rm -f temp_validation_test.dart
    exit 1
fi

# Clean up
rm -f temp_validation_test.dart

echo ""
log_success "Release build validation completed successfully!"
log_info "The app is ready for App Store submission"
echo ""
echo "📱 Next steps:"
echo "1. Take new screenshots from the release build"
echo "2. Verify no debug overlays are visible in screenshots"
echo "3. Submit to App Store Connect"
