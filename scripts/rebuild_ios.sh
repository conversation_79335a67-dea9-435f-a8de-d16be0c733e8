#!/bin/bash

# <PERSON><PERSON>t to clean and rebuild iOS project
set -e

echo "🧹 Cleaning Flutter build..."
flutter clean

echo "🗑️ Removing Pods directory..."
rm -rf ios/Pods
rm -rf ios/Podfile.lock

echo "📦 Getting Flutter dependencies..."
flutter pub get

echo "🔄 Installing CocoaPods dependencies..."
cd ios
pod install
cd ..

echo "🏗️ Building iOS app..."
flutter build ios --release

echo "✅ iOS build completed successfully!"
echo ""
echo "To archive for App Store submission:"
echo "1. Open the Xcode workspace: open ios/Runner.xcworkspace"
echo "2. Select 'Product > Archive' from the menu"
echo "3. Once archiving is complete, click 'Distribute App'"
echo ""
