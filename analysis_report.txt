Analyzing maxed_out_life...                                     

   info • The private field _isInitialized could be 'final' • lib/bulletproof/bulletproof_manager.dart:69:8 • prefer_final_fields
   info • Parameter 'key' could be a super parameter • lib/bulletproof/monitoring_dashboard.dart:11:9 • use_super_parameters
warning • Unused import: 'package:flutter/foundation.dart' • lib/controller/user_controller2.dart:1:8 • unused_import
   info • The member 'dispose' overrides an inherited member but isn't annotated with '@override' • lib/controller/user_controller2.dart:50:8 • annotate_overrides
warning • This method overrides a method annotated as '@mustCallSuper' in 'ChangeNotifier', but doesn't invoke the overridden method • lib/controller/user_controller2.dart:50:8 • must_call_super
   info • Parameter 'key' could be a super parameter • lib/cstcat/custom_category_setup_widget.dart:15:9 • use_super_parameters
   info • Don't use 'BuildContext's across async gaps • lib/cstcat/custom_category_setup_widget.dart:109:25 • use_build_context_synchronously
   info • Parameter 'key' could be a super parameter • lib/cstcat/first_time_user_flow.dart:13:9 • use_super_parameters
warning • A value for optional parameter 'headerBackgroundColor' isn't ever given • lib/design_system/components/app_card.dart:267:10 • unused_element_parameter
warning • A value for optional parameter 'footerBackgroundColor' isn't ever given • lib/design_system/components/app_card.dart:268:10 • unused_element_parameter
warning • A value for optional parameter 'headerBorder' isn't ever given • lib/design_system/components/app_card.dart:273:10 • unused_element_parameter
warning • A value for optional parameter 'footerBorder' isn't ever given • lib/design_system/components/app_card.dart:274:10 • unused_element_parameter
   info • Library names are not necessary • lib/design_system/design_system.dart:5:9 • unnecessary_library_name
   info • Parameter 'key' could be a super parameter • lib/dhabits/complete_habits_widget.dart:13:9 • use_super_parameters
   info • Parameter 'key' could be a super parameter • lib/dhabits/daily_habits_onboarding.dart:12:9 • use_super_parameters
   info • The private field _habits could be 'final' • lib/dhabits/daily_habits_onboarding.dart:19:16 • prefer_final_fields
   info • Statements in a for should be enclosed in a block • lib/dhabits/daily_habits_onboarding.dart:54:35 • curly_braces_in_flow_control_structures
   info • Use interpolation to compose strings and values • lib/dhabits/daily_habits_onboarding.dart:89:15 • prefer_interpolation_to_compose_strings
   info • Parameter 'key' could be a super parameter • lib/dhabits/exp_tally.dart:10:9 • use_super_parameters
   info • Parameter 'key' could be a super parameter • lib/dhabits/streak_meter_widget.dart:10:9 • use_super_parameters
   info • Parameter 'key' could be a super parameter • lib/dhabits/weekly_habit_overview.dart:9:9 • use_super_parameters
warning • The operand can't be 'null', so the condition is always 'false' • lib/diary/ns_journal_xl.dart:215:48 • unnecessary_null_comparison
   info • Parameter 'key' could be a super parameter • lib/diary/ns_journal_xl.dart:311:9 • use_super_parameters
   info • Invalid use of a private type in a public API • lib/diary/ns_journal_xl.dart:318:3 • library_private_types_in_public_api
   info • Parameter 'key' could be a super parameter • lib/diary/super_feed.dart:16:9 • use_super_parameters
   info • Invalid use of a private type in a public API • lib/diary/super_feed.dart:30:3 • library_private_types_in_public_api
   info • Parameter 'key' could be a super parameter • lib/gtape/widgets/guardian_product_card.dart:14:9 • use_super_parameters
   info • 'canLaunch' is deprecated and shouldn't be used. Use canLaunchUrl instead • lib/gtape/widgets/guardian_product_card.dart:52:15 • deprecated_member_use
   info • 'launch' is deprecated and shouldn't be used. Use launchUrl instead • lib/gtape/widgets/guardian_product_card.dart:53:13 • deprecated_member_use
   info • Parameter 'key' could be a super parameter • lib/gtape/widgets/guardian_product_carousel.dart:15:9 • use_super_parameters
   info • The import of 'package:flutter/services.dart' is unnecessary because all of the used elements are also provided by the import of 'package:flutter/material.dart' • lib/gtape/widgets/guardian_reward_banner.dart:2:8 • unnecessary_import
   info • Parameter 'key' could be a super parameter • lib/gtape/widgets/guardian_reward_banner.dart:16:9 • use_super_parameters
   info • Don't use 'BuildContext's across async gaps • lib/home/<USER>
   info • Don't use 'BuildContext's across async gaps • lib/home/<USER>
   info • Don't use 'BuildContext's across async gaps • lib/home/<USER>
   info • Don't use 'BuildContext's across async gaps • lib/home/<USER>
   info • Don't use 'BuildContext's across async gaps • lib/home/<USER>
warning • Unused import: 'theme/colors.dart' • lib/main.dart:5:8 • unused_import
   info • 'value' is deprecated and shouldn't be used. Use component accessors like .r or .g, or toARGB32 for an explicit conversion • lib/models/habit_model.dart:113:20 • deprecated_member_use
   info • Parameter 'key' could be a super parameter • lib/mxdadmin/admin_screen.dart:22:9 • use_super_parameters
   info • Parameter 'key' could be a super parameter • lib/mxdadmin/custom_category_section.dart:12:9 • use_super_parameters
   info • Parameter 'key' could be a super parameter • lib/mxdadmin/development_tools_section.dart:11:9 • use_super_parameters
   info • Don't use 'BuildContext's across async gaps • lib/mxdadmin/development_tools_section.dart:90:34 • use_build_context_synchronously
   info • Parameter 'key' could be a super parameter • lib/mxdadmin/user_tools_section.dart:16:9 • use_super_parameters
   info • Don't use 'BuildContext's across async gaps • lib/mxdadmin/user_tools_section.dart:122:70 • use_build_context_synchronously
warning • The member 'errorHandler' can only be used within instance members of subclasses of 'package:maxed_out_life/controller/user_controller_base.dart' • lib/mxdadmin/user_tools_section.dart:122:94 • invalid_use_of_protected_member
   info • Don't use 'BuildContext's across async gaps • lib/mxdadmin/user_tools_section.dart:128:30 • use_build_context_synchronously
   info • Don't use 'BuildContext's across async gaps • lib/mxdadmin/user_tools_section.dart:202:33 • use_build_context_synchronously
   info • Don't use 'BuildContext's across async gaps • lib/mxdadmin/user_tools_section.dart:204:26 • use_build_context_synchronously
warning • The member 'errorHandler' can only be used within instance members of subclasses of 'package:maxed_out_life/controller/user_controller_base.dart' • lib/mxdadmin/user_tools_section.dart:218:90 • invalid_use_of_protected_member
   info • Don't use 'BuildContext's across async gaps • lib/mxdadmin/user_tools_section.dart:222:33 • use_build_context_synchronously
   info • Don't use 'BuildContext's across async gaps • lib/mxdadmin/user_tools_section.dart:224:26 • use_build_context_synchronously
   info • Don't use 'BuildContext's across async gaps • lib/mxdadmin/user_tools_section.dart:265:27 • use_build_context_synchronously
   info • The file name '02_onboarding_screen.dart' isn't a lower_case_with_underscores identifier • lib/onboarding/02_onboarding_screen.dart:1:1 • file_names
   info • Parameter 'key' could be a super parameter • lib/onboarding/02_onboarding_screen.dart:9:9 • use_super_parameters
   info • The file name '03_welcome_start.dart' isn't a lower_case_with_underscores identifier • lib/onboarding/03_welcome_start.dart:1:1 • file_names
   info • Parameter 'key' could be a super parameter • lib/onboarding/03_welcome_start.dart:11:9 • use_super_parameters
   info • The file name '04_north_star_setup.dart' isn't a lower_case_with_underscores identifier • lib/onboarding/04_north_star_setup.dart:1:1 • file_names
   info • Unnecessary use of 'toList' in a spread • lib/onboarding/04_north_star_setup.dart:417:72 • unnecessary_to_list_in_spreads
   info • The file name '05_onboarding_page.dart' isn't a lower_case_with_underscores identifier • lib/onboarding/05_onboarding_page.dart:1:1 • file_names
   info • Parameter 'key' could be a super parameter • lib/onboarding/05_onboarding_page.dart:14:9 • use_super_parameters
   info • Unnecessary use of 'toList' in a spread • lib/onboarding/05_onboarding_page.dart:117:20 • unnecessary_to_list_in_spreads
   info • The file name '06_dashboard.dart' isn't a lower_case_with_underscores identifier • lib/onboarding/06_dashboard.dart:1:1 • file_names
   info • Parameter 'key' could be a super parameter • lib/onboarding/06_dashboard.dart:11:9 • use_super_parameters
   info • Unnecessary use of 'toList' in a spread • lib/onboarding/06_dashboard.dart:268:12 • unnecessary_to_list_in_spreads
   info • Unnecessary use of 'toList' in a spread • lib/onboarding/06_dashboard.dart:298:12 • unnecessary_to_list_in_spreads
   info • Function literals shouldn't be passed to 'forEach' • lib/performance/optimized_storage.dart:339:25 • avoid_function_literals_in_foreach_calls
   info • Function literals shouldn't be passed to 'forEach' • lib/performance/optimized_storage.dart:383:19 • avoid_function_literals_in_foreach_calls
   info • Function literals shouldn't be passed to 'forEach' • lib/performance/optimized_storage.dart:392:25 • avoid_function_literals_in_foreach_calls
   info • Use a 'SizedBox' to add whitespace to a layout • lib/performance/performance_dashboard.dart:43:16 • sized_box_for_whitespace
   info • Unnecessary use of 'toList' in a spread • lib/performance/performance_dashboard.dart:340:14 • unnecessary_to_list_in_spreads
   info • Unnecessary use of 'toList' in a spread • lib/performance/performance_dashboard.dart:407:14 • unnecessary_to_list_in_spreads
warning • Unused import: 'package:flutter/services.dart' • lib/performance/performance_initializer.dart:3:8 • unused_import
   info • The imported package 'flutter_test' isn't a dependency of the importing package • lib/prompts/chat_history_test.dart:1:8 • depend_on_referenced_packages
   info • Parameter 'key' could be a super parameter • lib/prompts/chat_screen.dart:8:9 • use_super_parameters
   info • The imported package 'flutter_test' isn't a dependency of the importing package • lib/prompts/message_generator_test.dart:1:8 • depend_on_referenced_packages
   info • 'androidAllowWhileIdle' is deprecated and shouldn't be used. Deprecated in favor of the androidScheduleMode parameter • lib/prompts/notification_scheduler.dart:60:17 • deprecated_member_use
   info • The imported package 'flutter_test' isn't a dependency of the importing package • lib/prompts/notification_scheduler_test.dart:1:8 • depend_on_referenced_packages
   info • Dangling library doc comment • lib/quests/mary&ryan'sjourney.dart:1:1 • dangling_library_doc_comments
   info • The file name 'mary&ryan'sjourney.dart' isn't a lower_case_with_underscores identifier • lib/quests/mary&ryan'sjourney.dart:1:1 • file_names
   info • 'value' is deprecated and shouldn't be used. Use component accessors like .r or .g, or toARGB32 for an explicit conversion • lib/quests/north_star_model.dart:118:28 • deprecated_member_use
   info • Parameter 'key' could be a super parameter • lib/quests/ns_glow_card_v2.dart:9:9 • use_super_parameters
   info • Parameter 'key' could be a super parameter • lib/quests/ns_progress_meter.dart:10:9 • use_super_parameters
   info • Parameter 'key' could be a super parameter • lib/quests/ns_quest_button.dart:8:9 • use_super_parameters
   info • Parameter 'key' could be a super parameter • lib/quests/ns_tracker.dart:11:9 • use_super_parameters
   info • The file name '7dd_screen.dart' isn't a lower_case_with_underscores identifier • lib/screens/7dd_screen.dart:1:1 • file_names
   info • Parameter 'key' could be a super parameter • lib/screens/7dd_screen.dart:15:9 • use_super_parameters
   info • Parameter 'key' could be a super parameter • lib/screens/7dd_screen.dart:130:9 • use_super_parameters
   info • Parameter 'key' could be a super parameter • lib/screens/auth_screen.dart:5:9 • use_super_parameters
   info • Parameter 'key' could be a super parameter • lib/screens/category_manager_screen.dart:12:9 • use_super_parameters
   info • Parameter 'key' could be a super parameter • lib/screens/coach_chat_screen.dart:43:9 • use_super_parameters
   info • Unnecessary braces in a string interpolation • lib/screens/coach_chat_screen.dart:154:24 • unnecessary_brace_in_string_interps
   info • Unnecessary braces in a string interpolation • lib/screens/coach_chat_screen.dart:154:97 • unnecessary_brace_in_string_interps
   info • Parameter 'key' could be a super parameter • lib/screens/countdown_timer.dart:17:9 • use_super_parameters
   info • Don't use 'BuildContext's across async gaps • lib/screens/countdown_timer.dart:65:20 • use_build_context_synchronously
   info • Parameter 'key' could be a super parameter • lib/screens/exp_entry_screen.dart:24:9 • use_super_parameters
   info • Don't use 'BuildContext's across async gaps • lib/screens/exp_entry_screen.dart:135:25 • use_build_context_synchronously
   info • Don't use 'BuildContext's across async gaps • lib/screens/exp_entry_screen.dart:144:9 • use_build_context_synchronously
   info • Don't use 'BuildContext's across async gaps • lib/screens/exp_entry_screen.dart:153:21 • use_build_context_synchronously
   info • Don't use 'BuildContext's across async gaps • lib/screens/exp_entry_screen.dart:252:26 • use_build_context_synchronously
   info • Parameter 'key' could be a super parameter • lib/screens/full_timer_screen.dart:14:9 • use_super_parameters
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/screens/home_screen.dart:153:62 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/screens/home_screen.dart:208:50 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/screens/home_screen.dart:249:30 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/screens/home_screen.dart:250:49 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/screens/home_screen.dart:278:52 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/screens/home_screen.dart:369:50 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/screens/home_screen.dart:395:30 • deprecated_member_use
   info • 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss • lib/screens/home_screen.dart:396:49 • deprecated_member_use
   info • Unnecessary use of 'toList' in a spread • lib/screens/home_screen3.dart:99:31 • unnecessary_to_list_in_spreads
   info • The 'child' argument should be last in widget constructor invocations • lib/screens/home_screen3.dart:228:13 • sort_child_properties_last
   info • Parameter 'key' could be a super parameter • lib/screens/set_oswidgets.dart:14:9 • use_super_parameters
   info • Don't use 'BuildContext's across async gaps • lib/screens/set_oswidgets.dart:130:37 • use_build_context_synchronously
   info • Don't use 'BuildContext's across async gaps • lib/screens/set_oswidgets.dart:131:44 • use_build_context_synchronously
   info • Don't use 'BuildContext's across async gaps, guarded by an unrelated 'mounted' check • lib/security/screens/security_settings_screen.dart:671:38 • use_build_context_synchronously
   info • Invalid regular expression syntax • lib/security/security_utils.dart:246:28 • valid_regexps
   info • Don't use 'BuildContext's across async gaps • lib/security/widgets/biometric_auth_widget.dart:440:28 • use_build_context_synchronously
   info • Statements in an if should be enclosed in a block • lib/services/music_service.dart:70:21 • curly_braces_in_flow_control_structures
   info • Statements in an if should be enclosed in a block • lib/services/music_service.dart:71:10 • curly_braces_in_flow_control_structures
   info • Statements in an if should be enclosed in a block • lib/topbar/milestone_notches_painter.dart:46:21 • curly_braces_in_flow_control_structures
   info • Statements in an if should be enclosed in a block • lib/topbar/milestone_notches_painter.dart:47:14 • curly_braces_in_flow_control_structures
   info • Parameter 'key' could be a super parameter • lib/topbar/particle_burst.dart:8:9 • use_super_parameters
   info • Invalid use of a private type in a public API • lib/topbar/particle_burst.dart:15:3 • library_private_types_in_public_api
   info • Angle brackets will be interpreted as HTML • lib/topbar/progress_quip.dart:7:33 • unintended_html_in_doc_comment
   info • Parameter 'key' could be a super parameter • lib/topbar/progress_quip.dart:11:9 • use_super_parameters
   info • Parameter 'key' could be a super parameter • lib/topbar/tooltip_manager.dart:7:9 • use_super_parameters
   info • Parameter 'key' could be a super parameter • lib/topbar/totalexpbar.dart:32:9 • use_super_parameters
   info • Parameter 'key' could be a super parameter • lib/topbar/xp_ticker_overlay.dart:8:9 • use_super_parameters
   info • Invalid use of a private type in a public API • lib/topbar/xp_ticker_overlay.dart:16:3 • library_private_types_in_public_api
   info • Don't use 'BuildContext's across async gaps • lib/utils/audio_popup_utils.dart:24:7 • use_build_context_synchronously
   info • Don't use 'BuildContext's across async gaps • lib/utils/audio_popup_utils.dart:43:7 • use_build_context_synchronously
   info • Angle brackets will be interpreted as HTML • lib/utils/dperfecter_util.dart:21:17 • unintended_html_in_doc_comment
   info • Angle brackets will be interpreted as HTML • lib/utils/dperfecter_util.dart:41:28 • unintended_html_in_doc_comment
   info • Angle brackets will be interpreted as HTML • lib/utils/dperfecter_util.dart:42:42 • unintended_html_in_doc_comment
   info • Angle brackets will be interpreted as HTML • lib/utils/rank_utils.dart:90:27 • unintended_html_in_doc_comment
   info • Angle brackets will be interpreted as HTML • lib/utils/rank_utils.dart:91:25 • unintended_html_in_doc_comment
   info • Angle brackets will be interpreted as HTML • lib/utils/rank_utils.dart:92:23 • unintended_html_in_doc_comment
   info • Angle brackets will be interpreted as HTML • lib/utils/rank_utils.dart:93:25 • unintended_html_in_doc_comment
   info • Angle brackets will be interpreted as HTML • lib/utils/rank_utils.dart:94:26 • unintended_html_in_doc_comment
   info • Angle brackets will be interpreted as HTML • lib/utils/rank_utils.dart:95:25 • unintended_html_in_doc_comment
   info • Angle brackets will be interpreted as HTML • lib/utils/rank_utils.dart:96:25 • unintended_html_in_doc_comment
   info • Angle brackets will be interpreted as HTML • lib/utils/rank_utils.dart:97:31 • unintended_html_in_doc_comment
   info • Angle brackets will be interpreted as HTML • lib/utils/rank_utils.dart:98:27 • unintended_html_in_doc_comment
   info • Angle brackets will be interpreted as HTML • lib/utils/rank_utils.dart:99:29 • unintended_html_in_doc_comment
   info • Parameter 'key' could be a super parameter • lib/utils/refreshonfocus.dart:25:9 • use_super_parameters
   info • Invalid use of a private type in a public API • lib/utils/refreshonfocus.dart:32:3 • library_private_types_in_public_api
   info • Parameter 'key' could be a super parameter • lib/widgets/back_to_home_button.dart:6:9 • use_super_parameters
   info • Parameter 'key' could be a super parameter • lib/widgets/bounty_card.dart:10:9 • use_super_parameters
   info • Parameter 'key' could be a super parameter • lib/widgets/bounty_spinner.dart:11:9 • use_super_parameters
   info • Parameter 'key' could be a super parameter • lib/widgets/cancel_button.dart:6:9 • use_super_parameters
   info • Parameter 'key' could be a super parameter • lib/widgets/cashed_bounties_modal.dart:17:9 • use_super_parameters
   info • Parameter 'key' could be a super parameter • lib/widgets/category_buttons.dart:10:9 • use_super_parameters
   info • Parameter 'key' could be a super parameter • lib/widgets/dhm_pop.dart:9:9 • use_super_parameters
   info • Parameter 'key' could be a super parameter • lib/widgets/exp_progress.dart:9:9 • use_super_parameters
   info • Parameter 'key' could be a super parameter • lib/widgets/image_picker.dart:12:9 • use_super_parameters
   info • Parameter 'key' could be a super parameter • lib/widgets/interactive_tutorial_overlay.dart:24:9 • use_super_parameters
   info • Parameter 'key' could be a super parameter • lib/widgets/lockdisplay.dart:16:9 • use_super_parameters
   info • Parameter 'key' could be a super parameter • lib/widgets/maxedout_appbar.dart:11:9 • use_super_parameters
   info • Parameter 'key' could be a super parameter • lib/widgets/onboarding_progress_indicator.dart:8:9 • use_super_parameters
   info • Parameter 'key' could be a super parameter • lib/widgets/reward_photo_proof_modal.dart:10:9 • use_super_parameters
   info • Parameter 'key' could be a super parameter • lib/widgets/reward_popups.dart:10:9 • use_super_parameters
   info • Parameter 'key' could be a super parameter • lib/widgets/rewards_dashboard.dart:17:9 • use_super_parameters
   info • Parameter 'key' could be a super parameter • lib/widgets/sbs_exp.dart:12:9 • use_super_parameters
   info • Parameter 'key' could be a super parameter • lib/widgets/shp_button.dart:6:9 • use_super_parameters
   info • Parameter 'key' could be a super parameter • lib/widgets/speech_to_text.dart:10:9 • use_super_parameters
warning • Unused import: 'package:maxed_out_life/models/user_model.dart' • test/services_test.dart:4:8 • unused_import

166 issues found. (ran in 1.6s)
