package com.guardiantape.maxedoutlife;

import android.appwidget.AppWidgetManager;
import android.appwidget.AppWidgetProvider;
import android.content.Context;
import android.widget.RemoteViews;

public class HomeLevelWidgetProvider extends AppWidgetProvider {
    @Override
    public void onUpdate(Context context, AppWidgetManager appWidgetManager, int[] appWidgetIds) {
        for (int appWidgetId : appWidgetIds) {
            RemoteViews views = new RemoteViews(context.getPackageName(), R.layout.home_level_widget);

            // Use SharedPreferences to get data
            android.content.SharedPreferences prefs = context.getSharedPreferences("HomeWidgetPreferences", Context.MODE_PRIVATE);
            String level = prefs.getString("level", "1");
            String exp = prefs.getString("exp", "0");

            views.setTextViewText(R.id.widget_level, "Level " + level);
            views.setTextViewText(R.id.widget_exp, exp + " XP");

            appWidgetManager.updateAppWidget(appWidgetId, views);
        }
    }
}