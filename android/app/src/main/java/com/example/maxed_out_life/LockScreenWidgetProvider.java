package com.guardiantape.maxedoutlife;

import android.app.PendingIntent;
import android.appwidget.AppWidgetManager;
import android.appwidget.AppWidgetProvider;
import android.content.Context;
import android.content.Intent;
import android.widget.RemoteViews;

public class LockScreenWidgetProvider extends AppWidgetProvider {
    @Override
    public void onUpdate(Context context, AppWidgetManager appWidgetManager, int[] appWidgetIds) {
        for (int appWidgetId : appWidgetIds) {
            RemoteViews views = new RemoteViews(context.getPackageName(), R.layout.lock_screen_widget);
            
            // Get data from SharedPreferences
            android.content.SharedPreferences prefs = context.getSharedPreferences("HomeWidgetPreferences", Context.MODE_PRIVATE);
            String level = prefs.getString("level", "1");
            String levelProgress = prefs.getString("level_progress", "0");
            String rank = prefs.getString("rank", "1");
            String rankProgress = prefs.getString("rank_progress", "0");
            
            // Update widget views
            views.setTextViewText(R.id.widget_level, "Lv." + level);
            views.setProgressBar(R.id.widget_level_progress, 100, Integer.parseInt(levelProgress), false);
            
            views.setTextViewText(R.id.widget_rank, "Rank " + rank);
            views.setProgressBar(R.id.widget_rank_progress, 100, Integer.parseInt(rankProgress), false);
            views.setTextViewText(R.id.widget_rank_progress_text, rankProgress + "%");
            
            // Add tap action to open the app
            Intent intent = context.getPackageManager().getLaunchIntentForPackage(context.getPackageName());
            if (intent != null) {
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP);
                PendingIntent pendingIntent = PendingIntent.getActivity(context, 0, intent, 
                    PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE);
                views.setOnClickPendingIntent(R.id.widget_title, pendingIntent);
            }
            
            appWidgetManager.updateAppWidget(appWidgetId, views);
        }
    }
} 