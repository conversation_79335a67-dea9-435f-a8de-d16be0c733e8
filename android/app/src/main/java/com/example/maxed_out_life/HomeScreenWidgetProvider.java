package com.guardiantape.maxedoutlife;

import android.appwidget.AppWidgetManager;
import android.appwidget.AppWidgetProvider;
import android.content.Context;
import android.widget.RemoteViews;

public class HomeScreenWidgetProvider extends AppWidgetProvider {
    @Override
    public void onUpdate(Context context, AppWidgetManager appWidgetManager, int[] appWidgetIds) {
        for (int appWidgetId : appWidgetIds) {
            RemoteViews views = new RemoteViews(context.getPackageName(), R.layout.home_screen_widget);
            
            // Get data from SharedPreferences
            android.content.SharedPreferences prefs = context.getSharedPreferences("HomeWidgetPreferences", Context.MODE_PRIVATE);
            String level = prefs.getString("level", "1");
            String levelProgress = prefs.getString("level_progress", "0");
            String rank = prefs.getString("rank", "1");
            String rankProgress = prefs.getString("rank_progress", "0");
            
            // Update widget views
            views.setTextViewText(R.id.widget_level, "Level " + level);
            views.setProgressBar(R.id.widget_level_progress, 100, Integer.parseInt(levelProgress), false);
            
            views.setTextViewText(R.id.widget_rank, "Rank " + rank);
            views.setProgressBar(R.id.widget_rank_progress, 100, Integer.parseInt(rankProgress), false);
            
            appWidgetManager.updateAppWidget(appWidgetId, views);
        }
    }
} 