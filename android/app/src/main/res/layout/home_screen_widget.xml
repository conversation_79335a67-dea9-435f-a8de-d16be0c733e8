<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/widget_background"
    android:orientation="vertical"
    android:padding="16dp">

    <TextView
        android:id="@+id/widget_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Maxed Out Life"
        android:textColor="#FFFFFF"
        android:textSize="20sp"
        android:fontFamily="sans-serif-medium"
        android:textStyle="bold" />

    <!-- Total Level Progress Section -->
    <TextView
        android:id="@+id/widget_level_label"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:text="Total Level"
        android:textColor="#B3FFFFFF"
        android:textSize="14sp"
        android:fontFamily="sans-serif" />

    <TextView
        android:id="@+id/widget_level"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Level 0"
        android:textColor="#FFFFFF"
        android:textSize="18sp"
        android:fontFamily="sans-serif-medium" />

    <ProgressBar
        android:id="@+id/widget_level_progress"
        style="?android:attr/progressBarStyleHorizontal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:progressTint="#00FFFF"
        android:progressBackgroundTint="#1AFFFFFF"
        android:minHeight="8dp"
        android:maxHeight="8dp" />

    <!-- North Star Rank Section -->
    <TextView
        android:id="@+id/widget_rank_label"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="North Star Rank"
        android:textColor="#B3FFFFFF"
        android:textSize="14sp"
        android:fontFamily="sans-serif" />

    <TextView
        android:id="@+id/widget_rank"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Rank 0"
        android:textColor="#FFFFFF"
        android:textSize="18sp"
        android:fontFamily="sans-serif-medium" />

    <ProgressBar
        android:id="@+id/widget_rank_progress"
        style="?android:attr/progressBarStyleHorizontal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:progressDrawable="@drawable/north_star_progress"
        android:minHeight="24dp"
        android:maxHeight="24dp" />

</LinearLayout> 