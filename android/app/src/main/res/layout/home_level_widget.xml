<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/widget_background"
    android:orientation="vertical"
    android:padding="8dp">

    <TextView
        android:id="@+id/widget_level"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Level 1"
        android:textColor="#FFFFFF"
        android:textSize="24sp"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/widget_exp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="0 XP"
        android:textColor="#FFFFFF"
        android:textSize="16sp" />

</LinearLayout> 