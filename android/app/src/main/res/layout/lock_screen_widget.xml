<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/widget_background"
    android:orientation="vertical"
    android:padding="12dp">

    <!-- Compact Header with Title and Level -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical">

        <TextView
            android:id="@+id/widget_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Maxed Out Life"
            android:textColor="#FFFFFF"
            android:textSize="16sp"
            android:fontFamily="sans-serif-medium"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/widget_level"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Lv.0"
            android:textColor="#FFFFFF"
            android:textSize="14sp"
            android:fontFamily="sans-serif-medium" />
    </LinearLayout>

    <!-- Level Progress -->
    <ProgressBar
        android:id="@+id/widget_level_progress"
        style="?android:attr/progressBarStyleHorizontal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:progressTint="#00FFFF"
        android:progressBackgroundTint="#1AFFFFFF"
        android:minHeight="4dp"
        android:maxHeight="4dp" />

    <!-- Rank Section -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginTop="8dp"
        android:gravity="center_vertical">

        <TextView
            android:id="@+id/widget_rank"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Rank 0"
            android:textColor="#FFFFFF"
            android:textSize="14sp"
            android:fontFamily="sans-serif-medium" />

        <TextView
            android:id="@+id/widget_rank_progress_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="0%"
            android:textColor="#FFFFFF"
            android:textSize="12sp"
            android:fontFamily="sans-serif" />
    </LinearLayout>

    <ProgressBar
        android:id="@+id/widget_rank_progress"
        style="?android:attr/progressBarStyleHorizontal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="2dp"
        android:progressDrawable="@drawable/lock_screen_progress"
        android:minHeight="12dp"
        android:maxHeight="12dp" />

</LinearLayout> 