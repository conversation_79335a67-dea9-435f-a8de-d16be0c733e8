// 📁 android/app/build.gradle.kts

plugins {
    id("com.android.application")
    id("kotlin-android")
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id("dev.flutter.flutter-gradle-plugin")
}

android {
    namespace = "com.guardiantape.maxedoutlife"
    compileSdk = flutter.compileSdkVersion
    ndkVersion = "27.0.12077973"

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
        isCoreLibraryDesugaringEnabled = true
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_11.toString()
    }

    defaultConfig {
        applicationId = "com.guardiantape.maxedoutlife"
        minSdk = 23
        targetSdk = flutter.targetSdkVersion
        versionCode = flutter.versionCode
        versionName = flutter.versionName
    }

    buildTypes {
        release {
            // Using debug signing for now so `flutter run --release` works out of the box.
            signingConfig = signingConfigs.getByName("debug")

            // If you want to enable code shrinking, uncomment and configure ProGuard/R8:
            // isMinifyEnabled = true
            // proguardFiles(
            //     getDefaultProguardFile("proguard-android-optimize.txt"),
            //     "proguard-rules.pro"
            // )
        }
        debug {
            // Default debug configuration
        }
    }

    // If you have custom source sets or resource directories, configure them here:
    // sourceSets["main"].apply {
    //     java.srcDirs("src/main/kotlin")
    // }
}

flutter {
    source = "../.."
}

dependencies {
    // Core dependencies will be added by Flutter as needed
    coreLibraryDesugaring("com.android.tools:desugar_jdk_libs:2.1.4")
}
