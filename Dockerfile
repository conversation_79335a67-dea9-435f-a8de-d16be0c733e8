# Maxed Out Life - Multi-stage Docker Build
# 
# This Dockerfile creates optimized builds for the Maxed Out Life app
# with support for web deployment and development environments.

# ============================================================================
# STAGE 1: Flutter Build Environment
# ============================================================================
FROM ubuntu:22.04 AS flutter-builder

# Set environment variables
ENV FLUTTER_VERSION=3.24.5
ENV FLUTTER_HOME=/opt/flutter
ENV PATH="$FLUTTER_HOME/bin:$PATH"
ENV DEBIAN_FRONTEND=noninteractive

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    git \
    unzip \
    xz-utils \
    zip \
    libglu1-mesa \
    wget \
    && rm -rf /var/lib/apt/lists/*

# Install Flutter
RUN git clone https://github.com/flutter/flutter.git -b stable $FLUTTER_HOME \
    && flutter doctor \
    && flutter config --enable-web

# Set working directory
WORKDIR /app

# Copy pubspec files
COPY pubspec.yaml pubspec.lock ./

# Get dependencies
RUN flutter pub get

# Copy source code
COPY . .

# Build web application
RUN flutter build web --release

# ============================================================================
# STAGE 2: Web Server (Production)
# ============================================================================
FROM nginx:alpine AS web-production

# Copy built web app
COPY --from=flutter-builder /app/build/web /usr/share/nginx/html

# Copy nginx configuration
COPY deployment/nginx.conf /etc/nginx/nginx.conf

# Create nginx user and set permissions
RUN addgroup -g 1001 -S nginx-app && \
    adduser -S -D -H -u 1001 -h /var/cache/nginx -s /sbin/nologin -G nginx-app -g nginx-app nginx-app && \
    chown -R nginx-app:nginx-app /usr/share/nginx/html && \
    chown -R nginx-app:nginx-app /var/cache/nginx && \
    chown -R nginx-app:nginx-app /var/log/nginx && \
    chown -R nginx-app:nginx-app /etc/nginx/conf.d

# Switch to non-root user
USER nginx-app

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/ || exit 1

# Start nginx
CMD ["nginx", "-g", "daemon off;"]

# ============================================================================
# STAGE 3: Development Environment
# ============================================================================
FROM flutter-builder AS development

# Install additional development tools
RUN apt-get update && apt-get install -y \
    vim \
    nano \
    htop \
    && rm -rf /var/lib/apt/lists/*

# Install Chrome for testing
RUN wget -q -O - https://dl-ssl.google.com/linux/linux_signing_key.pub | apt-key add - \
    && echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" >> /etc/apt/sources.list.d/google.list \
    && apt-get update \
    && apt-get install -y google-chrome-stable \
    && rm -rf /var/lib/apt/lists/*

# Set up development environment
ENV CHROME_EXECUTABLE=/usr/bin/google-chrome-stable
ENV FLUTTER_WEB_AUTO_DETECT=true

# Expose Flutter development server port
EXPOSE 3000

# Default command for development
CMD ["flutter", "run", "-d", "web-server", "--web-hostname", "0.0.0.0", "--web-port", "3000"]

# ============================================================================
# STAGE 4: CI/CD Build Environment
# ============================================================================
FROM flutter-builder AS ci-builder

# Install additional CI tools
RUN apt-get update && apt-get install -y \
    lcov \
    jq \
    && rm -rf /var/lib/apt/lists/*

# Install Android SDK for CI builds
ENV ANDROID_SDK_ROOT=/opt/android-sdk
ENV PATH="$ANDROID_SDK_ROOT/cmdline-tools/latest/bin:$ANDROID_SDK_ROOT/platform-tools:$PATH"

RUN mkdir -p $ANDROID_SDK_ROOT/cmdline-tools && \
    wget -q https://dl.google.com/android/repository/commandlinetools-linux-9477386_latest.zip && \
    unzip commandlinetools-linux-9477386_latest.zip -d $ANDROID_SDK_ROOT/cmdline-tools && \
    mv $ANDROID_SDK_ROOT/cmdline-tools/cmdline-tools $ANDROID_SDK_ROOT/cmdline-tools/latest && \
    rm commandlinetools-linux-9477386_latest.zip

# Accept Android licenses
RUN yes | sdkmanager --licenses

# Install required Android SDK components
RUN sdkmanager "platform-tools" "platforms;android-35" "build-tools;35.0.0"

# Set up Flutter for Android builds
RUN flutter config --android-sdk $ANDROID_SDK_ROOT

# Default command for CI
CMD ["flutter", "test", "--coverage"]

# ============================================================================
# STAGE 5: Testing Environment
# ============================================================================
FROM ci-builder AS testing

# Install testing dependencies
RUN flutter pub get

# Install Chrome for web testing
RUN wget -q -O - https://dl-ssl.google.com/linux/linux_signing_key.pub | apt-key add - \
    && echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" >> /etc/apt/sources.list.d/google.list \
    && apt-get update \
    && apt-get install -y google-chrome-stable \
    && rm -rf /var/lib/apt/lists/*

# Set Chrome executable for testing
ENV CHROME_EXECUTABLE=/usr/bin/google-chrome-stable

# Create test results directory
RUN mkdir -p /app/test-results

# Default command for testing
CMD ["flutter", "test", "--coverage", "--reporter=json", "--file-reporter=json:/app/test-results/test-results.json"]

# ============================================================================
# STAGE 6: Multi-platform Builder
# ============================================================================
FROM flutter-builder AS multi-platform

# Install platform-specific dependencies
RUN apt-get update && apt-get install -y \
    clang \
    cmake \
    ninja-build \
    pkg-config \
    libgtk-3-dev \
    && rm -rf /var/lib/apt/lists/*

# Enable desktop platforms
RUN flutter config --enable-linux-desktop

# Build script for multiple platforms
COPY scripts/docker-build.sh /usr/local/bin/docker-build.sh
RUN chmod +x /usr/local/bin/docker-build.sh

# Default command
CMD ["/usr/local/bin/docker-build.sh"]

# ============================================================================
# METADATA
# ============================================================================
LABEL maintainer="Maxed Out Life Team"
LABEL version="1.0.0"
LABEL description="Maxed Out Life Flutter Application"
LABEL org.opencontainers.image.source="https://github.com/maxedoutlife/maxed_out_life"
LABEL org.opencontainers.image.documentation="https://github.com/maxedoutlife/maxed_out_life/blob/main/README.md"
LABEL org.opencontainers.image.licenses="MIT"
