FRENCH ENCRYPTION DECLARATION FORM
DÉCLARATION DE MOYENS DE CRYPTOLOGIE

Application: MXD (MXD Out Life)
Developer: Guardian Tape
Date: July 11, 2025

SECTION 1: APPLICATION INFORMATION
Application Name: MXD Out Life
Bundle Identifier: com.guardiantape.maxedoutlife
Version: 1.0.0
Developer: Guardian Tape
Contact Email: <EMAIL>

SECTION 2: ENCRYPTION USAGE DECLARATION
The MXD application uses encryption solely for:

1. STANDARD DATA PROTECTION
   - HTTPS/TLS for secure data transmission
   - Standard iOS keychain storage for user credentials
   - Firebase encryption for cloud data storage
   - OpenAI API secure communication protocols

2. ENCRYPTION ALGORITHMS USED
   - AES (Advanced Encryption Standard) - Industry standard
   - RSA for key exchange - Industry standard
   - TLS 1.2/1.3 for transport security - Industry standard
   - SHA-256 for data integrity - Industry standard

3. PURPOSE OF ENCRYPTION
   - Protecting user personal data (usernames, progress, habits)
   - Securing AI coaching conversations
   - Protecting training and health data
   - Standard app security and authentication

SECTION 3: COMP<PERSON><PERSON><PERSON>E STATEMENT
This application:
✓ Uses only standard, publicly available encryption algorithms
✓ Does not implement proprietary or custom encryption
✓ Complies with iOS App Store encryption guidelines
✓ Uses encryption solely for data protection, not for military purposes
✓ Is intended for civilian personal development use only

SECTION 4: TECHNICAL SPECIFICATIONS
Encryption Implementation:
- Firebase SDK (Google) - Standard encryption
- iOS Security Framework - Apple's standard encryption
- OpenAI API - Standard HTTPS/TLS encryption
- No custom cryptographic implementations
- No encryption key generation beyond standard practices

SECTION 5: DISTRIBUTION DECLARATION
The MXD application will be distributed through:
- Apple App Store (Global distribution including France)
- Standard commercial distribution channels
- No military or restricted distribution

SECTION 6: CERTIFICATION
I hereby declare that the information provided in this form is accurate and complete. The MXD application uses only standard encryption technologies for legitimate data protection purposes and complies with all applicable French and international regulations.

Developer Signature: _RJLW____________________
Name: Guardian Tape Representative
Date: July 11, 2025
Title: Application Developer

REGULATORY REFERENCE:
- Article R.2335-1 et seq. of the French Defense Code
- EU Regulation 2021/821 (Dual-use items)
- Apple App Store Encryption Guidelines

This declaration is submitted in compliance with French encryption regulations for commercial software distribution.
