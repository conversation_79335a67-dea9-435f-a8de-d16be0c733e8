PODS:
  - audioplayers_da<PERSON>win (0.0.1):
    - Flutter
    - FlutterMacOS
  - CocoaAsyncSocket (7.6.5)
  - connectivity_plus (0.0.1):
    - FlutterMacOS
  - CwlCatchException (2.2.1):
    - CwlCatchExceptionSupport (~> 2.2.1)
  - CwlCatchExceptionSupport (2.2.1)
  - file_picker (0.0.1):
    - FlutterMacOS
  - file_selector_macos (0.0.1):
    - FlutterMacOS
  - Firebase/Analytics (11.15.0):
    - Firebase/Core
  - Firebase/Auth (11.15.0):
    - Firebase/CoreOnly
    - FirebaseAuth (~> 11.15.0)
  - Firebase/Core (11.15.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 11.15.0)
  - Firebase/CoreOnly (11.15.0):
    - FirebaseCore (~> 11.15.0)
  - Firebase/Crashlytics (11.15.0):
    - Firebase/CoreOnly
    - FirebaseCrashlytics (~> 11.15.0)
  - firebase_analytics (11.6.0):
    - Firebase/Analytics (= 11.15.0)
    - firebase_core
    - FlutterMacOS
  - firebase_auth (5.7.0):
    - Firebase/Auth (~> 11.15.0)
    - Firebase/CoreOnly (~> 11.15.0)
    - firebase_core
    - FlutterMacOS
  - firebase_core (3.15.2):
    - Firebase/CoreOnly (~> 11.15.0)
    - FlutterMacOS
  - firebase_crashlytics (4.3.10):
    - Firebase/CoreOnly (~> 11.15.0)
    - Firebase/Crashlytics (~> 11.15.0)
    - firebase_core
    - FlutterMacOS
  - FirebaseAnalytics (11.15.0):
    - FirebaseAnalytics/Default (= 11.15.0)
    - FirebaseCore (~> 11.15.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/MethodSwizzler (~> 8.1)
    - GoogleUtilities/Network (~> 8.1)
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
    - nanopb (~> 3.30910.0)
  - FirebaseAnalytics/Default (11.15.0):
    - FirebaseCore (~> 11.15.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleAppMeasurement/Default (= 11.15.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/MethodSwizzler (~> 8.1)
    - GoogleUtilities/Network (~> 8.1)
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
    - nanopb (~> 3.30910.0)
  - FirebaseAppCheckInterop (11.15.0)
  - FirebaseAuth (11.15.0):
    - FirebaseAppCheckInterop (~> 11.0)
    - FirebaseAuthInterop (~> 11.0)
    - FirebaseCore (~> 11.15.0)
    - FirebaseCoreExtension (~> 11.15.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/Environment (~> 8.1)
    - GTMSessionFetcher/Core (< 5.0, >= 3.4)
    - RecaptchaInterop (~> 101.0)
  - FirebaseAuthInterop (11.15.0)
  - FirebaseCore (11.15.0):
    - FirebaseCoreInternal (~> 11.15.0)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/Logger (~> 8.1)
  - FirebaseCoreExtension (11.15.0):
    - FirebaseCore (~> 11.15.0)
  - FirebaseCoreInternal (11.15.0):
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
  - FirebaseCrashlytics (11.15.0):
    - FirebaseCore (~> 11.15.0)
    - FirebaseInstallations (~> 11.0)
    - FirebaseRemoteConfigInterop (~> 11.0)
    - FirebaseSessions (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/Environment (~> 8.1)
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - FirebaseInstallations (11.15.0):
    - FirebaseCore (~> 11.15.0)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/UserDefaults (~> 8.1)
    - PromisesObjC (~> 2.4)
  - FirebaseRemoteConfigInterop (11.15.0)
  - FirebaseSessions (11.15.0):
    - FirebaseCore (~> 11.15.0)
    - FirebaseCoreExtension (~> 11.15.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/UserDefaults (~> 8.1)
    - nanopb (~> 3.30910.0)
    - PromisesSwift (~> 2.1)
  - flutter_local_notifications (0.0.1):
    - FlutterMacOS
  - flutter_secure_storage_macos (6.1.3):
    - FlutterMacOS
  - FlutterMacOS (1.0.0)
  - GoogleAppMeasurement/Core (11.15.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/MethodSwizzler (~> 8.1)
    - GoogleUtilities/Network (~> 8.1)
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/Default (11.15.0):
    - GoogleAdsOnDeviceConversion (= 2.1.0)
    - GoogleAppMeasurement/Core (= 11.15.0)
    - GoogleAppMeasurement/IdentitySupport (= 11.15.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/MethodSwizzler (~> 8.1)
    - GoogleUtilities/Network (~> 8.1)
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/IdentitySupport (11.15.0):
    - GoogleAppMeasurement/Core (= 11.15.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/MethodSwizzler (~> 8.1)
    - GoogleUtilities/Network (~> 8.1)
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
    - nanopb (~> 3.30910.0)
  - GoogleDataTransport (10.1.0):
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - GoogleUtilities/AppDelegateSwizzler (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.1.0):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.1.0):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.1.0)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.1.0)
  - GoogleUtilities/Reachability (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GTMSessionFetcher/Core (4.5.0)
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - package_info_plus (0.0.1):
    - FlutterMacOS
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - patrol (0.0.1):
    - CocoaAsyncSocket (~> 7.6)
    - Flutter
    - FlutterMacOS
  - PromisesObjC (2.4.0)
  - PromisesSwift (2.4.0):
    - PromisesObjC (= 2.4.0)
  - share_plus (0.0.1):
    - FlutterMacOS
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - speech_to_text (7.2.0):
    - CwlCatchException
    - Flutter
    - FlutterMacOS
  - url_launcher_macos (0.0.1):
    - FlutterMacOS

DEPENDENCIES:
  - audioplayers_darwin (from `Flutter/ephemeral/.symlinks/plugins/audioplayers_darwin/darwin`)
  - connectivity_plus (from `Flutter/ephemeral/.symlinks/plugins/connectivity_plus/macos`)
  - file_picker (from `Flutter/ephemeral/.symlinks/plugins/file_picker/macos`)
  - file_selector_macos (from `Flutter/ephemeral/.symlinks/plugins/file_selector_macos/macos`)
  - firebase_analytics (from `Flutter/ephemeral/.symlinks/plugins/firebase_analytics/macos`)
  - firebase_auth (from `Flutter/ephemeral/.symlinks/plugins/firebase_auth/macos`)
  - firebase_core (from `Flutter/ephemeral/.symlinks/plugins/firebase_core/macos`)
  - firebase_crashlytics (from `Flutter/ephemeral/.symlinks/plugins/firebase_crashlytics/macos`)
  - flutter_local_notifications (from `Flutter/ephemeral/.symlinks/plugins/flutter_local_notifications/macos`)
  - flutter_secure_storage_macos (from `Flutter/ephemeral/.symlinks/plugins/flutter_secure_storage_macos/macos`)
  - FlutterMacOS (from `Flutter/ephemeral`)
  - package_info_plus (from `Flutter/ephemeral/.symlinks/plugins/package_info_plus/macos`)
  - path_provider_foundation (from `Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin`)
  - patrol (from `Flutter/ephemeral/.symlinks/plugins/patrol/darwin`)
  - share_plus (from `Flutter/ephemeral/.symlinks/plugins/share_plus/macos`)
  - shared_preferences_foundation (from `Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/darwin`)
  - speech_to_text (from `Flutter/ephemeral/.symlinks/plugins/speech_to_text/darwin`)
  - url_launcher_macos (from `Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos`)

SPEC REPOS:
  trunk:
    - CocoaAsyncSocket
    - CwlCatchException
    - CwlCatchExceptionSupport
    - Firebase
    - FirebaseAnalytics
    - FirebaseAppCheckInterop
    - FirebaseAuth
    - FirebaseAuthInterop
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseCrashlytics
    - FirebaseInstallations
    - FirebaseRemoteConfigInterop
    - FirebaseSessions
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleUtilities
    - GTMSessionFetcher
    - nanopb
    - PromisesObjC
    - PromisesSwift

EXTERNAL SOURCES:
  audioplayers_darwin:
    :path: Flutter/ephemeral/.symlinks/plugins/audioplayers_darwin/darwin
  connectivity_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/connectivity_plus/macos
  file_picker:
    :path: Flutter/ephemeral/.symlinks/plugins/file_picker/macos
  file_selector_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/file_selector_macos/macos
  firebase_analytics:
    :path: Flutter/ephemeral/.symlinks/plugins/firebase_analytics/macos
  firebase_auth:
    :path: Flutter/ephemeral/.symlinks/plugins/firebase_auth/macos
  firebase_core:
    :path: Flutter/ephemeral/.symlinks/plugins/firebase_core/macos
  firebase_crashlytics:
    :path: Flutter/ephemeral/.symlinks/plugins/firebase_crashlytics/macos
  flutter_local_notifications:
    :path: Flutter/ephemeral/.symlinks/plugins/flutter_local_notifications/macos
  flutter_secure_storage_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/flutter_secure_storage_macos/macos
  FlutterMacOS:
    :path: Flutter/ephemeral
  package_info_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/package_info_plus/macos
  path_provider_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin
  patrol:
    :path: Flutter/ephemeral/.symlinks/plugins/patrol/darwin
  share_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/share_plus/macos
  shared_preferences_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/darwin
  speech_to_text:
    :path: Flutter/ephemeral/.symlinks/plugins/speech_to_text/darwin
  url_launcher_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos

SPEC CHECKSUMS:
  audioplayers_darwin: 4f9ca89d92d3d21cec7ec580e78ca888e5fb68bd
  CocoaAsyncSocket: 065fd1e645c7abab64f7a6a2007a48038fdc6a99
  connectivity_plus: 4adf20a405e25b42b9c9f87feff8f4b6fde18a4e
  CwlCatchException: 7acc161b299a6de7f0a46a6ed741eae2c8b4d75a
  CwlCatchExceptionSupport: 54ccab8d8c78907b57f99717fb19d4cc3bce02dc
  file_picker: 7584aae6fa07a041af2b36a2655122d42f578c1a
  file_selector_macos: 6280b52b459ae6c590af5d78fc35c7267a3c4b31
  Firebase: d99ac19b909cd2c548339c2241ecd0d1599ab02e
  firebase_analytics: 3091f96bd17636f6da5092a4701ffacf67c6e455
  firebase_auth: 693f1e1ef2bb11a241d4478e63f1f47676af0538
  firebase_core: 7667f880631ae8ad10e3d6567ab7582fe0682326
  firebase_crashlytics: af8dce4a4f3b2b1556bf51043623060a5fc7eca7
  FirebaseAnalytics: 6433dfd311ba78084fc93bdfc145e8cb75740eae
  FirebaseAppCheckInterop: 06fe5a3799278ae4667e6c432edd86b1030fa3df
  FirebaseAuth: a6575e5fbf46b046c58dc211a28a5fbdd8d4c83b
  FirebaseAuthInterop: 7087d7a4ee4bc4de019b2d0c240974ed5d89e2fd
  FirebaseCore: efb3893e5b94f32b86e331e3bd6dadf18b66568e
  FirebaseCoreExtension: edbd30474b5ccf04e5f001470bdf6ea616af2435
  FirebaseCoreInternal: 9afa45b1159304c963da48addb78275ef701c6b4
  FirebaseCrashlytics: e09d0bc19aa54a51e45b8039c836ef73f32c039a
  FirebaseInstallations: 317270fec08a5d418fdbc8429282238cab3ac843
  FirebaseRemoteConfigInterop: 1c6135e8a094cc6368949f5faeeca7ee8948b8aa
  FirebaseSessions: b9a92c1c51bbb81e78fc3142cda6d925d700f8e7
  flutter_local_notifications: 4bf37a31afde695b56091b4ae3e4d9c7a7e6cda0
  flutter_secure_storage_macos: 7f45e30f838cf2659862a4e4e3ee1c347c2b3b54
  FlutterMacOS: 8f6f14fa908a6fb3fba0cd85dbd81ec4b251fb24
  GoogleAppMeasurement: 700dce7541804bec33db590a5c496b663fbe2539
  GoogleDataTransport: aae35b7ea0c09004c3797d53c8c41f66f219d6a7
  GoogleUtilities: 00c88b9a86066ef77f0da2fab05f65d7768ed8e1
  GTMSessionFetcher: fc75fc972958dceedee61cb662ae1da7a83a91cf
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  package_info_plus: f0052d280d17aa382b932f399edf32507174e870
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  patrol: 51b76cc7c11a2933ee3e72482d930c75b9d4ec73
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  PromisesSwift: 9d77319bbe72ebf6d872900551f7eeba9bce2851
  share_plus: 3c787998077d6b31e839225a282e9e27edf99274
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  speech_to_text: 3b313d98516d3d0406cea424782ec25470c59d19
  url_launcher_macos: 0fba8ddabfc33ce0a9afe7c5fef5aab3d8d2d673

PODFILE CHECKSUM: 3d4424f535ee55d4a0326e94b8ab87e308bddacb

COCOAPODS: 1.16.2
