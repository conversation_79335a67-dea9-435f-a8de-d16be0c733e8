# Maxed Out Life Deployment Configuration
# 
# This file defines deployment settings for different environments
# and platforms for the Maxed Out Life application.

# Global Configuration
global:
  app_name: "Maxed Out Life"
  app_id: "com.guardiantape.maxedoutlife"
  version_format: "MAJOR.MINOR.PATCH+BUILD"
  
  # Build settings
  flutter_version: "3.24.5"
  dart_version: "3.5.4"
  
  # Quality gates
  min_test_coverage: 80
  max_build_time_minutes: 45
  
  # Artifact retention
  artifact_retention_days: 30
  release_retention_days: 365

# Environment Configuration
environments:
  development:
    description: "Development environment for testing"
    auto_deploy: true
    require_approval: false
    
    # Environment variables
    env_vars:
      FLUTTER_ENV: "development"
      DEBUG_MODE: "true"
      LOG_LEVEL: "debug"
      API_BASE_URL: "https://dev-api.maxedoutlife.com"
      
    # Build configuration
    build:
      type: "debug"
      obfuscation: false
      tree_shake_icons: false
      
    # Testing
    testing:
      run_unit_tests: true
      run_widget_tests: true
      run_integration_tests: false
      performance_tests: false
      
  staging:
    description: "Staging environment for pre-production testing"
    auto_deploy: false
    require_approval: true
    
    # Environment variables
    env_vars:
      FLUTTER_ENV: "staging"
      DEBUG_MODE: "false"
      LOG_LEVEL: "info"
      API_BASE_URL: "https://staging-api.maxedoutlife.com"
      
    # Build configuration
    build:
      type: "profile"
      obfuscation: false
      tree_shake_icons: true
      
    # Testing
    testing:
      run_unit_tests: true
      run_widget_tests: true
      run_integration_tests: true
      performance_tests: true
      
  production:
    description: "Production environment"
    auto_deploy: false
    require_approval: true
    
    # Environment variables
    env_vars:
      FLUTTER_ENV: "production"
      DEBUG_MODE: "false"
      LOG_LEVEL: "error"
      API_BASE_URL: "https://api.maxedoutlife.com"
      
    # Build configuration
    build:
      type: "release"
      obfuscation: true
      tree_shake_icons: true
      
    # Testing
    testing:
      run_unit_tests: true
      run_widget_tests: true
      run_integration_tests: true
      performance_tests: true
      security_scan: true

# Platform Configuration
platforms:
  android:
    enabled: true
    min_sdk: 21
    target_sdk: 35
    compile_sdk: 35
    
    # Build variants
    build_variants:
      - debug
      - profile
      - release
      
    # Output formats
    outputs:
      - apk
      - aab  # App Bundle for Play Store
      
    # Signing (production)
    signing:
      store_file: "android/keystore.jks"
      store_password_env: "ANDROID_KEYSTORE_PASSWORD"
      key_alias_env: "ANDROID_KEY_ALIAS"
      key_password_env: "ANDROID_KEY_PASSWORD"
      
    # Play Store deployment
    play_store:
      enabled: false  # Enable when ready for store deployment
      track: "internal"  # internal, alpha, beta, production
      package_name: "com.guardiantape.maxedoutlife"
      
  ios:
    enabled: true
    min_version: "12.0"
    target_version: "17.0"
    
    # Build configurations
    configurations:
      - Debug
      - Profile
      - Release
      
    # Signing (production)
    signing:
      team_id_env: "IOS_TEAM_ID"
      certificate_env: "IOS_CERTIFICATE_BASE64"
      provisioning_profile_env: "IOS_PROVISIONING_PROFILE_BASE64"
      
    # App Store deployment
    app_store:
      enabled: false  # Enable when ready for store deployment
      bundle_id: "com.guardiantape.maxedoutlife"
      
  web:
    enabled: true
    renderer: "canvaskit"  # canvaskit, html
    
    # Build settings
    base_href: "/"
    
    # Deployment targets
    deployment:
      github_pages:
        enabled: true
        custom_domain: "maxedoutlife.app"
        
      firebase_hosting:
        enabled: false
        project_id: "maxed-out-life"
        
      netlify:
        enabled: false
        site_id: ""
        
  windows:
    enabled: true
    
    # Build settings
    architecture: "x64"
    
    # Packaging
    packaging:
      msix:
        enabled: false
        publisher: "CN=MaxedOutLife"
        
  macos:
    enabled: true
    min_version: "10.14"
    
    # Build settings
    architecture: "universal"  # x64, arm64, universal
    
    # Signing
    signing:
      enabled: false
      team_id_env: "MACOS_TEAM_ID"
      
  linux:
    enabled: true
    
    # Build settings
    architecture: "x64"
    
    # Packaging
    packaging:
      snap:
        enabled: false
      appimage:
        enabled: false
      deb:
        enabled: false

# CI/CD Pipeline Configuration
pipeline:
  # Triggers
  triggers:
    push:
      branches: ["main", "develop"]
    pull_request:
      branches: ["main", "develop"]
    schedule:
      cron: "0 2 * * *"  # Nightly builds at 2 AM UTC
      
  # Stages
  stages:
    - name: "analyze"
      description: "Code analysis and linting"
      timeout_minutes: 10
      
    - name: "test"
      description: "Run test suites"
      timeout_minutes: 30
      parallel: true
      
    - name: "security"
      description: "Security scanning"
      timeout_minutes: 15
      
    - name: "build"
      description: "Build applications"
      timeout_minutes: 45
      parallel: true
      
    - name: "deploy"
      description: "Deploy to environments"
      timeout_minutes: 20
      require_approval: true
      
  # Quality gates
  quality_gates:
    - name: "test_coverage"
      threshold: 80
      metric: "percentage"
      
    - name: "build_success"
      required: true
      
    - name: "security_scan"
      severity: "high"
      
# Notification Configuration
notifications:
  # Channels
  channels:
    slack:
      enabled: false
      webhook_url_env: "SLACK_WEBHOOK_URL"
      
    email:
      enabled: false
      recipients: []
      
    github:
      enabled: true
      
  # Events
  events:
    build_success:
      channels: ["github"]
      
    build_failure:
      channels: ["github", "slack"]
      
    deployment_success:
      channels: ["github", "slack"]
      
    deployment_failure:
      channels: ["github", "slack", "email"]
      
    security_alert:
      channels: ["github", "slack", "email"]

# Monitoring and Analytics
monitoring:
  # Performance monitoring
  performance:
    enabled: true
    thresholds:
      app_start_time_ms: 3000
      frame_rate_fps: 55
      memory_usage_mb: 200
      
  # Error tracking
  error_tracking:
    enabled: false
    service: "sentry"  # sentry, crashlytics
    
  # Analytics
  analytics:
    enabled: false
    service: "firebase"  # firebase, mixpanel
    
# Security Configuration
security:
  # Code scanning
  code_scanning:
    enabled: true
    tools:
      - "dart_analyze"
      - "pub_audit"
      
  # Dependency scanning
  dependency_scanning:
    enabled: true
    auto_update: false
    
  # Secrets management
  secrets:
    # Required secrets for deployment
    required:
      - "ANDROID_KEYSTORE_PASSWORD"
      - "ANDROID_KEY_ALIAS"
      - "ANDROID_KEY_PASSWORD"
      - "IOS_CERTIFICATE_BASE64"
      - "IOS_PROVISIONING_PROFILE_BASE64"
      
    # Optional secrets
    optional:
      - "SLACK_WEBHOOK_URL"
      - "FIREBASE_TOKEN"
      - "SENTRY_DSN"

# Backup and Recovery
backup:
  # Source code backup
  source_code:
    enabled: true
    frequency: "daily"
    retention_days: 90
    
  # Build artifacts backup
  artifacts:
    enabled: true
    frequency: "per_build"
    retention_days: 30
    
  # Configuration backup
  configuration:
    enabled: true
    frequency: "on_change"
    retention_days: 365
