# Nginx Configuration for Maxed Out Life Web App
# Optimized for Flutter web applications with PWA support

user nginx-app;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Logging format
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;

    # Basic settings
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    server_tokens off;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        application/atom+xml
        application/geo+json
        application/javascript
        application/x-javascript
        application/json
        application/ld+json
        application/manifest+json
        application/rdf+xml
        application/rss+xml
        application/xhtml+xml
        application/xml
        font/eot
        font/otf
        font/ttf
        image/svg+xml
        text/css
        text/javascript
        text/plain
        text/xml;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=login:10m rate=1r/s;

    server {
        listen 8080;
        server_name localhost;
        root /usr/share/nginx/html;
        index index.html;

        # Security
        server_tokens off;
        
        # Remove trailing slash
        rewrite ^/(.*)/$ /$1 permanent;

        # Main location block for Flutter web app
        location / {
            try_files $uri $uri/ /index.html;
            
            # Cache static assets
            location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
                expires 1y;
                add_header Cache-Control "public, immutable";
                add_header Vary "Accept-Encoding";
            }
            
            # Cache HTML files for shorter period
            location ~* \.(html)$ {
                expires 1h;
                add_header Cache-Control "public, must-revalidate";
            }
            
            # Don't cache service worker
            location = /flutter_service_worker.js {
                expires off;
                add_header Cache-Control "no-cache, no-store, must-revalidate";
                add_header Pragma "no-cache";
            }
        }

        # PWA manifest
        location = /manifest.json {
            expires 1d;
            add_header Cache-Control "public, must-revalidate";
            add_header Content-Type "application/manifest+json";
        }

        # Favicon
        location = /favicon.ico {
            expires 1y;
            add_header Cache-Control "public, immutable";
            log_not_found off;
            access_log off;
        }

        # Robots.txt
        location = /robots.txt {
            expires 1d;
            add_header Cache-Control "public, must-revalidate";
            log_not_found off;
            access_log off;
        }

        # API proxy (if needed)
        location /api/ {
            limit_req zone=api burst=20 nodelay;
            
            # Proxy to backend API
            # proxy_pass http://backend-api;
            # proxy_set_header Host $host;
            # proxy_set_header X-Real-IP $remote_addr;
            # proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            # proxy_set_header X-Forwarded-Proto $scheme;
            
            # For now, return 404 for API calls
            return 404;
        }

        # Health check endpoint
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }

        # Block access to sensitive files
        location ~ /\. {
            deny all;
            access_log off;
            log_not_found off;
        }

        location ~ ~$ {
            deny all;
            access_log off;
            log_not_found off;
        }

        # Error pages
        error_page 404 /index.html;
        error_page 500 502 503 504 /50x.html;
        
        location = /50x.html {
            root /usr/share/nginx/html;
        }

        # Deny access to .htaccess files
        location ~ /\.ht {
            deny all;
        }
    }

    # HTTPS redirect (when using HTTPS)
    # server {
    #     listen 80;
    #     server_name maxedoutlife.app www.maxedoutlife.app;
    #     return 301 https://$server_name$request_uri;
    # }

    # HTTPS configuration (when SSL certificates are available)
    # server {
    #     listen 443 ssl http2;
    #     server_name maxedoutlife.app www.maxedoutlife.app;
    #     root /usr/share/nginx/html;
    #     index index.html;

    #     # SSL configuration
    #     ssl_certificate /etc/ssl/certs/maxedoutlife.app.crt;
    #     ssl_certificate_key /etc/ssl/private/maxedoutlife.app.key;
    #     ssl_session_timeout 1d;
    #     ssl_session_cache shared:SSL:50m;
    #     ssl_session_tickets off;

    #     # Modern configuration
    #     ssl_protocols TLSv1.2 TLSv1.3;
    #     ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384;
    #     ssl_prefer_server_ciphers off;

    #     # HSTS
    #     add_header Strict-Transport-Security "max-age=63072000" always;

    #     # Include the same location blocks as above
    # }
}
