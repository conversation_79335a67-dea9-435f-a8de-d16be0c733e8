name: Release Pipeline

on:
  push:
    tags:
      - 'v*.*.*'
  workflow_dispatch:
    inputs:
      version:
        description: 'Release version (e.g., 1.0.0)'
        required: true
        type: string
      release_type:
        description: 'Release type'
        required: true
        type: choice
        options:
          - patch
          - minor
          - major
        default: patch
      prerelease:
        description: 'Mark as pre-release'
        required: false
        type: boolean
        default: false

env:
  FLUTTER_VERSION: '3.24.5'
  JAVA_VERSION: '17'

jobs:
  # ============================================================================
  # PREPARE RELEASE
  # ============================================================================
  prepare:
    name: Prepare Release
    runs-on: ubuntu-latest
    outputs:
      version: ${{ steps.version.outputs.version }}
      build_number: ${{ steps.version.outputs.build_number }}
      
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          
      - name: Determine version
        id: version
        run: |
          if [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
            VERSION="${{ github.event.inputs.version }}"
          else
            VERSION="${GITHUB_REF#refs/tags/v}"
          fi
          
          BUILD_NUMBER=$(date +%s)
          
          echo "version=$VERSION" >> $GITHUB_OUTPUT
          echo "build_number=$BUILD_NUMBER" >> $GITHUB_OUTPUT
          echo "Release version: $VERSION"
          echo "Build number: $BUILD_NUMBER"

  # ============================================================================
  # QUALITY ASSURANCE
  # ============================================================================
  qa:
    name: Quality Assurance
    runs-on: ubuntu-latest
    needs: prepare
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ env.FLUTTER_VERSION }}
          cache: true
          
      - name: Get dependencies
        run: flutter pub get
        
      - name: Run full test suite
        run: |
          flutter test --coverage --reporter=github
          
      - name: Verify coverage threshold
        run: |
          # Extract coverage percentage
          COVERAGE=$(lcov --summary coverage/lcov.info | grep -o '[0-9.]*%' | head -1 | sed 's/%//')
          echo "Coverage: $COVERAGE%"
          
          # Check if coverage meets minimum threshold (80%)
          if (( $(echo "$COVERAGE < 80" | bc -l) )); then
            echo "❌ Coverage $COVERAGE% is below minimum threshold of 80%"
            exit 1
          fi
          
          echo "✅ Coverage $COVERAGE% meets minimum threshold"

  # ============================================================================
  # BUILD RELEASE ARTIFACTS
  # ============================================================================
  build-android:
    name: Build Android Release
    runs-on: ubuntu-latest
    needs: [prepare, qa]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ env.FLUTTER_VERSION }}
          cache: true
          
      - name: Setup Java
        uses: actions/setup-java@v4
        with:
          distribution: 'temurin'
          java-version: ${{ env.JAVA_VERSION }}
          
      - name: Get dependencies
        run: flutter pub get
        
      - name: Update version
        run: |
          sed -i "s/version: .*/version: ${{ needs.prepare.outputs.version }}+${{ needs.prepare.outputs.build_number }}/" pubspec.yaml
          
      - name: Build APK
        run: flutter build apk --release --split-per-abi
        
      - name: Build App Bundle
        run: flutter build appbundle --release
        
      - name: Sign APK (if keystore available)
        if: env.ANDROID_KEYSTORE_BASE64 != ''
        env:
          ANDROID_KEYSTORE_BASE64: ${{ secrets.ANDROID_KEYSTORE_BASE64 }}
          ANDROID_KEYSTORE_PASSWORD: ${{ secrets.ANDROID_KEYSTORE_PASSWORD }}
          ANDROID_KEY_ALIAS: ${{ secrets.ANDROID_KEY_ALIAS }}
          ANDROID_KEY_PASSWORD: ${{ secrets.ANDROID_KEY_PASSWORD }}
        run: |
          echo "Signing APK with release keystore"
          # Keystore signing would be implemented here
          
      - name: Upload Android artifacts
        uses: actions/upload-artifact@v4
        with:
          name: android-release
          path: |
            build/app/outputs/flutter-apk/*.apk
            build/app/outputs/bundle/release/*.aab
          retention-days: 30

  build-ios:
    name: Build iOS Release
    runs-on: macos-latest
    needs: [prepare, qa]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ env.FLUTTER_VERSION }}
          cache: true
          
      - name: Get dependencies
        run: flutter pub get
        
      - name: Update version
        run: |
          sed -i '' "s/version: .*/version: ${{ needs.prepare.outputs.version }}+${{ needs.prepare.outputs.build_number }}/" pubspec.yaml
          
      - name: Build iOS (unsigned)
        run: flutter build ios --release --no-codesign
        
      - name: Build iOS Archive (if certificates available)
        if: env.IOS_CERTIFICATE_BASE64 != ''
        env:
          IOS_CERTIFICATE_BASE64: ${{ secrets.IOS_CERTIFICATE_BASE64 }}
          IOS_CERTIFICATE_PASSWORD: ${{ secrets.IOS_CERTIFICATE_PASSWORD }}
          IOS_PROVISIONING_PROFILE_BASE64: ${{ secrets.IOS_PROVISIONING_PROFILE_BASE64 }}
        run: |
          echo "Building signed iOS archive"
          # iOS signing and archiving would be implemented here
          
      - name: Upload iOS artifacts
        uses: actions/upload-artifact@v4
        with:
          name: ios-release
          path: build/ios/iphoneos/Runner.app
          retention-days: 30

  build-web:
    name: Build Web Release
    runs-on: ubuntu-latest
    needs: [prepare, qa]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ env.FLUTTER_VERSION }}
          cache: true
          
      - name: Get dependencies
        run: flutter pub get
        
      - name: Update version
        run: |
          sed -i "s/version: .*/version: ${{ needs.prepare.outputs.version }}+${{ needs.prepare.outputs.build_number }}/" pubspec.yaml
          
      - name: Build Web
        run: flutter build web --release
        
      - name: Upload Web artifacts
        uses: actions/upload-artifact@v4
        with:
          name: web-release
          path: build/web
          retention-days: 30

  build-desktop:
    name: Build Desktop Release
    runs-on: ${{ matrix.os }}
    needs: [prepare, qa]
    
    strategy:
      matrix:
        include:
          - os: windows-latest
            platform: windows
            build-cmd: flutter build windows --release
            artifact-path: build/windows/x64/runner/Release
            
          - os: macos-latest
            platform: macos
            build-cmd: flutter build macos --release
            artifact-path: build/macos/Build/Products/Release/maxed_out_life.app
            
          - os: ubuntu-latest
            platform: linux
            build-cmd: flutter build linux --release
            artifact-path: build/linux/x64/release/bundle
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ env.FLUTTER_VERSION }}
          cache: true
          
      - name: Setup Linux dependencies
        if: matrix.platform == 'linux'
        run: |
          sudo apt-get update
          sudo apt-get install -y clang cmake ninja-build pkg-config libgtk-3-dev
          
      - name: Get dependencies
        run: flutter pub get
        
      - name: Enable desktop platform
        run: flutter config --enable-${{ matrix.platform }}-desktop
        
      - name: Update version
        run: |
          if [[ "${{ matrix.platform }}" == "windows" ]]; then
            sed -i "s/version: .*/version: ${{ needs.prepare.outputs.version }}+${{ needs.prepare.outputs.build_number }}/" pubspec.yaml
          else
            sed -i '' "s/version: .*/version: ${{ needs.prepare.outputs.version }}+${{ needs.prepare.outputs.build_number }}/" pubspec.yaml
          fi
          
      - name: Build desktop app
        run: ${{ matrix.build-cmd }}
        
      - name: Upload desktop artifacts
        uses: actions/upload-artifact@v4
        with:
          name: ${{ matrix.platform }}-release
          path: ${{ matrix.artifact-path }}
          retention-days: 30

  # ============================================================================
  # CREATE GITHUB RELEASE
  # ============================================================================
  release:
    name: Create GitHub Release
    runs-on: ubuntu-latest
    needs: [prepare, build-android, build-ios, build-web, build-desktop]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          
      - name: Download all artifacts
        uses: actions/download-artifact@v4
        with:
          path: release-artifacts
          
      - name: Generate changelog
        id: changelog
        run: |
          # Generate changelog from git commits
          PREVIOUS_TAG=$(git describe --tags --abbrev=0 HEAD^ 2>/dev/null || echo "")
          
          if [[ -n "$PREVIOUS_TAG" ]]; then
            CHANGELOG=$(git log --pretty=format:"- %s" $PREVIOUS_TAG..HEAD)
          else
            CHANGELOG="Initial release"
          fi
          
          echo "changelog<<EOF" >> $GITHUB_OUTPUT
          echo "$CHANGELOG" >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT
          
      - name: Create Release
        uses: softprops/action-gh-release@v2
        with:
          tag_name: v${{ needs.prepare.outputs.version }}
          name: Release v${{ needs.prepare.outputs.version }}
          body: |
            ## 🚀 Maxed Out Life v${{ needs.prepare.outputs.version }}
            
            ### 📱 Downloads
            - **Android**: APK and App Bundle files
            - **iOS**: iOS app (requires sideloading)
            - **Web**: Progressive Web App
            - **Desktop**: Windows, macOS, and Linux builds
            
            ### 📋 Changes
            ${{ steps.changelog.outputs.changelog }}
            
            ### 🔧 Technical Details
            - Flutter Version: ${{ env.FLUTTER_VERSION }}
            - Build Number: ${{ needs.prepare.outputs.build_number }}
            - Release Date: ${{ github.event.head_commit.timestamp }}
            
          files: release-artifacts/**/*
          prerelease: ${{ github.event.inputs.prerelease == 'true' }}
          draft: false
          
  # ============================================================================
  # DEPLOYMENT
  # ============================================================================
  deploy-web:
    name: Deploy Web App
    runs-on: ubuntu-latest
    needs: [prepare, build-web]
    if: github.ref == 'refs/heads/main' || startsWith(github.ref, 'refs/tags/')
    
    steps:
      - name: Download web artifacts
        uses: actions/download-artifact@v4
        with:
          name: web-release
          path: web-build
          
      - name: Deploy to GitHub Pages
        uses: peaceiris/actions-gh-pages@v4
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          publish_dir: web-build
          cname: maxedoutlife.app  # Custom domain if available
          
  # ============================================================================
  # NOTIFICATION
  # ============================================================================
  notify:
    name: Notify Release
    runs-on: ubuntu-latest
    needs: [release, deploy-web]
    if: always()
    
    steps:
      - name: Notify success
        if: needs.release.result == 'success'
        run: |
          echo "🎉 Release v${{ needs.prepare.outputs.version }} completed successfully!"
          echo "📱 All platform builds are available"
          echo "🌐 Web app deployed to GitHub Pages"
          
      - name: Notify failure
        if: needs.release.result != 'success'
        run: |
          echo "❌ Release v${{ needs.prepare.outputs.version }} failed"
          exit 1
