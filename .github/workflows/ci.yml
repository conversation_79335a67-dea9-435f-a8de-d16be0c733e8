name: Continuous Integration

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  schedule:
    # Run nightly builds at 2 AM UTC
    - cron: '0 2 * * *'

env:
  FLUTTER_VERSION: '3.24.5'
  JAVA_VERSION: '17'

jobs:
  # ============================================================================
  # CODE QUALITY & ANALYSIS
  # ============================================================================
  analyze:
    name: Code Analysis
    runs-on: ubuntu-latest
    timeout-minutes: 10
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ env.FLUTTER_VERSION }}
          cache: true
          
      - name: Get dependencies
        run: flutter pub get
        
      - name: Verify formatting
        run: dart format --output=none --set-exit-if-changed .
        
      - name: Analyze project source
        run: flutter analyze --fatal-infos
        
      - name: Check for outdated dependencies
        run: flutter pub outdated --exit-code
        continue-on-error: true

  # ============================================================================
  # TESTING PIPELINE
  # ============================================================================
  test:
    name: Run Tests
    runs-on: ubuntu-latest
    timeout-minutes: 30
    needs: analyze
    
    strategy:
      matrix:
        test-type: [unit, widget, integration]
        
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ env.FLUTTER_VERSION }}
          cache: true
          
      - name: Get dependencies
        run: flutter pub get
        
      - name: Generate mocks
        run: flutter packages pub run build_runner build --delete-conflicting-outputs
        
      - name: Run unit tests
        if: matrix.test-type == 'unit'
        run: |
          flutter test test/unit \
            --coverage \
            --reporter=github \
            --file-reporter=json:test-results-unit.json
            
      - name: Run widget tests
        if: matrix.test-type == 'widget'
        run: |
          flutter test test/widget \
            --coverage \
            --reporter=github \
            --file-reporter=json:test-results-widget.json
            
      - name: Run integration tests
        if: matrix.test-type == 'integration'
        run: |
          flutter test integration_test \
            --reporter=github \
            --file-reporter=json:test-results-integration.json
            
      - name: Upload test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: test-results-${{ matrix.test-type }}
          path: test-results-*.json
          retention-days: 30
          
      - name: Upload coverage to Codecov
        if: matrix.test-type == 'unit' || matrix.test-type == 'widget'
        uses: codecov/codecov-action@v4
        with:
          file: coverage/lcov.info
          flags: ${{ matrix.test-type }}
          name: ${{ matrix.test-type }}-coverage

  # ============================================================================
  # PERFORMANCE TESTING
  # ============================================================================
  performance:
    name: Performance Tests
    runs-on: ubuntu-latest
    timeout-minutes: 20
    needs: test
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ env.FLUTTER_VERSION }}
          cache: true
          
      - name: Get dependencies
        run: flutter pub get
        
      - name: Run performance tests
        run: |
          flutter test test/performance \
            --reporter=json \
            --file-reporter=json:performance-results.json
            
      - name: Upload performance results
        uses: actions/upload-artifact@v4
        with:
          name: performance-results
          path: performance-results.json
          retention-days: 30

  # ============================================================================
  # SECURITY SCANNING
  # ============================================================================
  security:
    name: Security Scan
    runs-on: ubuntu-latest
    timeout-minutes: 15
    needs: analyze
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ env.FLUTTER_VERSION }}
          cache: true
          
      - name: Get dependencies
        run: flutter pub get
        
      - name: Run security audit
        run: flutter pub deps --json | dart pub global run pana --json
        continue-on-error: true
        
      - name: Check for known vulnerabilities
        run: dart pub audit
        continue-on-error: true

  # ============================================================================
  # BUILD VALIDATION
  # ============================================================================
  build:
    name: Build Apps
    runs-on: ${{ matrix.os }}
    timeout-minutes: 45
    needs: [test, performance]
    
    strategy:
      fail-fast: false
      matrix:
        include:
          - os: ubuntu-latest
            platform: android
            build-cmd: flutter build apk --debug
            artifact-path: build/app/outputs/flutter-apk/app-debug.apk
            artifact-name: android-debug-apk
            
          - os: ubuntu-latest
            platform: web
            build-cmd: flutter build web --debug
            artifact-path: build/web
            artifact-name: web-debug-build
            
          - os: macos-latest
            platform: ios
            build-cmd: flutter build ios --debug --no-codesign
            artifact-path: build/ios/iphoneos/Runner.app
            artifact-name: ios-debug-app
            
          - os: macos-latest
            platform: macos
            build-cmd: flutter build macos --debug
            artifact-path: build/macos/Build/Products/Debug/maxed_out_life.app
            artifact-name: macos-debug-app
            
          - os: windows-latest
            platform: windows
            build-cmd: flutter build windows --debug
            artifact-path: build/windows/x64/runner/Debug
            artifact-name: windows-debug-build
            
          - os: ubuntu-latest
            platform: linux
            build-cmd: flutter build linux --debug
            artifact-path: build/linux/x64/release/bundle
            artifact-name: linux-debug-build
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: ${{ env.FLUTTER_VERSION }}
          cache: true
          
      - name: Setup Java (Android)
        if: matrix.platform == 'android'
        uses: actions/setup-java@v4
        with:
          distribution: 'temurin'
          java-version: ${{ env.JAVA_VERSION }}
          
      - name: Setup Linux dependencies
        if: matrix.platform == 'linux'
        run: |
          sudo apt-get update
          sudo apt-get install -y clang cmake ninja-build pkg-config libgtk-3-dev
          
      - name: Get dependencies
        run: flutter pub get
        
      - name: Enable platform
        run: flutter config --enable-${{ matrix.platform }}-desktop
        if: matrix.platform == 'windows' || matrix.platform == 'macos' || matrix.platform == 'linux'
        
      - name: Build app
        run: ${{ matrix.build-cmd }}
        
      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: ${{ matrix.artifact-name }}
          path: ${{ matrix.artifact-path }}
          retention-days: 7

  # ============================================================================
  # QUALITY GATES
  # ============================================================================
  quality-gate:
    name: Quality Gate
    runs-on: ubuntu-latest
    needs: [analyze, test, performance, security, build]
    if: always()
    
    steps:
      - name: Check job results
        run: |
          echo "Analyze: ${{ needs.analyze.result }}"
          echo "Test: ${{ needs.test.result }}"
          echo "Performance: ${{ needs.performance.result }}"
          echo "Security: ${{ needs.security.result }}"
          echo "Build: ${{ needs.build.result }}"
          
          if [[ "${{ needs.analyze.result }}" != "success" ]]; then
            echo "❌ Code analysis failed"
            exit 1
          fi
          
          if [[ "${{ needs.test.result }}" != "success" ]]; then
            echo "❌ Tests failed"
            exit 1
          fi
          
          if [[ "${{ needs.build.result }}" != "success" ]]; then
            echo "❌ Build failed"
            exit 1
          fi
          
          echo "✅ All quality gates passed"

  # ============================================================================
  # NOTIFICATION
  # ============================================================================
  notify:
    name: Notify Results
    runs-on: ubuntu-latest
    needs: quality-gate
    if: always() && github.event_name != 'pull_request'
    
    steps:
      - name: Notify success
        if: needs.quality-gate.result == 'success'
        run: |
          echo "✅ CI Pipeline completed successfully"
          echo "Branch: ${{ github.ref_name }}"
          echo "Commit: ${{ github.sha }}"
          
      - name: Notify failure
        if: needs.quality-gate.result != 'success'
        run: |
          echo "❌ CI Pipeline failed"
          echo "Branch: ${{ github.ref_name }}"
          echo "Commit: ${{ github.sha }}"
          exit 1
