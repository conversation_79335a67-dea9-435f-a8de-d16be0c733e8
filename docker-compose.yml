# Maxed Out Life - Docker Compose Configuration
# 
# This file defines the development and testing environment
# for the Maxed Out Life application using Docker containers.

version: '3.8'

services:
  # ============================================================================
  # DEVELOPMENT ENVIRONMENT
  # ============================================================================
  app-dev:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: maxed_out_life_dev
    ports:
      - "3000:3000"
    volumes:
      - .:/app
      - flutter_pub_cache:/root/.pub-cache
    environment:
      - FLUTTER_ENV=development
      - DEBUG_MODE=true
      - LOG_LEVEL=debug
    networks:
      - maxed_out_life_network
    profiles:
      - development

  # ============================================================================
  # TESTING ENVIRONMENT
  # ============================================================================
  app-test:
    build:
      context: .
      dockerfile: Dockerfile
      target: testing
    container_name: maxed_out_life_test
    volumes:
      - .:/app
      - ./test-results:/app/test-results
      - flutter_pub_cache:/root/.pub-cache
    environment:
      - FLUTTER_ENV=testing
      - CHROME_EXECUTABLE=/usr/bin/google-chrome-stable
    networks:
      - maxed_out_life_network
    profiles:
      - testing

  # ============================================================================
  # PRODUCTION WEB APP
  # ============================================================================
  app-web:
    build:
      context: .
      dockerfile: Dockerfile
      target: web-production
    container_name: maxed_out_life_web
    ports:
      - "8080:8080"
    environment:
      - NGINX_WORKER_PROCESSES=auto
      - NGINX_WORKER_CONNECTIONS=1024
    networks:
      - maxed_out_life_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    profiles:
      - production

  # ============================================================================
  # CI/CD BUILD ENVIRONMENT
  # ============================================================================
  app-ci:
    build:
      context: .
      dockerfile: Dockerfile
      target: ci-builder
    container_name: maxed_out_life_ci
    volumes:
      - .:/app
      - ./build-artifacts:/app/build
      - flutter_pub_cache:/root/.pub-cache
    environment:
      - FLUTTER_ENV=ci
      - ANDROID_SDK_ROOT=/opt/android-sdk
    networks:
      - maxed_out_life_network
    profiles:
      - ci

  # ============================================================================
  # MULTI-PLATFORM BUILDER
  # ============================================================================
  app-builder:
    build:
      context: .
      dockerfile: Dockerfile
      target: multi-platform
    container_name: maxed_out_life_builder
    volumes:
      - .:/app
      - ./release:/app/release
      - flutter_pub_cache:/root/.pub-cache
    environment:
      - BUILD_PLATFORMS=web,linux
      - BUILD_TYPE=release
    networks:
      - maxed_out_life_network
    profiles:
      - build

  # ============================================================================
  # REVERSE PROXY (NGINX)
  # ============================================================================
  nginx:
    image: nginx:alpine
    container_name: maxed_out_life_nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./deployment/nginx-proxy.conf:/etc/nginx/nginx.conf:ro
      - ./deployment/ssl:/etc/ssl:ro
    depends_on:
      - app-web
    networks:
      - maxed_out_life_network
    restart: unless-stopped
    profiles:
      - production
      - staging

  # ============================================================================
  # MONITORING (OPTIONAL)
  # ============================================================================
  prometheus:
    image: prom/prometheus:latest
    container_name: maxed_out_life_prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - maxed_out_life_network
    restart: unless-stopped
    profiles:
      - monitoring

  grafana:
    image: grafana/grafana:latest
    container_name: maxed_out_life_grafana
    ports:
      - "3001:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
    depends_on:
      - prometheus
    networks:
      - maxed_out_life_network
    restart: unless-stopped
    profiles:
      - monitoring

  # ============================================================================
  # DATABASE (FOR FUTURE USE)
  # ============================================================================
  postgres:
    image: postgres:15-alpine
    container_name: maxed_out_life_db
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    environment:
      - POSTGRES_DB=maxed_out_life
      - POSTGRES_USER=mol_user
      - POSTGRES_PASSWORD=mol_password
    networks:
      - maxed_out_life_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U mol_user -d maxed_out_life"]
      interval: 30s
      timeout: 10s
      retries: 5
    profiles:
      - database

  redis:
    image: redis:7-alpine
    container_name: maxed_out_life_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    networks:
      - maxed_out_life_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    profiles:
      - database

# ============================================================================
# NETWORKS
# ============================================================================
networks:
  maxed_out_life_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# ============================================================================
# VOLUMES
# ============================================================================
volumes:
  flutter_pub_cache:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  postgres_data:
    driver: local
  redis_data:
    driver: local
