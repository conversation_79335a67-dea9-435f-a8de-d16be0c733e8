<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Info.plist</key>
		<data>
		QJETILOPBwjioLrmTLs9PY5FL+s=
		</data>
		<key>flutter_assets/.env</key>
		<data>
		tbbTSqRqIZtE/UDGDse1pdtofEE=
		</data>
		<key>flutter_assets/AssetManifest.bin</key>
		<data>
		4qJfF4igx2YdghREDBks8B1hjHk=
		</data>
		<key>flutter_assets/AssetManifest.json</key>
		<data>
		VW0Q91/bSQhvFkbqRi1f91jEX60=
		</data>
		<key>flutter_assets/FontManifest.json</key>
		<data>
		np69rc2fuFbiys2MOmr0pndgz90=
		</data>
		<key>flutter_assets/NOTICES.Z</key>
		<data>
		VhFd6mreefGfWrSI3wtham1JzLg=
		</data>
		<key>flutter_assets/NativeAssetsManifest.json</key>
		<data>
		re4p7E8rPLLsN+wzaPN/+AVpXTY=
		</data>
		<key>flutter_assets/assets/fonts/BITSUMISHI.TTF</key>
		<data>
		si/JsjZrEQntpwQj83J2y9R7BeA=
		</data>
		<key>flutter_assets/assets/fonts/Orbitron.ttf</key>
		<data>
		UUQXgOGvd9W9v+H/+tOpBkTY7nY=
		</data>
		<key>flutter_assets/assets/fonts/Pirulen.otf</key>
		<data>
		f73p6uCb5RaTFWE8mofwNGzNlrg=
		</data>
		<key>flutter_assets/assets/fonts/SF-Pro.ttf</key>
		<data>
		RTXMBr7EF/+33g9ZRDMgZWaxJFo=
		</data>
		<key>flutter_assets/assets/fonts/digital-7.ttf</key>
		<data>
		524Vn53hOxyR4DwpM7P5RUeIBQ4=
		</data>
		<key>flutter_assets/assets/images/f2_aura.png</key>
		<data>
		88hUUjIFGW2orJD7k91pEFxNl+o=
		</data>
		<key>flutter_assets/assets/images/f3_aura.png</key>
		<data>
		JHDBEnhNqyWMZdulX7zXlyCM6fo=
		</data>
		<key>flutter_assets/assets/images/f_aura.png</key>
		<data>
		sCH7AZ3sz7P1XQ+L44dKTh4S93M=
		</data>
		<key>flutter_assets/assets/images/guardian_tape_splash.png</key>
		<data>
		a1xTzIDO14uCPWypvLKeCkxFv58=
		</data>
		<key>flutter_assets/assets/images/m6_aura.png</key>
		<data>
		+efnadfO6KzpmXpQvGt2O/NuB24=
		</data>
		<key>flutter_assets/assets/images/m_aura.png</key>
		<data>
		pOrm1i/HFyKA6CIfVnCSZcL2ak4=
		</data>
		<key>flutter_assets/assets/images/mana_aura.png</key>
		<data>
		b/WpHDOoNiVBQPat8T0nKpKLIIM=
		</data>
		<key>flutter_assets/assets/images/mana_aura_x.png</key>
		<data>
		AvxZJcSyHd+AiJN1i0ThSdWR5yI=
		</data>
		<key>flutter_assets/assets/images/mana_aura_y.png</key>
		<data>
		AoFHggVXhS0CZmYEVIR6bmmVfVQ=
		</data>
		<key>flutter_assets/assets/images/maxed_out_life_icon.png</key>
		<data>
		7yzt1G6FjK87asRBeEsXUzJntJI=
		</data>
		<key>flutter_assets/assets/images/mxd_logo.jpg</key>
		<data>
		plft3PW9lT0bL8pfamvMug41c78=
		</data>
		<key>flutter_assets/assets/images/mxd_logo_2.jpg</key>
		<data>
		u2rMkIKN/oycmELbe2TZlMVfNmM=
		</data>
		<key>flutter_assets/assets/images/mxd_logo_3_icon.jpg</key>
		<data>
		Tz32RBsSlQQcCdRAb3TppUeMfwQ=
		</data>
		<key>flutter_assets/assets/images/mxd_logo_vx.png</key>
		<data>
		lQZs2IQYaWA2rCl+oA1mGBqRqBE=
		</data>
		<key>flutter_assets/assets/images/progress.png</key>
		<data>
		0kB2uh1uM8CjpYGQ13rI2JhyggY=
		</data>
		<key>flutter_assets/assets/notification_icons/achievement.png</key>
		<data>
		jzJqo9Iniram2Jm9S3uXw8kPpFg=
		</data>
		<key>flutter_assets/assets/notification_icons/bounty.png</key>
		<data>
		qxMir5cvCm3ac60ACS2ZQsld2Tc=
		</data>
		<key>flutter_assets/assets/notification_icons/f_progress.png</key>
		<data>
		EOuUPmxYbd+AB3ubFxhGRIJJNp4=
		</data>
		<key>flutter_assets/assets/notification_icons/f_rank_up.png</key>
		<data>
		vLlgmY45O1x7svJzsxWTY1A7hcY=
		</data>
		<key>flutter_assets/assets/notification_icons/f_reminder.png</key>
		<data>
		b/E3UhAaxwKcANvVSvFC8sVNQEI=
		</data>
		<key>flutter_assets/assets/notification_icons/level_up.png</key>
		<data>
		u/cLUa1RMppweFWRoVvwUvrkDDg=
		</data>
		<key>flutter_assets/assets/notification_icons/level_up_small.png</key>
		<data>
		zOSMhHtYQjhc2rkFMzvkYGlaaiA=
		</data>
		<key>flutter_assets/assets/notification_icons/m_progress.png</key>
		<data>
		u6++KfFTEx69Bq5I1HhHB8xYHzk=
		</data>
		<key>flutter_assets/assets/notification_icons/m_rank_up.png</key>
		<data>
		CO3c2eqb5dMF8uJmUQeFgBC3qNs=
		</data>
		<key>flutter_assets/assets/notification_icons/m_reminder.png</key>
		<data>
		OkUpwR8TirRPLx65PwDCUQ09HL4=
		</data>
		<key>flutter_assets/assets/notification_icons/quest_progress.png</key>
		<data>
		iRDfmCYTwGkVI5bhdYcL3qqNtwY=
		</data>
		<key>flutter_assets/assets/sounds/.DS_Store</key>
		<data>
		oexviO1hACC1xHJnqCKAe72ZPVc=
		</data>
		<key>flutter_assets/assets/sounds/HSError.mp3</key>
		<data>
		CxpeYmGJUodRK5nmkMu9H08quzo=
		</data>
		<key>flutter_assets/assets/sounds/HSError2.mp3</key>
		<data>
		5YJKOSEic+CJcVym8D9Txy5TKfY=
		</data>
		<key>flutter_assets/assets/sounds/MXD_Music.mp3</key>
		<data>
		acubsHU0a3Bp8KI+ToarbMqW7Fw=
		</data>
		<key>flutter_assets/assets/sounds/lightning.mp3</key>
		<data>
		+Q62BrIfbS5R0ln+d+KiiTW6f0I=
		</data>
		<key>flutter_assets/assets/sounds/success.mp3</key>
		<data>
		zztF/bvBrzCfpGITyStU6pyzYew=
		</data>
		<key>flutter_assets/fonts/MaterialIcons-Regular.otf</key>
		<data>
		fISHTvZv4nE/K66mvydFS6XO7bI=
		</data>
		<key>flutter_assets/packages/cupertino_icons/assets/CupertinoIcons.ttf</key>
		<data>
		Bvk+P1ykE1PGRdktwgwDyz6AOqM=
		</data>
		<key>flutter_assets/packages/golden_toolkit/fonts/Roboto-Regular.ttf</key>
		<data>
		ywy5GjH0MpO9cELdq5Rc4WHCnT0=
		</data>
		<key>flutter_assets/shaders/ink_sparkle.frag</key>
		<data>
		VvTF10G1gIeea4aI0DhJjCjHgXQ=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>flutter_assets/.env</key>
		<dict>
			<key>hash2</key>
			<data>
			tyjfWq4XEwWvhY8y06ksIj2FbRyyTMlw6nSHbXbVNoo=
			</data>
		</dict>
		<key>flutter_assets/AssetManifest.bin</key>
		<dict>
			<key>hash2</key>
			<data>
			uIfv00uhoUiOdk1QuNO1/Ew6lj2SV8OaRSxSW6/h+e4=
			</data>
		</dict>
		<key>flutter_assets/AssetManifest.json</key>
		<dict>
			<key>hash2</key>
			<data>
			MmgCBxil+Q1Go+wBUF1eFjBGSx4bSc+dO55uyItdMgI=
			</data>
		</dict>
		<key>flutter_assets/FontManifest.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Xd5YKts7A+/AVNQiMQJno4Hup10MDsMYRkcPBGIb8N4=
			</data>
		</dict>
		<key>flutter_assets/NOTICES.Z</key>
		<dict>
			<key>hash2</key>
			<data>
			W7+aGUY58yuF2Kejxog5xfVhAI2xFfxDzVIRlFmSRTc=
			</data>
		</dict>
		<key>flutter_assets/NativeAssetsManifest.json</key>
		<dict>
			<key>hash2</key>
			<data>
			lUijHkoEgTXB2U+Rkyi/tirix7s8q5ZVfHlB2ql3dss=
			</data>
		</dict>
		<key>flutter_assets/assets/fonts/BITSUMISHI.TTF</key>
		<dict>
			<key>hash2</key>
			<data>
			+pu1jGbBfv0CA+kfx0J76A3mv+WBtriiLrRrfq6HsPU=
			</data>
		</dict>
		<key>flutter_assets/assets/fonts/Orbitron.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			FlLY+iILY8Mr4+zB36yJchTlJTnZ8aBq2/dcjyXDYb8=
			</data>
		</dict>
		<key>flutter_assets/assets/fonts/Pirulen.otf</key>
		<dict>
			<key>hash2</key>
			<data>
			YJCqUJBCVI2BsxLtwURi0/hwZea0j4Ru8gdWtne9kTo=
			</data>
		</dict>
		<key>flutter_assets/assets/fonts/SF-Pro.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			IWyPcPw1+4Fh0Dp/jd9f/1dg4YeWiNcOsg7Zd5uP/uI=
			</data>
		</dict>
		<key>flutter_assets/assets/fonts/digital-7.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			oQpEq+wOO6wBimSOyKcScoEhNA730ip7lzGUd85cm7s=
			</data>
		</dict>
		<key>flutter_assets/assets/images/f2_aura.png</key>
		<dict>
			<key>hash2</key>
			<data>
			SLsVtoqwWurUT+Brf/CeCBC//u6Amv5gL06HMNc0u1M=
			</data>
		</dict>
		<key>flutter_assets/assets/images/f3_aura.png</key>
		<dict>
			<key>hash2</key>
			<data>
			vOMbuyoroX54AUdMlH9HzHfyaxfNxxPavbm5igim9Zk=
			</data>
		</dict>
		<key>flutter_assets/assets/images/f_aura.png</key>
		<dict>
			<key>hash2</key>
			<data>
			zuKRz638gJo1pFxoVBJmvzEoLLMPde95SmvE5gn2Clc=
			</data>
		</dict>
		<key>flutter_assets/assets/images/guardian_tape_splash.png</key>
		<dict>
			<key>hash2</key>
			<data>
			Yl42wYQwgDGOk+hfHvlIfU5R8OwbRdfX4lsYCBuTtBg=
			</data>
		</dict>
		<key>flutter_assets/assets/images/m6_aura.png</key>
		<dict>
			<key>hash2</key>
			<data>
			jj8jz08g1AHYomSdUqOYdUZ4VD2o8OHwiNu49KJ2/CE=
			</data>
		</dict>
		<key>flutter_assets/assets/images/m_aura.png</key>
		<dict>
			<key>hash2</key>
			<data>
			DB8CP3a6636dAgsXoGkfqhjpXSpfTDCRvSDRC3vUOmo=
			</data>
		</dict>
		<key>flutter_assets/assets/images/mana_aura.png</key>
		<dict>
			<key>hash2</key>
			<data>
			TJZ5hZzwyS+dp+zn+AVKWt18DwspKkH8hK3TKQfNSXo=
			</data>
		</dict>
		<key>flutter_assets/assets/images/mana_aura_x.png</key>
		<dict>
			<key>hash2</key>
			<data>
			7oBh2YQtj1DgPQ3wJPz2riOr8LlyWiswFGgoREsSrqw=
			</data>
		</dict>
		<key>flutter_assets/assets/images/mana_aura_y.png</key>
		<dict>
			<key>hash2</key>
			<data>
			GZDhVOnhrZV+hnmOhVku7Qq2GvO0q1CTIMxl7Un99C8=
			</data>
		</dict>
		<key>flutter_assets/assets/images/maxed_out_life_icon.png</key>
		<dict>
			<key>hash2</key>
			<data>
			h/UGMZi5otI2fRQP1ebtqS/o3M1f0owZx0CwQfljiaU=
			</data>
		</dict>
		<key>flutter_assets/assets/images/mxd_logo.jpg</key>
		<dict>
			<key>hash2</key>
			<data>
			b2Q1a5bnmXGLvzkZvU9ZyO8STzBwfLzBlA2RnSEt/BY=
			</data>
		</dict>
		<key>flutter_assets/assets/images/mxd_logo_2.jpg</key>
		<dict>
			<key>hash2</key>
			<data>
			QhFtvGEt/Gn7vWysj1PLEZ7X4FqViPpb4lrsvY7DzaY=
			</data>
		</dict>
		<key>flutter_assets/assets/images/mxd_logo_3_icon.jpg</key>
		<dict>
			<key>hash2</key>
			<data>
			rmYqScYafPCkkAmiyeJSuMWxeLknstwGzOpMd4mJ31Y=
			</data>
		</dict>
		<key>flutter_assets/assets/images/mxd_logo_vx.png</key>
		<dict>
			<key>hash2</key>
			<data>
			AbPgUdacJHf55xBnGGUh7KAogHCdRDsETkmawLh1oBA=
			</data>
		</dict>
		<key>flutter_assets/assets/images/progress.png</key>
		<dict>
			<key>hash2</key>
			<data>
			yNFVnnvH0kWgQN8RGLvybM6OR894lKUx19TgfJDWqhg=
			</data>
		</dict>
		<key>flutter_assets/assets/notification_icons/achievement.png</key>
		<dict>
			<key>hash2</key>
			<data>
			vnwvpyEKrzQmwmWKFzje0SmHJdD503equYc/PeXjmB8=
			</data>
		</dict>
		<key>flutter_assets/assets/notification_icons/bounty.png</key>
		<dict>
			<key>hash2</key>
			<data>
			1VfXRA6rPW+ILMmjeI0g7sMZa2GrIA1WjOZehbhV+B4=
			</data>
		</dict>
		<key>flutter_assets/assets/notification_icons/f_progress.png</key>
		<dict>
			<key>hash2</key>
			<data>
			2sQfEpRBWmCh1CIy+7cGBmOTy45VOCteHlMD8CJo24Y=
			</data>
		</dict>
		<key>flutter_assets/assets/notification_icons/f_rank_up.png</key>
		<dict>
			<key>hash2</key>
			<data>
			h4MamQ8+oYhwI01/2mlmvwo3vii0WBlHhZM4GT4DMUU=
			</data>
		</dict>
		<key>flutter_assets/assets/notification_icons/f_reminder.png</key>
		<dict>
			<key>hash2</key>
			<data>
			zoMqx4hHE0AIsBm08QV/daGdXCdTmwUb3vJ/huCleqM=
			</data>
		</dict>
		<key>flutter_assets/assets/notification_icons/level_up.png</key>
		<dict>
			<key>hash2</key>
			<data>
			R7JSaUYqU6yjRWQBgql1r7Jgf8oaY6NG3wbMIMg6GPI=
			</data>
		</dict>
		<key>flutter_assets/assets/notification_icons/level_up_small.png</key>
		<dict>
			<key>hash2</key>
			<data>
			Hn0Zpz35fKPPuul/onbZKOtbH/LkWSRd/YE1zvsAkbE=
			</data>
		</dict>
		<key>flutter_assets/assets/notification_icons/m_progress.png</key>
		<dict>
			<key>hash2</key>
			<data>
			WyIlEFHCwxOnc9Jb6bzxb34EQh/e0d8jAdCRIIH0YfU=
			</data>
		</dict>
		<key>flutter_assets/assets/notification_icons/m_rank_up.png</key>
		<dict>
			<key>hash2</key>
			<data>
			3PaxWg8OLma/rR+NnMmfr2C6IhbCMQ+Neua12dLlYS8=
			</data>
		</dict>
		<key>flutter_assets/assets/notification_icons/m_reminder.png</key>
		<dict>
			<key>hash2</key>
			<data>
			PLMznosYT+lyCQ1upRTS28wT6933OGH7eiXlhu6+//w=
			</data>
		</dict>
		<key>flutter_assets/assets/notification_icons/quest_progress.png</key>
		<dict>
			<key>hash2</key>
			<data>
			H4jjmYIn8nPaU0dLc+JrsYkXyjDJmorXmcsJav3d0+I=
			</data>
		</dict>
		<key>flutter_assets/assets/sounds/HSError.mp3</key>
		<dict>
			<key>hash2</key>
			<data>
			y+HPntHY+WRVfkJ/6/pA/Nm7+g2zkNGT0SQUipWEKXA=
			</data>
		</dict>
		<key>flutter_assets/assets/sounds/HSError2.mp3</key>
		<dict>
			<key>hash2</key>
			<data>
			B3/iZUGZNHyoY8MUAu5CSvTVqIgHWHn6Pl8tVUqgrCk=
			</data>
		</dict>
		<key>flutter_assets/assets/sounds/MXD_Music.mp3</key>
		<dict>
			<key>hash2</key>
			<data>
			5A5Xm+1KdB3kHEshiRj/sWe5YYks2d5zX2zAN1a8Jmo=
			</data>
		</dict>
		<key>flutter_assets/assets/sounds/lightning.mp3</key>
		<dict>
			<key>hash2</key>
			<data>
			eqTAMgqYTj/EitZLT3xe+QJtSqORD4v0sNBclHK+Bvk=
			</data>
		</dict>
		<key>flutter_assets/assets/sounds/success.mp3</key>
		<dict>
			<key>hash2</key>
			<data>
			siV3VE+awGNLX0VVLLe8FGyo7JMx4O5tg1H1h+IGqus=
			</data>
		</dict>
		<key>flutter_assets/fonts/MaterialIcons-Regular.otf</key>
		<dict>
			<key>hash2</key>
			<data>
			tjH4DkoQeuRpeKpsIHXWJ9P5auoj7W4POY9R9REMhIs=
			</data>
		</dict>
		<key>flutter_assets/packages/cupertino_icons/assets/CupertinoIcons.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			Z8RP6Rg7AC553ef2l34piGYcmj5KPF/OloeH79vtgjw=
			</data>
		</dict>
		<key>flutter_assets/packages/golden_toolkit/fonts/Roboto-Regular.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			8OWiG/XJXkwbzivpijZW68xtQqIfQcTj6/ad2BVwLlQ=
			</data>
		</dict>
		<key>flutter_assets/shaders/ink_sparkle.frag</key>
		<dict>
			<key>hash2</key>
			<data>
			TGVjYgE+Oyl6guvhhPPrWfynkxkJeFjSzSLsQqn7Q3M=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
