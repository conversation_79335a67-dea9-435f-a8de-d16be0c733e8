// lib/debug_superintelligent_test.dart

import 'package:flutter/foundation.dart';
import 'models/user_model.dart';
import 'services/coach_orchestration_service.dart';
import 'services/smart_service_manager.dart';

/// Debug test to verify superintelligent AI features are working
class SuperintelligentDebugTest {
  
  /// Run comprehensive test of superintelligent features
  static Future<void> runDebugTest() async {
    if (!kDebugMode) {
      print('⚠️ Debug test only runs in debug mode');
      return;
    }
    
    print('🧪 SUPERINTELLIGENT AI DEBUG TEST');
    print('=' * 50);
    
    try {
      // Create test user
      final testUser = User.blank(
        id: 'debug_test_user',
        username: 'DebugTester',
      );
      // Note: gender is final, so we'll use the default
      
      print('👤 Test user created: ${testUser.username} (${testUser.id})');
      
      // Initialize services
      print('\n🚀 Initializing services...');
      await SmartServiceManager.initializeAllServices(user: testUser);
      
      // Intelligence systems are now stateless and don't require initialization
      print('\n🧠 Intelligence systems ready (stateless architecture)');

      // Check AI system status
      print('\n🤖 Checking AI system status...');
      final aiStatus = await _checkAISystem();
      print('📊 AI System Status: $aiStatus');
      
      // Test superintelligent response
      print('\n🤖 Testing superintelligent coach response...');
      print('📝 User prompt: "I want to build unstoppable confidence and become the best version of myself"');
      
      final response = await CoachOrchestrationService.generateSuperintelligentResponse(
        category: 'Health',
        userPrompt: 'I want to build unstoppable confidence and become the best version of myself',
        user: testUser,
      );
      
      print('\n📊 RESPONSE ANALYSIS:');
      print('Length: ${response.length} characters');
      print('Word count: ${response.split(' ').length} words');
      print('Contains user name: ${response.contains(testUser.username)}');
      print('Response preview: ${response.substring(0, response.length > 200 ? 200 : response.length)}...');
      
      // Check for superintelligent indicators
      final superintelligentIndicators = [
        'comprehensive',
        'holistic',
        'strategic',
        'evidence-based',
        'neuroscience',
        'psychology',
        'research',
        'studies',
        'proven',
        'systematic',
      ];
      
      final foundIndicators = superintelligentIndicators
          .where((indicator) => response.toLowerCase().contains(indicator))
          .toList();
      
      print('\n🧠 SUPERINTELLIGENCE INDICATORS FOUND:');
      if (foundIndicators.isNotEmpty) {
        for (final indicator in foundIndicators) {
          print('✅ $indicator');
        }
      } else {
        print('❌ No superintelligent indicators found - response may be basic');
      }
      
      // Test different categories
      print('\n🔄 Testing multiple categories...');
      
      final categories = ['Wealth', 'Purpose', 'Connection'];
      for (final category in categories) {
        print('\n📂 Testing $category coach...');
        final categoryResponse = await CoachOrchestrationService.generateSuperintelligentResponse(
          category: category,
          userPrompt: 'Help me excel in $category',
          user: testUser,
        );
        
        print('✅ $category response: ${categoryResponse.length} chars');
      }
      
      print('\n🎉 SUPERINTELLIGENT DEBUG TEST COMPLETE!');
      print('=' * 50);
      
    } catch (e, stackTrace) {
      print('❌ DEBUG TEST FAILED: $e');
      print('Stack trace: $stackTrace');
    }
  }
  
  /// Quick test to verify services are initialized
  static Future<bool> quickServiceCheck(String userId) async {
    try {
      print('🔍 Quick service check for user: $userId');
      
      // Services are now stateless and don't require initialization
      print('🧠 Stateless services ready');
      
      print('✅ Services initialized successfully');
      return true;
    } catch (e) {
      print('❌ Service check failed: $e');
      return false;
    }
  }

  /// Check AI system status
  static Future<String> _checkAISystem() async {
    try {
      // Test AI system availability
      final testUser = User.blank(id: 'ai_test', username: 'AITester');

      // Quick test response to verify AI connectivity
      final testResponse = await CoachOrchestrationService.generateSuperintelligentResponse(
        category: 'Health',
        userPrompt: 'Test AI connectivity',
        user: testUser,
      );

      if (testResponse.isEmpty) {
        return '❌ AI system not responding - check API configuration';
      }

      return '✅ AI system operational - response length: ${testResponse.length} characters';

    } catch (e) {
      return '❌ AI system error: $e';
    }
  }
}
