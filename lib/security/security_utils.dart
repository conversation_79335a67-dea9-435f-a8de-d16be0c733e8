import 'dart:convert';
import 'dart:math';
import 'dart:typed_data';
import 'package:crypto/crypto.dart';
import '../services/bulletproof_secure_storage.dart';
import 'package:flutter/foundation.dart';

/// Security utilities for password hashing, encryption, and validation.
/// 
/// Provides comprehensive security functions including:
/// - Secure password hashing with salt
/// - Password strength validation
/// - Data encryption/decryption
/// - Secure random generation
/// - Input sanitization
/// 
/// Example usage:
/// ```dart
/// // Hash a password
/// final hashedPassword = SecurityUtils.hashPassword('myPassword123');
/// 
/// // Verify password
/// final isValid = SecurityUtils.verifyPassword('myPassword123', hashedPassword);
/// 
/// // Check password strength
/// final strength = SecurityUtils.getPasswordStrength('myPassword123');
/// ```
class SecurityUtils {
  static const FlutterSecureStorage _secureStorage = FlutterSecureStorage();
  static final Random _random = Random.secure();

  // ═══════════════════════════════════════════════════════════════════════════
  // PASSWORD SECURITY
  // ═══════════════════════════════════════════════════════════════════════════

  /// Hash a password with a random salt using PBKDF2
  static String hashPassword(String password) {
    final salt = _generateSalt();
    final hashedPassword = _pbkdf2(password, salt, 10000, 32);
    return '$salt:${base64Encode(hashedPassword)}';
  }

  /// Verify a password against its hash
  static bool verifyPassword(String password, String hashedPassword) {
    try {
      final parts = hashedPassword.split(':');
      if (parts.length != 2) return false;

      final salt = parts[0];
      final hash = base64Decode(parts[1]);
      final computedHash = _pbkdf2(password, salt, 10000, 32);

      return _constantTimeEquals(hash, computedHash);
    } catch (e) {
      return false;
    }
  }

  /// Generate a cryptographically secure salt
  static String _generateSalt([int length = 32]) {
    final bytes = Uint8List(length);
    for (int i = 0; i < length; i++) {
      bytes[i] = _random.nextInt(256);
    }
    return base64Encode(bytes);
  }

  /// PBKDF2 key derivation function
  static Uint8List _pbkdf2(String password, String salt, int iterations, int keyLength) {
    final passwordBytes = utf8.encode(password);
    final saltBytes = base64Decode(salt);
    
    var hmac = Hmac(sha256, passwordBytes);
    var digest = hmac.convert(saltBytes + [0, 0, 0, 1]);
    var result = Uint8List.fromList(digest.bytes);
    
    for (int i = 1; i < iterations; i++) {
      digest = hmac.convert(digest.bytes);
      for (int j = 0; j < result.length; j++) {
        result[j] ^= digest.bytes[j];
      }
    }
    
    return Uint8List.fromList(result.take(keyLength).toList());
  }

  /// Constant-time comparison to prevent timing attacks
  static bool _constantTimeEquals(List<int> a, List<int> b) {
    if (a.length != b.length) return false;
    
    int result = 0;
    for (int i = 0; i < a.length; i++) {
      result |= a[i] ^ b[i];
    }
    return result == 0;
  }

  // ═══════════════════════════════════════════════════════════════════════════
  // PASSWORD VALIDATION
  // ═══════════════════════════════════════════════════════════════════════════

  /// Get password strength score (0-100)
  static PasswordStrength getPasswordStrength(String password) {
    int score = 0;
    List<String> feedback = [];

    // Length check
    if (password.length >= 8) {
      score += 20;
    } else {
      feedback.add('Password should be at least 8 characters long');
    }

    if (password.length >= 12) {
      score += 10;
    }

    // Character variety checks
    if (RegExp(r'[a-z]').hasMatch(password)) {
      score += 15;
    } else {
      feedback.add('Add lowercase letters');
    }

    if (RegExp(r'[A-Z]').hasMatch(password)) {
      score += 15;
    } else {
      feedback.add('Add uppercase letters');
    }

    if (RegExp(r'[0-9]').hasMatch(password)) {
      score += 15;
    } else {
      feedback.add('Add numbers');
    }

    if (RegExp(r'[!@#$%^&*(),.?":{}|<>]').hasMatch(password)) {
      score += 15;
    } else {
      feedback.add('Add special characters');
    }

    // Complexity bonus
    if (password.length >= 16) score += 5;
    if (RegExp(r'[a-z].*[A-Z]|[A-Z].*[a-z]').hasMatch(password)) score += 5;

    // Common password penalty
    if (_isCommonPassword(password)) {
      score = (score * 0.5).round();
      feedback.add('Avoid common passwords');
    }

    return PasswordStrength(
      score: score.clamp(0, 100),
      level: _getStrengthLevel(score),
      feedback: feedback,
    );
  }

  /// Check if password is in common passwords list
  static bool _isCommonPassword(String password) {
    final commonPasswords = [
      'password', '123456', '123456789', 'qwerty', 'abc123',
      'password123', 'admin', 'letmein', 'welcome', 'monkey',
      'dragon', 'master', 'shadow', 'superman', 'michael',
      'football', 'baseball', 'liverpool', 'jordan', 'princess',
    ];
    return commonPasswords.contains(password.toLowerCase());
  }

  /// Get strength level from score
  static PasswordStrengthLevel _getStrengthLevel(int score) {
    if (score >= 80) return PasswordStrengthLevel.strong;
    if (score >= 60) return PasswordStrengthLevel.good;
    if (score >= 40) return PasswordStrengthLevel.fair;
    if (score >= 20) return PasswordStrengthLevel.weak;
    return PasswordStrengthLevel.veryWeak;
  }

  // ═══════════════════════════════════════════════════════════════════════════
  // DATA ENCRYPTION
  // ═══════════════════════════════════════════════════════════════════════════

  /// Encrypt sensitive data using AES
  static Future<String> encryptData(String data, String key) async {
    try {
      // For now, use base64 encoding as a placeholder
      // In production, implement proper AES encryption
      final bytes = utf8.encode(data);
      return base64Encode(bytes);
    } catch (e) {
      throw SecurityException('Failed to encrypt data: $e');
    }
  }

  /// Decrypt sensitive data
  static Future<String> decryptData(String encryptedData, String key) async {
    try {
      // For now, use base64 decoding as a placeholder
      // In production, implement proper AES decryption
      final bytes = base64Decode(encryptedData);
      return utf8.decode(bytes);
    } catch (e) {
      throw SecurityException('Failed to decrypt data: $e');
    }
  }

  // ═══════════════════════════════════════════════════════════════════════════
  // SECURE STORAGE
  // ═══════════════════════════════════════════════════════════════════════════

  /// Store sensitive data securely
  static Future<void> storeSecureData(String key, String value) async {
    try {
      await _secureStorage.write(key: key, value: value);
    } catch (e) {
      throw SecurityException('Failed to store secure data: $e');
    }
  }

  /// Retrieve sensitive data securely
  static Future<String?> getSecureData(String key) async {
    try {
      return await _secureStorage.read(key: key);
    } catch (e) {
      throw SecurityException('Failed to retrieve secure data: $e');
    }
  }

  /// Delete sensitive data
  static Future<void> deleteSecureData(String key) async {
    try {
      await _secureStorage.delete(key: key);
    } catch (e) {
      throw SecurityException('Failed to delete secure data: $e');
    }
  }

  // ═══════════════════════════════════════════════════════════════════════════
  // INPUT VALIDATION
  // ═══════════════════════════════════════════════════════════════════════════

  /// Sanitize user input to prevent injection attacks
  static String sanitizeInput(String input) {
    return input
        .replaceAll('<', '')
        .replaceAll('>', '')
        .replaceAll('"', '')
        .replaceAll("'", '')
        .replaceAll('&', '&amp;')
        .trim();
  }

  /// Validate email format
  static bool isValidEmail(String email) {
    return RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
        .hasMatch(email);
  }

  /// Validate username format
  static bool isValidUsername(String username) {
    return RegExp(r'^[a-zA-Z0-9_]{3,20}$').hasMatch(username);
  }

  /// Generate secure random string
  static String generateSecureToken([int length = 32]) {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    return List.generate(length, (index) => chars[_random.nextInt(chars.length)]).join();
  }

  /// Generate secure numeric PIN
  static String generateSecurePIN([int length = 6]) {
    return List.generate(length, (index) => _random.nextInt(10).toString()).join();
  }
}

/// Password strength assessment result
class PasswordStrength {
  final int score;
  final PasswordStrengthLevel level;
  final List<String> feedback;

  const PasswordStrength({
    required this.score,
    required this.level,
    required this.feedback,
  });

  bool get isAcceptable => score >= 40;
  bool get isStrong => score >= 80;

  String get description {
    switch (level) {
      case PasswordStrengthLevel.veryWeak:
        return 'Very Weak';
      case PasswordStrengthLevel.weak:
        return 'Weak';
      case PasswordStrengthLevel.fair:
        return 'Fair';
      case PasswordStrengthLevel.good:
        return 'Good';
      case PasswordStrengthLevel.strong:
        return 'Strong';
    }
  }
}

/// Password strength levels
enum PasswordStrengthLevel {
  veryWeak,
  weak,
  fair,
  good,
  strong,
}

/// Security-related exception
class SecurityException implements Exception {
  final String message;
  const SecurityException(this.message);

  @override
  String toString() => 'SecurityException: $message';
}
