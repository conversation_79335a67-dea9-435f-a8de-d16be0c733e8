import 'package:flutter/material.dart';
import '../../design_system/design_system.dart';
import '../../models/user_model.dart';
import '../biometric_auth_service.dart';
import '../session_manager.dart';


/// Security settings screen for managing authentication and privacy options.
/// 
/// Provides comprehensive security management including:
/// - Biometric authentication settings
/// - Password change functionality
/// - Session management
/// - Security status overview
/// - Privacy controls
/// 
/// Example usage:
/// ```dart
/// Navigator.push(
///   context,
///   MaterialPageRoute(
///     builder: (context) => SecuritySettingsScreen(user: currentUser),
///   ),
/// );
/// ```
class SecuritySettingsScreen extends StatefulWidget {
  final User user;

  const SecuritySettingsScreen({
    super.key,
    required this.user,
  });

  @override
  State<SecuritySettingsScreen> createState() => _SecuritySettingsScreenState();
}

class _SecuritySettingsScreenState extends State<SecuritySettingsScreen> {
  final BiometricAuthService _biometricService = BiometricAuthService();
  final SessionManager _sessionManager = SessionManager();
  
  bool _isLoading = false;
  BiometricAuthStatus? _biometricStatus;
  SessionStats? _sessionStats;

  @override
  void initState() {
    super.initState();
    _loadSecurityStatus();
  }

  Future<void> _loadSecurityStatus() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final biometricStatus = await _biometricService.getAuthStatus(widget.user.username);
      final sessionStats = await _sessionManager.getSessionStats(widget.user.username);
      
      if (mounted) {
        setState(() {
          _biometricStatus = biometricStatus;
          _sessionStats = sessionStats;
        });
      }
    } catch (e) {
      // Handle error
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load security status: $e'),
            backgroundColor: MolColors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        title: Text(
          'Security Settings',
          style: AppTypography.withGlow(
            AppTypography.headingMedium,
            MolColors.cyan,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(MolColors.cyan),
              ),
            )
          : SingleChildScrollView(
              padding: EdgeInsets.all(DesignTokens.space2xl),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Security Overview
                  _buildSecurityOverview(),
                  
                  SizedBox(height: DesignTokens.space3xl),
                  
                  // Biometric Authentication
                  _buildBiometricSection(),
                  
                  SizedBox(height: DesignTokens.space3xl),
                  
                  // Password Security
                  _buildPasswordSection(),
                  
                  SizedBox(height: DesignTokens.space3xl),
                  
                  // Session Management
                  _buildSessionSection(),
                  
                  SizedBox(height: DesignTokens.space3xl),
                  
                  // Privacy Controls
                  _buildPrivacySection(),
                ],
              ),
            ),
    );
  }

  Widget _buildSecurityOverview() {
    final securityScore = _calculateSecurityScore();
    final scoreColor = _getSecurityScoreColor(securityScore);
    
    return AppCard.glow(
      glowColor: scoreColor,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.security,
                color: scoreColor,
                size: DesignTokens.iconSizeLg,
              ),
              SizedBox(width: DesignTokens.spaceLg),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Security Score',
                      style: AppTypography.withColor(
                        AppTypography.headingSmall,
                        Colors.white,
                      ),
                    ),
                    Text(
                      '$securityScore/100',
                      style: AppTypography.withColor(
                        AppTypography.headingLarge,
                        scoreColor,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: DesignTokens.spaceLg),
          LinearProgressIndicator(
            value: securityScore / 100,
            backgroundColor: Colors.grey[800],
            valueColor: AlwaysStoppedAnimation<Color>(scoreColor),
          ),
          SizedBox(height: DesignTokens.spaceLg),
          Text(
            _getSecurityScoreDescription(securityScore),
            style: AppTypography.withColor(
              AppTypography.bodyMedium,
              Colors.white70,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBiometricSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Biometric Authentication',
          style: AppTypography.withColor(
            AppTypography.headingMedium,
            Colors.white,
          ),
        ),
        SizedBox(height: DesignTokens.spaceLg),
        
        if (_biometricStatus != null) ...[
          AppCard.outlined(
            glowColor: _biometricStatus!.isEnabled ? MolColors.green : MolColors.blue,
            child: Column(
              children: [
                Row(
                  children: [
                    Icon(
                      _biometricStatus!.isEnabled ? Icons.check_circle : Icons.fingerprint,
                      color: _biometricStatus!.isEnabled ? MolColors.green : MolColors.blue,
                      size: DesignTokens.iconSizeLg,
                    ),
                    SizedBox(width: DesignTokens.spaceLg),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            _biometricStatus!.isEnabled ? 'Enabled' : 'Disabled',
                            style: AppTypography.withColor(
                              AppTypography.labelLarge,
                              _biometricStatus!.isEnabled ? MolColors.green : Colors.white,
                            ),
                          ),
                          Text(
                            _biometricStatus!.statusDescription,
                            style: AppTypography.withColor(
                              AppTypography.bodySmall,
                              Colors.white70,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Switch(
                      value: _biometricStatus!.isEnabled,
                      onChanged: _biometricStatus!.isAvailable ? _toggleBiometric : null,
                      activeColor: MolColors.green,
                    ),
                  ],
                ),
                
                if (_biometricStatus!.availableBiometrics.isNotEmpty) ...[
                  SizedBox(height: DesignTokens.spaceLg),
                  Wrap(
                    spacing: DesignTokens.spaceMd,
                    children: _biometricStatus!.availableBiometrics.map((type) {
                      return Chip(
                        label: Text(
                          type.name.toUpperCase(),
                          style: AppTypography.withColor(
                            AppTypography.captionSmall,
                            Colors.white,
                          ),
                        ),
                        backgroundColor: MolColors.blue.withValues(alpha: 0.2),
                        side: BorderSide(color: MolColors.blue),
                      );
                    }).toList(),
                  ),
                ],
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildPasswordSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Password Security',
          style: AppTypography.withColor(
            AppTypography.headingMedium,
            Colors.white,
          ),
        ),
        SizedBox(height: DesignTokens.spaceLg),
        
        AppCard.outlined(
          child: Column(
            children: [
              ListTile(
                leading: Icon(
                  Icons.lock_outline,
                  color: MolColors.yellow,
                  size: DesignTokens.iconSizeLg,
                ),
                title: Text(
                  'Change Password',
                  style: AppTypography.withColor(
                    AppTypography.labelLarge,
                    Colors.white,
                  ),
                ),
                subtitle: Text(
                  'Update your account password',
                  style: AppTypography.withColor(
                    AppTypography.bodySmall,
                    Colors.white70,
                  ),
                ),
                trailing: Icon(
                  Icons.arrow_forward_ios,
                  color: Colors.white54,
                  size: DesignTokens.iconSizeSm,
                ),
                onTap: _showChangePasswordDialog,
              ),
              
              Divider(color: Colors.grey[800]),
              
              ListTile(
                leading: Icon(
                  Icons.history,
                  color: MolColors.purple,
                  size: DesignTokens.iconSizeLg,
                ),
                title: Text(
                  'Password Strength',
                  style: AppTypography.withColor(
                    AppTypography.labelLarge,
                    Colors.white,
                  ),
                ),
                subtitle: Text(
                  'Check your password security',
                  style: AppTypography.withColor(
                    AppTypography.bodySmall,
                    Colors.white70,
                  ),
                ),
                trailing: Icon(
                  Icons.arrow_forward_ios,
                  color: Colors.white54,
                  size: DesignTokens.iconSizeSm,
                ),
                onTap: _showPasswordStrengthDialog,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSessionSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Session Management',
          style: AppTypography.withColor(
            AppTypography.headingMedium,
            Colors.white,
          ),
        ),
        SizedBox(height: DesignTokens.spaceLg),
        
        AppCard.outlined(
          child: Column(
            children: [
              if (_sessionStats != null) ...[
                ListTile(
                  leading: Icon(
                    Icons.devices,
                    color: MolColors.cyan,
                    size: DesignTokens.iconSizeLg,
                  ),
                  title: Text(
                    'Active Sessions',
                    style: AppTypography.withColor(
                      AppTypography.labelLarge,
                      Colors.white,
                    ),
                  ),
                  subtitle: Text(
                    'Total logins: ${_sessionStats!.sessionCount}',
                    style: AppTypography.withColor(
                      AppTypography.bodySmall,
                      Colors.white70,
                    ),
                  ),
                  trailing: Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: DesignTokens.spaceMd,
                      vertical: DesignTokens.spaceSm,
                    ),
                    decoration: BoxDecoration(
                      color: _sessionStats!.isActive ? MolColors.green : MolColors.red,
                      borderRadius: BorderRadius.circular(DesignTokens.radiusFull),
                    ),
                    child: Text(
                      _sessionStats!.isActive ? 'ACTIVE' : 'INACTIVE',
                      style: AppTypography.withColor(
                        AppTypography.captionSmall,
                        Colors.white,
                      ),
                    ),
                  ),
                ),
                
                Divider(color: Colors.grey[800]),
              ],
              
              ListTile(
                leading: Icon(
                  Icons.logout,
                  color: MolColors.red,
                  size: DesignTokens.iconSizeLg,
                ),
                title: Text(
                  'Sign Out All Devices',
                  style: AppTypography.withColor(
                    AppTypography.labelLarge,
                    Colors.white,
                  ),
                ),
                subtitle: Text(
                  'End all active sessions',
                  style: AppTypography.withColor(
                    AppTypography.bodySmall,
                    Colors.white70,
                  ),
                ),
                onTap: _showSignOutAllDialog,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildPrivacySection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Privacy Controls',
          style: AppTypography.withColor(
            AppTypography.headingMedium,
            Colors.white,
          ),
        ),
        SizedBox(height: DesignTokens.spaceLg),
        
        AppCard.outlined(
          child: Column(
            children: [
              SwitchListTile(
                secondary: Icon(
                  Icons.visibility_off,
                  color: MolColors.purple,
                  size: DesignTokens.iconSizeLg,
                ),
                title: Text(
                  'App Lock',
                  style: AppTypography.withColor(
                    AppTypography.labelLarge,
                    Colors.white,
                  ),
                ),
                subtitle: Text(
                  'Require authentication when opening app',
                  style: AppTypography.withColor(
                    AppTypography.bodySmall,
                    Colors.white70,
                  ),
                ),
                value: true, // This would be from user preferences
                onChanged: (value) {
                  // Handle app lock toggle
                },
                activeColor: MolColors.green,
              ),
              
              Divider(color: Colors.grey[800]),
              
              SwitchListTile(
                secondary: Icon(
                  Icons.screenshot,
                  color: MolColors.orange,
                  size: DesignTokens.iconSizeLg,
                ),
                title: Text(
                  'Block Screenshots',
                  style: AppTypography.withColor(
                    AppTypography.labelLarge,
                    Colors.white,
                  ),
                ),
                subtitle: Text(
                  'Prevent screenshots in sensitive areas',
                  style: AppTypography.withColor(
                    AppTypography.bodySmall,
                    Colors.white70,
                  ),
                ),
                value: false, // This would be from user preferences
                onChanged: (value) {
                  // Handle screenshot blocking toggle
                },
                activeColor: MolColors.green,
              ),
            ],
          ),
        ),
      ],
    );
  }

  // Helper methods

  int _calculateSecurityScore() {
    int score = 0;
    
    // Base score for having a password
    if (widget.user.passcode != null) score += 30;
    
    // Biometric authentication
    if (_biometricStatus?.isEnabled == true) score += 40;
    
    // Active session management
    if (_sessionStats?.isActive == true) score += 20;
    
    // Additional security features
    score += 10; // Base security features
    
    return score.clamp(0, 100);
  }

  Color _getSecurityScoreColor(int score) {
    if (score >= 80) return MolColors.green;
    if (score >= 60) return MolColors.yellow;
    if (score >= 40) return MolColors.orange;
    return MolColors.red;
  }

  String _getSecurityScoreDescription(int score) {
    if (score >= 80) return 'Excellent security! Your account is well protected.';
    if (score >= 60) return 'Good security. Consider enabling additional features.';
    if (score >= 40) return 'Fair security. Some improvements recommended.';
    return 'Poor security. Please enable additional security features.';
  }

  void _toggleBiometric(bool value) async {
    // Implementation would depend on having password verification
    // For now, just show a placeholder
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Biometric ${value ? 'enabled' : 'disabled'}'),
        backgroundColor: MolColors.green,
      ),
    );
    
    await _loadSecurityStatus();
  }

  void _showChangePasswordDialog() {
    // Show password change dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.grey[900],
        title: Text(
          'Change Password',
          style: AppTypography.withColor(
            AppTypography.headingSmall,
            Colors.white,
          ),
        ),
        content: Text(
          'Password change functionality would be implemented here.',
          style: AppTypography.withColor(
            AppTypography.bodyMedium,
            Colors.white70,
          ),
        ),
        actions: [
          AppButton.tertiary(
            text: 'Cancel',
            onPressed: () => Navigator.pop(context),
          ),
          AppButton.primary(
            text: 'Change',
            onPressed: () => Navigator.pop(context),
          ),
        ],
      ),
    );
  }

  void _showPasswordStrengthDialog() {
    // Show password strength analysis
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.grey[900],
        title: Text(
          'Password Strength',
          style: AppTypography.withColor(
            AppTypography.headingSmall,
            Colors.white,
          ),
        ),
        content: Text(
          'Password strength analysis would be shown here.',
          style: AppTypography.withColor(
            AppTypography.bodyMedium,
            Colors.white70,
          ),
        ),
        actions: [
          AppButton.primary(
            text: 'OK',
            onPressed: () => Navigator.pop(context),
          ),
        ],
      ),
    );
  }

  void _showSignOutAllDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.grey[900],
        title: Text(
          'Sign Out All Devices',
          style: AppTypography.withColor(
            AppTypography.headingSmall,
            Colors.white,
          ),
        ),
        content: Text(
          'This will end all active sessions on all devices. You will need to sign in again.',
          style: AppTypography.withColor(
            AppTypography.bodyMedium,
            Colors.white70,
          ),
        ),
        actions: [
          AppButton.tertiary(
            text: 'Cancel',
            onPressed: () => Navigator.pop(context),
          ),
          AppButton.destructive(
            text: 'Sign Out All',
            onPressed: () async {
              Navigator.pop(context);
              await _sessionManager.clearAllSessions();
              await _loadSecurityStatus();
              
              if (mounted) {
                // ignore: use_build_context_synchronously
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('All sessions have been ended'),
                    backgroundColor: MolColors.green,
                  ),
                );
              }
            },
          ),
        ],
      ),
    );
  }
}
