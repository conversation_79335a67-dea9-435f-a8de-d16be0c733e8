import 'dart:async';
import '../services/bulletproof_secure_storage.dart';
import '../bulletproof/error_handler.dart';
import 'security_utils.dart';

/// Secure session management for user authentication state.
/// 
/// Provides comprehensive session management including:
/// - Session creation and validation
/// - Automatic session expiry
/// - Session refresh and renewal
/// - Secure token storage
/// - Multi-device session handling
/// 
/// Example usage:
/// ```dart
/// final sessionManager = SessionManager();
/// await sessionManager.initialize();
/// 
/// // Create session after successful login
/// final session = await sessionManager.createSession('username123');
/// 
/// // Validate session
/// final isValid = await sessionManager.isSessionValid('username123');
/// 
/// // Refresh session
/// await sessionManager.refreshSession('username123');
/// ```
class SessionManager {
  static final SessionManager _instance = SessionManager._internal();
  factory SessionManager() => _instance;
  SessionManager._internal();

  final FlutterSecureStorage _secureStorage = const FlutterSecureStorage();
  final ErrorHandler _errorHandler = ErrorHandler();

  static const String _sessionTokenKey = 'session_token';
  static const String _sessionExpiryKey = 'session_expiry';
  static const String _sessionCreatedKey = 'session_created';
  static const String _sessionRefreshKey = 'session_refresh';
  static const String _deviceIdKey = 'device_id';
  static const String _sessionCountKey = 'session_count';

  // Session configuration
  static const Duration _sessionDuration = Duration(hours: 24);
  static const Duration _refreshThreshold = Duration(hours: 2);
  static const Duration _maxSessionDuration = Duration(days: 30);
  // static const int _maxConcurrentSessions = 5; // Unused field - commented out

  bool _isInitialized = false;
  String? _deviceId;
  Timer? _sessionCheckTimer;

  /// Initialize the session manager
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _generateDeviceId();
      _startSessionMonitoring();
      _isInitialized = true;
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'SessionManager.initialize');
    }
  }

  /// Create a new session for the user
  Future<UserSession> createSession(String username) async {
    try {
      await initialize();

      // Generate secure session token
      final sessionToken = SecurityUtils.generateSecureToken(64);
      final now = DateTime.now();
      final expiryTime = now.add(_sessionDuration);

      // Store session data securely
      await _secureStorage.write(
        key: '${_sessionTokenKey}_$username',
        value: sessionToken,
      );
      await _secureStorage.write(
        key: '${_sessionExpiryKey}_$username',
        value: expiryTime.toIso8601String(),
      );
      await _secureStorage.write(
        key: '${_sessionCreatedKey}_$username',
        value: now.toIso8601String(),
      );
      await _secureStorage.write(
        key: '${_sessionRefreshKey}_$username',
        value: now.toIso8601String(),
      );
      await _secureStorage.write(
        key: '${_deviceIdKey}_$username',
        value: _deviceId!,
      );

      // Update session count
      await _incrementSessionCount(username);

      return UserSession(
        username: username,
        sessionToken: sessionToken,
        createdAt: now,
        expiresAt: expiryTime,
        lastRefresh: now,
        deviceId: _deviceId!,
      );
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'SessionManager.createSession');
      throw SessionException('Failed to create session');
    }
  }

  /// Validate if a session is still valid
  Future<bool> isSessionValid(String username) async {
    try {
      await initialize();

      final sessionToken = await _secureStorage.read(key: '${_sessionTokenKey}_$username');
      if (sessionToken == null) return false;

      final expiryTimeStr = await _secureStorage.read(key: '${_sessionExpiryKey}_$username');
      if (expiryTimeStr == null) return false;

      final expiryTime = DateTime.parse(expiryTimeStr);
      final now = DateTime.now();

      // Check if session has expired
      if (now.isAfter(expiryTime)) {
        await _clearSession(username);
        return false;
      }

      // Check if session needs refresh
      if (now.isAfter(expiryTime.subtract(_refreshThreshold))) {
        await refreshSession(username);
      }

      return true;
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'SessionManager.isSessionValid');
      return false;
    }
  }

  /// Get current session information
  Future<UserSession?> getCurrentSession(String username) async {
    try {
      await initialize();

      if (!await isSessionValid(username)) return null;

      final sessionToken = await _secureStorage.read(key: '${_sessionTokenKey}_$username');
      final expiryTimeStr = await _secureStorage.read(key: '${_sessionExpiryKey}_$username');
      final createdTimeStr = await _secureStorage.read(key: '${_sessionCreatedKey}_$username');
      final refreshTimeStr = await _secureStorage.read(key: '${_sessionRefreshKey}_$username');
      final deviceId = await _secureStorage.read(key: '${_deviceIdKey}_$username');

      if (sessionToken == null || expiryTimeStr == null || createdTimeStr == null) {
        return null;
      }

      return UserSession(
        username: username,
        sessionToken: sessionToken,
        createdAt: DateTime.parse(createdTimeStr),
        expiresAt: DateTime.parse(expiryTimeStr),
        lastRefresh: refreshTimeStr != null ? DateTime.parse(refreshTimeStr) : DateTime.parse(createdTimeStr),
        deviceId: deviceId ?? _deviceId!,
      );
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'SessionManager.getCurrentSession');
      return null;
    }
  }

  /// Refresh an existing session
  Future<bool> refreshSession(String username) async {
    try {
      await initialize();

      final currentSession = await getCurrentSession(username);
      if (currentSession == null) return false;

      // Check if session can be refreshed (not too old)
      final sessionAge = DateTime.now().difference(currentSession.createdAt);
      if (sessionAge > _maxSessionDuration) {
        await _clearSession(username);
        return false;
      }

      // Extend session expiry
      final now = DateTime.now();
      final newExpiryTime = now.add(_sessionDuration);

      await _secureStorage.write(
        key: '${_sessionExpiryKey}_$username',
        value: newExpiryTime.toIso8601String(),
      );
      await _secureStorage.write(
        key: '${_sessionRefreshKey}_$username',
        value: now.toIso8601String(),
      );

      return true;
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'SessionManager.refreshSession');
      return false;
    }
  }

  /// Clear session for a user
  Future<void> clearSession(String username) async {
    try {
      await _clearSession(username);
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'SessionManager.clearSession');
    }
  }

  /// Clear all sessions (logout from all devices)
  Future<void> clearAllSessions() async {
    try {
      await initialize();

      // Get all stored keys
      final allKeys = await _secureStorage.readAll();
      
      // Find and delete all session-related keys
      for (final key in allKeys.keys) {
        if (key.startsWith(_sessionTokenKey) ||
            key.startsWith(_sessionExpiryKey) ||
            key.startsWith(_sessionCreatedKey) ||
            key.startsWith(_sessionRefreshKey) ||
            key.startsWith(_deviceIdKey) ||
            key.startsWith(_sessionCountKey)) {
          await _secureStorage.delete(key: key);
        }
      }
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'SessionManager.clearAllSessions');
    }
  }

  /// Get session statistics
  Future<SessionStats> getSessionStats(String username) async {
    try {
      await initialize();

      final currentSession = await getCurrentSession(username);
      final sessionCountStr = await _secureStorage.read(key: '${_sessionCountKey}_$username') ?? '0';
      final sessionCount = int.parse(sessionCountStr);

      return SessionStats(
        isActive: currentSession != null,
        sessionCount: sessionCount,
        currentSession: currentSession,
        deviceId: _deviceId!,
      );
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'SessionManager.getSessionStats');
      return SessionStats(
        isActive: false,
        sessionCount: 0,
        currentSession: null,
        deviceId: _deviceId ?? 'unknown',
      );
    }
  }

  /// Dispose resources
  void dispose() {
    _sessionCheckTimer?.cancel();
  }

  // Private methods

  Future<void> _generateDeviceId() async {
    try {
      _deviceId = await _secureStorage.read(key: _deviceIdKey);
      if (_deviceId == null) {
        _deviceId = SecurityUtils.generateSecureToken(32);
        await _secureStorage.write(key: _deviceIdKey, value: _deviceId!);
      }
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'SessionManager._generateDeviceId');
      _deviceId = SecurityUtils.generateSecureToken(32);
    }
  }

  Future<void> _clearSession(String username) async {
    await _secureStorage.delete(key: '${_sessionTokenKey}_$username');
    await _secureStorage.delete(key: '${_sessionExpiryKey}_$username');
    await _secureStorage.delete(key: '${_sessionCreatedKey}_$username');
    await _secureStorage.delete(key: '${_sessionRefreshKey}_$username');
    await _secureStorage.delete(key: '${_deviceIdKey}_$username');
  }

  Future<void> _incrementSessionCount(String username) async {
    try {
      final countStr = await _secureStorage.read(key: '${_sessionCountKey}_$username') ?? '0';
      final count = int.parse(countStr) + 1;
      await _secureStorage.write(key: '${_sessionCountKey}_$username', value: count.toString());
    } catch (e) {
      await _secureStorage.write(key: '${_sessionCountKey}_$username', value: '1');
    }
  }

  void _startSessionMonitoring() {
    _sessionCheckTimer = Timer.periodic(const Duration(minutes: 5), (timer) async {
      // Periodic cleanup of expired sessions
      try {
        final allKeys = await _secureStorage.readAll();
        final now = DateTime.now();

        for (final key in allKeys.keys) {
          if (key.startsWith(_sessionExpiryKey)) {
            final expiryTimeStr = allKeys[key];
            if (expiryTimeStr != null) {
              final expiryTime = DateTime.parse(expiryTimeStr);
              if (now.isAfter(expiryTime)) {
                final username = key.replaceFirst('${_sessionExpiryKey}_', '');
                await _clearSession(username);
              }
            }
          }
        }
      } catch (e, stackTrace) {
        await _errorHandler.handleError(e, stackTrace, context: 'SessionManager._startSessionMonitoring');
      }
    });
  }
}

/// User session data
class UserSession {
  final String username;
  final String sessionToken;
  final DateTime createdAt;
  final DateTime expiresAt;
  final DateTime lastRefresh;
  final String deviceId;

  const UserSession({
    required this.username,
    required this.sessionToken,
    required this.createdAt,
    required this.expiresAt,
    required this.lastRefresh,
    required this.deviceId,
  });

  bool get isExpired => DateTime.now().isAfter(expiresAt);
  bool get needsRefresh => DateTime.now().isAfter(expiresAt.subtract(const Duration(hours: 2)));
  Duration get timeUntilExpiry => expiresAt.difference(DateTime.now());
  Duration get sessionAge => DateTime.now().difference(createdAt);
}

/// Session statistics
class SessionStats {
  final bool isActive;
  final int sessionCount;
  final UserSession? currentSession;
  final String deviceId;

  const SessionStats({
    required this.isActive,
    required this.sessionCount,
    this.currentSession,
    required this.deviceId,
  });
}

/// Session-related exception
class SessionException implements Exception {
  final String message;
  const SessionException(this.message);

  @override
  String toString() => 'SessionException: $message';
}
