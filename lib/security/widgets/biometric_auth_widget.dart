import 'package:flutter/material.dart';
// import 'package:local_auth/local_auth.dart'; // Package not available
import '../../design_system/design_system.dart';
import '../biometric_auth_service.dart';

/// Biometric authentication widget with modern UI design.
/// 
/// Provides a user-friendly interface for biometric authentication including:
/// - Fingerprint and face ID support
/// - Visual feedback and animations
/// - Error handling and fallback options
/// - Accessibility support
/// 
/// Example usage:
/// ```dart
/// BiometricAuthWidget(
///   username: 'john_doe',
///   onSuccess: (result) => print('Auth successful'),
///   onError: (error) => print('Auth failed: $error'),
///   onFallback: () => showPasswordDialog(),
/// )
/// ```
class BiometricAuthWidget extends StatefulWidget {
  final String username;
  final String? title;
  final String? subtitle;
  final Function(BiometricAuthResult)? onSuccess;
  final Function(String)? onError;
  final VoidCallback? onFallback;
  final bool showFallbackButton;
  final Color? accentColor;

  const BiometricAuthWidget({
    super.key,
    required this.username,
    this.title,
    this.subtitle,
    this.onSuccess,
    this.onError,
    this.onFallback,
    this.showFallbackButton = true,
    this.accentColor,
  });

  @override
  State<BiometricAuthWidget> createState() => _BiometricAuthWidgetState();
}

class _BiometricAuthWidgetState extends State<BiometricAuthWidget>
    with SingleTickerProviderStateMixin {
  final BiometricAuthService _biometricService = BiometricAuthService();
  
  late AnimationController _animationController;
  late Animation<double> _pulseAnimation;
  // late Animation<double> _scaleAnimation; // Unused field - commented out
  
  bool _isAuthenticating = false;
  bool _isAvailable = false;
  List<BiometricType> _availableBiometrics = [];
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _checkBiometricAvailability();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: DesignTokens.durationLoading,
      vsync: this,
    );

    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    // _scaleAnimation = Tween<double>(
    //   begin: 1.0,
    //   end: 0.95,
    // ).animate(CurvedAnimation(
    //   parent: _animationController,
    //   curve: Curves.easeInOut,
    // )); // Unused animation - commented out
  }

  Future<void> _checkBiometricAvailability() async {
    try {
      await _biometricService.initialize();
      final isAvailable = await _biometricService.isBiometricAvailable();
      final availableBiometrics = await _biometricService.getAvailableBiometrics();
      
      if (mounted) {
        setState(() {
          _isAvailable = isAvailable;
          _availableBiometrics = availableBiometrics;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isAvailable = false;
          _errorMessage = 'Failed to check biometric availability';
        });
      }
    }
  }

  Future<void> _authenticate() async {
    if (_isAuthenticating || !_isAvailable) return;

    setState(() {
      _isAuthenticating = true;
      _errorMessage = null;
    });

    _animationController.repeat(reverse: true);

    try {
      final result = await _biometricService.authenticateWithBiometrics(
        reason: widget.title ?? 'Authenticate to access your account',
        username: widget.username,
      );

      if (mounted) {
        _animationController.stop();
        _animationController.reset();

        if (result.isSuccess) {
          widget.onSuccess?.call(result);
        } else {
          setState(() {
            _errorMessage = result.message;
          });
          widget.onError?.call(result.message);
        }
      }
    } catch (e) {
      if (mounted) {
        _animationController.stop();
        _animationController.reset();
        setState(() {
          _errorMessage = 'Authentication failed: $e';
        });
        widget.onError?.call('Authentication failed: $e');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isAuthenticating = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final accentColor = widget.accentColor ?? MolColors.cyan;

    if (!_isAvailable) {
      return _buildUnavailableState();
    }

    return AppCard.glow(
      glowColor: accentColor,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Title
          if (widget.title != null) ...[
            Text(
              widget.title!,
              style: AppTypography.withColor(
                AppTypography.headingMedium,
                Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: DesignTokens.spaceLg),
          ],

          // Subtitle
          if (widget.subtitle != null) ...[
            Text(
              widget.subtitle!,
              style: AppTypography.withColor(
                AppTypography.bodyMedium,
                Colors.white70,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: DesignTokens.space2xl),
          ],

          // Biometric icon and button
          _buildBiometricButton(accentColor),

          SizedBox(height: DesignTokens.space2xl),

          // Error message
          if (_errorMessage != null) ...[
            Container(
              padding: EdgeInsets.all(DesignTokens.spaceLg),
              decoration: BoxDecoration(
                color: MolColors.red.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(DesignTokens.radiusLg),
                border: Border.all(
                  color: MolColors.red.withValues(alpha: 0.3),
                  width: DesignTokens.borderThin,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.error_outline,
                    color: MolColors.red,
                    size: DesignTokens.iconSizeMd,
                  ),
                  SizedBox(width: DesignTokens.spaceMd),
                  Expanded(
                    child: Text(
                      _errorMessage!,
                      style: AppTypography.withColor(
                        AppTypography.bodySmall,
                        MolColors.red,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: DesignTokens.spaceLg),
          ],

          // Fallback button
          if (widget.showFallbackButton) ...[
            AppButton.tertiary(
              text: 'Use Password Instead',
              icon: Icons.lock_outline,
              onPressed: widget.onFallback,
              fullWidth: true,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildBiometricButton(Color accentColor) {
    return GestureDetector(
      onTap: _authenticate,
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Transform.scale(
            scale: _isAuthenticating ? _pulseAnimation.value : 1.0,
            child: Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: accentColor.withValues(alpha: 0.1),
                border: Border.all(
                  color: accentColor,
                  width: DesignTokens.borderMedium,
                ),
                boxShadow: _isAuthenticating
                    ? DesignSystemHelpers.createGlow(accentColor, intensity: 1.5)
                    : DesignSystemHelpers.createGlow(accentColor),
              ),
              child: Center(
                child: _isAuthenticating
                    ? SizedBox(
                        width: 40,
                        height: 40,
                        child: CircularProgressIndicator(
                          valueColor: AlwaysStoppedAnimation<Color>(accentColor),
                          strokeWidth: 3,
                        ),
                      )
                    : Icon(
                        _getBiometricIcon(),
                        size: 48,
                        color: accentColor,
                      ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildUnavailableState() {
    return AppCard.outlined(
      glowColor: Colors.grey,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.fingerprint_outlined,
            size: 64,
            color: Colors.grey,
          ),
          SizedBox(height: DesignTokens.spaceLg),
          Text(
            'Biometric Authentication Unavailable',
            style: AppTypography.withColor(
              AppTypography.headingSmall,
              Colors.grey,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: DesignTokens.spaceMd),
          Text(
            _errorMessage ?? 'This device does not support biometric authentication or it is not set up.',
            style: AppTypography.withColor(
              AppTypography.bodyMedium,
              Colors.grey[400]!,
            ),
            textAlign: TextAlign.center,
          ),
          if (widget.showFallbackButton) ...[
            SizedBox(height: DesignTokens.space2xl),
            AppButton.secondary(
              text: 'Use Password',
              icon: Icons.lock_outline,
              onPressed: widget.onFallback,
              fullWidth: true,
            ),
          ],
        ],
      ),
    );
  }

  IconData _getBiometricIcon() {
    if (_availableBiometrics.contains(BiometricType.face)) {
      return Icons.face;
    } else if (_availableBiometrics.contains(BiometricType.fingerprint)) {
      return Icons.fingerprint;
    } else if (_availableBiometrics.contains(BiometricType.iris)) {
      return Icons.visibility;
    } else {
      return Icons.security;
    }
  }
}

/// Biometric setup widget for enabling biometric authentication
class BiometricSetupWidget extends StatefulWidget {
  final String username;
  final Function(bool)? onSetupComplete;
  final VoidCallback? onSkip;

  const BiometricSetupWidget({
    super.key,
    required this.username,
    this.onSetupComplete,
    this.onSkip,
  });

  @override
  State<BiometricSetupWidget> createState() => _BiometricSetupWidgetState();
}

class _BiometricSetupWidgetState extends State<BiometricSetupWidget> {
  final BiometricAuthService _biometricService = BiometricAuthService();
  
  bool _isAvailable = false;
  bool _isEnabled = false;
  bool _isLoading = false;
  List<BiometricType> _availableBiometrics = [];

  @override
  void initState() {
    super.initState();
    _checkStatus();
  }

  Future<void> _checkStatus() async {
    try {
      await _biometricService.initialize();
      final isAvailable = await _biometricService.isBiometricAvailable();
      final isEnabled = await _biometricService.isBiometricEnabledForUser(widget.username);
      final availableBiometrics = await _biometricService.getAvailableBiometrics();
      
      if (mounted) {
        setState(() {
          _isAvailable = isAvailable;
          _isEnabled = isEnabled;
          _availableBiometrics = availableBiometrics;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isAvailable = false;
        });
      }
    }
  }

  Future<void> _toggleBiometric() async {
    if (!_isAvailable) return;

    final messenger = ScaffoldMessenger.of(context);

    setState(() {
      _isLoading = true;
    });

    try {
      if (_isEnabled) {
        // Disable biometric
        await _biometricService.disableBiometricAuth(widget.username);
        setState(() {
          _isEnabled = false;
        });
        widget.onSetupComplete?.call(false);
      } else {
        // Enable biometric - would need password verification in real implementation
        // For now, just simulate enabling
        setState(() {
          _isEnabled = true;
        });
        widget.onSetupComplete?.call(true);
      }
    } catch (e) {
      // Handle error
      messenger.showSnackBar(
        SnackBar(
          content: Text('Failed to ${_isEnabled ? 'disable' : 'enable'} biometric authentication'),
          backgroundColor: MolColors.red,
        ),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!_isAvailable) {
      return AppCard.outlined(
        child: Column(
          children: [
            Icon(
              Icons.fingerprint_outlined,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: DesignTokens.spaceLg),
            Text(
              'Biometric Authentication Not Available',
              style: AppTypography.headingSmall,
              textAlign: TextAlign.center,
            ),
            SizedBox(height: DesignTokens.spaceMd),
            Text(
              'Your device does not support biometric authentication.',
              style: AppTypography.bodyMedium,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return AppCard.glow(
      glowColor: _isEnabled ? MolColors.green : MolColors.cyan,
      child: Column(
        children: [
          Icon(
            _isEnabled ? Icons.check_circle : Icons.fingerprint,
            size: 64,
            color: _isEnabled ? MolColors.green : MolColors.cyan,
          ),
          SizedBox(height: DesignTokens.spaceLg),
          Text(
            _isEnabled ? 'Biometric Authentication Enabled' : 'Enable Biometric Authentication',
            style: AppTypography.headingSmall,
            textAlign: TextAlign.center,
          ),
          SizedBox(height: DesignTokens.spaceMd),
          Text(
            _isEnabled
                ? 'You can now use your ${_getBiometricTypeName()} to sign in quickly and securely.'
                : 'Use your ${_getBiometricTypeName()} to sign in quickly and securely.',
            style: AppTypography.bodyMedium,
            textAlign: TextAlign.center,
          ),
          SizedBox(height: DesignTokens.space2xl),
          AppButton.primary(
            text: _isEnabled ? 'Disable Biometric' : 'Enable Biometric',
            icon: _isEnabled ? Icons.fingerprint_outlined : Icons.fingerprint,
            isLoading: _isLoading,
            onPressed: _isLoading ? null : _toggleBiometric,
            fullWidth: true,
          ),
          if (widget.onSkip != null && !_isEnabled) ...[
            SizedBox(height: DesignTokens.spaceLg),
            AppButton.tertiary(
              text: 'Skip for Now',
              onPressed: widget.onSkip,
              fullWidth: true,
            ),
          ],
        ],
      ),
    );
  }

  String _getBiometricTypeName() {
    if (_availableBiometrics.contains(BiometricType.face)) {
      return 'face';
    } else if (_availableBiometrics.contains(BiometricType.fingerprint)) {
      return 'fingerprint';
    } else if (_availableBiometrics.contains(BiometricType.iris)) {
      return 'iris';
    } else {
      return 'biometric';
    }
  }
}
