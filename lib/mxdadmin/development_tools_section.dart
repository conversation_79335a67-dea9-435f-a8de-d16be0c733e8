import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import '../models/user_model.dart';
//import ../theme/colors.dart';
//import '../services/music_service.dart';
//import '../bulletproof/monitoring_dashboard.dart';
import '../services/exp_log_secure_service.dart';
import '../screens/developer_menu_screen.dart';
import '../services/release_config_service.dart';

class DevelopmentToolsSection extends StatelessWidget {
  final User user;

  const DevelopmentToolsSection({
    super.key,
    required this.user,
  });

  // Helper to draw outlined text
  Widget _outlinedText(
    String text, {
    required double fontSize,
    required Color fillColor,
    Color outlineColor = Colors.black,
    double outlineWidth = 1.5,
    String fontFamily = 'Bitsumishi',
  }) {
    return Stack(
      alignment: Alignment.center,
      children: [
        Text(
          text,
          style: TextStyle(
            fontFamily: fontFamily,
            fontSize: fontSize,
            foreground: Paint()
              ..style = PaintingStyle.stroke
              ..strokeWidth = outlineWidth
              ..color = outlineColor,
          ),
        ),
        Text(
          text,
          style: TextStyle(
            fontFamily: fontFamily,
            fontSize: fontSize,
            color: fillColor,
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    // Hide entire development tools section in release mode
    if (!ReleaseConfigService.shouldShowDevelopmentToolsSection) {
      return const SizedBox.shrink();
    }

    final width = MediaQuery.of(context).size.width;
    final sectionFontSize = (width / 1080) * 22;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _outlinedText(
          "🛠 Development Tools",
          fontSize: sectionFontSize.clamp(20.0, 30.0),
          fillColor: Colors.white,
        ),
        SizedBox(height: (width / 1080 * 12).clamp(8.0, 16.0)),

        // System Monitor
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: () {
              Navigator.pushNamed(context, '/monitor');
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blueGrey,
              padding: EdgeInsets.symmetric(
                vertical: (width / 1080 * 14).clamp(12.0, 20.0),
                horizontal: 16,
              ),
            ),
            child: _outlinedText(
              "📊 System Monitor",
              fontSize: sectionFontSize.clamp(20.0, 30.0),
              fillColor: Colors.white,
            ),
          ),
        ),
        SizedBox(height: (width / 1080 * 16).clamp(12.0, 24.0)),

        // Clear EXP Log
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: () async {
              final messenger = ScaffoldMessenger.of(context);
              await ExpLogSecureService.clearDiary(user.username);
              final msgFontSize = (width / 1080) * 18;
              messenger.showSnackBar(
                SnackBar(
                  content: Text(
                    "🧹 EXP log cleared",
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: msgFontSize.clamp(16.0, 22.0),
                    ),
                  ),
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blueGrey,
              padding: EdgeInsets.symmetric(
                vertical: (width / 1080 * 14).clamp(12.0, 20.0),
                horizontal: 16,
              ),
            ),
            child: _outlinedText(
              "🧹 Clear EXP Log",
              fontSize: sectionFontSize.clamp(20.0, 30.0),
              fillColor: Colors.white,
            ),
          ),
        ),

        const SizedBox(height: 20),

        // Secret Button for YouTube Transcript Downloader
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: () => _showPasswordDialog(context),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              padding: EdgeInsets.symmetric(
                vertical: (width / 1080 * 14).clamp(12.0, 20.0),
                horizontal: 16,
              ),
            ),
            child: _outlinedText(
              "🔒 Secret Button",
              fontSize: sectionFontSize.clamp(20.0, 30.0),
              fillColor: Colors.white,
            ),
          ),
        ),
      ],
    );
  }

  /// Show password dialog for secret developer features
  void _showPasswordDialog(BuildContext context) {
    final controller = TextEditingController();
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.grey[900],
        title: _outlinedText(
          'Enter Password',
          fontSize: 20,
          fillColor: Colors.white,
        ),
        content: TextField(
          controller: controller,
          obscureText: true,
          style: const TextStyle(
            color: Colors.white,
            fontFamily: 'Digital-7',
          ),
          decoration: InputDecoration(
            hintText: 'Password',
            hintStyle: TextStyle(color: Colors.grey[600]),
            filled: true,
            fillColor: Colors.grey[800],
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Colors.cyan),
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: _outlinedText(
              'Cancel',
              fontSize: 16,
              fillColor: Colors.grey,
            ),
          ),
          TextButton(
            onPressed: () {
              // FIXED: Use runtime environment variable instead of compile-time
              final devPassword = dotenv.env['DEV_PASSWORD'] ?? 'disabled';
              if (devPassword != 'disabled' && controller.text == devPassword) {
                Navigator.pop(context);
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const DeveloperMenuScreen(),
                  ),
                );
              } else {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      'Incorrect password',
                      style: TextStyle(
                        color: Colors.white,
                        fontFamily: 'Digital-7',
                      ),
                    ),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
            child: _outlinedText(
              'Submit',
              fontSize: 16,
              fillColor: Colors.cyan,
            ),
          ),
        ],
      ),
    );
  }
}