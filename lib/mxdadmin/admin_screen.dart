import 'package:flutter/material.dart';
//import 'package:provider/provider.dart';
import '../models/user_model.dart';
//import '../services/user_controller.dart';
//import '../services/user_service.dart';
import '../widgets/maxedout_appbar.dart';
//import '../dhabits/daily_habit_manager_popup.dart';
//import '../services/music_service.dart';
//import '../theme/colors.dart';
//import '../quests/ns_setup_widget.dart';
//import '../services/exp_log_secure_service.dart';
//import '../screens/set_oswidgets.dart';
import 'custom_category_section.dart';
import 'development_tools_section.dart';
import 'user_tools_section.dart';

class AdminScreen extends StatefulWidget {
  final User user;
  final void Function(User) onUserUpdated;
  final void Function(User?) onReset;

  const AdminScreen({
    super.key,
    required this.user,
    required this.onUserUpdated,
    required this.onReset,
  });

  @override
  State<AdminScreen> createState() => _AdminScreenState();
}

class _AdminScreenState extends State<AdminScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: const MaxedOutAppBar(title: "Admin Panel"),
      body: ListView(
        padding: const EdgeInsets.all(24),
        children: [
          // User Tools Section
          UserToolsSection(
            user: widget.user,
            onUserUpdated: (u) {
              widget.onUserUpdated(u);
              setState(() {});
            },
            onReset: widget.onReset,
          ),
          const Divider(color: Colors.grey),
          
          // Custom Categories Section
          CustomCategorySection(
            user: widget.user,
            onUserUpdated: (u) {
              widget.onUserUpdated(u);
              setState(() {});
            },
          ),
          const Divider(color: Colors.grey),
          
          // Development Tools Section
          DevelopmentToolsSection(
            user: widget.user,
          ),
          
          const SizedBox(height: 24),
          const Center(
            child: Text(
              "Do not stop.",
              style: TextStyle(
                color: Colors.white38,
                fontSize: 18,
                fontFamily: 'Pirulen',
              ),
            ),
          ),
        ],
      ),
    );
  }
} 