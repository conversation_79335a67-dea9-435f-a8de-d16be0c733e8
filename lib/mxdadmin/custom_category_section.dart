import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/user_model.dart';
import '../cstcat/custom_category_service.dart';
import '../controller/user_controller2.dart';
//import '../theme/colors.dart';
import '../services/music_service.dart';
//import '../data/user_storage.dart';
//import '../services/user_service.dart';

class CustomCategorySection extends StatefulWidget {
  final User user;
  final void Function(User) onUserUpdated;
  const CustomCategorySection({super.key, required this.user, required this.onUserUpdated});

  @override
  State<CustomCategorySection> createState() => _CustomCategorySectionState();
}

class _CustomCategorySectionState extends State<CustomCategorySection> {
  late User user;
  final CustomCategoryService _customCategoryService = CustomCategoryService();

  @override
  void initState() {
    super.initState();
    user = widget.user;
  }

  // Limit: Only 2 custom categories allowed per user (see User model)
  Future<void> _addCategory() async {
    if (user.customCategories.length >= 2) {
      _showDialog("Limit Reached", "You can only have 2 custom categories.");
      return;
    }
    final controller = TextEditingController();
    final result = await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.black,
        title: const Text('New Category', style: TextStyle(color: Colors.white)),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(
            hintText: "Enter category name",
            hintStyle: TextStyle(color: Colors.grey),
            filled: true,
            fillColor: Colors.grey,
          ),
          style: TextStyle(color: Colors.white),
        ),
        actions: [
          TextButton(
            child: const Text('Cancel', style: TextStyle(color: Colors.grey)),
            onPressed: () => Navigator.pop(context),
          ),
          TextButton(
            child: const Text('Add', style: TextStyle(color: Colors.amber)),
            onPressed: () => Navigator.pop(context, controller.text.trim()),
          ),
        ],
      ),
    );
    if (result != null && result.isNotEmpty) {
      try {
        print('🔧 Admin: Adding custom category: $result');
        final updated = await _customCategoryService.addCustomCategory(user, result);
        final latest = await _customCategoryService.loadUser(user.username);
        final finalUser = latest ?? updated;

        print('✅ Admin: Custom category added successfully');
        print('   Categories: ${finalUser.customCategories}');

        setState(() => user = finalUser);
        widget.onUserUpdated(finalUser);

        // Force UserController refresh to ensure UI updates
        if (mounted) {
          final userController = Provider.of<UserController2>(context, listen: false);
          await userController.updateUser(finalUser);
          await userController.save();
          print('✅ Admin: UserController updated and saved');
        }

        try {
          MusicService.playEffect('assets/sounds/lightning.mp3');
        } catch (_) {
          // MusicService is optional; ignore errors
        }
      } catch (e) {
        print('❌ Admin: Error adding custom category: $e');
        _showDialog("Error", e.toString());
      }
    }
  }

  Future<void> _removeCategory(String name) async {
    final confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.black,
        title: const Text("Remove Category", style: TextStyle(color: Colors.white)),
        content: Text("Are you sure you want to remove '$name'?",
            style: TextStyle(color: Colors.white70)),
        actions: [
          TextButton(
            child: const Text("Cancel", style: TextStyle(color: Colors.grey)),
            onPressed: () => Navigator.pop(context, false),
          ),
          TextButton(
            child: const Text("Remove", style: TextStyle(color: Colors.redAccent)),
            onPressed: () => Navigator.pop(context, true),
          ),
        ],
      ),
    );
    if (confirm == true) {
      try {
        print('🔧 Admin: Removing custom category: $name');
        final updated = await _customCategoryService.removeCustomCategory(user, name);
        final latest = await _customCategoryService.loadUser(user.username);
        final finalUser = latest ?? updated;

        print('✅ Admin: Custom category removed successfully');
        print('   Categories: ${finalUser.customCategories}');

        setState(() => user = finalUser);
        widget.onUserUpdated(finalUser);

        // Force UserController refresh to ensure UI updates
        if (mounted) {
          final userController = Provider.of<UserController2>(context, listen: false);
          await userController.updateUser(finalUser);
          await userController.save();
          print('✅ Admin: UserController updated and saved');
        }
      } catch (e) {
        print('❌ Admin: Error removing custom category: $e');
        _showDialog("Error", e.toString());
      }
    }
  }

  void _showDialog(String title, String message) {
    showDialog(
      context: context,
      builder: (_) => AlertDialog(
        backgroundColor: Colors.black,
        title: Text(title, style: TextStyle(color: Colors.white)),
        content: Text(message, style: TextStyle(color: Colors.white70)),
        actions: [
          TextButton(
            child: const Text("OK", style: TextStyle(color: Colors.amber)),
            onPressed: () => Navigator.pop(context),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final customCats = user.customCategories;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text("🛠 Custom Categories", style: TextStyle(color: Colors.white, fontSize: 20)),
        const SizedBox(height: 16),
        if (customCats.isEmpty)
          const Text("No custom categories yet.", style: TextStyle(color: Colors.white)),
        ...customCats.map((cat) => ListTile(
              title: Text(cat, style: TextStyle(color: Colors.white)),
              trailing: IconButton(
                icon: const Icon(Icons.delete, color: Colors.redAccent),
                onPressed: () => _removeCategory(cat),
              ),
            )),
        const SizedBox(height: 20),
        ElevatedButton.icon(
          onPressed: _addCategory,
          icon: const Icon(Icons.add),
          label: const Text("Add New Category"),
          style: ElevatedButton.styleFrom(backgroundColor: Colors.blueGrey),
        ),
        const SizedBox(height: 20),
      ],
    );
  }
} 