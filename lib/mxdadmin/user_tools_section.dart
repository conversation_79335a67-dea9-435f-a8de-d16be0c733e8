import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/user_model.dart';
import '../controller/user_controller2.dart';
import '../services/user_service.dart';
import '../dhabits/daily_habit_manager_popup.dart';
import '../services/music_service.dart';
import '../screens/set_oswidgets.dart';
import '../onboarding/north_star_setup_04.dart';
import '../bulletproof/error_handler.dart';

class UserToolsSection extends StatelessWidget {
  final User user;
  final void Function(User) onUserUpdated;
  final void Function(User?) onReset;

  const UserToolsSection({
    super.key,
    required this.user,
    required this.onUserUpdated,
    required this.onReset,
  });

  // Helper to draw outlined text
  Widget _outlinedText(
    String text, {
    required double fontSize,
    required Color fillColor,
    Color outlineColor = Colors.black,
    double outlineWidth = 1.5,
    String fontFamily = 'Bitsumishi',
  }) {
    return Stack(
      alignment: Alignment.center,
      children: [
        Text(
          text,
          style: TextStyle(
            fontFamily: fontFamily,
            fontSize: fontSize,
            foreground: Paint()
              ..style = PaintingStyle.stroke
              ..strokeWidth = outlineWidth
              ..color = outlineColor,
          ),
        ),
        Text(
          text,
          style: TextStyle(
            fontFamily: fontFamily,
            fontSize: fontSize,
            color: fillColor,
          ),
        ),
      ],
    );
  }

  Future<void> _changePasscode(BuildContext context) async {
    final width = MediaQuery.of(context).size.width;
    final titleFontSize = (width / 1080) * 20;
    final inputFontSize = (width / 1080) * 18;
    final buttonFontSize = (width / 1080) * 18;

    // Store references before async operations
    final messenger = ScaffoldMessenger.of(context);

    final controller = TextEditingController();
    final result = await showDialog<String>(
      context: context,
      builder: (_) => AlertDialog(
        backgroundColor: Colors.black,
        title: Text(
          'Change Passcode',
          style: TextStyle(
            color: Colors.white,
            fontFamily: 'Bitsumishi',
            fontSize: titleFontSize.clamp(18.0, 24.0),
          ),
        ),
        content: TextField(
          controller: controller,
          maxLength: 6,
          keyboardType: TextInputType.number,
          style: TextStyle(
            color: Colors.white,
            fontSize: inputFontSize.clamp(16.0, 22.0),
          ),
          decoration: InputDecoration(
            hintText: "Enter new 6-digit code",
            hintStyle: TextStyle(
              color: Colors.grey,
              fontSize: (inputFontSize * 0.9).clamp(14.0, 20.0),
            ),
            filled: true,
            fillColor: Colors.grey,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Cancel',
              style: TextStyle(
                color: Colors.grey,
                fontSize: buttonFontSize.clamp(16.0, 20.0),
              ),
            ),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, controller.text.trim()),
            child: Text(
              'Change',
              style: TextStyle(
                color: Colors.amber,
                fontSize: buttonFontSize.clamp(16.0, 20.0),
              ),
            ),
          ),
        ],
      ),
    );

    if (result != null && result.isNotEmpty) {
      try {
        // Create UserService with ErrorHandler singleton
        final userService = UserService(ErrorHandler());
        await userService.updatePasscode(user, result);
        final latest = await UserService.loadUserByUsernameStatic(user.username);
        if (latest != null) onUserUpdated(latest);
        MusicService.playEffect('assets/sounds/lightning.mp3');
      } catch (e) {
        messenger.showSnackBar(
          SnackBar(
            content: _outlinedText(
              '❌ ${e.toString()}',
              fontSize: 18,
              fillColor: Colors.white,
            ),
          ),
        );
      }
    }
  }

  Future<void> _confirmReset(BuildContext context, String action, Future<void> Function() resetFn) async {
    final width = MediaQuery.of(context).size.width;
    final titleFontSize = (width / 1080) * 20;
    final contentFontSize = (width / 1080) * 18;
    final buttonFontSize = (width / 1080) * 18;

    final confirm = await showDialog<bool>(
      context: context,
      builder: (_) => AlertDialog(
        backgroundColor: Colors.black,
        title: Text(
          "Are you sure?",
          style: TextStyle(
            color: Colors.white,
            fontFamily: 'Bitsumishi',
            fontSize: titleFontSize.clamp(18.0, 24.0),
          ),
        ),
        content: Text(
          "This will $action and cannot be undone.",
          style: TextStyle(
            color: Colors.white70,
            fontSize: contentFontSize.clamp(16.0, 22.0),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text(
              "Cancel",
              style: TextStyle(
                color: Colors.grey,
                fontSize: buttonFontSize.clamp(16.0, 20.0),
              ),
            ),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: Text(
              "Confirm",
              style: TextStyle(
                color: Colors.redAccent,
                fontSize: buttonFontSize.clamp(16.0, 20.0),
              ),
            ),
          ),
        ],
      ),
    );

    if (confirm == true) await resetFn();
  }

  Future<void> _resetUserData(BuildContext context) async {
    final userController = Provider.of<UserController2>(context, listen: false);
    final messenger = ScaffoldMessenger.of(context);
    final width = MediaQuery.of(context).size.width;
    final msgFontSize = (width / 1080) * 18;

    final currentUser = userController.user;
    if (currentUser == null) return;
    final freshUser = User.blank(id: currentUser.id, username: currentUser.username);
    await userController.updateUser(freshUser);
    onReset(freshUser);

    messenger.showSnackBar(
      SnackBar(
        content: Text(
          "🧼 Data reset complete",
          style: TextStyle(
            color: Colors.white,
            fontSize: msgFontSize.clamp(16.0, 22.0),
          ),
        ),
      ),
    );
  }

  Future<void> _deleteProfile(BuildContext context) async {
    final messenger = ScaffoldMessenger.of(context);
    final width = MediaQuery.of(context).size.width;
    final msgFontSize = (width / 1080) * 18;

    final userService = UserService(ErrorHandler());
    await userService.deleteUser(user.username);
    onReset(null);

    messenger.showSnackBar(
      SnackBar(
        content: Text(
          "🗑 Profile deleted",
          style: TextStyle(
            color: Colors.white,
            fontSize: msgFontSize.clamp(16.0, 22.0),
          ),
        ),
      ),
    );
  }

  Future<void> _openDailyHabitManager(BuildContext context) async {
    final userController = context.read<UserController2>();
    final currentUser = userController.user;
    if (currentUser == null) return;
    
    await showDialog(
      context: context,
      builder: (_) => DailyHabitManagerPopup(
        user: currentUser,
        onUserUpdated: (updatedUser) async {
          await userController.updateUser(updatedUser);
          onUserUpdated(updatedUser);
        },
      ),
    );
  }

  void _openNsSetupModal(BuildContext context) {
    final userController = Provider.of<UserController2>(context, listen: false);
    final navigator = Navigator.of(context);
    final currentUser = userController.user;
    if (currentUser == null) return;
    navigator.push(
      MaterialPageRoute(
        builder: (context) => NorthStarSetup(
          user: currentUser,
          onContinue: (updatedUser) async {
            print('🔧 Admin: North Star setup completed, saving user...');
            await userController.updateUser(updatedUser);
            await userController.save(); // Ensure persistence
            print('✅ Admin: User saved successfully');
            navigator.pop();
            onReset(updatedUser);
          },
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    final sectionFontSize = (width / 1080) * 22;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _outlinedText(
          "👤 User Tools",
          fontSize: sectionFontSize.clamp(20.0, 30.0),
          fillColor: Colors.white,
        ),
        SizedBox(height: (width / 1080 * 16).clamp(12.0, 24.0)),

        // Daily Habit Manager
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: () => _openDailyHabitManager(context),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.tealAccent.shade700,
              padding: EdgeInsets.symmetric(
                vertical: (width / 1080 * 14).clamp(12.0, 20.0),
                horizontal: 16,
              ),
            ),
            child: _outlinedText(
              "🛠 Daily Habit Manager",
              fontSize: sectionFontSize.clamp(20.0, 30.0),
              fillColor: Colors.white,
            ),
          ),
        ),
        SizedBox(height: (width / 1080 * 16).clamp(12.0, 24.0)),

        // North Star Quest Setup/Edit
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: () => _openNsSetupModal(context),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.deepPurpleAccent,
              padding: EdgeInsets.symmetric(
                vertical: (width / 1080 * 14).clamp(12.0, 20.0),
                horizontal: 16,
              ),
            ),
            child: _outlinedText(
              '⭐ North Star Quest Setup/Edit',
              fontSize: sectionFontSize.clamp(20.0, 30.0),
              fillColor: Colors.white,
            ),
          ),
        ),
        SizedBox(height: (width / 1080 * 16).clamp(12.0, 24.0)),

        // Set Operating System Widgets (Debug only - hidden in release)
        if (kDebugMode) ...[
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () {
                showDialog(
                  context: context,
                  builder: (_) => const SetOsWidgetsButton(),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.pink,
                padding: EdgeInsets.symmetric(
                  vertical: (width / 1080 * 14).clamp(12.0, 20.0),
                  horizontal: 16,
                ),
              ),
              child: _outlinedText(
                "⚙️ Configure OS Widgets",
                fontSize: sectionFontSize.clamp(20.0, 30.0),
                fillColor: Colors.white,
              ),
            ),
          ),
        ],
        SizedBox(height: (width / 1080 * 16).clamp(12.0, 24.0)),

        // Change Passcode
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: () => _changePasscode(context),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.grey[800],
              padding: EdgeInsets.symmetric(
                vertical: (width / 1080 * 14).clamp(12.0, 20.0),
                horizontal: 16,
              ),
            ),
            child: _outlinedText(
              "🔐 Change Passcode",
              fontSize: sectionFontSize.clamp(20.0, 30.0),
              fillColor: Colors.white,
            ),
          ),
        ),
        SizedBox(height: (width / 1080 * 16).clamp(12.0, 24.0)),

        // Reset Progress
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: () => _confirmReset(context, "reset all progress", () => _resetUserData(context)),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              padding: EdgeInsets.symmetric(
                vertical: (width / 1080 * 14).clamp(12.0, 20.0),
                horizontal: 16,
              ),
            ),
            child: _outlinedText(
              "🧼 Reset Progress",
              fontSize: sectionFontSize.clamp(20.0, 30.0),
              fillColor: Colors.white,
            ),
          ),
        ),
        SizedBox(height: (width / 1080 * 16).clamp(12.0, 24.0)),

        // Delete Profile
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: () => _confirmReset(context, "delete your profile", () => _deleteProfile(context)),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              padding: EdgeInsets.symmetric(
                vertical: (width / 1080 * 14).clamp(12.0, 20.0),
                horizontal: 16,
              ),
            ),
            child: _outlinedText(
              "🗑 Delete Profile",
              fontSize: sectionFontSize.clamp(20.0, 30.0),
              fillColor: Colors.white,
            ),
          ),
        ),
      ],
    );
  }
} 