// lib/home/<USER>

import 'package:flutter/material.dart';
//import 'package:provider/provider.dart';
import '../models/user_model.dart';
import '../screens/life_coach_screen.dart';
import '../dhabits/habits_screen.dart';

/// Renders text with a narrow black outline for contrast.
class StrokeText extends StatelessWidget {
  final String text;
  final TextStyle textStyle;
  final double strokeWidth;
  final Color strokeColor;
  final TextAlign textAlign;

  const StrokeText({
    super.key,
    required this.text,
    required this.textStyle,
    this.strokeWidth = 1.0,
    this.strokeColor = Colors.black,
    this.textAlign = TextAlign.center,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(children: [
      Text(
        text,
        textAlign: textAlign,
        style: textStyle.copyWith(
          foreground: Paint()
            ..style = PaintingStyle.stroke
            ..strokeWidth = strokeWidth
            ..color = strokeColor,
        ),
      ),
      Text(text, textAlign: textAlign, style: textStyle),
    ]);
  }
}

/// Two neon-styled navigation buttons at the top of Home.
class HmTopNavigationButtons extends StatelessWidget {
  final User currentUser;
  final Function(User) onUserUpdated;

  const HmTopNavigationButtons({
    super.key,
    required this.currentUser,
    required this.onUserUpdated,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(builder: (context, constraints) {
      return Center(
        child: Wrap(
          alignment: WrapAlignment.center,
          spacing: 12,
          runSpacing: 12,
          children: [
            _buildTopButton(
              label: 'Maxed Out\nLife Coach',
              color: const Color(0xFF3F4AE2),
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (_) => LifeCoachScreen(user: currentUser),
                  ),
                );
              },
            ),
            _buildTopButton(
              label: 'Daily\nHabits',
              color: const Color(0xFFB4FF28),
              textColor: Colors.white,
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (_) => const HabitsScreen(),
                  ),
                );
              },
            ),
          ],
        ),
      );
    });
  }

  Widget _buildTopButton({
    required String label,
    required Color color,
    Color textColor = Colors.white,
    required VoidCallback onPressed,
  }) {
    return SizedBox(
      width: 240,
      height: 60,
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(backgroundColor: color),
        onPressed: onPressed,
        child: FittedBox(
          fit: BoxFit.scaleDown,
          child: StrokeText(
            text: label,
            textAlign: TextAlign.center,
            textStyle: TextStyle(
              color: textColor,
              fontFamily: 'Pirulen',
              fontSize: 30,
            ),              strokeWidth: 1,
            strokeColor: Colors.black,
          ),
        ),
      ),
    );
  }
}
