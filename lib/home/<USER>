// lib/home/<USER>

import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

/// Renders text with a narrow black outline for contrast.
class StrokeText extends StatelessWidget {
  final String text;
  final TextStyle textStyle;
  final double strokeWidth;
  final Color strokeColor;
  final TextAlign textAlign;

  const StrokeText({
    super.key,
    required this.text,
    required this.textStyle,
    this.strokeWidth = 1.0,
    this.strokeColor = Colors.black,
    this.textAlign = TextAlign.center,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(children: [
      Text(
        text,
        textAlign: textAlign,
        style: textStyle.copyWith(
          foreground: Paint()
            ..style = PaintingStyle.stroke
            ..strokeWidth = strokeWidth
            ..color = strokeColor,
        ),
      ),
      Text(text, textAlign: textAlign, style: textStyle),
    ]);
  }
}

/// A neon-bordered "Supercharge Your Game" button that opens the Guardian Tape store.
class HmSuperchargeButton extends StatelessWidget {
  final Color glowColor;

  const HmSuperchargeButton({
    super.key,
    required this.glowColor,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(builder: (context, constraints) {
      return Container(
        decoration: BoxDecoration(
          boxShadow: [
            BoxShadow(
              color: glowColor.withValues(alpha: 0.6),
              blurRadius: 20,
              spreadRadius: 3,
              offset: const Offset(0, 0),
            ),
            BoxShadow(
              color: glowColor.withValues(alpha: 0.4),
              blurRadius: 30,
              spreadRadius: 5,
              offset: const Offset(0, 0),
            ),
          ],
          borderRadius: BorderRadius.circular(8),
        ),
        child: ElevatedButton(
          style: ElevatedButton.styleFrom(
            backgroundColor: glowColor,
            minimumSize: const Size(double.infinity, 48),
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          onPressed: () async {
            final messenger = ScaffoldMessenger.of(context);
            final url = Uri.parse('https://www.guardian-tape.com/products/guardian-team-pack');
            try {
              if (await canLaunchUrl(url)) {
                await launchUrl(url, mode: LaunchMode.externalApplication);
              } else {
                messenger.showSnackBar(
                  SnackBar(
                    content: const StrokeText(
                      text: "Could not launch the store page.",
                      textStyle: TextStyle(
                        color: Colors.white,
                        fontFamily: 'Bitsumishi',
                      ),
                    ),
                  ),
                );
              }
            } catch (e) {
              messenger.showSnackBar(
                SnackBar(
                  content: const StrokeText(
                    text: "An error occurred while launching the store page.",
                    textStyle: TextStyle(
                      color: Colors.white,
                      fontFamily: 'Bitsumishi',
                    ),
                  ),
                ),
              );
            }
          },
          child: Column(
            children: [
              StrokeText(
                text: '⚡ SUPERCHARGE YOUR GAME',
                textAlign: TextAlign.center,
                textStyle: const TextStyle(
                  fontFamily: 'Bitsumishi',
                  fontSize: 20,
                  color: Colors.white,
                ),
                strokeWidth: 1.0,
                strokeColor: Colors.black,
              ),

            ],
          ),
        ),
      );
    });
  }
}
