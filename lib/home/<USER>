// lib/home/<USER>

import 'package:flutter/material.dart';
//import 'package:maxed_out_life/theme/colors.dart';

/// Renders text with a narrow black outline for contrast.
class StrokeText extends StatelessWidget {
  final String text;
  final TextStyle textStyle;
  final double strokeWidth;
  final Color strokeColor;
  final TextAlign textAlign;

  const StrokeText({
    super.key,
    required this.text,
    required this.textStyle,
    this.strokeWidth = 1.0,
    this.strokeColor = Colors.black,
    this.textAlign = TextAlign.center,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Outline
        Text(
          text,
          textAlign: textAlign,
          style: textStyle.copyWith(
            foreground: Paint()
              ..style = PaintingStyle.stroke
              ..strokeWidth = strokeWidth
              ..color = strokeColor,
          ),
        ),
        // Fill
        Text(text, textAlign: textAlign, style: textStyle),
      ],
    );
  }
}

/// A full-height, centered header greeting the user, with outlined text.
class HmWelcomeHeader extends StatelessWidget {
  final String username;

  const HmWelcomeHeader({
    super.key,
    required this.username,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 60, // Fixed height to avoid infinite constraints
      child: Center(
        child: StrokeText(
          text: 'Welcome, $username',
          textAlign: TextAlign.center,
          textStyle: const TextStyle(
            color: Colors.white,
            fontFamily: 'Bitsumishi',
            fontSize: 22,
          ),
          strokeWidth: 1,
          strokeColor: Colors.black,
        ),
      ),
    );
  }
}
