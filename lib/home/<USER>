// lib/home/<USER>

import 'package:flutter/material.dart';
import '../models/user_model.dart';
import '../quests/ns_dashboard.dart';

/// A helper widget that draws text with a narrow black outline
class StrokeText extends StatelessWidget {
  final String text;
  final TextStyle textStyle;
  final double strokeWidth;
  final Color strokeColor;
  final TextAlign textAlign;

  const StrokeText({
    super.key,
    required this.text,
    required this.textStyle,
    this.strokeWidth = 1.0,
    this.strokeColor = Colors.black,
    this.textAlign = TextAlign.left,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Outline layer
        Text(
          text,
          textAlign: textAlign,
          style: textStyle.copyWith(
            foreground: Paint()
              ..style = PaintingStyle.stroke
              ..strokeWidth = strokeWidth
              ..color = strokeColor,
          ),
        ),
        // Fill layer
        Text(
          text,
          textAlign: textAlign,
          style: textStyle,
        ),
      ],
    );
  }
}

List<Widget> buildNorthStarSlivers({
  required User user,
  required Function(User) onQuestUpdate,
}) {
  final glowColor =
      user.northStarQuest?.glowColor ?? Colors.purpleAccent[700]!;

  return [
    // ────── Header ──────
    // SliverToBoxAdapter with SizedBox ensures finite height
    SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 46, vertical: 22),
        child: SizedBox(
          width: double.infinity,
          child: StrokeText(
            text: 'NORTH STAR QUEST',
            textAlign: TextAlign.center,
            textStyle: TextStyle(
              fontFamily: 'Bitsumishi',
              fontSize: 40,
              fontWeight: FontWeight.bold,
              color: glowColor,
              shadows: [
                Shadow(
                  blurRadius: 12.0,
                  color: glowColor,
                  offset: const Offset(0, 0),
                ),
              ],
            ),
            strokeWidth: 1,
            strokeColor: Colors.black,
          ),
        ),
      ),
    ),

    // ────── NS Dashboard ──────
    if (user.northStarQuest != null)
      SliverToBoxAdapter(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: NsDashboard(
            quest: user.northStarQuest!,
            onQuestUpdate: onQuestUpdate,
          ),
        ),
      ),
  ];
}
