// lib/home/<USER>

import 'package:flutter/material.dart';
import '../topbar/totalexpbar.dart';
import '../utils/rank_utils.dart';
import '../models/user_model.dart';

class HmExpBar extends StatelessWidget {
  final User user;

  const HmExpBar({
    super.key,
    required this.user,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        TotalExpBar(
          currentExp: user.exp,
          expToNext: RankUtils.getExpToNextRank(user.exp),
        ),
        const SizedBox(height: 20),
      ],
    );
  }
}
