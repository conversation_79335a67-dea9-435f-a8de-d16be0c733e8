import 'package:flutter/material.dart';
import '../models/user_model.dart';
import '../mxdadmin/admin_screen.dart';
import '../debug/debug_runner.dart';
import '../services/release_config_service.dart';
import '../services/bulletproof_storage_service.dart';

class HmAdminControls extends StatelessWidget {
  final User user;
  final VoidCallback onSignOut;
  final Function(User?) onReset;
  final VoidCallback toggleTheme;

  const HmAdminControls({
    super.key,
    required this.user,
    required this.onSignOut,
    required this.onReset,
    required this.toggleTheme,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            ElevatedButton(
              onPressed: () async {
                final result = await Navigator.push<User?>(
                  context,
                  MaterialPageRoute(
                    builder: (_) => AdminScreen(
                      user: user,
                      onReset: onReset,
                      onUserUpdated: (u) {
                        // Update the user in the parent widget
                        onReset(u);
                      },
                    ),
                  ),
                );
                if (result != null) {
                  onReset(result);
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.grey[800],
              ),
              child: const Text(
                'ADMIN VIEW',
                style: TextStyle(
                  color: Colors.white,
                  fontFamily: 'Bitsumishi',
                ),
              ),
            ),
            ElevatedButton(
              onPressed: onSignOut,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.grey[800],
              ),
              child: const Text(
                'SIGN OUT',
                style: TextStyle(
                  color: Colors.white,
                  fontFamily: 'Bitsumishi',
                ),
              ),
            ),
          ],
        ),
        // Hide debug buttons in release mode
        if (ReleaseConfigService.shouldShowDebugButtons) ...[
          const SizedBox(height: 20),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              ElevatedButton(
                onPressed: toggleTheme,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.grey[900],
                ),
                child: const Text(
                  'Toggle Theme',
                  style: TextStyle(
                    fontFamily: 'Bitsumishi',
                  ),
                ),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (_) => const DebugRunner(),
                    ),
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.cyan[700],
                ),
                child: const Text(
                  'DEBUG TESTS',
                  style: TextStyle(
                    color: Colors.black,
                    fontFamily: 'Bitsumishi',
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }
}
