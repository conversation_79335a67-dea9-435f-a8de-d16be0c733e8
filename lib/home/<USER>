// lib/home/<USER>

import 'package:flutter/material.dart';
import '../models/user_model.dart';

/// Renders text with a narrow black outline for contrast.
class StrokeText extends StatelessWidget {
  final String text;
  final TextStyle textStyle;
  final double strokeWidth;
  final Color strokeColor;
  final TextAlign textAlign;

  const StrokeText({
    super.key,
    required this.text,
    required this.textStyle,
    this.strokeWidth = 1.0,
    this.strokeColor = Colors.black,
    this.textAlign = TextAlign.center,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(children: [
      Text(
        text,
        textAlign: textAlign,
        style: textStyle.copyWith(
          foreground: Paint()
            ..style = PaintingStyle.stroke
            ..strokeWidth = strokeWidth
            ..color = strokeColor,
        ),
      ),
      Text(text, textAlign: textAlign, style: textStyle),
    ]);
  }
}

/// Displays the user's EXP streak with neon glow and full-height fill.
class HmStreakDisplay extends StatelessWidget {
  final User user;
  final Color glowColor;

  const HmStreakDisplay({
    super.key,
    required this.user,
    required this.glowColor,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(builder: (context, constraints) {
      return Center(
        child: StrokeText(
          text: 'EXP Streak: ${user.streakDays} days',
          textAlign: TextAlign.center,
          textStyle: TextStyle(
            fontFamily: 'Bitsumishi',
            fontSize: 24,
            color: Colors.white,
            shadows: [
              Shadow(
                blurRadius: 12,
                color: glowColor.withValues(alpha: 0.8),
                offset: const Offset(0, 0),
              ),
            ],
          ),
          strokeWidth: 1,
          strokeColor: Colors.black,
        ),
      );
    });
  }
}
