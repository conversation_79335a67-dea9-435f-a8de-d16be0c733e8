// lib/home/<USER>

import 'package:flutter/material.dart';
import '../models/user_model.dart';
import '../screens/exp_entry_screen.dart';
import '../theme/colors.dart'; // ← Import our centralized color helper

const int levelExp = 100;

/// A helper widget that draws text with a narrow black outline
class StrokeText extends StatelessWidget {
  final String text;
  final TextStyle textStyle;
  final double strokeWidth;
  final Color strokeColor;
  final TextAlign textAlign;

  const StrokeText({
    super.key,
    required this.text,
    required this.textStyle,
    this.strokeWidth = 1.0,
    this.strokeColor = Colors.black,
    this.textAlign = TextAlign.left,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Outline layer
        Text(
          text,
          textAlign: textAlign,
          style: textStyle.copyWith(
            foreground: Paint()
              ..style = PaintingStyle.stroke
              ..strokeWidth = strokeWidth
              ..color = strokeColor,
          ),
        ),
        // Fill layer
        Text(text, textAlign: textAlign, style: textStyle),
      ],
    );
  }
}

/// The EXP trackers section on the Home screen.
/// Fills its parent's vertical space and outlines all text.
class HmExpTrackers extends StatelessWidget {
  final User user;
  final List<String> categories;
  final Function(User) onUserUpdated;

  const HmExpTrackers({
    super.key,
    required this.user,
    required this.categories,
    required this.onUserUpdated,
  });

  /// Returns optimal font size based on category name length for perfect button fit
  double _getFontSizeForCategory(String category) {
    final totalLength = category.length + 4; // +4 for " EXP"

    // Optimized font sizes based on total character count
    if (totalLength <= 9) {
      return 17.0; // HEALTH EXP (6+3), WEALTH EXP (6+3), COMBAT EXP (6+3)
    } else if (totalLength <= 10) {
      return 15.0; // PURPOSE EXP (7+3) - perfect fit
    } else if (totalLength <= 12) {
      return 13.0; // LANGUAGES EXP (9+3)
    } else {
      return 10.5; // CONNECTION EXP (10+3) - reduced by 0.5 for perfect fit
    }
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Calculate button width as 40% of available width
        final buttonWidth = constraints.maxWidth * 0.40;

        return Column(
          mainAxisSize: MainAxisSize.min,
          children: categories.asMap().entries.map((entry) {
            final category = entry.value;
            final points = user.getExp(category);
            final level = points ~/ levelExp;
            final progress = points % levelExp;
            final percent = progress / levelExp;

            // Grab color from centralized helper
            final color = getCategoryColor(
              category,
              customCategories: user.customCategories,
            );

            return Padding(
              padding: const EdgeInsets.symmetric(vertical: 8),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Category button with outlined text - constrained to 40% width
                  SizedBox(
                    width: buttonWidth,
                    child: Container(
                      decoration: BoxDecoration(
                        boxShadow: [
                          BoxShadow(
                            color: color.withValues(alpha: 0.1),
                            blurRadius: 50,
                            spreadRadius: 2,
                            offset: const Offset(0, 0),
                          ),
                        ],
                        borderRadius: BorderRadius.circular(50),
                      ),
                      child: ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          backgroundColor: color,
                          minimumSize: Size(buttonWidth, 48), // Use calculated width, same height
                          shadowColor: color.withValues(alpha: 0.05),
                          elevation: 12,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(24), // Less rounded for better proportions
                          ),
                        ),
                      onPressed: () async {
                        final result = await Navigator.push<User?>(
                          context,
                          MaterialPageRoute(
                            builder: (_) => ExpEntryScreen(
                              user: user,
                              category: category,
                              onUserUpdated: onUserUpdated,
                            ),
                          ),
                        );
                        if (result != null) {
                          onUserUpdated(result);
                        }
                      },
                      child: Center(
                        child: StrokeText(
                          text: '$category EXP',
                          textAlign: TextAlign.center, // Center-justified text
                          textStyle: TextStyle(
                            color: Colors.white,
                            fontFamily: 'Pirulen',
                            fontSize: _getFontSizeForCategory(category), // Dynamic font size based on category length
                          ),
                          strokeWidth: 1,
                          strokeColor: Colors.black,
                        ),
                      ),
                      ),
                    ),
                  ),

                  const SizedBox(width: 12), // Increased spacing for better separation

                  // Progress info with outlined labeling
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        StrokeText(
                          text: '$points EXP | Level $level',
                          textStyle: TextStyle(
                            color: color,
                            fontFamily: 'Bitsumishi',
                            fontSize: 14,
                          ),
                          strokeWidth: 1,
                          strokeColor: Colors.black,
                        ),
                        const SizedBox(height: 6),
                        Container(
                          decoration: BoxDecoration(
                            boxShadow: [
                              BoxShadow(
                                color: color.withValues(alpha: 0.95),
                                blurRadius: 24,
                                spreadRadius: 0,
                                offset: const Offset(0, 0),
                              ),
                              BoxShadow(
                                color: color.withValues(alpha: 0.6),
                                blurRadius: 12,
                                spreadRadius: 1,
                                offset: const Offset(0, 0),
                              ),
                            ],
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(33),
                            child: LinearProgressIndicator(
                              value: percent,
                              minHeight: 6,
                              valueColor:
                                  AlwaysStoppedAnimation<Color>(color),
                              backgroundColor: Colors.grey[800],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
        );
      },
    );
  }
}
