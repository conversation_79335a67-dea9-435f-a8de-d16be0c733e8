// lib/home/<USER>

import 'package:flutter/material.dart';
import '../controller/user_controller2.dart';
import '../utils/refreshonfocus.dart';
import 'package:provider/provider.dart';
import '../models/user_model.dart';

/// Refresh wrapper for HomeScreen3: reloads user on focus and updates state.
class HmRefreshWrapper extends StatelessWidget {
  final Widget child;
  final Function(User) onUserUpdated;
  final void Function(User) updateLocalUser;

  const HmRefreshWrapper({
    super.key,
    required this.child,
    required this.onUserUpdated,
    required this.updateLocalUser,
  });

  @override
  Widget build(BuildContext context) {
    return RefreshOnFocus(
      onFocus: () async {
        print('🔄 HmRefreshWrapper: onFocus triggered');
        // Simple approach - just trigger a rebuild without refreshing from disk
        // The Provider will handle the state updates automatically
        if (context.mounted) {
          final userController = context.read<UserController2>();
          final currentUser = userController.user;
          if (currentUser != null) {
            print('🔄 HmRefreshWrapper: Using current user, EXP: ${currentUser.exp}');
            // Use post-frame callback to avoid setState during build
            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (context.mounted) {
                updateLocalUser(currentUser);
                onUserUpdated(currentUser);
              }
            });
          }
        }
      },
      child: child,
    );
  }
}

/// Refresh wrapper for Daily Habits: reloads and saves user on focus, ensuring habits stay up-to-date.
class HmHabitsRefreshWrapper extends StatelessWidget {
  final Widget child;
  final Function(User) onHabitsUpdated;
  final void Function(User) updateLocalUser;

  const HmHabitsRefreshWrapper({
    super.key,
    required this.child,
    required this.onHabitsUpdated,
    required this.updateLocalUser,
  });

  @override
  Widget build(BuildContext context) {
    return RefreshOnFocus(
      onFocus: () async {
        // 1️⃣ Reload user and habits from disk
        final userController = context.read<UserController2>();
        await userController.refreshFromDisk();
        // 2️⃣ Persist any local habit changes
        await userController.save();
        // 3️⃣ Grab fresh user and update state
        final updated = userController.user;
        if (updated != null) {
          updateLocalUser(updated);
          onHabitsUpdated(updated);
        }
      },
      child: child,
    );
  }
}
