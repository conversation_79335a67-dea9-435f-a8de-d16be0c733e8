// lib/home/<USER>

import 'package:flutter/material.dart';
import '../models/user_model.dart';
import '../screens/seven_day_development_screen.dart';

/// Renders text with a narrow black outline for contrast.
class StrokeText extends StatelessWidget {
  final String text;
  final TextStyle textStyle;
  final double strokeWidth;
  final Color strokeColor;
  final TextAlign textAlign;

  const StrokeText({
    super.key,
    required this.text,
    required this.textStyle,
    this.strokeWidth = 1.0,
    this.strokeColor = Colors.black,
    this.textAlign = TextAlign.left,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(children: [
      Text(
        text,
        textAlign: textAlign,
        style: textStyle.copyWith(
          foreground: Paint()
            ..style = PaintingStyle.stroke
            ..strokeWidth = strokeWidth
            ..color = strokeColor,
        ),
      ),
      Text(text, textAlign: textAlign, style: textStyle),
    ]);
  }
}

/// A full-width button for the 7-Day Progress screen, with outline and vertical fill.
class HmSevenDayDevelopmentButton extends StatelessWidget {
  final User user;

  const HmSevenDayDevelopmentButton({super.key, required this.user});

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(builder: (context, constraints) {
      return SizedBox(
        width: double.infinity,
        child: ElevatedButton(
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFF9128FF),
            padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 18),
          ),
          onPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (_) => SevenDayDevelopmentScreen(user: user),
              ),
            );
          },
          child: const StrokeText(
            text: '7-Day Progress',
            textAlign: TextAlign.center,
            textStyle: TextStyle(
              color: Colors.white,
              fontFamily: 'Bitsumishi',
              fontSize: 18,
            ),
          ),
        ),
      );
    });
  }
}
