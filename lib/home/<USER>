// 📁 lib/home/<USER>

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/music_service.dart';

/// Debug logging for music control modal (disabled for production)
void _debugLog(String message) {
  // Music control debugging disabled - service is fully operational
}

class MusicControlModal extends StatelessWidget {
  const MusicControlModal({super.key});

  void _showTrackSelector(BuildContext context) {
    _debugLog('🎵 Opening track selector modal');
    showDialog(
      context: context,
      builder: (context) => const TrackSelectorModal(),
    );
  }

  @override
  Widget build(BuildContext context) {
    final music = Provider.of<MusicService>(context);
    final width = MediaQuery.of(context).size.width;
    final height = MediaQuery.of(context).size.height;
    final fontSize = (width / 1080) * 20;

    // Calculate safe modal height for iPad compatibility
    final maxModalHeight = height * 0.85; // Max 85% of screen height
    final safeAreaBottom = MediaQuery.of(context).padding.bottom;

    return Container(
      constraints: BoxConstraints(
        maxHeight: maxModalHeight,
      ),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.black.withValues(alpha: 0.95),
            Colors.purple.withValues(alpha: 0.1),
            Colors.cyan.withValues(alpha: 0.1),
          ],
        ),
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      padding: EdgeInsets.only(
        left: width * 0.05,
        right: width * 0.05,
        top: width * 0.04,
        bottom: width * 0.04 + safeAreaBottom,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header line with neon glow
          Container(
            width: width * 0.15,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.cyanAccent,
              borderRadius: BorderRadius.circular(2),
              boxShadow: [
                BoxShadow(
                  color: Colors.cyanAccent.withValues(alpha: 0.5),
                  blurRadius: 8,
                  spreadRadius: 2,
                ),
              ],
            ),
          ),
          SizedBox(height: width * 0.04),

          // Track Info
          Container(
            padding: EdgeInsets.all(width * 0.03),
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.cyanAccent.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Column(
              children: [
                Text(
                  music.currentTrack.title,
                  style: TextStyle(
                    color: Colors.cyanAccent,
                    fontFamily: 'Pirulen',
                    fontSize: fontSize.clamp(16.0, 20.0),
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                SizedBox(height: width * 0.01),
                Text(
                  'by ${music.currentTrack.artist}',
                  style: TextStyle(
                    color: Colors.white70,
                    fontFamily: 'Bitsumishi',
                    fontSize: fontSize.clamp(12.0, 16.0),
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),

          SizedBox(height: width * 0.04),

          // Progress Bar
          Column(
            children: [
              SliderTheme(
                data: SliderTheme.of(context).copyWith(
                  activeTrackColor: Colors.cyanAccent,
                  inactiveTrackColor: Colors.grey[700],
                  thumbColor: Colors.cyanAccent,
                  overlayColor: Colors.cyanAccent.withValues(alpha: 0.2),
                  thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 8),
                ),
                child: Slider(
                  value: music.totalDuration.inMilliseconds > 0
                      ? music.currentPosition.inMilliseconds / music.totalDuration.inMilliseconds
                      : 0.0,
                  onChanged: (value) {
                    final position = Duration(
                      milliseconds: (value * music.totalDuration.inMilliseconds).round(),
                    );
                    _debugLog('⏩ Seek slider moved to: ${_formatDuration(position)}');
                    music.seekTo(position);
                  },
                ),
              ),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: width * 0.04),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      _formatDuration(music.currentPosition),
                      style: TextStyle(
                        color: Colors.white70,
                        fontFamily: 'Digital-7',
                        fontSize: fontSize.clamp(10.0, 14.0),
                      ),
                    ),
                    Text(
                      _formatDuration(music.totalDuration),
                      style: TextStyle(
                        color: Colors.white70,
                        fontFamily: 'Digital-7',
                        fontSize: fontSize.clamp(10.0, 14.0),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          SizedBox(height: width * 0.04),

          // Control Buttons Row
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              // Previous Track
              _buildControlButton(
                icon: Icons.skip_previous,
                onPressed: () {
                  _debugLog('⏮️ Previous track button pressed');
                  music.previousTrack();
                },
                size: width * 0.08,
              ),
              // Restart Track
              _buildControlButton(
                icon: Icons.replay,
                onPressed: () {
                  _debugLog('🔄 Restart track button pressed');
                  music.restartTrack();
                },
                size: width * 0.06,
              ),
              // Play/Pause (larger)
              _buildControlButton(
                icon: music.isPlaying ? Icons.pause_circle_filled : Icons.play_circle_fill,
                onPressed: () {
                  _debugLog('${music.isPlaying ? "⏸️ Pause" : "▶️ Play"} button pressed');
                  music.togglePlayPause();
                },
                size: width * 0.12,
                isMain: true,
              ),
              // Next Track
              _buildControlButton(
                icon: Icons.skip_next,
                onPressed: () {
                  _debugLog('⏭️ Next track button pressed');
                  music.nextTrack();
                },
                size: width * 0.08,
              ),
              // Shuffle Toggle
              _buildControlButton(
                icon: Icons.shuffle,
                onPressed: () {
                  _debugLog('🔀 Shuffle toggle pressed (currently: ${music.isShuffleEnabled})');
                  music.toggleShuffle();
                },
                size: width * 0.06,
                isActive: music.isShuffleEnabled,
              ),
            ],
          ),

          SizedBox(height: width * 0.04),

          // Action Buttons Row
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              // Track Selector Button
              _buildActionButton(
                label: 'Tracks',
                icon: Icons.queue_music,
                onPressed: () => _showTrackSelector(context),
                width: width,
              ),
              // Debug Button removed - music service is fully operational
            ],
          ),

          SizedBox(height: width * 0.04),

          // Volume Slider
          Row(
            children: [
              Icon(Icons.volume_down, color: Colors.white70, size: width * 0.06),
              Expanded(
                child: SliderTheme(
                  data: SliderTheme.of(context).copyWith(
                    activeTrackColor: Colors.cyanAccent,
                    inactiveTrackColor: Colors.grey[700],
                    thumbColor: Colors.cyanAccent,
                    overlayColor: Colors.cyanAccent.withValues(alpha: 0.2),
                  ),
                  child: Slider(
                    value: music.volume,
                    min: 0.0,
                    max: 1.0,
                    onChanged: (value) {
                      _debugLog('🔊 Volume slider changed to: ${(value * 100).round()}%');
                      music.setVolume(value);
                    },
                  ),
                ),
              ),
              Icon(Icons.volume_up, color: Colors.white70, size: width * 0.06),
            ],
          ),

          SizedBox(height: width * 0.02),
        ],
      ),
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required VoidCallback onPressed,
    required double size,
    bool isMain = false,
    bool isActive = false,
  }) {
    return Container(
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: isMain
            ? LinearGradient(
                colors: [Colors.cyanAccent, Colors.purple],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              )
            : null,
        color: isMain ? null : (isActive ? Colors.cyanAccent.withValues(alpha: 0.2) : Colors.transparent),
        border: Border.all(
          color: isActive ? Colors.cyanAccent : Colors.white30,
          width: isMain ? 2 : 1,
        ),
        boxShadow: isMain
            ? [
                BoxShadow(
                  color: Colors.cyanAccent.withValues(alpha: 0.3),
                  blurRadius: 12,
                  spreadRadius: 2,
                ),
              ]
            : null,
      ),
      child: IconButton(
        iconSize: size,
        color: isMain ? Colors.black : (isActive ? Colors.cyanAccent : Colors.white),
        icon: Icon(icon),
        onPressed: onPressed,
      ),
    );
  }

  Widget _buildActionButton({
    required String label,
    required IconData icon,
    required VoidCallback onPressed,
    required double width,
  }) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.cyanAccent.withValues(alpha: 0.5)),
        gradient: LinearGradient(
          colors: [
            Colors.black.withValues(alpha: 0.3),
            Colors.cyanAccent.withValues(alpha: 0.1),
          ],
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(8),
          onTap: onPressed,
          child: Padding(
            padding: EdgeInsets.symmetric(
              horizontal: width * 0.04,
              vertical: width * 0.02,
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(icon, color: Colors.cyanAccent, size: width * 0.05),
                SizedBox(width: width * 0.02),
                Text(
                  label,
                  style: TextStyle(
                    color: Colors.cyanAccent,
                    fontFamily: 'Pirulen',
                    fontSize: (width / 1080) * 16,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }

  // Debug method removed - music service is fully operational
}

/// Track Selector Modal for choosing specific tracks
class TrackSelectorModal extends StatelessWidget {
  const TrackSelectorModal({super.key});

  @override
  Widget build(BuildContext context) {
    final music = Provider.of<MusicService>(context);
    final width = MediaQuery.of(context).size.width;
    final height = MediaQuery.of(context).size.height;

    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        width: width * 0.9,
        height: height * 0.7,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.black.withValues(alpha: 0.95),
              Colors.purple.withValues(alpha: 0.2),
              Colors.cyan.withValues(alpha: 0.2),
            ],
          ),
          border: Border.all(
            color: Colors.cyanAccent.withValues(alpha: 0.5),
            width: 2,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.cyanAccent.withValues(alpha: 0.3),
              blurRadius: 20,
              spreadRadius: 5,
            ),
          ],
        ),
        child: Column(
          children: [
            // Header
            Container(
              padding: EdgeInsets.all(width * 0.04),
              decoration: BoxDecoration(
                borderRadius: const BorderRadius.vertical(top: Radius.circular(18)),
                gradient: LinearGradient(
                  colors: [
                    Colors.cyanAccent.withValues(alpha: 0.2),
                    Colors.purple.withValues(alpha: 0.2),
                  ],
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.queue_music,
                    color: Colors.cyanAccent,
                    size: width * 0.06,
                  ),
                  SizedBox(width: width * 0.03),
                  Expanded(
                    child: Text(
                      'MXD Tracks',
                      style: TextStyle(
                        color: Colors.cyanAccent,
                        fontFamily: 'Pirulen',
                        fontSize: (width / 1080) * 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: Icon(
                      Icons.close,
                      color: Colors.white70,
                      size: width * 0.06,
                    ),
                  ),
                ],
              ),
            ),

            // Track List
            Expanded(
              child: ListView.builder(
                padding: EdgeInsets.all(width * 0.02),
                itemCount: music.tracks.length,
                itemBuilder: (context, index) {
                  final track = music.tracks[index];
                  final isCurrentTrack = index == music.currentTrackIndex;

                  return Container(
                    margin: EdgeInsets.symmetric(vertical: width * 0.01),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      color: isCurrentTrack
                          ? Colors.cyanAccent.withValues(alpha: 0.2)
                          : Colors.black.withValues(alpha: 0.3),
                      border: Border.all(
                        color: isCurrentTrack
                            ? Colors.cyanAccent
                            : Colors.white.withValues(alpha: 0.1),
                        width: isCurrentTrack ? 2 : 1,
                      ),
                    ),
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        borderRadius: BorderRadius.circular(12),
                        onTap: () {
                          _debugLog('🎵 Track selected from modal: ${track.title} (index: $index)');
                          music.playTrack(index);
                          Navigator.of(context).pop();
                        },
                        child: Padding(
                          padding: EdgeInsets.all(width * 0.03),
                          child: Row(
                            children: [
                              // Track Number
                              Container(
                                width: width * 0.08,
                                height: width * 0.08,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: isCurrentTrack
                                      ? Colors.cyanAccent
                                      : Colors.white.withValues(alpha: 0.1),
                                ),
                                child: Center(
                                  child: Text(
                                    '${index + 1}',
                                    style: TextStyle(
                                      color: isCurrentTrack ? Colors.black : Colors.white,
                                      fontFamily: 'Digital-7',
                                      fontSize: (width / 1080) * 16,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ),

                              SizedBox(width: width * 0.03),

                              // Track Info
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      track.title,
                                      style: TextStyle(
                                        color: isCurrentTrack ? Colors.cyanAccent : Colors.white,
                                        fontFamily: 'Pirulen',
                                        fontSize: (width / 1080) * 18,
                                        fontWeight: FontWeight.bold,
                                      ),
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                    SizedBox(height: width * 0.005),
                                    Text(
                                      'by ${track.artist}',
                                      style: TextStyle(
                                        color: Colors.white70,
                                        fontFamily: 'Bitsumishi',
                                        fontSize: (width / 1080) * 14,
                                      ),
                                    ),
                                  ],
                                ),
                              ),

                              // Play Icon
                              if (isCurrentTrack && music.isPlaying)
                                Icon(
                                  Icons.volume_up,
                                  color: Colors.cyanAccent,
                                  size: width * 0.05,
                                )
                              else
                                Icon(
                                  Icons.play_arrow,
                                  color: Colors.white54,
                                  size: width * 0.05,
                                ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
