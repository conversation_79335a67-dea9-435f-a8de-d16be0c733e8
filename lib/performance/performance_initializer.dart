import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import '../bulletproof/error_handler.dart';
import 'performance_monitor.dart';
import 'optimized_storage.dart';

/// Performance initialization and configuration for the Maxed Out Life app.
/// 
/// Handles the setup and configuration of all performance-related systems:
/// - Performance monitoring initialization
/// - Optimized storage setup
/// - Memory management configuration
/// - Platform-specific optimizations
/// - Debug mode performance tools
/// 
/// Example usage:
/// ```dart
/// // Initialize performance systems
/// await PerformanceInitializer.initialize();
/// 
/// // Configure for production
/// PerformanceInitializer.configureForProduction();
/// 
/// // Enable debug tools
/// if (kDebugMode) {
///   PerformanceInitializer.enableDebugTools();
/// }
/// ```
class PerformanceInitializer {
  static bool _isInitialized = false;
  static final ErrorHandler _errorHandler = ErrorHandler();

  /// Initialize all performance systems
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Initialize performance monitoring
      final monitor = PerformanceMonitor();
      await monitor.initialize();

      // Initialize optimized storage
      final storage = OptimizedStorage();
      await storage.initialize();

      // Configure platform-specific optimizations
      await _configurePlatformOptimizations();

      // Set up memory management
      _configureMemoryManagement();

      // Enable debug tools in debug mode
      enableDebugTools();

      _isInitialized = true;

      print('✅ Performance systems initialized successfully');
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'PerformanceInitializer.initialize');
      rethrow;
    }
  }

  /// Configure optimizations for production builds
  static void configureForProduction() {
    // Disable debug-specific performance tracking
    // Enable production optimizations
    
    if (kDebugMode) {
      print('🚀 Performance configured for production');
    }
  }

  /// Enable debug tools and monitoring
  static void enableDebugTools() {
    if (!kDebugMode) return;

    // Enable performance overlay
    _enablePerformanceOverlay();
    
    // Set up debug callbacks
    _setupDebugCallbacks();
    
    print('🔧 Debug performance tools enabled');
  }

  /// Configure platform-specific optimizations
  static Future<void> _configurePlatformOptimizations() async {
    try {
      // Only configure platform channel for Android
      if (Platform.isAndroid) {
        // Platform channel configuration for Android only
        // iOS uses fallback monitoring to avoid MissingPluginException
        print('📱 Using Android-specific performance monitoring');
      } else {
        // iOS and other platforms use fallback monitoring
        print('📱 Using fallback performance monitoring for iOS');
      }
    } catch (e) {
      // Platform channel might not be available, continue without it
      print('⚠️ Platform-specific optimizations not available: $e');
    }
  }

  /// Configure memory management settings
  static void _configureMemoryManagement() {
    // Configure garbage collection hints
    // This is platform-specific and may not be available in all Flutter versions
    
    if (kDebugMode) {
      print('🧠 Memory management configured');
    }
  }

  /// Enable performance overlay for debug builds
  static void _enablePerformanceOverlay() {
    if (!kDebugMode) return;

    // Performance overlay is typically enabled through MaterialApp
    // This is a placeholder for additional overlay configuration
    
    print('📊 Performance overlay enabled');
  }

  /// Set up debug callbacks for performance monitoring
  static void _setupDebugCallbacks() {
    if (!kDebugMode) return;

    // Set up frame callbacks for FPS monitoring
    WidgetsBinding.instance.addPersistentFrameCallback((timeStamp) {
      PerformanceMonitor().recordFrameTime();
    });
    
    print('📈 Debug callbacks configured');
  }

  /// Get initialization status
  static bool get isInitialized => _isInitialized;

  /// Dispose performance systems
  static void dispose() {
    if (!_isInitialized) return;

    try {
      PerformanceMonitor().dispose();
      OptimizedStorage().dispose();
      
      _isInitialized = false;
      
      if (kDebugMode) {
        print('🧹 Performance systems disposed');
      }
    } catch (e, stackTrace) {
      _errorHandler.handleError(e, stackTrace, context: 'PerformanceInitializer.dispose');
    }
  }
}

/// Performance configuration options
class PerformanceConfig {
  final bool enableMonitoring;
  final bool enableCaching;
  final bool enableOptimizations;
  final Duration cacheExpiry;
  final int maxCacheSize;
  final bool enableDebugTools;

  const PerformanceConfig({
    this.enableMonitoring = true,
    this.enableCaching = true,
    this.enableOptimizations = true,
    this.cacheExpiry = const Duration(minutes: 10),
    this.maxCacheSize = 100,
    this.enableDebugTools = kDebugMode,
  });

  /// Default configuration for production
  static const PerformanceConfig production = PerformanceConfig(
    enableMonitoring: false,
    enableDebugTools: false,
  );

  /// Default configuration for development
  static const PerformanceConfig development = PerformanceConfig(
    enableMonitoring: true,
    enableDebugTools: true,
  );
}

/// Performance utilities for app-wide use
class PerformanceUtils {
  /// Check if performance monitoring is available
  static bool get isMonitoringAvailable => PerformanceInitializer.isInitialized;

  /// Get current performance status
  static String getPerformanceStatus() {
    if (!isMonitoringAvailable) {
      return 'Performance monitoring not available';
    }

    final monitor = PerformanceMonitor();
    final isHealthy = monitor.isPerformanceHealthy();
    final warnings = monitor.getPerformanceWarnings();

    if (isHealthy) {
      return 'Performance is healthy';
    } else {
      return 'Performance issues detected: ${warnings.length} warnings';
    }
  }

  /// Get performance report
  static String getPerformanceReport() {
    if (!isMonitoringAvailable) {
      return 'Performance monitoring not available';
    }

    return PerformanceMonitor().getPerformanceReport();
  }

  /// Log performance metrics (debug only)
  static void logPerformanceMetrics() {
    if (!kDebugMode || !isMonitoringAvailable) return;

    final report = getPerformanceReport();
    print('📊 PERFORMANCE METRICS:\n$report');
  }

  /// Check memory usage and warn if high
  static void checkMemoryUsage() {
    if (!isMonitoringAvailable) return;

    final metrics = PerformanceMonitor().getCurrentMetrics();
    if (metrics.currentMemoryMB > 200) {
      if (kDebugMode) {
        print('⚠️ High memory usage detected: ${metrics.currentMemoryMB.toStringAsFixed(1)} MB');
      }
    }
  }

  /// Suggest performance optimizations
  static List<String> getOptimizationSuggestions() {
    if (!isMonitoringAvailable) {
      return ['Enable performance monitoring for suggestions'];
    }

    final monitor = PerformanceMonitor();
    final metrics = monitor.getCurrentMetrics();
    final suggestions = <String>[];

    // FPS suggestions
    if (metrics.averageFps < 55 && metrics.averageFps > 0) {
      suggestions.add('Consider reducing widget complexity to improve frame rate');
    }

    // Memory suggestions
    if (metrics.currentMemoryMB > 200) {
      suggestions.add('High memory usage detected - consider implementing lazy loading');
    }

    // Frame time suggestions
    if (metrics.frameTimeP99 > 20) {
      suggestions.add('High frame times detected - optimize expensive operations');
    }

    // Widget rebuild suggestions
    final excessiveRebuilds = metrics.widgetRebuildCounts.entries
        .where((entry) => entry.value > 100)
        .toList();
    
    if (excessiveRebuilds.isNotEmpty) {
      suggestions.add('Excessive widget rebuilds detected - consider using const constructors');
    }

    if (suggestions.isEmpty) {
      suggestions.add('Performance looks good! No specific optimizations needed.');
    }

    return suggestions;
  }
}
