import 'dart:async';
import 'dart:developer' as developer;
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../bulletproof/error_handler.dart';

/// Comprehensive performance monitoring system for the Maxed Out Life app.
/// 
/// Provides real-time performance metrics including:
/// - Frame rate monitoring (FPS)
/// - Memory usage tracking
/// - CPU usage monitoring
/// - Network request timing
/// - Widget rebuild tracking
/// - Storage operation timing
/// 
/// Example usage:
/// ```dart
/// final monitor = PerformanceMonitor();
/// await monitor.initialize();
/// 
/// // Monitor a specific operation
/// final stopwatch = monitor.startTimer('data_load');
/// await loadUserData();
/// monitor.endTimer(stopwatch, 'data_load');
/// 
/// // Get performance metrics
/// final metrics = monitor.getCurrentMetrics();
/// print('FPS: ${metrics.averageFps}');
/// ```
class PerformanceMonitor {
  static final PerformanceMonitor _instance = PerformanceMonitor._internal();
  factory PerformanceMonitor() => _instance;
  PerformanceMonitor._internal();

  final ErrorHandler _errorHandler = ErrorHandler();
  
  // Performance tracking
  final List<double> _frameTimes = [];
  final List<int> _memoryUsage = [];
  final Map<String, List<int>> _operationTimes = {};
  final Map<String, int> _widgetRebuildCounts = {};
  
  Timer? _monitoringTimer;
  DateTime? _lastFrameTime;
  bool _isMonitoring = false;
  
  // Configuration
  static const int _maxFrameTimesSamples = 60; // 1 second at 60fps
  static const int _maxMemorySamples = 120; // 2 minutes at 1 sample/second
  static const Duration _monitoringInterval = Duration(seconds: 1);

  /// Initialize the performance monitor
  Future<void> initialize() async {
    if (_isMonitoring) return;

    try {
      _isMonitoring = true;
      _startMonitoring();
      
      if (kDebugMode) {
        developer.log('PerformanceMonitor initialized');
      }
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'PerformanceMonitor.initialize');
    }
  }

  /// Start monitoring performance metrics
  void _startMonitoring() {
    _monitoringTimer = Timer.periodic(_monitoringInterval, (timer) async {
      await _collectMetrics();
    });
  }

  /// Collect performance metrics
  Future<void> _collectMetrics() async {
    try {
      // Collect memory usage
      await _collectMemoryMetrics();
      
      // Clean up old samples
      _cleanupOldSamples();
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'PerformanceMonitor._collectMetrics');
    }
  }

  /// Collect memory usage metrics
  Future<void> _collectMemoryMetrics() async {
    try {
      if (Platform.isAndroid) {
        // Use platform channel to get memory info (Android only)
        const platform = MethodChannel('performance_monitor');
        final memoryInfo = await platform.invokeMethod<Map>('getMemoryInfo');

        if (memoryInfo != null) {
          final usedMemory = memoryInfo['usedMemory'] as int? ?? 0;
          _memoryUsage.add(usedMemory);
        }
      } else {
        // Fallback for iOS and other platforms - use basic memory estimation
        _memoryUsage.add(0);
      }
    } catch (e) {
      // Platform channel might not be available, use fallback
      _memoryUsage.add(0);
    }
  }

  /// Record frame time for FPS calculation
  void recordFrameTime() {
    final now = DateTime.now();
    if (_lastFrameTime != null) {
      final frameTime = now.difference(_lastFrameTime!).inMicroseconds / 1000.0;
      _frameTimes.add(frameTime);
    }
    _lastFrameTime = now;
  }

  /// Start timing an operation
  Stopwatch startTimer(String operationName) {
    final stopwatch = Stopwatch()..start();
    return stopwatch;
  }

  /// End timing an operation and record the result
  void endTimer(Stopwatch stopwatch, String operationName) {
    stopwatch.stop();
    final timeMs = stopwatch.elapsedMilliseconds;
    
    _operationTimes.putIfAbsent(operationName, () => []);
    _operationTimes[operationName]!.add(timeMs);
    
    // Keep only recent samples
    if (_operationTimes[operationName]!.length > 100) {
      _operationTimes[operationName]!.removeAt(0);
    }
  }

  /// Record widget rebuild
  void recordWidgetRebuild(String widgetName) {
    _widgetRebuildCounts[widgetName] = (_widgetRebuildCounts[widgetName] ?? 0) + 1;
  }

  /// Get current performance metrics
  PerformanceMetrics getCurrentMetrics() {
    return PerformanceMetrics(
      averageFps: _calculateAverageFps(),
      currentMemoryMB: _getCurrentMemoryMB(),
      averageMemoryMB: _calculateAverageMemoryMB(),
      operationTimes: Map.from(_operationTimes),
      widgetRebuildCounts: Map.from(_widgetRebuildCounts),
      frameTimeP95: _calculateFrameTimePercentile(0.95),
      frameTimeP99: _calculateFrameTimePercentile(0.99),
    );
  }

  /// Calculate average FPS from frame times
  double _calculateAverageFps() {
    if (_frameTimes.isEmpty) return 0.0;
    
    final averageFrameTime = _frameTimes.reduce((a, b) => a + b) / _frameTimes.length;
    return averageFrameTime > 0 ? 1000.0 / averageFrameTime : 0.0;
  }

  /// Get current memory usage in MB
  double _getCurrentMemoryMB() {
    if (_memoryUsage.isEmpty) return 0.0;
    return _memoryUsage.last / (1024 * 1024);
  }

  /// Calculate average memory usage in MB
  double _calculateAverageMemoryMB() {
    if (_memoryUsage.isEmpty) return 0.0;
    
    final average = _memoryUsage.reduce((a, b) => a + b) / _memoryUsage.length;
    return average / (1024 * 1024);
  }

  /// Calculate frame time percentile
  double _calculateFrameTimePercentile(double percentile) {
    if (_frameTimes.isEmpty) return 0.0;
    
    final sortedTimes = List<double>.from(_frameTimes)..sort();
    final index = (sortedTimes.length * percentile).floor();
    return sortedTimes[index.clamp(0, sortedTimes.length - 1)];
  }

  /// Clean up old samples to prevent memory leaks
  void _cleanupOldSamples() {
    // Keep only recent frame times
    if (_frameTimes.length > _maxFrameTimesSamples) {
      _frameTimes.removeRange(0, _frameTimes.length - _maxFrameTimesSamples);
    }
    
    // Keep only recent memory samples
    if (_memoryUsage.length > _maxMemorySamples) {
      _memoryUsage.removeRange(0, _memoryUsage.length - _maxMemorySamples);
    }
  }

  /// Get performance report as string
  String getPerformanceReport() {
    final metrics = getCurrentMetrics();
    final buffer = StringBuffer();
    
    buffer.writeln('=== PERFORMANCE REPORT ===');
    buffer.writeln('Average FPS: ${metrics.averageFps.toStringAsFixed(1)}');
    buffer.writeln('Current Memory: ${metrics.currentMemoryMB.toStringAsFixed(1)} MB');
    buffer.writeln('Average Memory: ${metrics.averageMemoryMB.toStringAsFixed(1)} MB');
    buffer.writeln('Frame Time P95: ${metrics.frameTimeP95.toStringAsFixed(1)} ms');
    buffer.writeln('Frame Time P99: ${metrics.frameTimeP99.toStringAsFixed(1)} ms');
    
    buffer.writeln('\n--- Operation Times ---');
    metrics.operationTimes.forEach((operation, times) {
      if (times.isNotEmpty) {
        final average = times.reduce((a, b) => a + b) / times.length;
        final max = times.reduce((a, b) => a > b ? a : b);
        buffer.writeln('$operation: avg ${average.toStringAsFixed(1)}ms, max ${max}ms');
      }
    });
    
    buffer.writeln('\n--- Widget Rebuilds ---');
    final sortedRebuilds = metrics.widgetRebuildCounts.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    for (final entry in sortedRebuilds.take(10)) {
      buffer.writeln('${entry.key}: ${entry.value} rebuilds');
    }
    
    return buffer.toString();
  }

  /// Check if performance is healthy
  bool isPerformanceHealthy() {
    final metrics = getCurrentMetrics();
    
    // Check FPS (should be > 55 for good performance)
    if (metrics.averageFps < 55 && metrics.averageFps > 0) return false;
    
    // Check frame time P99 (should be < 20ms for 60fps)
    if (metrics.frameTimeP99 > 20) return false;
    
    // Check memory usage (should be < 200MB for mobile)
    if (metrics.currentMemoryMB > 200) return false;
    
    return true;
  }

  /// Get performance warnings
  List<String> getPerformanceWarnings() {
    final metrics = getCurrentMetrics();
    final warnings = <String>[];
    
    if (metrics.averageFps < 55 && metrics.averageFps > 0) {
      warnings.add('Low FPS detected: ${metrics.averageFps.toStringAsFixed(1)}');
    }
    
    if (metrics.frameTimeP99 > 20) {
      warnings.add('High frame time P99: ${metrics.frameTimeP99.toStringAsFixed(1)}ms');
    }
    
    if (metrics.currentMemoryMB > 200) {
      warnings.add('High memory usage: ${metrics.currentMemoryMB.toStringAsFixed(1)}MB');
    }
    
    // Check for excessive rebuilds
    metrics.widgetRebuildCounts.forEach((widget, count) {
      if (count > 100) {
        warnings.add('Excessive rebuilds in $widget: $count');
      }
    });
    
    return warnings;
  }

  /// Dispose resources
  void dispose() {
    _monitoringTimer?.cancel();
    _isMonitoring = false;
    _frameTimes.clear();
    _memoryUsage.clear();
    _operationTimes.clear();
    _widgetRebuildCounts.clear();
  }
}

/// Performance metrics data class
class PerformanceMetrics {
  final double averageFps;
  final double currentMemoryMB;
  final double averageMemoryMB;
  final Map<String, List<int>> operationTimes;
  final Map<String, int> widgetRebuildCounts;
  final double frameTimeP95;
  final double frameTimeP99;

  const PerformanceMetrics({
    required this.averageFps,
    required this.currentMemoryMB,
    required this.averageMemoryMB,
    required this.operationTimes,
    required this.widgetRebuildCounts,
    required this.frameTimeP95,
    required this.frameTimeP99,
  });
}

/// Widget mixin for tracking rebuilds
mixin PerformanceTrackingMixin<T extends StatefulWidget> on State<T> {
  final PerformanceMonitor _monitor = PerformanceMonitor();
  
  @override
  Widget build(BuildContext context) {
    if (kDebugMode) {
      _monitor.recordWidgetRebuild(widget.runtimeType.toString());
    }
    return buildWithTracking(context);
  }
  
  /// Override this method instead of build()
  Widget buildWithTracking(BuildContext context);
}

/// Performance-aware StatelessWidget
abstract class PerformanceAwareStatelessWidget extends StatelessWidget {
  const PerformanceAwareStatelessWidget({super.key});
  
  @override
  Widget build(BuildContext context) {
    if (kDebugMode) {
      PerformanceMonitor().recordWidgetRebuild(runtimeType.toString());
    }
    return buildWithTracking(context);
  }
  
  /// Override this method instead of build()
  Widget buildWithTracking(BuildContext context);
}
