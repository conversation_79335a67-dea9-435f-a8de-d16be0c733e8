import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';

/// Widget optimization utilities for improved performance.
/// 
/// Provides optimized widgets and utilities including:
/// - Cached network images with memory management
/// - Optimized list views with lazy loading
/// - Efficient animation controllers
/// - Memory-aware image widgets
/// - Performance-optimized containers
/// 
/// Example usage:
/// ```dart
/// // Optimized list view
/// OptimizedListView.builder(
///   itemCount: items.length,
///   itemBuilder: (context, index) => ItemWidget(items[index]),
///   cacheExtent: 1000,
/// )
/// 
/// // Cached image with memory management
/// OptimizedImage.network(
///   imageUrl,
///   cacheWidth: 300,
///   cacheHeight: 200,
/// )
/// ```

/// Optimized ListView with performance enhancements
class OptimizedListView extends StatelessWidget {
  final int itemCount;
  final IndexedWidgetBuilder itemBuilder;
  final ScrollController? controller;
  final EdgeInsets? padding;
  final double? cacheExtent;
  final bool addAutomaticKeepAlives;
  final bool addRepaintBoundaries;

  const OptimizedListView.builder({
    super.key,
    required this.itemCount,
    required this.itemBuilder,
    this.controller,
    this.padding,
    this.cacheExtent = 1000.0,
    this.addAutomaticKeepAlives = false,
    this.addRepaintBoundaries = true,
  });

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      controller: controller,
      padding: padding,
      cacheExtent: cacheExtent,
      addAutomaticKeepAlives: addAutomaticKeepAlives,
      addRepaintBoundaries: addRepaintBoundaries,
      itemCount: itemCount,
      itemBuilder: (context, index) {
        // Wrap each item in a repaint boundary for better performance
        return RepaintBoundary(
          child: itemBuilder(context, index),
        );
      },
    );
  }
}

/// Optimized image widget with caching and memory management
class OptimizedImage extends StatelessWidget {
  final String imageUrl;
  final double? width;
  final double? height;
  final int? cacheWidth;
  final int? cacheHeight;
  final BoxFit fit;
  final Widget? placeholder;
  final Widget? errorWidget;

  const OptimizedImage.network(
    this.imageUrl, {
    super.key,
    this.width,
    this.height,
    this.cacheWidth,
    this.cacheHeight,
    this.fit = BoxFit.cover,
    this.placeholder,
    this.errorWidget,
  });

  @override
  Widget build(BuildContext context) {
    return Image.network(
      imageUrl,
      width: width,
      height: height,
      fit: fit,
      cacheWidth: cacheWidth,
      cacheHeight: cacheHeight,
      frameBuilder: (context, child, frame, wasSynchronouslyLoaded) {
        if (wasSynchronouslyLoaded) return child;
        
        return AnimatedOpacity(
          opacity: frame == null ? 0 : 1,
          duration: const Duration(milliseconds: 300),
          child: child,
        );
      },
      loadingBuilder: (context, child, loadingProgress) {
        if (loadingProgress == null) return child;
        
        return placeholder ?? 
          Center(
            child: CircularProgressIndicator(
              value: loadingProgress.expectedTotalBytes != null
                  ? loadingProgress.cumulativeBytesLoaded / 
                    loadingProgress.expectedTotalBytes!
                  : null,
            ),
          );
      },
      errorBuilder: (context, error, stackTrace) {
        return errorWidget ?? 
          const Icon(
            Icons.error,
            color: Colors.red,
          );
      },
    );
  }
}

/// Performance-optimized container with cached decorations
class OptimizedContainer extends StatelessWidget {
  final Widget? child;
  final double? width;
  final double? height;
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  final Color? color;
  final Decoration? decoration;
  final AlignmentGeometry? alignment;

  const OptimizedContainer({
    super.key,
    this.child,
    this.width,
    this.height,
    this.padding,
    this.margin,
    this.color,
    this.decoration,
    this.alignment,
  });

  @override
  Widget build(BuildContext context) {
    return RepaintBoundary(
      child: Container(
        width: width,
        height: height,
        padding: padding,
        margin: margin,
        decoration: decoration,
        alignment: alignment,
        child: child,
      ),
    );
  }
}

/// Efficient animation controller wrapper
class OptimizedAnimationController extends AnimationController {
  OptimizedAnimationController({
    required super.vsync,
    super.duration,
    super.debugLabel,
  });

  @override
  void dispose() {
    // Ensure proper cleanup
    stop();
    super.dispose();
  }
}

/// Lazy loading widget for expensive operations
class LazyWidget extends StatefulWidget {
  final Widget Function() builder;
  final Widget? placeholder;
  final bool preload;

  const LazyWidget({
    super.key,
    required this.builder,
    this.placeholder,
    this.preload = false,
  });

  @override
  State<LazyWidget> createState() => _LazyWidgetState();
}

class _LazyWidgetState extends State<LazyWidget> {
  Widget? _cachedWidget;
  bool _isBuilt = false;

  @override
  void initState() {
    super.initState();
    if (widget.preload) {
      _buildWidget();
    }
  }

  void _buildWidget() {
    if (!_isBuilt) {
      _cachedWidget = widget.builder();
      _isBuilt = true;
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!_isBuilt) {
      _buildWidget();
    }
    
    return _cachedWidget ?? widget.placeholder ?? const SizedBox.shrink();
  }
}

/// Viewport-aware widget that only builds when visible
class ViewportAwareWidget extends StatefulWidget {
  final Widget child;
  final double threshold;

  const ViewportAwareWidget({
    super.key,
    required this.child,
    this.threshold = 0.1,
  });

  @override
  State<ViewportAwareWidget> createState() => _ViewportAwareWidgetState();
}

class _ViewportAwareWidgetState extends State<ViewportAwareWidget> {
  bool _isVisible = false;

  @override
  Widget build(BuildContext context) {
    return VisibilityDetector(
      key: widget.key ?? ValueKey(widget.hashCode),
      onVisibilityChanged: (info) {
        final isVisible = info.visibleFraction > widget.threshold;
        if (isVisible != _isVisible) {
          setState(() {
            _isVisible = isVisible;
          });
        }
      },
      child: _isVisible ? widget.child : const SizedBox.shrink(),
    );
  }
}

/// Simple visibility detector
class VisibilityDetector extends StatefulWidget {
  final Widget child;
  final Function(VisibilityInfo) onVisibilityChanged;

  const VisibilityDetector({
    super.key,
    required this.child,
    required this.onVisibilityChanged,
  });

  @override
  State<VisibilityDetector> createState() => _VisibilityDetectorState();
}

class _VisibilityDetectorState extends State<VisibilityDetector> {
  @override
  Widget build(BuildContext context) {
    return NotificationListener<ScrollNotification>(
      onNotification: (notification) {
        _checkVisibility();
        return false;
      },
      child: widget.child,
    );
  }

  void _checkVisibility() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final renderObject = context.findRenderObject();
      if (renderObject is RenderBox) {
        final viewport = RenderAbstractViewport.of(renderObject);
        final vpHeight = viewport.paintBounds.height;
        final vpWidth = viewport.paintBounds.width;
        final position = renderObject.localToGlobal(Offset.zero);
        final size = renderObject.size;

        final visibleHeight = (size.height + position.dy).clamp(0.0, vpHeight) -
                             position.dy.clamp(0.0, vpHeight);
        final visibleWidth = (size.width + position.dx).clamp(0.0, vpWidth) -
                            position.dx.clamp(0.0, vpWidth);

        final visibleArea = visibleHeight * visibleWidth;
        final totalArea = size.height * size.width;
        final visibleFraction = totalArea > 0 ? visibleArea / totalArea : 0.0;

        widget.onVisibilityChanged(VisibilityInfo(visibleFraction));
      }
    });
  }
}

/// Visibility information
class VisibilityInfo {
  final double visibleFraction;
  
  const VisibilityInfo(this.visibleFraction);
}

/// Optimized text widget with caching
class OptimizedText extends StatelessWidget {
  final String text;
  final TextStyle? style;
  final TextAlign? textAlign;
  final int? maxLines;
  final TextOverflow? overflow;

  const OptimizedText(
    this.text, {
    super.key,
    this.style,
    this.textAlign,
    this.maxLines,
    this.overflow,
  });

  @override
  Widget build(BuildContext context) {
    return RepaintBoundary(
      child: Text(
        text,
        style: style,
        textAlign: textAlign,
        maxLines: maxLines,
        overflow: overflow,
      ),
    );
  }
}

/// Memory-efficient grid view
class OptimizedGridView extends StatelessWidget {
  final int itemCount;
  final IndexedWidgetBuilder itemBuilder;
  final SliverGridDelegate gridDelegate;
  final ScrollController? controller;
  final EdgeInsets? padding;

  const OptimizedGridView.builder({
    super.key,
    required this.itemCount,
    required this.itemBuilder,
    required this.gridDelegate,
    this.controller,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      controller: controller,
      padding: padding,
      gridDelegate: gridDelegate,
      itemCount: itemCount,
      addAutomaticKeepAlives: false,
      addRepaintBoundaries: true,
      cacheExtent: 1000,
      itemBuilder: (context, index) {
        return RepaintBoundary(
          child: itemBuilder(context, index),
        );
      },
    );
  }
}

/// Performance utilities
class PerformanceUtils {
  /// Debounce function calls to prevent excessive rebuilds
  static Timer? _debounceTimer;
  
  static void debounce(Duration delay, VoidCallback callback) {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(delay, callback);
  }

  /// Throttle function calls to limit frequency
  static DateTime? _lastThrottleTime;
  
  static void throttle(Duration interval, VoidCallback callback) {
    final now = DateTime.now();
    if (_lastThrottleTime == null || 
        now.difference(_lastThrottleTime!) >= interval) {
      _lastThrottleTime = now;
      callback();
    }
  }

  /// Batch widget updates
  static final List<VoidCallback> _batchedUpdates = [];
  static Timer? _batchTimer;
  
  static void batchUpdate(VoidCallback update) {
    _batchedUpdates.add(update);
    
    _batchTimer?.cancel();
    _batchTimer = Timer(const Duration(milliseconds: 16), () {
      for (final update in _batchedUpdates) {
        update();
      }
      _batchedUpdates.clear();
    });
  }

  /// Check if device has sufficient memory for operation
  static bool hasEnoughMemory(int requiredBytes) {
    // This would need platform-specific implementation
    // For now, return true as a placeholder
    return true;
  }

  /// Dispose static resources
  static void dispose() {
    _debounceTimer?.cancel();
    _batchTimer?.cancel();
    _batchedUpdates.clear();
  }
}
