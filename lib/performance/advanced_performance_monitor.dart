// 📁 lib/performance/advanced_performance_monitor.dart

import 'dart:async';
import 'dart:collection';
import 'package:flutter/foundation.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/services.dart';

/// Advanced performance monitoring system with real-time analysis,
/// automatic optimization, and predictive performance management.
class AdvancedPerformanceMonitor {
  static final AdvancedPerformanceMonitor _instance = AdvancedPerformanceMonitor._internal();
  factory AdvancedPerformanceMonitor() => _instance;
  AdvancedPerformanceMonitor._internal() {
    _initialize();
  }

  // Performance metrics storage
  final Queue<FrameMetrics> _frameMetrics = Queue();
  final Map<String, OperationMetrics> _operationMetrics = {};
  final Map<String, int> _widgetRebuildCounts = {};
  final List<PerformanceAlert> _alerts = [];
  
  // Real-time monitoring
  Timer? _monitoringTimer;
  bool _isMonitoring = false;
  
  // Performance thresholds
  static const double _warningFpsThreshold = 45.0;
  static const double _criticalFpsThreshold = 30.0;
  static const int _maxFrameTimeMs = 16; // 60 FPS = 16.67ms per frame
  static const int _maxOperationTimeMs = 100;
  static const int _maxRebuildCount = 50;

  /// Start advanced performance monitoring
  void startMonitoring() {
    if (_isMonitoring) return;
    
    _isMonitoring = true;
    
    // Start frame metrics collection
    SchedulerBinding.instance.addTimingsCallback(_onFrameMetrics);
    
    // Start periodic analysis
    _monitoringTimer = Timer.periodic(const Duration(seconds: 5), (_) {
      _analyzePerformance();
    });
    
    if (kDebugMode) print('🚀 Advanced performance monitoring started');
  }

  /// Stop performance monitoring
  void stopMonitoring() {
    if (!_isMonitoring) return;
    
    _isMonitoring = false;
    
    SchedulerBinding.instance.removeTimingsCallback(_onFrameMetrics);
    _monitoringTimer?.cancel();
    
    if (kDebugMode) print('⏹️ Performance monitoring stopped');
  }

  /// Record operation performance
  PerformanceTracker trackOperation(String operationName) {
    return PerformanceTracker(operationName, this);
  }

  /// Record widget rebuild
  void recordWidgetRebuild(String widgetName) {
    _widgetRebuildCounts[widgetName] = (_widgetRebuildCounts[widgetName] ?? 0) + 1;
    
    // Check for excessive rebuilds
    final count = _widgetRebuildCounts[widgetName]!;
    if (count > _maxRebuildCount) {
      _addAlert(PerformanceAlert(
        type: AlertType.excessiveRebuilds,
        message: 'Widget $widgetName has rebuilt $count times',
        severity: AlertSeverity.warning,
        timestamp: DateTime.now(),
      ));
    }
  }

  /// Get current performance metrics
  PerformanceSnapshot getCurrentMetrics() {
    final recentFrames = _frameMetrics.where((frame) {
      return DateTime.now().difference(frame.timestamp).inSeconds < 10;
    }).toList();
    
    final avgFps = recentFrames.isEmpty ? 0.0 : 
        recentFrames.length / (recentFrames.last.timestamp.difference(recentFrames.first.timestamp).inSeconds);
    
    final avgFrameTime = recentFrames.isEmpty ? 0.0 :
        recentFrames.map((f) => f.buildDuration + f.rasterDuration).reduce((a, b) => a + b) / recentFrames.length;
    
    return PerformanceSnapshot(
      averageFps: avgFps,
      averageFrameTimeMs: avgFrameTime,
      memoryUsageMB: _getMemoryUsage(),
      activeAlerts: _alerts.where((a) => a.isActive).length,
      operationMetrics: Map.from(_operationMetrics),
      widgetRebuildCounts: Map.from(_widgetRebuildCounts),
    );
  }

  /// Get performance recommendations
  List<PerformanceRecommendation> getRecommendations() {
    final recommendations = <PerformanceRecommendation>[];
    final metrics = getCurrentMetrics();
    
    // FPS recommendations
    if (metrics.averageFps < _warningFpsThreshold) {
      recommendations.add(PerformanceRecommendation(
        type: RecommendationType.fps,
        title: 'Low Frame Rate Detected',
        description: 'Current FPS: ${metrics.averageFps.toStringAsFixed(1)}. Consider reducing widget complexity.',
        priority: metrics.averageFps < _criticalFpsThreshold ? Priority.critical : Priority.high,
        actions: [
          'Use const constructors where possible',
          'Implement RepaintBoundary for complex widgets',
          'Consider lazy loading for large lists',
        ],
      ));
    }
    
    // Memory recommendations
    if (metrics.memoryUsageMB > 200) {
      recommendations.add(PerformanceRecommendation(
        type: RecommendationType.memory,
        title: 'High Memory Usage',
        description: 'Current usage: ${metrics.memoryUsageMB.toStringAsFixed(1)}MB',
        priority: Priority.medium,
        actions: [
          'Implement image caching with size limits',
          'Dispose unused controllers and streams',
          'Use ListView.builder for large lists',
        ],
      ));
    }
    
    // Widget rebuild recommendations
    final excessiveRebuilds = metrics.widgetRebuildCounts.entries
        .where((entry) => entry.value > _maxRebuildCount)
        .toList();
    
    if (excessiveRebuilds.isNotEmpty) {
      recommendations.add(PerformanceRecommendation(
        type: RecommendationType.rebuilds,
        title: 'Excessive Widget Rebuilds',
        description: '${excessiveRebuilds.length} widgets rebuilding frequently',
        priority: Priority.medium,
        actions: [
          'Use const constructors',
          'Implement proper state management',
          'Consider using ValueListenableBuilder',
        ],
      ));
    }
    
    return recommendations;
  }

  /// Force performance optimization
  Future<void> optimizePerformance() async {
    try {
      // Clear excessive rebuild counters
      _widgetRebuildCounts.clear();
      
      // Trigger garbage collection
      await _triggerGarbageCollection();
      
      // Clear old frame metrics
      _frameMetrics.clear();
      
      // Clear old alerts
      _alerts.removeWhere((alert) => 
          DateTime.now().difference(alert.timestamp).inMinutes > 5);
      
      if (kDebugMode) print('🧹 Performance optimization completed');
    } catch (e) {
      if (kDebugMode) print('❌ Performance optimization error: $e');
    }
  }

  // Private methods
  void _initialize() {
    if (kDebugMode) {
      print('🔧 Initializing advanced performance monitor');
    }
  }

  void _onFrameMetrics(List<FrameTiming> timings) {
    for (final timing in timings) {
      final buildDuration = timing.buildDuration.inMicroseconds / 1000.0; // Convert to ms
      final rasterDuration = timing.rasterDuration.inMicroseconds / 1000.0;
      
      _frameMetrics.add(FrameMetrics(
        buildDuration: buildDuration,
        rasterDuration: rasterDuration,
        timestamp: DateTime.now(),
      ));
      
      // Keep only recent metrics
      while (_frameMetrics.length > 300) { // ~5 seconds at 60 FPS
        _frameMetrics.removeFirst();
      }
      
      // Check for frame drops
      final totalFrameTime = buildDuration + rasterDuration;
      if (totalFrameTime > _maxFrameTimeMs) {
        _addAlert(PerformanceAlert(
          type: AlertType.frameDrops,
          message: 'Frame took ${totalFrameTime.toStringAsFixed(1)}ms (target: ${_maxFrameTimeMs}ms)',
          severity: AlertSeverity.warning,
          timestamp: DateTime.now(),
        ));
      }
    }
  }

  void _analyzePerformance() {
    final metrics = getCurrentMetrics();
    
    // Analyze FPS
    if (metrics.averageFps < _criticalFpsThreshold) {
      _addAlert(PerformanceAlert(
        type: AlertType.lowFps,
        message: 'Critical FPS: ${metrics.averageFps.toStringAsFixed(1)}',
        severity: AlertSeverity.critical,
        timestamp: DateTime.now(),
      ));
    }
    
    // Analyze memory
    if (metrics.memoryUsageMB > 300) {
      _addAlert(PerformanceAlert(
        type: AlertType.highMemory,
        message: 'High memory usage: ${metrics.memoryUsageMB.toStringAsFixed(1)}MB',
        severity: AlertSeverity.warning,
        timestamp: DateTime.now(),
      ));
    }
    
    if (kDebugMode) {
      print('📊 Performance Analysis: FPS: ${metrics.averageFps.toStringAsFixed(1)}, '
            'Memory: ${metrics.memoryUsageMB.toStringAsFixed(1)}MB, '
            'Alerts: ${metrics.activeAlerts}');
    }
  }

  void _addAlert(PerformanceAlert alert) {
    _alerts.add(alert);
    
    // Keep only recent alerts
    _alerts.removeWhere((a) => 
        DateTime.now().difference(a.timestamp).inMinutes > 10);
    
    if (kDebugMode) {
      print('⚠️ Performance Alert: ${alert.message}');
    }
  }

  double _getMemoryUsage() {
    // This would need platform-specific implementation
    // For now, return a placeholder
    return 150.0; // MB
  }

  Future<void> _triggerGarbageCollection() async {
    // Force garbage collection (platform-specific)
    try {
      await SystemChannels.platform.invokeMethod('System.gc');
    } catch (e) {
      // Ignore if not supported
    }
  }

  void _recordOperationEnd(String operationName, int durationMs) {
    final existing = _operationMetrics[operationName];
    if (existing == null) {
      _operationMetrics[operationName] = OperationMetrics(
        name: operationName,
        totalCalls: 1,
        totalDurationMs: durationMs,
        maxDurationMs: durationMs,
        minDurationMs: durationMs,
      );
    } else {
      _operationMetrics[operationName] = OperationMetrics(
        name: operationName,
        totalCalls: existing.totalCalls + 1,
        totalDurationMs: existing.totalDurationMs + durationMs,
        maxDurationMs: durationMs > existing.maxDurationMs ? durationMs : existing.maxDurationMs,
        minDurationMs: durationMs < existing.minDurationMs ? durationMs : existing.minDurationMs,
      );
    }
    
    // Check for slow operations
    if (durationMs > _maxOperationTimeMs) {
      _addAlert(PerformanceAlert(
        type: AlertType.slowOperation,
        message: 'Slow operation: $operationName took ${durationMs}ms',
        severity: AlertSeverity.warning,
        timestamp: DateTime.now(),
      ));
    }
  }
}

/// Performance tracker for individual operations
class PerformanceTracker {
  final String operationName;
  final AdvancedPerformanceMonitor monitor;
  final Stopwatch _stopwatch = Stopwatch();

  PerformanceTracker(this.operationName, this.monitor) {
    _stopwatch.start();
  }

  void end() {
    _stopwatch.stop();
    monitor._recordOperationEnd(operationName, _stopwatch.elapsedMilliseconds);
  }
}

// Data classes
class FrameMetrics {
  final double buildDuration;
  final double rasterDuration;
  final DateTime timestamp;

  FrameMetrics({
    required this.buildDuration,
    required this.rasterDuration,
    required this.timestamp,
  });
}

class OperationMetrics {
  final String name;
  final int totalCalls;
  final int totalDurationMs;
  final int maxDurationMs;
  final int minDurationMs;

  OperationMetrics({
    required this.name,
    required this.totalCalls,
    required this.totalDurationMs,
    required this.maxDurationMs,
    required this.minDurationMs,
  });

  double get averageDurationMs => totalDurationMs / totalCalls;
}

class PerformanceSnapshot {
  final double averageFps;
  final double averageFrameTimeMs;
  final double memoryUsageMB;
  final int activeAlerts;
  final Map<String, OperationMetrics> operationMetrics;
  final Map<String, int> widgetRebuildCounts;

  PerformanceSnapshot({
    required this.averageFps,
    required this.averageFrameTimeMs,
    required this.memoryUsageMB,
    required this.activeAlerts,
    required this.operationMetrics,
    required this.widgetRebuildCounts,
  });
}

class PerformanceAlert {
  final AlertType type;
  final String message;
  final AlertSeverity severity;
  final DateTime timestamp;

  PerformanceAlert({
    required this.type,
    required this.message,
    required this.severity,
    required this.timestamp,
  });

  bool get isActive => DateTime.now().difference(timestamp).inMinutes < 5;
}

class PerformanceRecommendation {
  final RecommendationType type;
  final String title;
  final String description;
  final Priority priority;
  final List<String> actions;

  PerformanceRecommendation({
    required this.type,
    required this.title,
    required this.description,
    required this.priority,
    required this.actions,
  });
}

enum AlertType { lowFps, frameDrops, highMemory, slowOperation, excessiveRebuilds }
enum AlertSeverity { info, warning, critical }
enum RecommendationType { fps, memory, rebuilds, operations }
enum Priority { low, medium, high, critical }
