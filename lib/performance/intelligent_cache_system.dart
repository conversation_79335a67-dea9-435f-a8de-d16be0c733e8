// 📁 lib/performance/intelligent_cache_system.dart

import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

/// Enterprise-grade intelligent caching system with automatic memory management,
/// predictive preloading, and performance optimization for maximum user experience.
class IntelligentCacheSystem {
  static final IntelligentCacheSystem _instance = IntelligentCacheSystem._internal();
  factory IntelligentCacheSystem() => _instance;
  IntelligentCacheSystem._internal();

  // Multi-tier cache architecture
  final Map<String, CacheEntry> _memoryCache = {};
  final Map<String, int> _accessFrequency = {};
  final Map<String, DateTime> _lastAccessed = {};
  
  static const FlutterSecureStorage _secureStorage = FlutterSecureStorage();

  // Performance constants
  static const int _maxMemoryCacheSize = 100;
  // Note: Disk cache size managed by underlying cache implementation
  static const Duration _memoryTtl = Duration(minutes: 30);
  static const Duration _diskTtl = Duration(hours: 24);
  static const Duration _preloadWindow = Duration(seconds: 5);

  /// Get cached data with intelligent fallback and preloading
  Future<T?> get<T>(
    String key, {
    Future<T> Function()? fallback,
    Duration? ttl,
    bool enablePreload = true,
  }) async {
    try {
      // Update access patterns for intelligence
      _updateAccessPatterns(key);
      
      // Try memory cache first (fastest)
      final memoryResult = _getFromMemory<T>(key, ttl ?? _memoryTtl);
      if (memoryResult != null) {
        if (enablePreload) _schedulePreload(key);
        return memoryResult;
      }
      
      // Try disk cache (fast)
      final diskResult = await _getFromDisk<T>(key, ttl ?? _diskTtl);
      if (diskResult != null) {
        // Promote to memory cache
        _setInMemory(key, diskResult, ttl ?? _memoryTtl);
        if (enablePreload) _schedulePreload(key);
        return diskResult;
      }
      
      // Use fallback and cache result (slow but cached for future)
      if (fallback != null) {
        final result = await fallback();
        if (result != null) {
          await set(key, result, ttl: ttl);
        }
        return result;
      }
      
      return null;
    } catch (e) {
      if (kDebugMode) print('❌ Cache get error for key $key: $e');
      return fallback?.call();
    }
  }

  /// Set data in cache with intelligent tier placement
  Future<void> set<T>(String key, T value, {Duration? ttl}) async {
    try {
      final effectiveTtl = ttl ?? _memoryTtl;
      
      // Always set in memory for immediate access
      _setInMemory(key, value, effectiveTtl);
      
      // Set in disk for persistence (async to not block)
      unawaited(_setInDisk(key, value, ttl ?? _diskTtl));
      
      // Update access patterns
      _updateAccessPatterns(key);
      
      // Trigger cache optimization
      _optimizeCache();
    } catch (e) {
      if (kDebugMode) print('❌ Cache set error for key $key: $e');
    }
  }

  /// Preload frequently accessed data
  Future<void> preload(Map<String, Future<dynamic> Function()> loaders) async {
    try {
      final futures = <Future<void>>[];
      
      for (final entry in loaders.entries) {
        final key = entry.key;
        final loader = entry.value;
        
        // Only preload if not already cached and frequently accessed
        if (!_isInMemory(key) && _isFrequentlyAccessed(key)) {
          futures.add(_preloadSingle(key, loader));
        }
      }
      
      // Execute preloads in parallel with timeout
      await Future.wait(futures).timeout(
        const Duration(seconds: 10),
        onTimeout: () {
          if (kDebugMode) print('⚠️ Preload timeout - continuing anyway');
          return [];
        },
      );
    } catch (e) {
      if (kDebugMode) print('❌ Preload error: $e');
    }
  }

  /// Clear cache with intelligent retention
  Future<void> clear({bool retainFrequent = true}) async {
    try {
      if (retainFrequent) {
        // Keep frequently accessed items
        final keysToRetain = _memoryCache.keys
            .where(_isFrequentlyAccessed)
            .toList();
        
        _memoryCache.clear();
        
        // Restore frequently accessed items
        for (final key in keysToRetain) {
          final diskValue = await _getFromDisk(key, _diskTtl);
          if (diskValue != null) {
            _setInMemory(key, diskValue, _memoryTtl);
          }
        }
      } else {
        _memoryCache.clear();
        _accessFrequency.clear();
        _lastAccessed.clear();
        
        // Clear disk cache
        await _clearDiskCache();
      }
    } catch (e) {
      if (kDebugMode) print('❌ Cache clear error: $e');
    }
  }

  /// Get cache statistics for monitoring
  CacheStats getStats() {
    final memorySize = _memoryCache.length;
    final totalAccesses = _accessFrequency.values.fold(0, (sum, freq) => sum + freq);
    final avgAccessFreq = totalAccesses / (_accessFrequency.length.clamp(1, double.infinity));
    
    return CacheStats(
      memorySize: memorySize,
      diskSize: 0, // Would need platform-specific implementation
      totalAccesses: totalAccesses,
      averageAccessFrequency: avgAccessFreq,
      hitRate: _calculateHitRate(),
    );
  }

  // Private methods
  T? _getFromMemory<T>(String key, Duration ttl) {
    final entry = _memoryCache[key];
    if (entry == null) return null;
    
    if (DateTime.now().difference(entry.timestamp) > ttl) {
      _memoryCache.remove(key);
      return null;
    }
    
    return entry.value as T?;
  }

  Future<T?> _getFromDisk<T>(String key, Duration ttl) async {
    try {
      final raw = await _secureStorage.read(key: 'cache_$key');
      if (raw == null) return null;
      
      final data = jsonDecode(raw);
      final timestamp = DateTime.parse(data['timestamp']);
      
      if (DateTime.now().difference(timestamp) > ttl) {
        await _secureStorage.delete(key: 'cache_$key');
        return null;
      }
      
      return data['value'] as T?;
    } catch (e) {
      if (kDebugMode) print('❌ Disk cache read error for $key: $e');
      return null;
    }
  }

  void _setInMemory<T>(String key, T value, Duration ttl) {
    _memoryCache[key] = CacheEntry(value, DateTime.now());
    _optimizeMemoryCache();
  }

  Future<void> _setInDisk<T>(String key, T value, Duration ttl) async {
    try {
      final data = {
        'value': value,
        'timestamp': DateTime.now().toIso8601String(),
      };
      await _secureStorage.write(key: 'cache_$key', value: jsonEncode(data));
    } catch (e) {
      if (kDebugMode) print('❌ Disk cache write error for $key: $e');
    }
  }

  bool _isInMemory(String key) => _memoryCache.containsKey(key);

  bool _isFrequentlyAccessed(String key) {
    final frequency = _accessFrequency[key] ?? 0;
    final avgFrequency = _accessFrequency.values.isEmpty 
        ? 0 
        : _accessFrequency.values.reduce((a, b) => a + b) / _accessFrequency.length;
    return frequency > avgFrequency * 1.5; // 50% above average
  }

  void _updateAccessPatterns(String key) {
    _accessFrequency[key] = (_accessFrequency[key] ?? 0) + 1;
    _lastAccessed[key] = DateTime.now();
  }

  void _schedulePreload(String key) {
    Timer(_preloadWindow, () {
      // Implement predictive preloading logic here
      if (kDebugMode) print('🔮 Predictive preload opportunity for $key');
    });
  }

  Future<void> _preloadSingle(String key, Future<dynamic> Function() loader) async {
    try {
      final value = await loader();
      if (value != null) {
        await set(key, value);
      }
    } catch (e) {
      if (kDebugMode) print('❌ Preload error for $key: $e');
    }
  }

  void _optimizeCache() {
    _optimizeMemoryCache();
    // Could add disk cache optimization here
  }

  void _optimizeMemoryCache() {
    if (_memoryCache.length <= _maxMemoryCacheSize) return;
    
    // Remove least recently used items
    final sortedKeys = _memoryCache.keys.toList()
      ..sort((a, b) {
        final aTime = _lastAccessed[a] ?? DateTime(1970);
        final bTime = _lastAccessed[b] ?? DateTime(1970);
        return aTime.compareTo(bTime);
      });
    
    final keysToRemove = sortedKeys.take(_memoryCache.length - _maxMemoryCacheSize);
    for (final key in keysToRemove) {
      _memoryCache.remove(key);
    }
  }

  Future<void> _clearDiskCache() async {
    try {
      final allKeys = await _secureStorage.readAll();
      final cacheKeys = allKeys.keys.where((key) => key.startsWith('cache_'));
      
      for (final key in cacheKeys) {
        await _secureStorage.delete(key: key);
      }
    } catch (e) {
      if (kDebugMode) print('❌ Disk cache clear error: $e');
    }
  }

  double _calculateHitRate() {
    // Simplified hit rate calculation
    final totalRequests = _accessFrequency.values.fold(0, (sum, freq) => sum + freq);
    final cacheHits = _memoryCache.length; // Simplified
    return totalRequests > 0 ? cacheHits / totalRequests : 0.0;
  }
}

class CacheEntry {
  final dynamic value;
  final DateTime timestamp;

  CacheEntry(this.value, this.timestamp);
}

class CacheStats {
  final int memorySize;
  final int diskSize;
  final int totalAccesses;
  final double averageAccessFrequency;
  final double hitRate;

  CacheStats({
    required this.memorySize,
    required this.diskSize,
    required this.totalAccesses,
    required this.averageAccessFrequency,
    required this.hitRate,
  });

  @override
  String toString() {
    return 'CacheStats(memory: $memorySize, disk: $diskSize, '
           'accesses: $totalAccesses, avgFreq: ${averageAccessFrequency.toStringAsFixed(2)}, '
           'hitRate: ${(hitRate * 100).toStringAsFixed(1)}%)';
  }
}
