import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import '../design_system/design_system.dart';
import 'performance_monitor.dart';

// Removed optimized_storage.dart - service was deleted

/// Performance dashboard for monitoring app performance in debug mode.
/// 
/// Provides real-time performance metrics including:
/// - FPS monitoring and frame time analysis
/// - Memory usage tracking and trends
/// - Storage operation timing
/// - Widget rebuild statistics
/// - Performance warnings and recommendations
/// 
/// Only available in debug mode for development purposes.
/// 
/// Example usage:
/// ```dart
/// // Show performance dashboard
/// Navigator.push(
///   context,
///   MaterialPageRoute(
///     builder: (context) => PerformanceDashboard(),
///   ),
/// );
/// 
/// // Or as overlay
/// PerformanceDashboard.showOverlay(context);
/// ```
class PerformanceDashboard extends StatefulWidget {
  const PerformanceDashboard({super.key});

  /// Show performance dashboard as overlay
  static void showOverlay(BuildContext context) {
    if (!kDebugMode) return;
    
    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: SizedBox(
          width: MediaQuery.of(context).size.width * 0.9,
          height: MediaQuery.of(context).size.height * 0.8,
          child: const PerformanceDashboard(),
        ),
      ),
    );
  }

  @override
  State<PerformanceDashboard> createState() => _PerformanceDashboardState();
}

class _PerformanceDashboardState extends State<PerformanceDashboard>
    with TickerProviderStateMixin {
  final PerformanceMonitor _monitor = PerformanceMonitor();
  // Removed OptimizedStorage - service was deleted

  late TabController _tabController;
  PerformanceMetrics? _metrics;
  // Removed CacheStats - using context-aware loading instead
  
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadMetrics();
    _initializeContextAwareLoading();

    // Refresh metrics every second
    Timer.periodic(const Duration(seconds: 1), (timer) {
      if (mounted) {
        _loadMetrics();
      } else {
        timer.cancel();
      }
    });
  }

  /// Initialize context-aware loading for performance screen (now handled by Smart Service Manager)
  Future<void> _initializeContextAwareLoading() async {
    try {
      // Services are now automatically managed by Smart Service Manager
      if (kDebugMode) print('🧠 Performance services managed by Smart Service Manager');
    } catch (e) {
      if (kDebugMode) print('❌ Failed to initialize services: $e');
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadMetrics() {
    setState(() {
      _metrics = _monitor.getCurrentMetrics();
      // Cache stats now handled by context-aware loading
    });
  }

  @override
  Widget build(BuildContext context) {
    if (!kDebugMode) {
      return const SizedBox.shrink();
    }

    return AppCard.glow(
      glowColor: MolColors.cyan,
      child: Column(
        children: [
          // Header
          Container(
            padding: EdgeInsets.all(DesignTokens.spaceXl),
            child: Row(
              children: [
                Icon(
                  Icons.speed,
                  color: MolColors.cyan,
                  size: DesignTokens.iconSizeLg,
                ),
                SizedBox(width: DesignTokens.spaceLg),
                Expanded(
                  child: Text(
                    'Performance Dashboard',
                    style: AppTypography.withColor(
                      AppTypography.headingMedium,
                      Colors.white,
                    ),
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.close, color: Colors.white),
                  onPressed: () => Navigator.of(context).pop(),
                ),
              ],
            ),
          ),
          
          // Performance Status
          _buildPerformanceStatus(),
          
          // Tabs
          TabBar(
            controller: _tabController,
            labelColor: MolColors.cyan,
            unselectedLabelColor: Colors.white54,
            indicatorColor: MolColors.cyan,
            tabs: const [
              Tab(text: 'Overview'),
              Tab(text: 'Memory'),
              Tab(text: 'Operations'),
              Tab(text: 'Widgets'),
            ],
          ),
          
          // Tab Content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildOverviewTab(),
                _buildMemoryTab(),
                _buildOperationsTab(),
                _buildWidgetsTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPerformanceStatus() {
    if (_metrics == null) {
      return const SizedBox.shrink();
    }

    final isHealthy = _monitor.isPerformanceHealthy();
    final warnings = _monitor.getPerformanceWarnings();
    
    return Container(
      padding: EdgeInsets.all(DesignTokens.spaceXl),
      child: Row(
        children: [
          Icon(
            isHealthy ? Icons.check_circle : Icons.warning,
            color: isHealthy ? MolColors.green : MolColors.orange,
            size: DesignTokens.iconSizeLg,
          ),
          SizedBox(width: DesignTokens.spaceLg),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  isHealthy ? 'Performance Healthy' : 'Performance Issues',
                  style: AppTypography.withColor(
                    AppTypography.labelLarge,
                    isHealthy ? MolColors.green : MolColors.orange,
                  ),
                ),
                if (warnings.isNotEmpty) ...[
                  SizedBox(height: DesignTokens.spaceSm),
                  Text(
                    '${warnings.length} warning${warnings.length > 1 ? 's' : ''}',
                    style: AppTypography.withColor(
                      AppTypography.bodySmall,
                      Colors.white70,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOverviewTab() {
    if (_metrics == null) {
      return const Center(child: CircularProgressIndicator());
    }

    return SingleChildScrollView(
      padding: EdgeInsets.all(DesignTokens.spaceXl),
      child: Column(
        children: [
          // FPS Metric
          _buildMetricCard(
            'Frame Rate',
            '${_metrics!.averageFps.toStringAsFixed(1)} FPS',
            _metrics!.averageFps >= 55 ? MolColors.green : MolColors.red,
            Icons.speed,
          ),
          
          SizedBox(height: DesignTokens.spaceLg),
          
          // Memory Metric
          _buildMetricCard(
            'Memory Usage',
            '${_metrics!.currentMemoryMB.toStringAsFixed(1)} MB',
            _metrics!.currentMemoryMB < 200 ? MolColors.green : MolColors.orange,
            Icons.memory,
          ),
          
          SizedBox(height: DesignTokens.spaceLg),
          
          // Frame Time P99
          _buildMetricCard(
            'Frame Time P99',
            '${_metrics!.frameTimeP99.toStringAsFixed(1)} ms',
            _metrics!.frameTimeP99 < 20 ? MolColors.green : MolColors.red,
            Icons.timer,
          ),
          
          SizedBox(height: DesignTokens.spaceLg),
          
          // Context-Aware Loading Status
          _buildMetricCard(
            'Context Loading',
            'Active',
            MolColors.green,
            Icons.smart_toy,
          ),
        ],
      ),
    );
  }

  Widget _buildMemoryTab() {
    if (_metrics == null) {
      return const Center(child: CircularProgressIndicator());
    }

    return SingleChildScrollView(
      padding: EdgeInsets.all(DesignTokens.spaceXl),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Memory Statistics',
            style: AppTypography.withColor(
              AppTypography.headingSmall,
              Colors.white,
            ),
          ),
          
          SizedBox(height: DesignTokens.spaceLg),
          
          _buildInfoRow('Current Memory', '${_metrics!.currentMemoryMB.toStringAsFixed(1)} MB'),
          _buildInfoRow('Average Memory', '${_metrics!.averageMemoryMB.toStringAsFixed(1)} MB'),
          _buildInfoRow('Context Services', 'Loaded on demand'),
          _buildInfoRow('Smart Loading', 'Active'),
        ],
      ),
    );
  }

  Widget _buildOperationsTab() {
    if (_metrics == null) {
      return const Center(child: CircularProgressIndicator());
    }

    return SingleChildScrollView(
      padding: EdgeInsets.all(DesignTokens.spaceXl),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Operation Timings',
            style: AppTypography.withColor(
              AppTypography.headingSmall,
              Colors.white,
            ),
          ),
          
          SizedBox(height: DesignTokens.spaceLg),
          
          ..._metrics!.operationTimes.entries.map((entry) {
            if (entry.value.isEmpty) return const SizedBox.shrink();

            final average = entry.value.reduce((a, b) => a + b) / entry.value.length;
            final max = entry.value.reduce((a, b) => a > b ? a : b);

            return Container(
              margin: EdgeInsets.only(bottom: DesignTokens.spaceLg),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    entry.key,
                    style: AppTypography.withColor(
                      AppTypography.labelMedium,
                      Colors.white,
                    ),
                  ),
                  SizedBox(height: DesignTokens.spaceSm),
                  _buildInfoRow('Average', '${average.toStringAsFixed(1)} ms'),
                  _buildInfoRow('Maximum', '$max ms'),
                  _buildInfoRow('Samples', '${entry.value.length}'),
                ],
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildWidgetsTab() {
    if (_metrics == null) {
      return const Center(child: CircularProgressIndicator());
    }

    final sortedRebuilds = _metrics!.widgetRebuildCounts.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    return SingleChildScrollView(
      padding: EdgeInsets.all(DesignTokens.spaceXl),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Widget Rebuilds',
            style: AppTypography.withColor(
              AppTypography.headingSmall,
              Colors.white,
            ),
          ),
          
          SizedBox(height: DesignTokens.spaceLg),
          
          ...sortedRebuilds.take(20).map((entry) {
            final isExcessive = entry.value > 100;
            return Container(
              margin: EdgeInsets.only(bottom: DesignTokens.spaceMd),
              padding: EdgeInsets.all(DesignTokens.spaceLg),
              decoration: BoxDecoration(
                color: isExcessive ? MolColors.red.withValues(alpha: 0.1) : Colors.grey[800],
                borderRadius: BorderRadius.circular(DesignTokens.radiusLg),
                border: isExcessive ? Border.all(color: MolColors.red.withValues(alpha: 0.3)) : null,
              ),
              child: Row(
                children: [
                  if (isExcessive)
                    Icon(
                      Icons.warning,
                      color: MolColors.red,
                      size: DesignTokens.iconSizeSm,
                    ),
                  if (isExcessive) SizedBox(width: DesignTokens.spaceMd),
                  Expanded(
                    child: Text(
                      entry.key,
                      style: AppTypography.withColor(
                        AppTypography.bodySmall,
                        Colors.white,
                      ),
                    ),
                  ),
                  Text(
                    '${entry.value}',
                    style: AppTypography.withColor(
                      AppTypography.labelMedium,
                      isExcessive ? MolColors.red : MolColors.cyan,
                    ),
                  ),
                ],
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildMetricCard(String title, String value, Color color, IconData icon) {
    return Container(
      padding: EdgeInsets.all(DesignTokens.spaceXl),
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(DesignTokens.radiusLg),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(icon, color: color, size: DesignTokens.iconSizeLg),
          SizedBox(width: DesignTokens.spaceLg),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: AppTypography.withColor(
                    AppTypography.labelMedium,
                    Colors.white70,
                  ),
                ),
                Text(
                  value,
                  style: AppTypography.withColor(
                    AppTypography.headingSmall,
                    color,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: DesignTokens.spaceSm),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: AppTypography.withColor(
              AppTypography.bodyMedium,
              Colors.white70,
            ),
          ),
          Text(
            value,
            style: AppTypography.withColor(
              AppTypography.labelMedium,
              Colors.white,
            ),
          ),
        ],
      ),
    );
  }
}
