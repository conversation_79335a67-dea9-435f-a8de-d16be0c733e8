import 'package:flutter/material.dart';
import '../topbar/bar_helpers.dart';

class MilestoneNotchesPainter extends CustomPainter {
  final double fillPercent; // 0.0–1.0
  final Color notchColor;
  final List<double> xpHistoryPercents; // from helper

  MilestoneNotchesPainter({
    required this.fillPercent,
    required this.notchColor,
    required this.xpHistoryPercents,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final Paint notchPaint = Paint()
      ..color = notchColor
      ..strokeWidth = 2;

    final double barWidth = size.width;
    final double barHeight = size.height;
    // Draw fixed‐position notches at milestonePercs:
    for (double m in milestonePercs) {
      final x = barWidth * m;
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, barHeight),
        notchPaint,
      );
    }

    // Draw the last 7‐day sparkline under the bar:
    if (xpHistoryPercents.isNotEmpty) {
      final Paint sparkPaint = Paint()
        ..color = Colors.cyanAccent.withValues(alpha: 0.7)
        ..strokeWidth = 1.5;
      final double sparkHeight = 10;
      final double startY = barHeight + 4;
      final int len = xpHistoryPercents.length;
      final double step = barWidth / len;
      Path path = Path();
      for (int i = 0; i < len; i++) {
        final dx = step * i + step / 2;
        final dy = startY + sparkHeight * (1 - xpHistoryPercents[i]);
        if (i == 0) {
          path.moveTo(dx, dy);
        } else {
          path.lineTo(dx, dy);
        }
      }
      canvas.drawPath(path, sparkPaint);
    }
  }

  @override
  bool shouldRepaint(covariant MilestoneNotchesPainter old) {
    return old.fillPercent != fillPercent ||
           old.xpHistoryPercents != xpHistoryPercents;
  }
}
