// lib/widgets/topbar/overlays/streak_ribbon.dart

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../controller/user_controller2.dart';

/// Displays a small "flame + X-day streak" indicator if the user has logged
/// progress for more than one day in a row.  Uses `user.streakDays`.
class StreakRibbon extends StatelessWidget {
  const StreakRibbon({super.key});

  @override
  Widget build(BuildContext context) {
    // NOTE: 7-Day Development screen refers to `user.streakDays`, not `dailyStreakDays`.
    final streak = context.select((UserController2 uc) => uc.user?.streakDays ?? 0);
    if (streak <= 1) return const SizedBox.shrink();

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        const Icon(Icons.whatshot, color: Colors.redAccent, size: 16),
        const SizedBox(width: 4),
        Text(
          "$streak-day streak",
          style: const TextStyle(
            color: Colors.redAccent,
            fontFamily: '<PERSON>su<PERSON><PERSON>',
            fontSize: 12,
          ),
        ),
      ],
    );
  }
}
