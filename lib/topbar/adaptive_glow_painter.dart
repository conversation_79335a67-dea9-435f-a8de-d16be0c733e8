import 'package:flutter/material.dart';

class AdaptiveGlowPainter extends CustomPainter {
  final double idleScale;
  final Color glowColor;
  final double borderRadius;

  AdaptiveGlowPainter({
    required this.idleScale,
    required this.glowColor,
    required this.borderRadius,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final radius = borderRadius;
    final Paint borderPaint = Paint()
      ..color = glowColor.withValues(alpha: (idleScale * 0.3).clamp(0.0, 0.3))
      ..style = PaintingStyle.stroke
      ..strokeWidth = 4;
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(0, 0, size.width, size.height),
        Radius.circular(radius),
      ),
      borderPaint,
    );
  }

  @override
  bool shouldRepaint(covariant AdaptiveGlowPainter old) {
    return old.idleScale != idleScale || old.glowColor != glowColor;
  }
}
