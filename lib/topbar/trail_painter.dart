import 'package:flutter/material.dart';

class TrailPainter extends CustomPainter {
  final double currentPct;
  final double previewPct;
  final Color trailColor;

  TrailPainter({
    required this.currentPct,
    required this.previewPct,
    required this.trailColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final double barHeight = size.height;
    final double barWidth = size.width;
    if (previewPct <= currentPct) return;

    final double trailStartX = barWidth * currentPct;
    final double trailWidth = barWidth * (previewPct - currentPct);
    final Rect trailRect = Rect.fromLTWH(
      trailStartX, 0, trailWidth, barHeight,
    );
    final Paint paint = Paint()
      ..color = trailColor.withValues(alpha: 0.3)
      ..style = PaintingStyle.fill;
    canvas.drawRRect(
      RRect.fromRectAndRadius(trailRect, Radius.circular(barHeight / 2)),
      paint,
    );
  }

  @override
  bool shouldRepaint(covariant TrailPainter old) {
    return old.currentPct != currentPct ||
           old.previewPct != previewPct;
  }
}
