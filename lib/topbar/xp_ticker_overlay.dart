import 'package:flutter/material.dart';

class XpTickerOverlay extends StatefulWidget {
  final int fromExp;
  final int toExp;
  final Duration duration;

  const XpTickerOverlay({
    super.key,
    required this.fromExp,
    required this.toExp,
    this.duration = const Duration(milliseconds: 600),
  });

  @override
  // ignore: library_private_types_in_public_api
  _XpTickerOverlayState createState() => _XpTickerOverlayState();
}

class _XpTickerOverlayState extends State<XpTickerOverlay> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<int> _expAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(vsync: this, duration: widget.duration);
    _expAnimation = IntTween(begin: widget.fromExp, end: widget.toExp).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeOut),
    )..addListener(() => setState(() {}));
    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Text(
      "${_expAnimation.value}",
      style: const TextStyle(
        color: Colors.cyanAccent,
        fontFamily: 'Bitsumishi',
        fontSize: 14,
        fontWeight: FontWeight.bold,
      ),
    );
  }
}
