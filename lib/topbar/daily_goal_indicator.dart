// lib/widgets/topbar/overlays/daily_goal_indicator.dart

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../controller/user_controller2.dart';

/// Shows a small circular progress indicator (24×24) representing
/// how close the user is to the fixed daily XP goal of 120 EXP.
/// It computes:
///   todayExp = sum of all DiaryEntry.exp where entry.timestamp is today
///   dailyGoal = 120 (fixed)
///
/// This version handles `entry.exp` potentially being null by treating null as 0.
class DailyGoalIndicator extends StatelessWidget {
  const DailyGoalIndicator({super.key});

  @override
  Widget build(BuildContext context) {
    final user = context.select((UserController2 uc) => uc.user);
    if (user == null) return const SizedBox.shrink();

    // 1) Determine "today" by year/month/day.
    final now = DateTime.now();
    final int currentYear = now.year;
    final int currentMonth = now.month;
    final int currentDay = now.day;

    // 2) Sum all entry.exp (using 0 if exp is null) for entries whose timestamp's Y/M/D = today.
    final int todayExp = (user.diary.values).fold<int>(0, (sum, entry) {
      final ts = entry.timestamp; // non-nullable DateTime
      if (ts.year == currentYear && ts.month == currentMonth && ts.day == currentDay) {
        // If entry.exp is null, default to 0 and cast to int.
        return sum + ((entry.exp ?? 0) as int);
      }
      return sum;
    });

    // 3) Daily goal is always 120 EXP (12 hours × 10 EXP).
    const int dailyGoal = 120;

    // 4) Compute a fraction between 0.0 and 1.0.
    //    todayExp and dailyGoal are ints, so division yields a double.
    double fraction = todayExp / dailyGoal;
    if (fraction < 0.0) {
      fraction = 0.0;
    } else if (fraction > 1.0) {
      fraction = 1.0;
    }

    return SizedBox(
      width: 24,
      height: 24,
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Circular background + progress ring
          CircularProgressIndicator(
            value: fraction,
            strokeWidth: 3,
            backgroundColor: Colors.white10,
            valueColor: const AlwaysStoppedAnimation<Color>(Colors.greenAccent),
          ),

          // Centered percentage text
          Text(
            "${(fraction * 100).round()}%",
            style: const TextStyle(
              color: Colors.white,
              fontFamily: 'Bitsumishi',
              fontSize: 10,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}
