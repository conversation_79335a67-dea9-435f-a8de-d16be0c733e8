import 'package:flutter/material.dart';
import 'dart:math';

class ParticleBurst extends StatefulWidget {
  final Color color;
  final Duration duration;

  const ParticleBurst({
    super.key,
    required this.color,
    this.duration = const Duration(milliseconds: 500),
  });

  @override
  // ignore: library_private_types_in_public_api
  _ParticleBurstState createState() => _ParticleBurstState();
}

class _ParticleBurstState extends State<ParticleBurst> with SingleTickerProviderStateMixin {
  late AnimationController _ctrl;
  final Random _rng = Random();

  @override
  void initState() {
    super.initState();
    _ctrl = AnimationController(vsync: this, duration: widget.duration)
      ..forward();
  }

  @override
  void dispose() {
    _ctrl.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _ctrl,
      builder: (context, child) {
        return CustomPaint(
          painter: _ParticleBurstPainter(
            progress: _ctrl.value,
            color: widget.color,
            rng: _rng,
          ),
          child: const SizedBox.expand(),
        );
      },
    );
  }
}

class _ParticleBurstPainter extends CustomPainter {
  final double progress;
  final Color color;
  final Random rng;

  _ParticleBurstPainter({
    required this.progress,
    required this.color,
    required this.rng,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final int particleCount = 30;
    final double maxRadius = size.width * 0.3;
    for (int i = 0; i < particleCount; i++) {
      final angle = rng.nextDouble() * 2 * pi;
      final radius = progress * rng.nextDouble() * maxRadius;
      final offset = Offset(
        size.width / 2 + cos(angle) * radius,
        size.height / 2 + sin(angle) * radius,
      );
      final paint = Paint()
        ..color = color.withValues(alpha: 1 - progress)
        ..style = PaintingStyle.fill;
      canvas.drawCircle(offset, (1 - progress) * 6, paint);
    }
  }

  @override
  bool shouldRepaint(covariant _ParticleBurstPainter old) {
    return old.progress != progress;
  }
}
