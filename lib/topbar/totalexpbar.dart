// 📁 lib/widgets/totalexpbar.dart

import 'dart:math';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:audioplayers/audioplayers.dart';

import '../widgets/exp_pie_chart_popup.dart';
import '../controller/user_controller2.dart';

import '../utils/rank_utils.dart'; // For LevelUtils.getRankLevel & getRankProgressPct

/// A full-width neon "Total EXP" bar at the very top of the screen.
/// - Tap → opens [ExpPieChartPopup].
/// - Long-press → shows a tooltip comparing your "Normal EXP" vs "[N.S.] EXP".
/// - Animates a neon glow flash whenever EXP increases.
/// - When you level up, plays a level-up sound and briefly shows a particle burst.
/// - Always overlays continuous "lightning" bolts over the filled portion.
/// 
/// We've beefed up the "electric" effect by:
///   • Increasing the number of lightning bolts from 3 → 4.
///   • Slightly increasing the neon glow blur radius.
///   • Cranking the opacity on certain highlights.
///
/// This widget expects its parent to supply:
///   • [currentExp] – the user's total EXP count.
///   • [expToNext] – how many EXP are left until the next level.
class TotalExpBar extends StatefulWidget {
  final int currentExp;  // e.g. 1006
  final int expToNext;   // EXP remaining until the next rank

  const TotalExpBar({
    super.key,
    required this.currentExp,
    required this.expToNext,
  });

  @override
  State<TotalExpBar> createState() => _TotalExpBarState();
}

class _TotalExpBarState extends State<TotalExpBar>
    with TickerProviderStateMixin {
  late final AnimationController _flashController;
  late final Animation<double> _flashAnim;

  late final AnimationController _lightningController;

  bool _showParticleBurst = false;
  final Random _rng = Random();

  @override
  void initState() {
    super.initState();

    // ─── Flash controller ─────────────────────────────────────────
    // On any EXP increase, we scale up the glow from 1.0 → 1.6 then back.
    _flashController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 600),
    );
    _flashAnim = Tween<double>(begin: 1.0, end: 1.6).animate(
      CurvedAnimation(parent: _flashController, curve: Curves.easeOut),
    );
    _flashController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        _flashController.reverse();
      }
    });

    // ─── Lightning controller ─────────────────────────────────────
    // Continuously cycles every 600ms so that the lightning bolts appear to flicker.
    _lightningController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 600),
    )..repeat();
  }

  @override
  void didUpdateWidget(covariant TotalExpBar oldWidget) {
    super.didUpdateWidget(oldWidget);
    final int newExp = widget.currentExp;
    final int newLevel = LevelUtils.getRankLevel(newExp);

    print('⚡ TotalExpBar: didUpdateWidget');
    print('   Old EXP: ${oldWidget.currentExp}');
    print('   New EXP: $newExp');
    print('   Old Level: ${LevelUtils.getRankLevel(oldWidget.currentExp)}');
    print('   New Level: $newLevel');

    // If EXP increased at all, trigger a neon‐glow "flash":
    if (newExp > oldWidget.currentExp) {
      print('⚡ TotalExpBar: EXP increased! Triggering flash animation');
      _flashController.forward(from: 0.0);
    }

    // If level actually changed, play a level-up sound + show particle burst:
    if (newLevel > LevelUtils.getRankLevel(oldWidget.currentExp)) {
      print('⚡ TotalExpBar: Level up! Playing sound and showing particle burst');
      _playLevelUpSound();
      setState(() => _showParticleBurst = true);
      Future.delayed(const Duration(milliseconds: 800), () {
        if (mounted) {
          setState(() => _showParticleBurst = false);
        }
      });
    }
  }

  @override
  void dispose() {
    _flashController.dispose();
    _lightningController.dispose();
    super.dispose();
  }

  /// Randomly pick one of three level-up sound files (levelup1.mp3, 2.mp3, 3.mp3).
  void _playLevelUpSound() {
    final player = AudioPlayer();
    final randomIndex = Random().nextInt(3) + 1; // 1..3
    // Use existing sound files since levelup files don't exist
    final soundFiles = ['sounds/success.mp3', 'sounds/lightning.mp3', 'sounds/success.mp3'];
    player.play(AssetSource(soundFiles[randomIndex - 1]), volume: 0.6);
  }

  @override
  Widget build(BuildContext context) {
    final userController = context.watch<UserController2>();
    final user = userController.user;
    if (user == null) return const SizedBox.shrink();

    final int totalExp = widget.currentExp;
    final int nsExp = user.northStarQuest?.totalExp ?? 0;
    final int normalExp = totalExp - nsExp;

    // Fill percentage (clamped between 0.0 and 1.0):
    final double percent =
        LevelUtils.getRankProgressPct(totalExp).clamp(0.0, 1.0);
    final int level = LevelUtils.getRankLevel(totalExp);

    return GestureDetector(
      behavior: HitTestBehavior.opaque,

      // ─── Tap: open the EXP Pie Chart popup ───────────────────
      onTap: () async {
        await showDialog(
          context: context,
          builder: (_) => ExpPieChartPopup(),
        );
      },

      // ─── Long-press: show "Normal vs [N.S.] EXP" tooltip ─────
      onLongPress: () {
        showDialog(
          context: context,
          builder: (_) => AlertDialog(
            backgroundColor: Colors.black,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            content: Text(
              "Normal EXP: $normalExp\n[N.S.] EXP:   $nsExp",
              style: const TextStyle(
                fontFamily: 'Bitsumishi',
                color: Colors.white70,
                fontSize: 14,
              ),
            ),
          ),
        );
      },

      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // ─── (1) Level & Total EXP label ────────────────────
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Level $level',
                  style: const TextStyle(
                    fontFamily: 'Pirulen',
                    fontSize: 16,
                    color: Colors.cyanAccent,
                    fontWeight: FontWeight.bold,
                    letterSpacing: 1.2,
                  ),
                ),
                Text(
                  'Total: $totalExp EXP',
                  style: const TextStyle(
                    fontFamily: 'Digital-7',
                    fontSize: 12,
                    color: Colors.white70,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 4),

            // ─── (2) Animated neon bar w/ flash + lightning overlay ──
            AnimatedBuilder(
              animation:
                  Listenable.merge([_flashController, _lightningController]),
              builder: (context, child) {
                final double flashScale = _flashAnim.value;
                final double lightningValue = _lightningController.value;
                return CustomPaint(
                  painter: _TotalExpPainter(
                    fillPercent: percent,
                    glowScale: flashScale,
                    lightningValue: lightningValue,
                    randomSeed: _rng.nextInt(100000),
                  ),
                  child: SizedBox(
                    height: 36,
                    width: double.infinity,
                    child: Stack(
                      alignment: Alignment.center,
                      children: [
                        // Always show "N XP to next" label in tiny text:
                        Positioned(
                          right: 16,
                          child: Container(
                            constraints: const BoxConstraints(maxWidth: 120),
                            child: Text(
                              '${widget.expToNext} XP to next',
                              style: const TextStyle(
                                fontFamily: 'Digital-7',
                                fontSize: 10,
                                color: Colors.white60,
                              ),
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                            ),
                          ),
                        ),

                        // Simplified particle burst icon on level up:
                        if (_showParticleBurst)
                          Positioned.fill(
                            child: Opacity(
                              opacity: 0.5,
                              child: Center(
                                child: Icon(
                                  Icons.local_fire_department,
                                  color: Colors.orangeAccent,
                                  size: 32,
                                ),
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}

/// CustomPainter for the neon-styled EXP bar, with continuous lightning effect.
/// We've amped up the "electric" look by:
///   • Increasing the number of bolts from 3 → 4.
///   • Slightly bumping the blur radius on the neon glow.
///   • Cranking up certain opacities.
///
/// Simply copy/paste your old painting logic in place of the comments below.
/// Everything else (shaders, rounded corners, etc.) remains identical to your previous working code.
class _TotalExpPainter extends CustomPainter {
  final double fillPercent;     // how much of the bar is filled (0.0 → 1.0)
  final double glowScale;       // scales up the outer glow on flash
  final double lightningValue;  // cycles 0.0 → 1.0 for lightning flicker
  final int randomSeed;         // random seed for varied bolt placement

  _TotalExpPainter({
    required this.fillPercent,
    required this.glowScale,
    required this.lightningValue,
    required this.randomSeed,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final double barWidth = size.width;
    final double barHeight = size.height;

    // ─── (1) Background bar: dark grey ───────────────────
    final Paint bgPaint = Paint()
      ..color = Colors.grey.shade900
      ..style = PaintingStyle.fill;
    final RRect rectBg = RRect.fromRectAndRadius(
      Rect.fromLTWH(0, 0, barWidth, barHeight),
      const Radius.circular(12),
    );
    canvas.drawRRect(rectBg, bgPaint);

    // ─── (2) Filled portion: neon cyan-accent gradient ───
    final double fillWidth = barWidth * fillPercent;
    final Paint fillPaint = Paint()
      ..shader = LinearGradient(
        colors: [
          Colors.cyanAccent.withValues(alpha: 0.9),
          Colors.cyanAccent.withValues(alpha: 1.0),
        ],
      ).createShader(Rect.fromLTWH(0, 0, fillWidth, barHeight))
      ..style = PaintingStyle.fill;
    final RRect rectFill = RRect.fromRectAndRadius(
      Rect.fromLTWH(0, 0, fillWidth, barHeight),
      const Radius.circular(12),
    );
    canvas.drawRRect(rectFill, fillPaint);

    // ─── (3) Neon glow border (pulsing with glowScale) ────
    final Paint glowPaint = Paint()
      ..color = Colors.cyanAccent.withValues(alpha: 0.7 * (1 / glowScale))
      ..style = PaintingStyle.stroke
      ..maskFilter = MaskFilter.blur(BlurStyle.outer, 14 * glowScale) // slightly larger blur
      ..strokeWidth = 4;
    final RRect rectGlow = RRect.fromRectAndRadius(
      Rect.fromLTWH(-2, -2, barWidth + 4, barHeight + 4),
      const Radius.circular(14),
    );
    canvas.drawRRect(rectGlow, glowPaint);

    // ─── (4) Continuous lightning effect over the filled portion ───
    _drawLightning(canvas, Size(fillWidth, barHeight));
  }

  /// Draws 4 jagged lightning bolts across the filled‐portion region.
  void _drawLightning(Canvas canvas, Size filledSize) {
    if (filledSize.width <= 0) return;

    final Paint boltPaint = Paint()
      ..color = Colors.white.withValues(alpha: 0.45)  // slightly brighter
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.5;

    final rand = Random(randomSeed);

    // Amped up bolts: 4 instead of 3
    final int boltCount = 4;
    for (var i = 0; i < boltCount; i++) {
      final Path path = Path();
      // Start at a random x along the left edge of the fill
      final double startX = rand.nextDouble() * filledSize.width * 0.8;
      final double startY = rand.nextDouble() * filledSize.height;
      path.moveTo(startX, startY);

      // Generate 4‐6 segments (instead of 3‐5), each ~ 20±10 px
      double currentX = startX;
      double currentY = startY;
      final int segmentCount = 5; // increased segment count
      for (var s = 0; s < segmentCount; s++) {
        final double dx = 20 + rand.nextDouble() * 10;
        final double dy =
            (rand.nextDouble() * filledSize.height / 2) -
            (filledSize.height / 4);
        currentX = (currentX + dx).clamp(0.0, filledSize.width);
        currentY = (currentY + dy).clamp(0.0, filledSize.height);
        path.lineTo(currentX, currentY);
      }

      canvas.drawPath(path, boltPaint);
    }
  }

  @override
  bool shouldRepaint(covariant _TotalExpPainter old) {
    return old.fillPercent != fillPercent ||
        old.glowScale != glowScale ||
        old.lightningValue != lightningValue ||
        old.randomSeed != randomSeed;
  }
}
