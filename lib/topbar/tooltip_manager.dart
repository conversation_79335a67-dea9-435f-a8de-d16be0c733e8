import 'package:flutter/material.dart';
//import '../topbar/bar_helpers.dart';

class DynamicTooltip extends StatelessWidget {
  final double fillPercent;

  const DynamicTooltip({super.key, required this.fillPercent});

  @override
  Widget build(BuildContext context) {
    final percentText = "${(fillPercent * 100).toStringAsFixed(1)}% complete";
    return Tooltip(
      message: percentText,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.8),
        borderRadius: BorderRadius.circular(8),
      ),
      textStyle: const TextStyle(
        color: Colors.cyanAccent,
        fontFamily: '<PERSON>su<PERSON><PERSON>',
        fontSize: 12,
      ),
      child: const SizedBox(), // the parent will wrap this
    );
  }
}
