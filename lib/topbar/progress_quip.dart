// lib/widgets/topbar/overlays/progress_quip.dart

import 'package:flutter/material.dart';
import '../topbar/bar_helpers.dart';

/// Displays a short, motivational “quip” based on the current fill percentage.
/// Uses `progressQuips` (a List of ProgressQuipEntry) from bar_helpers.dart.
class ProgressQuip extends StatelessWidget {
  final double fillPercent; // 0.0–1.0

  const ProgressQuip({super.key, required this.fillPercent});

  @override
  Widget build(BuildContext context) {
    // Default message if no matching range is found
    String quip = "Keep going!";

    // Iterate through each range‐message entry
    for (final entry in progressQuips) {
      if (fillPercent >= entry.start && fillPercent < entry.end) {
        quip = entry.message;
        break;
      }
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.7),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        quip,
        style: const TextStyle(
          color: Colors.cyanAccent,
          fontFamily: 'Bitsumishi',
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }
}
