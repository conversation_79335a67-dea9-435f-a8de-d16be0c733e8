//import 'dart:math';
import 'package:flutter/material.dart';
//import 'package:maxed_out_life/utils/category_utils.dart';

class SegmentedFillPainter extends CustomPainter {
  final Map<String, int> expByCategory;
  final int totalExp;
  final Color Function(String) categoryColorResolver;

  SegmentedFillPainter({
    required this.expByCategory,
    required this.totalExp,
    required this.categoryColorResolver,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final double barHeight = size.height;
    final double barWidth = size.width;
    if (totalExp == 0) return;

    double startX = 0;
    expByCategory.forEach((category, value) {
      final double pct = value / totalExp;
      final double segmentWidth = pct * barWidth;
      final Rect rect = Rect.fromLTWH(startX, 0, segmentWidth, barHeight);
      final Paint paint = Paint()
        ..color = categoryColorResolver(category)
        ..style = PaintingStyle.fill;
      canvas.drawRRect(
        RRect.fromRectAndRadius(rect, Radius.circular(barHeight / 2)),
        paint,
      );
      startX += segmentWidth;
    });
  }

  @override
  bool shouldRepaint(covariant SegmentedFillPainter old) {
    return old.expByCategory != expByCategory ||
           old.totalExp != totalExp;
  }
}
