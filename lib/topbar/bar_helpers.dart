// lib/widgets/topbar/utils/bar_helpers.dart

import 'dart:math';
//import 'package:flutter/material.dart';

/// A simple data class representing a “range + message” pair.
/// This replaces using RangeValues as a Map key (which isn’t allowed as a const Map key).
class ProgressQuipEntry {
  final double start;
  final double end;
  final String message;

  const ProgressQuipEntry(this.start, this.end, this.message);
}

/// 1) “Cool” quips keyed by start/end percent ranges.
///    Because Dart does not allow RangeValues (or any non-primitive) as const Map keys,
///    we instead use a const List of ProgressQuipEntry.
const List<ProgressQuipEntry> progressQuips = [
  ProgressQuipEntry(0.0, 0.1, "Getting warmed up, champ!"),
  ProgressQuipEntry(0.1, 0.2, "You’re on fire—20% done!"),
  ProgressQuipEntry(0.2, 0.3, "Almost 30%? Keep crushing it!"),
  ProgressQuipEntry(0.3, 0.4, "40% is no joke—stay focused!"),
  ProgressQuipEntry(0.4, 0.5, "Halfway there! Pumped up?"),
  ProgressQuipEntry(0.5, 0.6, "60%—you’re dominating today!"),
  ProgressQuipEntry(0.6, 0.7, "70%—the finish line’s in sight!"),
  ProgressQuipEntry(0.7, 0.8, "80%—legends never quit!"),
  ProgressQuipEntry(0.8, 0.9, "90%—closing in on greatness!"),
  ProgressQuipEntry(0.9, 1.0, "100%—you just crushed your goal!"),
];

/// 2) Convert a list of “last 7 days’ raw XP” into 0.0–1.0 percentages,
///    suitable for a sparkline. We look up the maximum value in `history`,
///    then divide each element by that maximum. If all days were zero,
///    we return zeros to avoid division by zero.
///
/// NOTE: We import `dart:math` above to get the `max(...)` function.
List<double> wpHistoryToPercents(List<int> history) {
  if (history.isEmpty) return <double>[];

  // Find the largest day’s value:
  final int largest = history.fold<int>(0, (acc, element) => max(acc, element));

  if (largest == 0) {
    // If no day had any XP, return all zeroes:
    return history.map((_) => 0.0).toList();
  }

  // Otherwise, convert each “raw XP” to a fraction of `largest`.
  return history.map((xp) => xp / largest).toList();
}

/// 3) Milestone thresholds (25%, 50%, 75%)
///    We’ll use these in the MilestoneNotchesPainter.
const List<double> milestonePercs = [0.25, 0.50, 0.75];
