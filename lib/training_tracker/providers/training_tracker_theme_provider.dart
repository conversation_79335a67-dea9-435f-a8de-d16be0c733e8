// lib/training_tracker/providers/training_tracker_theme_provider.dart

import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../training_tracker_config.dart';

/// Training Tracker Theme Provider
/// Manages theme state specifically for Training Tracker features
class TrainingTrackerThemeProvider extends ChangeNotifier {
  static const String _themeKey = 'training_tracker_theme';
  String _currentTheme = 'classic';
  late SharedPreferences _prefs;
  bool _isInitialized = false;

  TrainingTrackerThemeProvider() {
    _loadTheme();
  }

  String get currentTheme => _currentTheme;
  bool get isInitialized => _isInitialized;

  /// Get the current theme data
  Map<String, dynamic>? get currentThemeData {
    return TrainingTrackerThemes.getTheme(_currentTheme);
  }

  /// Get the primary color for the current theme
  Color get primaryColor {
    final theme = currentThemeData;
    if (theme == null) return const Color(0xFF00FFFF);
    return Color(theme['primary'] as int);
  }

  /// Get the secondary color for the current theme
  Color get secondaryColor {
    final theme = currentThemeData;
    if (theme == null) return const Color(0xFF0080FF);
    return Color(theme['secondary'] as int);
  }

  /// Get the accent color for the current theme
  Color get accentColor {
    final theme = currentThemeData;
    if (theme == null) return const Color(0xFFFF0080);
    return Color(theme['accent'] as int);
  }

  /// Get the background color for the current theme
  Color get backgroundColor {
    final theme = currentThemeData;
    if (theme == null) return const Color(0xFF000000);
    return Color(theme['background'] as int);
  }

  /// Get the theme name for display
  String get currentThemeName {
    final theme = currentThemeData;
    if (theme == null) return 'Classic Neon';
    return theme['name'] as String;
  }

  /// Load theme from SharedPreferences
  Future<void> _loadTheme() async {
    try {
      _prefs = await SharedPreferences.getInstance();
      _currentTheme = _prefs.getString(_themeKey) ?? 'classic';
      
      // Validate theme exists
      if (!TrainingTrackerThemes.availableThemes.contains(_currentTheme)) {
        _currentTheme = 'classic';
        await _saveTheme();
      }
      
      _isInitialized = true;
      notifyListeners();
    } catch (e) {
      // Fallback to default theme
      _currentTheme = 'classic';
      _isInitialized = true;
      notifyListeners();
    }
  }

  /// Save theme to SharedPreferences
  Future<void> _saveTheme() async {
    try {
      await _prefs.setString(_themeKey, _currentTheme);
    } catch (e) {
      // Silently fail - theme will revert to default on next load
    }
  }

  /// Change the current theme
  Future<void> changeTheme(String themeName) async {
    if (!TrainingTrackerThemes.availableThemes.contains(themeName)) {
      return; // Invalid theme name
    }

    if (_currentTheme != themeName) {
      _currentTheme = themeName;
      await _saveTheme();
      notifyListeners();
    }
  }

  /// Get all available themes
  List<String> get availableThemes => TrainingTrackerThemes.availableThemes;

  /// Get theme data for a specific theme
  Map<String, dynamic>? getThemeData(String themeName) {
    return TrainingTrackerThemes.getTheme(themeName);
  }

  /// Reset to default theme
  Future<void> resetToDefault() async {
    await changeTheme('classic');
  }
}
