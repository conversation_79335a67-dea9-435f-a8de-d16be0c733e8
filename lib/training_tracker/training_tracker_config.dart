// lib/training_tracker/training_tracker_config.dart

/// Configuration and feature flags for Training Tracker system
/// Allows easy enabling/disabling of features for deployment control
class TrainingTrackerConfig {
  // Core Features (always enabled)
  static const bool coreTrainingTracker = true;
  
  // Analytics Features
  static const bool analyticsEnabled = true;
  static const bool performanceMetricsEnabled = true;
  static const bool advancedChartsEnabled = true;
  
  // Calendar & Planning Features
  static const bool calendarIntegrationEnabled = true;
  static const bool dragDropSchedulingEnabled = true;
  static const bool restDayPlanningEnabled = true;
  
  // Template & Preset Features
  static const bool workoutTemplatesEnabled = true;
  static const bool customBuilderEnabled = true;
  static const bool templateSharingEnabled = true;
  
  // Advanced Timer Features
  static const bool multiPhaseTimerEnabled = true;
  static const bool intervalTrainingEnabled = true;
  static const bool audioCuesEnabled = true;
  
  // Goal & Achievement Features
  static const bool smartGoalsEnabled = true;
  static const bool achievementTrackingEnabled = true;
  static const bool milestoneSystemEnabled = true;
  
  // Comparison & Analysis Features
  static const bool enhancedComparisonEnabled = true;
  static const bool trendAnalysisEnabled = true;
  static const bool regressionDetectionEnabled = true;
  
  // UI & Customization Features
  static const bool customizableInterfaceEnabled = true;
  static const bool themeVariationsEnabled = true;
  static const bool advancedFeaturesToggleEnabled = true;
  
  // Export & Backup Features
  static const bool exportFeaturesEnabled = true;
  static const bool pdfReportsEnabled = true;
  static const bool csvExportEnabled = true;
  static const bool fitnessAppIntegrationEnabled = false; // Coming Soon
  
  // Competition & Social Features
  static const bool competitionFeaturesEnabled = true;
  static const bool personalBestsEnabled = true;
  static const bool challengeModesEnabled = true;
  static const bool teamChallengesEnabled = true;
  static const bool userCreatedChallengesEnabled = true;
  
  // Offline & Sync Features
  static const bool offlineCapabilitiesEnabled = true;
  static const bool cloudSyncEnabled = true;
  
  // Debug & Development Features
  static const bool debugModeEnabled = false;
  static const bool performanceMonitoringEnabled = false;
  
  /// Check if a feature is enabled
  static bool isFeatureEnabled(String featureName) {
    switch (featureName) {
      case 'analytics': return analyticsEnabled;
      case 'performance_metrics': return performanceMetricsEnabled;
      case 'calendar_integration': return calendarIntegrationEnabled;
      case 'workout_templates': return workoutTemplatesEnabled;
      case 'multi_phase_timer': return multiPhaseTimerEnabled;
      case 'smart_goals': return smartGoalsEnabled;
      case 'enhanced_comparison': return enhancedComparisonEnabled;
      case 'customizable_interface': return customizableInterfaceEnabled;
      case 'export_features': return exportFeaturesEnabled;
      case 'competition_features': return competitionFeaturesEnabled;
      case 'offline_capabilities': return offlineCapabilitiesEnabled;
      default: return false;
    }
  }
  
  /// Get list of enabled features
  static List<String> getEnabledFeatures() {
    final features = <String>[];
    
    if (analyticsEnabled) features.add('Analytics Dashboard');
    if (performanceMetricsEnabled) features.add('Performance Metrics');
    if (calendarIntegrationEnabled) features.add('Calendar Integration');
    if (workoutTemplatesEnabled) features.add('Workout Templates');
    if (multiPhaseTimerEnabled) features.add('Multi-Phase Timer');
    if (smartGoalsEnabled) features.add('Smart Goals');
    if (enhancedComparisonEnabled) features.add('Enhanced Comparison');
    if (customizableInterfaceEnabled) features.add('Customizable Interface');
    if (exportFeaturesEnabled) features.add('Export & Backup');
    if (competitionFeaturesEnabled) features.add('Competition Features');
    if (offlineCapabilitiesEnabled) features.add('Offline Capabilities');
    
    return features;
  }
  
  /// Get feature status for debugging
  static Map<String, bool> getFeatureStatus() {
    return {
      'Core Training Tracker': coreTrainingTracker,
      'Analytics': analyticsEnabled,
      'Performance Metrics': performanceMetricsEnabled,
      'Calendar Integration': calendarIntegrationEnabled,
      'Workout Templates': workoutTemplatesEnabled,
      'Multi-Phase Timer': multiPhaseTimerEnabled,
      'Smart Goals': smartGoalsEnabled,
      'Enhanced Comparison': enhancedComparisonEnabled,
      'Customizable Interface': customizableInterfaceEnabled,
      'Export Features': exportFeaturesEnabled,
      'Competition Features': competitionFeaturesEnabled,
      'Offline Capabilities': offlineCapabilitiesEnabled,
      'Fitness App Integration': fitnessAppIntegrationEnabled,
    };
  }
}

/// Training Tracker theme configuration
class TrainingTrackerThemes {
  static const Map<String, Map<String, dynamic>> themes = {
    'classic': {
      'name': 'Classic Neon',
      'primary': 0xFF00FFFF,
      'secondary': 0xFF0080FF,
      'accent': 0xFFFF0080,
      'background': 0xFF000000,
    },
    'fire': {
      'name': 'Fire Storm',
      'primary': 0xFFFF4500,
      'secondary': 0xFFFF6347,
      'accent': 0xFFFFD700,
      'background': 0xFF1A0000,
    },
    'ice': {
      'name': 'Ice Crystal',
      'primary': 0xFF00BFFF,
      'secondary': 0xFF87CEEB,
      'accent': 0xFFE0FFFF,
      'background': 0xFF000A1A,
    },
    'nature': {
      'name': 'Bio Pulse',
      'primary': 0xFF00FF00,
      'secondary': 0xFF32CD32,
      'accent': 0xFF90EE90,
      'background': 0xFF001A00,
    },
    'royal': {
      'name': 'Royal Purple',
      'primary': 0xFF8A2BE2,
      'secondary': 0xFF9370DB,
      'accent': 0xFFDDA0DD,
      'background': 0xFF0A001A,
    },
    'sunset': {
      'name': 'Sunset Blaze',
      'primary': 0xFFFF6B35,
      'secondary': 0xFFFF8E53,
      'accent': 0xFFFFB347,
      'background': 0xFF1A0A00,
    },
    'ocean': {
      'name': 'Deep Ocean',
      'primary': 0xFF006994,
      'secondary': 0xFF0582CA,
      'accent': 0xFF00A8E8,
      'background': 0xFF000D1A,
    },
    'galaxy': {
      'name': 'Galaxy Storm',
      'primary': 0xFF9D4EDD,
      'secondary': 0xFFC77DFF,
      'accent': 0xFFE0AAFF,
      'background': 0xFF0F001A,
    },
    'matrix': {
      'name': 'Matrix Code',
      'primary': 0xFF39FF14,
      'secondary': 0xFF7FFF00,
      'accent': 0xFFADFF2F,
      'background': 0xFF001A00,
    },
  };
  
  static List<String> get availableThemes => themes.keys.toList();
  
  static Map<String, dynamic>? getTheme(String themeName) {
    return themes[themeName];
  }
}
