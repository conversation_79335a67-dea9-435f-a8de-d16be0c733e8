// lib/training_tracker/training_tracker_enhanced.dart

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'training_tracker_config.dart';
import 'widgets/training_analytics_dashboard.dart';
import 'widgets/performance_metrics_widget.dart';
import 'widgets/training_calendar_widget.dart';
import 'widgets/workout_templates_widget.dart';
import '../models/training_session_model.dart';
import '../services/comprehensive_logging_service.dart';
import 'providers/training_tracker_theme_provider.dart';

/// Enhanced Training Tracker with advanced features
class EnhancedTrainingTracker extends StatefulWidget {
  final List<TrainingSession> sessions;
  final Color glowColor;
  final VoidCallback onClose;

  const EnhancedTrainingTracker({
    super.key,
    required this.sessions,
    required this.glowColor,
    required this.onClose,
  });

  @override
  State<EnhancedTrainingTracker> createState() => _EnhancedTrainingTrackerState();
}

class _EnhancedTrainingTrackerState extends State<EnhancedTrainingTracker> {
  bool _showAdvancedFeatures = false;

  @override
  void initState() {
    super.initState();
    _logFeatureAccess();
  }

  Future<void> _logFeatureAccess() async {
    await ComprehensiveLoggingService.logInfo('🚀 Enhanced Training Tracker: Opened');
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.black.withValues(alpha: 0.95),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.95,
        height: MediaQuery.of(context).size.height * 0.9,
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: widget.glowColor.withValues(alpha: 0.3), width: 2),
          boxShadow: [
            BoxShadow(
              color: widget.glowColor.withValues(alpha: 0.2),
              blurRadius: 20,
              spreadRadius: 4,
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildHeader(),
            const SizedBox(height: 20),
            _buildCoreFeatures(),
            if (_showAdvancedFeatures) ...[
              const SizedBox(height: 20),
              _buildAdvancedFeatures(),
            ],
            const SizedBox(height: 20),
            _buildAdvancedToggle(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Icon(Icons.rocket_launch, color: widget.glowColor, size: 24),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'ENHANCED\nTRAINING',
                style: TextStyle(
                  color: widget.glowColor,
                  fontSize: 14,
                  fontFamily: 'Pirulen',
                  fontWeight: FontWeight.bold,
                  height: 1.1,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              Text(
                'Advanced Features & Analytics',
                style: TextStyle(
                  color: widget.glowColor.withValues(alpha: 0.7),
                  fontSize: 10,
                  fontFamily: 'Bitsumishi',
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
        const Spacer(),
        IconButton(
          onPressed: widget.onClose,
          icon: Icon(Icons.close, color: widget.glowColor),
        ),
      ],
    );
  }

  Widget _buildCoreFeatures() {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 4),
        child: GridView.count(
          crossAxisCount: 2,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          childAspectRatio: 1.3,
          physics: const BouncingScrollPhysics(),
          children: [
            _buildFeatureCard(
              'ANALYTICS\nDASHBOARD',
              'Comprehensive training insights',
              Icons.analytics,
              TrainingTrackerConfig.analyticsEnabled,
              () => _openAnalyticsDashboard(),
            ),
            _buildFeatureCard(
              'PERFORMANCE\nMETRICS',
              'Track your progress & streaks',
              Icons.trending_up,
              TrainingTrackerConfig.performanceMetricsEnabled,
              () => _openPerformanceMetrics(),
            ),
            _buildFeatureCard(
              'TRAINING\nCALENDAR',
              'Plan & schedule workouts',
              Icons.calendar_month,
              TrainingTrackerConfig.calendarIntegrationEnabled,
              () => _openTrainingCalendar(),
            ),
            _buildFeatureCard(
              'WORKOUT\nTEMPLATES',
              'Pre-built & custom workouts',
              Icons.fitness_center,
              TrainingTrackerConfig.workoutTemplatesEnabled,
              () => _openWorkoutTemplates(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAdvancedFeatures() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.4),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: widget.glowColor.withValues(alpha: 0.3), width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'ADVANCED FEATURES',
            style: TextStyle(
              color: widget.glowColor,
              fontSize: 14,
              fontFamily: 'Pirulen',
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 3,
            crossAxisSpacing: 8,
            mainAxisSpacing: 8,
            childAspectRatio: 1.6,
            children: [
              _buildAdvancedFeatureCard(
                'MULTI-PHASE\nTIMER',
                Icons.timer,
                TrainingTrackerConfig.multiPhaseTimerEnabled,
                () => _openMultiPhaseTimer(),
              ),
              _buildAdvancedFeatureCard(
                'SMART\nGOALS',
                Icons.flag,
                TrainingTrackerConfig.smartGoalsEnabled,
                () => _openSmartGoals(),
              ),
              _buildAdvancedFeatureCard(
                'ENHANCED\nCOMPARE',
                Icons.compare_arrows,
                TrainingTrackerConfig.enhancedComparisonEnabled,
                () => _openEnhancedComparison(),
              ),
              _buildAdvancedFeatureCard(
                'EXPORT &\nBACKUP',
                Icons.download,
                TrainingTrackerConfig.exportFeaturesEnabled,
                () => _openExportFeatures(),
              ),
              _buildAdvancedFeatureCard(
                'COMPETITION',
                Icons.emoji_events,
                TrainingTrackerConfig.competitionFeaturesEnabled,
                () => _openCompetitionFeatures(),
              ),
              _buildAdvancedFeatureCard(
                'THEMES',
                Icons.palette,
                TrainingTrackerConfig.customizableInterfaceEnabled,
                () => _openThemeCustomization(),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFeatureCard(
    String title,
    String description,
    IconData icon,
    bool isEnabled,
    VoidCallback onTap,
  ) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: isEnabled ? onTap : null,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: isEnabled
                ? Colors.black.withValues(alpha: 0.4)
                : Colors.black.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: isEnabled
                  ? widget.glowColor.withValues(alpha: 0.3)
                  : Colors.white.withValues(alpha: 0.1),
              width: 1,
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                color: isEnabled ? widget.glowColor : Colors.white30,
                size: 28,
              ),
              const SizedBox(height: 8),
              Text(
                title,
                style: TextStyle(
                  color: isEnabled ? widget.glowColor : Colors.white30,
                  fontSize: 10,
                  fontFamily: 'Pirulen',
                  fontWeight: FontWeight.bold,
                  height: 1.1,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 2),
              Text(
                isEnabled ? description : 'Coming Soon',
                style: TextStyle(
                  color: isEnabled ? Colors.white70 : Colors.white30,
                  fontSize: 8,
                  fontFamily: 'Bitsumishi',
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAdvancedFeatureCard(
    String title,
    IconData icon,
    bool isEnabled,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: isEnabled ? onTap : null,
      child: Container(
        padding: const EdgeInsets.all(6),
        decoration: BoxDecoration(
          color: isEnabled
              ? Colors.black.withValues(alpha: 0.6)
              : Colors.black.withValues(alpha: 0.3),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isEnabled
                ? widget.glowColor.withValues(alpha: 0.4)
                : Colors.white.withValues(alpha: 0.1),
            width: 1,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              color: isEnabled ? widget.glowColor : Colors.white30,
              size: 18,
            ),
            const SizedBox(height: 3),
            Flexible(
              child: Text(
                title,
                style: TextStyle(
                  color: isEnabled ? widget.glowColor : Colors.white30,
                  fontSize: 7,
                  fontFamily: 'Pirulen',
                  fontWeight: FontWeight.bold,
                  height: 1.0,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAdvancedToggle() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: () {
          setState(() {
            _showAdvancedFeatures = !_showAdvancedFeatures;
          });
        },
        icon: Icon(
          _showAdvancedFeatures ? Icons.expand_less : Icons.expand_more,
          color: widget.glowColor,
        ),
        label: Text(
          _showAdvancedFeatures ? 'HIDE ADVANCED FEATURES' : 'SHOW ADVANCED FEATURES',
          style: TextStyle(
            fontFamily: 'Pirulen',
            fontSize: 12,
            color: widget.glowColor,
          ),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: widget.glowColor.withValues(alpha: 0.1),
          foregroundColor: widget.glowColor,
          side: BorderSide(color: widget.glowColor.withValues(alpha: 0.3), width: 1),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          padding: const EdgeInsets.symmetric(vertical: 16),
        ),
      ),
    );
  }

  // Feature opening methods
  void _openAnalyticsDashboard() {
    showDialog(
      context: context,
      builder: (context) => TrainingAnalyticsDashboard(
        sessions: widget.sessions,
        glowColor: widget.glowColor,
        onClose: () => Navigator.of(context).pop(),
      ),
    );
  }

  void _openPerformanceMetrics() {
    showDialog(
      context: context,
      builder: (context) => PerformanceMetricsWidget(
        sessions: widget.sessions,
        glowColor: widget.glowColor,
        onClose: () => Navigator.of(context).pop(),
      ),
    );
  }

  void _openTrainingCalendar() async {
    try {
      await ComprehensiveLoggingService.logInfo('📅 Opening Training Calendar');

      if (!mounted) return;

      showDialog(
        context: context,
        barrierDismissible: true,
        builder: (dialogContext) => ChangeNotifierProvider(
          create: (context) => TrainingTrackerThemeProvider(),
          child: Builder(
            builder: (providerContext) {
              return TrainingCalendarWidget(
                glowColor: widget.glowColor,
                onClose: () {
                  Navigator.of(dialogContext).pop();
                  ComprehensiveLoggingService.logInfo('📅 Training Calendar closed');
                },
                onSessionPlanned: (session) async {
                  await ComprehensiveLoggingService.logInfo('📅 Session planned: ${session.workoutType}');
                },
                onRestDayPlanned: (restDay) async {
                  await ComprehensiveLoggingService.logInfo('📅 Rest day planned: ${restDay.type}');
                },
              );
            },
          ),
        ),
      );
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Failed to open Training Calendar: $e');
      if (mounted) {
        _showErrorDialog('Training Calendar Error', 'Failed to open calendar: $e');
      }
    }
  }

  void _openWorkoutTemplates() {
    showDialog(
      context: context,
      builder: (context) => WorkoutTemplatesWidget(
        glowColor: widget.glowColor,
        onClose: () => Navigator.of(context).pop(),
      ),
    );
  }

  void _openMultiPhaseTimer() {
    _showComingSoon('Multi-Phase Timer');
  }

  void _openSmartGoals() {
    _showComingSoon('Smart Goals');
  }

  void _openEnhancedComparison() {
    _showComingSoon('Enhanced Comparison');
  }

  void _openExportFeatures() {
    _showComingSoon('Export & Backup');
  }

  void _openCompetitionFeatures() {
    _showComingSoon('Competition Features');
  }

  void _openThemeCustomization() {
    showDialog(
      context: context,
      builder: (context) => ChangeNotifierProvider(
        create: (context) => TrainingTrackerThemeProvider(),
        child: _ThemeCustomizationDialog(
          glowColor: widget.glowColor,
          onThemeChanged: (themeName) async {
            // Store context before async call
            final navigator = Navigator.of(context);
            final scaffoldMessenger = ScaffoldMessenger.of(context);

            // Apply theme change immediately
            final themeProvider = Provider.of<TrainingTrackerThemeProvider>(context, listen: false);
            await themeProvider.changeTheme(themeName);

            if (mounted) {
              navigator.pop();

              // Refresh the entire Enhanced Training Tracker
              setState(() {});

              scaffoldMessenger.showSnackBar(
                SnackBar(
                  content: Text('Theme "${themeProvider.currentThemeName}" applied!'),
                  backgroundColor: themeProvider.primaryColor.withValues(alpha: 0.8),
                ),
              );
            }
          },
        ),
      ),
    );
  }

  void _showComingSoon(String featureName) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.black.withValues(alpha: 0.9),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        title: Text(
          featureName,
          style: TextStyle(
            color: widget.glowColor,
            fontFamily: 'Pirulen',
            fontSize: 16,
          ),
        ),
        content: Text(
          'This feature is coming soon! We\'re working hard to bring you the most advanced training experience.',
          style: const TextStyle(
            color: Colors.white,
            fontFamily: 'Bitsumishi',
            fontSize: 14,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'Got it!',
              style: TextStyle(
                color: widget.glowColor,
                fontFamily: 'Pirulen',
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showErrorDialog(String title, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.black.withValues(alpha: 0.9),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        title: Text(
          title,
          style: TextStyle(
            color: Colors.red,
            fontFamily: 'Pirulen',
            fontSize: 16,
          ),
        ),
        content: Text(
          message,
          style: const TextStyle(
            color: Colors.white,
            fontFamily: 'Bitsumishi',
            fontSize: 14,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'OK',
              style: TextStyle(
                color: widget.glowColor,
                fontFamily: 'Pirulen',
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _ThemeCustomizationDialog extends StatelessWidget {
  final Color glowColor;
  final Function(String) onThemeChanged;

  const _ThemeCustomizationDialog({
    required this.glowColor,
    required this.onThemeChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.black.withValues(alpha: 0.95),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.7,
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: glowColor.withValues(alpha: 0.3), width: 2),
          boxShadow: [
            BoxShadow(
              color: glowColor.withValues(alpha: 0.2),
              blurRadius: 20,
              spreadRadius: 4,
            ),
          ],
        ),
        child: Column(
          children: [
            _buildHeader(context),
            const SizedBox(height: 20),
            Expanded(child: _buildThemeGrid()),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 4),
      child: Row(
        children: [
          Icon(Icons.palette, color: glowColor, size: 20),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'THEME',
                  style: TextStyle(
                    color: glowColor,
                    fontSize: 16,
                    fontFamily: 'Pirulen',
                    fontWeight: FontWeight.bold,
                    height: 1.0,
                  ),
                ),
                Text(
                  'CUSTOMIZATION',
                  style: TextStyle(
                    color: glowColor,
                    fontSize: 16,
                    fontFamily: 'Pirulen',
                    fontWeight: FontWeight.bold,
                    height: 1.0,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: Icon(Icons.close, color: glowColor, size: 20),
            constraints: const BoxConstraints(minWidth: 40, minHeight: 40),
          ),
        ],
      ),
    );
  }

  Widget _buildThemeGrid() {
    return Consumer<TrainingTrackerThemeProvider>(
      builder: (context, themeProvider, child) {
        if (!themeProvider.isInitialized) {
          return Center(
            child: CircularProgressIndicator(color: glowColor),
          );
        }

        return GridView.builder(
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            childAspectRatio: 1.2,
          ),
          itemCount: TrainingTrackerThemes.availableThemes.length,
          itemBuilder: (context, index) {
            final themeName = TrainingTrackerThemes.availableThemes[index];
            final theme = TrainingTrackerThemes.getTheme(themeName)!;
            final isSelected = themeProvider.currentTheme == themeName;
            return _buildThemeCard(themeName, theme, isSelected);
          },
        );
      },
    );
  }

  Widget _buildThemeCard(String themeName, Map<String, dynamic> theme, bool isSelected) {
    final primaryColor = Color(theme['primary'] as int);
    final secondaryColor = Color(theme['secondary'] as int);
    final accentColor = Color(theme['accent'] as int);

    return GestureDetector(
      onTap: () => onThemeChanged(themeName),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isSelected ? primaryColor.withValues(alpha: 0.1) : Colors.black.withValues(alpha: 0.4),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? primaryColor : primaryColor.withValues(alpha: 0.3),
            width: isSelected ? 3 : 1,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            // Selection indicator
            if (isSelected)
              Icon(
                Icons.check_circle,
                color: primaryColor,
                size: 16,
              ),
            if (isSelected) const SizedBox(height: 4),
            // Color preview
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildColorDot(primaryColor),
                _buildColorDot(secondaryColor),
                _buildColorDot(accentColor),
              ],
            ),
            const SizedBox(height: 8),
            Flexible(
              child: Text(
                theme['name'] as String,
                style: TextStyle(
                  color: primaryColor,
                  fontSize: 11,
                  fontFamily: 'Pirulen',
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            const SizedBox(height: 2),
            Flexible(
              child: Text(
                themeName.toUpperCase(),
                style: TextStyle(
                  color: Colors.white70,
                  fontSize: 9,
                  fontFamily: 'Bitsumishi',
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildColorDot(Color color) {
    return Container(
      width: 12,
      height: 12,
      decoration: BoxDecoration(
        color: color,
        shape: BoxShape.circle,
        boxShadow: [
          BoxShadow(
            color: color.withValues(alpha: 0.5),
            blurRadius: 3,
            spreadRadius: 0.5,
          ),
        ],
      ),
    );
  }
}
