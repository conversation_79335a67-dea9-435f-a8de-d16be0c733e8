// lib/training_tracker/models/workout_template_model.dart

import 'package:flutter/foundation.dart';
import 'package:uuid/uuid.dart';

/// Workout template for pre-built and custom workouts
@immutable
class WorkoutTemplate {
  final String id;
  final String name;
  final String description;
  final String category;
  final String notes;
  final List<String> exercises;
  final int estimatedDuration; // in minutes
  final String difficulty; // Beginner, Intermediate, Advanced
  final List<String> tags;
  final bool isPrebuilt;
  final DateTime createdAt;
  final DateTime? lastUsed;
  final int usageCount;

  const WorkoutTemplate({
    required this.id,
    required this.name,
    required this.description,
    required this.category,
    required this.notes,
    required this.exercises,
    required this.estimatedDuration,
    required this.difficulty,
    required this.tags,
    required this.isPrebuilt,
    required this.createdAt,
    this.lastUsed,
    required this.usageCount,
  });

  /// Create a new custom template
  factory WorkoutTemplate.create({
    required String name,
    required String description,
    required String category,
    required String notes,
    required List<String> exercises,
    required int estimatedDuration,
    required String difficulty,
    List<String> tags = const [],
  }) {
    return WorkoutTemplate(
      id: const Uuid().v4(),
      name: name,
      description: description,
      category: category,
      notes: notes,
      exercises: exercises,
      estimatedDuration: estimatedDuration,
      difficulty: difficulty,
      tags: tags,
      isPrebuilt: false,
      createdAt: DateTime.now(),
      usageCount: 0,
    );
  }

  /// Pre-built strength template
  factory WorkoutTemplate.strength() {
    return WorkoutTemplate(
      id: 'prebuilt_strength',
      name: 'Power Core Strength',
      description: 'Full-body strength training with compound movements',
      category: 'Strength',
      notes: '''Warm-up: 5-10 minutes light cardio
Main workout: Focus on proper form over speed
Cool-down: 5-10 minutes stretching

Rest 60-90 seconds between sets
Adjust weight as needed''',
      exercises: [
        'Squats - 3 sets x 8-12 reps',
        'Deadlifts - 3 sets x 6-10 reps',
        'Bench Press - 3 sets x 8-12 reps',
        'Pull-ups/Rows - 3 sets x 6-12 reps',
        'Overhead Press - 3 sets x 8-12 reps',
        'Plank - 3 sets x 30-60 seconds',
      ],
      estimatedDuration: 45,
      difficulty: 'Intermediate',
      tags: ['strength', 'compound', 'full-body', 'muscle-building'],
      isPrebuilt: true,
      createdAt: DateTime.now(),
      usageCount: 0,
    );
  }

  /// Pre-built cardio template
  factory WorkoutTemplate.cardio() {
    return WorkoutTemplate(
      id: 'prebuilt_cardio',
      name: 'Neon Cardio Blast',
      description: 'High-intensity cardio for maximum calorie burn',
      category: 'Cardio',
      notes: '''Warm-up: 5 minutes easy pace
Main workout: Push your limits but listen to your body
Cool-down: 5 minutes walking + stretching

Stay hydrated throughout
Monitor heart rate if possible''',
      exercises: [
        'Jumping Jacks - 3 sets x 30 seconds',
        'Burpees - 3 sets x 10-15 reps',
        'Mountain Climbers - 3 sets x 30 seconds',
        'High Knees - 3 sets x 30 seconds',
        'Sprint Intervals - 5 x 30 seconds',
        'Cool-down Walk - 5 minutes',
      ],
      estimatedDuration: 30,
      difficulty: 'Intermediate',
      tags: ['cardio', 'hiit', 'fat-burn', 'endurance'],
      isPrebuilt: true,
      createdAt: DateTime.now(),
      usageCount: 0,
    );
  }

  /// Pre-built flexibility template
  factory WorkoutTemplate.flexibility() {
    return WorkoutTemplate(
      id: 'prebuilt_flexibility',
      name: 'Zen Flow Flexibility',
      description: 'Gentle stretching and mobility work',
      category: 'Flexibility',
      notes: '''Focus on breathing deeply throughout
Hold each stretch for 30-60 seconds
Never force a stretch - go to comfortable edge
Perfect for recovery days or evening routine

Listen to your body and modify as needed''',
      exercises: [
        'Cat-Cow Stretch - 10 reps',
        'Downward Dog - 60 seconds',
        'Hip Flexor Stretch - 30 seconds each side',
        'Hamstring Stretch - 30 seconds each leg',
        'Shoulder Rolls - 10 forward, 10 backward',
        'Spinal Twist - 30 seconds each side',
        'Child\'s Pose - 60 seconds',
      ],
      estimatedDuration: 20,
      difficulty: 'Beginner',
      tags: ['flexibility', 'mobility', 'recovery', 'relaxation'],
      isPrebuilt: true,
      createdAt: DateTime.now(),
      usageCount: 0,
    );
  }

  /// Pre-built HIIT template
  factory WorkoutTemplate.hiit() {
    return WorkoutTemplate(
      id: 'prebuilt_hiit',
      name: 'Lightning HIIT',
      description: 'Quick and intense interval training',
      category: 'HIIT',
      notes: '''Work hard during intervals, rest completely during breaks
20 seconds work, 10 seconds rest format
Complete 4 rounds of each exercise

Modify exercises as needed for your fitness level
Stay hydrated and take breaks if needed''',
      exercises: [
        'Squat Jumps - 4 rounds x 20 seconds',
        'Push-ups - 4 rounds x 20 seconds',
        'Plank Jacks - 4 rounds x 20 seconds',
        'Lunges - 4 rounds x 20 seconds',
        'Bicycle Crunches - 4 rounds x 20 seconds',
      ],
      estimatedDuration: 15,
      difficulty: 'Advanced',
      tags: ['hiit', 'intense', 'quick', 'fat-burn'],
      isPrebuilt: true,
      createdAt: DateTime.now(),
      usageCount: 0,
    );
  }

  /// Pre-built yoga template
  factory WorkoutTemplate.yoga() {
    return WorkoutTemplate(
      id: 'prebuilt_yoga',
      name: 'Cyber Zen Yoga',
      description: 'Mindful movement and breath work',
      category: 'Yoga',
      notes: '''Focus on connecting breath with movement
Move slowly and mindfully
Hold poses for 5-8 breaths unless noted
Perfect for stress relief and flexibility

Create a peaceful environment
Use props if needed''',
      exercises: [
        'Sun Salutation A - 3 rounds',
        'Warrior I - 5 breaths each side',
        'Warrior II - 5 breaths each side',
        'Triangle Pose - 5 breaths each side',
        'Tree Pose - 5 breaths each side',
        'Seated Forward Fold - 8 breaths',
        'Savasana - 5 minutes',
      ],
      estimatedDuration: 35,
      difficulty: 'Beginner',
      tags: ['yoga', 'mindfulness', 'flexibility', 'stress-relief'],
      isPrebuilt: true,
      createdAt: DateTime.now(),
      usageCount: 0,
    );
  }

  /// Get all pre-built templates
  static List<WorkoutTemplate> getPrebuiltTemplates() {
    return [
      WorkoutTemplate.strength(),
      WorkoutTemplate.cardio(),
      WorkoutTemplate.flexibility(),
      WorkoutTemplate.hiit(),
      WorkoutTemplate.yoga(),
    ];
  }

  /// Copy template with usage tracking
  WorkoutTemplate copyWithUsage() {
    return WorkoutTemplate(
      id: id,
      name: name,
      description: description,
      category: category,
      notes: notes,
      exercises: exercises,
      estimatedDuration: estimatedDuration,
      difficulty: difficulty,
      tags: tags,
      isPrebuilt: isPrebuilt,
      createdAt: createdAt,
      lastUsed: DateTime.now(),
      usageCount: usageCount + 1,
    );
  }

  /// Get formatted duration string
  String get formattedDuration {
    if (estimatedDuration < 60) {
      return '${estimatedDuration}min';
    } else {
      final hours = estimatedDuration ~/ 60;
      final minutes = estimatedDuration % 60;
      if (minutes == 0) {
        return '${hours}h';
      } else {
        return '${hours}h ${minutes}min';
      }
    }
  }

  /// Create a copy of this template
  WorkoutTemplate copyWith({
    String? name,
    String? description,
    String? category,
    String? notes,
    List<String>? exercises,
    int? estimatedDuration,
    String? difficulty,
    List<String>? tags,
  }) {
    return WorkoutTemplate(
      id: const Uuid().v4(), // New ID for copy
      name: name ?? this.name,
      description: description ?? this.description,
      category: category ?? this.category,
      notes: notes ?? this.notes,
      exercises: exercises ?? this.exercises,
      estimatedDuration: estimatedDuration ?? this.estimatedDuration,
      difficulty: difficulty ?? this.difficulty,
      tags: tags ?? this.tags,
      isPrebuilt: false, // Copies are always custom
      createdAt: DateTime.now(),
      usageCount: 0,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'category': category,
      'notes': notes,
      'exercises': exercises,
      'estimatedDuration': estimatedDuration,
      'difficulty': difficulty,
      'tags': tags,
      'isPrebuilt': isPrebuilt,
      'createdAt': createdAt.toIso8601String(),
      'lastUsed': lastUsed?.toIso8601String(),
      'usageCount': usageCount,
    };
  }

  /// Create from JSON
  factory WorkoutTemplate.fromJson(Map<String, dynamic> json) {
    return WorkoutTemplate(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      category: json['category'] as String,
      notes: json['notes'] as String,
      exercises: List<String>.from(json['exercises'] as List),
      estimatedDuration: json['estimatedDuration'] as int,
      difficulty: json['difficulty'] as String,
      tags: List<String>.from(json['tags'] as List),
      isPrebuilt: json['isPrebuilt'] as bool,
      createdAt: DateTime.parse(json['createdAt'] as String),
      lastUsed: json['lastUsed'] != null ? DateTime.parse(json['lastUsed'] as String) : null,
      usageCount: json['usageCount'] as int,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is WorkoutTemplate &&
        other.id == id &&
        other.name == name &&
        listEquals(other.exercises, exercises);
  }

  @override
  int get hashCode => Object.hash(id, name, exercises);

  @override
  String toString() {
    return 'WorkoutTemplate(name: $name, category: $category, duration: ${estimatedDuration}min)';
  }
}
