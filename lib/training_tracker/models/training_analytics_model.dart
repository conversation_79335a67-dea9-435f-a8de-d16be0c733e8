// lib/training_tracker/models/training_analytics_model.dart

import 'package:flutter/foundation.dart';
import '../../models/training_session_model.dart';

/// Analytics data for training sessions
@immutable
class TrainingAnalytics {
  final DateTime startDate;
  final DateTime endDate;
  final List<TrainingSession> sessions;
  final Map<String, dynamic> metrics;

  const TrainingAnalytics({
    required this.startDate,
    required this.endDate,
    required this.sessions,
    required this.metrics,
  });

  /// Calculate weekly analytics
  factory TrainingAnalytics.weekly(List<TrainingSession> allSessions, DateTime weekStart) {
    final weekEnd = weekStart.add(const Duration(days: 7));
    final weekSessions = allSessions.where((session) {
      return session.createdAt.isAfter(weekStart) && session.createdAt.isBefore(weekEnd);
    }).toList();

    return TrainingAnalytics(
      startDate: weekStart,
      endDate: weekEnd,
      sessions: weekSessions,
      metrics: calculateMetrics(weekSessions),
    );
  }

  /// Calculate monthly analytics
  factory TrainingAnalytics.monthly(List<TrainingSession> allSessions, DateTime monthStart) {
    final monthEnd = DateTime(monthStart.year, monthStart.month + 1, 1);
    final monthSessions = allSessions.where((session) {
      return session.createdAt.isAfter(monthStart) && session.createdAt.isBefore(monthEnd);
    }).toList();

    return TrainingAnalytics(
      startDate: monthStart,
      endDate: monthEnd,
      sessions: monthSessions,
      metrics: calculateMetrics(monthSessions),
    );
  }

  static Map<String, dynamic> calculateMetrics(List<TrainingSession> sessions) {
    if (sessions.isEmpty) {
      return {
        'totalSessions': 0,
        'totalDuration': 0,
        'totalExp': 0.0,
        'averageDuration': 0.0,
        'averageExp': 0.0,
        'longestSession': 0,
        'shortestSession': 0,
        'mostExpInDay': 0.0,
        'consistency': 0.0,
        'frequencyByDay': <String, int>{},
        'durationTrend': <double>[],
        'expTrend': <double>[],
      };
    }

    final totalSessions = sessions.length;
    final totalDuration = sessions.fold<int>(0, (sum, session) => sum + session.durationSeconds);
    final totalExp = sessions.fold<double>(0, (sum, session) => sum + session.expEarned);
    
    final averageDuration = totalDuration / totalSessions;
    final averageExp = totalExp / totalSessions;
    
    final longestSession = sessions.map((s) => s.durationSeconds).reduce((a, b) => a > b ? a : b);
    final shortestSession = sessions.map((s) => s.durationSeconds).reduce((a, b) => a < b ? a : b);
    
    // Calculate most EXP in a single day
    final expByDay = <String, double>{};
    for (final session in sessions) {
      final dayKey = '${session.createdAt.year}-${session.createdAt.month}-${session.createdAt.day}';
      expByDay[dayKey] = (expByDay[dayKey] ?? 0) + session.expEarned;
    }
    final mostExpInDay = expByDay.values.isNotEmpty ? expByDay.values.reduce((a, b) => a > b ? a : b) : 0.0;
    
    // Calculate frequency by day of week
    final frequencyByDay = <String, int>{};
    final dayNames = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
    for (final session in sessions) {
      final dayName = dayNames[session.createdAt.weekday - 1];
      frequencyByDay[dayName] = (frequencyByDay[dayName] ?? 0) + 1;
    }
    
    // Calculate trends (simplified - last 7 data points)
    final sortedSessions = List<TrainingSession>.from(sessions)
      ..sort((a, b) => a.createdAt.compareTo(b.createdAt));
    
    final durationTrend = <double>[];
    final expTrend = <double>[];
    
    for (int i = 0; i < sortedSessions.length && i < 7; i++) {
      durationTrend.add(sortedSessions[i].durationSeconds.toDouble());
      expTrend.add(sortedSessions[i].expEarned);
    }
    
    // Calculate consistency (percentage of days with sessions)
    final uniqueDays = sessions.map((s) => 
      '${s.createdAt.year}-${s.createdAt.month}-${s.createdAt.day}'
    ).toSet().length;
    final totalDays = sessions.isNotEmpty ? 
      sessions.last.createdAt.difference(sessions.first.createdAt).inDays + 1 : 1;
    final consistency = (uniqueDays / totalDays) * 100;

    return {
      'totalSessions': totalSessions,
      'totalDuration': totalDuration,
      'totalExp': totalExp,
      'averageDuration': averageDuration,
      'averageExp': averageExp,
      'longestSession': longestSession,
      'shortestSession': shortestSession,
      'mostExpInDay': mostExpInDay,
      'consistency': consistency,
      'frequencyByDay': frequencyByDay,
      'durationTrend': durationTrend,
      'expTrend': expTrend,
      'uniqueTrainingDays': uniqueDays,
    };
  }

  // Getters for easy access to metrics
  int get totalSessions => metrics['totalSessions'] as int;
  int get totalDuration => metrics['totalDuration'] as int;
  double get totalExp => metrics['totalExp'] as double;
  double get averageDuration => metrics['averageDuration'] as double;
  double get averageExp => metrics['averageExp'] as double;
  int get longestSession => metrics['longestSession'] as int;
  int get shortestSession => metrics['shortestSession'] as int;
  double get mostExpInDay => metrics['mostExpInDay'] as double;
  double get consistency => metrics['consistency'] as double;
  Map<String, int> get frequencyByDay => metrics['frequencyByDay'] as Map<String, int>;
  List<double> get durationTrend => metrics['durationTrend'] as List<double>;
  List<double> get expTrend => metrics['expTrend'] as List<double>;
  int get uniqueTrainingDays => metrics['uniqueTrainingDays'] as int;

  /// Format duration in human-readable format
  String get formattedTotalDuration {
    final hours = totalDuration ~/ 3600;
    final minutes = (totalDuration % 3600) ~/ 60;
    if (hours > 0) {
      return '${hours}h ${minutes}m';
    }
    return '${minutes}m';
  }

  /// Format average duration
  String get formattedAverageDuration {
    final avgMinutes = (averageDuration / 60).round();
    return '${avgMinutes}m';
  }

  /// Get consistency rating
  String get consistencyRating {
    if (consistency >= 80) return 'Excellent';
    if (consistency >= 60) return 'Good';
    if (consistency >= 40) return 'Fair';
    if (consistency >= 20) return 'Poor';
    return 'Very Poor';
  }

  /// Get most active day
  String get mostActiveDay {
    if (frequencyByDay.isEmpty) return 'None';
    return frequencyByDay.entries
        .reduce((a, b) => a.value > b.value ? a : b)
        .key;
  }

  /// Convert to JSON for caching
  Map<String, dynamic> toJson() {
    return {
      'startDate': startDate.toIso8601String(),
      'endDate': endDate.toIso8601String(),
      'sessions': sessions.map((s) => s.toJson()).toList(),
      'metrics': metrics,
    };
  }

  /// Create from JSON
  factory TrainingAnalytics.fromJson(Map<String, dynamic> json) {
    return TrainingAnalytics(
      startDate: DateTime.parse(json['startDate'] as String),
      endDate: DateTime.parse(json['endDate'] as String),
      sessions: (json['sessions'] as List)
          .map((s) => TrainingSession.fromJson(s as Map<String, dynamic>))
          .toList(),
      metrics: json['metrics'] as Map<String, dynamic>,
    );
  }

  @override
  String toString() {
    return 'TrainingAnalytics(sessions: $totalSessions, duration: $formattedTotalDuration, exp: ${totalExp.toStringAsFixed(1)})';
  }
}

/// Personal records tracking
@immutable
class PersonalRecords {
  final int longestSessionDuration;
  final double mostExpInSession;
  final double mostExpInDay;
  final int longestStreak;
  final int currentStreak;
  final DateTime? longestSessionDate;
  final DateTime? mostExpSessionDate;
  final DateTime? mostExpDayDate;

  const PersonalRecords({
    required this.longestSessionDuration,
    required this.mostExpInSession,
    required this.mostExpInDay,
    required this.longestStreak,
    required this.currentStreak,
    this.longestSessionDate,
    this.mostExpSessionDate,
    this.mostExpDayDate,
  });

  factory PersonalRecords.fromSessions(List<TrainingSession> sessions) {
    if (sessions.isEmpty) {
      return const PersonalRecords(
        longestSessionDuration: 0,
        mostExpInSession: 0,
        mostExpInDay: 0,
        longestStreak: 0,
        currentStreak: 0,
      );
    }

    // Find longest session
    final longestSession = sessions.reduce((a, b) => 
      a.durationSeconds > b.durationSeconds ? a : b);
    
    // Find session with most EXP
    final mostExpSession = sessions.reduce((a, b) => 
      a.expEarned > b.expEarned ? a : b);
    
    // Calculate most EXP in a day
    final expByDay = <String, double>{};
    final dayToDate = <String, DateTime>{};
    
    for (final session in sessions) {
      final dayKey = '${session.createdAt.year}-${session.createdAt.month}-${session.createdAt.day}';
      expByDay[dayKey] = (expByDay[dayKey] ?? 0) + session.expEarned;
      dayToDate[dayKey] = session.createdAt;
    }
    
    final mostExpDay = expByDay.entries.reduce((a, b) => a.value > b.value ? a : b);
    
    // Calculate streaks
    final streaks = _calculateStreaks(sessions);

    return PersonalRecords(
      longestSessionDuration: longestSession.durationSeconds,
      mostExpInSession: mostExpSession.expEarned,
      mostExpInDay: mostExpDay.value,
      longestStreak: streaks['longest'] as int,
      currentStreak: streaks['current'] as int,
      longestSessionDate: longestSession.createdAt,
      mostExpSessionDate: mostExpSession.createdAt,
      mostExpDayDate: dayToDate[mostExpDay.key],
    );
  }

  static Map<String, int> _calculateStreaks(List<TrainingSession> sessions) {
    if (sessions.isEmpty) return {'longest': 0, 'current': 0};

    final sortedSessions = List<TrainingSession>.from(sessions)
      ..sort((a, b) => a.createdAt.compareTo(b.createdAt));

    final uniqueDays = <String>{};
    for (final session in sortedSessions) {
      final dayKey = '${session.createdAt.year}-${session.createdAt.month.toString().padLeft(2, '0')}-${session.createdAt.day.toString().padLeft(2, '0')}';
      uniqueDays.add(dayKey);
    }

    final sortedDays = uniqueDays.toList()..sort();
    
    int longestStreak = 0;
    int currentStreak = 0;
    int tempStreak = 1;

    for (int i = 1; i < sortedDays.length; i++) {
      final prevDate = DateTime.parse(sortedDays[i - 1]);
      final currDate = DateTime.parse(sortedDays[i]);
      
      if (currDate.difference(prevDate).inDays == 1) {
        tempStreak++;
      } else {
        longestStreak = tempStreak > longestStreak ? tempStreak : longestStreak;
        tempStreak = 1;
      }
    }
    
    longestStreak = tempStreak > longestStreak ? tempStreak : longestStreak;
    
    // Calculate current streak
    if (sortedDays.isNotEmpty) {
      final lastDay = DateTime.parse(sortedDays.last);
      final today = DateTime.now();
      final daysDiff = today.difference(lastDay).inDays;
      
      if (daysDiff <= 1) {
        // Count backwards from last day
        currentStreak = 1;
        for (int i = sortedDays.length - 2; i >= 0; i--) {
          final prevDate = DateTime.parse(sortedDays[i]);
          final currDate = DateTime.parse(sortedDays[i + 1]);
          
          if (currDate.difference(prevDate).inDays == 1) {
            currentStreak++;
          } else {
            break;
          }
        }
      }
    }

    return {'longest': longestStreak, 'current': currentStreak};
  }

  String get formattedLongestSession {
    final hours = longestSessionDuration ~/ 3600;
    final minutes = (longestSessionDuration % 3600) ~/ 60;
    if (hours > 0) {
      return '${hours}h ${minutes}m';
    }
    return '${minutes}m';
  }

  /// Convert to JSON for caching
  Map<String, dynamic> toJson() {
    return {
      'longestSessionDuration': longestSessionDuration,
      'mostExpInSession': mostExpInSession,
      'mostExpInDay': mostExpInDay,
      'longestStreak': longestStreak,
      'currentStreak': currentStreak,
      'longestSessionDate': longestSessionDate?.toIso8601String(),
      'mostExpSessionDate': mostExpSessionDate?.toIso8601String(),
      'mostExpDayDate': mostExpDayDate?.toIso8601String(),
    };
  }

  /// Create from JSON
  factory PersonalRecords.fromJson(Map<String, dynamic> json) {
    return PersonalRecords(
      longestSessionDuration: json['longestSessionDuration'] as int,
      mostExpInSession: json['mostExpInSession'] as double,
      mostExpInDay: json['mostExpInDay'] as double,
      longestStreak: json['longestStreak'] as int,
      currentStreak: json['currentStreak'] as int,
      longestSessionDate: json['longestSessionDate'] != null
          ? DateTime.parse(json['longestSessionDate'] as String)
          : null,
      mostExpSessionDate: json['mostExpSessionDate'] != null
          ? DateTime.parse(json['mostExpSessionDate'] as String)
          : null,
      mostExpDayDate: json['mostExpDayDate'] != null
          ? DateTime.parse(json['mostExpDayDate'] as String)
          : null,
    );
  }
}
