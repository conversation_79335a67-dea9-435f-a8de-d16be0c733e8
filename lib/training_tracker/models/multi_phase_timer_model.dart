// lib/training_tracker/models/multi_phase_timer_model.dart

import 'package:flutter/foundation.dart';

/// Represents a single phase in a multi-phase workout
@immutable
class WorkoutPhase {
  final String id;
  final String name;
  final int durationSeconds;
  final String description;
  final String color; // Hex color for visual representation
  final bool hasAudioCue;
  final String? audioFile;

  const WorkoutPhase({
    required this.id,
    required this.name,
    required this.durationSeconds,
    required this.description,
    required this.color,
    this.hasAudioCue = false,
    this.audioFile,
  });

  /// Warm-up phase
  factory WorkoutPhase.warmup({int duration = 300}) {
    return WorkoutPhase(
      id: 'warmup',
      name: 'Warm-up',
      durationSeconds: duration,
      description: 'Prepare your body for the workout',
      color: '#FFA500', // Orange
      hasAudioCue: true,
      audioFile: 'lightning.mp3',
    );
  }

  /// Main workout phase
  factory WorkoutPhase.workout({int duration = 1800}) {
    return WorkoutPhase(
      id: 'workout',
      name: 'Main Workout',
      durationSeconds: duration,
      description: 'Push your limits and achieve your goals',
      color: '#FF0080', // Neon Pink
      hasAudioCue: true,
      audioFile: 'lightning.mp3',
    );
  }

  /// Cool-down phase
  factory WorkoutPhase.cooldown({int duration = 300}) {
    return WorkoutPhase(
      id: 'cooldown',
      name: 'Cool-down',
      durationSeconds: duration,
      description: 'Gradually return to resting state',
      color: '#00BFFF', // Sky Blue
      hasAudioCue: true,
      audioFile: 'lightning.mp3',
    );
  }

  /// Rest phase for intervals
  factory WorkoutPhase.rest({int duration = 60}) {
    return WorkoutPhase(
      id: 'rest',
      name: 'Rest',
      durationSeconds: duration,
      description: 'Recover before the next interval',
      color: '#32CD32', // Lime Green
      hasAudioCue: true,
      audioFile: 'lightning.mp3',
    );
  }

  /// Work phase for intervals
  factory WorkoutPhase.work({int duration = 120}) {
    return WorkoutPhase(
      id: 'work',
      name: 'Work',
      durationSeconds: duration,
      description: 'High intensity effort',
      color: '#FF4500', // Red Orange
      hasAudioCue: true,
      audioFile: 'lightning.mp3',
    );
  }

  /// Format duration as MM:SS
  String get formattedDuration {
    final minutes = durationSeconds ~/ 60;
    final seconds = durationSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'durationSeconds': durationSeconds,
      'description': description,
      'color': color,
      'hasAudioCue': hasAudioCue,
      'audioFile': audioFile,
    };
  }

  /// Create from JSON
  factory WorkoutPhase.fromJson(Map<String, dynamic> json) {
    return WorkoutPhase(
      id: json['id'] as String,
      name: json['name'] as String,
      durationSeconds: json['durationSeconds'] as int,
      description: json['description'] as String,
      color: json['color'] as String,
      hasAudioCue: json['hasAudioCue'] as bool? ?? false,
      audioFile: json['audioFile'] as String?,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is WorkoutPhase &&
        other.id == id &&
        other.durationSeconds == durationSeconds;
  }

  @override
  int get hashCode => Object.hash(id, durationSeconds);
}

/// Multi-phase timer configuration
@immutable
class MultiPhaseTimer {
  final String id;
  final String name;
  final List<WorkoutPhase> phases;
  final int currentPhaseIndex;
  final int currentPhaseElapsed;
  final bool isRunning;
  final bool isPaused;
  final DateTime? startTime;

  const MultiPhaseTimer({
    required this.id,
    required this.name,
    required this.phases,
    this.currentPhaseIndex = 0,
    this.currentPhaseElapsed = 0,
    this.isRunning = false,
    this.isPaused = false,
    this.startTime,
  });

  /// Create a basic 3-phase timer (warm-up, workout, cool-down)
  factory MultiPhaseTimer.basic({
    String name = 'Basic Workout',
    int warmupDuration = 300,
    int workoutDuration = 1800,
    int cooldownDuration = 300,
  }) {
    return MultiPhaseTimer(
      id: 'basic_timer',
      name: name,
      phases: [
        WorkoutPhase.warmup(duration: warmupDuration),
        WorkoutPhase.workout(duration: workoutDuration),
        WorkoutPhase.cooldown(duration: cooldownDuration),
      ],
    );
  }

  /// Create an interval timer
  factory MultiPhaseTimer.interval({
    String name = 'Interval Training',
    int workDuration = 120,
    int restDuration = 60,
    int intervals = 8,
    int warmupDuration = 300,
    int cooldownDuration = 300,
  }) {
    final phases = <WorkoutPhase>[];
    
    // Add warm-up
    phases.add(WorkoutPhase.warmup(duration: warmupDuration));
    
    // Add intervals
    for (int i = 0; i < intervals; i++) {
      phases.add(WorkoutPhase.work(duration: workDuration));
      if (i < intervals - 1) { // Don't add rest after last interval
        phases.add(WorkoutPhase.rest(duration: restDuration));
      }
    }
    
    // Add cool-down
    phases.add(WorkoutPhase.cooldown(duration: cooldownDuration));

    return MultiPhaseTimer(
      id: 'interval_timer',
      name: name,
      phases: phases,
    );
  }

  /// Create a custom timer
  factory MultiPhaseTimer.custom({
    required String name,
    required List<WorkoutPhase> phases,
  }) {
    return MultiPhaseTimer(
      id: 'custom_timer',
      name: name,
      phases: phases,
    );
  }

  /// Get current phase
  WorkoutPhase? get currentPhase {
    if (currentPhaseIndex >= 0 && currentPhaseIndex < phases.length) {
      return phases[currentPhaseIndex];
    }
    return null;
  }

  /// Get next phase
  WorkoutPhase? get nextPhase {
    final nextIndex = currentPhaseIndex + 1;
    if (nextIndex < phases.length) {
      return phases[nextIndex];
    }
    return null;
  }

  /// Check if timer is completed
  bool get isCompleted => currentPhaseIndex >= phases.length;

  /// Get total duration of all phases
  int get totalDuration {
    return phases.fold<int>(0, (sum, phase) => sum + phase.durationSeconds);
  }

  /// Get total elapsed time across all phases
  int get totalElapsed {
    int elapsed = 0;
    
    // Add completed phases
    for (int i = 0; i < currentPhaseIndex; i++) {
      elapsed += phases[i].durationSeconds;
    }
    
    // Add current phase elapsed
    elapsed += currentPhaseElapsed;
    
    return elapsed;
  }

  /// Get remaining time in current phase
  int get currentPhaseRemaining {
    final phase = currentPhase;
    if (phase == null) return 0;
    return phase.durationSeconds - currentPhaseElapsed;
  }

  /// Get total remaining time
  int get totalRemaining => totalDuration - totalElapsed;

  /// Get progress percentage (0.0 to 1.0)
  double get progress {
    if (totalDuration == 0) return 0.0;
    return totalElapsed / totalDuration;
  }

  /// Get current phase progress (0.0 to 1.0)
  double get currentPhaseProgress {
    final phase = currentPhase;
    if (phase == null || phase.durationSeconds == 0) return 0.0;
    return currentPhaseElapsed / phase.durationSeconds;
  }

  /// Start the timer
  MultiPhaseTimer start() {
    return MultiPhaseTimer(
      id: id,
      name: name,
      phases: phases,
      currentPhaseIndex: currentPhaseIndex,
      currentPhaseElapsed: currentPhaseElapsed,
      isRunning: true,
      isPaused: false,
      startTime: startTime ?? DateTime.now(),
    );
  }

  /// Pause the timer
  MultiPhaseTimer pause() {
    return MultiPhaseTimer(
      id: id,
      name: name,
      phases: phases,
      currentPhaseIndex: currentPhaseIndex,
      currentPhaseElapsed: currentPhaseElapsed,
      isRunning: false,
      isPaused: true,
      startTime: startTime,
    );
  }

  /// Resume the timer
  MultiPhaseTimer resume() {
    return MultiPhaseTimer(
      id: id,
      name: name,
      phases: phases,
      currentPhaseIndex: currentPhaseIndex,
      currentPhaseElapsed: currentPhaseElapsed,
      isRunning: true,
      isPaused: false,
      startTime: startTime,
    );
  }

  /// Reset the timer
  MultiPhaseTimer reset() {
    return MultiPhaseTimer(
      id: id,
      name: name,
      phases: phases,
      currentPhaseIndex: 0,
      currentPhaseElapsed: 0,
      isRunning: false,
      isPaused: false,
      startTime: null,
    );
  }

  /// Advance to next phase
  MultiPhaseTimer advanceToNextPhase() {
    final nextIndex = currentPhaseIndex + 1;
    return MultiPhaseTimer(
      id: id,
      name: name,
      phases: phases,
      currentPhaseIndex: nextIndex,
      currentPhaseElapsed: 0,
      isRunning: isRunning,
      isPaused: isPaused,
      startTime: startTime,
    );
  }

  /// Update elapsed time in current phase
  MultiPhaseTimer updateElapsed(int elapsed) {
    return MultiPhaseTimer(
      id: id,
      name: name,
      phases: phases,
      currentPhaseIndex: currentPhaseIndex,
      currentPhaseElapsed: elapsed,
      isRunning: isRunning,
      isPaused: isPaused,
      startTime: startTime,
    );
  }

  /// Format total duration
  String get formattedTotalDuration {
    final minutes = totalDuration ~/ 60;
    final seconds = totalDuration % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  /// Format total elapsed
  String get formattedTotalElapsed {
    final minutes = totalElapsed ~/ 60;
    final seconds = totalElapsed % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'phases': phases.map((p) => p.toJson()).toList(),
      'currentPhaseIndex': currentPhaseIndex,
      'currentPhaseElapsed': currentPhaseElapsed,
      'isRunning': isRunning,
      'isPaused': isPaused,
      'startTime': startTime?.toIso8601String(),
    };
  }

  /// Create from JSON
  factory MultiPhaseTimer.fromJson(Map<String, dynamic> json) {
    return MultiPhaseTimer(
      id: json['id'] as String,
      name: json['name'] as String,
      phases: (json['phases'] as List)
          .map((p) => WorkoutPhase.fromJson(p as Map<String, dynamic>))
          .toList(),
      currentPhaseIndex: json['currentPhaseIndex'] as int? ?? 0,
      currentPhaseElapsed: json['currentPhaseElapsed'] as int? ?? 0,
      isRunning: json['isRunning'] as bool? ?? false,
      isPaused: json['isPaused'] as bool? ?? false,
      startTime: json['startTime'] != null 
          ? DateTime.parse(json['startTime'] as String) 
          : null,
    );
  }

  @override
  String toString() {
    return 'MultiPhaseTimer(name: $name, phase: ${currentPhaseIndex + 1}/${phases.length}, elapsed: $formattedTotalElapsed)';
  }
}
