// lib/training_tracker/models/performance_metrics_model.dart

import 'package:flutter/foundation.dart';
import '../../models/training_session_model.dart';

/// Performance metrics for training analysis
@immutable
class PerformanceMetrics {
  final double sessionCompletionRate;
  final double goalAchievementRate;
  final int currentStreak;
  final int longestStreak;
  final List<RestDay> restDays;
  final List<BodyweightEntry> bodyweightProgression;
  final Map<String, double> categoryPerformance;
  final DateTime calculatedAt;

  const PerformanceMetrics({
    required this.sessionCompletionRate,
    required this.goalAchievementRate,
    required this.currentStreak,
    required this.longestStreak,
    required this.restDays,
    required this.bodyweightProgression,
    required this.categoryPerformance,
    required this.calculatedAt,
  });

  /// Calculate performance metrics from training sessions
  factory PerformanceMetrics.fromSessions(List<TrainingSession> sessions) {
    if (sessions.isEmpty) {
      return PerformanceMetrics(
        sessionCompletionRate: 0.0,
        goalAchievementRate: 0.0,
        currentStreak: 0,
        longestStreak: 0,
        restDays: [],
        bodyweightProgression: [],
        categoryPerformance: {},
        calculatedAt: DateTime.now(),
      );
    }

    // Calculate session completion rate (completed vs started)
    final completedSessions = sessions.where((s) => s.isCompleted).length;
    final completionRate = (completedSessions / sessions.length) * 100;

    // Calculate goal achievement rate
    final sessionsWithGoals = sessions.where((s) => s.currentGoal.isNotEmpty).length;
    final goalAchievementRate = sessionsWithGoals > 0 ? 
        (completedSessions / sessionsWithGoals) * 100 : 0.0;

    // Calculate streaks
    final streaks = _calculateStreaks(sessions);

    // Calculate rest days
    final restDays = _calculateRestDays(sessions);

    // Extract bodyweight progression
    final bodyweightProgression = _extractBodyweightProgression(sessions);

    // Calculate category performance
    final categoryPerformance = _calculateCategoryPerformance(sessions);

    return PerformanceMetrics(
      sessionCompletionRate: completionRate,
      goalAchievementRate: goalAchievementRate,
      currentStreak: streaks['current'] as int,
      longestStreak: streaks['longest'] as int,
      restDays: restDays,
      bodyweightProgression: bodyweightProgression,
      categoryPerformance: categoryPerformance,
      calculatedAt: DateTime.now(),
    );
  }

  static Map<String, int> _calculateStreaks(List<TrainingSession> sessions) {
    if (sessions.isEmpty) return {'current': 0, 'longest': 0};

    final sortedSessions = List<TrainingSession>.from(sessions)
      ..sort((a, b) => a.createdAt.compareTo(b.createdAt));

    // Get unique training days
    final uniqueDays = <String>{};
    for (final session in sortedSessions) {
      final dayKey = '${session.createdAt.year}-${session.createdAt.month}-${session.createdAt.day}';
      uniqueDays.add(dayKey);
    }

    final sortedDays = uniqueDays.toList()..sort();
    
    int longestStreak = 0;
    int currentStreak = 0;
    int tempStreak = 1;

    // Calculate longest streak
    for (int i = 1; i < sortedDays.length; i++) {
      final prevDate = DateTime.parse(sortedDays[i - 1]);
      final currDate = DateTime.parse(sortedDays[i]);
      
      if (currDate.difference(prevDate).inDays == 1) {
        tempStreak++;
      } else {
        longestStreak = tempStreak > longestStreak ? tempStreak : longestStreak;
        tempStreak = 1;
      }
    }
    longestStreak = tempStreak > longestStreak ? tempStreak : longestStreak;

    // Calculate current streak
    if (sortedDays.isNotEmpty) {
      final lastDay = DateTime.parse(sortedDays.last);
      final today = DateTime.now();
      final daysDiff = today.difference(lastDay).inDays;
      
      if (daysDiff <= 1) {
        currentStreak = 1;
        for (int i = sortedDays.length - 2; i >= 0; i--) {
          final prevDate = DateTime.parse(sortedDays[i]);
          final currDate = DateTime.parse(sortedDays[i + 1]);
          
          if (currDate.difference(prevDate).inDays == 1) {
            currentStreak++;
          } else {
            break;
          }
        }
      }
    }

    return {'current': currentStreak, 'longest': longestStreak};
  }

  static List<RestDay> _calculateRestDays(List<TrainingSession> sessions) {
    if (sessions.isEmpty) return [];

    final restDays = <RestDay>[];
    final sortedSessions = List<TrainingSession>.from(sessions)
      ..sort((a, b) => a.createdAt.compareTo(b.createdAt));

    // Get training days
    final trainingDays = <DateTime>{};
    for (final session in sortedSessions) {
      final dayOnly = DateTime(session.createdAt.year, session.createdAt.month, session.createdAt.day);
      trainingDays.add(dayOnly);
    }

    final sortedTrainingDays = trainingDays.toList()..sort();

    // Find rest periods between training days
    for (int i = 1; i < sortedTrainingDays.length; i++) {
      final prevDay = sortedTrainingDays[i - 1];
      final currDay = sortedTrainingDays[i];
      final daysBetween = currDay.difference(prevDay).inDays;

      if (daysBetween > 1) {
        // There are rest days between training sessions
        for (int j = 1; j < daysBetween; j++) {
          final restDay = prevDay.add(Duration(days: j));
          restDays.add(RestDay(
            date: restDay,
            daysBefore: j,
            daysAfter: daysBetween - j,
            isPlanned: false, // We can't determine if it was planned from historical data
          ));
        }
      }
    }

    return restDays;
  }

  static List<BodyweightEntry> _extractBodyweightProgression(List<TrainingSession> sessions) {
    final bodyweightEntries = <BodyweightEntry>[];
    
    for (final session in sessions) {
      if (session.bodyweightKg != null) {
        bodyweightEntries.add(BodyweightEntry(
          date: session.createdAt,
          weight: session.bodyweightKg!,
          sessionId: session.id,
        ));
      }
    }

    // Sort by date
    bodyweightEntries.sort((a, b) => a.date.compareTo(b.date));
    
    return bodyweightEntries;
  }

  static Map<String, double> _calculateCategoryPerformance(List<TrainingSession> sessions) {
    final categoryStats = <String, List<double>>{};
    
    // Group sessions by label/category
    for (final session in sessions) {
      final category = session.label;
      if (!categoryStats.containsKey(category)) {
        categoryStats[category] = [];
      }
      categoryStats[category]!.add(session.expEarned);
    }

    // Calculate average performance per category
    final categoryPerformance = <String, double>{};
    for (final entry in categoryStats.entries) {
      final average = entry.value.reduce((a, b) => a + b) / entry.value.length;
      categoryPerformance[entry.key] = average;
    }

    return categoryPerformance;
  }

  /// Get performance rating based on completion rate
  String get completionRating {
    if (sessionCompletionRate >= 95) return 'Excellent';
    if (sessionCompletionRate >= 85) return 'Very Good';
    if (sessionCompletionRate >= 75) return 'Good';
    if (sessionCompletionRate >= 60) return 'Fair';
    return 'Needs Improvement';
  }

  /// Get goal achievement rating
  String get goalAchievementRating {
    if (goalAchievementRate >= 90) return 'Outstanding';
    if (goalAchievementRate >= 75) return 'Excellent';
    if (goalAchievementRate >= 60) return 'Good';
    if (goalAchievementRate >= 40) return 'Fair';
    return 'Needs Focus';
  }

  /// Get streak status
  String get streakStatus {
    if (currentStreak >= 30) return 'Legendary';
    if (currentStreak >= 14) return 'Amazing';
    if (currentStreak >= 7) return 'Great';
    if (currentStreak >= 3) return 'Building';
    if (currentStreak >= 1) return 'Started';
    return 'Inactive';
  }

  /// Get bodyweight trend
  String get bodyweightTrend {
    if (bodyweightProgression.length < 2) return 'Insufficient Data';
    
    final first = bodyweightProgression.first.weight;
    final last = bodyweightProgression.last.weight;
    final change = last - first;
    
    if (change > 2) return 'Increasing';
    if (change < -2) return 'Decreasing';
    return 'Stable';
  }

  /// Get average rest days between sessions
  double get averageRestDays {
    if (restDays.isEmpty) return 0.0;
    
    final totalRestDays = restDays.length;
    final totalPeriods = restDays.length; // Simplified calculation
    
    return totalRestDays / totalPeriods;
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'sessionCompletionRate': sessionCompletionRate,
      'goalAchievementRate': goalAchievementRate,
      'currentStreak': currentStreak,
      'longestStreak': longestStreak,
      'restDays': restDays.map((r) => r.toJson()).toList(),
      'bodyweightProgression': bodyweightProgression.map((b) => b.toJson()).toList(),
      'categoryPerformance': categoryPerformance,
      'calculatedAt': calculatedAt.toIso8601String(),
    };
  }

  /// Create from JSON
  factory PerformanceMetrics.fromJson(Map<String, dynamic> json) {
    return PerformanceMetrics(
      sessionCompletionRate: json['sessionCompletionRate'] as double,
      goalAchievementRate: json['goalAchievementRate'] as double,
      currentStreak: json['currentStreak'] as int,
      longestStreak: json['longestStreak'] as int,
      restDays: (json['restDays'] as List)
          .map((r) => RestDay.fromJson(r as Map<String, dynamic>))
          .toList(),
      bodyweightProgression: (json['bodyweightProgression'] as List)
          .map((b) => BodyweightEntry.fromJson(b as Map<String, dynamic>))
          .toList(),
      categoryPerformance: Map<String, double>.from(json['categoryPerformance'] as Map),
      calculatedAt: _parseDateTime(json['calculatedAt'] as String),
    );
  }

  /// Safe date parsing with fallback for malformed dates
  static DateTime _parseDateTime(String dateString) {
    try {
      return DateTime.parse(dateString);
    } catch (e) {
      // Handle malformed dates like "2025-7-8" by trying to fix them
      try {
        final parts = dateString.split('-');
        if (parts.length >= 3) {
          final year = int.parse(parts[0]);
          final month = int.parse(parts[1]);
          final day = int.parse(parts[2]);
          return DateTime(year, month, day);
        }

        // Try other common formats
        if (dateString.contains('/')) {
          final parts = dateString.split('/');
          if (parts.length >= 3) {
            final day = int.parse(parts[0]);
            final month = int.parse(parts[1]);
            final year = int.parse(parts[2]);
            return DateTime(year, month, day);
          }
        }

        // Try dot format
        if (dateString.contains('.')) {
          final parts = dateString.split('.');
          if (parts.length >= 3) {
            final day = int.parse(parts[0]);
            final month = int.parse(parts[1]);
            final year = int.parse(parts[2]);
            // Handle 2-digit years
            final fullYear = year < 100 ? 2000 + year : year;
            return DateTime(fullYear, month, day);
          }
        }
      } catch (_) {
        // If all parsing fails, return current time
      }
      return DateTime.now();
    }
  }
}

/// Represents a rest day between training sessions
@immutable
class RestDay {
  final DateTime date;
  final int daysBefore;
  final int daysAfter;
  final bool isPlanned;

  const RestDay({
    required this.date,
    required this.daysBefore,
    required this.daysAfter,
    required this.isPlanned,
  });

  Map<String, dynamic> toJson() {
    return {
      'date': date.toIso8601String(),
      'daysBefore': daysBefore,
      'daysAfter': daysAfter,
      'isPlanned': isPlanned,
    };
  }

  factory RestDay.fromJson(Map<String, dynamic> json) {
    return RestDay(
      date: PerformanceMetrics._parseDateTime(json['date'] as String),
      daysBefore: json['daysBefore'] as int,
      daysAfter: json['daysAfter'] as int,
      isPlanned: json['isPlanned'] as bool,
    );
  }
}

/// Represents a bodyweight measurement entry
@immutable
class BodyweightEntry {
  final DateTime date;
  final double weight;
  final String sessionId;

  const BodyweightEntry({
    required this.date,
    required this.weight,
    required this.sessionId,
  });

  Map<String, dynamic> toJson() {
    return {
      'date': date.toIso8601String(),
      'weight': weight,
      'sessionId': sessionId,
    };
  }

  factory BodyweightEntry.fromJson(Map<String, dynamic> json) {
    return BodyweightEntry(
      date: PerformanceMetrics._parseDateTime(json['date'] as String),
      weight: json['weight'] as double,
      sessionId: json['sessionId'] as String,
    );
  }
}
