// lib/training_tracker/models/training_calendar_model.dart

import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';

/// Represents a planned training session in the calendar
@immutable
class PlannedSession {
  final String id;
  final DateTime plannedDate;
  final String workoutType;
  final String label;
  final String notes;
  final int estimatedDuration; // in minutes
  final String intensity; // Low, Medium, High
  final bool isCompleted;
  final String? completedSessionId;
  final DateTime createdAt;
  final DateTime? completedAt;
  final int timeSlot; // 0-3 for up to 4 sessions per day
  final int workoutColorValue; // Color value for workout type
  final String? imagePath; // Optional image path for the session

  const PlannedSession({
    required this.id,
    required this.plannedDate,
    required this.workoutType,
    required this.label,
    required this.notes,
    required this.estimatedDuration,
    required this.intensity,
    this.isCompleted = false,
    this.completedSessionId,
    required this.createdAt,
    this.completedAt,
    this.timeSlot = 0,
    this.workoutColorValue = 0xFF4CAF50, // Default green
    this.imagePath,
  });

  /// Create a new planned session
  factory PlannedSession.create({
    required DateTime plannedDate,
    required String workoutType,
    required String label,
    String notes = '',
    int estimatedDuration = 30,
    String intensity = 'Medium',
    int timeSlot = 0,
    int? workoutColorValue,
    String? imagePath,
  }) {
    return PlannedSession(
      id: const Uuid().v4(),
      plannedDate: plannedDate,
      workoutType: workoutType,
      label: label,
      notes: notes,
      estimatedDuration: estimatedDuration,
      intensity: intensity,
      timeSlot: timeSlot,
      workoutColorValue: workoutColorValue ?? _getDefaultColorForType(workoutType),
      imagePath: imagePath,
      createdAt: DateTime.now(),
    );
  }

  /// Get default color for workout type
  static int _getDefaultColorForType(String workoutType) {
    switch (workoutType.toLowerCase()) {
      case 'strength':
        return 0xFFFF4500; // Red Orange
      case 'cardio':
        return 0xFF00BFFF; // Deep Sky Blue
      case 'flexibility':
        return 0xFF32CD32; // Lime Green
      case 'hiit':
        return 0xFFFF1493; // Deep Pink
      case 'yoga':
        return 0xFF9370DB; // Medium Purple
      case 'sports':
        return 0xFFFFD700; // Gold
      case 'recovery':
        return 0xFF87CEEB; // Sky Blue
      default:
        return 0xFF4CAF50; // Default Green
    }
  }

  /// Mark session as completed
  PlannedSession markCompleted(String sessionId) {
    return PlannedSession(
      id: id,
      plannedDate: plannedDate,
      workoutType: workoutType,
      label: label,
      notes: notes,
      estimatedDuration: estimatedDuration,
      intensity: intensity,
      isCompleted: true,
      completedSessionId: sessionId,
      createdAt: createdAt,
      completedAt: DateTime.now(),
    );
  }

  /// Update planned session
  PlannedSession copyWith({
    DateTime? plannedDate,
    String? workoutType,
    String? label,
    String? notes,
    int? estimatedDuration,
    String? intensity,
    bool? isCompleted,
    String? completedSessionId,
    DateTime? completedAt,
    int? timeSlot,
    int? workoutColorValue,
  }) {
    return PlannedSession(
      id: id,
      plannedDate: plannedDate ?? this.plannedDate,
      workoutType: workoutType ?? this.workoutType,
      label: label ?? this.label,
      notes: notes ?? this.notes,
      estimatedDuration: estimatedDuration ?? this.estimatedDuration,
      intensity: intensity ?? this.intensity,
      isCompleted: isCompleted ?? this.isCompleted,
      completedSessionId: completedSessionId ?? this.completedSessionId,
      createdAt: createdAt,
      completedAt: completedAt ?? this.completedAt,
      timeSlot: timeSlot ?? this.timeSlot,
      workoutColorValue: workoutColorValue ?? this.workoutColorValue,
    );
  }

  /// Get color based on workout type
  int get typeColor {
    switch (workoutType.toLowerCase()) {
      case 'strength':
        return 0xFFFF4500; // Red Orange
      case 'cardio':
        return 0xFF00BFFF; // Deep Sky Blue
      case 'flexibility':
        return 0xFF32CD32; // Lime Green
      case 'hiit':
        return 0xFFFF1493; // Deep Pink
      case 'yoga':
        return 0xFF9370DB; // Medium Purple
      case 'rest':
        return 0xFF708090; // Slate Gray
      default:
        return 0xFF00FFFF; // Cyan
    }
  }

  /// Get intensity color
  int get intensityColor {
    switch (intensity.toLowerCase()) {
      case 'low':
        return 0xFF32CD32; // Lime Green
      case 'medium':
        return 0xFFFFA500; // Orange
      case 'high':
        return 0xFFFF4500; // Red Orange
      default:
        return 0xFFFFA500; // Orange
    }
  }

  /// Format estimated duration
  String get formattedDuration {
    final hours = estimatedDuration ~/ 60;
    final minutes = estimatedDuration % 60;
    if (hours > 0) {
      return '${hours}h ${minutes}m';
    }
    return '${minutes}m';
  }

  /// Get workout color as Color object
  Color get workoutColor => Color(workoutColorValue);

  /// Get time slot label
  String get timeSlotLabel {
    switch (timeSlot) {
      case 0:
        return 'Morning';
      case 1:
        return 'Afternoon';
      case 2:
        return 'Evening';
      case 3:
        return 'Night';
      default:
        return 'Slot ${timeSlot + 1}';
    }
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'plannedDate': plannedDate.toIso8601String(),
      'workoutType': workoutType,
      'label': label,
      'notes': notes,
      'estimatedDuration': estimatedDuration,
      'intensity': intensity,
      'isCompleted': isCompleted,
      'completedSessionId': completedSessionId,
      'createdAt': createdAt.toIso8601String(),
      'completedAt': completedAt?.toIso8601String(),
      'timeSlot': timeSlot,
      'workoutColorValue': workoutColorValue,
      'imagePath': imagePath,
    };
  }

  /// Create from JSON
  factory PlannedSession.fromJson(Map<String, dynamic> json) {
    return PlannedSession(
      id: json['id'] as String,
      plannedDate: DateTime.parse(json['plannedDate'] as String),
      workoutType: json['workoutType'] as String,
      label: json['label'] as String,
      notes: json['notes'] as String,
      estimatedDuration: json['estimatedDuration'] as int,
      intensity: json['intensity'] as String,
      isCompleted: json['isCompleted'] as bool? ?? false,
      completedSessionId: json['completedSessionId'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      completedAt: json['completedAt'] != null
          ? DateTime.parse(json['completedAt'] as String)
          : null,
      timeSlot: json['timeSlot'] as int? ?? 0,
      workoutColorValue: json['workoutColorValue'] as int? ?? 0xFF4CAF50,
      imagePath: json['imagePath'] as String?, // Backward compatibility
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PlannedSession && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'PlannedSession(label: $label, type: $workoutType, date: ${plannedDate.day}/${plannedDate.month})';
  }
}

/// Represents a rest day in the calendar
@immutable
class RestDay {
  final String id;
  final DateTime date;
  final String type; // Active, Complete, Recovery
  final String notes;
  final bool isPlanned;
  final DateTime createdAt;

  const RestDay({
    required this.id,
    required this.date,
    required this.type,
    required this.notes,
    required this.isPlanned,
    required this.createdAt,
  });

  /// Create a new rest day
  factory RestDay.create({
    required DateTime date,
    String type = 'Complete',
    String notes = '',
    bool isPlanned = true,
  }) {
    return RestDay(
      id: const Uuid().v4(),
      date: date,
      type: type,
      notes: notes,
      isPlanned: isPlanned,
      createdAt: DateTime.now(),
    );
  }

  /// Get color based on rest type
  int get typeColor {
    switch (type.toLowerCase()) {
      case 'active':
        return 0xFF90EE90; // Light Green
      case 'complete':
        return 0xFF708090; // Slate Gray
      case 'recovery':
        return 0xFF87CEEB; // Sky Blue
      default:
        return 0xFF708090; // Slate Gray
    }
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'date': date.toIso8601String(),
      'type': type,
      'notes': notes,
      'isPlanned': isPlanned,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  /// Create from JSON
  factory RestDay.fromJson(Map<String, dynamic> json) {
    return RestDay(
      id: json['id'] as String,
      date: DateTime.parse(json['date'] as String),
      type: json['type'] as String,
      notes: json['notes'] as String,
      isPlanned: json['isPlanned'] as bool,
      createdAt: DateTime.parse(json['createdAt'] as String),
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is RestDay && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// Training calendar data model
@immutable
class TrainingCalendar {
  final DateTime month;
  final List<PlannedSession> plannedSessions;
  final List<RestDay> restDays;
  final Map<String, dynamic> monthlyGoals;

  const TrainingCalendar({
    required this.month,
    required this.plannedSessions,
    required this.restDays,
    required this.monthlyGoals,
  });

  /// Create empty calendar for a month
  factory TrainingCalendar.forMonth(DateTime month) {
    return TrainingCalendar(
      month: DateTime(month.year, month.month, 1),
      plannedSessions: [],
      restDays: [],
      monthlyGoals: {},
    );
  }

  /// Get sessions for a specific date
  List<PlannedSession> getSessionsForDate(DateTime date) {
    final dateOnly = DateTime(date.year, date.month, date.day);
    return plannedSessions.where((session) {
      final sessionDate = DateTime(
        session.plannedDate.year,
        session.plannedDate.month,
        session.plannedDate.day,
      );
      return sessionDate == dateOnly;
    }).toList();
  }

  /// Get rest day for a specific date
  RestDay? getRestDayForDate(DateTime date) {
    final dateOnly = DateTime(date.year, date.month, date.day);
    try {
      return restDays.firstWhere((restDay) {
        final restDate = DateTime(
          restDay.date.year,
          restDay.date.month,
          restDay.date.day,
        );
        return restDate == dateOnly;
      });
    } catch (e) {
      return null;
    }
  }

  /// Check if date has any planned activities
  bool hasActivityOnDate(DateTime date) {
    return getSessionsForDate(date).isNotEmpty || getRestDayForDate(date) != null;
  }

  /// Get sessions for a specific date sorted by time slot
  List<PlannedSession> getSessionsForDateSorted(DateTime date) {
    final sessions = getSessionsForDate(date);
    sessions.sort((a, b) => a.timeSlot.compareTo(b.timeSlot));
    return sessions;
  }

  /// Get available time slots for a date (0-3, up to 4 sessions per day)
  List<int> getAvailableTimeSlotsForDate(DateTime date) {
    final sessions = getSessionsForDate(date);
    final usedSlots = sessions.map((s) => s.timeSlot).toSet();
    return [0, 1, 2, 3].where((slot) => !usedSlots.contains(slot)).toList();
  }

  /// Check if a time slot is available for a date
  bool isTimeSlotAvailable(DateTime date, int timeSlot) {
    final sessions = getSessionsForDate(date);
    return !sessions.any((s) => s.timeSlot == timeSlot);
  }

  /// Get next available time slot for a date
  int? getNextAvailableTimeSlot(DateTime date) {
    final availableSlots = getAvailableTimeSlotsForDate(date);
    return availableSlots.isEmpty ? null : availableSlots.first;
  }

  /// Get session count for a date
  int getSessionCountForDate(DateTime date) {
    return getSessionsForDate(date).length;
  }

  /// Check if date is fully booked (4 sessions)
  bool isDateFullyBooked(DateTime date) {
    return getSessionCountForDate(date) >= 4;
  }

  /// Add planned session
  TrainingCalendar addPlannedSession(PlannedSession session) {
    final updatedSessions = List<PlannedSession>.from(plannedSessions)
      ..add(session);
    
    return TrainingCalendar(
      month: month,
      plannedSessions: updatedSessions,
      restDays: restDays,
      monthlyGoals: monthlyGoals,
    );
  }

  /// Remove planned session
  TrainingCalendar removePlannedSession(String sessionId) {
    final updatedSessions = plannedSessions
        .where((session) => session.id != sessionId)
        .toList();
    
    return TrainingCalendar(
      month: month,
      plannedSessions: updatedSessions,
      restDays: restDays,
      monthlyGoals: monthlyGoals,
    );
  }

  /// Add rest day
  TrainingCalendar addRestDay(RestDay restDay) {
    final updatedRestDays = List<RestDay>.from(restDays)
      ..add(restDay);
    
    return TrainingCalendar(
      month: month,
      plannedSessions: plannedSessions,
      restDays: updatedRestDays,
      monthlyGoals: monthlyGoals,
    );
  }

  /// Remove rest day
  TrainingCalendar removeRestDay(String restDayId) {
    final updatedRestDays = restDays
        .where((restDay) => restDay.id != restDayId)
        .toList();
    
    return TrainingCalendar(
      month: month,
      plannedSessions: plannedSessions,
      restDays: updatedRestDays,
      monthlyGoals: monthlyGoals,
    );
  }

  /// Update monthly goals
  TrainingCalendar updateMonthlyGoals(Map<String, dynamic> goals) {
    return TrainingCalendar(
      month: month,
      plannedSessions: plannedSessions,
      restDays: restDays,
      monthlyGoals: goals,
    );
  }

  /// Get completion statistics
  Map<String, dynamic> get completionStats {
    final totalPlanned = plannedSessions.length;
    final completed = plannedSessions.where((s) => s.isCompleted).length;
    final completionRate = totalPlanned > 0 ? (completed / totalPlanned) * 100 : 0.0;
    
    return {
      'totalPlanned': totalPlanned,
      'completed': completed,
      'remaining': totalPlanned - completed,
      'completionRate': completionRate,
    };
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'month': month.toIso8601String(),
      'plannedSessions': plannedSessions.map((s) => s.toJson()).toList(),
      'restDays': restDays.map((r) => r.toJson()).toList(),
      'monthlyGoals': monthlyGoals,
    };
  }

  /// Create from JSON
  factory TrainingCalendar.fromJson(Map<String, dynamic> json) {
    return TrainingCalendar(
      month: DateTime.parse(json['month'] as String),
      plannedSessions: (json['plannedSessions'] as List)
          .map((s) => PlannedSession.fromJson(s as Map<String, dynamic>))
          .toList(),
      restDays: (json['restDays'] as List)
          .map((r) => RestDay.fromJson(r as Map<String, dynamic>))
          .toList(),
      monthlyGoals: json['monthlyGoals'] as Map<String, dynamic>? ?? {},
    );
  }
}
