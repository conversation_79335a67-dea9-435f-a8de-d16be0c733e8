// lib/training_tracker/services/training_analytics_service.dart

import 'dart:convert';
import '../../services/bulletproof_storage_service.dart';
import '../../services/comprehensive_logging_service.dart';
import '../../models/training_session_model.dart';
import '../models/training_analytics_model.dart';
import '../training_tracker_config.dart';

/// Service for managing training analytics and insights
class TrainingAnalyticsService {
  static final TrainingAnalyticsService _instance = TrainingAnalyticsService._internal();
  factory TrainingAnalyticsService() => _instance;
  TrainingAnalyticsService._internal();

  static final BulletproofStorageService _storage = BulletproofStorageService();
  static const String _analyticsKey = 'training_analytics_cache';
  static const String _personalRecordsKey = 'training_personal_records';
  static const String _insightsKey = 'training_insights';

  /// Generate comprehensive analytics for a date range
  Future<TrainingAnalytics> generateAnalytics({
    required List<TrainingSession> sessions,
    required DateTime startDate,
    required DateTime endDate,
    String period = 'custom',
  }) async {
    try {
      if (!TrainingTrackerConfig.analyticsEnabled) {
        throw Exception('Analytics feature is disabled');
      }

      await ComprehensiveLoggingService.logInfo('📊 Analytics: Generating $period analytics');

      final filteredSessions = sessions.where((session) {
        return session.createdAt.isAfter(startDate) && 
               session.createdAt.isBefore(endDate);
      }).toList();

      TrainingAnalytics analytics;
      
      switch (period) {
        case 'weekly':
          analytics = TrainingAnalytics.weekly(sessions, startDate);
          break;
        case 'monthly':
          analytics = TrainingAnalytics.monthly(sessions, startDate);
          break;
        default:
          analytics = TrainingAnalytics(
            startDate: startDate,
            endDate: endDate,
            sessions: filteredSessions,
            metrics: TrainingAnalytics.calculateMetrics(filteredSessions),
          );
      }

      // Cache analytics for performance
      await _cacheAnalytics(period, analytics);

      await ComprehensiveLoggingService.logInfo('✅ Analytics: Generated successfully');
      return analytics;
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Analytics: Generation failed: $e');
      rethrow;
    }
  }

  /// Get weekly analytics
  Future<TrainingAnalytics> getWeeklyAnalytics(List<TrainingSession> sessions) async {
    final now = DateTime.now();
    final weekStart = now.subtract(Duration(days: now.weekday - 1));
    
    return generateAnalytics(
      sessions: sessions,
      startDate: weekStart,
      endDate: weekStart.add(const Duration(days: 7)),
      period: 'weekly',
    );
  }

  /// Get monthly analytics
  Future<TrainingAnalytics> getMonthlyAnalytics(List<TrainingSession> sessions) async {
    final now = DateTime.now();
    final monthStart = DateTime(now.year, now.month, 1);
    
    return generateAnalytics(
      sessions: sessions,
      startDate: monthStart,
      endDate: DateTime(now.year, now.month + 1, 1),
      period: 'monthly',
    );
  }

  /// Get personal records
  Future<PersonalRecords> getPersonalRecords(List<TrainingSession> sessions) async {
    try {
      await ComprehensiveLoggingService.logInfo('🏆 Analytics: Calculating personal records');

      final records = PersonalRecords.fromSessions(sessions);
      
      // Cache records
      await _cachePersonalRecords(records);

      await ComprehensiveLoggingService.logInfo('✅ Analytics: Personal records calculated');
      return records;
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Analytics: Personal records calculation failed: $e');
      rethrow;
    }
  }

  /// Generate training insights
  Future<Map<String, dynamic>> generateInsights(List<TrainingSession> sessions) async {
    try {
      if (!TrainingTrackerConfig.analyticsEnabled) {
        return {'insights': [], 'recommendations': []};
      }

      await ComprehensiveLoggingService.logInfo('💡 Analytics: Generating insights');

      final insights = <String>[];
      final recommendations = <String>[];

      if (sessions.isEmpty) {
        insights.add('Start your training journey today!');
        recommendations.add('Begin with a 15-20 minute session to build the habit');
        return {'insights': insights, 'recommendations': recommendations};
      }

      final weeklyAnalytics = await getWeeklyAnalytics(sessions);
      final monthlyAnalytics = await getMonthlyAnalytics(sessions);
      final personalRecords = await getPersonalRecords(sessions);

      // Consistency insights
      if (weeklyAnalytics.consistency >= 80) {
        insights.add('🔥 Excellent consistency! You\'re training ${weeklyAnalytics.consistency.toStringAsFixed(0)}% of days');
      } else if (weeklyAnalytics.consistency >= 60) {
        insights.add('👍 Good consistency at ${weeklyAnalytics.consistency.toStringAsFixed(0)}%');
        recommendations.add('Try to add one more training day per week');
      } else {
        insights.add('📈 Room for improvement in consistency (${weeklyAnalytics.consistency.toStringAsFixed(0)}%)');
        recommendations.add('Start with 3 training days per week to build momentum');
      }

      // Duration insights
      if (weeklyAnalytics.averageDuration > 1800) { // 30+ minutes
        insights.add('💪 Great session lengths! Average: ${weeklyAnalytics.formattedAverageDuration}');
      } else if (weeklyAnalytics.averageDuration > 900) { // 15+ minutes
        insights.add('✅ Solid session durations averaging ${weeklyAnalytics.formattedAverageDuration}');
        recommendations.add('Consider gradually increasing session length for better results');
      } else {
        insights.add('⏱️ Short but effective sessions (${weeklyAnalytics.formattedAverageDuration} avg)');
        recommendations.add('Quality over quantity - keep building the habit!');
      }

      // Streak insights
      if (personalRecords.currentStreak >= 7) {
        insights.add('🔥 Amazing ${personalRecords.currentStreak}-day streak!');
      } else if (personalRecords.currentStreak >= 3) {
        insights.add('🌟 Building momentum with a ${personalRecords.currentStreak}-day streak');
      } else if (personalRecords.currentStreak == 0) {
        recommendations.add('Start a new streak today - consistency is key!');
      }

      // Most active day insight
      if (weeklyAnalytics.frequencyByDay.isNotEmpty) {
        final mostActiveDay = weeklyAnalytics.mostActiveDay;
        insights.add('📅 You\'re most active on ${mostActiveDay}s');
      }

      // Progress insights
      if (monthlyAnalytics.totalSessions > weeklyAnalytics.totalSessions * 3) {
        insights.add('📈 Training frequency is increasing this month!');
      }

      // EXP insights
      if (weeklyAnalytics.totalExp > 50) {
        insights.add('⭐ Earning great EXP this week: ${weeklyAnalytics.totalExp.toStringAsFixed(1)}');
      }

      // Personal records insights
      if (personalRecords.longestSessionDuration > 3600) { // 1+ hour
        insights.add('🏆 Impressive endurance with ${personalRecords.formattedLongestSession} longest session');
      }

      // Cache insights
      await _cacheInsights({'insights': insights, 'recommendations': recommendations});

      await ComprehensiveLoggingService.logInfo('✅ Analytics: Insights generated');
      return {'insights': insights, 'recommendations': recommendations};
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Analytics: Insights generation failed: $e');
      return {'insights': [], 'recommendations': []};
    }
  }

  /// Get training frequency heatmap data
  Future<Map<String, int>> getFrequencyHeatmap(List<TrainingSession> sessions, {int days = 30}) async {
    try {
      final now = DateTime.now();
      final startDate = now.subtract(Duration(days: days));
      
      final heatmapData = <String, int>{};
      
      // Initialize all days with 0
      for (int i = 0; i < days; i++) {
        final date = startDate.add(Duration(days: i));
        final dateKey = '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
        heatmapData[dateKey] = 0;
      }
      
      // Count sessions per day
      for (final session in sessions) {
        if (session.createdAt.isAfter(startDate)) {
          final dateKey = '${session.createdAt.year}-${session.createdAt.month.toString().padLeft(2, '0')}-${session.createdAt.day.toString().padLeft(2, '0')}';
          heatmapData[dateKey] = (heatmapData[dateKey] ?? 0) + 1;
        }
      }
      
      return heatmapData;
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Analytics: Heatmap generation failed: $e');
      return {};
    }
  }

  /// Get trend analysis
  Future<Map<String, List<double>>> getTrendAnalysis(List<TrainingSession> sessions, {int weeks = 8}) async {
    try {
      final trends = <String, List<double>>{
        'duration': [],
        'exp': [],
        'frequency': [],
      };
      
      final now = DateTime.now();
      
      for (int i = weeks - 1; i >= 0; i--) {
        final weekStart = now.subtract(Duration(days: (i * 7) + now.weekday - 1));
        final weekEnd = weekStart.add(const Duration(days: 7));
        
        final weekSessions = sessions.where((session) {
          return session.createdAt.isAfter(weekStart) && session.createdAt.isBefore(weekEnd);
        }).toList();
        
        final totalDuration = weekSessions.fold<int>(0, (sum, session) => sum + session.durationSeconds);
        final totalExp = weekSessions.fold<double>(0, (sum, session) => sum + session.expEarned);
        final frequency = weekSessions.length;
        
        trends['duration']!.add(totalDuration / 60.0); // Convert to minutes
        trends['exp']!.add(totalExp);
        trends['frequency']!.add(frequency.toDouble());
      }
      
      return trends;
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Analytics: Trend analysis failed: $e');
      return {'duration': [], 'exp': [], 'frequency': []};
    }
  }

  /// Cache analytics data
  Future<void> _cacheAnalytics(String period, TrainingAnalytics analytics) async {
    try {
      final cacheData = await _storage.read(_analyticsKey);
      final cache = cacheData != null ? jsonDecode(cacheData) as Map<String, dynamic> : <String, dynamic>{};
      
      cache[period] = {
        'data': analytics.toJson(),
        'timestamp': DateTime.now().toIso8601String(),
      };
      
      await _storage.write(_analyticsKey, jsonEncode(cache));
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Analytics: Cache write failed: $e');
    }
  }

  /// Cache personal records
  Future<void> _cachePersonalRecords(PersonalRecords records) async {
    try {
      final recordsData = {
        'data': records.toJson(),
        'timestamp': DateTime.now().toIso8601String(),
      };
      
      await _storage.write(_personalRecordsKey, jsonEncode(recordsData));
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Analytics: Personal records cache failed: $e');
    }
  }

  /// Cache insights
  Future<void> _cacheInsights(Map<String, dynamic> insights) async {
    try {
      final insightsData = {
        'data': insights,
        'timestamp': DateTime.now().toIso8601String(),
      };
      
      await _storage.write(_insightsKey, jsonEncode(insightsData));
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Analytics: Insights cache failed: $e');
    }
  }

  /// Clear analytics cache
  Future<void> clearCache() async {
    try {
      await _storage.delete(_analyticsKey);
      await _storage.delete(_personalRecordsKey);
      await _storage.delete(_insightsKey);
      
      await ComprehensiveLoggingService.logInfo('🧹 Analytics: Cache cleared');
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Analytics: Cache clear failed: $e');
    }
  }
}
