import 'dart:convert';
import '../../services/bulletproof_storage_service.dart';
import '../../services/comprehensive_logging_service.dart';
import '../models/training_calendar_model.dart';
import '../../models/training_timer_model.dart';
import 'package:uuid/uuid.dart';

/// Comprehensive Training Calendar Service
/// Manages planned sessions, rest days, and calendar data
class TrainingCalendarService {
  static final TrainingCalendarService _instance = TrainingCalendarService._internal();
  factory TrainingCalendarService() => _instance;
  TrainingCalendarService._internal();

  final BulletproofStorageService _storage = BulletproofStorageService();
  static const String _plannedSessionsKey = 'planned_training_sessions';
  static const String _restDaysKey = 'planned_rest_days';

  /// Load calendar data for a specific month
  Future<TrainingCalendar> getCalendarForMonth(DateTime month) async {
    try {
      final monthKey = '${month.year}-${month.month.toString().padLeft(2, '0')}';
      await ComprehensiveLoggingService.logInfo('📅 TrainingCalendar: Loading calendar for $monthKey');

      // Load planned sessions for the month
      final plannedSessions = await _getPlannedSessionsForMonth(month);
      
      // Load rest days for the month
      final restDays = await _getRestDaysForMonth(month);
      
      // Load monthly goals
      final monthlyGoals = await _getMonthlyGoals(month);

      final calendar = TrainingCalendar(
        month: DateTime(month.year, month.month, 1),
        plannedSessions: plannedSessions,
        restDays: restDays,
        monthlyGoals: monthlyGoals,
      );

      await ComprehensiveLoggingService.logInfo('📅 TrainingCalendar: Loaded ${plannedSessions.length} sessions, ${restDays.length} rest days');
      return calendar;
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ TrainingCalendar: Error loading calendar: $e');
      return TrainingCalendar.forMonth(month);
    }
  }

  /// Save a planned training session
  Future<bool> savePlannedSession(PlannedSession session) async {
    try {
      await ComprehensiveLoggingService.logInfo('💾 TrainingCalendar: Saving planned session for ${session.plannedDate}');

      final sessions = await _getAllPlannedSessions();
      
      // Remove any existing session with the same ID
      sessions.removeWhere((s) => s.id == session.id);
      
      // Add the new/updated session
      sessions.add(session);
      
      // Sort by planned date
      sessions.sort((a, b) => a.plannedDate.compareTo(b.plannedDate));

      final success = await _savePlannedSessions(sessions);
      
      if (success) {
        await ComprehensiveLoggingService.logInfo('✅ TrainingCalendar: Planned session saved successfully');
      }
      
      return success;
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ TrainingCalendar: Error saving planned session: $e');
      return false;
    }
  }

  /// Save a rest day
  Future<bool> saveRestDay(RestDay restDay) async {
    try {
      await ComprehensiveLoggingService.logInfo('💾 TrainingCalendar: Saving rest day for ${restDay.date}');

      final restDays = await _getAllRestDays();
      
      // Remove any existing rest day for the same date
      restDays.removeWhere((r) => _isSameDay(r.date, restDay.date));
      
      // Add the new rest day
      restDays.add(restDay);
      
      // Sort by date
      restDays.sort((a, b) => a.date.compareTo(b.date));

      final success = await _saveRestDays(restDays);
      
      if (success) {
        await ComprehensiveLoggingService.logInfo('✅ TrainingCalendar: Rest day saved successfully');
      }
      
      return success;
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ TrainingCalendar: Error saving rest day: $e');
      return false;
    }
  }

  /// Mark a planned session as completed
  Future<bool> markSessionCompleted(String plannedSessionId, String completedSessionId) async {
    try {
      await ComprehensiveLoggingService.logInfo('✅ TrainingCalendar: Marking session $plannedSessionId as completed');

      final sessions = await _getAllPlannedSessions();
      final sessionIndex = sessions.indexWhere((s) => s.id == plannedSessionId);
      
      if (sessionIndex == -1) {
        await ComprehensiveLoggingService.logError('❌ TrainingCalendar: Planned session not found');
        return false;
      }

      final updatedSession = sessions[sessionIndex].markCompleted(completedSessionId);
      sessions[sessionIndex] = updatedSession;

      final success = await _savePlannedSessions(sessions);
      
      if (success) {
        await ComprehensiveLoggingService.logInfo('✅ TrainingCalendar: Session marked as completed');
      }
      
      return success;
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ TrainingCalendar: Error marking session completed: $e');
      return false;
    }
  }

  /// Delete a planned session
  Future<bool> deletePlannedSession(String sessionId) async {
    try {
      await ComprehensiveLoggingService.logInfo('🗑️ TrainingCalendar: Deleting planned session $sessionId');

      final sessions = await _getAllPlannedSessions();
      final initialCount = sessions.length;
      sessions.removeWhere((s) => s.id == sessionId);
      
      if (sessions.length == initialCount) {
        await ComprehensiveLoggingService.logError('❌ TrainingCalendar: Planned session not found');
        return false;
      }

      final success = await _savePlannedSessions(sessions);
      
      if (success) {
        await ComprehensiveLoggingService.logInfo('✅ TrainingCalendar: Planned session deleted');
      }
      
      return success;
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ TrainingCalendar: Error deleting planned session: $e');
      return false;
    }
  }

  /// Delete a rest day
  Future<bool> deleteRestDay(DateTime date) async {
    try {
      await ComprehensiveLoggingService.logInfo('🗑️ TrainingCalendar: Deleting rest day for $date');

      final restDays = await _getAllRestDays();
      final initialCount = restDays.length;
      restDays.removeWhere((r) => _isSameDay(r.date, date));
      
      if (restDays.length == initialCount) {
        await ComprehensiveLoggingService.logError('❌ TrainingCalendar: Rest day not found');
        return false;
      }

      final success = await _saveRestDays(restDays);
      
      if (success) {
        await ComprehensiveLoggingService.logInfo('✅ TrainingCalendar: Rest day deleted');
      }
      
      return success;
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ TrainingCalendar: Error deleting rest day: $e');
      return false;
    }
  }

  /// Get sessions for a specific date
  Future<List<PlannedSession>> getSessionsForDate(DateTime date) async {
    try {
      final sessions = await _getAllPlannedSessions();
      return sessions.where((s) => _isSameDay(s.plannedDate, date)).toList();
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ TrainingCalendar: Error getting sessions for date: $e');
      return [];
    }
  }

  /// Get rest day for a specific date
  Future<RestDay?> getRestDayForDate(DateTime date) async {
    try {
      final restDays = await _getAllRestDays();
      return restDays.where((r) => _isSameDay(r.date, date)).firstOrNull;
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ TrainingCalendar: Error getting rest day for date: $e');
      return null;
    }
  }

  /// Check if a date has any planned activities
  Future<bool> hasActivityOnDate(DateTime date) async {
    try {
      final sessions = await getSessionsForDate(date);
      final restDay = await getRestDayForDate(date);
      return sessions.isNotEmpty || restDay != null;
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ TrainingCalendar: Error checking activity for date: $e');
      return false;
    }
  }

  /// Generate a quick training plan for the week
  Future<List<PlannedSession>> generateWeeklyPlan({
    required DateTime startDate,
    required List<String> workoutTypes,
    required Map<String, int> durationMinutes,
    required String intensity,
  }) async {
    try {
      await ComprehensiveLoggingService.logInfo('🎯 TrainingCalendar: Generating weekly plan starting ${startDate.toString().split(' ')[0]}');

      final sessions = <PlannedSession>[];
      
      for (int i = 0; i < workoutTypes.length && i < 7; i++) {
        final sessionDate = startDate.add(Duration(days: i));
        final workoutType = workoutTypes[i];
        final duration = durationMinutes[workoutType] ?? 60;
        
        final session = PlannedSession(
          id: const Uuid().v4(),
          plannedDate: sessionDate,
          workoutType: workoutType,
          label: workoutType,
          notes: 'Auto-generated weekly plan session',
          estimatedDuration: duration,
          intensity: intensity,
          createdAt: DateTime.now(),
        );
        
        sessions.add(session);
      }

      // Save all sessions
      for (final session in sessions) {
        await savePlannedSession(session);
      }

      await ComprehensiveLoggingService.logInfo('✅ TrainingCalendar: Generated ${sessions.length} sessions for weekly plan');
      return sessions;
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ TrainingCalendar: Error generating weekly plan: $e');
      return [];
    }
  }

  // Private helper methods
  Future<List<PlannedSession>> _getPlannedSessionsForMonth(DateTime month) async {
    final sessions = await _getAllPlannedSessions();
    return sessions.where((s) => s.plannedDate.year == month.year && s.plannedDate.month == month.month).toList();
  }

  Future<List<RestDay>> _getRestDaysForMonth(DateTime month) async {
    final restDays = await _getAllRestDays();
    return restDays.where((r) => r.date.year == month.year && r.date.month == month.month).toList();
  }

  Future<Map<String, dynamic>> _getMonthlyGoals(DateTime month) async {
    try {
      final monthKey = '${month.year}-${month.month.toString().padLeft(2, '0')}';
      final goalsKey = 'monthly_goals_$monthKey';
      final goalsData = await _storage.read(goalsKey);
      
      if (goalsData != null) {
        return jsonDecode(goalsData) as Map<String, dynamic>;
      }
      
      return {};
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ TrainingCalendar: Error loading monthly goals: $e');
      return {};
    }
  }

  Future<List<PlannedSession>> _getAllPlannedSessions() async {
    try {
      final sessionsData = await _storage.read(_plannedSessionsKey);
      if (sessionsData != null) {
        final sessionsList = jsonDecode(sessionsData) as List;
        return sessionsList.map((s) => PlannedSession.fromJson(s as Map<String, dynamic>)).toList();
      }
      return [];
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ TrainingCalendar: Error loading planned sessions: $e');
      return [];
    }
  }

  Future<List<RestDay>> _getAllRestDays() async {
    try {
      final restDaysData = await _storage.read(_restDaysKey);
      if (restDaysData != null) {
        final restDaysList = jsonDecode(restDaysData) as List;
        return restDaysList.map((r) => RestDay.fromJson(r as Map<String, dynamic>)).toList();
      }
      return [];
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ TrainingCalendar: Error loading rest days: $e');
      return [];
    }
  }

  Future<bool> _savePlannedSessions(List<PlannedSession> sessions) async {
    try {
      final sessionsJson = sessions.map((s) => s.toJson()).toList();
      return await _storage.write(_plannedSessionsKey, jsonEncode(sessionsJson));
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ TrainingCalendar: Error saving planned sessions: $e');
      return false;
    }
  }

  Future<bool> _saveRestDays(List<RestDay> restDays) async {
    try {
      final restDaysJson = restDays.map((r) => r.toJson()).toList();
      return await _storage.write(_restDaysKey, jsonEncode(restDaysJson));
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ TrainingCalendar: Error saving rest days: $e');
      return false;
    }
  }

  /// Automatically load training program schedule into calendar
  /// This loads the current training program's workout labels as planned sessions
  Future<bool> loadProgramScheduleIntoCalendar(TrainingProgram program, {
    DateTime? startDate,
    int daysToSchedule = 7,
    String defaultIntensity = 'Medium',
    int defaultDuration = 60,
  }) async {
    try {
      await ComprehensiveLoggingService.logInfo('📅 Loading program schedule into calendar: ${program.programType}');

      final scheduleStartDate = startDate ?? DateTime.now();
      final sessions = <PlannedSession>[];

      // Create planned sessions based on program labels
      for (int i = 0; i < daysToSchedule && i < program.workoutLabels.length; i++) {
        final sessionDate = scheduleStartDate.add(Duration(days: i));
        final workoutLabel = program.workoutLabels[i % program.workoutLabels.length];

        // Get notes template if available
        final notes = program.notesTemplates[workoutLabel] ?? 'Auto-scheduled from ${program.programType} program';

        // Determine workout type based on label
        String workoutType = _getWorkoutTypeFromLabel(workoutLabel);

        final session = PlannedSession.create(
          plannedDate: sessionDate,
          workoutType: workoutType,
          label: workoutLabel,
          notes: notes,
          estimatedDuration: defaultDuration,
          intensity: defaultIntensity,
          timeSlot: 0, // Default to morning slot
        );

        sessions.add(session);
      }

      // Save all sessions
      bool allSuccessful = true;
      for (final session in sessions) {
        final success = await savePlannedSession(session);
        if (!success) {
          allSuccessful = false;
          await ComprehensiveLoggingService.logError('❌ Failed to save session: ${session.label}');
        }
      }

      if (allSuccessful) {
        await ComprehensiveLoggingService.logInfo('✅ Successfully loaded ${sessions.length} sessions from program schedule');
      }

      return allSuccessful;
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Error loading program schedule: $e');
      return false;
    }
  }

  /// Helper method to determine workout type from program label
  String _getWorkoutTypeFromLabel(String label) {
    final lowerLabel = label.toLowerCase();

    // Map common labels to workout types
    if (lowerLabel.contains('cardio') || lowerLabel.contains('run') || lowerLabel.contains('bike')) {
      return 'Cardio';
    } else if (lowerLabel.contains('strength') || lowerLabel.contains('weight') || lowerLabel.contains('lift')) {
      return 'Strength';
    } else if (lowerLabel.contains('flex') || lowerLabel.contains('stretch') || lowerLabel.contains('yoga')) {
      return 'Flexibility';
    } else if (lowerLabel.contains('hiit') || lowerLabel.contains('interval')) {
      return 'HIIT';
    } else if (lowerLabel.contains('sport')) {
      return 'Sports';
    } else if (lowerLabel.contains('recovery') || lowerLabel.contains('rest')) {
      return 'Recovery';
    }

    // Default to Strength for letter-based programs (A, B, C, etc.)
    return 'Strength';
  }

  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year && date1.month == date2.month && date1.day == date2.day;
  }
}
