// lib/training_tracker/services/workout_template_service.dart

import 'dart:convert';
import '../../services/bulletproof_storage_service.dart';
import '../../services/comprehensive_logging_service.dart';
import '../models/workout_template_model.dart';
import '../training_tracker_config.dart';

/// Service for managing workout templates
class WorkoutTemplateService {
  static final WorkoutTemplateService _instance = WorkoutTemplateService._internal();
  factory WorkoutTemplateService() => _instance;
  WorkoutTemplateService._internal();

  static final BulletproofStorageService _storage = BulletproofStorageService();

  static const String _customTemplatesKey = 'custom_workout_templates';
  static const String _templateUsageKey = 'template_usage_stats';

  /// Get all available templates (pre-built + custom)
  Future<List<WorkoutTemplate>> getAllTemplates() async {
    try {
      if (!TrainingTrackerConfig.workoutTemplatesEnabled) {
        return [];
      }

      await ComprehensiveLoggingService.logInfo('📋 Templates: Loading all templates');

      final prebuiltTemplates = WorkoutTemplate.getPrebuiltTemplates();
      final customTemplates = await getCustomTemplates();

      final allTemplates = [...prebuiltTemplates, ...customTemplates];

      await ComprehensiveLoggingService.logInfo('✅ Templates: Loaded ${allTemplates.length} templates');
      return allTemplates;
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Templates: Failed to load templates: $e');
      return [];
    }
  }

  /// Get custom templates only
  Future<List<WorkoutTemplate>> getCustomTemplates() async {
    try {
      final data = await _storage.read(_customTemplatesKey);
      if (data == null) return [];

      final templatesJson = jsonDecode(data) as List;
      final templates = templatesJson
          .map((json) => WorkoutTemplate.fromJson(json as Map<String, dynamic>))
          .toList();

      return templates;
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Templates: Failed to load custom templates: $e');
      return [];
    }
  }

  /// Get templates by category
  Future<List<WorkoutTemplate>> getTemplatesByCategory(String category) async {
    try {
      final allTemplates = await getAllTemplates();
      return allTemplates.where((template) => 
          template.category.toLowerCase() == category.toLowerCase()).toList();
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Templates: Failed to filter by category: $e');
      return [];
    }
  }

  /// Get templates by difficulty
  Future<List<WorkoutTemplate>> getTemplatesByDifficulty(String difficulty) async {
    try {
      final allTemplates = await getAllTemplates();
      return allTemplates.where((template) => 
          template.difficulty.toLowerCase() == difficulty.toLowerCase()).toList();
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Templates: Failed to filter by difficulty: $e');
      return [];
    }
  }

  /// Search templates by name or tags
  Future<List<WorkoutTemplate>> searchTemplates(String query) async {
    try {
      final allTemplates = await getAllTemplates();
      final lowercaseQuery = query.toLowerCase();

      return allTemplates.where((template) {
        return template.name.toLowerCase().contains(lowercaseQuery) ||
               template.description.toLowerCase().contains(lowercaseQuery) ||
               template.tags.any((tag) => tag.toLowerCase().contains(lowercaseQuery));
      }).toList();
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Templates: Search failed: $e');
      return [];
    }
  }

  /// Save custom template
  Future<bool> saveCustomTemplate(WorkoutTemplate template) async {
    try {
      if (!TrainingTrackerConfig.workoutTemplatesEnabled) {
        throw Exception('Workout templates feature is disabled');
      }

      await ComprehensiveLoggingService.logInfo('💾 Templates: Saving custom template: ${template.name}');

      final customTemplates = await getCustomTemplates();
      
      // Check if template with same ID exists
      final existingIndex = customTemplates.indexWhere((t) => t.id == template.id);
      if (existingIndex != -1) {
        customTemplates[existingIndex] = template;
      } else {
        customTemplates.add(template);
      }

      final templatesJson = customTemplates.map((t) => t.toJson()).toList();
      final success = await _storage.write(_customTemplatesKey, jsonEncode(templatesJson));

      if (success) {
        await ComprehensiveLoggingService.logInfo('✅ Templates: Custom template saved successfully');
      }

      return success;
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Templates: Failed to save custom template: $e');
      return false;
    }
  }

  /// Delete custom template
  Future<bool> deleteCustomTemplate(String templateId) async {
    try {
      await ComprehensiveLoggingService.logInfo('🗑️ Templates: Deleting template: $templateId');

      final customTemplates = await getCustomTemplates();
      final updatedTemplates = customTemplates.where((t) => t.id != templateId).toList();

      final templatesJson = updatedTemplates.map((t) => t.toJson()).toList();
      final success = await _storage.write(_customTemplatesKey, jsonEncode(templatesJson));

      if (success) {
        await ComprehensiveLoggingService.logInfo('✅ Templates: Template deleted successfully');
      }

      return success;
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Templates: Failed to delete template: $e');
      return false;
    }
  }

  /// Record template usage
  Future<void> recordTemplateUsage(String templateId) async {
    try {
      await ComprehensiveLoggingService.logInfo('📊 Templates: Recording usage for: $templateId');

      // Update template usage count
      final allTemplates = await getAllTemplates();
      final template = allTemplates.firstWhere((t) => t.id == templateId);
      final updatedTemplate = template.copyWithUsage();

      if (!template.isPrebuilt) {
        await saveCustomTemplate(updatedTemplate);
      }

      // Record usage statistics
      await _recordUsageStats(templateId);

      await ComprehensiveLoggingService.logInfo('✅ Templates: Usage recorded');
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Templates: Failed to record usage: $e');
    }
  }

  /// Get template usage statistics
  Future<Map<String, dynamic>> getUsageStatistics() async {
    try {
      final data = await _storage.read(_templateUsageKey);
      if (data == null) return {};

      return jsonDecode(data) as Map<String, dynamic>;
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Templates: Failed to get usage stats: $e');
      return {};
    }
  }

  /// Get most used templates
  Future<List<WorkoutTemplate>> getMostUsedTemplates({int limit = 5}) async {
    try {
      final allTemplates = await getAllTemplates();
      
      // Sort by usage count
      allTemplates.sort((a, b) => b.usageCount.compareTo(a.usageCount));
      
      return allTemplates.take(limit).toList();
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Templates: Failed to get most used templates: $e');
      return [];
    }
  }

  /// Get recently used templates
  Future<List<WorkoutTemplate>> getRecentlyUsedTemplates({int limit = 5}) async {
    try {
      final allTemplates = await getAllTemplates();
      
      // Filter templates that have been used
      final usedTemplates = allTemplates.where((t) => t.lastUsed != null).toList();
      
      // Sort by last used date
      usedTemplates.sort((a, b) => b.lastUsed!.compareTo(a.lastUsed!));
      
      return usedTemplates.take(limit).toList();
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Templates: Failed to get recently used templates: $e');
      return [];
    }
  }

  /// Create template from training session
  Future<WorkoutTemplate?> createTemplateFromSession({
    required String sessionLabel,
    required String sessionNotes,
    required int sessionDuration,
    required String templateName,
    required String category,
    required String difficulty,
    List<String> tags = const [],
  }) async {
    try {
      await ComprehensiveLoggingService.logInfo('🔄 Templates: Creating template from session');

      // Parse exercises from session notes
      final exercises = _parseExercisesFromNotes(sessionNotes);

      final template = WorkoutTemplate.create(
        name: templateName,
        description: 'Created from training session: $sessionLabel',
        category: category,
        notes: sessionNotes,
        exercises: exercises,
        estimatedDuration: (sessionDuration / 60).round(),
        difficulty: difficulty,
        tags: tags,
      );

      final success = await saveCustomTemplate(template);
      if (success) {
        await ComprehensiveLoggingService.logInfo('✅ Templates: Template created from session');
        return template;
      }

      return null;
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Templates: Failed to create template from session: $e');
      return null;
    }
  }

  /// Export templates to JSON
  Future<String?> exportTemplates() async {
    try {
      final customTemplates = await getCustomTemplates();
      final exportData = {
        'templates': customTemplates.map((t) => t.toJson()).toList(),
        'exportDate': DateTime.now().toIso8601String(),
        'version': '1.0',
      };

      return jsonEncode(exportData);
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Templates: Export failed: $e');
      return null;
    }
  }

  /// Import templates from JSON
  Future<bool> importTemplates(String jsonData) async {
    try {
      final importData = jsonDecode(jsonData) as Map<String, dynamic>;
      final templatesJson = importData['templates'] as List;
      
      final importedTemplates = templatesJson
          .map((json) => WorkoutTemplate.fromJson(json as Map<String, dynamic>))
          .toList();

      // Save each imported template
      for (final template in importedTemplates) {
        await saveCustomTemplate(template);
      }

      await ComprehensiveLoggingService.logInfo('✅ Templates: Imported ${importedTemplates.length} templates');
      return true;
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Templates: Import failed: $e');
      return false;
    }
  }

  /// Get available categories
  Future<List<String>> getAvailableCategories() async {
    try {
      final allTemplates = await getAllTemplates();
      final categories = allTemplates.map((t) => t.category).toSet().toList();
      categories.sort();
      return categories;
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Templates: Failed to get categories: $e');
      return ['Strength', 'Cardio', 'Flexibility', 'HIIT', 'Yoga'];
    }
  }

  /// Record usage statistics
  Future<void> _recordUsageStats(String templateId) async {
    try {
      final stats = await getUsageStatistics();
      final today = DateTime.now().toIso8601String().split('T')[0];
      
      // Initialize stats structure if needed
      stats['daily'] ??= <String, dynamic>{};
      stats['templates'] ??= <String, dynamic>{};
      
      // Record daily usage
      stats['daily'][today] ??= <String, dynamic>{};
      stats['daily'][today][templateId] = (stats['daily'][today][templateId] ?? 0) + 1;
      
      // Record template total usage
      stats['templates'][templateId] = (stats['templates'][templateId] ?? 0) + 1;
      
      await _storage.write(_templateUsageKey, jsonEncode(stats));
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Templates: Failed to record usage stats: $e');
    }
  }

  /// Parse exercises from session notes
  List<String> _parseExercisesFromNotes(String notes) {
    final exercises = <String>[];
    final lines = notes.split('\n');
    
    for (final line in lines) {
      final trimmed = line.trim();
      if (trimmed.isNotEmpty && !trimmed.startsWith('//') && !trimmed.startsWith('#')) {
        // Simple heuristic: if line contains numbers or 'x', it's likely an exercise
        if (trimmed.contains(RegExp(r'\d')) || trimmed.toLowerCase().contains('x')) {
          exercises.add(trimmed);
        }
      }
    }
    
    // If no exercises found, create a generic one
    if (exercises.isEmpty) {
      exercises.add('Custom Exercise - See notes for details');
    }
    
    return exercises;
  }

  /// Clear all data
  Future<void> clearAllData() async {
    try {
      await _storage.delete(_customTemplatesKey);
      await _storage.delete(_templateUsageKey);
      
      await ComprehensiveLoggingService.logInfo('🧹 Templates: All data cleared');
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Templates: Failed to clear data: $e');
    }
  }
}
