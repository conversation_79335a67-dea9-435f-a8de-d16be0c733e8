import 'package:flutter/material.dart';
import '../models/training_calendar_model.dart';
import '../services/training_calendar_service.dart';
import '../../services/comprehensive_logging_service.dart';
import '../../services/robust_image_service.dart';
import 'dart:io';

/// Comprehensive Workout Scheduling Widget
/// Allows scheduling up to 4 workouts per day with labels and color coding
class WorkoutSchedulingWidget extends StatefulWidget {
  final DateTime selectedDate;
  final Color glowColor;
  final TrainingCalendar? calendar;
  final Function(PlannedSession) onSessionScheduled;
  final Function() onSchedulingComplete;
  final Function(String)? onSessionDeleted; // New callback for deletions

  const WorkoutSchedulingWidget({
    super.key,
    required this.selectedDate,
    required this.glowColor,
    required this.calendar,
    required this.onSessionScheduled,
    required this.onSchedulingComplete,
    this.onSessionDeleted,
  });

  @override
  State<WorkoutSchedulingWidget> createState() => _WorkoutSchedulingWidgetState();
}

class _WorkoutSchedulingWidgetState extends State<WorkoutSchedulingWidget> {
  final TrainingCalendarService _calendarService = TrainingCalendarService();
  final TextEditingController _labelController = TextEditingController();
  final TextEditingController _notesController = TextEditingController();


  String _selectedWorkoutType = 'Strength';
  String _selectedIntensity = 'Medium';
  int _estimatedDuration = 60;
  int _selectedTimeSlot = 0;
  bool _isScheduling = false;
  String? _selectedImagePath;

  // State management for deletions
  final Set<String> _deletingSessionIds = {}; // Track sessions being deleted
  TrainingCalendar? _localCalendar; // Local copy for immediate updates

  // Workout types with colors
  final Map<String, Color> _workoutTypes = {
    'Strength': const Color(0xFFFF4500), // Red Orange
    'Cardio': const Color(0xFF00BFFF), // Deep Sky Blue
    'Flexibility': const Color(0xFF32CD32), // Lime Green
    'HIIT': const Color(0xFFFF1493), // Deep Pink
    'Yoga': const Color(0xFF9370DB), // Medium Purple
    'Sports': const Color(0xFFFFD700), // Gold
    'Recovery': const Color(0xFF87CEEB), // Sky Blue
    'Custom': const Color(0xFF4CAF50), // Default Green
  };

  final List<String> _intensityLevels = ['Low', 'Medium', 'High'];
  final List<int> _durationOptions = [15, 30, 45, 60, 90, 120];

  @override
  void initState() {
    super.initState();
    _localCalendar = widget.calendar; // Initialize local copy
    _initializeTimeSlot();
  }

  /// Refresh local calendar data from the service
  Future<void> _refreshLocalCalendar() async {
    try {
      final updatedCalendar = await _calendarService.getCalendarForMonth(widget.selectedDate);
      if (mounted) {
        setState(() {
          _localCalendar = updatedCalendar;
        });
      }
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Error refreshing local calendar: $e');
    }
  }

  @override
  void dispose() {
    _labelController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  void _initializeTimeSlot() {
    if (widget.calendar != null) {
      final nextSlot = widget.calendar!.getNextAvailableTimeSlot(widget.selectedDate);
      if (nextSlot != null) {
        _selectedTimeSlot = nextSlot;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.85,
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.95),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
        border: Border.all(
          color: widget.glowColor.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          _buildHeader(),
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildExistingSessionsSection(),
                  const SizedBox(height: 20),
                  _buildSchedulingSection(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: widget.glowColor.withValues(alpha: 0.1),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.schedule,
            color: widget.glowColor,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Workout Scheduling',
                  style: TextStyle(
                    color: widget.glowColor,
                    fontFamily: 'Pirulen',
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  _formatDate(widget.selectedDate),
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.8),
                    fontFamily: 'Bitsumishi',
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(
              Icons.close,
              color: Colors.white,
              size: 24,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExistingSessionsSection() {
    final sessions = _localCalendar?.getSessionsForDateSorted(widget.selectedDate) ?? [];
    final availableSlots = _localCalendar?.getAvailableTimeSlotsForDate(widget.selectedDate) ?? [0, 1, 2, 3];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Scheduled Workouts',
              style: TextStyle(
                color: widget.glowColor,
                fontFamily: 'Pirulen',
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(width: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
              decoration: BoxDecoration(
                color: widget.glowColor.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Text(
                '${sessions.length}/4',
                style: TextStyle(
                  color: widget.glowColor,
                  fontFamily: 'Bitsumishi',
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        if (sessions.isEmpty)
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: Colors.grey.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.event_available,
                  color: Colors.grey.withValues(alpha: 0.7),
                  size: 20,
                ),
                const SizedBox(width: 12),
                Text(
                  'No workouts scheduled for this day',
                  style: TextStyle(
                    color: Colors.grey.withValues(alpha: 0.7),
                    fontFamily: 'Bitsumishi',
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          )
        else
          ...sessions.map((session) => _buildSessionCard(session)),
        if (availableSlots.isNotEmpty) ...[
          const SizedBox(height: 12),
          Text(
            'Available Slots: ${availableSlots.length}',
            style: TextStyle(
              color: Colors.green.withValues(alpha: 0.8),
              fontFamily: 'Bitsumishi',
              fontSize: 12,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildSessionCard(PlannedSession session) {
    final isDeleting = _deletingSessionIds.contains(session.id);

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isDeleting
            ? Colors.grey.withValues(alpha: 0.05)
            : session.workoutColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isDeleting
              ? Colors.grey.withValues(alpha: 0.2)
              : session.workoutColor.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 4,
            height: 40,
            decoration: BoxDecoration(
              color: session.workoutColor,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Opacity(
              opacity: isDeleting ? 0.5 : 1.0,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                Row(
                  children: [
                    Text(
                      session.timeSlotLabel,
                      style: TextStyle(
                        color: session.workoutColor,
                        fontFamily: 'Pirulen',
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        session.label,
                        style: const TextStyle(
                          color: Colors.white,
                          fontFamily: 'Pirulen',
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  '${session.workoutType} • ${session.formattedDuration} • ${session.intensity}',
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.7),
                    fontFamily: 'Bitsumishi',
                    fontSize: 12,
                  ),
                ),
                if (session.notes.isNotEmpty) ...[
                  const SizedBox(height: 4),
                  Text(
                    session.notes,
                    style: TextStyle(
                      color: Colors.white.withValues(alpha: 0.6),
                      fontFamily: 'Bitsumishi',
                      fontSize: 11,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
                ],
              ),
            ),
          ),
          // Delete button
          IconButton(
            onPressed: _deletingSessionIds.contains(session.id)
                ? null
                : () => _deleteSession(session),
            icon: _deletingSessionIds.contains(session.id)
                ? SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: Colors.red.withValues(alpha: 0.8),
                    ),
                  )
                : Icon(
                    Icons.close,
                    color: Colors.red.withValues(alpha: 0.8),
                    size: 18,
                  ),
            padding: EdgeInsets.zero,
            constraints: BoxConstraints(
              minWidth: 32,
              minHeight: 32,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSchedulingSection() {
    final availableSlots = _localCalendar?.getAvailableTimeSlotsForDate(widget.selectedDate) ?? [0, 1, 2, 3];
    final isFullyBooked = _localCalendar?.isDateFullyBooked(widget.selectedDate) ?? false;

    if (isFullyBooked) {
      return _buildFullyBookedMessage();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Schedule New Workout',
          style: TextStyle(
            color: widget.glowColor,
            fontFamily: 'Pirulen',
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        _buildWorkoutTypeSelector(),
        const SizedBox(height: 16),
        _buildLabelInput(),
        const SizedBox(height: 16),
        _buildTimeSlotSelector(availableSlots),
        const SizedBox(height: 16),
        _buildDurationSelector(),
        const SizedBox(height: 16),
        _buildIntensitySelector(),
        const SizedBox(height: 16),
        _buildNotesInput(),
        const SizedBox(height: 16),
        _buildImagePicker(),
        const SizedBox(height: 24),
        _buildScheduleButton(),
      ],
    );
  }

  Widget _buildFullyBookedMessage() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.orange.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.orange.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.event_busy,
            color: Colors.orange,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Day Fully Booked',
                  style: TextStyle(
                    color: Colors.orange,
                    fontFamily: 'Pirulen',
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'You have reached the maximum of 4 workouts for this day. Consider rescheduling or removing a session.',
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.8),
                    fontFamily: 'Bitsumishi',
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWorkoutTypeSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Workout Type',
          style: TextStyle(
            color: widget.glowColor,
            fontFamily: 'Pirulen',
            fontSize: 12,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _workoutTypes.entries.map((entry) {
            final isSelected = _selectedWorkoutType == entry.key;
            return GestureDetector(
              onTap: () {
                setState(() {
                  _selectedWorkoutType = entry.key;
                });
              },
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color: isSelected
                      ? entry.value.withValues(alpha: 0.2)
                      : Colors.grey.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: isSelected
                        ? entry.value
                        : Colors.grey.withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      width: 12,
                      height: 12,
                      decoration: BoxDecoration(
                        color: entry.value,
                        shape: BoxShape.circle,
                      ),
                    ),
                    const SizedBox(width: 6),
                    Text(
                      entry.key,
                      style: TextStyle(
                        color: isSelected ? entry.value : Colors.white,
                        fontFamily: 'Bitsumishi',
                        fontSize: 12,
                        fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                      ),
                    ),
                  ],
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildLabelInput() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Workout Label',
          style: TextStyle(
            color: widget.glowColor,
            fontFamily: 'Pirulen',
            fontSize: 12,
          ),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: _labelController,
          style: const TextStyle(color: Colors.white),
          decoration: InputDecoration(
            hintText: 'e.g., Upper Body Strength, Morning Run',
            hintStyle: TextStyle(
              color: Colors.white.withValues(alpha: 0.5),
              fontFamily: 'Bitsumishi',
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: widget.glowColor.withValues(alpha: 0.3)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: widget.glowColor),
            ),
            filled: true,
            fillColor: Colors.grey.withValues(alpha: 0.1),
          ),
        ),
      ],
    );
  }

  Widget _buildTimeSlotSelector(List<int> availableSlots) {
    final timeSlotLabels = ['Morning', 'Afternoon', 'Evening', 'Night'];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Time Slot',
          style: TextStyle(
            color: widget.glowColor,
            fontFamily: 'Pirulen',
            fontSize: 12,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: List.generate(4, (index) {
            final isAvailable = availableSlots.contains(index);
            final isSelected = _selectedTimeSlot == index;

            return Expanded(
              child: GestureDetector(
                onTap: isAvailable ? () {
                  setState(() {
                    _selectedTimeSlot = index;
                  });
                } : null,
                child: Container(
                  margin: EdgeInsets.only(right: index < 3 ? 8 : 0),
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  decoration: BoxDecoration(
                    color: !isAvailable
                        ? Colors.red.withValues(alpha: 0.1)
                        : isSelected
                            ? widget.glowColor.withValues(alpha: 0.2)
                            : Colors.grey.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: !isAvailable
                          ? Colors.red.withValues(alpha: 0.3)
                          : isSelected
                              ? widget.glowColor
                              : Colors.grey.withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: Column(
                    children: [
                      Icon(
                        !isAvailable ? Icons.block : Icons.access_time,
                        color: !isAvailable
                            ? Colors.red.withValues(alpha: 0.7)
                            : isSelected
                                ? widget.glowColor
                                : Colors.white.withValues(alpha: 0.7),
                        size: 16,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        timeSlotLabels[index],
                        style: TextStyle(
                          color: !isAvailable
                              ? Colors.red.withValues(alpha: 0.7)
                              : isSelected
                                  ? widget.glowColor
                                  : Colors.white.withValues(alpha: 0.7),
                          fontFamily: 'Bitsumishi',
                          fontSize: 10,
                          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
            );
          }),
        ),
      ],
    );
  }

  Widget _buildDurationSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Duration',
          style: TextStyle(
            color: widget.glowColor,
            fontFamily: 'Pirulen',
            fontSize: 12,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _durationOptions.map((duration) {
            final isSelected = _estimatedDuration == duration;
            return GestureDetector(
              onTap: () {
                setState(() {
                  _estimatedDuration = duration;
                });
              },
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color: isSelected
                      ? widget.glowColor.withValues(alpha: 0.2)
                      : Colors.grey.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: isSelected
                        ? widget.glowColor
                        : Colors.grey.withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                child: Text(
                  '${duration}min',
                  style: TextStyle(
                    color: isSelected ? widget.glowColor : Colors.white,
                    fontFamily: 'Bitsumishi',
                    fontSize: 12,
                    fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildIntensitySelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Intensity',
          style: TextStyle(
            color: widget.glowColor,
            fontFamily: 'Pirulen',
            fontSize: 12,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: _intensityLevels.map((intensity) {
            final isSelected = _selectedIntensity == intensity;
            final intensityColor = _getIntensityColor(intensity);

            return Expanded(
              child: GestureDetector(
                onTap: () {
                  setState(() {
                    _selectedIntensity = intensity;
                  });
                },
                child: Container(
                  margin: EdgeInsets.only(right: intensity != _intensityLevels.last ? 8 : 0),
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  decoration: BoxDecoration(
                    color: isSelected
                        ? intensityColor.withValues(alpha: 0.2)
                        : Colors.grey.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: isSelected
                          ? intensityColor
                          : Colors.grey.withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: Text(
                    intensity,
                    style: TextStyle(
                      color: isSelected ? intensityColor : Colors.white,
                      fontFamily: 'Bitsumishi',
                      fontSize: 12,
                      fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildNotesInput() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Notes (Optional)',
          style: TextStyle(
            color: widget.glowColor,
            fontFamily: 'Pirulen',
            fontSize: 12,
          ),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: _notesController,
          style: const TextStyle(color: Colors.white),
          maxLines: 3,
          decoration: InputDecoration(
            hintText: 'Add any notes about this workout...',
            hintStyle: TextStyle(
              color: Colors.white.withValues(alpha: 0.5),
              fontFamily: 'Bitsumishi',
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: widget.glowColor.withValues(alpha: 0.3)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: widget.glowColor),
            ),
            filled: true,
            fillColor: Colors.grey.withValues(alpha: 0.1),
          ),
        ),
      ],
    );
  }

  Widget _buildImagePicker() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Workout Image (Optional)',
          style: TextStyle(
            color: widget.glowColor,
            fontFamily: 'Pirulen',
            fontSize: 12,
          ),
        ),
        const SizedBox(height: 8),
        GestureDetector(
          onTap: _pickImage,
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: _selectedImagePath != null
                  ? widget.glowColor.withValues(alpha: 0.1)
                  : Colors.grey.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: _selectedImagePath != null
                    ? widget.glowColor.withValues(alpha: 0.3)
                    : Colors.grey.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: _selectedImagePath != null
                ? Column(
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: Image.file(
                          File(_selectedImagePath!),
                          height: 120,
                          width: double.infinity,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              height: 120,
                              color: Colors.grey.withValues(alpha: 0.2),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.broken_image,
                                    color: Colors.grey,
                                    size: 32,
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    'Image not found',
                                    style: TextStyle(
                                      color: Colors.grey,
                                      fontSize: 12,
                                      fontFamily: 'Bitsumishi',
                                    ),
                                  ),
                                ],
                              ),
                            );
                          },
                        ),
                      ),
                      const SizedBox(height: 8),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.photo,
                            color: widget.glowColor,
                            size: 16,
                          ),
                          const SizedBox(width: 6),
                          Text(
                            'Tap to change image',
                            style: TextStyle(
                              color: widget.glowColor,
                              fontSize: 12,
                              fontFamily: 'Bitsumishi',
                            ),
                          ),
                        ],
                      ),
                    ],
                  )
                : Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.add_a_photo,
                        color: Colors.white70,
                        size: 24,
                      ),
                      const SizedBox(width: 12),
                      Text(
                        'Add workout image',
                        style: TextStyle(
                          color: Colors.white70,
                          fontSize: 14,
                          fontFamily: 'Bitsumishi',
                        ),
                      ),
                    ],
                  ),
          ),
        ),
      ],
    );
  }

  Widget _buildScheduleButton() {
    final availableSlots = widget.calendar?.getAvailableTimeSlotsForDate(widget.selectedDate) ?? [0, 1, 2, 3];
    final canSchedule = availableSlots.contains(_selectedTimeSlot) && !_isScheduling;

    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: canSchedule ? _scheduleWorkout : null,
        style: ElevatedButton.styleFrom(
          backgroundColor: widget.glowColor,
          disabledBackgroundColor: Colors.grey.withValues(alpha: 0.3),
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        child: _isScheduling
            ? Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.black),
                    ),
                  ),
                  const SizedBox(width: 8),
                  const Text(
                    'Scheduling...',
                    style: TextStyle(
                      color: Colors.black,
                      fontFamily: 'Pirulen',
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              )
            : Text(
                'Schedule Workout',
                style: TextStyle(
                  color: canSchedule ? Colors.black : Colors.grey,
                  fontFamily: 'Pirulen',
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
      ),
    );
  }

  Color _getIntensityColor(String intensity) {
    switch (intensity.toLowerCase()) {
      case 'low':
        return Colors.green;
      case 'medium':
        return Colors.orange;
      case 'high':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  Future<void> _scheduleWorkout() async {
    if (_isScheduling) return;

    try {
      setState(() {
        _isScheduling = true;
      });

      await ComprehensiveLoggingService.logInfo('📅 Scheduling workout for ${widget.selectedDate}');

      // Validate inputs
      if (_labelController.text.trim().isEmpty) {
        _labelController.text = _selectedWorkoutType;
      }

      // Create the planned session
      final session = PlannedSession.create(
        plannedDate: widget.selectedDate,
        workoutType: _selectedWorkoutType,
        label: _labelController.text.trim(),
        notes: _notesController.text.trim(),
        estimatedDuration: _estimatedDuration,
        intensity: _selectedIntensity,
        timeSlot: _selectedTimeSlot,
        workoutColorValue: (_workoutTypes[_selectedWorkoutType]?.toARGB32() ?? 0xFF4CAF50),
        imagePath: _selectedImagePath,
      );

      // Save the session
      final success = await _calendarService.savePlannedSession(session);

      if (success) {
        await ComprehensiveLoggingService.logInfo('✅ Workout scheduled successfully');

        // Notify parent
        widget.onSessionScheduled(session);

        // Reset form
        _resetForm();

        // Close if fully booked or complete scheduling
        final updatedCalendar = await _calendarService.getCalendarForMonth(widget.selectedDate);
        if (updatedCalendar.isDateFullyBooked(widget.selectedDate)) {
          widget.onSchedulingComplete();
          if (mounted) {
            Navigator.of(context).pop();
          }
        } else {
          setState(() {
            _initializeTimeSlot();
          });
        }
      } else {
        await ComprehensiveLoggingService.logError('❌ Failed to schedule workout');
        _showErrorMessage('Failed to schedule workout. Please try again.');
      }
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Error scheduling workout: $e');
      _showErrorMessage('Error scheduling workout: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isScheduling = false;
        });
      }
    }
  }

  void _resetForm() {
    _labelController.clear();
    _notesController.clear();
    _selectedWorkoutType = 'Strength';
    _selectedIntensity = 'Medium';
    _estimatedDuration = 60;
    _selectedImagePath = null;
  }

  /// Delete a scheduled session
  Future<void> _deleteSession(PlannedSession session) async {
    // Prevent multiple deletes of the same session
    if (_deletingSessionIds.contains(session.id)) {
      await ComprehensiveLoggingService.logInfo('🗑️ Session ${session.id} already being deleted, skipping');
      return;
    }

    try {
      setState(() {
        _deletingSessionIds.add(session.id);
      });

      await ComprehensiveLoggingService.logInfo('🗑️ Deleting scheduled session: ${session.label}');

      final success = await _calendarService.deletePlannedSession(session.id);

      if (success) {
        await ComprehensiveLoggingService.logInfo('✅ Session deleted successfully');

        // Refresh local calendar from service to ensure consistency
        await _refreshLocalCalendar();

        // Notify parent about deletion
        if (widget.onSessionDeleted != null) {
          widget.onSessionDeleted!(session.id);
        }

        // Reset form and update time slot
        _resetForm();
        _initializeTimeSlot();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Session deleted successfully',
                style: TextStyle(
                  fontFamily: 'Bitsumishi',
                  fontSize: 14,
                ),
              ),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 2),
            ),
          );
        }
      } else {
        await ComprehensiveLoggingService.logError('❌ Failed to delete session');
        _showErrorMessage('Failed to delete session. Please try again.');
      }
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Error deleting session: $e');
      _showErrorMessage('Error deleting session: $e');
    } finally {
      if (mounted) {
        setState(() {
          _deletingSessionIds.remove(session.id);
        });
      }
    }
  }

  void _showErrorMessage(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            message,
            style: const TextStyle(
              fontFamily: 'Bitsumishi',
              fontSize: 14,
            ),
          ),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  String _formatDate(DateTime date) {
    final months = [
      '', 'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    return '${months[date.month]} ${date.day}, ${date.year}';
  }

  /// Pick image using robust image service (completely avoids iOS photo picker)
  Future<void> _pickImage() async {
    try {
      await ComprehensiveLoggingService.logInfo('🛡️ Opening robust image picker for workout scheduling');

      final String? imagePath = await RobustImageService.selectImageSafely(
        context: 'workout_scheduling',
        allowCamera: false,
      );

      if (imagePath != null) {
        // Validate the selected image
        final bool isValid = await RobustImageService.validateImageFile(imagePath);

        if (isValid && mounted) {
          setState(() {
            _selectedImagePath = imagePath;
          });

          await ComprehensiveLoggingService.logInfo('✅ Image selected for workout: $imagePath');
        } else {
          await ComprehensiveLoggingService.logError('❌ Selected image file is not valid');
        }
      } else {
        await ComprehensiveLoggingService.logInfo('📸 Image selection cancelled');
      }
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Error picking image for workout: $e');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Failed to select image: $e',
              style: const TextStyle(
                fontFamily: 'Bitsumishi',
                fontSize: 14,
              ),
            ),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }
}
