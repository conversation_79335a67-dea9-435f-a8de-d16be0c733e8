// lib/training_tracker/widgets/enhanced_features_button.dart

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../training_tracker_enhanced.dart';
import '../training_tracker_config.dart';
import '../providers/training_tracker_theme_provider.dart';
import '../../models/training_session_model.dart';
import '../../services/comprehensive_logging_service.dart';

/// Button to access enhanced training tracker features
class EnhancedFeaturesButton extends StatelessWidget {
  final List<TrainingSession> sessions;
  final Color glowColor;

  const EnhancedFeaturesButton({
    super.key,
    required this.sessions,
    required this.glowColor,
  });

  @override
  Widget build(BuildContext context) {
    // Only show if any enhanced features are enabled
    if (!_hasEnabledFeatures()) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: ElevatedButton.icon(
        onPressed: () => _openEnhancedTracker(context),
        icon: Icon(
          Icons.rocket_launch,
          color: glowColor,
          size: 20,
        ),
        label: Text(
          'ADVANCED FEATURES',
          style: TextStyle(
            fontFamily: 'Pirulen',
            fontSize: 12,
            color: glowColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.black.withValues(alpha: 0.4),
          foregroundColor: glowColor,
          side: BorderSide(
            color: glowColor.withValues(alpha: 0.6),
            width: 2,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          padding: const EdgeInsets.symmetric(
            horizontal: 20,
            vertical: 12,
          ),
          elevation: 0,
        ),
      ),
    );
  }

  bool _hasEnabledFeatures() {
    return TrainingTrackerConfig.analyticsEnabled ||
           TrainingTrackerConfig.performanceMetricsEnabled ||
           TrainingTrackerConfig.calendarIntegrationEnabled ||
           TrainingTrackerConfig.workoutTemplatesEnabled ||
           TrainingTrackerConfig.multiPhaseTimerEnabled ||
           TrainingTrackerConfig.smartGoalsEnabled ||
           TrainingTrackerConfig.enhancedComparisonEnabled ||
           TrainingTrackerConfig.customizableInterfaceEnabled ||
           TrainingTrackerConfig.exportFeaturesEnabled ||
           TrainingTrackerConfig.competitionFeaturesEnabled;
  }

  void _openEnhancedTracker(BuildContext context) async {
    await ComprehensiveLoggingService.logInfo('🚀 Enhanced Features: Button pressed');

    if (!context.mounted) return;

    showDialog(
      context: context,
      builder: (context) => ChangeNotifierProvider(
        create: (context) => TrainingTrackerThemeProvider(),
        child: EnhancedTrainingTracker(
          sessions: sessions,
          glowColor: glowColor,
          onClose: () => Navigator.of(context).pop(),
        ),
      ),
    );
  }
}

/// Compact version for smaller spaces
class CompactEnhancedFeaturesButton extends StatelessWidget {
  final List<TrainingSession> sessions;
  final Color glowColor;

  const CompactEnhancedFeaturesButton({
    super.key,
    required this.sessions,
    required this.glowColor,
  });

  @override
  Widget build(BuildContext context) {
    if (!_hasEnabledFeatures()) {
      return const SizedBox.shrink();
    }

    return IconButton(
      onPressed: () => _openEnhancedTracker(context),
      icon: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: glowColor.withValues(alpha: 0.2),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: glowColor.withValues(alpha: 0.6),
            width: 1,
          ),
        ),
        child: Icon(
          Icons.rocket_launch,
          color: glowColor,
          size: 20,
        ),
      ),
      tooltip: 'Advanced Features',
    );
  }

  bool _hasEnabledFeatures() {
    return TrainingTrackerConfig.analyticsEnabled ||
           TrainingTrackerConfig.performanceMetricsEnabled ||
           TrainingTrackerConfig.calendarIntegrationEnabled ||
           TrainingTrackerConfig.workoutTemplatesEnabled ||
           TrainingTrackerConfig.multiPhaseTimerEnabled ||
           TrainingTrackerConfig.smartGoalsEnabled ||
           TrainingTrackerConfig.enhancedComparisonEnabled ||
           TrainingTrackerConfig.customizableInterfaceEnabled ||
           TrainingTrackerConfig.exportFeaturesEnabled ||
           TrainingTrackerConfig.competitionFeaturesEnabled;
  }

  void _openEnhancedTracker(BuildContext context) async {
    await ComprehensiveLoggingService.logInfo('🚀 Enhanced Features: Compact button pressed');

    if (!context.mounted) return;

    showDialog(
      context: context,
      builder: (context) => ChangeNotifierProvider(
        create: (context) => TrainingTrackerThemeProvider(),
        child: EnhancedTrainingTracker(
          sessions: sessions,
          glowColor: glowColor,
          onClose: () => Navigator.of(context).pop(),
        ),
      ),
    );
  }
}

/// Feature status indicator
class FeatureStatusIndicator extends StatelessWidget {
  final Color glowColor;

  const FeatureStatusIndicator({
    super.key,
    required this.glowColor,
  });

  @override
  Widget build(BuildContext context) {
    final enabledFeatures = TrainingTrackerConfig.getEnabledFeatures();
    
    if (enabledFeatures.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: glowColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: glowColor.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.new_releases,
            color: glowColor,
            size: 16,
          ),
          const SizedBox(width: 4),
          Text(
            '${enabledFeatures.length} NEW FEATURES',
            style: TextStyle(
              color: glowColor,
              fontSize: 10,
              fontFamily: 'Pirulen',
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}

/// Quick access menu for specific features
class QuickAccessMenu extends StatelessWidget {
  final List<TrainingSession> sessions;
  final Color glowColor;

  const QuickAccessMenu({
    super.key,
    required this.sessions,
    required this.glowColor,
  });

  @override
  Widget build(BuildContext context) {
    return PopupMenuButton<String>(
      icon: Icon(
        Icons.more_vert,
        color: glowColor,
      ),
      color: Colors.black.withValues(alpha: 0.9),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: glowColor.withValues(alpha: 0.3)),
      ),
      itemBuilder: (context) => [
        if (TrainingTrackerConfig.analyticsEnabled)
          _buildMenuItem('analytics', 'Analytics Dashboard', Icons.analytics),
        if (TrainingTrackerConfig.performanceMetricsEnabled)
          _buildMenuItem('performance', 'Performance Metrics', Icons.trending_up),
        if (TrainingTrackerConfig.calendarIntegrationEnabled)
          _buildMenuItem('calendar', 'Training Calendar', Icons.calendar_month),
        if (TrainingTrackerConfig.workoutTemplatesEnabled)
          _buildMenuItem('templates', 'Workout Templates', Icons.fitness_center),
        _buildMenuItem('all', 'All Features', Icons.rocket_launch),
      ],
      onSelected: (value) => _handleMenuSelection(context, value),
    );
  }

  PopupMenuItem<String> _buildMenuItem(String value, String text, IconData icon) {
    return PopupMenuItem<String>(
      value: value,
      child: Row(
        children: [
          Icon(icon, color: glowColor, size: 20),
          const SizedBox(width: 12),
          Text(
            text,
            style: TextStyle(
              color: Colors.white,
              fontFamily: 'Bitsumishi',
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  void _handleMenuSelection(BuildContext context, String value) async {
    await ComprehensiveLoggingService.logInfo('🚀 Quick Access: $value selected');

    if (!context.mounted) return;

    switch (value) {
      case 'analytics':
        // Open analytics directly
        break;
      case 'performance':
        // Open performance metrics directly
        break;
      case 'calendar':
        // Open calendar directly
        break;
      case 'templates':
        // Open templates directly
        break;
      case 'all':
      default:
        showDialog(
          context: context,
          builder: (context) => ChangeNotifierProvider(
            create: (context) => TrainingTrackerThemeProvider(),
            child: EnhancedTrainingTracker(
              sessions: sessions,
              glowColor: glowColor,
              onClose: () => Navigator.of(context).pop(),
            ),
          ),
        );
        break;
    }
  }
}
