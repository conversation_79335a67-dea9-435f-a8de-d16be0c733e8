// lib/training_tracker/widgets/performance_metrics_widget.dart

import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../models/performance_metrics_model.dart';
import '../training_tracker_config.dart';
import '../../models/training_session_model.dart';
import '../../services/comprehensive_logging_service.dart';

/// Performance Metrics Widget
class PerformanceMetricsWidget extends StatefulWidget {
  final List<TrainingSession> sessions;
  final Color glowColor;
  final VoidCallback onClose;

  const PerformanceMetricsWidget({
    super.key,
    required this.sessions,
    required this.glowColor,
    required this.onClose,
  });

  @override
  State<PerformanceMetricsWidget> createState() => _PerformanceMetricsWidgetState();
}

class _PerformanceMetricsWidgetState extends State<PerformanceMetricsWidget> {
  PerformanceMetrics? _metrics;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadMetrics();
  }

  Future<void> _loadMetrics() async {
    if (!TrainingTrackerConfig.performanceMetricsEnabled) {
      setState(() => _isLoading = false);
      return;
    }

    try {
      await ComprehensiveLoggingService.logInfo('📈 Performance: Loading metrics');

      _metrics = PerformanceMetrics.fromSessions(widget.sessions);

      setState(() => _isLoading = false);

      await ComprehensiveLoggingService.logInfo('✅ Performance: Metrics loaded successfully');
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Performance: Failed to load metrics: $e');
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.black.withValues(alpha: 0.95),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.95,
        height: MediaQuery.of(context).size.height * 0.85,
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: widget.glowColor.withValues(alpha: 0.3), width: 2),
          boxShadow: [
            BoxShadow(
              color: widget.glowColor.withValues(alpha: 0.2),
              blurRadius: 20,
              spreadRadius: 4,
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildHeader(),
            const SizedBox(height: 16),
            Expanded(child: _buildContent()),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 4),
      child: Row(
        children: [
          Icon(Icons.trending_up, color: widget.glowColor, size: 20),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'PERFORMANCE',
                  style: TextStyle(
                    color: widget.glowColor,
                    fontSize: 16,
                    fontFamily: 'Pirulen',
                    fontWeight: FontWeight.bold,
                    height: 1.0,
                  ),
                ),
                Text(
                  'METRICS',
                  style: TextStyle(
                    color: widget.glowColor,
                    fontSize: 16,
                    fontFamily: 'Pirulen',
                    fontWeight: FontWeight.bold,
                    height: 1.0,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: widget.onClose,
            icon: Icon(Icons.close, color: widget.glowColor, size: 20),
            constraints: const BoxConstraints(minWidth: 40, minHeight: 40),
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    if (_isLoading) {
      return Center(
        child: CircularProgressIndicator(color: widget.glowColor),
      );
    }

    if (!TrainingTrackerConfig.performanceMetricsEnabled) {
      return _buildFeatureDisabled();
    }

    return SingleChildScrollView(
      child: Column(
        children: [
          _buildMetricsOverview(),
          const SizedBox(height: 20),
          _buildStreakSection(),
          const SizedBox(height: 20),
          _buildBodyweightChart(),
          const SizedBox(height: 20),
          _buildCategoryPerformance(),
        ],
      ),
    );
  }

  Widget _buildFeatureDisabled() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.trending_up_outlined,
            color: Colors.white30,
            size: 64,
          ),
          const SizedBox(height: 16),
          Text(
            'Performance Metrics Coming Soon',
            style: TextStyle(
              color: Colors.white30,
              fontSize: 18,
              fontFamily: 'Pirulen',
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Advanced performance tracking will be available in a future update',
            style: TextStyle(
              color: Colors.white30,
              fontSize: 12,
              fontFamily: 'Bitsumishi',
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildMetricsOverview() {
    if (_metrics == null) {
      return const SizedBox.shrink();
    }

    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      crossAxisSpacing: 12,
      mainAxisSpacing: 12,
      childAspectRatio: 1.5,
      children: [
        _buildMetricCard(
          'Completion Rate',
          '${_metrics!.sessionCompletionRate.toStringAsFixed(1)}%',
          _metrics!.completionRating,
          Icons.check_circle,
          _getCompletionColor(_metrics!.sessionCompletionRate),
        ),
        _buildMetricCard(
          'Goal Achievement',
          '${_metrics!.goalAchievementRate.toStringAsFixed(1)}%',
          _metrics!.goalAchievementRating,
          Icons.flag,
          _getGoalColor(_metrics!.goalAchievementRate),
        ),
        _buildMetricCard(
          'Current Streak',
          '${_metrics!.currentStreak} days',
          _metrics!.streakStatus,
          Icons.local_fire_department,
          _getStreakColor(_metrics!.currentStreak),
        ),
        _buildMetricCard(
          'Bodyweight Trend',
          _metrics!.bodyweightTrend,
          '${_metrics!.bodyweightProgression.length} entries',
          Icons.monitor_weight,
          widget.glowColor,
        ),
      ],
    );
  }

  Widget _buildMetricCard(String title, String value, String subtitle, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.4),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3), width: 1),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 8),
          Text(
            title,
            style: TextStyle(
              color: color,
              fontSize: 10,
              fontFamily: 'Pirulen',
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 12,
              fontFamily: 'Digital-7',
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          Text(
            subtitle,
            style: const TextStyle(
              color: Colors.white70,
              fontSize: 10,
              fontFamily: 'Bitsumishi',
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildStreakSection() {
    if (_metrics == null) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.4),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: widget.glowColor.withValues(alpha: 0.3), width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'STREAK ANALYSIS',
            style: TextStyle(
              color: widget.glowColor,
              fontSize: 12,
              fontFamily: 'Pirulen',
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildStreakInfo(
                  'Current Streak',
                  '${_metrics!.currentStreak}',
                  'days',
                  _getStreakColor(_metrics!.currentStreak),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStreakInfo(
                  'Longest Streak',
                  '${_metrics!.longestStreak}',
                  'days',
                  widget.glowColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          _buildStreakProgress(),
        ],
      ),
    );
  }

  Widget _buildStreakInfo(String title, String value, String unit, Color color) {
    return Column(
      children: [
        Text(
          title,
          style: TextStyle(
            color: color,
            fontSize: 10,
            fontFamily: 'Pirulen',
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 4),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.baseline,
          textBaseline: TextBaseline.alphabetic,
          children: [
            Text(
              value,
              style: TextStyle(
                color: color,
                fontSize: 20,
                fontFamily: 'Digital-7',
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(width: 4),
            Text(
              unit,
              style: TextStyle(
                color: color.withValues(alpha: 0.7),
                fontSize: 10,
                fontFamily: 'Bitsumishi',
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStreakProgress() {
    if (_metrics == null) return const SizedBox.shrink();
    final progress = _metrics!.longestStreak > 0 ? _metrics!.currentStreak / _metrics!.longestStreak : 0.0;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Progress to Personal Best',
          style: TextStyle(
            color: widget.glowColor.withValues(alpha: 0.8),
            fontSize: 10,
            fontFamily: 'Bitsumishi',
          ),
        ),
        const SizedBox(height: 4),
        LinearProgressIndicator(
          value: progress.clamp(0.0, 1.0),
          backgroundColor: Colors.white.withValues(alpha: 0.1),
          valueColor: AlwaysStoppedAnimation<Color>(widget.glowColor),
        ),
      ],
    );
  }

  Widget _buildBodyweightChart() {
    if (_metrics == null || _metrics!.bodyweightProgression.length < 2) {
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.4),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: widget.glowColor.withValues(alpha: 0.3), width: 1),
        ),
        child: Column(
          children: [
            Text(
              'BODYWEIGHT PROGRESSION',
              style: TextStyle(
                color: widget.glowColor,
                fontSize: 12,
                fontFamily: 'Pirulen',
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'Not enough data for bodyweight tracking',
              style: TextStyle(
                color: Colors.white30,
                fontSize: 12,
                fontFamily: 'Bitsumishi',
              ),
            ),
          ],
        ),
      );
    }

    return Container(
      height: 200,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.4),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: widget.glowColor.withValues(alpha: 0.3), width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'BODYWEIGHT PROGRESSION',
            style: TextStyle(
              color: widget.glowColor,
              fontSize: 12,
              fontFamily: 'Pirulen',
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          Expanded(child: _buildBodyweightLineChart()),
        ],
      ),
    );
  }

  Widget _buildBodyweightLineChart() {
    if (_metrics == null) return const SizedBox.shrink();
    final spots = _metrics!.bodyweightProgression.asMap().entries.map((entry) {
      return FlSpot(entry.key.toDouble(), entry.value.weight);
    }).toList();

    return LineChart(
      LineChartData(
        gridData: FlGridData(
          show: true,
          drawVerticalLine: false,
          horizontalInterval: 5,
          getDrawingHorizontalLine: (value) {
            return FlLine(
              color: Colors.white.withValues(alpha: 0.1),
              strokeWidth: 1,
            );
          },
        ),
        titlesData: FlTitlesData(show: false),
        borderData: FlBorderData(show: false),
        lineBarsData: [
          LineChartBarData(
            spots: spots,
            isCurved: true,
            color: widget.glowColor,
            barWidth: 2,
            isStrokeCapRound: true,
            dotData: FlDotData(
              show: true,
              getDotPainter: (spot, percent, barData, index) {
                return FlDotCirclePainter(
                  radius: 3,
                  color: widget.glowColor,
                  strokeWidth: 1,
                  strokeColor: Colors.white,
                );
              },
            ),
            belowBarData: BarAreaData(
              show: true,
              color: widget.glowColor.withValues(alpha: 0.1),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryPerformance() {
    if (_metrics == null || _metrics!.categoryPerformance.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.4),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: widget.glowColor.withValues(alpha: 0.3), width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'CATEGORY PERFORMANCE',
            style: TextStyle(
              color: widget.glowColor,
              fontSize: 12,
              fontFamily: 'Pirulen',
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          ..._metrics!.categoryPerformance.entries.map((entry) {
            return Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      entry.key,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontFamily: 'Bitsumishi',
                      ),
                    ),
                  ),
                  Text(
                    '${entry.value.toStringAsFixed(1)} EXP',
                    style: TextStyle(
                      color: widget.glowColor,
                      fontSize: 12,
                      fontFamily: 'Digital-7',
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            );
          }),
        ],
      ),
    );
  }

  Color _getCompletionColor(double rate) {
    if (rate >= 90) return Colors.green;
    if (rate >= 75) return Colors.lightGreen;
    if (rate >= 60) return Colors.orange;
    return Colors.red;
  }

  Color _getGoalColor(double rate) {
    if (rate >= 80) return Colors.purple;
    if (rate >= 60) return Colors.blue;
    if (rate >= 40) return Colors.orange;
    return Colors.red;
  }

  Color _getStreakColor(int streak) {
    if (streak >= 30) return const Color(0xFFFFD700); // Gold
    if (streak >= 14) return Colors.orange;
    if (streak >= 7) return Colors.green;
    if (streak >= 3) return Colors.blue;
    return Colors.grey;
  }
}
