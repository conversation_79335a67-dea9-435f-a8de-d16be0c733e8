// lib/training_tracker/widgets/workout_templates_widget.dart

import 'package:flutter/material.dart';
import '../models/workout_template_model.dart';
import '../services/workout_template_service.dart';
import '../training_tracker_config.dart';
import '../../services/comprehensive_logging_service.dart';

/// Workout Templates Widget
class WorkoutTemplatesWidget extends StatefulWidget {
  final Color glowColor;
  final VoidCallback onClose;
  final Function(WorkoutTemplate)? onTemplateSelected;

  const WorkoutTemplatesWidget({
    super.key,
    required this.glowColor,
    required this.onClose,
    this.onTemplateSelected,
  });

  @override
  State<WorkoutTemplatesWidget> createState() => _WorkoutTemplatesWidgetState();
}

class _WorkoutTemplatesWidgetState extends State<WorkoutTemplatesWidget>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final WorkoutTemplateService _templateService = WorkoutTemplateService();
  
  List<WorkoutTemplate> _allTemplates = [];
  List<WorkoutTemplate> _filteredTemplates = [];
  List<String> _categories = [];
  String _selectedCategory = 'All';
  String _selectedDifficulty = 'All';
  String _searchQuery = '';
  bool _isLoading = true;

  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadTemplates();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadTemplates() async {
    if (!TrainingTrackerConfig.workoutTemplatesEnabled) {
      setState(() => _isLoading = false);
      return;
    }

    try {
      await ComprehensiveLoggingService.logInfo('📋 Templates: Loading templates widget');

      final templates = await _templateService.getAllTemplates();
      final categories = await _templateService.getAvailableCategories();

      setState(() {
        _allTemplates = templates;
        _filteredTemplates = templates;
        _categories = ['All', ...categories];
        _isLoading = false;
      });

      await ComprehensiveLoggingService.logInfo('✅ Templates: Widget loaded with ${templates.length} templates');
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Templates: Failed to load widget: $e');
      setState(() => _isLoading = false);
    }
  }

  void _applyFilters() {
    setState(() {
      _filteredTemplates = _allTemplates.where((template) {
        // Category filter
        if (_selectedCategory != 'All' && template.category != _selectedCategory) {
          return false;
        }
        
        // Difficulty filter
        if (_selectedDifficulty != 'All' && template.difficulty != _selectedDifficulty) {
          return false;
        }
        
        // Search filter
        if (_searchQuery.isNotEmpty) {
          final query = _searchQuery.toLowerCase();
          return template.name.toLowerCase().contains(query) ||
                 template.description.toLowerCase().contains(query) ||
                 template.tags.any((tag) => tag.toLowerCase().contains(query));
        }
        
        return true;
      }).toList();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.black.withValues(alpha: 0.95),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.95,
        height: MediaQuery.of(context).size.height * 0.9,
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: widget.glowColor.withValues(alpha: 0.3), width: 2),
          boxShadow: [
            BoxShadow(
              color: widget.glowColor.withValues(alpha: 0.2),
              blurRadius: 20,
              spreadRadius: 4,
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildHeader(),
            const SizedBox(height: 16),
            _buildSearchAndFilters(),
            const SizedBox(height: 16),
            _buildTabBar(),
            const SizedBox(height: 16),
            Expanded(child: _buildContent()),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 4),
      child: Row(
        children: [
          Icon(Icons.fitness_center, color: widget.glowColor, size: 20),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'WORKOUT',
                  style: TextStyle(
                    color: widget.glowColor,
                    fontSize: 16,
                    fontFamily: 'Pirulen',
                    fontWeight: FontWeight.bold,
                    height: 1.0,
                  ),
                ),
                Text(
                  'TEMPLATES',
                  style: TextStyle(
                    color: widget.glowColor,
                    fontSize: 16,
                    fontFamily: 'Pirulen',
                    fontWeight: FontWeight.bold,
                    height: 1.0,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: widget.onClose,
            icon: Icon(Icons.close, color: widget.glowColor, size: 20),
            constraints: const BoxConstraints(minWidth: 40, minHeight: 40),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchAndFilters() {
    return Column(
      children: [
        // Search bar
        TextField(
          controller: _searchController,
          style: const TextStyle(color: Colors.white, fontFamily: 'Bitsumishi'),
          decoration: InputDecoration(
            hintText: 'Search templates...',
            hintStyle: const TextStyle(color: Colors.white60),
            prefixIcon: Icon(Icons.search, color: widget.glowColor),
            filled: true,
            fillColor: Colors.black.withValues(alpha: 0.3),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: widget.glowColor.withValues(alpha: 0.3)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: widget.glowColor, width: 2),
            ),
          ),
          onChanged: (value) {
            _searchQuery = value;
            _applyFilters();
          },
        ),
        const SizedBox(height: 12),
        
        // Filter dropdowns
        Row(
          children: [
            Expanded(child: _buildCategoryFilter()),
            const SizedBox(width: 12),
            Expanded(child: _buildDifficultyFilter()),
          ],
        ),
      ],
    );
  }

  Widget _buildCategoryFilter() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: widget.glowColor.withValues(alpha: 0.3)),
      ),
      child: DropdownButton<String>(
        value: _selectedCategory,
        isExpanded: true,
        dropdownColor: Colors.black,
        style: const TextStyle(color: Colors.white, fontFamily: 'Bitsumishi'),
        underline: const SizedBox(),
        items: _categories.map((category) {
          return DropdownMenuItem(
            value: category,
            child: Text(category),
          );
        }).toList(),
        onChanged: (value) {
          setState(() => _selectedCategory = value!);
          _applyFilters();
        },
      ),
    );
  }

  Widget _buildDifficultyFilter() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: widget.glowColor.withValues(alpha: 0.3)),
      ),
      child: DropdownButton<String>(
        value: _selectedDifficulty,
        isExpanded: true,
        dropdownColor: Colors.black,
        style: const TextStyle(color: Colors.white, fontFamily: 'Bitsumishi'),
        underline: const SizedBox(),
        items: ['All', 'Beginner', 'Intermediate', 'Advanced'].map((difficulty) {
          return DropdownMenuItem(
            value: difficulty,
            child: Text(difficulty),
          );
        }).toList(),
        onChanged: (value) {
          setState(() => _selectedDifficulty = value!);
          _applyFilters();
        },
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.4),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: widget.glowColor.withValues(alpha: 0.3), width: 1),
      ),
      child: TabBar(
        controller: _tabController,
        indicator: BoxDecoration(
          color: widget.glowColor.withValues(alpha: 0.2),
          borderRadius: BorderRadius.circular(12),
        ),
        indicatorSize: TabBarIndicatorSize.tab,
        dividerColor: Colors.transparent,
        tabs: [
          Tab(
            child: FittedBox(
              fit: BoxFit.scaleDown,
              child: Text(
                'ALL',
                style: TextStyle(
                  fontFamily: 'Pirulen',
                  fontSize: 11,
                  color: widget.glowColor,
                ),
              ),
            ),
          ),
          Tab(
            child: FittedBox(
              fit: BoxFit.scaleDown,
              child: Text(
                'FAVORITES',
                style: TextStyle(
                  fontFamily: 'Pirulen',
                  fontSize: 10,
                  color: widget.glowColor,
                ),
              ),
            ),
          ),
          Tab(
            child: FittedBox(
              fit: BoxFit.scaleDown,
              child: Text(
                'CUSTOM',
                style: TextStyle(
                  fontFamily: 'Pirulen',
                  fontSize: 11,
                  color: widget.glowColor,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    if (_isLoading) {
      return Center(
        child: CircularProgressIndicator(color: widget.glowColor),
      );
    }

    if (!TrainingTrackerConfig.workoutTemplatesEnabled) {
      return _buildFeatureDisabled();
    }

    return TabBarView(
      controller: _tabController,
      physics: const BouncingScrollPhysics(),
      children: [
        _buildTemplatesList(_filteredTemplates),
        _buildFavoritesList(),
        _buildCustomTemplatesList(),
      ],
    );
  }

  Widget _buildFeatureDisabled() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.fitness_center_outlined,
            color: Colors.white30,
            size: 64,
          ),
          const SizedBox(height: 16),
          Text(
            'Workout Templates Coming Soon',
            style: TextStyle(
              color: Colors.white30,
              fontSize: 18,
              fontFamily: 'Pirulen',
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Pre-built and custom workout templates will be available in a future update',
            style: TextStyle(
              color: Colors.white30,
              fontSize: 12,
              fontFamily: 'Bitsumishi',
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildTemplatesList(List<WorkoutTemplate> templates) {
    if (templates.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              color: Colors.white30,
              size: 48,
            ),
            const SizedBox(height: 16),
            Text(
              'No templates found',
              style: TextStyle(
                color: Colors.white30,
                fontSize: 16,
                fontFamily: 'Bitsumishi',
              ),
            ),
            Text(
              'Try adjusting your search or filters',
              style: TextStyle(
                color: Colors.white30,
                fontSize: 12,
                fontFamily: 'Bitsumishi',
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: templates.length,
      itemBuilder: (context, index) {
        final template = templates[index];
        return _buildTemplateCard(template);
      },
    );
  }

  Widget _buildTemplateCard(WorkoutTemplate template) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.4),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withValues(alpha: 0.1), width: 1),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        template.name,
                        style: TextStyle(
                          color: widget.glowColor,
                          fontSize: 16,
                          fontFamily: 'Pirulen',
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        template.description,
                        style: const TextStyle(
                          color: Colors.white70,
                          fontSize: 12,
                          fontFamily: 'Bitsumishi',
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                Column(
                  children: [
                    _buildCategoryChip(template.category),
                    const SizedBox(height: 4),
                    _buildDifficultyChip(template.difficulty),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 12),
            
            // Stats
            Row(
              children: [
                _buildStatChip(Icons.timer, template.formattedDuration),
                const SizedBox(width: 8),
                _buildStatChip(Icons.fitness_center, '${template.exercises.length} exercises'),
                if (template.usageCount > 0) ...[
                  const SizedBox(width: 8),
                  _buildStatChip(Icons.star, '${template.usageCount} uses'),
                ],
              ],
            ),
            const SizedBox(height: 12),
            
            // Tags
            if (template.tags.isNotEmpty) ...[
              Wrap(
                spacing: 4,
                runSpacing: 4,
                children: template.tags.take(3).map((tag) => _buildTag(tag)).toList(),
              ),
              const SizedBox(height: 12),
            ],
            
            // Actions
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => _useTemplate(template),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: widget.glowColor.withValues(alpha: 0.2),
                      foregroundColor: widget.glowColor,
                      side: BorderSide(color: widget.glowColor.withValues(alpha: 0.6)),
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                    ),
                    child: const Text(
                      'Use Template',
                      style: TextStyle(
                        fontFamily: 'Pirulen',
                        fontSize: 12,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                IconButton(
                  onPressed: () => _showTemplateDetails(template),
                  icon: Icon(Icons.info_outline, color: widget.glowColor),
                ),
                if (!template.isPrebuilt) ...[
                  IconButton(
                    onPressed: () => _editTemplate(template),
                    icon: Icon(Icons.edit, color: widget.glowColor),
                  ),
                  IconButton(
                    onPressed: () => _deleteTemplate(template),
                    icon: const Icon(Icons.delete, color: Colors.red),
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryChip(String category) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: widget.glowColor.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: widget.glowColor.withValues(alpha: 0.4)),
      ),
      child: Text(
        category,
        style: TextStyle(
          color: widget.glowColor,
          fontSize: 10,
          fontFamily: 'Bitsumishi',
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildDifficultyChip(String difficulty) {
    Color color;
    switch (difficulty.toLowerCase()) {
      case 'beginner':
        color = Colors.green;
        break;
      case 'intermediate':
        color = Colors.orange;
        break;
      case 'advanced':
        color = Colors.red;
        break;
      default:
        color = Colors.grey;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.4)),
      ),
      child: Text(
        difficulty,
        style: TextStyle(
          color: color,
          fontSize: 10,
          fontFamily: 'Bitsumishi',
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildStatChip(IconData icon, String text) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: Colors.white70, size: 12),
          const SizedBox(width: 4),
          Text(
            text,
            style: const TextStyle(
              color: Colors.white70,
              fontSize: 10,
              fontFamily: 'Bitsumishi',
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTag(String tag) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Text(
        '#$tag',
        style: const TextStyle(
          color: Colors.white60,
          fontSize: 9,
          fontFamily: 'Bitsumishi',
        ),
      ),
    );
  }

  Widget _buildFavoritesList() {
    // For now, show most used templates as favorites
    return FutureBuilder<List<WorkoutTemplate>>(
      future: _templateService.getMostUsedTemplates(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return Center(child: CircularProgressIndicator(color: widget.glowColor));
        }
        
        final favorites = snapshot.data ?? [];
        return _buildTemplatesList(favorites);
      },
    );
  }

  Widget _buildCustomTemplatesList() {
    final customTemplates = _filteredTemplates.where((t) => !t.isPrebuilt).toList();
    return Column(
      children: [
        // Add new template button
        Container(
          width: double.infinity,
          margin: const EdgeInsets.only(bottom: 16),
          child: ElevatedButton.icon(
            onPressed: _createNewTemplate,
            icon: const Icon(Icons.add),
            label: const Text('Create New Template'),
            style: ElevatedButton.styleFrom(
              backgroundColor: widget.glowColor.withValues(alpha: 0.2),
              foregroundColor: widget.glowColor,
              side: BorderSide(color: widget.glowColor.withValues(alpha: 0.6)),
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
          ),
        ),
        
        // Custom templates list
        Expanded(child: _buildTemplatesList(customTemplates)),
      ],
    );
  }

  void _useTemplate(WorkoutTemplate template) async {
    await _templateService.recordTemplateUsage(template.id);
    widget.onTemplateSelected?.call(template);
    widget.onClose();
  }

  void _showTemplateDetails(WorkoutTemplate template) {
    // Show detailed template information
    showDialog(
      context: context,
      builder: (context) => _TemplateDetailsDialog(
        template: template,
        glowColor: widget.glowColor,
      ),
    );
  }

  void _editTemplate(WorkoutTemplate template) {
    // Show template editor
    // This would open a template creation/editing dialog
  }

  void _deleteTemplate(WorkoutTemplate template) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.black.withValues(alpha: 0.9),
        title: Text(
          'Delete Template',
          style: TextStyle(color: widget.glowColor, fontFamily: 'Pirulen'),
        ),
        content: Text(
          'Are you sure you want to delete "${template.name}"?',
          style: const TextStyle(color: Colors.white, fontFamily: 'Bitsumishi'),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Delete', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await _templateService.deleteCustomTemplate(template.id);
      _loadTemplates(); // Refresh the list
    }
  }

  void _createNewTemplate() {
    // Show template creation dialog
    // This would open a template creation dialog
  }
}

/// Template details dialog
class _TemplateDetailsDialog extends StatelessWidget {
  final WorkoutTemplate template;
  final Color glowColor;

  const _TemplateDetailsDialog({
    required this.template,
    required this.glowColor,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.black.withValues(alpha: 0.95),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.85,
        height: MediaQuery.of(context).size.height * 0.7,
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: glowColor.withValues(alpha: 0.3), width: 2),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Expanded(
                  child: Text(
                    template.name,
                    style: TextStyle(
                      color: glowColor,
                      fontSize: 18,
                      fontFamily: 'Pirulen',
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: Icon(Icons.close, color: glowColor),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // Description
            Text(
              template.description,
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 14,
                fontFamily: 'Bitsumishi',
              ),
            ),
            const SizedBox(height: 16),
            
            // Exercises
            Text(
              'EXERCISES',
              style: TextStyle(
                color: glowColor,
                fontSize: 12,
                fontFamily: 'Pirulen',
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Expanded(
              child: ListView.builder(
                itemCount: template.exercises.length,
                itemBuilder: (context, index) {
                  return Padding(
                    padding: const EdgeInsets.only(bottom: 4),
                    child: Text(
                      '• ${template.exercises[index]}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontFamily: 'Bitsumishi',
                      ),
                    ),
                  );
                },
              ),
            ),
            
            // Notes
            if (template.notes.isNotEmpty) ...[
              const SizedBox(height: 16),
              Text(
                'NOTES',
                style: TextStyle(
                  color: glowColor,
                  fontSize: 12,
                  fontFamily: 'Pirulen',
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.4),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.white.withValues(alpha: 0.2)),
                ),
                child: SelectableText(
                  template.notes,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontFamily: 'Bitsumishi',
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
