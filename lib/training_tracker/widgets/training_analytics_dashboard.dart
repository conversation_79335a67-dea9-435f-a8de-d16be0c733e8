// lib/training_tracker/widgets/training_analytics_dashboard.dart

import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../models/training_analytics_model.dart';
import '../training_tracker_config.dart';
import '../../models/training_session_model.dart';
import '../../services/comprehensive_logging_service.dart';

/// Training Analytics Dashboard Widget
class TrainingAnalyticsDashboard extends StatefulWidget {
  final List<TrainingSession> sessions;
  final Color glowColor;
  final VoidCallback onClose;

  const TrainingAnalyticsDashboard({
    super.key,
    required this.sessions,
    required this.glowColor,
    required this.onClose,
  });

  @override
  State<TrainingAnalyticsDashboard> createState() => _TrainingAnalyticsDashboardState();
}

class _TrainingAnalyticsDashboardState extends State<TrainingAnalyticsDashboard>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  TrainingAnalytics? _weeklyAnalytics;
  TrainingAnalytics? _monthlyAnalytics;
  PersonalRecords? _personalRecords;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadAnalytics();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadAnalytics() async {
    if (!TrainingTrackerConfig.analyticsEnabled) {
      setState(() => _isLoading = false);
      return;
    }

    try {
      await ComprehensiveLoggingService.logInfo('📊 Analytics: Loading dashboard data');

      final now = DateTime.now();
      final weekStart = now.subtract(Duration(days: now.weekday - 1));
      final monthStart = DateTime(now.year, now.month, 1);

      _weeklyAnalytics = TrainingAnalytics.weekly(widget.sessions, weekStart);
      _monthlyAnalytics = TrainingAnalytics.monthly(widget.sessions, monthStart);
      _personalRecords = PersonalRecords.fromSessions(widget.sessions);

      setState(() => _isLoading = false);

      await ComprehensiveLoggingService.logInfo('✅ Analytics: Dashboard loaded successfully');
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Analytics: Failed to load dashboard: $e');
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.black.withValues(alpha: 0.95),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.95,
        height: MediaQuery.of(context).size.height * 0.85,
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: widget.glowColor.withValues(alpha: 0.3), width: 2),
          boxShadow: [
            BoxShadow(
              color: widget.glowColor.withValues(alpha: 0.2),
              blurRadius: 20,
              spreadRadius: 4,
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildHeader(),
            const SizedBox(height: 16),
            _buildTabBar(),
            const SizedBox(height: 16),
            Expanded(child: _buildContent()),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 4),
      child: Row(
        children: [
          Icon(Icons.analytics, color: widget.glowColor, size: 20),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'TRAINING',
                  style: TextStyle(
                    color: widget.glowColor,
                    fontSize: 16,
                    fontFamily: 'Pirulen',
                    fontWeight: FontWeight.bold,
                    height: 1.0,
                  ),
                ),
                Text(
                  'ANALYTICS',
                  style: TextStyle(
                    color: widget.glowColor,
                    fontSize: 16,
                    fontFamily: 'Pirulen',
                    fontWeight: FontWeight.bold,
                    height: 1.0,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: widget.onClose,
            icon: Icon(Icons.close, color: widget.glowColor, size: 20),
            constraints: const BoxConstraints(minWidth: 40, minHeight: 40),
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.4),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: widget.glowColor.withValues(alpha: 0.3), width: 1),
      ),
      child: TabBar(
        controller: _tabController,
        indicator: BoxDecoration(
          color: widget.glowColor.withValues(alpha: 0.2),
          borderRadius: BorderRadius.circular(12),
        ),
        indicatorSize: TabBarIndicatorSize.tab,
        dividerColor: Colors.transparent,
        tabs: [
          Tab(
            child: FittedBox(
              fit: BoxFit.scaleDown,
              child: Text(
                'OVERVIEW',
                style: TextStyle(
                  fontFamily: 'Pirulen',
                  fontSize: 10,
                  color: widget.glowColor,
                ),
              ),
            ),
          ),
          Tab(
            child: FittedBox(
              fit: BoxFit.scaleDown,
              child: Text(
                'TRENDS',
                style: TextStyle(
                  fontFamily: 'Pirulen',
                  fontSize: 10,
                  color: widget.glowColor,
                ),
              ),
            ),
          ),
          Tab(
            child: FittedBox(
              fit: BoxFit.scaleDown,
              child: Text(
                'RECORDS',
                style: TextStyle(
                  fontFamily: 'Pirulen',
                  fontSize: 10,
                  color: widget.glowColor,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    if (_isLoading) {
      return Center(
        child: CircularProgressIndicator(color: widget.glowColor),
      );
    }

    if (!TrainingTrackerConfig.analyticsEnabled) {
      return _buildFeatureDisabled();
    }

    return TabBarView(
      controller: _tabController,
      physics: const BouncingScrollPhysics(),
      children: [
        _buildOverviewTab(),
        _buildTrendsTab(),
        _buildRecordsTab(),
      ],
    );
  }

  Widget _buildFeatureDisabled() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.analytics_outlined,
            color: Colors.white30,
            size: 64,
          ),
          const SizedBox(height: 16),
          Text(
            'Analytics Coming Soon',
            style: TextStyle(
              color: Colors.white30,
              fontSize: 18,
              fontFamily: 'Pirulen',
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Advanced analytics features will be available in a future update',
            style: TextStyle(
              color: Colors.white30,
              fontSize: 12,
              fontFamily: 'Bitsumishi',
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      child: Column(
        children: [
          _buildStatsGrid(),
          const SizedBox(height: 20),
          _buildFrequencyHeatmap(),
        ],
      ),
    );
  }

  Widget _buildStatsGrid() {
    if (_weeklyAnalytics == null || _monthlyAnalytics == null) {
      return const SizedBox.shrink();
    }

    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      crossAxisSpacing: 8,
      mainAxisSpacing: 8,
      childAspectRatio: 1.8,
      children: [
        _buildStatCard(
          'This Week',
          '${_weeklyAnalytics!.totalSessions} Sessions',
          _weeklyAnalytics!.formattedTotalDuration,
          Icons.calendar_today,
        ),
        _buildStatCard(
          'This Month',
          '${_monthlyAnalytics!.totalSessions} Sessions',
          _monthlyAnalytics!.formattedTotalDuration,
          Icons.calendar_month,
        ),
        _buildStatCard(
          'Weekly Avg',
          _weeklyAnalytics!.formattedAverageDuration,
          '${_weeklyAnalytics!.totalExp.toStringAsFixed(1)} EXP',
          Icons.trending_up,
        ),
        _buildStatCard(
          'Consistency',
          _weeklyAnalytics!.consistencyRating,
          '${_weeklyAnalytics!.consistency.toStringAsFixed(0)}%',
          Icons.check_circle,
        ),
      ],
    );
  }

  Widget _buildStatCard(String title, String value, String subtitle, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.4),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: widget.glowColor.withValues(alpha: 0.3), width: 1),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: widget.glowColor, size: 16),
          const SizedBox(height: 4),
          Flexible(
            child: Text(
              title,
              style: TextStyle(
                color: widget.glowColor,
                fontSize: 9,
                fontFamily: 'Pirulen',
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          const SizedBox(height: 2),
          Flexible(
            child: Text(
              value,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 11,
                fontFamily: 'Digital-7',
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          Flexible(
            child: Text(
              subtitle,
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 8,
                fontFamily: 'Bitsumishi',
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFrequencyHeatmap() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.4),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: widget.glowColor.withValues(alpha: 0.3), width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'WEEKLY FREQUENCY',
            style: TextStyle(
              color: widget.glowColor,
              fontSize: 12,
              fontFamily: 'Pirulen',
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          _buildDayFrequencyBars(),
        ],
      ),
    );
  }

  Widget _buildDayFrequencyBars() {
    if (_weeklyAnalytics == null) {
      return const SizedBox.shrink();
    }

    final dayNames = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    final maxSessions = _weeklyAnalytics!.frequencyByDay.values.isNotEmpty
        ? _weeklyAnalytics!.frequencyByDay.values.reduce((a, b) => a > b ? a : b)
        : 1;

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: dayNames.map((day) {
        final sessions = _weeklyAnalytics!.frequencyByDay[day] ?? 0;
        final intensity = maxSessions > 0 ? sessions / maxSessions : 0.0;
        
        return Column(
          children: [
            Container(
              width: 30,
              height: 60,
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.6),
                borderRadius: BorderRadius.circular(4),
                border: Border.all(color: Colors.white.withValues(alpha: 0.2), width: 1),
              ),
              child: Align(
                alignment: Alignment.bottomCenter,
                child: Container(
                  width: 30,
                  height: 60 * intensity,
                  decoration: BoxDecoration(
                    color: widget.glowColor.withValues(alpha: 0.3 + (intensity * 0.7)),
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ),
            ),
            const SizedBox(height: 4),
            Text(
              day,
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 10,
                fontFamily: 'Bitsumishi',
              ),
            ),
            Text(
              sessions.toString(),
              style: TextStyle(
                color: widget.glowColor,
                fontSize: 10,
                fontFamily: 'Digital-7',
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        );
      }).toList(),
    );
  }

  Widget _buildTrendsTab() {
    if (_weeklyAnalytics == null) {
      return const Center(
        child: Text(
          'No data available',
          style: TextStyle(color: Colors.white70),
        ),
      );
    }

    return SingleChildScrollView(
      child: Column(
        children: [
          _buildDurationTrendChart(),
          const SizedBox(height: 20),
          _buildExpTrendChart(),
        ],
      ),
    );
  }

  Widget _buildDurationTrendChart() {
    if (_weeklyAnalytics == null) {
      return const SizedBox.shrink();
    }

    return Container(
      height: 200,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.4),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: widget.glowColor.withValues(alpha: 0.3), width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'DURATION TREND',
            style: TextStyle(
              color: widget.glowColor,
              fontSize: 12,
              fontFamily: 'Pirulen',
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          Expanded(child: _buildLineChart(_weeklyAnalytics!.durationTrend, 'Duration (min)')),
        ],
      ),
    );
  }

  Widget _buildExpTrendChart() {
    if (_weeklyAnalytics == null) {
      return const SizedBox.shrink();
    }

    return Container(
      height: 200,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.4),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: widget.glowColor.withValues(alpha: 0.3), width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'EXP TREND',
            style: TextStyle(
              color: widget.glowColor,
              fontSize: 12,
              fontFamily: 'Pirulen',
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          Expanded(child: _buildLineChart(_weeklyAnalytics!.expTrend, 'EXP')),
        ],
      ),
    );
  }

  Widget _buildLineChart(List<double> data, String label) {
    if (data.isEmpty) {
      return Center(
        child: Text(
          'No data available',
          style: TextStyle(
            color: Colors.white30,
            fontSize: 12,
            fontFamily: 'Bitsumishi',
          ),
        ),
      );
    }

    final spots = data.asMap().entries.map((entry) {
      return FlSpot(entry.key.toDouble(), entry.value);
    }).toList();

    return LineChart(
      LineChartData(
        gridData: FlGridData(
          show: true,
          drawVerticalLine: false,
          horizontalInterval: data.isNotEmpty ? data.reduce((a, b) => a > b ? a : b) / 4 : 1,
          getDrawingHorizontalLine: (value) {
            return FlLine(
              color: Colors.white.withValues(alpha: 0.1),
              strokeWidth: 1,
            );
          },
        ),
        titlesData: FlTitlesData(show: false),
        borderData: FlBorderData(show: false),
        lineBarsData: [
          LineChartBarData(
            spots: spots,
            isCurved: true,
            color: widget.glowColor,
            barWidth: 2,
            isStrokeCapRound: true,
            dotData: FlDotData(
              show: true,
              getDotPainter: (spot, percent, barData, index) {
                return FlDotCirclePainter(
                  radius: 3,
                  color: widget.glowColor,
                  strokeWidth: 1,
                  strokeColor: Colors.white,
                );
              },
            ),
            belowBarData: BarAreaData(
              show: true,
              color: widget.glowColor.withValues(alpha: 0.1),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecordsTab() {
    return SingleChildScrollView(
      child: Column(
        children: [
          _buildPersonalRecordsGrid(),
          const SizedBox(height: 20),
          _buildStreakInfo(),
        ],
      ),
    );
  }

  Widget _buildPersonalRecordsGrid() {
    if (_personalRecords == null) {
      return const SizedBox.shrink();
    }

    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      crossAxisSpacing: 8,
      mainAxisSpacing: 8,
      childAspectRatio: 1.0,
      children: [
        _buildRecordCard(
          'LONGEST\nSESSION',
          _personalRecords!.formattedLongestSession,
          '',
          Icons.timer,
        ),
        _buildRecordCard(
          'MOST EXP\nSESSION',
          _personalRecords!.mostExpInSession.toStringAsFixed(1),
          '',
          Icons.star,
        ),
        _buildRecordCard(
          'MOST EXP\nDAY',
          _personalRecords!.mostExpInDay.toStringAsFixed(1),
          '',
          Icons.emoji_events,
        ),
        _buildRecordCard(
          'LONGEST\nSTREAK',
          '${_personalRecords!.longestStreak} days',
          '',
          Icons.local_fire_department,
        ),
      ],
    );
  }

  Widget _buildRecordCard(String title, String value, String subtitle, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.4),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: widget.glowColor.withValues(alpha: 0.3), width: 1),
      ),
      child: ClipRect(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: widget.glowColor, size: 14),
            const SizedBox(height: 2),
            Flexible(
              child: FittedBox(
                fit: BoxFit.scaleDown,
                child: Text(
                  title,
                  style: TextStyle(
                    color: widget.glowColor,
                    fontSize: 7,
                    fontFamily: 'Pirulen',
                    fontWeight: FontWeight.bold,
                    height: 0.8,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                ),
              ),
            ),
            const SizedBox(height: 1),
            Flexible(
              child: FittedBox(
                fit: BoxFit.scaleDown,
                child: Text(
                  value,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 9,
                    fontFamily: 'Digital-7',
                    fontWeight: FontWeight.bold,
                    height: 0.8,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 1,
                ),
              ),
            ),
            if (subtitle.isNotEmpty) ...[
              const SizedBox(height: 1),
              Flexible(
                child: FittedBox(
                  fit: BoxFit.scaleDown,
                  child: Text(
                    subtitle,
                    style: const TextStyle(
                      color: Colors.white70,
                      fontSize: 6,
                      fontFamily: 'Bitsumishi',
                      height: 0.8,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 1,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStreakInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.4),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: widget.glowColor.withValues(alpha: 0.3), width: 1),
      ),
      child: Column(
        children: [
          Text(
            'CURRENT STREAK',
            style: TextStyle(
              color: widget.glowColor,
              fontSize: 12,
              fontFamily: 'Pirulen',
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.local_fire_department, color: widget.glowColor, size: 32),
              const SizedBox(width: 12),
              Column(
                children: [
                  Text(
                    '${_personalRecords?.currentStreak ?? 0}',
                    style: TextStyle(
                      color: widget.glowColor,
                      fontSize: 24,
                      fontFamily: 'Digital-7',
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    'DAYS',
                    style: TextStyle(
                      color: widget.glowColor,
                      fontSize: 12,
                      fontFamily: 'Pirulen',
                    ),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            (_personalRecords?.currentStreak ?? 0) > 0
                ? 'Keep it up! You\'re on fire! 🔥'
                : 'Start a new streak today! 💪',
            style: const TextStyle(
              color: Colors.white70,
              fontSize: 12,
              fontFamily: 'Bitsumishi',
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
