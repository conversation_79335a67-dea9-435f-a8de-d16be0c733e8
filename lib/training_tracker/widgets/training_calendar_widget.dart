// lib/training_tracker/widgets/training_calendar_widget.dart

import 'package:flutter/material.dart';
import '../models/training_calendar_model.dart';
import '../services/training_calendar_service.dart';
import '../../services/comprehensive_logging_service.dart';
import '../../models/training_timer_model.dart';
import 'workout_scheduling_widget.dart';
import 'package:uuid/uuid.dart';
import 'dart:io';

/// Semantics-Safe Training Calendar Widget - Completely Isolated
class TrainingCalendarWidget extends StatelessWidget {
  final Color glowColor;
  final VoidCallback onClose;
  final Function(PlannedSession)? onSessionPlanned;
  final Function(RestDay)? onRestDayPlanned;
  final TrainingProgram? programToLoad;

  const TrainingCalendarWidget({
    super.key,
    required this.glowColor,
    required this.onClose,
    this.onSessionPlanned,
    this.onRestDayPlanned,
    this.programToLoad,
  });

  @override
  Widget build(BuildContext context) {
    return _SemanticsSafeCalendar(
      glowColor: glowColor,
      onClose: onClose,
      onSessionPlanned: onSessionPlanned,
      onRestDayPlanned: onRestDayPlanned,
      programToLoad: programToLoad,
    );
  }
}

/// Internal Semantics-Safe Calendar Implementation
class _SemanticsSafeCalendar extends StatefulWidget {
  final Color glowColor;
  final VoidCallback onClose;
  final Function(PlannedSession)? onSessionPlanned;
  final Function(RestDay)? onRestDayPlanned;
  final TrainingProgram? programToLoad;

  const _SemanticsSafeCalendar({
    required this.glowColor,
    required this.onClose,
    this.onSessionPlanned,
    this.onRestDayPlanned,
    this.programToLoad,
  });

  @override
  State<_SemanticsSafeCalendar> createState() => _SemanticsSafeCalendarState();
}

/// Semantics-Safe Calendar State - Minimal and Stable
class _SemanticsSafeCalendarState extends State<_SemanticsSafeCalendar> {
  
  // Enhanced State - Calendar functionality
  DateTime _currentMonth = DateTime.now();
  bool _isLoading = false;
  String? _errorMessage;

  // Calendar data and services
  final TrainingCalendarService _calendarService = TrainingCalendarService();
  TrainingCalendar? _currentCalendar;
  final List<DateTime> _calendarDays = [];
  
  @override
  void initState() {
    super.initState();
    _initializeCalendar();
  }

  /// Load program schedule into calendar
  Future<void> _loadProgramSchedule() async {
    if (widget.programToLoad == null) return;

    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      await ComprehensiveLoggingService.logInfo('📅 Loading program schedule: ${widget.programToLoad!.programType}');

      // Load program schedule starting from today
      final success = await _calendarService.loadProgramScheduleIntoCalendar(
        widget.programToLoad!,
        startDate: DateTime.now(),
        daysToSchedule: widget.programToLoad!.workoutLabels.length,
        defaultIntensity: 'Medium',
        defaultDuration: 60,
      );

      if (success) {
        await ComprehensiveLoggingService.logInfo('✅ Program schedule loaded successfully');
        await _loadCalendarData(); // Refresh calendar data

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Program schedule loaded successfully!',
                style: TextStyle(
                  fontFamily: 'Bitsumishi',
                  fontSize: 14,
                ),
              ),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 2),
            ),
          );
        }
      } else {
        await ComprehensiveLoggingService.logError('❌ Failed to load program schedule');
        if (mounted) {
          setState(() {
            _errorMessage = 'Failed to load program schedule';
          });
        }
      }
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Error loading program schedule: $e');
      if (mounted) {
        setState(() {
          _errorMessage = 'Error loading program schedule: $e';
        });
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// Initialize calendar with error handling and data loading
  Future<void> _initializeCalendar() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      await ComprehensiveLoggingService.logInfo('📅 TrainingCalendar: Initializing calendar widget');

      // Load calendar data for current month
      await _loadCalendarData();

      // Generate calendar days
      _generateCalendarDays();

      setState(() {
        _isLoading = false;
      });

      await ComprehensiveLoggingService.logInfo('✅ TrainingCalendar: Calendar widget initialized successfully');
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ TrainingCalendar: Error initializing calendar: $e');
      setState(() {
        _isLoading = false;
        _errorMessage = 'Failed to load calendar: ${e.toString()}';
      });
    }
  }

  /// Load calendar data for current month
  Future<void> _loadCalendarData() async {
    try {
      _currentCalendar = await _calendarService.getCalendarForMonth(_currentMonth);
      await ComprehensiveLoggingService.logInfo('📅 TrainingCalendar: Loaded ${_currentCalendar?.plannedSessions.length ?? 0} planned sessions');
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ TrainingCalendar: Error loading calendar data: $e');
      _currentCalendar = TrainingCalendar.forMonth(_currentMonth);
    }
  }

  void _generateCalendarDays() {
    final firstDayOfMonth = DateTime(_currentMonth.year, _currentMonth.month, 1);
    final lastDayOfMonth = DateTime(_currentMonth.year, _currentMonth.month + 1, 0);
    final firstWeekday = firstDayOfMonth.weekday % 7;
    final daysInMonth = lastDayOfMonth.day;

    _calendarDays.clear();
    
    // Add empty days for the first week
    for (int i = 0; i < firstWeekday; i++) {
      _calendarDays.add(DateTime(1900, 1, 1)); // Placeholder date
    }
    
    // Add actual days
    for (int day = 1; day <= daysInMonth; day++) {
      _calendarDays.add(DateTime(_currentMonth.year, _currentMonth.month, day));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        width: MediaQuery.of(context).size.width * 0.95,
        height: MediaQuery.of(context).size.height * 0.8,
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.95),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: widget.glowColor.withValues(alpha: 0.3), width: 2),
          boxShadow: [
            BoxShadow(
              color: widget.glowColor.withValues(alpha: 0.2),
              blurRadius: 20,
              spreadRadius: 4,
            ),
          ],
        ),
        child: Column(
          children: [
            _buildHeader(),
            const SizedBox(height: 16),
            Expanded(child: _buildContent()),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: widget.glowColor.withValues(alpha: 0.1),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.calendar_month,
            color: widget.glowColor,
            size: 24,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              'Training \nCalendar',
              style: TextStyle(
                color: widget.glowColor,
                fontFamily: 'Pirulen',
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          if (widget.programToLoad != null) ...[
            ElevatedButton(
              onPressed: _loadProgramSchedule,
              style: ElevatedButton.styleFrom(
                backgroundColor: widget.glowColor.withValues(alpha: 0.2),
                foregroundColor: widget.glowColor,
                padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                  side: BorderSide(color: widget.glowColor.withValues(alpha: 0.5)),
                ),
              ),
              child: Text(
                'Load \nSchedule',
                style: TextStyle(
                  fontFamily: 'Pirulen',
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const SizedBox(width: 6),
          ],
          IconButton(
            onPressed: widget.onClose,
            icon: Icon(
              Icons.close,
              color: Colors.white,
              size: 12,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    if (_errorMessage != null) {
      return _buildErrorContent(_errorMessage!);
    }

    if (_isLoading) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(widget.glowColor),
            ),
            const SizedBox(height: 16),
            Text(
              'Loading Calendar...',
              style: TextStyle(
                color: Colors.white,
                fontFamily: 'Bitsumishi',
                fontSize: 16,
              ),
            ),
          ],
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildMonthNavigation(),
          const SizedBox(height: 16),
          Expanded(
            child: _buildCalendarGrid(),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorContent(String error) {
    return Center(
      child: Container(
        padding: const EdgeInsets.all(20),
        margin: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.red.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.red.withValues(alpha: 0.3), width: 1),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.error_outline,
              color: Colors.red,
              size: 48,
            ),
            const SizedBox(height: 16),
            Text(
              'Calendar Error',
              style: TextStyle(
                color: Colors.red,
                fontFamily: 'Pirulen',
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              'The training calendar encountered an error and cannot be displayed.',
              style: TextStyle(
                color: Colors.white,
                fontFamily: 'Bitsumishi',
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton(
                  onPressed: () {
                    setState(() {
                      _isLoading = true;
                      _errorMessage = null;
                    });
                    _initializeCalendar();
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: widget.glowColor,
                    foregroundColor: Colors.black,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: Text(
                    'Retry',
                    style: TextStyle(
                      fontFamily: 'Pirulen',
                      fontSize: 14,
                    ),
                  ),
                ),
                ElevatedButton(
                  onPressed: widget.onClose,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: Text(
                    'Close',
                    style: TextStyle(
                      fontFamily: 'Pirulen',
                      fontSize: 14,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMonthNavigation() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        IconButton(
          onPressed: () async {
            setState(() {
              _currentMonth = DateTime(_currentMonth.year, _currentMonth.month - 1);
            });
            await _loadCalendarData();
            _generateCalendarDays();
          },
          icon: Icon(
            Icons.chevron_left,
            color: widget.glowColor,
            size: 28,
          ),
        ),
        Expanded(
          child: Text(
            '${_getMonthName(_currentMonth.month)} ${_currentMonth.year}',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Colors.white,
              fontFamily: 'Pirulen',
              fontSize: 16, // Reduced from 18 to prevent overflow
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        IconButton(
          onPressed: () async {
            setState(() {
              _currentMonth = DateTime(_currentMonth.year, _currentMonth.month + 1);
            });
            await _loadCalendarData();
            _generateCalendarDays();
          },
          icon: Icon(
            Icons.chevron_right,
            color: widget.glowColor,
            size: 28,
          ),
        ),
      ],
    );
  }

  Widget _buildCalendarGrid() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: widget.glowColor.withValues(alpha: 0.2), width: 1),
      ),
      child: Column(
        children: [
          _buildWeekdayHeaders(),
          Expanded(
            child: _buildCalendarDays(),
          ),
        ],
      ),
    );
  }

  Widget _buildWeekdayHeaders() {
    final weekdays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8),
      decoration: BoxDecoration(
        color: widget.glowColor.withValues(alpha: 0.1),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12),
        ),
      ),
      child: Row(
        children: weekdays.map((day) => Expanded(
          child: Center(
            child: Text(
              day,
              style: TextStyle(
                color: widget.glowColor,
                fontFamily: 'Pirulen',
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        )).toList(),
      ),
    );
  }

  Widget _buildCalendarDays() {
    return GridView.builder(
      padding: const EdgeInsets.all(8),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 7,
        childAspectRatio: 1.0,
        crossAxisSpacing: 2,
        mainAxisSpacing: 2,
      ),
      itemCount: _calendarDays.length,
      itemBuilder: (context, index) {
        final date = _calendarDays[index];

        // Skip placeholder dates
        if (date.year == 1900) {
          return Container();
        }

        final isToday = _isToday(date);

        return _buildDayCell(date, isToday);
      },
    );
  }

  Widget _buildDayCell(DateTime date, bool isToday) {
    return Container(
      decoration: BoxDecoration(
        color: isToday
          ? widget.glowColor.withValues(alpha: 0.2)
          : Colors.transparent,
        borderRadius: BorderRadius.circular(8),
        border: isToday
          ? Border.all(color: widget.glowColor, width: 2)
          : null,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(8),
          onTap: () => _onDayTapped(date),
          child: Container(
            padding: const EdgeInsets.all(2),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  date.day.toString(),
                  style: TextStyle(
                    color: isToday ? widget.glowColor : Colors.white,
                    fontFamily: 'Pirulen',
                    fontSize: 12,
                    fontWeight: isToday ? FontWeight.bold : FontWeight.normal,
                  ),
                ),
                const SizedBox(height: 1),
                _buildWorkoutIndicators(date),
              ],
            ),
          ),
        ),
      ),
    );
  }

  bool _isToday(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year &&
           date.month == now.month &&
           date.day == now.day;
  }

  /// Get workout indicators for a date (up to 4 colored dots)
  List<Color> _getWorkoutIndicators(DateTime date) {
    if (_currentCalendar == null) return [];

    final sessions = _currentCalendar!.getSessionsForDateSorted(date);
    return sessions.map((session) => session.workoutColor).toList();
  }

  /// Build workout indicators for a date (colored dots for each workout)
  Widget _buildWorkoutIndicators(DateTime date) {
    final workoutColors = _getWorkoutIndicators(date);
    final restDay = _currentCalendar?.getRestDayForDate(date);

    if (workoutColors.isEmpty && restDay == null) {
      return const SizedBox(height: 6); // Maintain spacing
    }

    return SizedBox(
      height: 6,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Show rest day indicator
          if (restDay != null)
            Container(
              width: 4,
              height: 4,
              margin: const EdgeInsets.symmetric(horizontal: 0.5),
              decoration: BoxDecoration(
                color: Colors.blue,
                shape: BoxShape.circle,
              ),
            ),
          // Show workout indicators (up to 4)
          ...workoutColors.take(4).map((color) => Container(
            width: 3,
            height: 3,
            margin: const EdgeInsets.symmetric(horizontal: 0.25),
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
            ),
          )),
          // Show overflow indicator if more than 4 workouts
          if (workoutColors.length > 4)
            Container(
              width: 3,
              height: 3,
              margin: const EdgeInsets.symmetric(horizontal: 0.25),
              decoration: BoxDecoration(
                color: Colors.white,
                shape: BoxShape.circle,
                border: Border.all(color: widget.glowColor, width: 0.5),
              ),
            ),
        ],
      ),
    );
  }

  void _onDayTapped(DateTime date) async {
    try {
      await ComprehensiveLoggingService.logInfo('📅 Day tapped: ${date.toString().split(' ')[0]}');

      // Show enhanced day details modal
      if (mounted) {
        _showDayDetailsModal(date);
      }
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ TrainingCalendar: Error handling day tap: $e');
    }
  }

  void _showDayDetailsModal(DateTime date) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => WorkoutSchedulingWidget(
        selectedDate: date,
        glowColor: widget.glowColor,
        calendar: _currentCalendar,
        onSessionScheduled: (session) async {
          await ComprehensiveLoggingService.logInfo('📅 Session scheduled: ${session.label}');
          await _loadCalendarData();
          setState(() {});
          if (widget.onSessionPlanned != null) {
            widget.onSessionPlanned!(session);
          }
        },
        onSchedulingComplete: () async {
          await ComprehensiveLoggingService.logInfo('📅 Scheduling completed for ${date.toString().split(' ')[0]}');
          await _loadCalendarData();
          setState(() {});
        },
        onSessionDeleted: (sessionId) async {
          await ComprehensiveLoggingService.logInfo('📅 Session deleted: $sessionId');
          await _loadCalendarData();
          setState(() {});
        },
      ),
    );
  }

  String _getMonthName(int month) {
    const months = [
      '', 'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    return months[month];
  }


}

/// Day Details Modal - Shows sessions and planning options for a specific date
class _DayDetailsModal extends StatefulWidget {
  final DateTime date;
  final Color glowColor;
  final TrainingCalendar? calendar;
  final Function(PlannedSession) onSessionPlanned;
  final Function(RestDay) onRestDayPlanned;

  const _DayDetailsModal({
    required this.date,
    required this.glowColor,
    required this.calendar,
    required this.onSessionPlanned,
    required this.onRestDayPlanned,
  });

  @override
  State<_DayDetailsModal> createState() => _DayDetailsModalState();
}

class _DayDetailsModalState extends State<_DayDetailsModal> {
  final TextEditingController _labelController = TextEditingController();
  final TextEditingController _notesController = TextEditingController();
  String _selectedWorkoutType = 'Strength';
  final String _selectedIntensity = 'Medium';
  final int _estimatedDuration = 60;

  @override
  void dispose() {
    _labelController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.8,
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.95),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
        border: Border.all(
          color: widget.glowColor.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          _buildHeader(),
          Expanded(
            child: _buildContent(),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: widget.glowColor.withValues(alpha: 0.1),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.event,
            color: widget.glowColor,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              _formatDate(widget.date),
              style: TextStyle(
                color: widget.glowColor,
                fontFamily: 'Pirulen',
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(
              Icons.close,
              color: Colors.white,
              size: 24,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    final sessions = widget.calendar?.getSessionsForDate(widget.date) ?? [];
    final restDay = widget.calendar?.getRestDayForDate(widget.date);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (sessions.isNotEmpty) ...[
            _buildSectionTitle('Planned Sessions'),
            const SizedBox(height: 8),
            ...sessions.map((session) => _buildSessionCard(session)),
            const SizedBox(height: 16),
          ],
          if (restDay != null) ...[
            _buildSectionTitle('Rest Day'),
            const SizedBox(height: 8),
            _buildRestDayCard(restDay),
            const SizedBox(height: 16),
          ],
          if (sessions.isEmpty && restDay == null) ...[
            _buildSectionTitle('No Plans Yet'),
            const SizedBox(height: 8),
            Text(
              'This day has no planned activities. Add a training session or mark it as a rest day.',
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.7),
                fontFamily: 'Bitsumishi',
                fontSize: 14,
              ),
            ),
            const SizedBox(height: 16),
          ],
          _buildPlanningSection(),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: TextStyle(
        color: widget.glowColor,
        fontFamily: 'Pirulen',
        fontSize: 14,
        fontWeight: FontWeight.bold,
      ),
    );
  }

  Widget _buildSessionCard(PlannedSession session) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: widget.glowColor.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  session.label,
                  style: const TextStyle(
                    color: Colors.white,
                    fontFamily: 'Pirulen',
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: _getIntensityColor(session.intensity).withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  session.intensity,
                  style: TextStyle(
                    color: _getIntensityColor(session.intensity),
                    fontFamily: 'Bitsumishi',
                    fontSize: 12,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            '${session.workoutType} • ${session.formattedDuration}',
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.7),
              fontFamily: 'Bitsumishi',
              fontSize: 12,
            ),
          ),
          if (session.notes.isNotEmpty) ...[
            const SizedBox(height: 4),
            Text(
              session.notes,
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.6),
                fontFamily: 'Bitsumishi',
                fontSize: 11,
              ),
            ),
          ],
          if (session.imagePath != null && session.imagePath!.isNotEmpty) ...[
            const SizedBox(height: 8),
            GestureDetector(
              onTap: () => _showImageModal(session.imagePath!),
              child: Container(
                height: 80,
                width: double.infinity,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(6),
                  border: Border.all(
                    color: widget.glowColor.withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(6),
                  child: Image.file(
                    File(session.imagePath!),
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        color: Colors.grey.withValues(alpha: 0.2),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.broken_image,
                              color: Colors.grey,
                              size: 20,
                            ),
                            const SizedBox(height: 2),
                            Text(
                              'Image not found',
                              style: TextStyle(
                                color: Colors.grey,
                                fontSize: 10,
                                fontFamily: 'Bitsumishi',
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildRestDayCard(RestDay restDay) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.blue.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.spa,
            color: Colors.blue,
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Rest Day',
                  style: TextStyle(
                    color: Colors.blue,
                    fontFamily: 'Pirulen',
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                if (restDay.notes.isNotEmpty) ...[
                  const SizedBox(height: 4),
                  Text(
                    restDay.notes,
                    style: TextStyle(
                      color: Colors.white.withValues(alpha: 0.7),
                      fontFamily: 'Bitsumishi',
                      fontSize: 12,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPlanningSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle('Plan Training'),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildPlanButton(
                'Add Session',
                Icons.fitness_center,
                () => _showSessionPlanningDialog(),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildPlanButton(
                'Rest Day',
                Icons.spa,
                () => _planRestDay(),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildPlanButton(String label, IconData icon, VoidCallback onTap) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: widget.glowColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: widget.glowColor.withValues(alpha: 0.3),
              width: 1,
            ),
          ),
          child: Column(
            children: [
              Icon(
                icon,
                color: widget.glowColor,
                size: 24,
              ),
              const SizedBox(height: 4),
              Text(
                label,
                style: TextStyle(
                  color: widget.glowColor,
                  fontFamily: 'Pirulen',
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showSessionPlanningDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.black.withValues(alpha: 0.95),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
        title: Text(
          'Plan Training Session',
          style: TextStyle(
            color: widget.glowColor,
            fontFamily: 'Pirulen',
            fontSize: 16,
          ),
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: _labelController,
                style: const TextStyle(color: Colors.white),
                decoration: InputDecoration(
                  labelText: 'Session Label',
                  labelStyle: TextStyle(color: widget.glowColor),
                  enabledBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: widget.glowColor.withValues(alpha: 0.3)),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: widget.glowColor),
                  ),
                ),
              ),
              const SizedBox(height: 12),
              DropdownButtonFormField<String>(
                value: _selectedWorkoutType,
                dropdownColor: Colors.black,
                style: const TextStyle(color: Colors.white),
                decoration: InputDecoration(
                  labelText: 'Workout Type',
                  labelStyle: TextStyle(color: widget.glowColor),
                  enabledBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: widget.glowColor.withValues(alpha: 0.3)),
                  ),
                ),
                items: ['Strength', 'Cardio', 'Flexibility', 'Sports', 'Recovery']
                    .map((type) => DropdownMenuItem(value: type, child: Text(type)))
                    .toList(),
                onChanged: (value) => setState(() => _selectedWorkoutType = value!),
              ),
              const SizedBox(height: 12),
              TextField(
                controller: _notesController,
                style: const TextStyle(color: Colors.white),
                maxLines: 3,
                decoration: InputDecoration(
                  labelText: 'Notes (optional)',
                  labelStyle: TextStyle(color: widget.glowColor),
                  enabledBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: widget.glowColor.withValues(alpha: 0.3)),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: widget.glowColor),
                  ),
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'Cancel',
              style: TextStyle(color: Colors.white.withValues(alpha: 0.7)),
            ),
          ),
          TextButton(
            onPressed: () {
              _createPlannedSession();
              Navigator.of(context).pop();
              Navigator.of(context).pop();
            },
            child: Text(
              'Plan Session',
              style: TextStyle(color: widget.glowColor),
            ),
          ),
        ],
      ),
    );
  }

  void _createPlannedSession() {
    if (_labelController.text.trim().isEmpty) {
      _labelController.text = _selectedWorkoutType;
    }

    final session = PlannedSession(
      id: const Uuid().v4(),
      plannedDate: widget.date,
      workoutType: _selectedWorkoutType,
      label: _labelController.text.trim(),
      notes: _notesController.text.trim(),
      estimatedDuration: _estimatedDuration,
      intensity: _selectedIntensity,
      createdAt: DateTime.now(),
    );

    widget.onSessionPlanned(session);
  }

  void _planRestDay() {
    final restDay = RestDay(
      id: const Uuid().v4(),
      date: widget.date,
      type: 'Active',
      notes: 'Planned rest day',
      isPlanned: true,
      createdAt: DateTime.now(),
    );

    widget.onRestDayPlanned(restDay);
    Navigator.of(context).pop();
  }

  Color _getIntensityColor(String intensity) {
    switch (intensity.toLowerCase()) {
      case 'low':
        return Colors.green;
      case 'medium':
        return Colors.orange;
      case 'high':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  String _formatDate(DateTime date) {
    final months = [
      '', 'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    return '${months[date.month]} ${date.day}, ${date.year}';
  }

  /// Show image in full screen modal
  void _showImageModal(String imagePath) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) => Dialog(
        backgroundColor: Colors.black.withValues(alpha: 0.9),
        child: Stack(
          children: [
            Center(
              child: InteractiveViewer(
                child: Image.file(
                  File(imagePath),
                  fit: BoxFit.contain,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      padding: const EdgeInsets.all(32),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.broken_image,
                            color: Colors.grey,
                            size: 64,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Image not found',
                            style: TextStyle(
                              color: Colors.grey,
                              fontSize: 16,
                              fontFamily: 'Bitsumishi',
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'The image file may have been moved or deleted.',
                            style: TextStyle(
                              color: Colors.grey.withValues(alpha: 0.7),
                              fontSize: 12,
                              fontFamily: 'Bitsumishi',
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
            ),
            Positioned(
              top: 16,
              right: 16,
              child: IconButton(
                onPressed: () => Navigator.of(context).pop(),
                icon: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.7),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.close,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
