import 'package:meta/meta.dart';
import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'bounty_model.g.dart';

/// Represents a Bounty (daily quest/challenge) in the gamification system.
///
/// Bounties are daily challenges that users can complete to earn experience
/// points across different life categories. They vary in difficulty and
/// reward amounts, with some requiring photo proof of completion.
///
/// Each bounty targets specific categories (Health, Wealth, Purpose, Connection)
/// and awards experience points accordingly. Epic bounties provide higher
/// rewards but are more challenging to complete.
///
/// Example:
/// ```dart
/// final bounty = BountyModel(
///   id: 'b1',
///   description: 'Complete a 30-minute workout',
///   categories: ['Health'],
///   expPerCategory: {'Health': 50},
///   difficulty: 'medium',
///   isEpic: false,
/// );
/// ```
@JsonSerializable()
@immutable
class BountyModel extends Equatable {
  /// Unique identifier for the bounty
  final String id;

  /// Human-readable description of the challenge
  final String description;

  /// Categories this bounty contributes to (e.g. ["Health", "Purpose"])
  final List<String> categories;

  /// Experience points awarded per category (e.g. {"Health": 40, "Purpose": 40})
  final Map<String, int> expPerCategory;

  /// Difficulty level: "easy", "medium", "hard", or "epic"
  final String difficulty;

  /// Whether this is an epic-tier bounty with higher rewards
  final bool isEpic;

  /// Optional path to photo proof of completion
  final String? photoProofPath;

  const BountyModel({
    required this.id,
    required this.description,
    required this.categories,
    required this.expPerCategory,
    required this.difficulty,
    required this.isEpic,
    this.photoProofPath,
  });

  factory BountyModel.fromJson(Map<String, dynamic> json) => _$BountyModelFromJson(json);
  Map<String, dynamic> toJson() => _$BountyModelToJson(this);

  @override
  List<Object?> get props => [id, description, categories, expPerCategory, difficulty, isEpic, photoProofPath];

  @override
  String toString() => 'BountyModel(id: $id, description: $description)';
} 