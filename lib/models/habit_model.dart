import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';

/// Represents a daily habit that users can track and complete.
///
/// Habits are the core building blocks of the Maxed Out Life system.
/// Users create habits they want to build, track completion daily,
/// and earn experience points for consistency.
///
/// Each habit has a color for visual identification, tracks streaks
/// for motivation, and maintains a weekly completion log.
///
/// Example:
/// ```dart
/// final habit = Habit.create(
///   name: 'Morning Workout',
///   description: '30 minutes of exercise',
///   color: Colors.green,
/// );
/// ```
class Habit {
  /// Maximum allowed length for habit names
  static const int maxNameLength = 50;

  /// Minimum required length for habit names
  static const int minNameLength = 1;

  /// Unique identifier for the habit
  final String id;

  /// Display name of the habit (1-50 characters)
  final String name;

  /// Optional detailed description of the habit
  final String description;

  /// Color used for visual identification in the UI
  final Color color;

  /// Current consecutive completion streak
  final int streak;

  /// Date and time when habit was last completed
  final DateTime? lastCompleted;

  /// When the habit was first created
  final DateTime createdAt;

  /// Last time the habit data was modified
  final DateTime lastModified;

  /// Weekly completion log with day-of-week keys
  final Map<String, int> weeklyLog;

  const Habit({
    required this.id,
    required this.name,
    required this.description,
    required this.color,
    this.streak = 0,
    this.lastCompleted,
    required this.createdAt,
    required this.lastModified,
    this.weeklyLog = const {},
  });

  factory Habit.create({
    required String name,
    required String description,
    required Color color,
  }) {
    final now = DateTime.now();
    return Habit(
      id: const Uuid().v4(),
      name: name,
      description: description,
      color: color,
      streak: 0,
      createdAt: now,
      lastModified: now,
      weeklyLog: {},
    );
  }

  Habit copyWith({
    String? id,
    String? name,
    String? description,
    Color? color,
    int? streak,
    DateTime? lastCompleted,
    DateTime? createdAt,
    DateTime? lastModified,
    Map<String, int>? weeklyLog,
  }) {
    return Habit(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      color: color ?? this.color,
      streak: streak ?? this.streak,
      lastCompleted: lastCompleted ?? this.lastCompleted,
      createdAt: createdAt ?? this.createdAt,
      lastModified: lastModified ?? this.lastModified,
      weeklyLog: weeklyLog ?? this.weeklyLog,
    );
  }

  Map<String, dynamic> toJson() => {
    'id': id,
    'name': name,
    'description': description,
    // ignore: deprecated_member_use
    'color': color.value, // Using .value for backward compatibility with existing data
    'streak': streak,
    'lastCompleted': lastCompleted?.toIso8601String(),
    'createdAt': createdAt.toIso8601String(),
    'lastModified': lastModified.toIso8601String(),
    'weeklyLog': weeklyLog,
  };

  factory Habit.fromJson(Map<String, dynamic> json) {
    return Habit(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String? ?? '',
      color: Color(json['color'] as int),
      streak: json['streak'] as int? ?? 0,
      lastCompleted: json['lastCompleted'] != null 
          ? DateTime.parse(json['lastCompleted'] as String)
          : null,
      createdAt: DateTime.parse(json['createdAt'] as String),
      lastModified: DateTime.parse(json['lastModified'] as String),
      weeklyLog: Map<String, int>.from(json['weeklyLog'] as Map? ?? {}),
    );
  }

  bool validate() {
    if (name.isEmpty || name.length > maxNameLength) {
      return false;
    }
    if (streak < 0) {
      return false;
    }
    if (lastCompleted != null && lastCompleted!.isAfter(DateTime.now())) {
      return false;
    }
    if (description.length > 200) {
      return false;
    }
    if (weeklyLog.entries.any((entry) => entry.value < 0)) {
      return false;
    }
    return true;
  }

  /// Checks if the habit was completed today
  bool isCompletedToday() {
    if (lastCompleted == null) return false;
    final now = DateTime.now();
    return lastCompleted!.year == now.year &&
           lastCompleted!.month == now.month &&
           lastCompleted!.day == now.day;
  }
} 