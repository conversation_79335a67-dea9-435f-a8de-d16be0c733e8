// lib/models/training_session_model.dart

import 'package:flutter/foundation.dart';
import 'package:uuid/uuid.dart';

/// Represents a single training session with timer data, notes, and bodyweight
@immutable
class TrainingSession {
  /// Unique identifier for this training session
  final String id;

  /// User-defined label for the session (e.g., "A", "Mon", "1", "Hips & Glutes - 7.7.25")
  final String label;

  /// Free-form training notes (workout details, exercises, etc.)
  final String notes;

  /// Current training goal for this session (e.g., "Build strength", "Improve endurance")
  final String currentGoal;

  /// Duration of training in seconds
  final int durationSeconds;

  /// Optional bodyweight recorded for this session (in kg)
  final double? bodyweightKg;

  /// Optional image path for this session (saved workout photo)
  final String? imagePath;

  /// When this session was created/started
  final DateTime createdAt;

  /// When this session was completed/saved
  final DateTime? completedAt;

  /// Experience points earned for this session (12.5 EXP per hour with 25% boost)
  final double expEarned;

  /// Whether this session has been completed and saved
  final bool isCompleted;

  const TrainingSession({
    required this.id,
    required this.label,
    required this.notes,
    required this.currentGoal,
    required this.durationSeconds,
    this.bodyweightKg,
    this.imagePath,
    required this.createdAt,
    this.completedAt,
    required this.expEarned,
    required this.isCompleted,
  });

  /// Factory constructor for creating a new training session
  factory TrainingSession.create({
    required String label,
    String notes = '',
    String currentGoal = '',
    double? bodyweightKg,
    String? imagePath,
  }) {
    final now = DateTime.now();
    return TrainingSession(
      id: const Uuid().v4(),
      label: label,
      notes: notes,
      currentGoal: currentGoal,
      durationSeconds: 0,
      bodyweightKg: bodyweightKg,
      imagePath: imagePath,
      createdAt: now,
      completedAt: null,
      expEarned: 0,
      isCompleted: false,
    );
  }

  /// Calculate EXP based on training duration (12.5 EXP per hour with 25% boost)
  /// Base Health EXP: 10 per hour, Training Tracker boost: +25% = 12.5 per hour
  /// Per minute: 12.5 ÷ 60 = 0.208333... EXP per minute
  /// Rounded to nearest half (0, 0.5, 1.0, 1.5, 2.0, etc.)
  static double calculateExp(int durationSeconds) {
    final minutes = durationSeconds / 60.0;
    final rawExp = minutes * (12.5 / 60.0); // 0.208333... EXP per minute
    return _roundToNearestHalf(rawExp);
  }

  /// Round to nearest half number (0, 0.5, 1.0, 1.5, 2.0, etc.)
  static double _roundToNearestHalf(double value) {
    return (value * 2).round() / 2.0;
  }

  /// Create a copy with updated values
  TrainingSession copyWith({
    String? id,
    String? label,
    String? notes,
    String? currentGoal,
    int? durationSeconds,
    double? bodyweightKg,
    String? imagePath,
    DateTime? createdAt,
    DateTime? completedAt,
    double? expEarned,
    bool? isCompleted,
  }) {
    return TrainingSession(
      id: id ?? this.id,
      label: label ?? this.label,
      notes: notes ?? this.notes,
      currentGoal: currentGoal ?? this.currentGoal,
      durationSeconds: durationSeconds ?? this.durationSeconds,
      bodyweightKg: bodyweightKg ?? this.bodyweightKg,
      imagePath: imagePath ?? this.imagePath,
      createdAt: createdAt ?? this.createdAt,
      completedAt: completedAt ?? this.completedAt,
      expEarned: expEarned ?? this.expEarned,
      isCompleted: isCompleted ?? this.isCompleted,
    );
  }

  /// Complete the session with final duration and calculate EXP
  TrainingSession complete({
    required int finalDurationSeconds,
    String? finalNotes,
    String? finalCurrentGoal,
    double? finalBodyweightKg,
    String? finalImagePath,
  }) {
    final exp = calculateExp(finalDurationSeconds);
    return copyWith(
      durationSeconds: finalDurationSeconds,
      notes: finalNotes ?? notes,
      currentGoal: finalCurrentGoal ?? currentGoal,
      bodyweightKg: finalBodyweightKg ?? bodyweightKg,
      imagePath: finalImagePath ?? imagePath,
      completedAt: DateTime.now(),
      expEarned: exp,
      isCompleted: true,
    );
  }

  /// Get formatted duration string (MM:SS or HH:MM:SS)
  String get formattedDuration {
    final hours = durationSeconds ~/ 3600;
    final minutes = (durationSeconds % 3600) ~/ 60;
    final seconds = durationSeconds % 60;
    
    if (hours > 0) {
      return '${hours.toString().padLeft(2, '0')}:'
             '${minutes.toString().padLeft(2, '0')}:'
             '${seconds.toString().padLeft(2, '0')}';
    } else {
      return '${minutes.toString().padLeft(2, '0')}:'
             '${seconds.toString().padLeft(2, '0')}';
    }
  }

  /// Get formatted date string for display
  String get formattedDate {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final sessionDate = DateTime(createdAt.year, createdAt.month, createdAt.day);
    
    if (sessionDate == today) {
      return 'Today';
    } else if (sessionDate == today.subtract(const Duration(days: 1))) {
      return 'Yesterday';
    } else {
      return '${createdAt.day.toString().padLeft(2, '0')}.'
             '${createdAt.month.toString().padLeft(2, '0')}.'
             '${createdAt.year.toString().substring(2)}';
    }
  }

  /// Convert to JSON for storage
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'label': label,
      'notes': notes,
      'currentGoal': currentGoal,
      'durationSeconds': durationSeconds,
      'bodyweightKg': bodyweightKg,
      'imagePath': imagePath,
      'createdAt': createdAt.toIso8601String(),
      'completedAt': completedAt?.toIso8601String(),
      'expEarned': expEarned,
      'isCompleted': isCompleted,
    };
  }

  /// Create from JSON
  factory TrainingSession.fromJson(Map<String, dynamic> json) {
    return TrainingSession(
      id: json['id'] as String,
      label: json['label'] as String,
      notes: json['notes'] as String,
      currentGoal: json['currentGoal'] as String? ?? '', // Default to empty string for backward compatibility
      durationSeconds: json['durationSeconds'] as int,
      bodyweightKg: json['bodyweightKg'] as double?,
      imagePath: json['imagePath'] as String?, // Backward compatibility - null if not present
      createdAt: DateTime.parse(json['createdAt'] as String),
      completedAt: json['completedAt'] != null
          ? DateTime.parse(json['completedAt'] as String)
          : null,
      expEarned: json['expEarned'] is int
          ? (json['expEarned'] as int).toDouble()
          : (json['expEarned'] as num).toDouble(),
      isCompleted: json['isCompleted'] as bool,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TrainingSession && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'TrainingSession(id: $id, label: $label, duration: $formattedDuration, exp: $expEarned)';
  }
}
