import 'dart:math';
import '../quests/north_star_model.dart';
import 'diary_entry_model.dart';
import '../utils/rank_utils.dart';
import 'habit_model.dart';
import 'onboarding_progress.dart';

/// Core constants for level calculations
const int kExpPerLevel = 100;
const int kMaxLevel = 100;
const int kMaxExp = kExpPerLevel * kMaxLevel;

/// Represents a user in the Maxed Out Life gamification system.
///
/// The [User] class is the core data model that tracks a user's progress
/// across four main categories: Health, Wealth, Purpose, and Connection.
/// It includes experience points, streaks, habits, diary entries, and
/// various gamification elements like levels and ranks.
///
/// Example:
/// ```dart
/// final user = User.blank(id: 'user123', username: 'john_doe');
/// final updatedUser = user.copyWith(exp: 100, level: 2);
/// ```
class User {
  /// Unique identifier for the user
  final String id;

  /// Display name chosen by the user
  final String username;

  /// User's gender (optional, defaults to 'Unknown')
  final String gender;

  /// Total experience points earned across all categories
  final int exp;

  /// Current daily habit completion streak
  final int streak;

  /// Experience points per category (Health, Wealth, Purpose, Connection)
  final Map<String, int> categories;

  /// List of diary entries with experience logs
  final List<DiaryEntry> diaryEntries;

  /// User's North Star quest (long-term goal), if set
  final NorthStarQuest? northStarQuest;

  /// When the user account was created
  final DateTime createdAt;

  /// Last time the user logged into the app
  final DateTime lastLoginAt;

  /// List of daily habits the user is tracking
  final List<Habit> dailyHabits;

  /// Custom categories created by the user beyond the default four
  final List<String> customCategories;

  /// Experience points earned in the previous week, by category
  final Map<String, int> lastWeekExp;

  /// Optional passcode for app security
  final String? passcode;

  /// Current level based on total experience points
  final int level;

  /// Current rank title (e.g., 'Novice', 'Expert', 'Master')
  final String rank;

  /// Progress towards next rank (0.0 to 1.0)
  final double rankProgress;

  /// Number of consecutive days with completed habits
  final int streakDays;

  /// Experience points earned from challenge/bounty completion
  final int challengeExp;

  /// List of completed quests and challenges
  final List<dynamic> quests;

  /// Last time any user data was modified
  final DateTime lastModified;

  /// Diary entries stored as key-value pairs
  final Map<String, dynamic> diary;

  /// Progress towards daily goal (0-100)
  final int dailyGoalProgress;

  /// Last motivational quote shown in notifications
  final String? lastNotificationQuote;

  /// Last time user data was updated
  final DateTime lastUpdated;

  /// Whether to show the level widget on home screen
  final bool showHomeLevelWidget;

  /// Whether to show progress on device lock screen widget
  final bool showLockScreenWidget;
  final OnboardingProgress onboardingProgress;

  /// User's email address for authentication and communication
  final String? email;

  /// Hashed password for authentication (never store plain text)
  final String? passwordHash;

  /// Whether the user's email has been verified
  final bool isEmailVerified;

  /// Whether the user has been successfully added to Klaviyo email list
  final bool klaviyoSubscribed;

  /// Assigned coaches for non-gender users (stored permanently)
  final Map<String, String>? assignedCoaches;

  /// Daily EXP tracking for bonus spinner system
  final int dailyExpTotal;

  /// Last time daily EXP was reset (for midnight reset)
  final DateTime lastExpReset;

  /// Active bonus categories that have 25% chance for bonus EXP
  final List<String> activeBonusCategories;

  /// Number of spinner plays available (unlocked every 20 EXP after 40)
  final int availableSpinnerPlays;

  User({
    required this.id,
    required this.username,
    required this.gender,
    required this.exp,
    required this.streak,
    required this.categories,
    required this.diaryEntries,
    required this.northStarQuest,
    required this.createdAt,
    required this.lastLoginAt,
    this.dailyHabits = const [],
    this.customCategories = const [],
    this.lastWeekExp = const {},
    this.passcode,
    required this.level,
    required this.rank,
    required this.rankProgress,
    required this.streakDays,
    required this.challengeExp,
    required this.quests,
    required this.lastModified,
    required this.diary,
    required this.dailyGoalProgress,
    required this.lastNotificationQuote,
    required this.lastUpdated,
    required this.showHomeLevelWidget,
    required this.showLockScreenWidget,
    this.onboardingProgress = const OnboardingProgress(),
    this.email,
    this.passwordHash,
    this.isEmailVerified = false,
    this.klaviyoSubscribed = false,
    this.assignedCoaches,
    this.dailyExpTotal = 0,
    DateTime? lastExpReset,
    this.activeBonusCategories = const [],
    this.availableSpinnerPlays = 0,
  }) : lastExpReset = lastExpReset ?? DateTime.now();

  /// Creates a new user with default values.
  ///
  /// This factory constructor is used for creating new user accounts
  /// with minimal required information. All other fields are initialized
  /// with sensible defaults.
  ///
  /// Parameters:
  /// - [id]: Unique identifier for the user
  /// - [username]: Display name for the user
  /// - [gender]: User's gender (optional, defaults to 'Unknown')
  ///
  /// Returns a [User] instance with:
  /// - 0 experience points and level 1
  /// - Empty habits and diary entries
  /// - Current timestamp for creation and login dates
  /// - Default UI preferences enabled
  factory User.blank({
    required String id,
    required String username,
    String gender = 'Unknown',
  }) {
    final now = DateTime.now();
    return User(
      id: id,
      username: username,
      gender: gender,
      exp: 0,
      streak: 0,
      categories: {},
      diaryEntries: [],
      northStarQuest: null,
      createdAt: now,
      lastLoginAt: now,
      dailyHabits: [],
      customCategories: [],
      lastWeekExp: {},
      level: 1,
      rank: 'Novice',
      rankProgress: 0.0,
      streakDays: 0,
      challengeExp: 0,
      quests: [],
      lastModified: now,
      diary: {},
      dailyGoalProgress: 0,
      lastNotificationQuote: null,
      lastUpdated: now,
      showHomeLevelWidget: true,
      showLockScreenWidget: true,
      email: null,
      passwordHash: null,
      isEmailVerified: false,
      klaviyoSubscribed: false,
      assignedCoaches: null,
      dailyExpTotal: 0,
      lastExpReset: now,
      activeBonusCategories: const [],
      availableSpinnerPlays: 0,
    );
  }

  /// Creates a copy of this user with the given fields replaced with new values.
  ///
  /// This method is essential for immutable state management. It allows
  /// updating specific user properties while preserving all other data.
  ///
  /// Example:
  /// ```dart
  /// final updatedUser = user.copyWith(
  ///   exp: user.exp + 50,
  ///   level: 3,
  ///   lastModified: DateTime.now(),
  /// );
  /// ```
  ///
  /// Returns a new [User] instance with updated values.
  User copyWith({
    String? id,
    String? username,
    String? gender,
    int? exp,
    int? streak,
    Map<String, int>? categories,
    List<DiaryEntry>? diaryEntries,
    NorthStarQuest? northStarQuest,
    DateTime? createdAt,
    DateTime? lastLoginAt,
    List<Habit>? dailyHabits,
    List<String>? customCategories,
    Map<String, int>? lastWeekExp,
    String? passcode,
    int? level,
    String? rank,
    double? rankProgress,
    int? streakDays,
    int? challengeExp,
    List<dynamic>? quests,
    DateTime? lastModified,
    Map<String, dynamic>? diary,
    int? dailyGoalProgress,
    String? lastNotificationQuote,
    DateTime? lastUpdated,
    bool? showHomeLevelWidget,
    bool? showLockScreenWidget,
    OnboardingProgress? onboardingProgress,
    String? email,
    String? passwordHash,
    bool? isEmailVerified,
    bool? klaviyoSubscribed,
    Map<String, String>? assignedCoaches,
    int? dailyExpTotal,
    DateTime? lastExpReset,
    List<String>? activeBonusCategories,
    int? availableSpinnerPlays,
  }) {
    return User(
      id: id ?? this.id,
      username: username ?? this.username,
      gender: gender ?? this.gender,
      exp: exp ?? this.exp,
      streak: streak ?? this.streak,
      categories: categories ?? this.categories,
      diaryEntries: diaryEntries ?? this.diaryEntries,
      northStarQuest: northStarQuest ?? this.northStarQuest,
      createdAt: createdAt ?? this.createdAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      dailyHabits: dailyHabits ?? this.dailyHabits,
      customCategories: customCategories ?? this.customCategories,
      lastWeekExp: lastWeekExp ?? this.lastWeekExp,
      passcode: passcode ?? this.passcode,
      level: level ?? this.level,
      rank: rank ?? this.rank,
      rankProgress: rankProgress ?? this.rankProgress,
      streakDays: streakDays ?? this.streakDays,
      challengeExp: challengeExp ?? this.challengeExp,
      quests: quests ?? this.quests,
      lastModified: lastModified ?? this.lastModified,
      diary: diary ?? this.diary,
      dailyGoalProgress: dailyGoalProgress ?? this.dailyGoalProgress,
      lastNotificationQuote: lastNotificationQuote ?? this.lastNotificationQuote,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      showHomeLevelWidget: showHomeLevelWidget ?? this.showHomeLevelWidget,
      showLockScreenWidget: showLockScreenWidget ?? this.showLockScreenWidget,
      onboardingProgress: onboardingProgress ?? this.onboardingProgress,
      email: email ?? this.email,
      passwordHash: passwordHash ?? this.passwordHash,
      isEmailVerified: isEmailVerified ?? this.isEmailVerified,
      klaviyoSubscribed: klaviyoSubscribed ?? this.klaviyoSubscribed,
      assignedCoaches: assignedCoaches ?? this.assignedCoaches,
      dailyExpTotal: dailyExpTotal ?? this.dailyExpTotal,
      lastExpReset: lastExpReset ?? this.lastExpReset,
      activeBonusCategories: activeBonusCategories ?? this.activeBonusCategories,
      availableSpinnerPlays: availableSpinnerPlays ?? this.availableSpinnerPlays,
    );
  }

  User copyWithAddedExp(String category, int exp, String note) {
    final now = DateTime.now();

    // Check if daily EXP should be reset
    final shouldReset = _shouldResetDailyExp(lastExpReset);
    final newBonusCategories = shouldReset ? <String>[] : activeBonusCategories;
    final newSpinnerPlays = shouldReset ? 0 : availableSpinnerPlays;

    // Calculate bonus EXP if category has active bonus
    int bonusExp = 0;
    if (!shouldReset && newBonusCategories.contains(category)) {
      bonusExp = _calculateBonusExp();
    }

    final totalExp = exp + bonusExp;
    final newDailyTotal = shouldReset ? totalExp : dailyExpTotal + totalExp;
    final newLastReset = shouldReset ? now : lastExpReset;

    // Create diary entry with bonus EXP included
    final entryNote = bonusExp > 0 ? '$note (+$bonusExp bonus EXP!)' : note;
    final newEntry = DiaryEntry.create(
      category: category,
      note: entryNote,
      exp: totalExp,
    );

    final updatedCategories = Map<String, int>.from(categories);
    updatedCategories[category] = (updatedCategories[category] ?? 0) + totalExp;

    // Calculate new spinner plays based on daily EXP thresholds
    final spinnerPlaysToAdd = _calculateNewSpinnerPlays(dailyExpTotal, newDailyTotal);

    return copyWith(
      categories: updatedCategories,
      diaryEntries: [newEntry, ...diaryEntries],
      exp: this.exp + totalExp, // Update total EXP with bonus included
      lastLoginAt: now,
      lastModified: now,
      dailyExpTotal: newDailyTotal,
      lastExpReset: newLastReset,
      activeBonusCategories: newBonusCategories,
      availableSpinnerPlays: newSpinnerPlays + spinnerPlaysToAdd,
    );
  }

  /// Check if daily EXP should be reset (midnight in user's timezone)
  bool _shouldResetDailyExp(DateTime lastReset) {
    final now = DateTime.now();
    final lastResetDate = DateTime(lastReset.year, lastReset.month, lastReset.day);
    final todayDate = DateTime(now.year, now.month, now.day);

    return todayDate.isAfter(lastResetDate);
  }

  /// Calculate new spinner plays to add based on EXP thresholds
  int _calculateNewSpinnerPlays(int oldDailyExp, int newDailyExp) {
    const int unlockThreshold = 40;
    const int intervalThreshold = 20;

    int oldPlays = 0;
    if (oldDailyExp >= unlockThreshold) {
      oldPlays = 1 + ((oldDailyExp - unlockThreshold) ~/ intervalThreshold);
    }

    int newPlays = 0;
    if (newDailyExp >= unlockThreshold) {
      newPlays = 1 + ((newDailyExp - unlockThreshold) ~/ intervalThreshold);
    }

    return newPlays - oldPlays;
  }

  /// Calculate bonus EXP with 25% chance (5-20 EXP range)
  int _calculateBonusExp() {
    const int minBonusExp = 5;
    const int maxBonusExp = 20;
    const double bonusChance = 0.25; // 25% chance

    // Use proper random number generation (consistent with RewardEngine)
    final random = Random();

    // 25% chance to win bonus
    if (random.nextDouble() > bonusChance) return 0;

    // Random bonus between 5-20 EXP (inclusive)
    final bonusExp = minBonusExp + random.nextInt(maxBonusExp - minBonusExp + 1);

    return bonusExp;
  }

  int get totalExp => exp + challengeExp;
  int get expToNextLevel => LevelUtils.getExpToNextRank(exp);
  double get levelProgress => LevelUtils.getRankProgressPct(exp);

  int getExp(String category) => categories[category] ?? 0;
  int getLastWeekExp(String category) => lastWeekExp[category] ?? 0;

  Map<String, dynamic> toJson() => {
    'id': id,
    'username': username,
    'gender': gender,
    'exp': exp,
    'streak': streak,
    'categories': categories,
    'diaryEntries': DiaryEntry.listToJson(diaryEntries),
    'northStarQuest': northStarQuest?.toJson(),
    'createdAt': createdAt.toIso8601String(),
    'lastLoginAt': lastLoginAt.toIso8601String(),
    'dailyHabits': dailyHabits.map((h) => h.toJson()).toList(),
    'customCategories': customCategories,
    'lastWeekExp': lastWeekExp,
    'passcode': passcode,
    'level': level,
    'rank': rank,
    'rankProgress': rankProgress,
    'streakDays': streakDays,
    'challengeExp': challengeExp,
    'quests': quests,
    'lastModified': lastModified.toIso8601String(),
    'diary': diary,
    'dailyGoalProgress': dailyGoalProgress,
    'lastNotificationQuote': lastNotificationQuote,
    'lastUpdated': lastUpdated.toIso8601String(),
    'showHomeLevelWidget': showHomeLevelWidget,
    'showLockScreenWidget': showLockScreenWidget,
    'onboardingProgress': onboardingProgress.toJson(),
    'email': email,
    'passwordHash': passwordHash,
    'isEmailVerified': isEmailVerified,
    'klaviyoSubscribed': klaviyoSubscribed,
    'assignedCoaches': assignedCoaches,
    'dailyExpTotal': dailyExpTotal,
    'lastExpReset': lastExpReset.toIso8601String(),
    'activeBonusCategories': activeBonusCategories,
    'availableSpinnerPlays': availableSpinnerPlays,
  };

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'] as String,
      username: json['username'] as String,
      gender: json['gender'] as String,
      exp: json['exp'] as int,
      streak: json['streak'] as int,
      categories: Map<String, int>.from(json['categories'] as Map),
      diaryEntries: DiaryEntry.listFromJson(json['diaryEntries'] as List),
      northStarQuest: json['northStarQuest'] != null
          ? NorthStarQuest.fromJson(json['northStarQuest'] as Map<String, dynamic>)
          : null,
      createdAt: DateTime.parse(json['createdAt'] as String),
      lastLoginAt: DateTime.parse(json['lastLoginAt'] as String),
      dailyHabits: (json['dailyHabits'] as List)
          .map((h) => Habit.fromJson(h as Map<String, dynamic>))
          .toList(),
      customCategories: List<String>.from(json['customCategories'] as List),
      lastWeekExp: Map<String, int>.from(json['lastWeekExp'] as Map? ?? {}),
      passcode: json['passcode'] as String?,
      level: json['level'] as int? ?? 1,
      rank: json['rank'] as String? ?? 'Novice',
      rankProgress: (json['rankProgress'] as num?)?.toDouble() ?? 0.0,
      streakDays: json['streakDays'] as int? ?? 0,
      challengeExp: json['challengeExp'] as int? ?? 0,
      quests: json['quests'] as List? ?? [],
      lastModified: DateTime.parse(json['lastModified'] as String? ?? json['createdAt'] as String),
      diary: json['diary'] as Map<String, dynamic>? ?? {},
      dailyGoalProgress: json['dailyGoalProgress'] as int? ?? 0,
      lastNotificationQuote: json['lastNotificationQuote'] as String?,
      lastUpdated: DateTime.parse(json['lastUpdated'] as String? ?? json['createdAt'] as String),
      showHomeLevelWidget: json['showHomeLevelWidget'] as bool? ?? true,
      showLockScreenWidget: json['showLockScreenWidget'] as bool? ?? true,
      onboardingProgress: json['onboardingProgress'] != null
        ? OnboardingProgress.fromJson(json['onboardingProgress'])
        : const OnboardingProgress(),
      email: json['email'] as String?,
      passwordHash: json['passwordHash'] as String?,
      isEmailVerified: json['isEmailVerified'] as bool? ?? false,
      klaviyoSubscribed: json['klaviyoSubscribed'] as bool? ?? false,
      assignedCoaches: json['assignedCoaches'] != null
        ? Map<String, String>.from(json['assignedCoaches'] as Map)
        : null,
      dailyExpTotal: json['dailyExpTotal'] as int? ?? 0,
      lastExpReset: json['lastExpReset'] != null
        ? DateTime.parse(json['lastExpReset'] as String)
        : DateTime.now(),
      activeBonusCategories: json['activeBonusCategories'] != null
        ? List<String>.from(json['activeBonusCategories'] as List)
        : const [],
      availableSpinnerPlays: json['availableSpinnerPlays'] as int? ?? 0,
    );
  }
}
