// 📁 lib/models/guest_user_model.dart

import 'package:flutter/material.dart';
import 'user_model.dart';
import 'diary_entry_model.dart';
import 'habit_model.dart';
import 'onboarding_progress.dart';
import '../quests/north_star_model.dart';

/// Guest user model for browsing the app without account creation
/// 
/// Provides a temporary user experience that mimics the full User model
/// but with session-only data that doesn't persist. Used to comply with
/// Apple App Store guidelines allowing users to browse non-account features.
class GuestUser {
  /// Unique session ID for this guest user
  final String sessionId;
  
  /// When this guest session was created
  final DateTime sessionStarted;
  
  /// Whether this is a guest user (always true)
  final bool isGuest = true;
  
  /// Demo data for showing app capabilities
  final int demoExp;
  final Map<String, int> demoCategories;
  final List<String> demoCustomCategories;
  
  /// Session preferences (not persisted)
  final bool hasSeenTutorial;
  final bool hasSeenCoachDemo;
  final Set<String> viewedFeatures;
  
  const GuestUser({
    required this.sessionId,
    required this.sessionStarted,
    this.demoExp = 150,
    this.demoCategories = const {
      'Health': 45,
      'Wealth': 30,
      'Purpose': 40,
      'Connection': 35,
    },
    this.demoCustomCategories = const [],
    this.hasSeenTutorial = false,
    this.hasSeenCoachDemo = false,
    this.viewedFeatures = const {},
  });
  
  /// Create a new guest user session
  factory GuestUser.createSession() {
    final now = DateTime.now();
    final sessionId = 'guest_${now.millisecondsSinceEpoch}';
    
    return GuestUser(
      sessionId: sessionId,
      sessionStarted: now,
    );
  }
  
  /// Convert guest user to a User model for compatibility with existing UI
  /// This allows guest users to browse the app using the same components
  User toUserModel() {
    final now = DateTime.now();
    
    return User(
      id: sessionId,
      username: 'Guest User',
      gender: 'Non-Gender',
      exp: demoExp,
      streak: 3, // Demo streak
      categories: demoCategories,
      diaryEntries: _createDemoDiaryEntries(),
      northStarQuest: _createDemoNorthStarQuest(),
      createdAt: sessionStarted,
      lastLoginAt: sessionStarted,
      dailyHabits: _createDemoHabits(),
      customCategories: demoCustomCategories,
      lastWeekExp: _createDemoWeeklyExp(),
      passcode: null,
      level: 3, // Demo level
      rank: 'Explorer',
      rankProgress: 0.6,
      streakDays: 3,
      challengeExp: 25,
      quests: [],
      lastModified: now,
      diary: _createDemoDiary(),
      dailyGoalProgress: 75,
      lastNotificationQuote: null,
      lastUpdated: now,
      showHomeLevelWidget: true,
      showLockScreenWidget: false, // Don't show for guests
      onboardingProgress: OnboardingProgress(
        hasCompletedWelcome: true,
        hasCreatedNorthStar: true,
        hasSetCategories: true,
        hasMetCoaches: hasSeenCoachDemo,
        hasSetHabits: true,
        hasCompletedTutorial: hasSeenTutorial,
        lastUpdated: now,
      ),
      email: null,
      passwordHash: null,
      isEmailVerified: false,
      klaviyoSubscribed: false,
    );
  }
  
  /// Create demo diary entries to show app functionality
  List<DiaryEntry> _createDemoDiaryEntries() {
    final now = DateTime.now();
    return [
      DiaryEntry(
        id: 'demo_1',
        category: 'Health',
        note: 'Completed morning workout - feeling energized!',
        exp: 15,
        timestamp: now.subtract(const Duration(hours: 2)),
        createdAt: now.subtract(const Duration(hours: 2)),
        lastModified: now.subtract(const Duration(hours: 2)),
      ),
      DiaryEntry(
        id: 'demo_2',
        category: 'Purpose',
        note: 'Made progress on my side project today',
        exp: 20,
        timestamp: now.subtract(const Duration(days: 1)),
        createdAt: now.subtract(const Duration(days: 1)),
        lastModified: now.subtract(const Duration(days: 1)),
      ),
    ];
  }
  
  /// Create demo North Star quest
  NorthStarQuest _createDemoNorthStarQuest() {
    return NorthStarQuest(
      id: 'demo_quest',
      title: 'Build a Healthier Lifestyle',
      summary: 'Focus on consistent exercise, better nutrition, and mental wellness',
      glowColor: const Color(0xFF00FF88),
      coreValues: ['Health', 'Consistency', 'Growth'],
      createdAt: sessionStarted,
      logs: [],
      totalExp: 45,
      hoursLogged: 12.5,
      milestones: ['First week completed', 'Habit streak established'],
      visionImagePath: null,
      icon: '🌟',
      category: 'Health',
    );
  }
  
  /// Create demo habits
  List<Habit> _createDemoHabits() {
    final now = DateTime.now();
    return [
      Habit(
        id: 'demo_habit_1',
        name: 'Morning Exercise',
        description: '30 minutes of physical activity',
        color: Colors.green,
        streak: 5,
        lastCompleted: now.subtract(const Duration(days: 1)),
        createdAt: sessionStarted,
        lastModified: now,
      ),
      Habit(
        id: 'demo_habit_2',
        name: 'Read for 30 minutes',
        description: 'Daily reading for personal growth',
        color: Colors.blue,
        streak: 3,
        lastCompleted: now.subtract(const Duration(days: 2)),
        createdAt: sessionStarted,
        lastModified: now,
      ),
    ];
  }
  
  /// Create demo weekly EXP data
  Map<String, int> _createDemoWeeklyExp() {
    return {
      'Health': 25,
      'Wealth': 15,
      'Purpose': 30,
      'Connection': 20,
    };
  }
  
  /// Create demo diary map
  Map<String, dynamic> _createDemoDiary() {
    return {
      'totalEntries': 12,
      'weeklyEntries': 4,
      'favoriteCategory': 'Health',
    };
  }
  
  /// Update guest session with viewed feature
  GuestUser markFeatureViewed(String feature) {
    final updatedFeatures = Set<String>.from(viewedFeatures)..add(feature);
    return copyWith(viewedFeatures: updatedFeatures);
  }
  
  /// Mark tutorial as seen
  GuestUser markTutorialSeen() {
    return copyWith(hasSeenTutorial: true);
  }
  
  /// Mark coach demo as seen
  GuestUser markCoachDemoSeen() {
    return copyWith(hasSeenCoachDemo: true);
  }
  
  /// Create a copy with updated values
  GuestUser copyWith({
    String? sessionId,
    DateTime? sessionStarted,
    int? demoExp,
    Map<String, int>? demoCategories,
    List<String>? demoCustomCategories,
    bool? hasSeenTutorial,
    bool? hasSeenCoachDemo,
    Set<String>? viewedFeatures,
  }) {
    return GuestUser(
      sessionId: sessionId ?? this.sessionId,
      sessionStarted: sessionStarted ?? this.sessionStarted,
      demoExp: demoExp ?? this.demoExp,
      demoCategories: demoCategories ?? this.demoCategories,
      demoCustomCategories: demoCustomCategories ?? this.demoCustomCategories,
      hasSeenTutorial: hasSeenTutorial ?? this.hasSeenTutorial,
      hasSeenCoachDemo: hasSeenCoachDemo ?? this.hasSeenCoachDemo,
      viewedFeatures: viewedFeatures ?? this.viewedFeatures,
    );
  }
  
  @override
  String toString() {
    return 'GuestUser(sessionId: $sessionId, started: $sessionStarted, exp: $demoExp)';
  }
}
