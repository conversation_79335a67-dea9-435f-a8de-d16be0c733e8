// lib/models/conversation_context.dart

/// Represents the context of a conversation for memory tracking
class ConversationContext {
  final String id;
  final String userId;
  final String coachCategory;
  final String userMessage;
  final String coachResponse;
  final DateTime timestamp;
  final String userSentiment;
  final String topicCategory;
  final bool wasHelpful;
  final Map<String, dynamic> metadata;

  ConversationContext({
    required this.id,
    required this.userId,
    required this.coachCategory,
    required this.userMessage,
    required this.coachResponse,
    required this.timestamp,
    required this.userSentiment,
    required this.topicCategory,
    required this.wasHelpful,
    required this.metadata,
  });

  factory ConversationContext.fromJson(Map<String, dynamic> json) {
    return ConversationContext(
      id: json['id'] as String,
      userId: json['userId'] as String,
      coachCategory: json['coachCategory'] as String,
      userMessage: json['userMessage'] as String,
      coachResponse: json['coachResponse'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      userSentiment: json['userSentiment'] as String,
      topicCategory: json['topicCategory'] as String,
      wasHelpful: json['wasHelpful'] as bool,
      metadata: Map<String, dynamic>.from(json['metadata'] as Map),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'coachCategory': coachCategory,
      'userMessage': userMessage,
      'coachResponse': coachResponse,
      'timestamp': timestamp.toIso8601String(),
      'userSentiment': userSentiment,
      'topicCategory': topicCategory,
      'wasHelpful': wasHelpful,
      'metadata': metadata,
    };
  }

  ConversationContext copyWith({
    String? id,
    String? userId,
    String? coachCategory,
    String? userMessage,
    String? coachResponse,
    DateTime? timestamp,
    String? userSentiment,
    String? topicCategory,
    bool? wasHelpful,
    Map<String, dynamic>? metadata,
  }) {
    return ConversationContext(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      coachCategory: coachCategory ?? this.coachCategory,
      userMessage: userMessage ?? this.userMessage,
      coachResponse: coachResponse ?? this.coachResponse,
      timestamp: timestamp ?? this.timestamp,
      userSentiment: userSentiment ?? this.userSentiment,
      topicCategory: topicCategory ?? this.topicCategory,
      wasHelpful: wasHelpful ?? this.wasHelpful,
      metadata: metadata ?? this.metadata,
    );
  }
}

/// Represents user behavioral patterns over time
class UserPatterns {
  final String userId;
  final Map<String, double> communicationStyle;
  final Map<String, int> topicFrequency;
  final Map<String, double> sentimentTrends;
  final Map<String, List<int>> activityPatterns;
  final Map<String, double> responseEffectiveness;
  final DateTime lastUpdated;

  UserPatterns({
    required this.userId,
    required this.communicationStyle,
    required this.topicFrequency,
    required this.sentimentTrends,
    required this.activityPatterns,
    required this.responseEffectiveness,
    required this.lastUpdated,
  });

  factory UserPatterns.empty(String userId) {
    return UserPatterns(
      userId: userId,
      communicationStyle: {},
      topicFrequency: {},
      sentimentTrends: {},
      activityPatterns: {},
      responseEffectiveness: {},
      lastUpdated: DateTime.now(),
    );
  }

  factory UserPatterns.fromJson(Map<String, dynamic> json) {
    return UserPatterns(
      userId: json['userId'] as String,
      communicationStyle: Map<String, double>.from(json['communicationStyle'] as Map),
      topicFrequency: Map<String, int>.from(json['topicFrequency'] as Map),
      sentimentTrends: Map<String, double>.from(json['sentimentTrends'] as Map),
      activityPatterns: (json['activityPatterns'] as Map).map(
        (key, value) => MapEntry(key as String, List<int>.from(value as List)),
      ),
      responseEffectiveness: Map<String, double>.from(json['responseEffectiveness'] as Map),
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'communicationStyle': communicationStyle,
      'topicFrequency': topicFrequency,
      'sentimentTrends': sentimentTrends,
      'activityPatterns': activityPatterns,
      'responseEffectiveness': responseEffectiveness,
      'lastUpdated': lastUpdated.toIso8601String(),
    };
  }

  UserPatterns copyWith({
    String? userId,
    Map<String, double>? communicationStyle,
    Map<String, int>? topicFrequency,
    Map<String, double>? sentimentTrends,
    Map<String, List<int>>? activityPatterns,
    Map<String, double>? responseEffectiveness,
    DateTime? lastUpdated,
  }) {
    return UserPatterns(
      userId: userId ?? this.userId,
      communicationStyle: communicationStyle ?? this.communicationStyle,
      topicFrequency: topicFrequency ?? this.topicFrequency,
      sentimentTrends: sentimentTrends ?? this.sentimentTrends,
      activityPatterns: activityPatterns ?? this.activityPatterns,
      responseEffectiveness: responseEffectiveness ?? this.responseEffectiveness,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }
}

/// Represents core personality traits and preferences
class UserPersonality {
  final String userId;
  final Map<String, double> traits;
  final Map<String, String> preferences;
  final Map<String, double> motivationFactors;
  final Map<String, String> communicationPreferences;
  final List<String> coreValues;
  final DateTime lastUpdated;

  UserPersonality({
    required this.userId,
    required this.traits,
    required this.preferences,
    required this.motivationFactors,
    required this.communicationPreferences,
    required this.coreValues,
    required this.lastUpdated,
  });

  factory UserPersonality.empty(String userId) {
    return UserPersonality(
      userId: userId,
      traits: {},
      preferences: {},
      motivationFactors: {},
      communicationPreferences: {},
      coreValues: [],
      lastUpdated: DateTime.now(),
    );
  }

  factory UserPersonality.fromJson(Map<String, dynamic> json) {
    return UserPersonality(
      userId: json['userId'] as String,
      traits: Map<String, double>.from(json['traits'] as Map),
      preferences: Map<String, String>.from(json['preferences'] as Map),
      motivationFactors: Map<String, double>.from(json['motivationFactors'] as Map),
      communicationPreferences: Map<String, String>.from(json['communicationPreferences'] as Map),
      coreValues: List<String>.from(json['coreValues'] as List),
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'traits': traits,
      'preferences': preferences,
      'motivationFactors': motivationFactors,
      'communicationPreferences': communicationPreferences,
      'coreValues': coreValues,
      'lastUpdated': lastUpdated.toIso8601String(),
    };
  }

  UserPersonality copyWith({
    String? userId,
    Map<String, double>? traits,
    Map<String, String>? preferences,
    Map<String, double>? motivationFactors,
    Map<String, String>? communicationPreferences,
    List<String>? coreValues,
    DateTime? lastUpdated,
  }) {
    return UserPersonality(
      userId: userId ?? this.userId,
      traits: traits ?? this.traits,
      preferences: preferences ?? this.preferences,
      motivationFactors: motivationFactors ?? this.motivationFactors,
      communicationPreferences: communicationPreferences ?? this.communicationPreferences,
      coreValues: coreValues ?? this.coreValues,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }
}
