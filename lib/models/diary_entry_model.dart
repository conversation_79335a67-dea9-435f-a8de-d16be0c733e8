// 📁 lib/models/diary_entry_model.dart

import 'package:flutter/foundation.dart';
import 'package:uuid/uuid.dart';
import '../utils/date_formatter.dart';

@immutable
class DiaryEntry {
  /// Unique identifier for this entry
  final String id;
  /// Timestamp of the entry
  final DateTime timestamp;
  /// Category under which this entry was logged
  final String category;
  /// User note for this entry
  final String note;
  /// Experience points awarded for this entry
  final int exp;
  /// Date of creation
  final DateTime createdAt;
  /// Date of last modification
  final DateTime lastModified;

  /// Creates a DiaryEntry. [id] will be generated if not provided.
  const DiaryEntry({
    required this.id,
    required this.timestamp,
    required this.category,
    required this.note,
    required this.exp,
    required this.createdAt,
    required this.lastModified,
  });

  /// Easy factory for new entries at "now"
  factory DiaryEntry.create({
    required String category,
    required String note,
    required int exp,
  }) {
    return DiaryEntry(
      id: const Uuid().v4(),
      timestamp: DateTime.now(),
      category: category,
      note: note,
      exp: exp,
      createdAt: DateTime.now(),
      lastModified: DateTime.now(),
    );
  }

  /// Human-readable full date+time
  String get formattedTimestamp => DateFormatter.formatDateTime(timestamp);

  /// Human-readable date only
  String get formattedDate => DateFormatter.formatDate(timestamp);

  /// Immutable copier
  DiaryEntry copyWith({
    String? id,
    DateTime? timestamp,
    String? category,
    String? note,
    int? exp,
    DateTime? createdAt,
    DateTime? lastModified,
  }) {
    return DiaryEntry(
      id: id ?? this.id,
      timestamp: timestamp ?? this.timestamp,
      category: category ?? this.category,
      note: note ?? this.note,
      exp: exp ?? this.exp,
      createdAt: createdAt ?? this.createdAt,
      lastModified: lastModified ?? this.lastModified,
    );
  }

  /// Convert one entry to JSON
  Map<String, dynamic> toJson() => {
        'id': id,
        'timestamp': timestamp.toIso8601String(),
        'category': category,
        'note': note,
        'exp': exp,
        'createdAt': createdAt.toIso8601String(),
        'lastModified': lastModified.toIso8601String(),
      };

  /// Build entry from JSON, with safe fallback for timestamp
  factory DiaryEntry.fromJson(Map<String, dynamic> json) {
    final raw = json['timestamp'];
    DateTime timestamp;
    if (raw is String) {
      timestamp = DateTime.tryParse(raw) ?? DateTime.now();
    } else if (raw is DateTime) {
      timestamp = raw;
    } else {
      timestamp = DateTime.now();
    }
    return DiaryEntry(
      id: json['id'] as String,
      timestamp: timestamp,
      category: json['category'] as String? ?? 'General',
      note: json['note'] as String? ?? '',
      exp: json['exp'] as int? ?? 0,
      createdAt: DateTime.parse(json['createdAt'] as String),
      lastModified: DateTime.parse(json['lastModified'] as String),
    );
  }

  /// Encode a list of entries into JSON-like List<Map>
  static List<Map<String, dynamic>> listToJson(List<DiaryEntry> entries) =>
      entries.map((e) => e.toJson()).toList();

  /// Decode a list of entries from map payloads
  static List<DiaryEntry> listFromJson(List<dynamic> list) =>
      list.map((e) => DiaryEntry.fromJson(Map<String, dynamic>.from(e))).toList();

  @override
  String toString() =>
      'DiaryEntry(id: $id, timestamp: $timestamp, category: $category, note: $note, exp: $exp)';

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is DiaryEntry &&
          other.id == id &&
          other.timestamp == timestamp &&
          other.category == category &&
          other.note == note &&
          other.exp == exp;

  @override
  int get hashCode => Object.hash(id, timestamp, category, note, exp);
}
