//pool reset immediatelyr/material.dart';

class OnboardingProgress {
  final bool hasCompletedWelcome;
  final bool hasCreatedNorthStar;
  final bool hasSetCategories;
  final bool hasMetCoaches;
  final bool hasSetHabits;
  final bool hasCompletedTutorial;
  final DateTime? lastUpdated;

  const OnboardingProgress({
    this.hasCompletedWelcome = false,
    this.hasCreatedNorthStar = false,
    this.hasSetCategories = false,
    this.hasMetCoaches = false,
    this.hasSetHabits = false,
    this.hasCompletedTutorial = false,
    this.lastUpdated,
  });

  OnboardingProgress copyWith({
    bool? hasCompletedWelcome,
    bool? hasCreatedNorthStar,
    bool? hasSetCategories,
    bool? hasMetCoaches,
    bool? hasSetHabits,
    bool? hasCompletedTutorial,
  }) {
    return OnboardingProgress(
      hasCompletedWelcome: hasCompletedWelcome ?? this.hasCompletedWelcome,
      hasCreatedNorthStar: hasCreatedNorthStar ?? this.hasCreatedNorthStar,
      hasSetCategories: hasSetCategories ?? this.hasSetCategories,
      hasMetCoaches: hasMetCoaches ?? this.hasMetCoaches,
      hasSetHabits: hasSetHabits ?? this.hasSetHabits,
      hasCompletedTutorial: hasCompletedTutorial ?? this.hasCompletedTutorial,
      lastUpdated: DateTime.now(),
    );
  }

  bool get isComplete => 
    hasCompletedWelcome && 
    hasCreatedNorthStar && 
    hasSetCategories && 
    hasMetCoaches && 
    hasSetHabits &&
    hasCompletedTutorial;

  double get progressPercentage {
    final totalSteps = 6;
    final completedSteps = [
      hasCompletedWelcome,
      hasCreatedNorthStar,
      hasSetCategories,
      hasMetCoaches,
      hasSetHabits,
      hasCompletedTutorial,
    ].where((step) => step).length;
    
    return completedSteps / totalSteps;
  }

  Map<String, dynamic> toJson() => {
    'hasCompletedWelcome': hasCompletedWelcome,
    'hasCompletedNorthStar': hasCreatedNorthStar,
    'hasSetCategories': hasSetCategories,
    'hasMetCoaches': hasMetCoaches,
    'hasSetHabits': hasSetHabits,
    'hasCompletedTutorial': hasCompletedTutorial,
    'lastUpdated': lastUpdated?.toIso8601String(),
  };

  factory OnboardingProgress.fromJson(Map<String, dynamic> json) => OnboardingProgress(
    hasCompletedWelcome: json['hasCompletedWelcome'] ?? false,
    hasCreatedNorthStar: json['hasCompletedNorthStar'] ?? false,
    hasSetCategories: json['hasSetCategories'] ?? false,
    hasMetCoaches: json['hasMetCoaches'] ?? false,
    hasSetHabits: json['hasSetHabits'] ?? false,
    hasCompletedTutorial: json['hasCompletedTutorial'] ?? false,
    lastUpdated: json['lastUpdated'] != null 
      ? DateTime.parse(json['lastUpdated']) 
      : null,
  );
} 