// lib/models/training_timer_model.dart

import 'package:flutter/foundation.dart';

/// Represents the current state of the training timer
@immutable
class TrainingTimerState {
  /// Current elapsed time in seconds
  final int elapsedSeconds;
  
  /// Whether the timer is currently running
  final bool isRunning;
  
  /// Whether the timer has been started at least once
  final bool hasStarted;
  
  /// When the timer was last started (for calculating elapsed time)
  final DateTime? lastStartTime;
  
  /// Total accumulated time from previous start/pause cycles
  final int accumulatedSeconds;

  const TrainingTimerState({
    required this.elapsedSeconds,
    required this.isRunning,
    required this.hasStarted,
    this.lastStartTime,
    required this.accumulatedSeconds,
  });

  /// Initial timer state (reset)
  factory TrainingTimerState.initial() {
    return const TrainingTimerState(
      elapsedSeconds: 0,
      isRunning: false,
      hasStarted: false,
      lastStartTime: null,
      accumulatedSeconds: 0,
    );
  }

  /// Start the timer
  TrainingTimerState start() {
    return TrainingTimerState(
      elapsedSeconds: elapsedSeconds,
      isRunning: true,
      hasStarted: true,
      lastStartTime: DateTime.now(),
      accumulatedSeconds: accumulatedSeconds,
    );
  }

  /// Pause the timer
  TrainingTimerState pause() {
    final now = DateTime.now();
    final additionalSeconds = lastStartTime != null 
        ? now.difference(lastStartTime!).inSeconds 
        : 0;
    
    return TrainingTimerState(
      elapsedSeconds: accumulatedSeconds + additionalSeconds,
      isRunning: false,
      hasStarted: hasStarted,
      lastStartTime: null,
      accumulatedSeconds: accumulatedSeconds + additionalSeconds,
    );
  }

  /// Reset the timer to initial state
  TrainingTimerState reset() {
    return TrainingTimerState.initial();
  }

  /// Update elapsed time (called periodically when running)
  TrainingTimerState updateElapsed() {
    if (!isRunning || lastStartTime == null) {
      return this;
    }
    
    final now = DateTime.now();
    final currentElapsed = accumulatedSeconds + now.difference(lastStartTime!).inSeconds;
    
    return TrainingTimerState(
      elapsedSeconds: currentElapsed,
      isRunning: isRunning,
      hasStarted: hasStarted,
      lastStartTime: lastStartTime,
      accumulatedSeconds: accumulatedSeconds,
    );
  }

  /// Get formatted time string for display (MM:SS or HH:MM:SS)
  String get formattedTime {
    final hours = elapsedSeconds ~/ 3600;
    final minutes = (elapsedSeconds % 3600) ~/ 60;
    final seconds = elapsedSeconds % 60;
    
    if (hours > 0) {
      return '${hours.toString().padLeft(2, '0')}:'
             '${minutes.toString().padLeft(2, '0')}:'
             '${seconds.toString().padLeft(2, '0')}';
    } else {
      return '${minutes.toString().padLeft(2, '0')}:'
             '${seconds.toString().padLeft(2, '0')}';
    }
  }

  /// Calculate current EXP earned (12.5 EXP per hour with 25% boost)
  /// Base Health EXP: 10 per hour, Training Tracker boost: +25% = 12.5 per hour
  /// Per minute: 12.5 ÷ 60 = 0.208333... EXP per minute
  /// Rounded to nearest half (0, 0.5, 1.0, 1.5, 2.0, etc.)
  double get currentExp {
    final minutes = elapsedSeconds / 60.0;
    final rawExp = minutes * (12.5 / 60.0); // 0.208333... EXP per minute
    return _roundToNearestHalf(rawExp);
  }

  /// Round to nearest half number (0, 0.5, 1.0, 1.5, 2.0, etc.)
  static double _roundToNearestHalf(double value) {
    return (value * 2).round() / 2.0;
  }

  /// Get formatted EXP string for display
  String get formattedExp {
    return '+${currentExp.toStringAsFixed(1)} EXP';
  }

  /// Check if timer can be started
  bool get canStart => !isRunning;

  /// Check if timer can be paused
  bool get canPause => isRunning;

  /// Check if timer can be reset
  bool get canReset => hasStarted;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TrainingTimerState &&
        other.elapsedSeconds == elapsedSeconds &&
        other.isRunning == isRunning &&
        other.hasStarted == hasStarted &&
        other.lastStartTime == lastStartTime &&
        other.accumulatedSeconds == accumulatedSeconds;
  }

  @override
  int get hashCode {
    return Object.hash(
      elapsedSeconds,
      isRunning,
      hasStarted,
      lastStartTime,
      accumulatedSeconds,
    );
  }

  @override
  String toString() {
    return 'TrainingTimerState(elapsed: $formattedTime, running: $isRunning, started: $hasStarted)';
  }
}

/// Training program configuration for cycling through workouts
@immutable
class TrainingProgram {
  /// List of workout labels in sequence (e.g., ['A', 'B', 'C'] or ['Mon', 'Tue', 'Wed'])
  final List<String> workoutLabels;

  /// Current position in the program cycle
  final int currentIndex;

  /// Type of program (e.g., 'Letters', 'Days', 'Numbers')
  final String programType;

  /// Notes templates for each workout label (optional)
  final Map<String, String> notesTemplates;

  const TrainingProgram({
    required this.workoutLabels,
    required this.currentIndex,
    required this.programType,
    this.notesTemplates = const {},
  });

  /// Default letter-based program (supports up to 21 letters)
  factory TrainingProgram.letters() {
    return const TrainingProgram(
      workoutLabels: ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U'],
      currentIndex: 0,
      programType: 'Letters',
      notesTemplates: {},
    );
  }

  /// Day-based program
  factory TrainingProgram.days() {
    return const TrainingProgram(
      workoutLabels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
      currentIndex: 0,
      programType: 'Days',
      notesTemplates: {},
    );
  }

  /// Number-based program (supports up to 21 numbers)
  factory TrainingProgram.numbers() {
    return const TrainingProgram(
      workoutLabels: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21'],
      currentIndex: 0,
      programType: 'Numbers',
      notesTemplates: {},
    );
  }

  /// Custom program with user-defined labels
  factory TrainingProgram.custom({
    required List<String> customLabels,
    String? customName,
    Map<String, String>? notesTemplates,
  }) {
    // Validate labels
    final validLabels = customLabels
        .where((label) => label.trim().isNotEmpty && label.trim().length <= 30)
        .map((label) => label.trim())
        .toList();

    if (validLabels.isEmpty) {
      // Fallback to default if no valid labels
      return TrainingProgram.letters();
    }

    return TrainingProgram(
      workoutLabels: validLabels,
      currentIndex: 0,
      programType: customName?.trim().isNotEmpty == true ? customName!.trim() : 'Custom',
      notesTemplates: notesTemplates ?? {},
    );
  }

  /// Get current workout label
  String get currentLabel => workoutLabels[currentIndex];

  /// Get notes template for a specific label
  String getNotesTemplate(String label) {
    return notesTemplates[label] ?? '';
  }

  /// Get notes template for current workout
  String get currentNotesTemplate => getNotesTemplate(currentLabel);

  /// Update notes template for a specific label
  TrainingProgram updateNotesTemplate(String label, String notes) {
    final updatedTemplates = Map<String, String>.from(notesTemplates);
    if (notes.trim().isEmpty) {
      updatedTemplates.remove(label);
    } else {
      updatedTemplates[label] = notes.trim();
    }

    return TrainingProgram(
      workoutLabels: workoutLabels,
      currentIndex: currentIndex,
      programType: programType,
      notesTemplates: updatedTemplates,
    );
  }

  /// Advance to next workout in the cycle
  TrainingProgram advance() {
    final nextIndex = (currentIndex + 1) % workoutLabels.length;
    return TrainingProgram(
      workoutLabels: workoutLabels,
      currentIndex: nextIndex,
      programType: programType,
      notesTemplates: notesTemplates,
    );
  }

  /// Reset to first workout
  TrainingProgram reset() {
    return TrainingProgram(
      workoutLabels: workoutLabels,
      currentIndex: 0,
      programType: programType,
      notesTemplates: notesTemplates,
    );
  }

  /// Convert to JSON for storage
  Map<String, dynamic> toJson() {
    return {
      'workoutLabels': workoutLabels,
      'currentIndex': currentIndex,
      'programType': programType,
      'notesTemplates': notesTemplates,
    };
  }

  /// Create from JSON
  factory TrainingProgram.fromJson(Map<String, dynamic> json) {
    return TrainingProgram(
      workoutLabels: List<String>.from(json['workoutLabels'] as List),
      currentIndex: json['currentIndex'] as int,
      programType: json['programType'] as String,
      notesTemplates: Map<String, String>.from(json['notesTemplates'] as Map? ?? {}),
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TrainingProgram &&
        listEquals(other.workoutLabels, workoutLabels) &&
        other.currentIndex == currentIndex &&
        other.programType == programType &&
        mapEquals(other.notesTemplates, notesTemplates);
  }

  @override
  int get hashCode {
    return Object.hash(workoutLabels, currentIndex, programType, notesTemplates);
  }

  @override
  String toString() {
    return 'TrainingProgram(type: $programType, current: $currentLabel, index: $currentIndex)';
  }
}
