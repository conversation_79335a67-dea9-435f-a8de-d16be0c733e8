// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'bounty_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BountyModel _$BountyModelFromJson(Map<String, dynamic> json) => BountyModel(
  id: json['id'] as String,
  description: json['description'] as String,
  categories:
      (json['categories'] as List<dynamic>).map((e) => e as String).toList(),
  expPerCategory: Map<String, int>.from(json['expPerCategory'] as Map),
  difficulty: json['difficulty'] as String,
  isEpic: json['isEpic'] as bool,
  photoProofPath: json['photoProofPath'] as String?,
);

Map<String, dynamic> _$BountyModelToJson(BountyModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'description': instance.description,
      'categories': instance.categories,
      'expPerCategory': instance.expPerCategory,
      'difficulty': instance.difficulty,
      'isEpic': instance.isEpic,
      'photoProofPath': instance.photoProofPath,
    };
