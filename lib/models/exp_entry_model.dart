// 📁 lib/models/exp_entry_model.dart

import 'dart:convert';

class ExpEntry {
  final String id;
  final String category;
  final int amount;
  final String note;
  final String timestamp;
  final Map<String, dynamic>? metadata;

  ExpEntry({
    required this.id,
    required this.category,
    required this.amount,
    required this.note,
    required this.timestamp,
    this.metadata,
  });

  /// Convert single entry to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'category': category,
      'amount': amount,
      'note': note,
      'timestamp': timestamp,
      if (metadata != null) 'metadata': metadata,
    };
  }

  /// Create entry from JSON
  factory ExpEntry.fromJson(Map<String, dynamic> json) {
    return ExpEntry(
      id: json['id'],
      category: json['category'],
      amount: json['amount'],
      note: json['note'],
      timestamp: json['timestamp'],
      metadata: json['metadata'] != null ? Map<String, dynamic>.from(json['metadata']) : null,
    );
  }

  /// Clone entry with optional overrides
  ExpEntry copyWith({
    String? id,
    String? category,
    int? amount,
    String? note,
    String? timestamp,
    Map<String, dynamic>? metadata,
  }) {
    return ExpEntry(
      id: id ?? this.id,
      category: category ?? this.category,
      amount: amount ?? this.amount,
      note: note ?? this.note,
      timestamp: timestamp ?? this.timestamp,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Convert list of entries to JSON string
  static String toJsonList(List<ExpEntry> entries) {
    final List<Map<String, dynamic>> data = entries.map((e) => e.toJson()).toList();
    return jsonEncode(data);
  }

  /// Parse list of entries from JSON string
  static List<ExpEntry> fromJsonList(String source) {
    final List<dynamic> data = jsonDecode(source);
    return data.map((item) => ExpEntry.fromJson(item)).toList();
  }

  /// Get YYYY-MM-DD for filtering/logging/grouping
  String get shortDate => timestamp.split('T').first;
}
