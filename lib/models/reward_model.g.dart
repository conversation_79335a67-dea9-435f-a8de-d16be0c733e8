// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'reward_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

RewardModel _$RewardModelFromJson(Map<String, dynamic> json) => RewardModel(
  totalXp: (json['totalXp'] as num).toInt(),
  dailyEffortHours: (json['dailyEffortHours'] as num).toInt(),
  bonusUnlocked: json['bonusUnlocked'] as bool,
  bonusCategory: json['bonusCategory'] as String?,
  completedQuestIds:
      (json['completedQuestIds'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
  lastResetDate: DateTime.parse(json['lastResetDate'] as String),
  activeBountyId: json['activeBountyId'] as String?,
  completedBountyIds:
      (json['completedBountyIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  bonusExpToday: (json['bonusExpToday'] as num?)?.toInt() ?? 0,
  bonusCategoryToday: json['bonusCategoryToday'] as String?,
);

Map<String, dynamic> _$RewardModelToJson(RewardModel instance) =>
    <String, dynamic>{
      'totalXp': instance.totalXp,
      'dailyEffortHours': instance.dailyEffortHours,
      'bonusUnlocked': instance.bonusUnlocked,
      'bonusCategory': instance.bonusCategory,
      'completedQuestIds': instance.completedQuestIds,
      'lastResetDate': instance.lastResetDate.toIso8601String(),
      'activeBountyId': instance.activeBountyId,
      'completedBountyIds': instance.completedBountyIds,
      'bonusExpToday': instance.bonusExpToday,
      'bonusCategoryToday': instance.bonusCategoryToday,
    };

BonusConfig _$BonusConfigFromJson(Map<String, dynamic> json) => BonusConfig(
  probability: (json['probability'] as num?)?.toDouble() ?? 0.25,
  minXp: (json['minXp'] as num?)?.toInt() ?? 5,
  maxXp: (json['maxXp'] as num?)?.toInt() ?? 20,
  unlockThresholdHours: (json['unlockThresholdHours'] as num?)?.toInt() ?? 4,
);

Map<String, dynamic> _$BonusConfigToJson(BonusConfig instance) =>
    <String, dynamic>{
      'probability': instance.probability,
      'minXp': instance.minXp,
      'maxXp': instance.maxXp,
      'unlockThresholdHours': instance.unlockThresholdHours,
    };

MicroQuest _$MicroQuestFromJson(Map<String, dynamic> json) => MicroQuest(
  id: json['id'] as String,
  description: json['description'] as String,
  rewardXp: (json['rewardXp'] as num).toInt(),
  isCompleted: json['isCompleted'] as bool? ?? false,
);

Map<String, dynamic> _$MicroQuestToJson(MicroQuest instance) =>
    <String, dynamic>{
      'id': instance.id,
      'description': instance.description,
      'rewardXp': instance.rewardXp,
      'isCompleted': instance.isCompleted,
    };
