import 'package:meta/meta.dart';
import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'reward_model.g.dart';

/// Holds all reward state and progress tracking for the user.
///
/// The [RewardModel] manages the gamification reward system, including
/// experience points, daily bonuses, quest completion tracking, and
/// bounty management. It handles daily resets and bonus multipliers.
///
/// This model is persisted and reset daily to maintain fresh challenges
/// and prevent reward exploitation.
///
/// Example:
/// ```dart
/// final rewards = RewardModel(
///   totalXp: 1500,
///   dailyEffortHours: 3,
///   bonusUnlocked: true,
///   bonusCategory: 'Health',
///   completedQuestIds: ['quest1', 'quest2'],
///   lastResetDate: DateTime.now(),
/// );
/// ```
@JsonSerializable()
@immutable
class RewardModel extends Equatable {
  /// Total experience points accumulated across all activities
  final int totalXp;

  /// Hours of effort logged today for daily goals
  final int dailyEffortHours;

  /// Whether the daily bonus multiplier has been unlocked
  final bool bonusUnlocked;

  /// Category selected for bonus experience multiplier
  final String? bonusCategory;

  /// List of quest IDs completed by the user
  final List<String> completedQuestIds;

  /// Date when rewards were last reset (daily reset mechanism)
  final DateTime lastResetDate;

  /// ID of the currently active bounty, if any
  final String? activeBountyId;

  /// List of bounty IDs completed today
  final List<String> completedBountyIds;

  /// Bonus experience points earned today
  final int bonusExpToday;

  /// Category that earned bonus experience today
  final String? bonusCategoryToday;

  const RewardModel({
    required this.totalXp,
    required this.dailyEffortHours,
    required this.bonusUnlocked,
    required this.bonusCategory,
    required this.completedQuestIds,
    required this.lastResetDate,
    this.activeBountyId,
    this.completedBountyIds = const [],
    this.bonusExpToday = 0,
    this.bonusCategoryToday,
  });

  RewardModel copyWith({
    int? totalXp,
    int? dailyEffortHours,
    bool? bonusUnlocked,
    String? bonusCategory,
    List<String>? completedQuestIds,
    DateTime? lastResetDate,
    String? activeBountyId,
    List<String>? completedBountyIds,
    int? bonusExpToday,
    String? bonusCategoryToday,
  }) => RewardModel(
    totalXp: totalXp ?? this.totalXp,
    dailyEffortHours: dailyEffortHours ?? this.dailyEffortHours,
    bonusUnlocked: bonusUnlocked ?? this.bonusUnlocked,
    bonusCategory: bonusCategory ?? this.bonusCategory,
    completedQuestIds: completedQuestIds ?? this.completedQuestIds,
    lastResetDate: lastResetDate ?? this.lastResetDate,
    activeBountyId: activeBountyId ?? this.activeBountyId,
    completedBountyIds: completedBountyIds ?? this.completedBountyIds,
    bonusExpToday: bonusExpToday ?? this.bonusExpToday,
    bonusCategoryToday: bonusCategoryToday ?? this.bonusCategoryToday,
  );

  factory RewardModel.fromJson(Map<String, dynamic> json) => _$RewardModelFromJson(json);
  Map<String, dynamic> toJson() => _$RewardModelToJson(this);

  @override
  List<Object?> get props => [totalXp, dailyEffortHours, bonusUnlocked, bonusCategory, completedQuestIds, lastResetDate, activeBountyId, completedBountyIds, bonusExpToday, bonusCategoryToday];
}

/// Config for bonus XP logic.
@JsonSerializable()
@immutable
class BonusConfig extends Equatable {
  final double probability;
  final int minXp;
  final int maxXp;
  final int unlockThresholdHours;

  const BonusConfig({
    this.probability = 0.25,
    this.minXp = 5,
    this.maxXp = 20,
    this.unlockThresholdHours = 4,
  });

  factory BonusConfig.fromJson(Map<String, dynamic> json) => _$BonusConfigFromJson(json);
  Map<String, dynamic> toJson() => _$BonusConfigToJson(this);

  @override
  List<Object?> get props => [probability, minXp, maxXp, unlockThresholdHours];
}

/// Represents a micro-quest.
@JsonSerializable()
@immutable
class MicroQuest extends Equatable {
  final String id;
  final String description;
  final int rewardXp;
  final bool isCompleted;

  const MicroQuest({
    required this.id,
    required this.description,
    required this.rewardXp,
    this.isCompleted = false,
  });

  MicroQuest copyWith({bool? isCompleted}) => MicroQuest(
    id: id,
    description: description,
    rewardXp: rewardXp,
    isCompleted: isCompleted ?? this.isCompleted,
  );

  factory MicroQuest.fromJson(Map<String, dynamic> json) => _$MicroQuestFromJson(json);
  Map<String, dynamic> toJson() => _$MicroQuestToJson(this);

  @override
  List<Object?> get props => [id, description, rewardXp, isCompleted];
}

/// Event for quest completion.
class QuestEvent {
  final String questId;
  final int xp;
  QuestEvent(this.questId, this.xp);
}

/// Event for bonus XP.
class BonusEvent {
  final String category;
  final int xp;
  BonusEvent(this.category, this.xp);
} 