// 📁 lib/services/super_entry_service.dart

import 'package:uuid/uuid.dart';
import '../controller/user_controller2.dart';
import '../models/user_model.dart';
import '../quests/north_star_model.dart';
import '../bulletproof/error_handler.dart';

/// A unified service for creating and persisting diary and North Star entries.
class SuperEntryService {
  static final SuperEntryService _instance = SuperEntryService._internal();
  factory SuperEntryService() => _instance;

  final ErrorHandler _errorHandler;
  final _uuid = const Uuid();

  SuperEntryService._internal() : _errorHandler = ErrorHandler();

  /// Normalizes a raw category string into a consistent map key.
  /// E.g. " health " → "Health", "WEALTH" → "Wealth"
  String _normalizeCategory(String raw) {
    final trimmed = raw.trim();
    if (trimmed.isEmpty) return '';
    return trimmed[0].toUpperCase() + trimmed.substring(1).toLowerCase();
  }

  /// Adds a generic EXP diary entry.
  ///
  /// Updates the user's EXP, streak, categories, and diary, then persists.
  Future<User> addDiaryEntry({
    required UserController2 userController,
    required String category,
    required String note,
    required num exp,
  }) async {
    try {
      final user = userController.user;
      if (user == null) {
        throw Exception('No user loaded');
      }

      final validCategory = _normalizeCategory(category);

      // 1) Create a new User with updated categories, streak, and diary entry
      // Note: copyWithAddedExp already updates the total exp, so no need to add it again
      final expInt = exp.round(); // Convert double/num to int for user model
      final updatedUser = user.copyWithAddedExp(validCategory, expInt, note);

      print('🔧 SuperEntryService: Adding EXP');
      print('   Category: $validCategory');
      print('   EXP: $exp (rounded to $expInt)');
      print('   Old total EXP: ${user.exp}');
      print('   New total EXP: ${updatedUser.exp}');
      print('   Old category EXP: ${user.getExp(validCategory)}');
      print('   New category EXP: ${updatedUser.getExp(validCategory)}');

      await userController.updateUser(updatedUser);

      print('🔧 SuperEntryService: User updated in controller');
      print('   Controller user EXP: ${userController.user?.exp}');

      return updatedUser;
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace);
      rethrow;
    }
  }

  /// Logs a North Star quest entry.
  ///
  /// Updates North Star quest logs, hours, totalExp, and also updates the
  /// user's global EXP and categories. Then persists the user.
  Future<User> logNorthStar({
    required UserController2 userController,
    required double hours,
    required String category,
    required String note,
  }) async {
    try {
      final user = userController.user;
      if (user == null) {
        throw Exception('No user loaded');
      }

      final quest = user.northStarQuest;
      if (quest == null) {
        throw Exception('No North Star quest configured.');
      }

      final now = DateTime.now();
      final id = _uuid.v4();
      final cleanedCategory = _normalizeCategory(category);
      final displayNote = note.trim();
      final expAmount = (hours * 10).round();
      final logEntryText = '[N.S. - $cleanedCategory] $displayNote';

      // 1) Build the new NorthStarLog
      final newLog = NorthStarLog(
        id: id,
        entryText: logEntryText,
        hours: hours,
        title: logEntryText,
        loggedAt: now,
      );

      // 2) Update the quest with the new log, hours, and totalExp
      final updatedLogs = <NorthStarLog>[newLog, ...quest.logs];
      final updatedQuest = quest.copyWith(
        logs: updatedLogs,
        hoursLogged: quest.hoursLogged + hours,
        totalExp: quest.totalExp + expAmount,
      );

      // 3) Use copyWithAddedExp to properly handle daily EXP tracking and spinner unlocks
      final userWithExp = user.copyWithAddedExp(cleanedCategory, expAmount, logEntryText);

      // 4) Update the user with the North Star quest changes
      final updatedUser = userWithExp.copyWith(
        northStarQuest: updatedQuest,
      );

      await userController.updateUser(updatedUser);
      return updatedUser;
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace);
      rethrow;
    }
  }
}
