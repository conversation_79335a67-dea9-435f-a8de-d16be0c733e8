import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:audioplayers/audioplayers.dart';
import '../models/reward_model.dart';
import '../models/bounty_model.dart';
import '../data/reward_storage.dart';
import '../data/random_quests.dart';
import '../data/bounty_data.dart';
import '../widgets/cashed_bounties_modal.dart';
import '../models/user_model.dart';
import '../utils/debug_logger.dart';

/// Core engine for the Bounty Hunter reward system.
/// Core reward and gamification engine for the Maxed Out Life system.
///
/// The [RewardEngine] manages all aspects of the reward system including:
/// - Daily quest activation and completion tracking
/// - Bounty selection and management
/// - Experience point calculations and bonuses
/// - Daily reset mechanisms
/// - Notification scheduling for rewards
///
/// This service operates as a singleton and provides real-time updates
/// through [ChangeNotifier] for UI reactivity.
///
/// Key features:
/// - Automatic daily resets at midnight
/// - Random bounty selection from available pool
/// - Bonus category multipliers
/// - Quest completion tracking
/// - Persistent state management
///
/// Example usage:
/// ```dart
/// final engine = RewardEngine.instance;
/// await engine.initialize();
/// await engine.addExperience('Health', 50);
/// ```
class RewardEngine extends ChangeNotifier {
  static final RewardEngine instance = RewardEngine._();
  RewardEngine._() {
    _initializeAudio();
  }

  /// Initialize audio player with error handling
  void _initializeAudio() {
    try {
      _audioPlayer = AudioPlayer();
      _audioInitialized = true;
    } catch (e) {
      print('⚠️ Audio player initialization failed: $e');
      _audioInitialized = false;
    }
  }

  final BonusConfig _config = const BonusConfig();
  RewardModel _state = RewardModel(
    totalXp: 0,
    dailyEffortHours: 0,
    bonusUnlocked: false,
    bonusCategory: null,
    completedQuestIds: [],
    lastResetDate: DateTime(2000),
  );
  List<MicroQuest> _activeQuests = [];
  BountyModel? _activeBounty;

  final ValueNotifier<RewardModel> stateNotifier = ValueNotifier(RewardModel(
    totalXp: 0,
    dailyEffortHours: 0,
    bonusUnlocked: false,
    bonusCategory: null,
    completedQuestIds: [],
    lastResetDate: DateTime(2000),
  ));
  final StreamController<QuestEvent> _questStream = StreamController.broadcast();
  final StreamController<BonusEvent> _bonusStream = StreamController.broadcast();
  final StreamController<void> _bonusMissStream = StreamController.broadcast();

  final FlutterLocalNotificationsPlugin _notifications = FlutterLocalNotificationsPlugin();
  bool _notificationsInitialized = false;

  late final AudioPlayer _audioPlayer;
  bool _audioInitialized = false;

  Stream<QuestEvent> get questStream => _questStream.stream;
  Stream<BonusEvent> get bonusStream => _bonusStream.stream;
  Stream<void> get bonusMissStream => _bonusMissStream.stream;
  BountyModel? get activeBounty => _activeBounty;

  /// Initializes the reward engine and performs daily reset if needed.
  ///
  /// This method should be called once during app startup. It:
  /// - Loads the current reward state from storage
  /// - Performs daily reset if a new day has started
  /// - Activates daily quests and selects a new bounty
  /// - Initializes notification scheduling
  ///
  /// The initialization is idempotent and safe to call multiple times.
  Future<void> initialize() async {
    _state = await RewardStorage.instance.resetDailyIfNeeded();
    stateNotifier.value = _state;
    activateDailyQuests();
    await _selectDailyBounty();
    await initializeNotifications();
  }

  /// Selects a random daily bounty
  Future<void> _selectDailyBounty() async {
    final availableBounties = allBounties.where((bounty) => 
      !_state.completedBountyIds.contains(bounty.id)).toList();
    
    if (availableBounties.isNotEmpty) {
      availableBounties.shuffle();
      _activeBounty = availableBounties.first;
    }
  }

  /// Records a work block and handles experience, bonuses, and quest completion.
  ///
  /// This is the primary method for tracking user activity and awarding
  /// experience points. It manages the complete reward flow including:
  ///
  /// Features:
  /// - Awards base experience points (10 XP per work block)
  /// - Increments daily effort hours counter
  /// - Unlocks bonus gate when threshold is reached
  /// - Applies bonus multipliers for selected categories
  /// - Checks and completes eligible quests
  /// - Provides haptic and audio feedback
  /// - Persists all state changes
  ///
  /// Parameters:
  /// - [category]: The category being worked on (Health, Wealth, Purpose, Connection)
  ///
  /// Side effects:
  /// - May trigger bonus gate notification
  /// - May complete quests and award bonus XP
  /// - Updates UI through [ChangeNotifier]
  Future<void> recordWorkBlock(String category) async {
    _state = _state.copyWith(
      dailyEffortHours: _state.dailyEffortHours + 1,
      totalXp: _state.totalXp + 10,
    );
    // Unlock bonus gate
    if (_state.dailyEffortHours == _config.unlockThresholdHours && !_state.bonusUnlocked) {
      _state = _state.copyWith(bonusUnlocked: true);
      await _showBountyGateNotification();
      HapticFeedback.mediumImpact();
    }
    // Persistent bonus mode
    if (_state.bonusUnlocked && category == _state.bonusCategory) {
      await _tryGrantBonus(category);
    }
    // Quest completion
    for (final quest in _activeQuests) {
      if (!quest.isCompleted && _questConditionMet(quest, category)) {
        _activeQuests = _activeQuests.map((q) =>
          q.id == quest.id ? q.copyWith(isCompleted: true) : q).toList();
        _state = _state.copyWith(
          totalXp: _state.totalXp + quest.rewardXp,
          completedQuestIds: List<String>.from(_state.completedQuestIds)..add(quest.id),
        );
        _questStream.add(QuestEvent(quest.id, quest.rewardXp));
      }
    }
    await RewardStorage.instance.saveRewardModel(_state);
    stateNotifier.value = _state;
    notifyListeners();
  }

  /// Randomly grants bonus XP if eligible.
  Future<void> _tryGrantBonus(String category) async {
    if (Random().nextDouble() < _config.probability) {
      final bonusXp = _config.minXp + Random().nextInt(_config.maxXp - _config.minXp + 1);
      _state = _state.copyWith(totalXp: _state.totalXp + bonusXp);
      await RewardStorage.instance.saveRewardModel(_state);
      stateNotifier.value = _state;
      _bonusStream.add(BonusEvent(category, bonusXp));
      HapticFeedback.heavyImpact();
      if (_audioInitialized) {
        try {
          await _audioPlayer.play(AssetSource('sounds/lightning.mp3'));
        } catch (e) {
          print('⚠️ Audio playback failed: $e');
        }
      }
      notifyListeners();
    }
  }

  /// Sets the bonus category after spinning.
  Future<void> setBonusCategory(String category) async {
    _state = _state.copyWith(bonusCategory: category);
    await RewardStorage.instance.saveRewardModel(_state);
    stateNotifier.value = _state;
    notifyListeners();
  }

  /// Activates daily quests (shuffles and picks 5).
  void activateDailyQuests() {
    final shuffled = List<MicroQuest>.from(allQuests)..shuffle();
    _activeQuests = shuffled.take(5).toList();
  }

  /// Checks if a quest's condition is met (stub: always true for demo).
  bool _questConditionMet(MicroQuest quest, String category) {
    // Note: Implement real quest logic based on quest.description/category
    return true;
  }

  /// Resets daily state manually (for testing).
  Future<void> resetDaily() async {
    _state = await RewardStorage.instance.resetDailyIfNeeded();
    stateNotifier.value = _state;
    activateDailyQuests();
    notifyListeners();
  }

  /// Initializes local notifications.
  Future<void> initializeNotifications() async {
    if (_notificationsInitialized) return;
    const android = AndroidInitializationSettings('@mipmap/ic_launcher');
    const ios = DarwinInitializationSettings();
    const macos = DarwinInitializationSettings();
    const settings = InitializationSettings(android: android, iOS: ios, macOS: macos);
    await _notifications.initialize(settings);
    _notificationsInitialized = true;
  }

  Future<void> _showBountyGateNotification() async {
    if (!_notificationsInitialized) return;
    const android = AndroidNotificationDetails(
      'bounty_gate',
      'Bounty Gate',
      channelDescription: 'Bounty Gate Notifications',
      importance: Importance.max,
      priority: Priority.high,
      showWhen: false,
    );
    const ios = DarwinNotificationDetails();
    const details = NotificationDetails(android: android, iOS: ios);
    await _notifications.show(
      1,
      'BOUNTY GATE OPEN!',
      'Tap to spin for bonus XP.',
      details,
    );
  }

  /// Checks if a bounty is completed with photo proof
  Future<void> checkBountyCompletion(String category, {required String photoProofPath}) async {
    if (_activeBounty == null) return;

    // Create a new cashed bounty
    final cashedBounty = CashedBounty(
      bounty: _activeBounty!,
      photoPath: photoProofPath,
      completedAt: DateTime.now(),
    );

    // Add to storage
    await RewardStorage.instance.addCashedBounty(cashedBounty);

    // Grant XP for each category
    for (final entry in _activeBounty!.expPerCategory.entries) {
      _state = _state.copyWith(
        totalXp: _state.totalXp + entry.value,
      );
    }

    // Add to completed bounties list
    final completedBountyIds = List<String>.from(_state.completedBountyIds)..add(_activeBounty!.id);
    _state = _state.copyWith(completedBountyIds: completedBountyIds);

    // Play success sound
    if (_audioInitialized) {
      try {
        await _audioPlayer.play(AssetSource('sounds/success.mp3'));
      } catch (e) {
        print('⚠️ Audio playback failed: $e');
      }
    }
    HapticFeedback.heavyImpact();

    // Clear active bounty
    _activeBounty = null;

    // Save state
    await RewardStorage.instance.saveRewardModel(_state);
    stateNotifier.value = _state;
    notifyListeners();
  }

  // ========== SPINNER SYSTEM METHODS ==========

  /// Constants for spinner system
  static const int _spinnerUnlockThreshold = 40;
  static const int _spinnerIntervalThreshold = 20;
  static const double _spinnerBonusChance = 0.25; // 25% chance
  static const int _minSpinnerBonusExp = 5;
  static const int _maxSpinnerBonusExp = 20;

  /// Check if daily EXP should be reset (midnight in user's timezone)
  bool shouldResetDailyExp(User user) {
    final now = DateTime.now();
    final lastResetDate = DateTime(user.lastExpReset.year, user.lastExpReset.month, user.lastExpReset.day);
    final todayDate = DateTime(now.year, now.month, now.day);

    return todayDate.isAfter(lastResetDate);
  }

  /// Reset daily EXP tracking for new day
  User resetDailyExp(User user) {
    DebugLogger.log('RewardEngine', 'Resetting daily EXP for user ${user.username}');

    return user.copyWith(
      dailyExpTotal: 0,
      lastExpReset: DateTime.now(),
      activeBonusCategories: [], // Clear bonus categories
      availableSpinnerPlays: 0, // Reset spinner plays
    );
  }

  /// Add EXP to daily total and check for spinner unlocks
  User addDailyExp(User user, int expAmount) {
    final newDailyTotal = user.dailyExpTotal + expAmount;
    final newSpinnerPlays = _calculateSpinnerPlays(newDailyTotal) - user.availableSpinnerPlays;

    DebugLogger.log('RewardEngine',
      'Adding $expAmount EXP. Daily total: ${user.dailyExpTotal} -> $newDailyTotal. '
      'New spinner plays: $newSpinnerPlays');

    return user.copyWith(
      dailyExpTotal: newDailyTotal,
      availableSpinnerPlays: user.availableSpinnerPlays + newSpinnerPlays,
    );
  }

  /// Calculate total spinner plays available based on daily EXP
  int _calculateSpinnerPlays(int dailyExp) {
    if (dailyExp < _spinnerUnlockThreshold) return 0;

    // First unlock at 40, then every 20 after that
    return 1 + ((dailyExp - _spinnerUnlockThreshold) ~/ _spinnerIntervalThreshold);
  }

  /// Get EXP needed for next spinner unlock
  int getExpToNextSpinner(int dailyExp) {
    if (dailyExp < _spinnerUnlockThreshold) {
      return _spinnerUnlockThreshold - dailyExp;
    }

    final nextThreshold = _spinnerUnlockThreshold +
      ((dailyExp - _spinnerUnlockThreshold) ~/ _spinnerIntervalThreshold + 1) * _spinnerIntervalThreshold;

    return nextThreshold - dailyExp;
  }

  /// Check if user has bonus chance for a category
  bool hasBonusChance(User user, String category) {
    return user.activeBonusCategories.contains(category);
  }

  /// Calculate bonus EXP if user wins the 25% chance
  int? calculateBonusExp(User user, String category) {
    if (!hasBonusChance(user, category)) return null;

    // 25% chance to win bonus
    if (Random().nextDouble() > _spinnerBonusChance) return null;

    // Random bonus between 5-20 EXP
    final bonusExp = _minSpinnerBonusExp + Random().nextInt(_maxSpinnerBonusExp - _minSpinnerBonusExp + 1);

    DebugLogger.log('RewardEngine',
      'Bonus EXP triggered for $category: $bonusExp EXP');

    return bonusExp;
  }

  /// Spin the reward wheel and return results
  SpinResult spinWheel(User user) {
    if (user.availableSpinnerPlays <= 0) {
      DebugLogger.log('RewardEngine', 'Spin attempted with no available plays');
      return SpinResult(
        success: false,
        bonusExp: 0,
        wonCategory: null,
        message: 'No spinner plays available',
      );
    }

    // 25% chance to win
    final won = Random().nextDouble() <= _spinnerBonusChance;

    if (!won) {
      DebugLogger.log('RewardEngine', 'Spinner result: Loss');
      return SpinResult(
        success: false,
        bonusExp: 0,
        wonCategory: null,
        message: 'Better luck next time!',
      );
    }

    // Won! Generate bonus EXP and random category
    final bonusExp = _minSpinnerBonusExp + Random().nextInt(_maxSpinnerBonusExp - _minSpinnerBonusExp + 1);
    final categories = ['Health', 'Wealth', 'Purpose', 'Connection'];
    final wonCategory = categories[Random().nextInt(categories.length)];

    DebugLogger.log('RewardEngine',
      'Spinner result: Win! $bonusExp EXP for $wonCategory category');

    return SpinResult(
      success: true,
      bonusExp: bonusExp,
      wonCategory: wonCategory,
      message: 'Congratulations! You won $bonusExp EXP for $wonCategory!',
    );
  }

  /// Spin the reward wheel with a specific landed category (for authentic visual spinning)
  SpinResult spinWheelWithCategory(User user, String landedCategory) {
    if (user.availableSpinnerPlays <= 0) {
      DebugLogger.log('RewardEngine', 'Spin attempted with no available plays');
      return SpinResult(
        success: false,
        bonusExp: 0,
        wonCategory: null,
        message: 'No spinner plays available',
      );
    }

    // 25% chance to win (same probability as regular spin)
    final won = Random().nextDouble() <= _spinnerBonusChance;

    if (!won) {
      DebugLogger.log('RewardEngine', 'Spinner result: Loss (landed on $landedCategory)');
      return SpinResult(
        success: false,
        bonusExp: 0,
        wonCategory: null,
        message: 'Better luck next time!',
      );
    }

    // Won! Generate bonus EXP for the landed category
    final bonusExp = _minSpinnerBonusExp + Random().nextInt(_maxSpinnerBonusExp - _minSpinnerBonusExp + 1);

    DebugLogger.log('RewardEngine',
      'Spinner result: Win! $bonusExp EXP for $landedCategory category (where wheel landed)');

    return SpinResult(
      success: true,
      bonusExp: bonusExp,
      wonCategory: landedCategory,
      message: 'Congratulations! You won $bonusExp EXP for $landedCategory!',
    );
  }

  /// Use a spinner play and update user state
  User useSpinnerPlay(User user, SpinResult result) {
    final updatedUser = user.copyWith(
      availableSpinnerPlays: user.availableSpinnerPlays - 1,
    );

    if (result.success && result.wonCategory != null && result.bonusExp > 0) {
      // Add the won category to active bonus categories (stacking)
      final newBonusCategories = List<String>.from(user.activeBonusCategories);
      if (!newBonusCategories.contains(result.wonCategory!)) {
        newBonusCategories.add(result.wonCategory!);
      }

      // CRITICAL FIX: Add the bonus EXP to user's total EXP and category EXP
      final updatedCategories = Map<String, int>.from(updatedUser.categories);
      updatedCategories[result.wonCategory!] = (updatedCategories[result.wonCategory!] ?? 0) + result.bonusExp;

      DebugLogger.log('RewardEngine',
        'SPINNER BONUS: Adding ${result.bonusExp} EXP to ${result.wonCategory} category. '
        'Total EXP: ${updatedUser.exp} -> ${updatedUser.exp + result.bonusExp}');

      return updatedUser.copyWith(
        activeBonusCategories: newBonusCategories,
        categories: updatedCategories,
        exp: updatedUser.exp + result.bonusExp, // Add bonus EXP to total EXP
        dailyExpTotal: updatedUser.dailyExpTotal + result.bonusExp, // Add to daily total for spinner unlocks
        lastModified: DateTime.now(),
      );
    }

    return updatedUser;
  }

  /// Check if user can spin (has available plays)
  bool canSpin(User user) {
    return user.availableSpinnerPlays > 0;
  }

  /// Get progress toward next spinner unlock
  double getSpinnerProgress(int dailyExp) {
    if (dailyExp < _spinnerUnlockThreshold) {
      return dailyExp / _spinnerUnlockThreshold;
    }

    final expSinceUnlock = dailyExp - _spinnerUnlockThreshold;
    final progressInCurrentInterval = expSinceUnlock % _spinnerIntervalThreshold;

    return progressInCurrentInterval / _spinnerIntervalThreshold;
  }

  @override
  void dispose() {
    _questStream.close();
    _bonusStream.close();
    _bonusMissStream.close();
    stateNotifier.dispose();
    if (_audioInitialized) {
      try {
        _audioPlayer.dispose();
      } catch (e) {
        print('⚠️ Audio disposal failed: $e');
      }
    }
    super.dispose();
  }
}

/// Result of a spinner wheel spin
class SpinResult {
  final bool success;
  final int bonusExp;
  final String? wonCategory;
  final String message;

  const SpinResult({
    required this.success,
    required this.bonusExp,
    required this.wonCategory,
    required this.message,
  });

  @override
  String toString() {
    return 'SpinResult(success: $success, bonusExp: $bonusExp, '
           'wonCategory: $wonCategory, message: $message)';
  }
}