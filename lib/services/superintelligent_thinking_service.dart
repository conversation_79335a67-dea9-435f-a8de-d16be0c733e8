// lib/services/superintelligent_thinking_service.dart

import 'dart:async';
import '../prompts/mxd_life_coaches.dart';

/// Advanced thinking visualization service for superintelligent AI coaches
/// 
/// Provides personality-specific thinking animations and unlimited thinking time
/// to showcase the depth of AI coach intelligence processing.
class SuperintelligentThinkingService {
  static final Map<String, StreamController<ThinkingState>> _thinkingStreams = {};
  static final Map<String, Timer?> _thinkingTimers = {};
  
  /// Start thinking visualization for a specific coach
  static Stream<ThinkingState> startThinking({
    required String coachCategory,
    required String userGender,
    required String userMessage,
    Map<String, String>? assignedCoaches,
  }) {
    final coachName = _getCoachName(coachCategory, userGender, assignedCoaches);
    final streamKey = '${coachCategory}_$coachName';
    
    // Clean up existing stream if any
    stopThinking(coachCategory, userGender, assignedCoaches);
    
    // Create new stream controller
    final controller = StreamController<ThinkingState>.broadcast();
    _thinkingStreams[streamKey] = controller;
    
    // Start thinking animation sequence
    _startThinkingSequence(
      controller: controller,
      coachName: coachName,
      coachCategory: coachCategory,
      userMessage: userMessage,
    );
    
    return controller.stream;
  }
  
  /// Stop thinking visualization
  static void stopThinking(String coachCategory, String userGender, Map<String, String>? assignedCoaches) {
    final coachName = _getCoachName(coachCategory, userGender, assignedCoaches);
    final streamKey = '${coachCategory}_$coachName';

    _thinkingTimers[streamKey]?.cancel();
    _thinkingTimers.remove(streamKey);

    final controller = _thinkingStreams[streamKey];
    if (controller != null && !controller.isClosed) {
      controller.add(ThinkingState.completed(coachName));
      controller.close();
    }
    _thinkingStreams.remove(streamKey);
  }

  /// Dispose all thinking resources (call on app shutdown)
  static void disposeAll() {
    // Cancel all timers
    for (final timer in _thinkingTimers.values) {
      timer?.cancel();
    }
    _thinkingTimers.clear();

    // Close all streams
    for (final controller in _thinkingStreams.values) {
      if (!controller.isClosed) {
        controller.close();
      }
    }
    _thinkingStreams.clear();
  }
  
  /// Start the thinking animation sequence
  static void _startThinkingSequence({
    required StreamController<ThinkingState> controller,
    required String coachName,
    required String coachCategory,
    required String userMessage,
  }) {
    final phases = _getThinkingPhases(coachName, coachCategory, userMessage);
    int currentPhase = 0;
    
    void nextPhase() {
      if (controller.isClosed) return;
      
      if (currentPhase < phases.length) {
        final phase = phases[currentPhase];
        controller.add(phase);
        
        currentPhase++;
        
        // Schedule next phase with dynamic timing
        final delay = _calculatePhaseDelay(phase, userMessage);
        _thinkingTimers['${coachCategory}_$coachName'] = Timer(delay, nextPhase);
      }
    }
    
    // Start the sequence
    nextPhase();
  }
  
  /// Get thinking phases for specific coach personality
  static List<ThinkingState> _getThinkingPhases(String coachName, String coachCategory, String userMessage) {
    final complexity = _analyzeMessageComplexity(userMessage);
    
    switch (coachCategory.toLowerCase()) {
      case 'health':
        return _getHealthCoachThinking(coachName, complexity);
      case 'wealth':
        return _getWealthCoachThinking(coachName, complexity);
      case 'purpose':
        return _getPurposeCoachThinking(coachName, complexity);
      case 'connection':
        return _getConnectionCoachThinking(coachName, complexity);
      default:
        return _getCustomCoachThinking(coachName, complexity);
    }
  }
  
  /// Health coach thinking phases
  static List<ThinkingState> _getHealthCoachThinking(String coachName, MessageComplexity complexity) {
    final basePhases = [
      ThinkingState.analyzing(coachName, "⚡ Analyzing your physical and mental state..."),
      ThinkingState.processing(coachName, "🧠 Processing neuroscience and performance data..."),
      ThinkingState.synthesizing(coachName, "💪 Synthesizing optimal training strategies..."),
      ThinkingState.strategizing(coachName, "🎯 Crafting your personalized action plan..."),
    ];
    
    if (complexity == MessageComplexity.high) {
      basePhases.insertAll(1, [
        ThinkingState.deepAnalysis(coachName, "⚡ Conducting deep biomechanical analysis..."),
        ThinkingState.crossReference(coachName, "📚 Cross-referencing expert protocols..."),
      ]);
    }
    
    return basePhases;
  }
  
  /// Wealth coach thinking phases
  static List<ThinkingState> _getWealthCoachThinking(String coachName, MessageComplexity complexity) {
    final basePhases = [
      ThinkingState.analyzing(coachName, "💎 Analyzing market opportunities and risks..."),
      ThinkingState.processing(coachName, "📊 Processing financial data and trends..."),
      ThinkingState.synthesizing(coachName, "💰 Synthesizing wealth-building strategies..."),
      ThinkingState.strategizing(coachName, "🚀 Designing your financial roadmap..."),
    ];
    
    if (complexity == MessageComplexity.high) {
      basePhases.insertAll(1, [
        ThinkingState.deepAnalysis(coachName, "💎 Conducting deep market analysis..."),
        ThinkingState.crossReference(coachName, "📈 Cross-referencing investment strategies..."),
      ]);
    }
    
    return basePhases;
  }
  
  /// Purpose coach thinking phases
  static List<ThinkingState> _getPurposeCoachThinking(String coachName, MessageComplexity complexity) {
    final basePhases = [
      ThinkingState.analyzing(coachName, "🌌 Analyzing your soul's calling and destiny..."),
      ThinkingState.processing(coachName, "🔮 Processing cosmic wisdom and ancient knowledge..."),
      ThinkingState.synthesizing(coachName, "✨ Synthesizing your unique path forward..."),
      ThinkingState.strategizing(coachName, "🌟 Revealing your next steps on the journey..."),
    ];
    
    if (complexity == MessageComplexity.high) {
      basePhases.insertAll(1, [
        ThinkingState.deepAnalysis(coachName, "🌌 Diving deep into existential patterns..."),
        ThinkingState.crossReference(coachName, "📜 Cross-referencing philosophical wisdom..."),
      ]);
    }
    
    return basePhases;
  }
  
  /// Connection coach thinking phases
  static List<ThinkingState> _getConnectionCoachThinking(String coachName, MessageComplexity complexity) {
    final basePhases = [
      ThinkingState.analyzing(coachName, "💝 Analyzing emotional patterns and relationships..."),
      ThinkingState.processing(coachName, "🧘 Processing psychological and spiritual insights..."),
      ThinkingState.synthesizing(coachName, "🤝 Synthesizing connection strategies..."),
      ThinkingState.strategizing(coachName, "❤️ Crafting your path to deeper intimacy..."),
    ];
    
    if (complexity == MessageComplexity.high) {
      basePhases.insertAll(1, [
        ThinkingState.deepAnalysis(coachName, "💝 Exploring deep emotional landscapes..."),
        ThinkingState.crossReference(coachName, "📖 Cross-referencing therapeutic wisdom..."),
      ]);
    }
    
    return basePhases;
  }
  
  /// Custom coach thinking phases
  static List<ThinkingState> _getCustomCoachThinking(String coachName, MessageComplexity complexity) {
    final basePhases = [
      ThinkingState.analyzing(coachName, "🔍 Analyzing your unique challenge..."),
      ThinkingState.processing(coachName, "⚙️ Processing specialized knowledge..."),
      ThinkingState.synthesizing(coachName, "🎨 Synthesizing creative solutions..."),
      ThinkingState.strategizing(coachName, "🎯 Designing your custom approach..."),
    ];
    
    if (complexity == MessageComplexity.high) {
      basePhases.insertAll(1, [
        ThinkingState.deepAnalysis(coachName, "🔍 Conducting specialized deep analysis..."),
        ThinkingState.crossReference(coachName, "📚 Cross-referencing domain expertise..."),
      ]);
    }
    
    return basePhases;
  }
  
  /// Analyze message complexity to determine thinking depth
  static MessageComplexity _analyzeMessageComplexity(String message) {
    final wordCount = message.split(' ').length;
    final hasQuestions = message.contains('?');
    final hasEmotionalWords = _containsEmotionalWords(message);
    final hasComplexConcepts = _containsComplexConcepts(message);
    
    int complexityScore = 0;
    if (wordCount > 20) complexityScore++;
    if (hasQuestions) complexityScore++;
    if (hasEmotionalWords) complexityScore++;
    if (hasComplexConcepts) complexityScore++;
    
    if (complexityScore >= 3) return MessageComplexity.high;
    if (complexityScore >= 2) return MessageComplexity.medium;
    return MessageComplexity.low;
  }
  
  /// Calculate delay between thinking phases
  static Duration _calculatePhaseDelay(ThinkingState phase, String userMessage) {
    final baseDelay = Duration(milliseconds: 1500);
    final complexity = _analyzeMessageComplexity(userMessage);
    
    // Adjust delay based on phase type and complexity
    double multiplier = 1.0;
    
    switch (phase.type) {
      case ThinkingType.deepAnalysis:
        multiplier = 2.5;
        break;
      case ThinkingType.crossReference:
        multiplier = 2.0;
        break;
      case ThinkingType.synthesizing:
        multiplier = 1.8;
        break;
      case ThinkingType.strategizing:
        multiplier = 1.5;
        break;
      default:
        multiplier = 1.0;
    }
    
    // Adjust for message complexity
    switch (complexity) {
      case MessageComplexity.high:
        multiplier *= 1.5;
        break;
      case MessageComplexity.medium:
        multiplier *= 1.2;
        break;
      case MessageComplexity.low:
        multiplier *= 0.8;
        break;
    }
    
    return Duration(milliseconds: (baseDelay.inMilliseconds * multiplier).round());
  }
  
  /// Helper methods
  static bool _containsEmotionalWords(String message) {
    final emotionalWords = ['feel', 'emotion', 'sad', 'happy', 'angry', 'frustrated', 'excited', 'anxious', 'stressed', 'overwhelmed'];
    return emotionalWords.any((word) => message.toLowerCase().contains(word));
  }
  
  static bool _containsComplexConcepts(String message) {
    final complexWords = ['strategy', 'philosophy', 'psychology', 'neuroscience', 'investment', 'relationship', 'purpose', 'meaning'];
    return complexWords.any((word) => message.toLowerCase().contains(word));
  }
  
  static String _getCoachName(String category, String userGender, Map<String, String>? assignedCoaches) {
    // Handle non-gender users with assigned coaches
    String effectiveGender = userGender;
    if (userGender.toLowerCase() == 'non-gender' && assignedCoaches != null && assignedCoaches.containsKey(category)) {
      effectiveGender = assignedCoaches[category]!;
    }

    // Handle custom categories with hardcoded coach assignments
    if (category == 'Custom Category 1') {
      return effectiveGender.toLowerCase() == 'female' ? 'Luna' : 'Aether';
    } else if (category == 'Custom Category 2') {
      return effectiveGender.toLowerCase() == 'female' ? 'Elysia' : 'Chronos';
    }

    final coach = mxdLifeCoaches.firstWhere(
      (c) => c.category.toLowerCase() == category.toLowerCase(),
      orElse: () => mxdLifeCoaches.first,
    );

    return effectiveGender.toLowerCase() == 'male' ? coach.maleName : coach.femaleName;
  }
}

/// Thinking state data class
class ThinkingState {
  final String coachName;
  final String message;
  final ThinkingType type;
  final bool isCompleted;
  
  const ThinkingState({
    required this.coachName,
    required this.message,
    required this.type,
    this.isCompleted = false,
  });
  
  factory ThinkingState.analyzing(String coachName, String message) =>
      ThinkingState(coachName: coachName, message: message, type: ThinkingType.analyzing);
  
  factory ThinkingState.processing(String coachName, String message) =>
      ThinkingState(coachName: coachName, message: message, type: ThinkingType.processing);
  
  factory ThinkingState.synthesizing(String coachName, String message) =>
      ThinkingState(coachName: coachName, message: message, type: ThinkingType.synthesizing);
  
  factory ThinkingState.strategizing(String coachName, String message) =>
      ThinkingState(coachName: coachName, message: message, type: ThinkingType.strategizing);
  
  factory ThinkingState.deepAnalysis(String coachName, String message) =>
      ThinkingState(coachName: coachName, message: message, type: ThinkingType.deepAnalysis);
  
  factory ThinkingState.crossReference(String coachName, String message) =>
      ThinkingState(coachName: coachName, message: message, type: ThinkingType.crossReference);
  
  factory ThinkingState.completed(String coachName) =>
      ThinkingState(coachName: coachName, message: 'Thinking complete', type: ThinkingType.completed, isCompleted: true);
}

/// Thinking type enumeration
enum ThinkingType {
  analyzing,
  processing,
  synthesizing,
  strategizing,
  deepAnalysis,
  crossReference,
  completed,
}

/// Message complexity enumeration
enum MessageComplexity {
  low,
  medium,
  high,
}
