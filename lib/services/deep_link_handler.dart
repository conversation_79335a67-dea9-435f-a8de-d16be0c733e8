import 'dart:async';
import 'package:flutter/services.dart';
import 'enhanced_email_verification_service.dart';
import 'comprehensive_logging_service.dart';

/// 🔗 Deep Link Handler Service
/// 
/// Handles incoming deep links for email verification, magic links,
/// and other app navigation scenarios.
/// 
/// Features:
/// - Email verification link handling
/// - Magic link processing
/// - Secure token validation
/// - Navigation coordination
/// - Error handling and logging
class DeepLinkHandler {
  static final DeepLinkHandler _instance = DeepLinkHandler._internal();
  factory DeepLinkHandler() => _instance;
  DeepLinkHandler._internal();

  static const MethodChannel _channel = MethodChannel('mxd.app/deep_links');
  
  final StreamController<DeepLinkResult> _linkController = StreamController<DeepLinkResult>.broadcast();
  Stream<DeepLinkResult> get linkStream => _linkController.stream;
  
  bool _initialized = false;
  EnhancedEmailVerificationService? _emailService;

  /// Initialize deep link handling
  Future<void> initialize() async {
    if (_initialized) return;
    
    try {
      await ComprehensiveLoggingService.logInfo('🔗 Initializing deep link handler');
      
      // Initialize email verification service
      _emailService = EnhancedEmailVerificationService();
      await _emailService!.initialize();
      
      // Set up method channel handler
      _channel.setMethodCallHandler(_handleMethodCall);
      
      // Check for initial link (app launched via deep link)
      await _checkInitialLink();
      
      _initialized = true;
      await ComprehensiveLoggingService.logInfo('✅ Deep link handler initialized');
      
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Failed to initialize deep link handler: $e');
      rethrow;
    }
  }

  /// Handle method calls from native platforms
  Future<dynamic> _handleMethodCall(MethodCall call) async {
    try {
      switch (call.method) {
        case 'onDeepLink':
          final String url = call.arguments['url'];
          await processDeepLink(url);
          break;
        default:
          throw PlatformException(
            code: 'UNIMPLEMENTED',
            message: 'Method ${call.method} not implemented',
          );
      }
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Deep link method call error: $e');
    }
  }

  /// Check for initial deep link when app launches
  Future<void> _checkInitialLink() async {
    try {
      final String? initialLink = await _channel.invokeMethod('getInitialLink');
      if (initialLink != null) {
        await processDeepLink(initialLink);
      }
    } catch (e) {
      // Initial link check is optional - don't fail initialization
      await ComprehensiveLoggingService.logInfo('ℹ️ No initial deep link found: $e');
    }
  }

  /// Process incoming deep link (public for proxy access)
  Future<void> processDeepLink(String url) async {
    try {
      await ComprehensiveLoggingService.logInfo('🔗 Processing deep link: $url');
      
      final uri = Uri.parse(url);
      final result = await _handleDeepLinkUri(uri);
      
      // Emit result to listeners
      _linkController.add(result);
      
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Failed to process deep link: $e');
      
      _linkController.add(DeepLinkResult(
        success: false,
        type: DeepLinkType.unknown,
        message: 'Failed to process deep link: $e',
        url: url,
      ));
    }
  }

  /// Handle specific deep link URI
  Future<DeepLinkResult> _handleDeepLinkUri(Uri uri) async {
    // Handle email verification links
    if (uri.path == '/verify' || uri.path.startsWith('/verify')) {
      return await _handleEmailVerification(uri);
    }
    
    // Handle magic links
    if (uri.path.startsWith('/magic/verify')) {
      return await _handleMagicLink(uri);
    }
    
    // Handle other app links
    if (uri.path == '/app' || uri.path.startsWith('/app/')) {
      return _handleAppNavigation(uri);
    }
    
    // Unknown link type
    return DeepLinkResult(
      success: false,
      type: DeepLinkType.unknown,
      message: 'Unknown deep link type: ${uri.path}',
      url: uri.toString(),
    );
  }

  /// Handle email verification deep link
  Future<DeepLinkResult> _handleEmailVerification(Uri uri) async {
    try {
      final token = uri.queryParameters['token'];
      if (token == null) {
        return DeepLinkResult(
          success: false,
          type: DeepLinkType.emailVerification,
          message: 'Missing verification token',
          url: uri.toString(),
        );
      }
      
      // Verify the email using the token
      final verificationResult = await _emailService!.verifyEmail(token, isMagicLink: false);
      
      if (verificationResult.success) {
        return DeepLinkResult(
          success: true,
          type: DeepLinkType.emailVerification,
          message: 'Email verified successfully',
          url: uri.toString(),
          email: verificationResult.email,
          username: verificationResult.username,
          navigationTarget: 'onboarding',
        );
      } else {
        return DeepLinkResult(
          success: false,
          type: DeepLinkType.emailVerification,
          message: verificationResult.message,
          url: uri.toString(),
        );
      }
      
    } catch (e) {
      return DeepLinkResult(
        success: false,
        type: DeepLinkType.emailVerification,
        message: 'Email verification failed: $e',
        url: uri.toString(),
      );
    }
  }

  /// Handle magic link deep link
  Future<DeepLinkResult> _handleMagicLink(Uri uri) async {
    try {
      final token = uri.queryParameters['token'];
      if (token == null) {
        return DeepLinkResult(
          success: false,
          type: DeepLinkType.magicLink,
          message: 'Missing magic link token',
          url: uri.toString(),
        );
      }
      
      // Verify the magic link
      final verificationResult = await _emailService!.verifyEmail(token, isMagicLink: true);
      
      if (verificationResult.success) {
        return DeepLinkResult(
          success: true,
          type: DeepLinkType.magicLink,
          message: 'Magic link verified successfully',
          url: uri.toString(),
          email: verificationResult.email,
          username: verificationResult.username,
          navigationTarget: 'onboarding',
        );
      } else {
        return DeepLinkResult(
          success: false,
          type: DeepLinkType.magicLink,
          message: verificationResult.message,
          url: uri.toString(),
        );
      }
      
    } catch (e) {
      return DeepLinkResult(
        success: false,
        type: DeepLinkType.magicLink,
        message: 'Magic link verification failed: $e',
        url: uri.toString(),
      );
    }
  }

  /// Handle app navigation deep link
  DeepLinkResult _handleAppNavigation(Uri uri) {
    // Extract navigation target from path
    final pathSegments = uri.pathSegments;
    String? target;
    
    if (pathSegments.length > 1) {
      target = pathSegments[1]; // /app/dashboard -> 'dashboard'
    }
    
    return DeepLinkResult(
      success: true,
      type: DeepLinkType.appNavigation,
      message: 'App navigation link processed',
      url: uri.toString(),
      navigationTarget: target ?? 'home',
    );
  }

  /// Dispose resources
  void dispose() {
    _linkController.close();
  }
}

/// Deep link result
class DeepLinkResult {
  final bool success;
  final DeepLinkType type;
  final String message;
  final String url;
  final String? email;
  final String? username;
  final String? navigationTarget;

  DeepLinkResult({
    required this.success,
    required this.type,
    required this.message,
    required this.url,
    this.email,
    this.username,
    this.navigationTarget,
  });
}

/// Deep link types
enum DeepLinkType {
  emailVerification,
  magicLink,
  appNavigation,
  unknown,
}
