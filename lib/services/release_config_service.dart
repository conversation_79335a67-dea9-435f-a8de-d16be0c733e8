// 📁 lib/services/release_config_service.dart

import 'package:flutter/foundation.dart';

/// Release configuration service for managing debug vs production features
/// 
/// This service centralizes all release mode configurations to ensure
/// debug features are properly hidden in production builds.
/// 
/// Features controlled:
/// - Debug buttons and controls
/// - Performance monitoring overlays
/// - Admin panels and secret buttons
/// - System monitoring tools
/// - Developer menus and tools
/// - Logging and debug output
class ReleaseConfigService {

  // ═══════════════════════════════════════════════════════════════
  // APP STORE SCREENSHOT MODE
  // ═══════════════════════════════════════════════════════════════

  /// Manual override for App Store screenshots - set to true to hide all debug markers
  /// This allows you to take clean screenshots even in debug mode
  static const bool _appStoreScreenshotMode = true;

  // ═══════════════════════════════════════════════════════════════
  // CORE RELEASE MODE CHECKS
  // ═══════════════════════════════════════════════════════════════

  /// Check if app is running in release mode (production)
  static bool get isReleaseMode => !kDebugMode;

  /// Check if app is running in debug mode (development)
  static bool get isDebugMode => kDebugMode;

  // ═══════════════════════════════════════════════════════════════
  // UI FEATURE VISIBILITY
  // ═══════════════════════════════════════════════════════════════

  /// Should debug buttons be visible? (Toggle Theme, Debug Tests, etc.)
  static bool get shouldShowDebugButtons => kDebugMode;

  /// Should admin controls be accessible? (Admin Screen, User Tools, etc.)
  static bool get shouldShowAdminControls => kDebugMode;

  /// Should secret buttons be visible? (Secret Button in Admin Screen)
  static bool get shouldShowSecretButtons => kDebugMode;

  /// Should developer tools be accessible? (Developer Menu, etc.)
  static bool get shouldShowDeveloperTools => kDebugMode;

  /// Should OS Widgets configuration be available?
  static bool get shouldShowOSWidgetsButton => kDebugMode;

  /// Should system monitor be accessible?
  static bool get shouldShowSystemMonitor => kDebugMode;

  /// Should Clear EXP Log button be visible?
  static bool get shouldShowClearExpLog => kDebugMode;

  // ═══════════════════════════════════════════════════════════════
  // PERFORMANCE & MONITORING
  // ═══════════════════════════════════════════════════════════════

  /// Should performance monitoring be enabled?
  static bool get shouldEnablePerformanceMonitoring => kDebugMode;

  /// Should performance dashboard be accessible?
  static bool get shouldShowPerformanceDashboard => kDebugMode;

  /// Should debug overlays be shown? (FABs, service status, etc.)
  static bool get shouldShowDebugOverlays => kDebugMode && !_appStoreScreenshotMode;

  /// Should continuous monitoring be enabled?
  static bool get shouldEnableContinuousMonitoring => kDebugMode;

  /// Should health monitoring be enabled?
  static bool get shouldEnableHealthMonitoring => kDebugMode;

  // ═══════════════════════════════════════════════════════════════
  // LOGGING & DEBUG OUTPUT
  // ═══════════════════════════════════════════════════════════════

  /// Should debug logging be enabled?
  static bool get shouldEnableDebugLogging => kDebugMode;

  /// Should verbose logging be enabled?
  static bool get shouldEnableVerboseLogging => kDebugMode;

  /// Should performance logging be enabled?
  static bool get shouldEnablePerformanceLogging => kDebugMode;

  /// Should error tracking be detailed?
  static bool get shouldEnableDetailedErrorTracking => kDebugMode;

  // ═══════════════════════════════════════════════════════════════
  // DEVELOPMENT FEATURES
  // ═══════════════════════════════════════════════════════════════

  /// Should development tools section be visible?
  static bool get shouldShowDevelopmentToolsSection => kDebugMode;

  /// Should debug tests be accessible?
  static bool get shouldShowDebugTests => kDebugMode;

  /// Should transcript downloader be accessible?
  static bool get shouldShowTranscriptDownloader => kDebugMode;

  /// Should AI testing tools be accessible?
  static bool get shouldShowAITestingTools => kDebugMode;

  /// Should Phase 7 debug controls be accessible?
  static bool get shouldShowPhase7DebugControls => kDebugMode;

  // ═══════════════════════════════════════════════════════════════
  // SECURITY & ACCESS CONTROL
  // ═══════════════════════════════════════════════════════════════

  /// Should developer password protection be enforced?
  static bool get shouldEnforceDevPassword => !kDebugMode;

  /// Should admin features require authentication?
  static bool get shouldRequireAdminAuth => !kDebugMode;

  /// Should secret features be completely disabled?
  static bool get shouldDisableSecretFeatures => !kDebugMode;

  // ═══════════════════════════════════════════════════════════════
  // APP STORE SCREENSHOT HELPERS
  // ═══════════════════════════════════════════════════════════════

  /// Check if app is in App Store screenshot mode
  static bool get isAppStoreScreenshotMode => _appStoreScreenshotMode;

  /// Instructions for enabling App Store screenshot mode
  static String get appStoreScreenshotInstructions => '''
🍎 APP STORE SCREENSHOT MODE

To hide all debug markers for App Store screenshots:

1. Open lib/services/release_config_service.dart
2. Change line 25: static const bool _appStoreScreenshotMode = true;
3. Hot reload the app
4. Take your screenshots
5. Change it back to false when done

This will hide:
• Debug banner in top right corner
• Debug floating action buttons
• All debug overlays and markers
''';

  // ═══════════════════════════════════════════════════════════════
  // CONFIGURATION METHODS
  // ═══════════════════════════════════════════════════════════════

  /// Configure app for release mode
  static void configureForRelease() {
    if (kDebugMode) {
      debugPrint('🚀 Configuring app for release mode...');
      debugPrint('📱 Debug features will be hidden');
      debugPrint('🔒 Admin features will be restricted');
      debugPrint('📊 Performance monitoring will be disabled');
      debugPrint('🛡️ Security features will be enforced');
    }
  }

  /// Configure app for debug mode
  static void configureForDebug() {
    if (kDebugMode) {
      debugPrint('🔧 Configuring app for debug mode...');
      debugPrint('🛠️ Debug features enabled');
      debugPrint('👨‍💻 Developer tools accessible');
      debugPrint('📊 Performance monitoring enabled');
      debugPrint('🔍 Verbose logging enabled');
    }
  }

  /// Get current configuration summary
  static Map<String, dynamic> getConfigurationSummary() {
    return {
      'mode': isReleaseMode ? 'RELEASE' : 'DEBUG',
      'debugFeatures': shouldShowDebugButtons,
      'adminControls': shouldShowAdminControls,
      'secretButtons': shouldShowSecretButtons,
      'developerTools': shouldShowDeveloperTools,
      'performanceMonitoring': shouldEnablePerformanceMonitoring,
      'debugOverlays': shouldShowDebugOverlays,
      'debugLogging': shouldEnableDebugLogging,
      'securityEnforced': shouldEnforceDevPassword,
    };
  }

  /// Validate release configuration
  static bool validateReleaseConfiguration() {
    if (isReleaseMode) {
      // In release mode, all debug features should be disabled
      final issues = <String>[];
      
      if (shouldShowDebugButtons) issues.add('Debug buttons still visible');
      if (shouldShowSecretButtons) issues.add('Secret buttons still visible');
      if (shouldShowDeveloperTools) issues.add('Developer tools still accessible');
      if (shouldShowDebugOverlays) issues.add('Debug overlays still visible');
      
      if (issues.isNotEmpty) {
        debugPrint('❌ Release configuration validation failed:');
        for (final issue in issues) {
          debugPrint('   - $issue');
        }
        return false;
      }
      
      debugPrint('✅ Release configuration validation passed');
      return true;
    }
    
    // In debug mode, validation always passes
    debugPrint('🔧 Debug mode - validation skipped');
    return true;
  }
}
