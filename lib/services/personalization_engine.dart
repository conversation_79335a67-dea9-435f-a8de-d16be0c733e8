// lib/services/personalization_engine.dart

import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../models/user_model.dart';
import 'coach_memory_service.dart';

/// Advanced personalization engine that learns and adapts to each user
class PersonalizationEngine {
  static const FlutterSecureStorage _storage = FlutterSecureStorage();
  static const String _personalizationDataKey = 'personalization_data';
  
  // Cache for performance
  static final Map<String, PersonalizationProfile> _profileCache = {};

  /// Initialize personalization for a user
  static Future<void> initializePersonalization(String userId) async {
    try {
      await _loadPersonalizationProfile(userId);
      if (kDebugMode) print('🎯 Personalization initialized for user: $userId');
    } catch (e) {
      if (kDebugMode) print('❌ Failed to initialize personalization: $e');
    }
  }

  /// Get highly personalized response using all available data
  static Future<String> getPersonalizedResponse({
    required String message,
    required String category,
    required User user,
    required String basePrompt,
  }) async {
    try {
      // Get personalization profile
      final profile = await _getPersonalizationProfile(user.id);
      
      // Get memory context
      final memoryContext = await CoachMemoryService.getConversationContext(user.id, category);
      
      // Analyze current message for personalization cues
      final messageAnalysis = _analyzeMessage(message);
      
      // Determine optimal response strategy
      final responseStrategy = await _calculateOptimalStrategy(
        profile,
        memoryContext,
        messageAnalysis,
        category,
      );
      
      // Generate personalized prompt
      final personalizedPrompt = _buildPersonalizedPrompt(
        basePrompt,
        responseStrategy,
        memoryContext,
        user,
      );
      
      if (kDebugMode) print('🎯 Generated personalized response strategy for ${user.username}');
      
      return personalizedPrompt;
    } catch (e) {
      if (kDebugMode) print('❌ Personalization failed, using base prompt: $e');
      return basePrompt;
    }
  }

  /// Record user response to learn preferences
  static Future<void> recordUserFeedback({
    required String userId,
    required String category,
    required String coachResponse,
    required bool wasHelpful,
    required String userFeedback,
    String? responseType,
  }) async {
    try {
      final profile = await _getPersonalizationProfile(userId);
      
      // Analyze what made the response effective or ineffective
      final responseAnalysis = _analyzeResponseEffectiveness(
        coachResponse,
        wasHelpful,
        userFeedback,
        responseType,
      );
      
      // Update personalization profile
      await _updatePersonalizationProfile(userId, profile, responseAnalysis, category);
      
      if (kDebugMode) print('🎯 Recorded feedback for personalization learning');
    } catch (e) {
      if (kDebugMode) print('❌ Failed to record personalization feedback: $e');
    }
  }

  /// Get user's communication preferences
  static Future<Map<String, dynamic>> getUserCommunicationPreferences(String userId) async {
    try {
      final profile = await _getPersonalizationProfile(userId);
      final memoryAnalysis = await CoachMemoryService.analyzeUserCommunication(userId);
      
      return {
        'preferredTone': profile.preferredTone,
        'preferredLength': profile.preferredResponseLength,
        'motivationStyle': profile.motivationStyle,
        'communicationStyle': memoryAnalysis['communicationStyle'],
        'topicPreferences': memoryAnalysis['topicPreferences'],
        'optimalTiming': memoryAnalysis['optimalTiming'],
        'personalityTraits': profile.personalityTraits,
        'learningStyle': profile.learningStyle,
      };
    } catch (e) {
      if (kDebugMode) print('❌ Failed to get communication preferences: $e');
      return {};
    }
  }

  /// Predict optimal response characteristics for current context
  static Future<Map<String, dynamic>> predictOptimalResponse({
    required String userId,
    required String category,
    required String message,
  }) async {
    try {
      final profile = await _getPersonalizationProfile(userId);
      final messageAnalysis = _analyzeMessage(message);
      
      // Predict based on user's historical preferences
      final prediction = {
        'optimalTone': _predictOptimalTone(profile, messageAnalysis),
        'optimalLength': _predictOptimalLength(profile, messageAnalysis),
        'shouldIncludeQuestion': _shouldIncludeQuestion(profile, messageAnalysis),
        'shouldIncludeAction': _shouldIncludeAction(profile, messageAnalysis),
        'emotionalSupport': _predictEmotionalSupportLevel(profile, messageAnalysis),
        'motivationalLevel': _predictMotivationalLevel(profile, messageAnalysis),
        'personalityAdaptation': _predictPersonalityAdaptation(profile, category),
      };
      
      return prediction;
    } catch (e) {
      if (kDebugMode) print('❌ Failed to predict optimal response: $e');
      return {};
    }
  }

  /// Private helper methods

  static Future<PersonalizationProfile> _getPersonalizationProfile(String userId) async {
    if (!_profileCache.containsKey(userId)) {
      await _loadPersonalizationProfile(userId);
    }
    return _profileCache[userId] ?? PersonalizationProfile.empty(userId);
  }

  static Future<void> _loadPersonalizationProfile(String userId) async {
    try {
      final data = await _storage.read(key: '${_personalizationDataKey}_$userId');
      if (data != null) {
        _profileCache[userId] = PersonalizationProfile.fromJson(jsonDecode(data));
      } else {
        _profileCache[userId] = PersonalizationProfile.empty(userId);
      }
    } catch (e) {
      _profileCache[userId] = PersonalizationProfile.empty(userId);
    }
  }

  static Future<void> _savePersonalizationProfile(String userId) async {
    try {
      final profile = _profileCache[userId];
      if (profile != null) {
        await _storage.write(
          key: '${_personalizationDataKey}_$userId',
          value: jsonEncode(profile.toJson()),
        );
      }
    } catch (e) {
      if (kDebugMode) print('❌ Failed to save personalization profile: $e');
    }
  }

  static Map<String, dynamic> _analyzeMessage(String message) {
    return {
      'sentiment': _detectSentiment(message),
      'urgency': _detectUrgency(message),
      'complexity': _detectComplexity(message),
      'emotionalState': _detectEmotionalState(message),
      'questionType': _detectQuestionType(message),
      'topicCategory': _detectTopicCategory(message),
      'lengthPreference': message.length > 100 ? 'detailed' : 'concise',
    };
  }

  static Future<Map<String, dynamic>> _calculateOptimalStrategy(
    PersonalizationProfile profile,
    Map<String, dynamic> memoryContext,
    Map<String, dynamic> messageAnalysis,
    String category,
  ) async {
    // Combine all data sources to determine optimal strategy
    final strategy = <String, dynamic>{};
    
    // Determine tone based on user's mood and preferences
    final userMood = memoryContext['userMood'] ?? 'neutral';
    final messageSentiment = messageAnalysis['sentiment'] ?? 'neutral';
    
    if (messageSentiment == 'negative' || userMood == 'negative') {
      strategy['tone'] = profile.preferredSupportiveTone;
      strategy['emotionalSupport'] = 'high';
    } else if (messageSentiment == 'positive') {
      strategy['tone'] = profile.preferredMotivationalTone;
      strategy['motivationalLevel'] = 'high';
    } else {
      strategy['tone'] = profile.preferredTone;
      strategy['emotionalSupport'] = 'moderate';
    }
    
    // Determine response length
    strategy['responseLength'] = profile.preferredResponseLength;
    
    // Determine if questions should be included
    strategy['includeQuestion'] = profile.respondsWellToQuestions;
    
    // Determine if action items should be included
    strategy['includeAction'] = profile.prefersActionableAdvice;
    
    // Adapt personality for this category
    strategy['personalityAdaptation'] = profile.categoryPersonalities[category] ?? 'balanced';
    
    // Include effective strategies from memory
    strategy['effectiveStrategies'] = memoryContext['effectiveStrategies'] ?? [];
    
    return strategy;
  }

  static String _buildPersonalizedPrompt(
    String basePrompt,
    Map<String, dynamic> strategy,
    Map<String, dynamic> memoryContext,
    User user,
  ) {
    final personalizedPrompt = '''
$basePrompt

PERSONALIZATION INSTRUCTIONS:
- Use a ${strategy['tone']} tone
- Provide a ${strategy['responseLength']} response
- Emotional support level: ${strategy['emotionalSupport']}
- Motivational level: ${strategy['motivationalLevel'] ?? 'moderate'}
- Personality adaptation: ${strategy['personalityAdaptation']}
${strategy['includeQuestion'] == true ? '- Include a thoughtful question to engage the user' : ''}
${strategy['includeAction'] == true ? '- Provide specific, actionable advice' : ''}

EFFECTIVE STRATEGIES FOR THIS USER:
${(strategy['effectiveStrategies'] as List).join(', ')}

USER CONTEXT:
- Name: ${user.username}
- Recent mood: ${memoryContext['userMood']}
- Progress: ${memoryContext['userProgress']}
- Preferred style: ${memoryContext['preferredStyle']}

Remember to make this response feel personal and tailored specifically to ${user.username}.
''';

    return personalizedPrompt;
  }

  static Map<String, dynamic> _analyzeResponseEffectiveness(
    String response,
    bool wasHelpful,
    String userFeedback,
    String? responseType,
  ) {
    return {
      'wasHelpful': wasHelpful,
      'responseLength': response.length,
      'responseType': responseType ?? _categorizeResponse(response),
      'includedQuestion': response.contains('?'),
      'includedAction': _containsActionableAdvice(response),
      'tone': _analyzeTone(response),
      'userFeedbackSentiment': _detectSentiment(userFeedback),
    };
  }

  static Future<void> _updatePersonalizationProfile(
    String userId,
    PersonalizationProfile profile,
    Map<String, dynamic> responseAnalysis,
    String category,
  ) async {
    var updatedProfile = profile;
    
    final wasHelpful = responseAnalysis['wasHelpful'] as bool;
    final learningRate = 0.1; // How quickly to adapt
    
    if (wasHelpful) {
      // Reinforce successful strategies
      final responseType = responseAnalysis['responseType'] as String;
      updatedProfile.successfulStrategies[responseType] = 
          (updatedProfile.successfulStrategies[responseType] ?? 0.5) * (1 - learningRate) + 
          1.0 * learningRate;
      
      // Update preferences based on what worked
      var respondsWellToQuestions = updatedProfile.respondsWellToQuestions;
      var prefersActionableAdvice = updatedProfile.prefersActionableAdvice;
      var preferredResponseLength = updatedProfile.preferredResponseLength;

      if (responseAnalysis['includedQuestion'] == true) {
        respondsWellToQuestions = true;
      }

      if (responseAnalysis['includedAction'] == true) {
        prefersActionableAdvice = true;
      }

      // Update preferred response length
      final responseLength = responseAnalysis['responseLength'] as int;
      if (responseLength > 200) {
        preferredResponseLength = 'detailed';
      } else if (responseLength < 100) {
        preferredResponseLength = 'concise';
      }

      // Apply updates using copyWith
      updatedProfile = updatedProfile.copyWith(
        respondsWellToQuestions: respondsWellToQuestions,
        prefersActionableAdvice: prefersActionableAdvice,
        preferredResponseLength: preferredResponseLength,
      );
    } else {
      // Learn from unsuccessful strategies
      final responseType = responseAnalysis['responseType'] as String;
      updatedProfile.unsuccessfulStrategies[responseType] = 
          (updatedProfile.unsuccessfulStrategies[responseType] ?? 0.5) * (1 - learningRate) + 
          1.0 * learningRate;
    }
    
    updatedProfile = updatedProfile.copyWith(
      lastUpdated: DateTime.now(),
      totalInteractions: updatedProfile.totalInteractions + 1,
    );
    
    _profileCache[userId] = updatedProfile;
    await _savePersonalizationProfile(userId);
  }

  // Analysis helper methods
  static String _detectSentiment(String text) {
    final lowerText = text.toLowerCase();
    
    final positiveWords = ['great', 'good', 'happy', 'excited', 'love', 'amazing', 'wonderful'];
    final negativeWords = ['bad', 'sad', 'angry', 'frustrated', 'hate', 'terrible', 'awful'];
    
    final positiveCount = positiveWords.where((word) => lowerText.contains(word)).length;
    final negativeCount = negativeWords.where((word) => lowerText.contains(word)).length;
    
    if (positiveCount > negativeCount) return 'positive';
    if (negativeCount > positiveCount) return 'negative';
    return 'neutral';
  }

  static String _detectUrgency(String message) {
    final lowerMessage = message.toLowerCase();

    if (lowerMessage.contains(RegExp(r'\b(urgent|asap|immediately|now|emergency)\b'))) {
      return 'high';
    }
    if (lowerMessage.contains(RegExp(r'\b(soon|quickly|fast|hurry)\b'))) {
      return 'medium';
    }
    return 'low';
  }

  static String _detectComplexity(String message) {
    if (message.length > 200) return 'high';
    if (message.length > 100) return 'medium';
    return 'low';
  }

  static String _detectEmotionalState(String message) {
    final lowerMessage = message.toLowerCase();

    if (lowerMessage.contains(RegExp(r'\b(stressed|overwhelmed|anxious|worried)\b'))) {
      return 'stressed';
    }
    if (lowerMessage.contains(RegExp(r'\b(excited|motivated|energized|pumped)\b'))) {
      return 'energized';
    }
    if (lowerMessage.contains(RegExp(r'\b(confused|lost|uncertain|unclear)\b'))) {
      return 'confused';
    }
    if (lowerMessage.contains(RegExp(r'\b(tired|exhausted|drained|weary)\b'))) {
      return 'tired';
    }
    return 'neutral';
  }

  static String _detectQuestionType(String message) {
    if (!message.contains('?')) return 'none';
    
    final lowerMessage = message.toLowerCase();
    
    if (lowerMessage.startsWith('how')) return 'how';
    if (lowerMessage.startsWith('what')) return 'what';
    if (lowerMessage.startsWith('why')) return 'why';
    if (lowerMessage.startsWith('when')) return 'when';
    if (lowerMessage.startsWith('where')) return 'where';
    
    return 'general';
  }

  static String _detectTopicCategory(String message) {
    final lowerMessage = message.toLowerCase();

    if (lowerMessage.contains(RegExp(r'\b(exercise|workout|fitness|health|diet)\b'))) {
      return 'health';
    }
    if (lowerMessage.contains(RegExp(r'\b(money|finance|budget|career|job)\b'))) {
      return 'wealth';
    }
    if (lowerMessage.contains(RegExp(r'\b(purpose|meaning|goal|dream|vision)\b'))) {
      return 'purpose';
    }
    if (lowerMessage.contains(RegExp(r'\b(relationship|friend|family|social)\b'))) {
      return 'connection';
    }
    
    return 'general';
  }

  static String _categorizeResponse(String response) {
    final lowerResponse = response.toLowerCase();
    
    if (lowerResponse.contains(RegExp(r'\b(question|ask|wonder)\b'))) {
      return 'questioning';
    }
    if (lowerResponse.contains(RegExp(r'\b(step|plan|action|do)\b'))) {
      return 'actionable';
    }
    if (lowerResponse.contains(RegExp(r'\b(understand|feel|empathy)\b'))) {
      return 'empathetic';
    }
    if (lowerResponse.contains(RegExp(r'\b(motivate|inspire|encourage)\b'))) {
      return 'motivational';
    }
    
    return 'informational';
  }

  static bool _containsActionableAdvice(String response) {
    final lowerResponse = response.toLowerCase();
    return lowerResponse.contains(RegExp(r'\b(try|do|start|begin|take|make|create)\b'));
  }

  static String _analyzeTone(String response) {
    final lowerResponse = response.toLowerCase();
    
    if (lowerResponse.contains(RegExp(r'\b(amazing|fantastic|incredible|awesome)\b'))) {
      return 'enthusiastic';
    }
    if (lowerResponse.contains(RegExp(r'\b(understand|feel|empathy|support)\b'))) {
      return 'supportive';
    }
    if (lowerResponse.contains(RegExp(r"\b(let's|we|together|team)\b"))) {
      return 'collaborative';
    }
    
    return 'balanced';
  }

  // Prediction methods
  static String _predictOptimalTone(PersonalizationProfile profile, Map<String, dynamic> messageAnalysis) {
    final emotionalState = messageAnalysis['emotionalState'] as String;
    
    if (emotionalState == 'stressed') return profile.preferredSupportiveTone;
    if (emotionalState == 'energized') return profile.preferredMotivationalTone;
    if (emotionalState == 'confused') return 'patient and explanatory';
    
    return profile.preferredTone;
  }

  static String _predictOptimalLength(PersonalizationProfile profile, Map<String, dynamic> messageAnalysis) {
    final complexity = messageAnalysis['complexity'] as String;
    
    if (complexity == 'high') return 'detailed';
    if (complexity == 'low') return 'concise';
    
    return profile.preferredResponseLength;
  }

  static bool _shouldIncludeQuestion(PersonalizationProfile profile, Map<String, dynamic> messageAnalysis) {
    if (!profile.respondsWellToQuestions) return false;
    
    final questionType = messageAnalysis['questionType'] as String;
    return questionType != 'none'; // If user asked a question, they're likely open to questions
  }

  static bool _shouldIncludeAction(PersonalizationProfile profile, Map<String, dynamic> messageAnalysis) {
    if (!profile.prefersActionableAdvice) return false;
    
    final emotionalState = messageAnalysis['emotionalState'] as String;
    return emotionalState != 'stressed'; // Don't overwhelm stressed users with actions
  }

  static String _predictEmotionalSupportLevel(PersonalizationProfile profile, Map<String, dynamic> messageAnalysis) {
    final emotionalState = messageAnalysis['emotionalState'] as String;
    final sentiment = messageAnalysis['sentiment'] as String;
    
    if (emotionalState == 'stressed' || sentiment == 'negative') return 'high';
    if (emotionalState == 'confused') return 'medium';
    
    return 'low';
  }

  static String _predictMotivationalLevel(PersonalizationProfile profile, Map<String, dynamic> messageAnalysis) {
    final emotionalState = messageAnalysis['emotionalState'] as String;
    
    if (emotionalState == 'tired') return 'gentle';
    if (emotionalState == 'energized') return 'high';
    
    return 'moderate';
  }

  static String _predictPersonalityAdaptation(PersonalizationProfile profile, String category) {
    return profile.categoryPersonalities[category] ?? 'balanced';
  }
}

/// Personalization profile for a user
class PersonalizationProfile {
  final String userId;
  final String preferredTone;
  final String preferredResponseLength;
  final String motivationStyle;
  final String learningStyle;
  final String preferredSupportiveTone;
  final String preferredMotivationalTone;
  final bool respondsWellToQuestions;
  final bool prefersActionableAdvice;
  final Map<String, double> personalityTraits;
  final Map<String, String> categoryPersonalities;
  final Map<String, double> successfulStrategies;
  final Map<String, double> unsuccessfulStrategies;
  final int totalInteractions;
  final DateTime lastUpdated;

  PersonalizationProfile({
    required this.userId,
    required this.preferredTone,
    required this.preferredResponseLength,
    required this.motivationStyle,
    required this.learningStyle,
    required this.preferredSupportiveTone,
    required this.preferredMotivationalTone,
    required this.respondsWellToQuestions,
    required this.prefersActionableAdvice,
    required this.personalityTraits,
    required this.categoryPersonalities,
    required this.successfulStrategies,
    required this.unsuccessfulStrategies,
    required this.totalInteractions,
    required this.lastUpdated,
  });

  factory PersonalizationProfile.empty(String userId) {
    return PersonalizationProfile(
      userId: userId,
      preferredTone: 'balanced',
      preferredResponseLength: 'moderate',
      motivationStyle: 'balanced',
      learningStyle: 'balanced',
      preferredSupportiveTone: 'empathetic',
      preferredMotivationalTone: 'encouraging',
      respondsWellToQuestions: true,
      prefersActionableAdvice: true,
      personalityTraits: {},
      categoryPersonalities: {},
      successfulStrategies: {},
      unsuccessfulStrategies: {},
      totalInteractions: 0,
      lastUpdated: DateTime.now(),
    );
  }

  factory PersonalizationProfile.fromJson(Map<String, dynamic> json) {
    return PersonalizationProfile(
      userId: json['userId'] as String,
      preferredTone: json['preferredTone'] as String,
      preferredResponseLength: json['preferredResponseLength'] as String,
      motivationStyle: json['motivationStyle'] as String,
      learningStyle: json['learningStyle'] as String,
      preferredSupportiveTone: json['preferredSupportiveTone'] as String,
      preferredMotivationalTone: json['preferredMotivationalTone'] as String,
      respondsWellToQuestions: json['respondsWellToQuestions'] as bool,
      prefersActionableAdvice: json['prefersActionableAdvice'] as bool,
      personalityTraits: Map<String, double>.from(json['personalityTraits'] as Map),
      categoryPersonalities: Map<String, String>.from(json['categoryPersonalities'] as Map),
      successfulStrategies: Map<String, double>.from(json['successfulStrategies'] as Map),
      unsuccessfulStrategies: Map<String, double>.from(json['unsuccessfulStrategies'] as Map),
      totalInteractions: json['totalInteractions'] as int,
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'preferredTone': preferredTone,
      'preferredResponseLength': preferredResponseLength,
      'motivationStyle': motivationStyle,
      'learningStyle': learningStyle,
      'preferredSupportiveTone': preferredSupportiveTone,
      'preferredMotivationalTone': preferredMotivationalTone,
      'respondsWellToQuestions': respondsWellToQuestions,
      'prefersActionableAdvice': prefersActionableAdvice,
      'personalityTraits': personalityTraits,
      'categoryPersonalities': categoryPersonalities,
      'successfulStrategies': successfulStrategies,
      'unsuccessfulStrategies': unsuccessfulStrategies,
      'totalInteractions': totalInteractions,
      'lastUpdated': lastUpdated.toIso8601String(),
    };
  }

  PersonalizationProfile copyWith({
    String? userId,
    String? preferredTone,
    String? preferredResponseLength,
    String? motivationStyle,
    String? learningStyle,
    String? preferredSupportiveTone,
    String? preferredMotivationalTone,
    bool? respondsWellToQuestions,
    bool? prefersActionableAdvice,
    Map<String, double>? personalityTraits,
    Map<String, String>? categoryPersonalities,
    Map<String, double>? successfulStrategies,
    Map<String, double>? unsuccessfulStrategies,
    int? totalInteractions,
    DateTime? lastUpdated,
  }) {
    return PersonalizationProfile(
      userId: userId ?? this.userId,
      preferredTone: preferredTone ?? this.preferredTone,
      preferredResponseLength: preferredResponseLength ?? this.preferredResponseLength,
      motivationStyle: motivationStyle ?? this.motivationStyle,
      learningStyle: learningStyle ?? this.learningStyle,
      preferredSupportiveTone: preferredSupportiveTone ?? this.preferredSupportiveTone,
      preferredMotivationalTone: preferredMotivationalTone ?? this.preferredMotivationalTone,
      respondsWellToQuestions: respondsWellToQuestions ?? this.respondsWellToQuestions,
      prefersActionableAdvice: prefersActionableAdvice ?? this.prefersActionableAdvice,
      personalityTraits: personalityTraits ?? this.personalityTraits,
      categoryPersonalities: categoryPersonalities ?? this.categoryPersonalities,
      successfulStrategies: successfulStrategies ?? this.successfulStrategies,
      unsuccessfulStrategies: unsuccessfulStrategies ?? this.unsuccessfulStrategies,
      totalInteractions: totalInteractions ?? this.totalInteractions,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }
}
