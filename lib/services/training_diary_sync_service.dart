// lib/services/training_diary_sync_service.dart

import 'package:flutter/foundation.dart';
import '../models/training_session_model.dart';
import '../models/user_model.dart';
import '../models/diary_entry_model.dart';
import '../controller/user_controller2.dart';
import '../services/comprehensive_logging_service.dart';
import '../bulletproof/error_handler.dart';

/// Service for synchronizing training sessions with diary entries
/// 
/// This service ensures data integrity between training sessions and diary entries
/// when training sessions are edited or deleted. It handles EXP adjustments and
/// maintains accurate user statistics.
class TrainingDiarySyncService {
  static final TrainingDiarySyncService _instance = TrainingDiarySyncService._internal();
  factory TrainingDiarySyncService() => _instance;
  
  TrainingDiarySyncService._internal() : _errorHandler = ErrorHandler();

  final ErrorHandler _errorHandler;

  /// Update diary entry when training session is edited
  /// 
  /// This method finds the corresponding diary entry for a training session
  /// and updates it with the new duration and EXP values, ensuring data consistency.
  Future<User?> updateDiaryEntryForEditedSession({
    required UserController2 userController,
    required TrainingSession originalSession,
    required TrainingSession updatedSession,
  }) async {
    try {
      await ComprehensiveLoggingService.logInfo('🔄 TrainingDiarySync: Updating diary entry for edited session ${updatedSession.id}');
      
      final user = userController.user;
      if (user == null) {
        await ComprehensiveLoggingService.logError('❌ TrainingDiarySync: No user loaded');
        return null;
      }

      // Find the corresponding diary entry
      final diaryEntry = _findDiaryEntryForSession(user, originalSession);
      if (diaryEntry == null) {
        await ComprehensiveLoggingService.logWarning('⚠️ TrainingDiarySync: No diary entry found for session ${originalSession.id}');
        // Session might have been created before diary integration - create new entry
        return await _createDiaryEntryForSession(userController, updatedSession);
      }

      // Calculate EXP difference
      final expDifference = updatedSession.expEarned - originalSession.expEarned;
      
      await ComprehensiveLoggingService.logInfo('📊 TrainingDiarySync: EXP difference: $expDifference');
      await ComprehensiveLoggingService.logInfo('   Original EXP: ${originalSession.expEarned}');
      await ComprehensiveLoggingService.logInfo('   Updated EXP: ${updatedSession.expEarned}');

      // Create updated diary note
      final timeStamp = '${updatedSession.createdAt.hour.toString().padLeft(2, '0')}:${updatedSession.createdAt.minute.toString().padLeft(2, '0')}';
      final updatedNote = '${updatedSession.label} Training (${updatedSession.formattedDuration}) - $timeStamp';

      // Update the diary entry
      final updatedDiaryEntry = diaryEntry.copyWith(
        note: updatedNote,
        exp: updatedSession.expEarned.round(),
        lastModified: DateTime.now(),
      );

      // Update user's diary entries list
      final updatedDiaryEntries = user.diaryEntries.map((entry) {
        return entry.id == diaryEntry.id ? updatedDiaryEntry : entry;
      }).toList();

      // Adjust user's category and total EXP
      final updatedCategories = Map<String, int>.from(user.categories);
      updatedCategories['Health'] = (updatedCategories['Health'] ?? 0) + expDifference.round();

      // Create updated user
      final updatedUser = user.copyWith(
        diaryEntries: updatedDiaryEntries,
        categories: updatedCategories,
        exp: user.exp + expDifference.round(),
        lastModified: DateTime.now(),
      );

      // Update user in controller
      await userController.updateUser(updatedUser);

      await ComprehensiveLoggingService.logInfo('✅ TrainingDiarySync: Diary entry updated successfully');
      await ComprehensiveLoggingService.logInfo('   Updated note: "$updatedNote"');
      await ComprehensiveLoggingService.logInfo('   Updated EXP: ${updatedSession.expEarned}');
      await ComprehensiveLoggingService.logInfo('   User total EXP: ${updatedUser.exp}');

      return updatedUser;
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace);
      await ComprehensiveLoggingService.logError('❌ TrainingDiarySync: Error updating diary entry: $e');
      return null;
    }
  }

  /// Remove diary entry when training session is deleted
  /// 
  /// This method finds and removes the corresponding diary entry for a deleted
  /// training session and adjusts the user's EXP accordingly.
  Future<User?> removeDiaryEntryForDeletedSession({
    required UserController2 userController,
    required TrainingSession deletedSession,
  }) async {
    try {
      await ComprehensiveLoggingService.logInfo('🗑️ TrainingDiarySync: Removing diary entry for deleted session ${deletedSession.id}');
      
      final user = userController.user;
      if (user == null) {
        await ComprehensiveLoggingService.logError('❌ TrainingDiarySync: No user loaded');
        return null;
      }

      // Find the corresponding diary entry
      final diaryEntry = _findDiaryEntryForSession(user, deletedSession);
      if (diaryEntry == null) {
        await ComprehensiveLoggingService.logWarning('⚠️ TrainingDiarySync: No diary entry found for deleted session ${deletedSession.id}');
        return user; // No diary entry to remove, return user unchanged
      }

      // Remove the diary entry
      final updatedDiaryEntries = user.diaryEntries.where((entry) => entry.id != diaryEntry.id).toList();

      // Adjust user's category and total EXP (subtract the removed EXP)
      final updatedCategories = Map<String, int>.from(user.categories);
      updatedCategories['Health'] = (updatedCategories['Health'] ?? 0) - diaryEntry.exp;

      // Create updated user
      final updatedUser = user.copyWith(
        diaryEntries: updatedDiaryEntries,
        categories: updatedCategories,
        exp: user.exp - diaryEntry.exp,
        lastModified: DateTime.now(),
      );

      // Update user in controller
      await userController.updateUser(updatedUser);

      await ComprehensiveLoggingService.logInfo('✅ TrainingDiarySync: Diary entry removed successfully');
      await ComprehensiveLoggingService.logInfo('   Removed EXP: ${diaryEntry.exp}');
      await ComprehensiveLoggingService.logInfo('   User total EXP: ${updatedUser.exp}');

      return updatedUser;
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace);
      await ComprehensiveLoggingService.logError('❌ TrainingDiarySync: Error removing diary entry: $e');
      return null;
    }
  }

  /// Create diary entry for session (for sessions created before diary integration)
  Future<User?> _createDiaryEntryForSession(
    UserController2 userController,
    TrainingSession session,
  ) async {
    try {
      await ComprehensiveLoggingService.logInfo('📝 TrainingDiarySync: Creating diary entry for session ${session.id}');
      
      final user = userController.user;
      if (user == null) return null;

      // Create diary note
      final timeStamp = '${session.createdAt.hour.toString().padLeft(2, '0')}:${session.createdAt.minute.toString().padLeft(2, '0')}';
      final diaryNote = '${session.label} Training (${session.formattedDuration}) - $timeStamp';

      // Create new diary entry
      final newEntry = DiaryEntry.create(
        category: 'Health',
        note: diaryNote,
        exp: session.expEarned.round(),
      );

      // Update user's diary entries and EXP
      final updatedCategories = Map<String, int>.from(user.categories);
      updatedCategories['Health'] = (updatedCategories['Health'] ?? 0) + session.expEarned.round();

      final updatedUser = user.copyWith(
        diaryEntries: [newEntry, ...user.diaryEntries],
        categories: updatedCategories,
        exp: user.exp + session.expEarned.round(),
        lastModified: DateTime.now(),
      );

      await userController.updateUser(updatedUser);

      await ComprehensiveLoggingService.logInfo('✅ TrainingDiarySync: Diary entry created successfully');
      return updatedUser;
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace);
      await ComprehensiveLoggingService.logError('❌ TrainingDiarySync: Error creating diary entry: $e');
      return null;
    }
  }

  /// Find diary entry that corresponds to a training session
  /// 
  /// Uses pattern matching on the diary note to find the corresponding entry
  /// since there's no direct ID relationship between sessions and diary entries.
  DiaryEntry? _findDiaryEntryForSession(User user, TrainingSession session) {
    try {
      // Create the expected diary note pattern
      final timeStamp = '${session.createdAt.hour.toString().padLeft(2, '0')}:${session.createdAt.minute.toString().padLeft(2, '0')}';

      // Look for diary entries that match the pattern
      for (final entry in user.diaryEntries) {
        if (entry.category == 'Health' && 
            entry.note.contains('${session.label} Training') &&
            entry.note.contains(timeStamp)) {
          
          // Additional validation: check if the timestamp is close to session creation
          final timeDifference = (entry.timestamp.millisecondsSinceEpoch - session.createdAt.millisecondsSinceEpoch).abs();
          if (timeDifference < 60000) { // Within 1 minute
            return entry;
          }
        }
      }
      
      return null;
    } catch (e) {
      if (kDebugMode) print('❌ TrainingDiarySync: Error finding diary entry: $e');
      return null;
    }
  }

  /// Validate that training sessions and diary entries are in sync
  /// 
  /// This method can be used for debugging and data integrity checks
  Future<Map<String, dynamic>> validateSyncStatus({
    required User user,
    required List<TrainingSession> sessions,
  }) async {
    try {
      int syncedSessions = 0;
      int unsyncedSessions = 0;
      final List<String> issues = [];

      for (final session in sessions) {
        final diaryEntry = _findDiaryEntryForSession(user, session);
        if (diaryEntry != null) {
          syncedSessions++;
          
          // Check if EXP values match
          if (diaryEntry.exp != session.expEarned.round()) {
            issues.add('Session ${session.id}: EXP mismatch (Session: ${session.expEarned}, Diary: ${diaryEntry.exp})');
          }
        } else {
          unsyncedSessions++;
          issues.add('Session ${session.id}: No corresponding diary entry found');
        }
      }

      final result = {
        'syncedSessions': syncedSessions,
        'unsyncedSessions': unsyncedSessions,
        'totalSessions': sessions.length,
        'syncPercentage': sessions.isNotEmpty ? (syncedSessions / sessions.length * 100).toStringAsFixed(1) : '0.0',
        'issues': issues,
      };

      await ComprehensiveLoggingService.logInfo('📊 TrainingDiarySync: Validation complete');
      await ComprehensiveLoggingService.logInfo('   Synced: $syncedSessions, Unsynced: $unsyncedSessions');
      await ComprehensiveLoggingService.logInfo('   Sync rate: ${result['syncPercentage']}%');

      return result;
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace);
      return {
        'error': e.toString(),
        'syncedSessions': 0,
        'unsyncedSessions': 0,
        'totalSessions': 0,
        'syncPercentage': '0.0',
        'issues': ['Validation failed: $e'],
      };
    }
  }
}
