import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user_model.dart';
import '../models/bounty_model.dart';
import '../services/real_notification_service.dart';
import '../services/bounty_notification_service.dart';
import '../services/advanced_bounty_generator.dart';
import '../utils/debug_logger.dart';

/// High-level notification manager that orchestrates all notification features
/// 
/// Features:
/// - Automatic bounty notification scheduling
/// - Smart timing and frequency management
/// - Epic bounty alerts
/// - Background notification processing
/// - Integration with all bounty services
/// - Comprehensive failure handling
class NotificationManager {
  static final NotificationManager _instance = NotificationManager._internal();
  factory NotificationManager() => _instance;
  NotificationManager._internal();

  static const String _debugPrefix = 'NotificationManager';
  static const String _autoScheduleKey = 'auto_schedule_enabled';
  static const String _lastAutoScheduleKey = 'last_auto_schedule';
  static const String _notificationIntervalKey = 'notification_interval_hours';

  final RealNotificationService _realNotificationService = RealNotificationService();
  Timer? _autoScheduleTimer;
  bool _isInitialized = false;
  bool _autoScheduleEnabled = true;
  int _notificationIntervalHours = 72; // 3 days default

  /// Initialize the notification manager
  Future<bool> initialize() async {
    try {
      DebugLogger.log(_debugPrefix, '🚀 Initializing notification manager...');

      // Initialize the real notification service
      final serviceInitialized = await _realNotificationService.initialize();
      if (!serviceInitialized) {
        DebugLogger.error(_debugPrefix, 'Failed to initialize real notification service', 
          Exception('Service initialization failed'), StackTrace.current);
        return false;
      }

      // Request permissions
      final permissionsGranted = await _realNotificationService.requestPermissions();
      if (!permissionsGranted) {
        DebugLogger.warn(_debugPrefix, 'Notification permissions not granted');
        // Continue anyway - user might grant later
      }

      // Load settings
      await _loadSettings();

      // Start auto-scheduling if enabled
      if (_autoScheduleEnabled) {
        _startAutoScheduling();
      }

      _isInitialized = true;
      DebugLogger.log(_debugPrefix, '✅ Notification manager initialized successfully');
      return true;

    } catch (e, stackTrace) {
      DebugLogger.error(_debugPrefix, 'Error initializing notification manager', e, stackTrace);
      return false;
    }
  }

  /// Load settings from persistent storage
  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _autoScheduleEnabled = prefs.getBool(_autoScheduleKey) ?? true;
      _notificationIntervalHours = prefs.getInt(_notificationIntervalKey) ?? 72;
      
      DebugLogger.log(_debugPrefix, 
        'Settings loaded: AutoSchedule=$_autoScheduleEnabled, Interval=${_notificationIntervalHours}h');
    } catch (e, stackTrace) {
      DebugLogger.error(_debugPrefix, 'Error loading settings', e, stackTrace);
    }
  }

  /// Start automatic notification scheduling
  void _startAutoScheduling() {
    _autoScheduleTimer?.cancel();
    
    // Check every hour for scheduling opportunities
    _autoScheduleTimer = Timer.periodic(Duration(hours: 1), (timer) {
      _checkAndScheduleNotifications();
    });
    
    DebugLogger.log(_debugPrefix, '⏰ Auto-scheduling started (checking every hour)');
    
    // Do initial check
    _checkAndScheduleNotifications();
  }

  /// Check if notifications should be scheduled and schedule them
  Future<void> _checkAndScheduleNotifications() async {
    try {
      if (!_isInitialized) return;

      DebugLogger.log(_debugPrefix, '🔍 Checking for notification scheduling opportunities...');

      final prefs = await SharedPreferences.getInstance();
      final lastScheduleTime = prefs.getString(_lastAutoScheduleKey);
      
      DateTime? lastSchedule;
      if (lastScheduleTime != null) {
        lastSchedule = DateTime.parse(lastScheduleTime);
      }

      final now = DateTime.now();
      
      // Check if enough time has passed since last auto-schedule
      if (lastSchedule != null) {
        final timeSinceLastSchedule = now.difference(lastSchedule);
        if (timeSinceLastSchedule.inHours < _notificationIntervalHours) {
          DebugLogger.log(_debugPrefix, 
            'Too soon for auto-schedule: ${timeSinceLastSchedule.inHours}h < ${_notificationIntervalHours}h');
          return;
        }
      }

      // TODO: Get current user from app state
      // For now, we'll skip auto-scheduling without user context
      DebugLogger.log(_debugPrefix, 'Auto-scheduling requires user context - skipping');
      
      // Update last schedule time
      await prefs.setString(_lastAutoScheduleKey, now.toIso8601String());

    } catch (e, stackTrace) {
      DebugLogger.error(_debugPrefix, 'Error in auto-scheduling check', e, stackTrace);
    }
  }

  /// Send immediate bounty notification to user
  Future<bool> sendBountyNotification({
    required User user,
    BountyModel? bounty,
    String? customMessage,
  }) async {
    try {
      if (!_isInitialized) {
        DebugLogger.error(_debugPrefix, 'Cannot send notification: manager not initialized', 
          Exception('Manager not initialized'), StackTrace.current);
        return false;
      }

      DebugLogger.log(_debugPrefix, '📤 Sending bounty notification to ${user.username}...');

      // Generate bounty if not provided
      BountyModel? targetBounty = bounty;
      if (targetBounty == null) {
        // TODO: Get completed bounty IDs from user data
        final completedIds = <String>[];
        targetBounty = AdvancedBountyGenerator.generateDailyBounty(user, completedIds);
        
        if (targetBounty == null) {
          DebugLogger.warn(_debugPrefix, 'No suitable bounty found for user ${user.username}');
          return false;
        }
      }

      // Send notification
      final success = await _realNotificationService.sendBountyNotification(
        user: user,
        bounty: targetBounty,
        customMessage: customMessage,
      );

      if (success) {
        DebugLogger.log(_debugPrefix, '✅ Bounty notification sent successfully');
        await _recordNotificationSent(user.id, targetBounty.id, 'immediate');
      } else {
        DebugLogger.error(_debugPrefix, 'Failed to send bounty notification', 
          Exception('Send failed'), StackTrace.current);
      }

      return success;

    } catch (e, stackTrace) {
      DebugLogger.error(_debugPrefix, 'Error sending bounty notification', e, stackTrace);
      return false;
    }
  }

  /// Schedule bounty notification for optimal time
  Future<bool> scheduleOptimalBountyNotification({
    required User user,
    BountyModel? bounty,
    String? customMessage,
  }) async {
    try {
      if (!_isInitialized) {
        DebugLogger.error(_debugPrefix, 'Cannot schedule notification: manager not initialized', 
          Exception('Manager not initialized'), StackTrace.current);
        return false;
      }

      DebugLogger.log(_debugPrefix, '⏰ Scheduling optimal bounty notification for ${user.username}...');

      // Calculate optimal notification time
      final optimalTime = _calculateOptimalNotificationTime();
      
      // Generate bounty if not provided
      BountyModel? targetBounty = bounty;
      if (targetBounty == null) {
        // TODO: Get completed bounty IDs from user data
        final completedIds = <String>[];
        targetBounty = AdvancedBountyGenerator.generateDailyBounty(user, completedIds);
        
        if (targetBounty == null) {
          DebugLogger.warn(_debugPrefix, 'No suitable bounty found for user ${user.username}');
          return false;
        }
      }

      // Schedule notification
      final success = await _realNotificationService.scheduleBountyNotification(
        user: user,
        bounty: targetBounty,
        scheduledTime: optimalTime,
        customMessage: customMessage,
      );

      if (success) {
        DebugLogger.log(_debugPrefix, 
          '✅ Bounty notification scheduled for $optimalTime');
        await _recordNotificationSent(user.id, targetBounty.id, 'scheduled');
      } else {
        DebugLogger.error(_debugPrefix, 'Failed to schedule bounty notification', 
          Exception('Schedule failed'), StackTrace.current);
      }

      return success;

    } catch (e, stackTrace) {
      DebugLogger.error(_debugPrefix, 'Error scheduling bounty notification', e, stackTrace);
      return false;
    }
  }

  /// Send epic bounty alert if user qualifies
  Future<bool> sendEpicBountyAlert(User user) async {
    try {
      if (!_isInitialized) return false;

      // Check if user qualifies for epic bounty alert
      if (!BountyNotificationService.shouldSendEpicBountyAlert(user)) {
        DebugLogger.log(_debugPrefix, 'User ${user.username} does not qualify for epic bounty alert');
        return false;
      }

      DebugLogger.log(_debugPrefix, '⚡ Sending epic bounty alert to ${user.username}...');

      // Generate epic bounty
      final completedIds = <String>[]; // TODO: Get from user data
      final epicBounty = AdvancedBountyGenerator.generateDailyBounty(user, completedIds);
      
      if (epicBounty == null || !epicBounty.isEpic) {
        DebugLogger.warn(_debugPrefix, 'No epic bounty available for alert');
        return false;
      }

      // Generate epic alert message
      final alertMessage = BountyNotificationService.generateEpicBountyAlert(user);

      // Send notification
      final success = await _realNotificationService.sendBountyNotification(
        user: user,
        bounty: epicBounty,
        customMessage: alertMessage,
      );

      if (success) {
        DebugLogger.log(_debugPrefix, '✅ Epic bounty alert sent successfully');
        await _recordNotificationSent(user.id, epicBounty.id, 'epic_alert');
      }

      return success;

    } catch (e, stackTrace) {
      DebugLogger.error(_debugPrefix, 'Error sending epic bounty alert', e, stackTrace);
      return false;
    }
  }

  /// Calculate optimal notification time based on user preferences and business rules
  DateTime _calculateOptimalNotificationTime() {
    final now = DateTime.now();
    final random = Random();
    
    // Calculate next notification time (3 days from now by default)
    DateTime nextTime = now.add(Duration(hours: _notificationIntervalHours));
    
    // Adjust to be within allowed time window (5:45am - 8:45pm)
    final timeOfDay = TimeOfDay.fromDateTime(nextTime);
    final currentMinutes = timeOfDay.hour * 60 + timeOfDay.minute;
    
    const earliestMinutes = 5 * 60 + 45; // 5:45am
    const latestMinutes = 20 * 60 + 45; // 8:45pm
    
    if (currentMinutes < earliestMinutes) {
      // Too early, move to 5:45am same day
      nextTime = DateTime(nextTime.year, nextTime.month, nextTime.day, 5, 45);
    } else if (currentMinutes > latestMinutes) {
      // Too late, move to 5:45am next day
      nextTime = DateTime(nextTime.year, nextTime.month, nextTime.day + 1, 5, 45);
    }
    
    // Add some randomness (±2 hours) to avoid predictability
    final randomOffset = random.nextInt(240) - 120; // -120 to +120 minutes
    nextTime = nextTime.add(Duration(minutes: randomOffset));
    
    // Ensure still within time window after randomization
    final finalTimeOfDay = TimeOfDay.fromDateTime(nextTime);
    final finalMinutes = finalTimeOfDay.hour * 60 + finalTimeOfDay.minute;
    
    if (finalMinutes < earliestMinutes || finalMinutes > latestMinutes) {
      // Reset to safe time if randomization pushed outside window
      nextTime = DateTime(nextTime.year, nextTime.month, nextTime.day, 10, 0); // 10:00am
    }
    
    DebugLogger.log(_debugPrefix, 'Calculated optimal notification time: $nextTime');
    return nextTime;
  }

  /// Record notification sent for analytics
  Future<void> _recordNotificationSent(String userId, String bountyId, String type) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final timestamp = DateTime.now().toIso8601String();
      
      // Record in analytics
      final analyticsKey = 'notification_analytics_$timestamp';
      final analyticsData = {
        'user_id': userId,
        'bounty_id': bountyId,
        'type': type,
        'timestamp': timestamp,
      };
      
      await prefs.setString(analyticsKey, analyticsData.toString());
      
      DebugLogger.log(_debugPrefix, 
        'Recorded notification: User=$userId, Bounty=$bountyId, Type=$type');
      
    } catch (e, stackTrace) {
      DebugLogger.error(_debugPrefix, 'Error recording notification', e, stackTrace);
    }
  }

  /// Enable or disable auto-scheduling
  Future<void> setAutoScheduleEnabled(bool enabled) async {
    try {
      _autoScheduleEnabled = enabled;
      
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_autoScheduleKey, enabled);
      
      if (enabled && _isInitialized) {
        _startAutoScheduling();
      } else {
        _autoScheduleTimer?.cancel();
      }
      
      DebugLogger.log(_debugPrefix, 'Auto-scheduling ${enabled ? 'enabled' : 'disabled'}');
      
    } catch (e, stackTrace) {
      DebugLogger.error(_debugPrefix, 'Error setting auto-schedule', e, stackTrace);
    }
  }

  /// Set notification interval in hours
  Future<void> setNotificationInterval(int hours) async {
    try {
      _notificationIntervalHours = hours.clamp(1, 168); // 1 hour to 1 week
      
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_notificationIntervalKey, _notificationIntervalHours);
      
      DebugLogger.log(_debugPrefix, 'Notification interval set to ${_notificationIntervalHours}h');
      
    } catch (e, stackTrace) {
      DebugLogger.error(_debugPrefix, 'Error setting notification interval', e, stackTrace);
    }
  }

  /// Get notification manager status
  Map<String, dynamic> getStatus() {
    return {
      'is_initialized': _isInitialized,
      'auto_schedule_enabled': _autoScheduleEnabled,
      'notification_interval_hours': _notificationIntervalHours,
      'auto_schedule_timer_active': _autoScheduleTimer?.isActive ?? false,
    };
  }

  /// Dispose resources
  void dispose() {
    _autoScheduleTimer?.cancel();
    _realNotificationService.dispose();
    DebugLogger.log(_debugPrefix, '🧹 Notification manager disposed');
  }
}
