// No imports needed for this static template service

/// 🎨 Email Template Service
/// 
/// Creates beautiful MOL-styled email templates with rainbow/neon/retro-futurism
/// design. All templates maintain visual consistency with the app's theme.
/// 
/// Features:
/// - MOL visual consistency (rainbow, neon, black, retro-futurism)
/// - Responsive email design
/// - Dark theme optimized
/// - Cross-client compatibility
/// - Dynamic content injection
/// - Template versioning and A/B testing
class EmailTemplateService {
  static final EmailTemplateService _instance = EmailTemplateService._internal();
  factory EmailTemplateService() => _instance;
  EmailTemplateService._internal();

  /// Generate verification email HTML for Klaviyo Flow
  static String generateVerificationEmail({
    required String username,
    required String verificationLink,
    required String magicLink,
    String? firstName,
    bool useKlaviyoVariables = true,
  }) {
    final displayName = firstName ?? username;
    
    // Choose variables based on whether we're using Klaviyo template variables
    final nameVariable = useKlaviyoVariables ? '{{ person.first_name|default:"User" }}' : displayName;
    final magicLinkVariable = useKlaviyoVariables ? '{{ event.magic_link|default:"#" }}' : magicLink;
    final verificationLinkVariable = useKlaviyoVariables ? '{{ event.verification_link|default:"#" }}' : verificationLink;

    return '''
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verify Your MXD Account</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Orbitron', monospace;
            background: linear-gradient(135deg, #000000 0%, #1a0033 50%, #000000 100%);
            color: #ffffff;
            line-height: 1.6;
            padding: 20px;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: rgba(0, 0, 0, 0.9);
            border-radius: 20px;
            border: 2px solid #00ffff;
            box-shadow: 
                0 0 30px rgba(0, 255, 255, 0.3),
                inset 0 0 30px rgba(255, 0, 255, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(45deg, #ff0080, #00ffff, #ff8000, #8000ff);
            background-size: 400% 400%;
            animation: gradientShift 3s ease infinite;
            padding: 30px;
            text-align: center;
            position: relative;
        }
        
        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
        
        .logo {
            font-size: 48px;
            font-weight: 900;
            color: #000000;
            text-shadow: 2px 2px 4px rgba(255, 255, 255, 0.3);
            margin-bottom: 10px;
        }
        
        .tagline {
            font-size: 14px;
            color: #000000;
            font-weight: 700;
            opacity: 0.8;
        }
        
        .content {
            padding: 40px 30px;
            background: rgba(0, 0, 0, 0.95);
        }
        
        .welcome-title {
            font-size: 28px;
            font-weight: 700;
            color: #00ffff;
            text-align: center;
            margin-bottom: 20px;
            text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
        }
        
        .message {
            font-size: 16px;
            color: #ffffff;
            margin-bottom: 30px;
            text-align: center;
            line-height: 1.8;
        }
        
        .username-highlight {
            color: #ff0080;
            font-weight: 700;
            text-shadow: 0 0 5px rgba(255, 0, 128, 0.5);
        }
        
        .cta-container {
            text-align: center;
            margin: 40px 0;
        }
        
        .cta-button {
            display: inline-block;
            padding: 18px 40px;
            background: linear-gradient(45deg, #ff0080, #00ffff);
            color: #000000;
            text-decoration: none;
            font-weight: 700;
            font-size: 18px;
            border-radius: 50px;
            border: 2px solid transparent;
            box-shadow: 
                0 0 20px rgba(0, 255, 255, 0.4),
                inset 0 0 20px rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .cta-button:hover {
            transform: translateY(-2px);
            box-shadow: 
                0 5px 25px rgba(0, 255, 255, 0.6),
                inset 0 0 30px rgba(255, 255, 255, 0.2);
        }
        
        .magic-link-section {
            background: rgba(128, 0, 255, 0.1);
            border: 1px solid #8000ff;
            border-radius: 15px;
            padding: 25px;
            margin: 30px 0;
            text-align: center;
        }
        
        .magic-link-title {
            color: #8000ff;
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 15px;
            text-shadow: 0 0 8px rgba(128, 0, 255, 0.5);
        }
        
        .magic-link-text {
            color: #ffffff;
            font-size: 14px;
            margin-bottom: 20px;
        }
        
        .magic-button {
            display: inline-block;
            padding: 15px 35px;
            background: linear-gradient(45deg, #8000ff, #ff0080);
            color: #ffffff;
            text-decoration: none;
            font-weight: 700;
            font-size: 16px;
            border-radius: 25px;
            box-shadow: 0 0 15px rgba(128, 0, 255, 0.4);
            transition: all 0.3s ease;
        }
        
        .magic-button:hover {
            transform: scale(1.05);
            box-shadow: 0 0 25px rgba(128, 0, 255, 0.6);
        }
        
        .alternative-section {
            margin: 30px 0;
            padding: 20px;
            background: rgba(255, 128, 0, 0.1);
            border: 1px solid #ff8000;
            border-radius: 10px;
        }
        
        .alternative-title {
            color: #ff8000;
            font-size: 16px;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .alternative-text {
            color: #ffffff;
            font-size: 14px;
            margin-bottom: 15px;
        }
        
        .verification-code {
            background: rgba(0, 0, 0, 0.8);
            border: 1px solid #00ffff;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 18px;
            color: #00ffff;
            text-align: center;
            letter-spacing: 3px;
            word-break: break-all;
        }
        
        .footer {
            background: rgba(0, 0, 0, 0.95);
            padding: 30px;
            text-align: center;
            border-top: 1px solid #333333;
        }
        
        .footer-text {
            color: #888888;
            font-size: 12px;
            margin-bottom: 15px;
        }
        
        .social-links {
            margin: 20px 0;
        }
        
        .social-link {
            display: inline-block;
            margin: 0 10px;
            padding: 8px 15px;
            background: rgba(255, 255, 255, 0.1);
            color: #ffffff;
            text-decoration: none;
            border-radius: 20px;
            font-size: 12px;
            transition: all 0.3s ease;
        }
        
        .social-link:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-1px);
        }
        
        .security-notice {
            background: rgba(255, 0, 0, 0.1);
            border: 1px solid #ff0000;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .security-title {
            color: #ff0000;
            font-size: 14px;
            font-weight: 700;
            margin-bottom: 8px;
        }
        
        .security-text {
            color: #ffffff;
            font-size: 12px;
        }
        
        @media (max-width: 600px) {
            .container {
                margin: 10px;
                border-radius: 15px;
            }
            
            .header {
                padding: 20px;
            }
            
            .logo {
                font-size: 36px;
            }
            
            .content {
                padding: 30px 20px;
            }
            
            .welcome-title {
                font-size: 24px;
            }
            
            .cta-button {
                padding: 15px 30px;
                font-size: 16px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">MXD</div>
            <div class="tagline">MAX OUT LIFE</div>
        </div>
        
        <div class="content">
            <h1 class="welcome-title">Welcome to MXD, <span class="username-highlight">$nameVariable</span>!</h1>

            <p class="message">
                You're just one click away from unlocking your MXD Out Life coaches and starting your journey to exponential growth.
                Let's verify your email and get you maxed out! 🚀
            </p>

            <div class="magic-link-section">
                <h2 class="magic-link-title">⚡ One-Click Verification</h2>
                <p class="magic-link-text">
                    Click the button below to instantly verify your account and dive into MXD:
                </p>
                <a href="$magicLinkVariable" class="magic-button">VERIFY & ENTER MXD</a>
            </div>

            <div class="cta-container">
                <a href="$verificationLinkVariable" class="cta-button">Verify My Account</a>
            </div>

            <div class="alternative-section">
                <h3 class="alternative-title">Alternative Verification</h3>
                <p class="alternative-text">
                    If the buttons don't work, copy and paste this link into your browser:
                </p>
                <div class="verification-code">$verificationLinkVariable</div>
            </div>
            
            <div class="security-notice">
                <h4 class="security-title">🔒 Security Notice</h4>
                <p class="security-text">
                    This verification link will expire in 24 hours for your security. 
                    If you didn't create an MXD account, please ignore this email.
                </p>
            </div>
        </div>
        
        <div class="footer">
            <p class="footer-text">
                Welcome to the MXD community! Get ready to max out every area of your life.
            </p>
            
            <div class="social-links">
                <a href="#" class="social-link">Support</a>
                <a href="#" class="social-link">Community</a>
                <a href="#" class="social-link">Privacy</a>
            </div>
            
            <p class="footer-text">
                © 2024 MXD - Max Out Life. All rights reserved.<br>
                This email was sent to verify your account creation.
            </p>
        </div>
    </div>
</body>
</html>
    '''.replaceAll('\$nameVariable', nameVariable)
        .replaceAll('\$verificationLinkVariable', verificationLinkVariable)
        .replaceAll('\$magicLinkVariable', magicLinkVariable);
  }

  /// Generate welcome email after verification
  static String generateWelcomeEmail({
    required String username,
    String? firstName,
    required List<String> assignedCoaches,
  }) {
    final displayName = firstName ?? username;
    final coachList = assignedCoaches.map((coach) => '• $coach').join('\n                ');
    
    return '''
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to MXD - You're In!</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&display=swap');
        
        body {
            font-family: 'Orbitron', monospace;
            background: linear-gradient(135deg, #000000 0%, #1a0033 50%, #000000 100%);
            color: #ffffff;
            line-height: 1.6;
            padding: 20px;
            margin: 0;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: rgba(0, 0, 0, 0.9);
            border-radius: 20px;
            border: 2px solid #00ff00;
            box-shadow: 
                0 0 30px rgba(0, 255, 0, 0.3),
                inset 0 0 30px rgba(0, 255, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(45deg, #00ff00, #00ffff, #ff0080, #ffff00);
            background-size: 400% 400%;
            animation: celebrationGradient 2s ease infinite;
            padding: 30px;
            text-align: center;
        }
        
        @keyframes celebrationGradient {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
        
        .success-icon {
            font-size: 64px;
            margin-bottom: 15px;
        }
        
        .logo {
            font-size: 48px;
            font-weight: 900;
            color: #000000;
            text-shadow: 2px 2px 4px rgba(255, 255, 255, 0.3);
        }
        
        .content {
            padding: 40px 30px;
            background: rgba(0, 0, 0, 0.95);
        }
        
        .welcome-title {
            font-size: 32px;
            font-weight: 700;
            color: #00ff00;
            text-align: center;
            margin-bottom: 20px;
            text-shadow: 0 0 15px rgba(0, 255, 0, 0.5);
        }
        
        .coaches-section {
            background: rgba(0, 255, 255, 0.1);
            border: 1px solid #00ffff;
            border-radius: 15px;
            padding: 25px;
            margin: 30px 0;
        }
        
        .coaches-title {
            color: #00ffff;
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 15px;
            text-align: center;
        }
        
        .coaches-list {
            color: #ffffff;
            font-size: 16px;
            line-height: 2;
            white-space: pre-line;
        }
        
        .cta-button {
            display: inline-block;
            padding: 20px 50px;
            background: linear-gradient(45deg, #00ff00, #00ffff);
            color: #000000;
            text-decoration: none;
            font-weight: 700;
            font-size: 20px;
            border-radius: 50px;
            box-shadow: 0 0 25px rgba(0, 255, 0, 0.5);
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin: 20px 0;
        }
        
        .cta-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 30px rgba(0, 255, 0, 0.7);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="success-icon">🎉</div>
            <div class="logo">MXD</div>
        </div>
        
        <div class="content">
            <h1 class="welcome-title">You're In, $displayName! 🚀</h1>
            
            <p style="text-align: center; font-size: 18px; margin-bottom: 30px;">
                Your email is verified and your MXD journey begins now!
            </p>
            
            <div class="coaches-section">
                <h2 class="coaches-title">Your MXD Coaches Are Ready:</h2>
                <div class="coaches-list">$coachList</div>
            </div>
            
            <div style="text-align: center;">
                <a href="#" class="cta-button">Start Your MXD Journey</a>
            </div>
        </div>
    </div>
</body>
</html>
    '''.replaceAll('\$displayName', displayName)
        .replaceAll('\$coachList', coachList);
  }

  /// Generate password reset email
  static String generatePasswordResetEmail({
    required String username,
    required String resetLink,
    required String magicResetLink,
  }) {
    return '''
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Your MXD Password</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&display=swap');
        
        body {
            font-family: 'Orbitron', monospace;
            background: linear-gradient(135deg, #000000 0%, #330000 50%, #000000 100%);
            color: #ffffff;
            line-height: 1.6;
            padding: 20px;
            margin: 0;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: rgba(0, 0, 0, 0.9);
            border-radius: 20px;
            border: 2px solid #ff0000;
            box-shadow: 
                0 0 30px rgba(255, 0, 0, 0.3),
                inset 0 0 30px rgba(255, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(45deg, #ff0000, #ff8000, #ffff00);
            background-size: 400% 400%;
            animation: warningGradient 3s ease infinite;
            padding: 30px;
            text-align: center;
        }
        
        @keyframes warningGradient {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
        
        .warning-icon {
            font-size: 48px;
            margin-bottom: 10px;
        }
        
        .logo {
            font-size: 36px;
            font-weight: 900;
            color: #000000;
        }
        
        .content {
            padding: 40px 30px;
            background: rgba(0, 0, 0, 0.95);
        }
        
        .reset-title {
            font-size: 28px;
            font-weight: 700;
            color: #ff0000;
            text-align: center;
            margin-bottom: 20px;
            text-shadow: 0 0 10px rgba(255, 0, 0, 0.5);
        }
        
        .reset-button {
            display: inline-block;
            padding: 18px 40px;
            background: linear-gradient(45deg, #ff0000, #ff8000);
            color: #ffffff;
            text-decoration: none;
            font-weight: 700;
            font-size: 18px;
            border-radius: 50px;
            box-shadow: 0 0 20px rgba(255, 0, 0, 0.4);
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .reset-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 25px rgba(255, 0, 0, 0.6);
        }
        
        .security-warning {
            background: rgba(255, 255, 0, 0.1);
            border: 1px solid #ffff00;
            border-radius: 10px;
            padding: 20px;
            margin: 30px 0;
        }
        
        .security-title {
            color: #ffff00;
            font-size: 16px;
            font-weight: 700;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="warning-icon">🔒</div>
            <div class="logo">MXD</div>
        </div>
        
        <div class="content">
            <h1 class="reset-title">Password Reset Request</h1>
            
            <p style="text-align: center; font-size: 16px; margin-bottom: 30px;">
                Hi $username, we received a request to reset your MXD password.
            </p>
            
            <div style="text-align: center; margin: 30px 0;">
                <a href="$magicResetLink" class="reset-button">Reset Password</a>
            </div>
            
            <div class="security-warning">
                <h3 class="security-title">⚠️ Security Notice</h3>
                <p style="color: #ffffff; font-size: 14px;">
                    This reset link expires in 24 hours. If you didn't request this reset, 
                    please ignore this email and your password will remain unchanged.
                </p>
            </div>
        </div>
    </div>
</body>
</html>
    '''.replaceAll('\$username', username)
        .replaceAll('\$resetLink', resetLink)
        .replaceAll('\$magicResetLink', magicResetLink);
  }

  /// Get template metadata for analytics
  static Map<String, dynamic> getTemplateMetadata(String templateType) {
    return {
      'templateType': templateType,
      'version': '1.0',
      'theme': 'MOL_retro_futurism',
      'responsive': true,
      'darkMode': true,
      'generatedAt': DateTime.now().toIso8601String(),
    };
  }
}
