// lib/services/coach_checkin_coordinator.dart

import 'dart:async';
import 'dart:math';
import 'package:flutter/foundation.dart';
import '../models/user_model.dart';
import '../prompts/mxd_life_coaches.dart';
import 'coach_checkin_service.dart';
import 'coach_notification_service.dart';
import 'feature_flag_service.dart';

/// Coordinator service that manages the overall check-in system
/// Handles scheduling, timing, and coordination between services
class CoachCheckinCoordinator {
  static Timer? _checkTimer;
  static bool _isInitialized = false;
  static User? _currentUser;

  /// Initialize the check-in coordinator for a user
  static Future<void> initialize(User user) async {
    try {
      // Clean up any existing resources first
      await dispose();

      _currentUser = user;

      // Initialize notification service
      await CoachNotificationService.initialize();

      // Request notification permissions
      final permissionsGranted = await CoachNotificationService.requestPermissions();
      if (!permissionsGranted) {
        if (kDebugMode) print('🔔 Notification permissions not granted');
        return;
      }

      // Initialize check-in service for user
      await CoachCheckinService.initializeForUser(user);

      // Start the check-in timer
      _startCheckinTimer();

      _isInitialized = true;

      if (kDebugMode) {
        print('🔔 Coach check-in coordinator initialized for ${user.username}');
      }
    } catch (e) {
      if (kDebugMode) print('❌ Failed to initialize check-in coordinator: $e');
    }
  }

  /// Dispose of all resources and stop timers
  ///
  /// This method should be called when the app is backgrounded,
  /// closed, or when switching users to prevent memory leaks.
  static Future<void> dispose() async {
    try {
      // Cancel the check timer
      _checkTimer?.cancel();
      _checkTimer = null;

      // Clear user reference
      _currentUser = null;

      // Mark as not initialized
      _isInitialized = false;

      if (kDebugMode) {
        print('🔔 Coach check-in coordinator disposed');
      }
    } catch (e) {
      if (kDebugMode) print('⚠️ Error disposing check-in coordinator: $e');
    }
  }

  /// Pause check-ins (when app is backgrounded)
  static void pause() {
    _checkTimer?.cancel();
    _checkTimer = null;

    if (kDebugMode) {
      print('⏸️ Coach check-in coordinator paused');
    }
  }

  /// Resume check-ins (when app is foregrounded)
  static void resume() {
    if (_isInitialized && _currentUser != null) {
      _startCheckinTimer();

      if (kDebugMode) {
        print('▶️ Coach check-in coordinator resumed');
      }
    }
  }

  /// Start the periodic check-in timer
  static void _startCheckinTimer() {
    // Cancel existing timer if any
    _checkTimer?.cancel();

    // Get check frequency from feature flags
    final userId = _currentUser?.username ?? 'anonymous';
    final frequencyVariant = FeatureFlagService.getCheckinFrequencyVariant(userId);

    // Determine check interval based on A/B test
    Duration checkInterval;
    switch (frequencyVariant) {
      case 'high_frequency':
        checkInterval = const Duration(minutes: 10); // More frequent checks
        break;
      case 'low_frequency':
        checkInterval = const Duration(minutes: 30); // Less frequent checks
        break;
      default:
        checkInterval = const Duration(minutes: 15); // Standard frequency
    }

    if (kDebugMode) {
      print('🔔 Starting check-in timer with ${checkInterval.inMinutes}min interval (variant: $frequencyVariant)');
    }

    _checkTimer = Timer.periodic(checkInterval, (timer) {
      _processCheckins();
    });

    // Also do an immediate check
    _processCheckins();
  }

  /// Process check-ins for eligible coaches
  static Future<void> _processCheckins() async {
    try {
      if (_currentUser == null) return;
      
      final now = DateTime.now();
      
      // Check if we're within notification window (5:45 AM - 8:45 PM)
      if (!_isWithinNotificationWindow(now)) {
        return;
      }
      
      // Get eligible coaches
      final eligibleCoaches = await CoachCheckinService.getEligibleCoaches();
      
      if (eligibleCoaches.isNotEmpty) {
        // Add some randomization to prevent predictable timing
        final randomDelay = Random().nextInt(300); // 0-5 minutes
        
        await Future.delayed(Duration(seconds: randomDelay));
        
        // Select a random eligible coach
        final selectedCoach = CoachCheckinService.selectRandomEligibleCoach(eligibleCoaches);
        
        if (selectedCoach != null) {
          await CoachNotificationService.sendImmediateCheckin(
            category: selectedCoach['category'],
            coachName: selectedCoach['coachName'],
            username: _currentUser!.username,
            user: _currentUser!,
          );
          
          if (kDebugMode) {
            print('🔔 Sent check-in from ${selectedCoach['coachName']} (${selectedCoach['category']})');
          }
        }
      }
    } catch (e) {
      if (kDebugMode) print('❌ Failed to process check-ins: $e');
    }
  }

  /// Check if current time is within notification window (5:45 AM - 8:45 PM)
  static bool _isWithinNotificationWindow(DateTime time) {
    final hour = time.hour;
    final minute = time.minute;
    
    // Convert to minutes for easier comparison
    final currentMinutes = hour * 60 + minute;
    const startMinutes = 5 * 60 + 45; // 5:45 AM
    const endMinutes = 20 * 60 + 45;  // 8:45 PM
    
    return currentMinutes >= startMinutes && currentMinutes <= endMinutes;
  }

  /// Manually trigger a check-in (for testing)
  static Future<void> triggerManualCheckin() async {
    if (_currentUser == null) {
      if (kDebugMode) print('🔔 No user set for manual check-in');
      return;
    }
    
    await _processCheckins();
  }

  /// Update user (when user data changes)
  static void updateUser(User user) {
    _currentUser = user;
  }

  /// Stop the check-in coordinator
  static void stop() {
    _checkTimer?.cancel();
    _checkTimer = null;
    _isInitialized = false;
    _currentUser = null;
    
    if (kDebugMode) {
      print('🔔 Coach check-in coordinator stopped');
    }
  }

  /// Check if coordinator is running
  static bool get isRunning => _isInitialized && _checkTimer != null;

  /// Get current user
  static User? get currentUser => _currentUser;

  /// Force a check for eligible coaches (for debugging)
  static Future<List<Map<String, dynamic>>> getEligibleCoachesDebug() async {
    return await CoachCheckinService.getEligibleCoaches();
  }

  /// Get coordinator status for debugging
  static Map<String, dynamic> getStatus() {
    final now = DateTime.now();
    return {
      'isInitialized': _isInitialized,
      'isRunning': isRunning,
      'hasUser': _currentUser != null,
      'username': _currentUser?.username,
      'timerActive': _checkTimer?.isActive ?? false,
      'withinNotificationWindow': _isWithinNotificationWindow(now),
      'currentTime': now.toString(),
      'currentHour': now.hour,
      'currentMinute': now.minute,
      'notificationWindow': '5:45 AM - 8:45 PM',
      'nextCheckIn': _checkTimer != null ? 'Timer active' : 'No timer',
      'systemHealth': _isInitialized && _currentUser != null ? 'HEALTHY' : 'DEGRADED',
    };
  }

  /// Enhanced debug method for troubleshooting
  static Future<Map<String, dynamic>> getDetailedDiagnostics() async {
    try {
      final status = getStatus();
      final eligibleCoaches = _currentUser != null
          ? await CoachCheckinService.getEligibleCoaches()
          : <Map<String, dynamic>>[];

      return {
        ...status,
        'eligibleCoaches': eligibleCoaches.length,
        'eligibleCoachDetails': eligibleCoaches,
        'lastError': 'None',
        'diagnosticTime': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      return {
        'error': e.toString(),
        'diagnosticTime': DateTime.now().toIso8601String(),
        'systemHealth': 'ERROR',
      };
    }
  }

  /// Force trigger a test check-in immediately (for testing)
  static Future<void> triggerTestCheckin() async {
    if (_currentUser == null) {
      if (kDebugMode) print('🔔 No user set for test check-in');
      return;
    }

    try {
      // Get all coaches for testing
      final allCoaches = _getAllCoachesForUser(_currentUser!);
      if (allCoaches.isNotEmpty) {
        final randomCoach = allCoaches[Random().nextInt(allCoaches.length)];

        await CoachNotificationService.sendImmediateCheckin(
          category: randomCoach.category,
          coachName: randomCoach.name,
          username: _currentUser!.username,
          user: _currentUser!,
        );

        if (kDebugMode) {
          print('🔔 TEST: Sent check-in from ${randomCoach.name} (${randomCoach.category})');
        }
      }
    } catch (e) {
      if (kDebugMode) print('❌ Failed to trigger test check-in: $e');
    }
  }

  /// Schedule a specific check-in (for testing or manual scheduling)
  static Future<void> scheduleCheckin({
    required String category,
    required String coachName,
    required DateTime scheduledTime,
  }) async {
    if (_currentUser == null) {
      if (kDebugMode) print('🔔 No user set for scheduled check-in');
      return;
    }
    
    await CoachNotificationService.scheduleCheckinNotification(
      category: category,
      coachName: coachName,
      username: _currentUser!.username,
      user: _currentUser!,
      scheduledTime: scheduledTime,
    );
  }

  /// Cancel all scheduled notifications
  static Future<void> cancelAllNotifications() async {
    await CoachNotificationService.cancelAllNotifications();
  }

  /// Send welcome check-ins for new users
  static Future<void> sendWelcomeCheckins(User user) async {
    try {
      // Wait a bit after onboarding completion
      await Future.delayed(const Duration(minutes: 5));
      
      // Send welcome check-ins from 2-3 random coaches
      final allCoaches = _getAllCoachesForUser(user);
      final shuffledCoaches = List.from(allCoaches)..shuffle();
      final welcomeCoaches = shuffledCoaches.take(2).toList();
      
      for (int i = 0; i < welcomeCoaches.length; i++) {
        final coach = welcomeCoaches[i];
        
        // Stagger the welcome messages by 10-30 minutes
        final delay = Duration(minutes: 10 + (i * 20) + Random().nextInt(20));
        
        Timer(delay, () async {
          await CoachNotificationService.sendImmediateCheckin(
            category: coach.category,
            coachName: coach.name,
            username: user.username,
            user: user,
          );
        });
      }
      
      if (kDebugMode) {
        print('🔔 Scheduled ${welcomeCoaches.length} welcome check-ins for ${user.username}');
      }
    } catch (e) {
      if (kDebugMode) print('❌ Failed to send welcome check-ins: $e');
    }
  }

  /// Get all coaches for a user
  static List<({String category, String name})> _getAllCoachesForUser(User user) {
    final coaches = <({String category, String name})>[];
    
    // Add core coaches (Health, Wealth, Purpose, Connection)
    final coreCategories = ['Health', 'Wealth', 'Purpose', 'Connection'];
    for (final category in coreCategories) {
      String effectiveGender = user.gender;
      if (user.gender.toLowerCase() == 'non-gender' && user.assignedCoaches != null) {
        effectiveGender = user.assignedCoaches![category] ?? 'male';
      }
      
      // Get coach name based on category and gender using authoritative source
      String coachName;
      final coach = mxdLifeCoaches.firstWhere(
        (c) => c.category.toLowerCase() == category.toLowerCase(),
        orElse: () => mxdLifeCoaches.first,
      );
      coachName = effectiveGender.toLowerCase() == 'male' ? coach.maleName : coach.femaleName;
      
      coaches.add((category: category, name: coachName));
    }
    
    // Add custom category coaches
    for (int i = 0; i < user.customCategories.length; i++) {
      final categoryKey = 'Custom Category ${i + 1}';

      String effectiveGender = user.gender;
      if (user.gender.toLowerCase() == 'non-gender' && user.assignedCoaches != null) {
        effectiveGender = user.assignedCoaches![categoryKey] ?? 'male';
      }

      String coachName;
      if (i == 0) {
        // Custom Category 1: Aether/Luna
        coachName = effectiveGender.toLowerCase() == 'male' ? 'Aether' : 'Luna';
      } else {
        // Custom Category 2: Chronos/Elysia
        coachName = effectiveGender.toLowerCase() == 'male' ? 'Chronos' : 'Elysia';
      }

      coaches.add((category: categoryKey, name: coachName));
    }
    
    return coaches;
  }
}
