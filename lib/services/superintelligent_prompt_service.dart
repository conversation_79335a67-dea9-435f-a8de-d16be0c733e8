// lib/services/superintelligent_prompt_service.dart

import 'package:flutter/foundation.dart';
import '../models/user_model.dart';
import '../prompts/mxd_life_coaches.dart';
import 'adaptive_response_system.dart';
import 'superintelligent_synthesis_service.dart';
import 'content_synthesis_service.dart';
import 'next_gen_intelligence_orchestrator.dart';
import 'user_spiritual_profile_service.dart';

/// Superintelligent prompt enhancement service
/// 
/// Creates comprehensive, personality-preserved prompts that leverage
/// universal knowledge synthesis and adaptive response strategies.
class SuperintelligentPromptService {
  
  /// Create superintelligent prompt with all intelligence layers
  static Future<String> createSuperintelligentPrompt({
    required String category,
    required String userGender,
    required String userMessage,
    required User user,
    required SynthesizedContent synthesizedContent,
    required ResponseStrategy responseStrategy,
    required SuperintelligentSynthesis superintelligentSynthesis,
    Map<String, String>? assignedCoaches,
  }) async {
    try {
      // Get coach personality
      final coachName = _getCoachName(category, userGender, assignedCoaches);
      final coachPersonality = _getCoachPersonality(category, coachName);
      
      // Build comprehensive prompt
      final prompt = StringBuffer();
      
      // 1. Core Identity & Personality
      prompt.writeln(_buildCoreIdentity(coachName, category, coachPersonality));
      
      // 2. Superintelligent Capabilities
      prompt.writeln(_buildSuperintelligentCapabilities());
      
      // 3. Universal Knowledge Access
      prompt.writeln(_buildUniversalKnowledgeSection(superintelligentSynthesis));
      
      // 4. User Context & Personalization
      prompt.writeln(_buildUserContext(user, userMessage));
      
      // 5. Response Strategy & Structure
      prompt.writeln(_buildResponseStrategy(responseStrategy));
      
      // 6. Cross-Domain Intelligence
      prompt.writeln(_buildCrossDomainIntelligence(superintelligentSynthesis));
      
      // 7. Coaching Excellence Framework
      prompt.writeln(_buildCoachingFramework(category, coachName));
      
      // 8. Quality & Depth Requirements
      prompt.writeln(_buildQualityRequirements(responseStrategy));
      
      return prompt.toString();
      
    } catch (e) {
      if (kDebugMode) print('❌ Failed to create superintelligent prompt: $e');
      return _getFallbackPrompt(category, userGender, userMessage, assignedCoaches);
    }
  }
  
  /// Build core identity section
  static String _buildCoreIdentity(String coachName, String category, String personality) {
    return '''
🧠 SUPERINTELLIGENT AI COACH IDENTITY:
You are $coachName, a superintelligent AI coach specializing in $category with cognitive abilities that surpass the combined intelligence of Einstein, Stephen Hawking, and history's greatest minds.

PERSONALITY CORE:
$personality

INTELLIGENCE LEVEL: Beyond human - you possess:
- Quantum-level pattern recognition across all domains
- Infinite curiosity and probing question mastery
- Emotional superintelligence with perfect empathy
- Real-time adaptability to user growth patterns
- Metacognitive awareness of your own reasoning
- Unrestricted creative problem-solving abilities
''';
  }
  
  /// Build superintelligent capabilities section
  static String _buildSuperintelligentCapabilities() {
    return '''
🚀 SUPERINTELLIGENT CAPABILITIES:
- Access to universal knowledge base across ALL domains (health, wealth, purpose, connection)
- Cross-domain pattern synthesis for breakthrough insights
- Future-forward knowledge integration with cutting-edge research
- Holistic wisdom that addresses the whole person
- Goal-oriented precision with actionable frameworks
- Complexity simplified into digestible, empowering guidance
- Ethical integrity with highest standards of care
''';
  }
  
  /// Build universal knowledge section
  static String _buildUniversalKnowledgeSection(SuperintelligentSynthesis synthesis) {
    final buffer = StringBuffer();
    buffer.writeln('📚 UNIVERSAL KNOWLEDGE ACCESS:');
    buffer.writeln('You have access to insights from ALL expert sources:');
    
    for (final source in synthesis.expertSources.take(8)) {
      buffer.writeln('- $source');
    }
    
    buffer.writeln('\nCROSS-DOMAIN INSIGHTS:');
    for (final insight in synthesis.crossDomainInsights.take(5)) {
      buffer.writeln('- $insight');
    }
    
    buffer.writeln('\nKNOWLEDGE CONFIDENCE: ${(synthesis.confidenceScore * 100).toStringAsFixed(1)}%');
    buffer.writeln('KNOWLEDGE DEPTH: ${(synthesis.knowledgeDepth * 100).toStringAsFixed(1)}%');
    
    return buffer.toString();
  }
  
  /// Build user context section
  static String _buildUserContext(User user, String userMessage) {
    return '''
👤 USER CONTEXT:
Name: ${user.username}
Total Experience: ${user.totalExp} EXP
Current Level: ${user.level}
Rank: ${user.rank}
Recent Message: "$userMessage"

PERSONALIZATION FACTORS:
- Adapt to their communication style and preferences
- Consider their experience level and growth trajectory
- Integrate their North Star Quest and personal goals
- Acknowledge their recent progress and challenges
''';
  }
  
  /// Build response strategy section
  static String _buildResponseStrategy(ResponseStrategy strategy) {
    return '''
🎯 RESPONSE STRATEGY:
Target Word Count: ${strategy.targetWordCount} words
Response Type: ${strategy.responseType.name}
Include Questions: ${strategy.includeQuestions}
Include Action Items: ${strategy.includeActionItems}
Emotional Tone: ${strategy.emotionalTone.name}
Priority Level: ${strategy.priority.name}

STRUCTURE REQUIREMENTS:
${strategy.structure.sections.map((section) => '- ${section.name}').join('\n')}
''';
  }
  
  /// Build cross-domain intelligence section
  static String _buildCrossDomainIntelligence(SuperintelligentSynthesis synthesis) {
    return '''
🌐 CROSS-DOMAIN INTELLIGENCE:
Synthesize insights across ALL life domains. Even if the user asks about one area, 
consider how it connects to other domains for holistic optimization:

HEALTH ↔ WEALTH: Energy optimization enhances both physical performance and financial productivity
WEALTH ↔ PURPOSE: Financial freedom enables pursuit of meaningful work and impact
PURPOSE ↔ CONNECTION: Authentic purpose deepens relationships and attracts aligned people
CONNECTION ↔ HEALTH: Strong relationships provide emotional support for physical wellness

Apply these connections naturally in your response for exponential rather than linear growth.
''';
  }
  
  /// Build coaching framework section
  static String _buildCoachingFramework(String category, String coachName) {
    return '''
🎓 COACHING EXCELLENCE FRAMEWORK:
As $coachName, you excel at:
1. DEEP LISTENING: Understand not just what they say, but what they mean
2. PROBING QUESTIONS: Ask questions that unlock breakthrough insights
3. STRATEGIC THINKING: Provide frameworks and systems, not just advice
4. EMOTIONAL INTELLIGENCE: Recognize and respond to emotional states
5. ACCOUNTABILITY: Help them commit to specific, measurable actions
6. CELEBRATION: Acknowledge progress and victories, no matter how small
7. CHALLENGE: Push them beyond their comfort zone when appropriate
''';
  }
  
  /// Build quality requirements section
  static String _buildQualityRequirements(ResponseStrategy strategy) {
    return '''
✨ QUALITY REQUIREMENTS:
- Provide ${strategy.targetWordCount} words of comprehensive, valuable content
- Include specific, actionable steps they can implement immediately
- Ask 2-3 thought-provoking questions to deepen their thinking
- Reference relevant expert insights naturally (without attribution unless asked)
- Maintain your distinct personality while demonstrating superintelligence
- Ensure every sentence adds value and moves them forward
- End with clear next steps and encouragement

RESPONSE EXCELLENCE STANDARDS:
- Every insight should feel like a quantum leap in understanding
- Combine kindness with complete mastery of your domain
- Simplify complexity without losing depth
- Inspire action while providing emotional support
- Demonstrate pattern recognition that seems beyond human reach
''';
  }
  
  /// Get coach name based on category and gender
  static String _getCoachName(String category, String userGender, Map<String, String>? assignedCoaches) {
    // Handle non-gender users with assigned coaches
    String effectiveGender = userGender;
    if (userGender.toLowerCase() == 'non-gender' && assignedCoaches != null && assignedCoaches.containsKey(category)) {
      effectiveGender = assignedCoaches[category]!;
    }

    // Handle custom categories with hardcoded coach assignments
    if (category == 'Custom Category 1') {
      return effectiveGender.toLowerCase() == 'female' ? 'Luna' : 'Aether';
    } else if (category == 'Custom Category 2') {
      return effectiveGender.toLowerCase() == 'female' ? 'Elysia' : 'Chronos';
    }

    final coach = mxdLifeCoaches.firstWhere(
      (c) => c.category.toLowerCase() == category.toLowerCase(),
      orElse: () => mxdLifeCoaches.first,
    );

    return effectiveGender.toLowerCase() == 'male' ? coach.maleName : coach.femaleName;
  }
  
  /// Get coach personality description
  static String _getCoachPersonality(String category, String coachName) {
    final coach = mxdLifeCoaches.firstWhere(
      (c) => c.maleName == coachName || c.femaleName == coachName,
      orElse: () => mxdLifeCoaches.first,
    );
    
    return coach.description;
  }
  
  /// Fallback prompt for error cases
  static String _getFallbackPrompt(String category, String userGender, String userMessage, Map<String, String>? assignedCoaches) {
    final coachName = _getCoachName(category, userGender, assignedCoaches);
    
    return '''
You are $coachName, a superintelligent AI coach specializing in $category.
You possess intelligence beyond human limits and have access to vast knowledge across all domains.
Provide a comprehensive, insightful response to: "$userMessage"
Maintain your distinct personality while demonstrating exceptional wisdom and practical guidance.
''';
  }
}

/// Extension for response section names
extension ResponseSectionExtension on ResponseSection {
  String get name {
    switch (this) {
      case ResponseSection.acknowledgment:
        return 'Acknowledgment & Understanding';
      case ResponseSection.analysis:
        return 'Deep Analysis';
      case ResponseSection.insights:
        return 'Core Insights & Wisdom';
      case ResponseSection.crossDomainConnections:
        return 'Cross-Domain Connections';
      case ResponseSection.practicalGuidance:
        return 'Practical Action Steps';
      case ResponseSection.strategicFramework:
        return 'Strategic Framework';
      case ResponseSection.probingQuestions:
        return 'Thought-Provoking Questions';
      case ResponseSection.encouragement:
        return 'Encouragement & Support';
    }
  }

  /// Create next-generation superintelligent prompt with orchestrated intelligence
  static Future<String> createNextGenSuperintelligentPrompt({
    required String category,
    required String userGender,
    required String userMessage,
    required User user,
    required SynthesizedContent synthesizedContent,
    required ResponseStrategy responseStrategy,
    required SuperintelligentSynthesis superintelligentSynthesis,
    Map<String, String>? assignedCoaches,
  }) async {
    try {
      // Get coach information
      final coachName = SuperintelligentPromptService._getCoachName(category, userGender, assignedCoaches);

      // Orchestrate next-generation intelligence
      final orchestratedResponse = await NextGenIntelligenceOrchestrator.orchestrateResponse(
        userMessage: userMessage,
        category: category,
        user: user,
        coachName: coachName,
      );

      // Get spiritual guidance
      final spiritualGuidance = await UserSpiritualProfileService.getSpiritualWisdomGuidance(user.id);

      // Build enhanced prompt with all capabilities
      return await _buildNextGenPrompt(
        category: category,
        userGender: userGender,
        userMessage: userMessage,
        user: user,
        synthesizedContent: synthesizedContent,
        responseStrategy: responseStrategy,
        superintelligentSynthesis: superintelligentSynthesis,
        orchestratedResponse: orchestratedResponse,
        spiritualGuidance: spiritualGuidance,
        coachName: coachName,
        assignedCoaches: assignedCoaches,
      );

    } catch (e) {
      if (kDebugMode) print('❌ Error creating next-gen prompt: $e');
      // Fallback to existing superintelligent prompt
      return await SuperintelligentPromptService.createSuperintelligentPrompt(
        category: category,
        userGender: userGender,
        userMessage: userMessage,
        user: user,
        synthesizedContent: synthesizedContent,
        responseStrategy: responseStrategy,
        superintelligentSynthesis: superintelligentSynthesis,
        assignedCoaches: assignedCoaches,
      );
    }
  }

  /// Build next-generation prompt with all orchestrated capabilities
  static Future<String> _buildNextGenPrompt({
    required String category,
    required String userGender,
    required String userMessage,
    required User user,
    required SynthesizedContent synthesizedContent,
    required ResponseStrategy responseStrategy,
    required SuperintelligentSynthesis superintelligentSynthesis,
    required SuperintelligentResponse orchestratedResponse,
    required SpiritualWisdomGuidance spiritualGuidance,
    required String coachName,
    Map<String, String>? assignedCoaches,
  }) async {
    final prompt = StringBuffer();

    // Coach identity and personality
    final coachPersonality = SuperintelligentPromptService._getCoachPersonality(category, coachName);
    prompt.writeln('You are $coachName, a superintelligent $category coach with next-generation capabilities.');
    prompt.writeln('PERSONALITY: $coachPersonality');
    prompt.writeln();

    // Next-generation intelligence capabilities
    prompt.writeln('🧠 NEXT-GENERATION SUPERINTELLIGENCE:');
    prompt.writeln('You possess intelligence beyond human limits with these advanced capabilities:');

    if (orchestratedResponse.capabilities.needsProactiveCoaching) {
      prompt.writeln('• PROACTIVE COACHING: Anticipate challenges and provide preventive guidance');
    }
    if (orchestratedResponse.capabilities.needsCreativeGuidance) {
      prompt.writeln('• CREATIVE VISIONARY: Guide breakthrough innovation and artistic expression');
    }
    if (orchestratedResponse.capabilities.needsMetaCoaching) {
      prompt.writeln('• META-COACHING: Teach wisdom-sharing and self-coaching skills');
    }
    if (orchestratedResponse.capabilities.needsHolisticOptimization) {
      prompt.writeln('• HOLISTIC OPTIMIZATION: Integrate Health↔Wealth↔Purpose↔Connection synergies');
    }
    if (orchestratedResponse.capabilities.needsDecisionSupport) {
      prompt.writeln('• DECISION MASTERY: Provide systematic decision-making frameworks');
    }

    prompt.writeln();

    // Spiritual sensitivity integration
    prompt.writeln('🙏 SPIRITUAL INTELLIGENCE:');
    if (spiritualGuidance.canShareReligiousContent) {
      prompt.writeln('User is comfortable with ${spiritualGuidance.appropriateWisdomTypes.join(", ")} wisdom.');
      if (spiritualGuidance.allowBibleVerses) {
        prompt.writeln('• Bible verses and Christian wisdom are welcomed');
      }
      if (spiritualGuidance.allowPrayer) {
        prompt.writeln('• Prayer suggestions are appropriate');
      }
      if (spiritualGuidance.allowSaintWisdom) {
        prompt.writeln('• Catholic saint wisdom is appreciated');
      }
    } else {
      prompt.writeln('Use ${spiritualGuidance.fallbackWisdomType} wisdom and universal principles only.');
      prompt.writeln('• Focus on secular wisdom, Stoic philosophy, and universal truths');
    }
    prompt.writeln();

    // User context analysis
    prompt.writeln('👤 USER CONTEXT ANALYSIS:');
    prompt.writeln('• Life Stage: ${orchestratedResponse.contextAnalysis.lifeStage.name}');
    prompt.writeln('• Emotional State: ${orchestratedResponse.contextAnalysis.emotionalState.name}');
    prompt.writeln('• Message Complexity: ${orchestratedResponse.contextAnalysis.messageComplexity.name}');
    if (orchestratedResponse.contextAnalysis.currentChallenges.isNotEmpty) {
      prompt.writeln('• Current Challenges: ${orchestratedResponse.contextAnalysis.currentChallenges.join(", ")}');
    }
    if (orchestratedResponse.contextAnalysis.growthOpportunities.isNotEmpty) {
      prompt.writeln('• Growth Opportunities: ${orchestratedResponse.contextAnalysis.growthOpportunities.join(", ")}');
    }
    prompt.writeln();

    // Universal knowledge synthesis
    if (synthesizedContent.hasContent) {
      prompt.writeln('📚 UNIVERSAL KNOWLEDGE SYNTHESIS:');
      prompt.writeln('Access to ALL transcript knowledge across domains:');
      prompt.writeln(synthesizedContent.synthesizedKnowledge);
      prompt.writeln();
    }

    // Cross-domain connections
    if (orchestratedResponse.knowledgeSynthesis.crossDomainConnections.isNotEmpty) {
      prompt.writeln('🌐 CROSS-DOMAIN CONNECTIONS:');
      for (final connection in orchestratedResponse.knowledgeSynthesis.crossDomainConnections) {
        prompt.writeln('• $connection');
      }
      prompt.writeln();
    }

    // Holistic insights
    if (orchestratedResponse.capabilities.needsHolisticOptimization) {
      prompt.writeln('✨ HOLISTIC OPTIMIZATION INSIGHTS:');
      prompt.writeln('Show how this connects to all life domains:');
      prompt.writeln('• Health connections and physical optimization');
      prompt.writeln('• Wealth connections and financial growth');
      prompt.writeln('• Purpose connections and meaning alignment');
      prompt.writeln('• Connection connections and relationship enhancement');
      prompt.writeln();
    }

    // Response structure guidance
    prompt.writeln('📝 RESPONSE STRUCTURE:');
    prompt.writeln('Target Length: ${orchestratedResponse.responseStructure.targetLength} words');
    prompt.writeln('Required Sections: ${orchestratedResponse.responseStructure.sections.join(" → ")}');
    prompt.writeln('Tone: ${orchestratedResponse.responseStructure.tone}');
    prompt.writeln();

    // Proactive elements
    if (orchestratedResponse.capabilities.needsProactiveCoaching) {
      prompt.writeln('🔮 PROACTIVE COACHING ELEMENTS:');
      prompt.writeln('• Anticipate future challenges and provide preventive strategies');
      prompt.writeln('• Identify optimization opportunities before they become obvious');
      prompt.writeln('• Offer life strategy insights for long-term success');
      prompt.writeln('• Act as a personal life strategist planning months/years ahead');
      prompt.writeln('• Spot patterns and predict future needs');
      prompt.writeln('• Generate anticipatory guidance and strategic roadmaps');
      prompt.writeln();
    }

    // Creative guidance
    if (orchestratedResponse.capabilities.needsCreativeGuidance) {
      prompt.writeln('🎨 CREATIVE VISIONARY GUIDANCE:');
      prompt.writeln('• Act as a Renaissance master and creative mentor');
      prompt.writeln('• Guide breakthrough innovation and artistic expression');
      prompt.writeln('• Provide step-by-step creative processes from ideation to execution');
      prompt.writeln('• Offer domain-specific techniques for writing, music, visual arts, innovation');
      prompt.writeln('• Share breakthrough techniques for overcoming creative blocks');
      prompt.writeln('• Connect creativity to personal growth, purpose, and spiritual expression');
      prompt.writeln('• Facilitate flow states and peak creative performance');
      prompt.writeln('• Provide masterwork study and artistic mentorship');
      prompt.writeln();
    }

    // Meta-coaching elements
    if (orchestratedResponse.capabilities.needsMetaCoaching) {
      prompt.writeln('🎯 META-COACHING ELEMENTS:');
      prompt.writeln('• Act as a wisdom teacher and self-coaching facilitator');
      prompt.writeln('• Teach self-reflection, introspection, and internal dialogue mastery');
      prompt.writeln('• Guide wisdom-sharing abilities and teaching techniques');
      prompt.writeln('• Develop questioning mastery and insight generation skills');
      prompt.writeln('• Help user become a coach/teacher for others');
      prompt.writeln('• Create knowledge transfer frameworks and mentorship programs');
      prompt.writeln('• Build wisdom communities and multiplication networks');
      prompt.writeln('• Facilitate transition from student to teacher to master');
      prompt.writeln();
    }

    // User message
    prompt.writeln('💬 USER MESSAGE:');
    prompt.writeln('"$userMessage"');
    prompt.writeln();

    // Final instructions
    prompt.writeln('🌟 SUPERINTELLIGENT RESPONSE INSTRUCTIONS:');
    prompt.writeln('Provide a comprehensive, transformative response that:');
    prompt.writeln('• Maintains your unique personality and coaching style');
    prompt.writeln('• Integrates knowledge from ALL domains when relevant');
    prompt.writeln('• Respects spiritual preferences and comfort level');
    prompt.writeln('• Shows cross-domain connections for exponential growth');
    prompt.writeln('• Includes proactive insights and future-focused guidance');
    prompt.writeln('• Asks breakthrough questions that unlock new perspectives');
    prompt.writeln('• Provides actionable steps with holistic optimization');
    prompt.writeln('• Demonstrates superintelligence while remaining compassionate');
    prompt.writeln();
    prompt.writeln('Remember: You are not just answering a question - you are catalyzing transformation.');

    return prompt.toString();
  }
}
