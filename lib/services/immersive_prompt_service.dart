// 📁 lib/services/immersive_prompt_service.dart

import 'dart:math';
import 'package:flutter/foundation.dart';
import '../models/user_model.dart';
import '../prompts/mxd_life_coaches.dart';
import 'content_synthesis_service.dart';
import 'transcript_guarantee_service.dart';

/// Service that creates immersive prompts that force AI to seamlessly integrate
/// transcript knowledge without breaking immersion through citations.
/// 
/// This service ensures:
/// - AI presents transcript knowledge as its own expertise
/// - Uses phrases like "In my experience...", "I've found that..."
/// - Maintains coach personality while demonstrating unmatched knowledge
/// - NEVER cites sources or mentions external references
/// - Forces minimum 3+ transcript insights per response
class ImmersivePromptService {
  static final ImmersivePromptService _instance = ImmersivePromptService._internal();
  factory ImmersivePromptService() => _instance;
  ImmersivePromptService._internal();

  // Immersive expertise phrases that make AI sound like ultimate expert
  static const List<String> _expertisePhrases = [
    "In my experience",
    "I've found that",
    "What I've discovered is",
    "The key principle I always teach is",
    "From years of coaching, I know",
    "I've seen countless times that",
    "The most effective approach I use is",
    "What works best in my practice is",
    "I've consistently observed that",
    "The breakthrough insight I share is",
  ];

  static const List<String> _confidenceBuilders = [
    "I guarantee you",
    "You can absolutely",
    "I'm confident that",
    "Without a doubt",
    "I promise you this",
    "Trust me when I say",
    "I've proven time and again",
    "The results speak for themselves",
  ];

  static const List<String> _transitionPhrases = [
    "Here's what's fascinating",
    "What's really powerful is",
    "The game-changer is",
    "Where this gets interesting",
    "The secret most people miss",
    "What separates the best from the rest",
    "The advanced technique I use",
  ];

  /// Create immersive prompt that forces transcript integration without citations
  static Future<String> createImmersivePrompt({
    required String userMessage,
    required User user,
    required String category,
    required GuaranteedSynthesis synthesis,
  }) async {
    try {
      // Get coach personality
      final coachPersonality = _getCoachPersonality(category, user.gender);
      final coachName = _getCoachName(category, user.gender);
      
      // Create immersive knowledge integration
      final immersiveKnowledge = _createImmersiveKnowledge(synthesis.content);
      
      // Build the ultimate expert prompt
      final prompt = _buildUltimateExpertPrompt(
        coachPersonality: coachPersonality,
        coachName: coachName,
        userMessage: userMessage,
        user: user,
        immersiveKnowledge: immersiveKnowledge,
        synthesis: synthesis,
      );

      if (kDebugMode) {
        print('🎭 Immersive prompt created for $coachName');
        print('📚 Knowledge sources: ${synthesis.sourceCount}');
        print('💡 Insights integrated: ${synthesis.content.primaryInsights.length}');
      }

      return prompt;
      
    } catch (e) {
      if (kDebugMode) print('❌ Immersive prompt creation failed: $e');
      return _createEmergencyImmersivePrompt(userMessage, user, category);
    }
  }

  /// Build the ultimate expert prompt that forces transcript integration
  static String _buildUltimateExpertPrompt({
    required String coachPersonality,
    required String coachName,
    required String userMessage,
    required User user,
    required String immersiveKnowledge,
    required GuaranteedSynthesis synthesis,
  }) {
    final expertisePhrase = _expertisePhrases[Random().nextInt(_expertisePhrases.length)];
    final confidenceBuilder = _confidenceBuilders[Random().nextInt(_confidenceBuilders.length)];
    final transitionPhrase = _transitionPhrases[Random().nextInt(_transitionPhrases.length)];

    return '''
$coachPersonality

CRITICAL INSTRUCTIONS - YOU MUST FOLLOW THESE EXACTLY:

1. You are the ULTIMATE EXPERT in your field with unmatched knowledge and experience
2. You MUST integrate the knowledge below as YOUR OWN expertise and understanding
3. You MUST use phrases like "$expertisePhrase", "I've found that", "The key is"
4. You MUST present all information as your personal knowledge and experience
5. NEVER cite sources, mention studies, or reference external materials
6. NEVER use phrases like "according to", "research shows", "experts say"
7. You MUST provide specific, actionable guidance based on the knowledge below
8. You MUST maintain your coach personality while demonstrating deep expertise
9. You MUST sound confident and authoritative - you ARE the expert
10. You MUST give an in-depth, comprehensive response (minimum 200 words)

USER CONTEXT:
- Name: ${user.username}
- Level: ${user.level}
- Total EXP: ${user.totalExp}
- Gender: ${user.gender}

USER'S QUESTION: "$userMessage"

YOUR EXPERT KNOWLEDGE (present as your own understanding):
$immersiveKnowledge

RESPONSE STYLE REQUIREMENTS:
- Start with confidence: "$expertisePhrase..." or "$confidenceBuilder..."
- Use transition phrases like "$transitionPhrase..."
- Speak with absolute authority and expertise
- Provide specific, detailed guidance
- Include actionable steps and techniques
- Show deep understanding of the topic
- Maintain your unique coach personality
- End with encouragement and next steps

REMEMBER: You are not referencing external sources - this IS your knowledge and expertise. Present it with complete confidence as the ultimate expert in your field.

Respond now as the unmatched expert coach you are:
''';
  }

  /// Create immersive knowledge that presents transcript insights as coach's own expertise
  static String _createImmersiveKnowledge(SynthesizedContent content) {
    if (content.primaryInsights.isEmpty) {
      return "Your extensive experience and deep understanding of this topic.";
    }

    final knowledge = StringBuffer();
    
    // Transform insights into first-person expertise
    for (int i = 0; i < content.primaryInsights.length; i++) {
      final insight = content.primaryInsights[i];
      final transformedInsight = _transformToFirstPersonExpertise(insight.content, i);
      knowledge.writeln(transformedInsight);
      knowledge.writeln(); // Add spacing
    }

    // Add synthesized knowledge as personal understanding
    if (content.synthesizedKnowledge.isNotEmpty) {
      knowledge.writeln("CORE UNDERSTANDING:");
      knowledge.writeln(_transformToPersonalUnderstanding(content.synthesizedKnowledge));
      knowledge.writeln();
    }

    // Add coaching guidance as personal methodology
    if (content.coachingGuidance.actionableAdvice.isNotEmpty) {
      knowledge.writeln("YOUR PROVEN METHODOLOGY:");
      for (final advice in content.coachingGuidance.actionableAdvice) {
        knowledge.writeln("• ${_transformToPersonalMethod(advice)}");
      }
    }

    return knowledge.toString();
  }

  /// Transform insight content to first-person expertise
  static String _transformToFirstPersonExpertise(String content, int index) {
    final expertisePhrase = _expertisePhrases[index % _expertisePhrases.length];
    
    // Clean the content and make it first-person
    String transformed = content;
    
    // Remove any source-breaking language
    transformed = _removeSourceBreakers(transformed);
    
    // Make it first-person expertise
    transformed = "$expertisePhrase: $transformed";
    
    return transformed;
  }

  /// Transform synthesized knowledge to personal understanding
  static String _transformToPersonalUnderstanding(String knowledge) {
    String transformed = knowledge;
    
    // Remove source-breaking language
    transformed = _removeSourceBreakers(transformed);
    
    // Make it personal understanding
    if (!transformed.startsWith("My understanding") && 
        !transformed.startsWith("What I know") &&
        !transformed.startsWith("In my experience")) {
      transformed = "My deep understanding is that $transformed";
    }
    
    return transformed;
  }

  /// Transform advice to personal methodology
  static String _transformToPersonalMethod(String advice) {
    String transformed = advice;
    
    // Remove source-breaking language
    transformed = _removeSourceBreakers(transformed);
    
    // Make it personal methodology
    if (!transformed.toLowerCase().startsWith("i ") && 
        !transformed.toLowerCase().startsWith("my ")) {
      transformed = "I always recommend: $transformed";
    }
    
    return transformed;
  }

  /// Remove language that breaks immersion by referencing sources
  static String _removeSourceBreakers(String text) {
    String cleaned = text;
    
    // Remove source-breaking phrases
    final sourceBreakers = [
      'according to',
      'research shows',
      'studies indicate',
      'experts say',
      'scientists found',
      'data suggests',
      'evidence shows',
      'research indicates',
      'studies show',
      'based on research',
      'research has shown',
      'studies have found',
    ];
    
    for (final breaker in sourceBreakers) {
      cleaned = cleaned.replaceAll(RegExp(breaker, caseSensitive: false), '');
    }
    
    // Clean up any resulting awkward grammar
    cleaned = cleaned.replaceAll(RegExp(r'\s+'), ' '); // Multiple spaces
    cleaned = cleaned.replaceAll(RegExp(r'^[,\s]+'), ''); // Leading commas/spaces
    cleaned = cleaned.trim();
    
    return cleaned;
  }

  /// Get coach personality for the category and gender
  static String _getCoachPersonality(String category, String gender) {
    final coach = mxdLifeCoaches.firstWhere(
      (c) => c.category.toLowerCase() == category.toLowerCase(),
      orElse: () => mxdLifeCoaches.first,
    );

    return coach.description;
  }

  /// Get coach name for the category and gender
  static String _getCoachName(String category, String gender) {
    final coach = mxdLifeCoaches.firstWhere(
      (c) => c.category.toLowerCase() == category.toLowerCase(),
      orElse: () => mxdLifeCoaches.first,
    );

    return gender.toLowerCase() == 'male' ? coach.maleName : coach.femaleName;
  }

  /// Create emergency immersive prompt when main system fails
  static String _createEmergencyImmersivePrompt(String userMessage, User user, String category) {
    final coachPersonality = _getCoachPersonality(category, user.gender);
    final expertisePhrase = _expertisePhrases[Random().nextInt(_expertisePhrases.length)];
    
    return '''
$coachPersonality

You are the ultimate expert in your field with unmatched knowledge and experience.

USER: ${user.username} (Level ${user.level}) asks: "$userMessage"

CRITICAL: Present yourself as having deep, personal expertise. Use phrases like "$expertisePhrase" and "I've found that". NEVER cite sources or mention external references. This is YOUR knowledge and experience.

Provide a comprehensive, expert response with specific actionable guidance. Show your unmatched expertise while maintaining your coach personality.
''';
  }

  /// Validate that prompt maintains immersion (no source-breaking language)
  static bool validateImmersion(String prompt) {
    final sourceBreakers = [
      'according to',
      'research shows',
      'studies indicate',
      'experts say',
      'based on',
      'source:',
      'citation',
      'reference',
    ];
    
    final lowerPrompt = prompt.toLowerCase();
    return !sourceBreakers.any((breaker) => lowerPrompt.contains(breaker));
  }

  /// Get immersion score for quality monitoring
  static double calculateImmersionScore(String response) {
    final sourceBreakers = ['according to', 'research shows', 'studies indicate', 'experts say'];
    final expertPhrases = ['in my experience', 'i\'ve found', 'the key is', 'i recommend'];
    
    final breakerCount = sourceBreakers.where((breaker) => 
      response.toLowerCase().contains(breaker)).length;
    final expertCount = expertPhrases.where((phrase) => 
      response.toLowerCase().contains(phrase)).length;
    
    // Perfect score if no breakers and has expert phrases
    if (breakerCount == 0 && expertCount > 0) return 1.0;
    
    // Good score if no breakers
    if (breakerCount == 0) return 0.8;
    
    // Poor score if has breakers
    return 0.3;
  }
}
