// lib/services/phase7_coach_evolution_engine.dart

import 'dart:async';
import 'package:flutter/foundation.dart';
import '../models/user_model.dart';
import 'phase7_knowledge_synth_engine.dart';
import 'phase7_wisdom_distillation_engine.dart';
import 'comprehensive_logging_service.dart';

/// 🧬 PHASE 7C: COACH EVOLUTION ENGINE
/// 
/// Revolutionary coach adaptation system that:
/// - Gradually evolves coach personalities based on user interactions
/// - Learns from successful coaching patterns and user responses
/// - Adapts communication style for maximum effectiveness
/// - Maintains coach authenticity while optimizing for user resonance
/// - Creates personalized coaching relationships that improve over time
/// 
/// This engine transforms static coaches into dynamic, learning entities
/// that become perfectly attuned to each individual user's needs.
class Phase7CoachEvolutionEngine {
  static final Phase7CoachEvolutionEngine _instance = Phase7CoachEvolutionEngine._internal();
  factory Phase7CoachEvolutionEngine() => _instance;
  Phase7CoachEvolutionEngine._internal();

  // Cache for coach evolution data
  static final Map<String, CoachEvolutionProfile> _evolutionProfiles = {};
  static final Map<String, List<InteractionLearning>> _learningHistory = {};
  
  /// Initialize the coach evolution engine
  static Future<bool> initialize() async {
    try {
      await ComprehensiveLoggingService.logInfo('🧬 Initializing Phase 7 Coach Evolution Engine...');
      
      // Load existing evolution profiles
      await _loadEvolutionProfiles();
      
      // Initialize learning algorithms
      await _initializeLearningAlgorithms();
      
      await ComprehensiveLoggingService.logInfo('✅ Phase 7 Coach Evolution Engine initialized successfully');
      return true;
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Failed to initialize Phase 7 Coach Evolution Engine: $e');
      return false;
    }
  }

  /// Evolve coach based on interaction and user feedback
  static Future<CoachEvolution> evolveCoach({
    required String coachName,
    required User user,
    required String userMessage,
    required String coachResponse,
    required KnowledgeSynthesis knowledgeSynthesis,
    required WisdomDistillation wisdomDistillation,
    required Map<String, dynamic> interactionContext,
  }) async {
    try {
      if (kDebugMode) print('🧬 Evolving coach $coachName for user: ${user.username}');
      
      // 1. Analyze interaction effectiveness
      final interactionAnalysis = await _analyzeInteractionEffectiveness(
        coachName: coachName,
        user: user,
        userMessage: userMessage,
        coachResponse: coachResponse,
        context: interactionContext,
      );
      
      // 2. Extract learning insights from the interaction
      final learningInsights = await _extractLearningInsights(
        interactionAnalysis: interactionAnalysis,
        knowledgeSynthesis: knowledgeSynthesis,
        wisdomDistillation: wisdomDistillation,
      );
      
      // 3. Determine evolution adjustments
      final evolutionAdjustments = await _determineEvolutionAdjustments(
        coachName: coachName,
        user: user,
        learningInsights: learningInsights,
        interactionAnalysis: interactionAnalysis,
      );
      
      // 4. Apply gradual personality shifts
      final personalityShifts = await _applyPersonalityShifts(
        coachName: coachName,
        user: user,
        adjustments: evolutionAdjustments,
      );
      
      // 5. Update communication style adaptations
      final communicationAdaptations = await _updateCommunicationStyle(
        coachName: coachName,
        user: user,
        learningInsights: learningInsights,
        personalityShifts: personalityShifts,
      );
      
      // 6. Record learning for future interactions
      await _recordLearning(
        coachName: coachName,
        user: user,
        interactionAnalysis: interactionAnalysis,
        learningInsights: learningInsights,
      );
      
      // 7. Calculate evolution impact
      final evolutionImpact = await _calculateEvolutionImpact(
        personalityShifts: personalityShifts,
        communicationAdaptations: communicationAdaptations,
      );
      
      return CoachEvolution(
        coachName: coachName,
        interactionAnalysis: interactionAnalysis,
        learningInsights: learningInsights,
        evolutionAdjustments: evolutionAdjustments,
        personalityShifts: personalityShifts,
        communicationAdaptations: communicationAdaptations,
        evolutionImpact: evolutionImpact,
        evolutionTimestamp: DateTime.now(),
      );
      
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Coach evolution failed: $e');
      return CoachEvolution.fallback(coachName, user.id);
    }
  }

  /// Analyze interaction effectiveness
  static Future<InteractionAnalysis> _analyzeInteractionEffectiveness({
    required String coachName,
    required User user,
    required String userMessage,
    required String coachResponse,
    required Map<String, dynamic> context,
  }) async {
    // Analyze user engagement indicators
    final engagementScore = _calculateEngagementScore(userMessage, context);
    
    // Assess response relevance and quality
    final responseQuality = _assessResponseQuality(userMessage, coachResponse);
    
    // Evaluate emotional resonance
    final emotionalResonance = _evaluateEmotionalResonance(userMessage, coachResponse);
    
    // Check for user satisfaction indicators
    final satisfactionIndicators = _identifySatisfactionIndicators(userMessage, context);
    
    // Analyze coaching effectiveness patterns
    final effectivenessPatterns = await _analyzeEffectivenessPatterns(
      coachName: coachName,
      user: user,
      currentInteraction: {
        'message': userMessage,
        'response': coachResponse,
        'context': context,
      },
    );
    
    return InteractionAnalysis(
      engagementScore: engagementScore,
      responseQuality: responseQuality,
      emotionalResonance: emotionalResonance,
      satisfactionIndicators: satisfactionIndicators,
      effectivenessPatterns: effectivenessPatterns,
      overallEffectiveness: _calculateOverallEffectiveness(
        engagementScore,
        responseQuality,
        emotionalResonance,
      ),
    );
  }

  /// Extract learning insights from interaction
  static Future<LearningInsights> _extractLearningInsights({
    required InteractionAnalysis interactionAnalysis,
    required KnowledgeSynthesis knowledgeSynthesis,
    required WisdomDistillation wisdomDistillation,
  }) async {
    final successfulPatterns = <String>[];
    final improvementOpportunities = <String>[];
    final userPreferences = <String>[];
    final communicationOptimizations = <String>[];
    
    // Identify successful patterns
    if (interactionAnalysis.overallEffectiveness > 0.8) {
      successfulPatterns.add('High-quality knowledge synthesis resonated well');
      successfulPatterns.add('Wisdom distillation matched user needs effectively');
      
      // Extract specific successful elements
      if (interactionAnalysis.emotionalResonance > 0.8) {
        successfulPatterns.add('Emotional tone and empathy were well-received');
      }
      if (interactionAnalysis.responseQuality > 0.8) {
        successfulPatterns.add('Response depth and insight quality were appreciated');
      }
    }
    
    // Identify improvement opportunities
    if (interactionAnalysis.engagementScore < 0.7) {
      improvementOpportunities.add('Increase engagement through more interactive elements');
    }
    if (interactionAnalysis.responseQuality < 0.7) {
      improvementOpportunities.add('Enhance response depth and practical applicability');
    }
    if (interactionAnalysis.emotionalResonance < 0.7) {
      improvementOpportunities.add('Improve emotional connection and empathy');
    }
    
    // Extract user preferences
    userPreferences.addAll(_extractUserPreferences(interactionAnalysis));
    
    // Identify communication optimizations
    communicationOptimizations.addAll(_identifyCommunicationOptimizations(
      interactionAnalysis,
      knowledgeSynthesis,
      wisdomDistillation,
    ));
    
    return LearningInsights(
      successfulPatterns: successfulPatterns,
      improvementOpportunities: improvementOpportunities,
      userPreferences: userPreferences,
      communicationOptimizations: communicationOptimizations,
      learningConfidence: _calculateLearningConfidence(interactionAnalysis),
      applicabilityScore: _calculateApplicabilityScore(successfulPatterns, improvementOpportunities),
    );
  }

  /// Determine evolution adjustments
  static Future<EvolutionAdjustments> _determineEvolutionAdjustments({
    required String coachName,
    required User user,
    required LearningInsights learningInsights,
    required InteractionAnalysis interactionAnalysis,
  }) async {
    // Get current evolution profile
    final currentProfile = _evolutionProfiles['${coachName}_${user.id}'] ?? 
        CoachEvolutionProfile.initial(coachName, user.id);
    
    // Calculate adjustment magnitudes (gradual evolution)
    final personalityAdjustmentMagnitude = _calculatePersonalityAdjustmentMagnitude(
      learningInsights,
      currentProfile,
    );
    
    final communicationAdjustmentMagnitude = _calculateCommunicationAdjustmentMagnitude(
      learningInsights,
      interactionAnalysis,
    );
    
    // Determine specific adjustments
    final personalityAdjustments = _determinePersonalityAdjustments(
      learningInsights,
      personalityAdjustmentMagnitude,
      currentProfile,
    );
    
    final communicationAdjustments = _determineCommunicationAdjustments(
      learningInsights,
      communicationAdjustmentMagnitude,
      currentProfile,
    );
    
    return EvolutionAdjustments(
      personalityAdjustments: personalityAdjustments,
      communicationAdjustments: communicationAdjustments,
      adjustmentMagnitude: (personalityAdjustmentMagnitude + communicationAdjustmentMagnitude) / 2,
      evolutionDirection: _determineEvolutionDirection(learningInsights),
      confidenceLevel: learningInsights.learningConfidence,
    );
  }

  /// Apply gradual personality shifts
  static Future<PersonalityShifts> _applyPersonalityShifts({
    required String coachName,
    required User user,
    required EvolutionAdjustments adjustments,
  }) async {
    final profileKey = '${coachName}_${user.id}';
    final currentProfile = _evolutionProfiles[profileKey] ?? 
        CoachEvolutionProfile.initial(coachName, user.id);
    
    final shifts = <String, double>{};
    final shiftDescriptions = <String>[];
    
    // Apply personality adjustments gradually
    for (final adjustment in adjustments.personalityAdjustments.entries) {
      final currentValue = currentProfile.personalityTraits[adjustment.key] ?? 0.5;
      final targetShift = adjustment.value * adjustments.adjustmentMagnitude;
      final newValue = (currentValue + targetShift).clamp(0.0, 1.0);
      
      shifts[adjustment.key] = newValue;
      
      if ((newValue - currentValue).abs() > 0.05) {
        shiftDescriptions.add(
          'Adjusted ${adjustment.key} from ${(currentValue * 100).toInt()}% to ${(newValue * 100).toInt()}%'
        );
      }
    }
    
    // Update evolution profile
    currentProfile.personalityTraits.addAll(shifts);
    currentProfile.lastEvolution = DateTime.now();
    _evolutionProfiles[profileKey] = currentProfile;
    
    return PersonalityShifts(
      shifts: shifts,
      shiftDescriptions: shiftDescriptions,
      shiftMagnitude: adjustments.adjustmentMagnitude,
      evolutionStage: _determineEvolutionStage(currentProfile),
    );
  }

  /// Update communication style adaptations
  static Future<CommunicationAdaptations> _updateCommunicationStyle({
    required String coachName,
    required User user,
    required LearningInsights learningInsights,
    required PersonalityShifts personalityShifts,
  }) async {
    final adaptations = <String>[];
    final styleChanges = <String, String>{};
    
    // Adapt based on successful patterns
    for (final pattern in learningInsights.successfulPatterns) {
      if (pattern.contains('emotional')) {
        adaptations.add('Maintain empathetic and emotionally resonant communication');
        styleChanges['emotional_tone'] = 'enhanced';
      }
      if (pattern.contains('depth')) {
        adaptations.add('Continue providing deep, insightful responses');
        styleChanges['response_depth'] = 'maintained_high';
      }
    }
    
    // Adapt based on improvement opportunities
    for (final opportunity in learningInsights.improvementOpportunities) {
      if (opportunity.contains('engagement')) {
        adaptations.add('Increase interactive elements and user engagement');
        styleChanges['engagement_style'] = 'more_interactive';
      }
      if (opportunity.contains('practical')) {
        adaptations.add('Focus more on practical, actionable guidance');
        styleChanges['guidance_style'] = 'more_practical';
      }
    }
    
    // Integrate personality shifts into communication
    for (final shift in personalityShifts.shifts.entries) {
      if (shift.value > 0.7) {
        adaptations.add('Emphasize ${shift.key} in communication style');
      }
    }
    
    return CommunicationAdaptations(
      adaptations: adaptations,
      styleChanges: styleChanges,
      adaptationStrength: _calculateAdaptationStrength(adaptations),
      implementationStrategy: _determineImplementationStrategy(styleChanges),
    );
  }

  /// Record learning for future interactions
  static Future<void> _recordLearning({
    required String coachName,
    required User user,
    required InteractionAnalysis interactionAnalysis,
    required LearningInsights learningInsights,
  }) async {
    final learningKey = '${coachName}_${user.id}';
    final learningHistory = _learningHistory[learningKey] ?? <InteractionLearning>[];
    
    final learning = InteractionLearning(
      timestamp: DateTime.now(),
      effectiveness: interactionAnalysis.overallEffectiveness,
      successfulPatterns: learningInsights.successfulPatterns,
      improvementOpportunities: learningInsights.improvementOpportunities,
      userPreferences: learningInsights.userPreferences,
    );
    
    learningHistory.add(learning);
    
    // Keep only recent learning history (last 50 interactions)
    if (learningHistory.length > 50) {
      learningHistory.removeRange(0, learningHistory.length - 50);
    }
    
    _learningHistory[learningKey] = learningHistory;
  }

  /// Calculate evolution impact
  static Future<EvolutionImpact> _calculateEvolutionImpact({
    required PersonalityShifts personalityShifts,
    required CommunicationAdaptations communicationAdaptations,
  }) async {
    final impactScore = (personalityShifts.shiftMagnitude + 
                        communicationAdaptations.adaptationStrength) / 2;
    
    final expectedImprovements = <String>[];
    
    if (personalityShifts.shiftMagnitude > 0.1) {
      expectedImprovements.add('Enhanced personality alignment with user preferences');
    }
    if (communicationAdaptations.adaptationStrength > 0.7) {
      expectedImprovements.add('Improved communication effectiveness and resonance');
    }
    
    return EvolutionImpact(
      impactScore: impactScore,
      expectedImprovements: expectedImprovements,
      timeToRealization: _estimateTimeToRealization(impactScore),
      confidenceLevel: _calculateImpactConfidence(personalityShifts, communicationAdaptations),
    );
  }

  // Helper methods for calculations and analysis
  static double _calculateEngagementScore(String userMessage, Map<String, dynamic> context) {
    // Analyze message length, enthusiasm, follow-up questions
    final messageLength = userMessage.length;
    final hasQuestions = userMessage.contains('?');
    final hasEnthusiasm = userMessage.contains('!') || userMessage.toLowerCase().contains('great');
    
    double score = 0.5; // Base score
    if (messageLength > 50) score += 0.2; // Detailed message
    if (hasQuestions) score += 0.2; // Engaged questioning
    if (hasEnthusiasm) score += 0.1; // Positive response
    
    return score.clamp(0.0, 1.0);
  }

  static double _assessResponseQuality(String userMessage, String coachResponse) {
    // Assess depth, relevance, actionability
    final responseLength = coachResponse.length;
    final hasActionableAdvice = coachResponse.toLowerCase().contains('try') || 
                               coachResponse.toLowerCase().contains('consider');
    
    double quality = 0.6; // Base quality
    if (responseLength > 200) quality += 0.2; // Detailed response
    if (hasActionableAdvice) quality += 0.2; // Actionable guidance
    
    return quality.clamp(0.0, 1.0);
  }

  static double _evaluateEmotionalResonance(String userMessage, String coachResponse) {
    // Check for emotional alignment and empathy
    final coachEmpathy = coachResponse.toLowerCase().contains('understand') ||
                        coachResponse.toLowerCase().contains('feel');
    
    double resonance = 0.6; // Base resonance
    if (coachEmpathy) resonance += 0.3; // Empathetic response
    
    return resonance.clamp(0.0, 1.0);
  }

  static List<String> _identifySatisfactionIndicators(String userMessage, Map<String, dynamic> context) {
    final indicators = <String>[];
    final lowerMessage = userMessage.toLowerCase();
    
    if (lowerMessage.contains('thank') || lowerMessage.contains('helpful')) {
      indicators.add('Explicit gratitude expressed');
    }
    if (lowerMessage.contains('great') || lowerMessage.contains('perfect')) {
      indicators.add('Positive feedback given');
    }
    
    return indicators;
  }

  static double _calculateOverallEffectiveness(double engagement, double quality, double resonance) {
    return (engagement * 0.3 + quality * 0.4 + resonance * 0.3);
  }

  // Placeholder methods for complex calculations
  static Future<List<String>> _analyzeEffectivenessPatterns(
      {required String coachName, required User user, required Map<String, dynamic> currentInteraction}) async => [];
  static List<String> _extractUserPreferences(InteractionAnalysis analysis) => [];
  static List<String> _identifyCommunicationOptimizations(
      InteractionAnalysis analysis, KnowledgeSynthesis synthesis, WisdomDistillation distillation) => [];
  static double _calculateLearningConfidence(InteractionAnalysis analysis) => 0.8;
  static double _calculateApplicabilityScore(List<String> patterns, List<String> opportunities) => 0.7;
  static double _calculatePersonalityAdjustmentMagnitude(LearningInsights insights, CoachEvolutionProfile profile) => 0.05;
  static double _calculateCommunicationAdjustmentMagnitude(LearningInsights insights, InteractionAnalysis analysis) => 0.1;
  static Map<String, double> _determinePersonalityAdjustments(
      LearningInsights insights, double magnitude, CoachEvolutionProfile profile) => {};
  static Map<String, double> _determineCommunicationAdjustments(
      LearningInsights insights, double magnitude, CoachEvolutionProfile profile) => {};
  static String _determineEvolutionDirection(LearningInsights insights) => 'positive';
  static String _determineEvolutionStage(CoachEvolutionProfile profile) => 'developing';
  static double _calculateAdaptationStrength(List<String> adaptations) => 0.7;
  static String _determineImplementationStrategy(Map<String, String> changes) => 'gradual';
  static String _estimateTimeToRealization(double impact) => '1-2 weeks';
  static double _calculateImpactConfidence(PersonalityShifts shifts, CommunicationAdaptations adaptations) => 0.8;


  // Placeholder methods for initialization
  static Future<void> _loadEvolutionProfiles() async {}
  static Future<void> _initializeLearningAlgorithms() async {}
}

// Data models for Phase 7 Coach Evolution
class CoachEvolution {
  final String coachName;
  final InteractionAnalysis interactionAnalysis;
  final LearningInsights learningInsights;
  final EvolutionAdjustments evolutionAdjustments;
  final PersonalityShifts personalityShifts;
  final CommunicationAdaptations communicationAdaptations;
  final EvolutionImpact evolutionImpact;
  final DateTime evolutionTimestamp;

  CoachEvolution({
    required this.coachName,
    required this.interactionAnalysis,
    required this.learningInsights,
    required this.evolutionAdjustments,
    required this.personalityShifts,
    required this.communicationAdaptations,
    required this.evolutionImpact,
    required this.evolutionTimestamp,
  });

  factory CoachEvolution.fallback(String coachName, String userId) {
    return CoachEvolution(
      coachName: coachName,
      interactionAnalysis: InteractionAnalysis.basic(),
      learningInsights: LearningInsights.basic(),
      evolutionAdjustments: EvolutionAdjustments.basic(),
      personalityShifts: PersonalityShifts.basic(),
      communicationAdaptations: CommunicationAdaptations.basic(),
      evolutionImpact: EvolutionImpact.basic(),
      evolutionTimestamp: DateTime.now(),
    );
  }
}

class InteractionAnalysis {
  final double engagementScore;
  final double responseQuality;
  final double emotionalResonance;
  final List<String> satisfactionIndicators;
  final List<String> effectivenessPatterns;
  final double overallEffectiveness;

  InteractionAnalysis({
    required this.engagementScore,
    required this.responseQuality,
    required this.emotionalResonance,
    required this.satisfactionIndicators,
    required this.effectivenessPatterns,
    required this.overallEffectiveness,
  });

  factory InteractionAnalysis.basic() {
    return InteractionAnalysis(
      engagementScore: 0.7,
      responseQuality: 0.7,
      emotionalResonance: 0.7,
      satisfactionIndicators: [],
      effectivenessPatterns: [],
      overallEffectiveness: 0.7,
    );
  }
}

class LearningInsights {
  final List<String> successfulPatterns;
  final List<String> improvementOpportunities;
  final List<String> userPreferences;
  final List<String> communicationOptimizations;
  final double learningConfidence;
  final double applicabilityScore;

  LearningInsights({
    required this.successfulPatterns,
    required this.improvementOpportunities,
    required this.userPreferences,
    required this.communicationOptimizations,
    required this.learningConfidence,
    required this.applicabilityScore,
  });

  factory LearningInsights.basic() {
    return LearningInsights(
      successfulPatterns: [],
      improvementOpportunities: [],
      userPreferences: [],
      communicationOptimizations: [],
      learningConfidence: 0.6,
      applicabilityScore: 0.6,
    );
  }
}

class EvolutionAdjustments {
  final Map<String, double> personalityAdjustments;
  final Map<String, double> communicationAdjustments;
  final double adjustmentMagnitude;
  final String evolutionDirection;
  final double confidenceLevel;

  EvolutionAdjustments({
    required this.personalityAdjustments,
    required this.communicationAdjustments,
    required this.adjustmentMagnitude,
    required this.evolutionDirection,
    required this.confidenceLevel,
  });

  factory EvolutionAdjustments.basic() {
    return EvolutionAdjustments(
      personalityAdjustments: {},
      communicationAdjustments: {},
      adjustmentMagnitude: 0.05,
      evolutionDirection: 'neutral',
      confidenceLevel: 0.6,
    );
  }
}

class PersonalityShifts {
  final Map<String, double> shifts;
  final List<String> shiftDescriptions;
  final double shiftMagnitude;
  final String evolutionStage;

  PersonalityShifts({
    required this.shifts,
    required this.shiftDescriptions,
    required this.shiftMagnitude,
    required this.evolutionStage,
  });

  factory PersonalityShifts.basic() {
    return PersonalityShifts(
      shifts: {},
      shiftDescriptions: [],
      shiftMagnitude: 0.0,
      evolutionStage: 'stable',
    );
  }
}

class CommunicationAdaptations {
  final List<String> adaptations;
  final Map<String, String> styleChanges;
  final double adaptationStrength;
  final String implementationStrategy;

  CommunicationAdaptations({
    required this.adaptations,
    required this.styleChanges,
    required this.adaptationStrength,
    required this.implementationStrategy,
  });

  factory CommunicationAdaptations.basic() {
    return CommunicationAdaptations(
      adaptations: [],
      styleChanges: {},
      adaptationStrength: 0.0,
      implementationStrategy: 'maintain',
    );
  }
}

class EvolutionImpact {
  final double impactScore;
  final List<String> expectedImprovements;
  final String timeToRealization;
  final double confidenceLevel;

  EvolutionImpact({
    required this.impactScore,
    required this.expectedImprovements,
    required this.timeToRealization,
    required this.confidenceLevel,
  });

  factory EvolutionImpact.basic() {
    return EvolutionImpact(
      impactScore: 0.0,
      expectedImprovements: [],
      timeToRealization: 'No change',
      confidenceLevel: 0.5,
    );
  }
}

class CoachEvolutionProfile {
  final String coachName;
  final String userId;
  final Map<String, double> personalityTraits;
  final Map<String, String> communicationPreferences;
  final DateTime createdAt;
  DateTime lastEvolution;
  final int interactionCount;

  CoachEvolutionProfile({
    required this.coachName,
    required this.userId,
    required this.personalityTraits,
    required this.communicationPreferences,
    required this.createdAt,
    required this.lastEvolution,
    required this.interactionCount,
  });

  factory CoachEvolutionProfile.initial(String coachName, String userId) {
    return CoachEvolutionProfile(
      coachName: coachName,
      userId: userId,
      personalityTraits: {
        'empathy': 0.7,
        'directness': 0.6,
        'enthusiasm': 0.7,
        'analytical': 0.6,
        'supportive': 0.8,
      },
      communicationPreferences: {
        'tone': 'balanced',
        'depth': 'moderate',
        'style': 'conversational',
      },
      createdAt: DateTime.now(),
      lastEvolution: DateTime.now(),
      interactionCount: 0,
    );
  }
}

class InteractionLearning {
  final DateTime timestamp;
  final double effectiveness;
  final List<String> successfulPatterns;
  final List<String> improvementOpportunities;
  final List<String> userPreferences;

  InteractionLearning({
    required this.timestamp,
    required this.effectiveness,
    required this.successfulPatterns,
    required this.improvementOpportunities,
    required this.userPreferences,
  });
}
