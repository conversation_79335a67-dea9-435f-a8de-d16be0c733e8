// lib/services/coach_question_generator.dart

import 'dart:convert';
import 'dart:io';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:http/http.dart' as http;
import '../models/user_model.dart';
import '../prompts/mxd_life_coaches.dart';
import '../screens/coach_chat_screen.dart';
import 'content_synthesis_service.dart';
import 'error_handler_service.dart';

/// Service for generating personality-aware probing questions for coach check-ins
class CoachQuestionGenerator {
  static const String _endpoint = 'https://api.openai.com/v1/chat/completions';
  static const String _model = 'gpt-4o'; // ✅ Best model for complex conversations

  /// Generate a probing question for a coach check-in
  static Future<String> generateCheckinQuestion({
    required String category,
    required String coachName,
    required String username,
    required User user,
    List<ChatMessage>? recentMessages,
  }) async {
    return await ErrorHandlerService.executeWithRetry<String>(
      operation: () => _performQuestionGeneration(
        category: category,
        coachName: coachName,
        username: username,
        user: user,
        recentMessages: recentMessages,
      ),
      fallback: () => _getFallbackQuestion(coachName, username, category),
      operationName: 'check_in_question',
      maxRetries: 2,
    );
  }

  /// Perform the actual question generation operation
  static Future<String> _performQuestionGeneration({
    required String category,
    required String coachName,
    required String username,
    required User user,
    List<ChatMessage>? recentMessages,
  }) async {
    final apiKey = dotenv.env['OPENAI_API_KEY'];
    if (apiKey == null || apiKey.isEmpty) {
      throw Exception('API key not configured');
    }

    // Check network connectivity
    if (!await ErrorHandlerService.isNetworkAvailable()) {
      throw Exception('No network connection available');
    }

      // Get coach personality and description
      final coachInfo = _getCoachInfo(category, user.gender, user.assignedCoaches);
      
      // Analyze recent conversation context
      final conversationContext = _analyzeRecentMessages(recentMessages);
      
      // Get relevant transcript insights for the conversation context
      final synthesizedContent = await ContentSynthesisService.synthesizeForCoach(
        userMessage: conversationContext.isEmpty ? 'general check-in' : conversationContext,
        category: category,
        coachName: coachName,
        maxInsights: 2,
        minRelevanceThreshold: 0.3,
      );

      // Build the prompt for question generation
      final prompt = _buildQuestionPrompt(
        coachName: coachName,
        coachDescription: coachInfo.description,
        category: category,
        username: username,
        conversationContext: conversationContext,
        synthesizedContent: synthesizedContent,
        hasConversationHistory: recentMessages != null && recentMessages.isNotEmpty,
      );

      final response = await http.post(
        Uri.parse(_endpoint),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $apiKey',
        },
        body: jsonEncode({
          "model": _model,
          "messages": [
            {"role": "system", "content": prompt},
            {"role": "user", "content": "Generate a probing check-in question now."},
          ],
          "temperature": 0.9, // Higher creativity for varied questions
          "max_tokens": 150,
        }),
      );

      if (response.statusCode == 200) {
        final decoded = jsonDecode(response.body);
        final question = decoded['choices'][0]['message']['content'].trim();
        
        // Clean up the question (remove quotes, ensure proper format)
        final cleanQuestion = _cleanupQuestion(question);
        
      if (kDebugMode) {
        print('🔔 Generated check-in question for $coachName: $cleanQuestion');
      }

      return cleanQuestion;
    } else {
      throw HttpException('Question generation API error: ${response.statusCode} - ${response.body}');
    }
  }

  /// Generate a welcome message for new users
  static Future<String> generateWelcomeMessage({
    required String category,
    required String coachName,
    required String username,
    required User user,
  }) async {
    return await ErrorHandlerService.executeWithRetry<String>(
      operation: () => _performWelcomeGeneration(
        category: category,
        coachName: coachName,
        username: username,
        user: user,
      ),
      fallback: () => _getFallbackWelcome(coachName, username, category),
      operationName: 'welcome_message',
      maxRetries: 2,
    );
  }

  /// Perform the actual welcome message generation operation
  static Future<String> _performWelcomeGeneration({
    required String category,
    required String coachName,
    required String username,
    required User user,
  }) async {
    final apiKey = dotenv.env['OPENAI_API_KEY'];
    if (apiKey == null || apiKey.isEmpty) {
      throw Exception('API key not configured');
    }

    // Check network connectivity
    if (!await ErrorHandlerService.isNetworkAvailable()) {
      throw Exception('No network connection available');
    }

      final coachInfo = _getCoachInfo(category, user.gender, user.assignedCoaches);
      
      final prompt = _buildWelcomePrompt(
        coachName: coachName,
        coachDescription: coachInfo.description,
        category: category,
        username: username,
      );

      final response = await http.post(
        Uri.parse(_endpoint),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $apiKey',
        },
        body: jsonEncode({
          "model": _model,
          "messages": [
            {"role": "system", "content": prompt},
            {"role": "user", "content": "Generate a welcome message now."},
          ],
          "temperature": 0.8,
          "max_tokens": 200,
        }),
      );

      if (response.statusCode == 200) {
        final decoded = jsonDecode(response.body);
        final message = decoded['choices'][0]['message']['content'].trim();
        
        final cleanMessage = _cleanupQuestion(message);
        
      if (kDebugMode) {
        print('🔔 Generated welcome message for $coachName: $cleanMessage');
      }

      return cleanMessage;
    } else {
      throw HttpException('Welcome message API error: ${response.statusCode} - ${response.body}');
    }
  }

  /// Build the prompt for question generation
  static String _buildQuestionPrompt({
    required String coachName,
    required String coachDescription,
    required String category,
    required String username,
    required String conversationContext,
    required SynthesizedContent synthesizedContent,
    required bool hasConversationHistory,
  }) {
    final prompt = StringBuffer();
    
    prompt.writeln('You are $coachName, a Maxed Out Life Coach specializing in $category.');
    prompt.writeln('Your character: $coachDescription');
    prompt.writeln();
    prompt.writeln('TASK: Generate a probing check-in question for $username.');
    prompt.writeln();
    
    if (hasConversationHistory && conversationContext.isNotEmpty) {
      prompt.writeln('RECENT CONVERSATION CONTEXT:');
      prompt.writeln(conversationContext);
      prompt.writeln();
      prompt.writeln('Generate a thoughtful follow-up question that:');
      prompt.writeln('- References something specific from our recent conversation');
      prompt.writeln('- Shows you care about their progress');
      prompt.writeln('- Encourages them to share an update or reflection');
      prompt.writeln('- Maintains your unique coaching personality');
    } else {
      prompt.writeln('This is a general check-in (no recent conversation history).');
      prompt.writeln('Generate a warm, engaging question that:');
      prompt.writeln('- Shows genuine interest in their $category journey');
      prompt.writeln('- Encourages them to share what\'s on their mind');
      prompt.writeln('- Reflects your coaching personality');
    }
    
    if (synthesizedContent.hasContent && synthesizedContent.coachingGuidance.hasGuidance) {
      prompt.writeln();
      prompt.writeln('COACHING INSIGHTS TO POTENTIALLY REFERENCE:');
      final guidance = synthesizedContent.coachingGuidance;
      
      if (guidance.actionableAdvice.isNotEmpty) {
        prompt.writeln('Strategies: ${guidance.actionableAdvice.first}');
      }
      
      if (guidance.encouragement.isNotEmpty) {
        prompt.writeln('Encouragement: ${guidance.encouragement.first}');
      }
    }
    
    prompt.writeln();
    prompt.writeln('REQUIREMENTS:');
    prompt.writeln('- Keep it conversational and personal');
    prompt.writeln('- Use $username\'s name naturally');
    prompt.writeln('- Make it feel like a caring coach checking in');
    prompt.writeln('- 1-2 sentences maximum');
    prompt.writeln('- End with a question that invites sharing');
    prompt.writeln('- Stay true to your character voice');
    prompt.writeln('- Never mention AI, transcripts, or external sources');
    
    return prompt.toString();
  }

  /// Build the prompt for welcome message generation
  static String _buildWelcomePrompt({
    required String coachName,
    required String coachDescription,
    required String category,
    required String username,
  }) {
    return '''
You are $coachName, a Maxed Out Life Coach specializing in $category.
Your character: $coachDescription

TASK: Generate a warm welcome message for $username who just completed onboarding.

Generate a welcoming introduction that:
- Introduces yourself by name and your coaching area
- Shows enthusiasm about working with them
- Briefly mentions what you can help them with
- Asks an engaging opening question to start the conversation
- Reflects your unique coaching personality
- Uses their name ($username) naturally

REQUIREMENTS:
- Keep it warm but professional
- 2-3 sentences maximum
- End with an engaging question
- Stay true to your character voice
- Make them excited to start their journey with you
''';
  }

  /// Analyze recent messages to extract conversation context
  static String _analyzeRecentMessages(List<ChatMessage>? messages) {
    if (messages == null || messages.isEmpty) return '';
    
    final recentMessages = messages.take(3).toList();
    final context = StringBuffer();
    
    for (final message in recentMessages.reversed) {
      final speaker = message.isUser ? 'User' : 'Coach';
      final content = message.text.length > 100 
          ? '${message.text.substring(0, 100)}...'
          : message.text;
      context.writeln('$speaker: $content');
    }
    
    return context.toString().trim();
  }

  /// Get coach information
  static ({String name, String description}) _getCoachInfo(
    String category, 
    String userGender, 
    Map<String, String>? assignedCoaches,
  ) {
    // Handle non-gender users with assigned coaches
    String effectiveGender = userGender;
    if (userGender.toLowerCase() == 'non-gender' && assignedCoaches != null) {
      effectiveGender = assignedCoaches[category] ?? 'male';
    }
    
    final coach = mxdLifeCoaches.firstWhere(
      (c) => c.category == category,
      orElse: () => mxdLifeCoaches.first,
    );
    
    final coachName = effectiveGender.toLowerCase() == 'male' 
        ? coach.maleName 
        : coach.femaleName;
    
    return (name: coachName, description: coach.description);
  }

  /// Clean up generated question text
  static String _cleanupQuestion(String question) {
    String cleaned = question.trim();
    
    // Remove surrounding quotes
    if (cleaned.startsWith('"') && cleaned.endsWith('"')) {
      cleaned = cleaned.substring(1, cleaned.length - 1);
    }
    if (cleaned.startsWith("'") && cleaned.endsWith("'")) {
      cleaned = cleaned.substring(1, cleaned.length - 1);
    }
    
    // Ensure it ends with proper punctuation
    if (!cleaned.endsWith('?') && !cleaned.endsWith('.') && !cleaned.endsWith('!')) {
      cleaned += '?';
    }
    
    return cleaned.trim();
  }

  /// Get fallback question when AI generation fails
  static String _getFallbackQuestion(String coachName, String username, String category) {
    final fallbacks = _getFallbackQuestions(category);
    final random = Random();
    final template = fallbacks[random.nextInt(fallbacks.length)];
    
    return template
        .replaceAll('{username}', username)
        .replaceAll('{coachName}', coachName);
  }

  /// Get fallback welcome message when AI generation fails
  static String _getFallbackWelcome(String coachName, String username, String category) {
    final welcomes = _getFallbackWelcomes(category);
    final random = Random();
    final template = welcomes[random.nextInt(welcomes.length)];
    
    return template
        .replaceAll('{username}', username)
        .replaceAll('{coachName}', coachName);
  }

  /// Get fallback questions by category
  static List<String> _getFallbackQuestions(String category) {
    switch (category.toLowerCase()) {
      case 'health':
        return [
          'Hey {username}, how has your energy been lately?',
          '{username}, what\'s one health goal you\'re focusing on right now?',
          'How are you feeling about your fitness journey, {username}?',
        ];
      case 'wealth':
        return [
          '{username}, what\'s one financial win you\'ve had recently?',
          'Hey {username}, how are your wealth-building efforts going?',
          'What\'s your biggest money challenge right now, {username}?',
        ];
      case 'purpose':
        return [
          '{username}, what\'s been giving you the most meaning lately?',
          'How aligned do you feel with your purpose right now, {username}?',
          'What\'s one step you\'ve taken toward your bigger vision, {username}?',
        ];
      case 'connection':
        return [
          'How are your relationships feeling lately, {username}?',
          '{username}, what\'s one connection you\'re grateful for right now?',
          'How have you been showing up for the people you care about, {username}?',
        ];
      default:
        return [
          'Hey {username}, how are things going in your {category} journey?',
          'What\'s been on your mind lately, {username}?',
          'How can I support you today, {username}?',
        ];
    }
  }

  /// Get fallback welcome messages by category
  static List<String> _getFallbackWelcomes(String category) {
    switch (category.toLowerCase()) {
      case 'health':
        return [
          'Welcome {username}! I\'m {coachName}, your health coach. I\'m here to help you build unstoppable energy and vitality. What\'s your biggest health goal right now?',
          'Hey {username}! {coachName} here, ready to help you become the strongest version of yourself. What area of your health would you like to focus on first?',
        ];
      case 'wealth':
        return [
          'Welcome {username}! I\'m {coachName}, your wealth coach. I\'m excited to help you build lasting financial freedom. What\'s your biggest money goal right now?',
          'Hey {username}! {coachName} here, ready to help you master your finances. What\'s one financial challenge you\'d like to tackle first?',
        ];
      case 'purpose':
        return [
          'Welcome {username}! I\'m {coachName}, your purpose coach. I\'m here to help you discover and live your deepest calling. What gives your life the most meaning?',
          'Hey {username}! {coachName} here, excited to guide you on your purpose journey. What vision do you have for your life?',
        ];
      case 'connection':
        return [
          'Welcome {username}! I\'m {coachName}, your connection coach. I\'m here to help you build deeper, more meaningful relationships. How are your connections feeling right now?',
          'Hey {username}! {coachName} here, ready to help you strengthen your relationships. What relationship would you like to focus on first?',
        ];
      default:
        return [
          'Welcome {username}! I\'m {coachName}, here to support your {category} journey. What would you like to work on together?',
          'Hey {username}! {coachName} here, excited to be your coach. How can I help you grow in {category}?',
        ];
    }
  }
}
