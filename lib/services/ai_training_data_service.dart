// lib/services/ai_training_data_service.dart

import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';


/// Proprietary AI training data collection and learning system
class AITrainingDataService {
  static const FlutterSecureStorage _storage = FlutterSecureStorage();
  static const String _trainingDataKey = 'ai_training_data';
  static const String _learningPatternsKey = 'learning_patterns';
  
  // In-memory cache for performance (with size limits to prevent memory leaks)
  static final List<TrainingDataPoint> _trainingDataCache = [];
  static final Map<String, LearningPattern> _learningPatternsCache = {};

  // Memory management constants
  static const int _maxCacheSize = 1000; // Limit cache to prevent memory issues
  static const int _maxPatternsCache = 100;
  
  static Timer? _learningTimer;
  static bool _isLearningActive = false;

  /// Initialize AI training data system
  static Future<void> initializeTrainingSystem() async {
    try {
      await _loadTrainingData();
      await _loadLearningPatterns();
      
      // Start continuous learning
      startContinuousLearning();
      
      if (kDebugMode) print('🧬 AI training data system initialized');
    } catch (e) {
      if (kDebugMode) print('❌ Failed to initialize training system: $e');
    }
  }

  /// Record successful interaction for training data
  static Future<void> recordSuccessfulInteraction({
    required String userMessage,
    required String coachResponse,
    required String category,
    required bool wasHelpful,
    required String userFeedback,
    String? responseStrategy,
    Map<String, dynamic>? additionalContext,
  }) async {
    try {
      // Create anonymized training data point
      final dataPoint = TrainingDataPoint(
        id: _generateId(),
        messageType: _categorizeMessage(userMessage),
        messageLength: _categorizeLength(userMessage.length),
        messageSentiment: _analyzeSentiment(userMessage),
        messageComplexity: _analyzeComplexity(userMessage),
        responseStrategy: responseStrategy ?? _analyzeResponseStrategy(coachResponse),
        responseLength: _categorizeLength(coachResponse.length),
        responseTone: _analyzeTone(coachResponse),
        category: category,
        successRating: wasHelpful ? 1.0 : 0.0,
        userSatisfaction: _analyzeSatisfaction(userFeedback),
        contextFactors: _extractContextFactors(additionalContext ?? {}),
        timestamp: DateTime.now(),
      );
      
      // Add to training dataset
      await _addTrainingDataPoint(dataPoint);
      
      // Update learning patterns
      await _updateLearningPatterns(dataPoint);
      
      if (kDebugMode) print('🧬 Recorded training data point');
    } catch (e) {
      if (kDebugMode) print('❌ Failed to record training data: $e');
    }
  }

  /// Get optimized response strategy based on learned patterns
  static Future<ResponseOptimization> getOptimizedResponseStrategy({
    required String userMessage,
    required String category,
    Map<String, dynamic>? userContext,
  }) async {
    try {
      // Analyze current message
      final messageAnalysis = _analyzeMessageForOptimization(userMessage);
      
      // Find similar successful cases
      final similarCases = await _findSimilarSuccessfulCases(messageAnalysis, category);
      
      // Extract success patterns
      final successPatterns = _extractSuccessPatterns(similarCases);
      
      // Generate optimization recommendations
      final optimization = ResponseOptimization(
        recommendedStrategy: successPatterns['strategy'] ?? 'balanced',
        recommendedTone: successPatterns['tone'] ?? 'supportive',
        recommendedLength: successPatterns['length'] ?? 'moderate',
        confidenceLevel: _calculateOptimizationConfidence(similarCases),
        successProbability: _calculateSuccessProbability(successPatterns),
        learningInsights: _generateLearningInsights(successPatterns),
        basedOnCases: similarCases.length,
      );
      
      return optimization;
    } catch (e) {
      if (kDebugMode) print('❌ Failed to get optimization strategy: $e');
      return ResponseOptimization.defaultOptimization();
    }
  }

  /// Get learning analytics report
  static Future<Map<String, dynamic>> getLearningAnalytics() async {
    try {
      final totalDataPoints = _trainingDataCache.length;
      final successfulInteractions = _trainingDataCache.where((d) => d.successRating > 0.7).length;
      
      // Calculate success rates by category
      final categorySuccessRates = <String, double>{};
      for (final category in ['Health', 'Wealth', 'Purpose', 'Connection']) {
        final categoryData = _trainingDataCache.where((d) => d.category == category).toList();
        if (categoryData.isNotEmpty) {
          final successRate = categoryData.map((d) => d.successRating).reduce((a, b) => a + b) / categoryData.length;
          categorySuccessRates[category] = successRate;
        }
      }
      
      // Analyze most effective strategies
      final strategyEffectiveness = <String, double>{};
      for (final pattern in _learningPatternsCache.values) {
        strategyEffectiveness[pattern.patternType] = pattern.successRate;
      }
      
      return {
        'totalDataPoints': totalDataPoints,
        'successfulInteractions': successfulInteractions,
        'overallSuccessRate': totalDataPoints > 0 ? successfulInteractions / totalDataPoints : 0.0,
        'categorySuccessRates': categorySuccessRates,
        'strategyEffectiveness': strategyEffectiveness,
        'learningPatterns': _learningPatternsCache.length,
        'dataCollectionPeriod': _calculateDataCollectionPeriod(),
        'improvementTrend': _calculateImprovementTrend(),
      };
    } catch (e) {
      if (kDebugMode) print('❌ Failed to generate learning analytics: $e');
      return {};
    }
  }

  /// Get personalized learning insights for a specific context
  static Future<List<String>> getPersonalizedLearningInsights({
    required String messageType,
    required String category,
  }) async {
    try {
      final insights = <String>[];
      
      // Find relevant learning patterns
      final relevantPatterns = _learningPatternsCache.values
          .where((p) => p.messageType == messageType && p.category == category)
          .toList()
        ..sort((a, b) => b.successRate.compareTo(a.successRate));
      
      for (final pattern in relevantPatterns.take(3)) {
        if (pattern.successRate > 0.7) {
          insights.add(_generateInsightFromPattern(pattern));
        }
      }
      
      // Add general insights if specific ones are limited
      if (insights.length < 2) {
        insights.addAll(_getGeneralLearningInsights(category));
      }
      
      return insights.take(5).toList();
    } catch (e) {
      if (kDebugMode) print('❌ Failed to get personalized insights: $e');
      return [];
    }
  }

  /// Private helper methods

  static Future<void> _addTrainingDataPoint(TrainingDataPoint dataPoint) async {
    _trainingDataCache.add(dataPoint);

    // Keep only last N data points for performance (prevent memory leaks)
    if (_trainingDataCache.length > _maxCacheSize) {
      _trainingDataCache.removeAt(0);
    }

    await _saveTrainingData();
  }

  static Future<void> _updateLearningPatterns(TrainingDataPoint dataPoint) async {
    final patternKey = '${dataPoint.messageType}_${dataPoint.category}_${dataPoint.responseStrategy}';
    
    var pattern = _learningPatternsCache[patternKey] ?? LearningPattern(
      patternType: dataPoint.responseStrategy,
      messageType: dataPoint.messageType,
      category: dataPoint.category,
      successRate: 0.5,
      totalOccurrences: 0,
      successfulOccurrences: 0,
      averageUserSatisfaction: 0.5,
      lastUpdated: DateTime.now(),
    );
    
    // Update pattern with new data
    pattern.totalOccurrences += 1;
    if (dataPoint.successRating > 0.7) {
      pattern.successfulOccurrences += 1;
    }
    
    pattern.successRate = pattern.successfulOccurrences / pattern.totalOccurrences;
    pattern.averageUserSatisfaction = 
        (pattern.averageUserSatisfaction * 0.9) + (dataPoint.userSatisfaction * 0.1);
    pattern.lastUpdated = DateTime.now();
    
    _learningPatternsCache[patternKey] = pattern;

    // Manage pattern cache size to prevent memory leaks
    if (_learningPatternsCache.length > _maxPatternsCache) {
      // Remove oldest patterns (simple LRU-like behavior)
      final oldestKey = _learningPatternsCache.keys.first;
      _learningPatternsCache.remove(oldestKey);
    }

    await _saveLearningPatterns();
  }

  /// Start continuous learning process
  static void startContinuousLearning() {
    if (_isLearningActive) return;
    
    _isLearningActive = true;
    _learningTimer = Timer.periodic(const Duration(hours: 6), (timer) {
      _performContinuousLearning();
    });
    
    if (kDebugMode) print('🧬 Continuous learning started');
  }

  /// Stop continuous learning
  static void stopContinuousLearning() {
    _learningTimer?.cancel();
    _learningTimer = null;
    _isLearningActive = false;

    if (kDebugMode) print('🧬 Continuous learning stopped');
  }

  static Future<void> _performContinuousLearning() async {
    try {
      // Analyze recent patterns
      await _analyzeRecentPatterns();
      
      // Update learning models
      await _updateLearningModels();
      
      // Clean old data
      await _cleanOldData();
      
      if (kDebugMode) print('🧬 Continuous learning cycle completed');
    } catch (e) {
      if (kDebugMode) print('❌ Continuous learning failed: $e');
    }
  }

  static Map<String, dynamic> _analyzeMessageForOptimization(String message) {
    return {
      'type': _categorizeMessage(message),
      'length': _categorizeLength(message.length),
      'sentiment': _analyzeSentiment(message),
      'complexity': _analyzeComplexity(message),
      'urgency': _analyzeUrgency(message),
      'emotionalState': _analyzeEmotionalState(message),
    };
  }

  static Future<List<TrainingDataPoint>> _findSimilarSuccessfulCases(
    Map<String, dynamic> messageAnalysis,
    String category,
  ) async {
    final similarCases = _trainingDataCache.where((dataPoint) {
      return dataPoint.category == category &&
             dataPoint.messageType == messageAnalysis['type'] &&
             dataPoint.successRating > 0.7;
    }).toList();
    
    // Sort by success rating
    similarCases.sort((a, b) => b.successRating.compareTo(a.successRating));
    
    return similarCases.take(10).toList();
  }

  static Map<String, String> _extractSuccessPatterns(List<TrainingDataPoint> cases) {
    if (cases.isEmpty) {
      return {
        'strategy': 'balanced',
        'tone': 'supportive',
        'length': 'moderate',
      };
    }
    
    // Find most common successful patterns
    final strategies = <String, int>{};
    final tones = <String, int>{};
    final lengths = <String, int>{};
    
    for (final case_ in cases) {
      strategies[case_.responseStrategy] = (strategies[case_.responseStrategy] ?? 0) + 1;
      tones[case_.responseTone] = (tones[case_.responseTone] ?? 0) + 1;
      lengths[case_.responseLength] = (lengths[case_.responseLength] ?? 0) + 1;
    }
    
    return {
      'strategy': _getMostCommon(strategies),
      'tone': _getMostCommon(tones),
      'length': _getMostCommon(lengths),
    };
  }

  static String _getMostCommon(Map<String, int> counts) {
    if (counts.isEmpty) return 'balanced';
    
    var maxCount = 0;
    var mostCommon = 'balanced';
    
    for (final entry in counts.entries) {
      if (entry.value > maxCount) {
        maxCount = entry.value;
        mostCommon = entry.key;
      }
    }
    
    return mostCommon;
  }

  static double _calculateOptimizationConfidence(List<TrainingDataPoint> cases) {
    if (cases.isEmpty) return 0.3;
    
    final averageSuccess = cases.map((c) => c.successRating).reduce((a, b) => a + b) / cases.length;
    final caseCount = cases.length;
    
    // Higher confidence with more cases and higher success rates
    return (averageSuccess * 0.7 + min(1.0, caseCount / 10.0) * 0.3).clamp(0.0, 1.0);
  }

  static double _calculateSuccessProbability(Map<String, String> patterns) {
    // Calculate probability based on pattern effectiveness
    final strategyPattern = _learningPatternsCache.values
        .where((p) => p.patternType == patterns['strategy'])
        .toList();
    
    if (strategyPattern.isNotEmpty) {
      return strategyPattern.first.successRate;
    }
    
    return 0.7; // Default probability
  }

  static List<String> _generateLearningInsights(Map<String, String> patterns) {
    final insights = <String>[];
    
    insights.add('Most effective strategy: ${patterns['strategy']}');
    insights.add('Optimal tone: ${patterns['tone']}');
    insights.add('Preferred length: ${patterns['length']}');
    
    return insights;
  }

  // Analysis methods
  static String _categorizeMessage(String message) {
    final lowerMessage = message.toLowerCase();

    if (lowerMessage.contains('?')) return 'question';
    if (lowerMessage.contains(RegExp(r'\b(help|support|advice)\b'))) return 'help_request';
    if (lowerMessage.contains(RegExp(r'\b(problem|issue|struggle)\b'))) return 'problem_sharing';
    if (lowerMessage.contains(RegExp(r'\b(goal|want|plan)\b'))) return 'goal_setting';
    if (lowerMessage.contains(RegExp(r'\b(feel|emotion|mood)\b'))) return 'emotional_sharing';
    
    return 'general';
  }

  static String _categorizeLength(int length) {
    if (length < 50) return 'short';
    if (length < 150) return 'medium';
    return 'long';
  }

  static String _analyzeSentiment(String text) {
    final lowerText = text.toLowerCase();
    
    final positiveWords = ['good', 'great', 'happy', 'excited', 'motivated'];
    final negativeWords = ['bad', 'sad', 'frustrated', 'overwhelmed', 'stressed'];
    
    final positiveCount = positiveWords.where((word) => lowerText.contains(word)).length;
    final negativeCount = negativeWords.where((word) => lowerText.contains(word)).length;
    
    if (positiveCount > negativeCount) return 'positive';
    if (negativeCount > positiveCount) return 'negative';
    return 'neutral';
  }

  static String _analyzeComplexity(String message) {
    final sentences = message.split(RegExp(r'[.!?]')).where((s) => s.trim().isNotEmpty).length;
    final words = message.split(' ').length;
    
    if (sentences > 3 || words > 50) return 'high';
    if (sentences > 1 || words > 20) return 'medium';
    return 'low';
  }

  static String _analyzeResponseStrategy(String response) {
    final lowerResponse = response.toLowerCase();

    if (lowerResponse.contains(RegExp(r'\b(question|ask|wonder)\b'))) return 'questioning';
    if (lowerResponse.contains(RegExp(r'\b(step|plan|action)\b'))) return 'actionable';
    if (lowerResponse.contains(RegExp(r'\b(understand|feel|empathy)\b'))) return 'empathetic';
    if (lowerResponse.contains(RegExp(r'\b(motivate|inspire|encourage)\b'))) return 'motivational';
    if (lowerResponse.contains(RegExp(r'\b(analyze|think|consider)\b'))) return 'analytical';
    
    return 'balanced';
  }

  static String _analyzeTone(String response) {
    final lowerResponse = response.toLowerCase();

    if (lowerResponse.contains(RegExp(r'\b(amazing|fantastic|incredible)\b'))) return 'enthusiastic';
    if (lowerResponse.contains(RegExp(r'\b(understand|support|care)\b'))) return 'supportive';
    if (lowerResponse.contains(RegExp(r'\b(challenge|push|achieve)\b'))) return 'challenging';
    if (lowerResponse.contains(RegExp(r'\b(gentle|calm|patient)\b'))) return 'gentle';
    
    return 'balanced';
  }

  static double _analyzeSatisfaction(String feedback) {
    final lowerFeedback = feedback.toLowerCase();
    
    if (lowerFeedback.contains(RegExp(r'\b(excellent|perfect|amazing|love)\b'))) return 1.0;
    if (lowerFeedback.contains(RegExp(r'\b(good|helpful|useful|thanks)\b'))) return 0.8;
    if (lowerFeedback.contains(RegExp(r'\b(okay|fine|alright)\b'))) return 0.6;
    if (lowerFeedback.contains(RegExp(r'\b(not helpful|bad|wrong)\b'))) return 0.2;
    
    return 0.5; // Default neutral
  }

  static Map<String, dynamic> _extractContextFactors(Map<String, dynamic> context) {
    return {
      'timeOfDay': DateTime.now().hour,
      'dayOfWeek': DateTime.now().weekday,
      'userMood': context['mood'] ?? 'neutral',
      'sessionLength': context['sessionLength'] ?? 'medium',
    };
  }

  static String _analyzeUrgency(String message) {
    final lowerMessage = message.toLowerCase();
    
    if (lowerMessage.contains(RegExp(r'\b(urgent|asap|immediately|emergency)\b'))) return 'high';
    if (lowerMessage.contains(RegExp(r'\b(soon|quickly|fast)\b'))) return 'medium';
    return 'low';
  }

  static String _analyzeEmotionalState(String message) {
    final lowerMessage = message.toLowerCase();
    
    if (lowerMessage.contains(RegExp(r'\b(stressed|overwhelmed|anxious)\b'))) return 'stressed';
    if (lowerMessage.contains(RegExp(r'\b(excited|motivated|energized)\b'))) return 'energized';
    if (lowerMessage.contains(RegExp(r'\b(confused|lost|uncertain)\b'))) return 'confused';
    if (lowerMessage.contains(RegExp(r'\b(sad|down|depressed)\b'))) return 'sad';
    
    return 'neutral';
  }

  static String _generateInsightFromPattern(LearningPattern pattern) {
    final successPercentage = (pattern.successRate * 100).toStringAsFixed(1);
    return 'Using ${pattern.patternType} approach for ${pattern.messageType} messages has $successPercentage% success rate';
  }

  static List<String> _getGeneralLearningInsights(String category) {
    switch (category) {
      case 'Health':
        return [
          'Motivational approaches work well for health goals',
          'Action-oriented responses increase engagement',
        ];
      case 'Wealth':
        return [
          'Analytical approaches are effective for financial topics',
          'Step-by-step guidance improves success rates',
        ];
      case 'Purpose':
        return [
          'Questioning approaches help with purpose exploration',
          'Empathetic responses work well for life direction topics',
        ];
      case 'Connection':
        return [
          'Supportive tones are most effective for relationship topics',
          'Emotional validation improves user satisfaction',
        ];
      default:
        return [
          'Balanced approaches work well for general topics',
          'Personalized responses increase effectiveness',
        ];
    }
  }

  // Storage and maintenance methods
  static Future<void> _loadTrainingData() async {
    try {
      final data = await _storage.read(key: _trainingDataKey);
      if (data != null) {
        final List<dynamic> jsonList = jsonDecode(data);
        _trainingDataCache.clear();
        _trainingDataCache.addAll(
          jsonList.map((json) => TrainingDataPoint.fromJson(json)).toList()
        );
      }
    } catch (e) {
      if (kDebugMode) print('❌ Failed to load training data: $e');
    }
  }

  static Future<void> _saveTrainingData() async {
    try {
      final jsonList = _trainingDataCache.map((point) => point.toJson()).toList();
      await _storage.write(key: _trainingDataKey, value: jsonEncode(jsonList));
    } catch (e) {
      if (kDebugMode) print('❌ Failed to save training data: $e');
    }
  }

  static Future<void> _loadLearningPatterns() async {
    try {
      final data = await _storage.read(key: _learningPatternsKey);
      if (data != null) {
        final Map<String, dynamic> jsonData = jsonDecode(data);
        _learningPatternsCache.clear();
        
        for (final entry in jsonData.entries) {
          _learningPatternsCache[entry.key] = LearningPattern.fromJson(entry.value);
        }
      }
    } catch (e) {
      if (kDebugMode) print('❌ Failed to load learning patterns: $e');
    }
  }

  static Future<void> _saveLearningPatterns() async {
    try {
      final jsonData = <String, dynamic>{};
      for (final entry in _learningPatternsCache.entries) {
        jsonData[entry.key] = entry.value.toJson();
      }
      await _storage.write(key: _learningPatternsKey, value: jsonEncode(jsonData));
    } catch (e) {
      if (kDebugMode) print('❌ Failed to save learning patterns: $e');
    }
  }

  static Future<void> _analyzeRecentPatterns() async {
    // Analyze patterns from recent data points
    final recentData = _trainingDataCache
        .where((d) => DateTime.now().difference(d.timestamp).inDays < 7)
        .toList();
    
    // Update pattern effectiveness based on recent data
    for (final dataPoint in recentData) {
      await _updateLearningPatterns(dataPoint);
    }
  }

  static Future<void> _updateLearningModels() async {
    // Update learning models based on accumulated patterns
    // This could include more sophisticated ML model updates in the future
  }

  static Future<void> _cleanOldData() async {
    // Remove data points older than 30 days
    final cutoffDate = DateTime.now().subtract(const Duration(days: 30));
    _trainingDataCache.removeWhere((d) => d.timestamp.isBefore(cutoffDate));
    
    await _saveTrainingData();
  }

  static String _calculateDataCollectionPeriod() {
    if (_trainingDataCache.isEmpty) return 'No data';
    
    final oldest = _trainingDataCache.map((d) => d.timestamp).reduce((a, b) => a.isBefore(b) ? a : b);
    final days = DateTime.now().difference(oldest).inDays;
    
    return '$days days';
  }

  static double _calculateImprovementTrend() {
    if (_trainingDataCache.length < 20) return 0.0;
    
    // Compare recent success rate to older success rate
    final recent = _trainingDataCache.skip(_trainingDataCache.length - 10).toList();
    final older = _trainingDataCache.take(10).toList();
    
    final recentSuccess = recent.map((d) => d.successRating).reduce((a, b) => a + b) / recent.length;
    final olderSuccess = older.map((d) => d.successRating).reduce((a, b) => a + b) / older.length;
    
    return recentSuccess - olderSuccess;
  }

  static String _generateId() {
    return DateTime.now().millisecondsSinceEpoch.toString() + 
           Random().nextInt(1000).toString();
  }
}

/// Represents a training data point
class TrainingDataPoint {
  final String id;
  final String messageType;
  final String messageLength;
  final String messageSentiment;
  final String messageComplexity;
  final String responseStrategy;
  final String responseLength;
  final String responseTone;
  final String category;
  final double successRating;
  final double userSatisfaction;
  final Map<String, dynamic> contextFactors;
  final DateTime timestamp;

  TrainingDataPoint({
    required this.id,
    required this.messageType,
    required this.messageLength,
    required this.messageSentiment,
    required this.messageComplexity,
    required this.responseStrategy,
    required this.responseLength,
    required this.responseTone,
    required this.category,
    required this.successRating,
    required this.userSatisfaction,
    required this.contextFactors,
    required this.timestamp,
  });

  factory TrainingDataPoint.fromJson(Map<String, dynamic> json) {
    return TrainingDataPoint(
      id: json['id'] as String,
      messageType: json['messageType'] as String,
      messageLength: json['messageLength'] as String,
      messageSentiment: json['messageSentiment'] as String,
      messageComplexity: json['messageComplexity'] as String,
      responseStrategy: json['responseStrategy'] as String,
      responseLength: json['responseLength'] as String,
      responseTone: json['responseTone'] as String,
      category: json['category'] as String,
      successRating: (json['successRating'] as num).toDouble(),
      userSatisfaction: (json['userSatisfaction'] as num).toDouble(),
      contextFactors: Map<String, dynamic>.from(json['contextFactors'] as Map),
      timestamp: DateTime.parse(json['timestamp'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'messageType': messageType,
      'messageLength': messageLength,
      'messageSentiment': messageSentiment,
      'messageComplexity': messageComplexity,
      'responseStrategy': responseStrategy,
      'responseLength': responseLength,
      'responseTone': responseTone,
      'category': category,
      'successRating': successRating,
      'userSatisfaction': userSatisfaction,
      'contextFactors': contextFactors,
      'timestamp': timestamp.toIso8601String(),
    };
  }
}

/// Represents a learned pattern
class LearningPattern {
  final String patternType;
  final String messageType;
  final String category;
  double successRate;
  int totalOccurrences;
  int successfulOccurrences;
  double averageUserSatisfaction;
  DateTime lastUpdated;

  LearningPattern({
    required this.patternType,
    required this.messageType,
    required this.category,
    required this.successRate,
    required this.totalOccurrences,
    required this.successfulOccurrences,
    required this.averageUserSatisfaction,
    required this.lastUpdated,
  });

  factory LearningPattern.fromJson(Map<String, dynamic> json) {
    return LearningPattern(
      patternType: json['patternType'] as String,
      messageType: json['messageType'] as String,
      category: json['category'] as String,
      successRate: (json['successRate'] as num).toDouble(),
      totalOccurrences: json['totalOccurrences'] as int,
      successfulOccurrences: json['successfulOccurrences'] as int,
      averageUserSatisfaction: (json['averageUserSatisfaction'] as num).toDouble(),
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'patternType': patternType,
      'messageType': messageType,
      'category': category,
      'successRate': successRate,
      'totalOccurrences': totalOccurrences,
      'successfulOccurrences': successfulOccurrences,
      'averageUserSatisfaction': averageUserSatisfaction,
      'lastUpdated': lastUpdated.toIso8601String(),
    };
  }
}

/// Response optimization recommendations
class ResponseOptimization {
  final String recommendedStrategy;
  final String recommendedTone;
  final String recommendedLength;
  final double confidenceLevel;
  final double successProbability;
  final List<String> learningInsights;
  final int basedOnCases;

  ResponseOptimization({
    required this.recommendedStrategy,
    required this.recommendedTone,
    required this.recommendedLength,
    required this.confidenceLevel,
    required this.successProbability,
    required this.learningInsights,
    required this.basedOnCases,
  });

  factory ResponseOptimization.defaultOptimization() {
    return ResponseOptimization(
      recommendedStrategy: 'balanced',
      recommendedTone: 'supportive',
      recommendedLength: 'moderate',
      confidenceLevel: 0.5,
      successProbability: 0.7,
      learningInsights: ['Using balanced approach'],
      basedOnCases: 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'recommendedStrategy': recommendedStrategy,
      'recommendedTone': recommendedTone,
      'recommendedLength': recommendedLength,
      'confidenceLevel': confidenceLevel,
      'successProbability': successProbability,
      'learningInsights': learningInsights,
      'basedOnCases': basedOnCases,
    };
  }
}
