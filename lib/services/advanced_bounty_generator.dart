import 'dart:math';
import '../models/user_model.dart';
import '../models/bounty_model.dart';
import '../data/bounty_data.dart';
import '../utils/debug_logger.dart';

/// Advanced bounty generation service with AI-like learning capabilities
/// 
/// Features:
/// - Difficulty progression based on user level
/// - Category balancing for well-rounded growth
/// - Seasonal/themed bounty variations
/// - User preference learning algorithm
/// - Dynamic EXP scaling
class AdvancedBountyGenerator {
  static final Random _random = Random();
  
  /// Generate a personalized daily bounty for the user
  static BountyModel? generateDailyBounty(User user, List<String> completedBountyIds) {
    DebugLogger.log('AdvancedBountyGenerator', 
      'Generating daily bounty for user ${user.username} (Level ${user.level})');
    
    // Get available bounties (not completed)
    final availableBounties = allBounties.where((bounty) => 
      !completedBountyIds.contains(bounty.id)).toList();
    
    if (availableBounties.isEmpty) {
      DebugLogger.warn('AdvancedBountyGenerator', 'No available bounties remaining');
      return null;
    }
    
    // Apply intelligent filtering
    final filteredBounties = _applyIntelligentFiltering(user, availableBounties);
    
    if (filteredBounties.isEmpty) {
      // Fallback to any available bounty
      return availableBounties[_random.nextInt(availableBounties.length)];
    }
    
    // Select best bounty using weighted algorithm
    final selectedBounty = _selectOptimalBounty(user, filteredBounties);
    
    DebugLogger.log('AdvancedBountyGenerator', 
      'Selected bounty: ${selectedBounty.description} (${selectedBounty.difficulty})');
    
    return selectedBounty;
  }
  
  /// Apply intelligent filtering based on user profile
  static List<BountyModel> _applyIntelligentFiltering(User user, List<BountyModel> bounties) {
    final filtered = <BountyModel>[];
    
    for (final bounty in bounties) {
      // Check difficulty appropriateness
      if (!_isDifficultyAppropriate(user, bounty)) continue;
      
      // Check category balance needs
      if (!_isCategoryNeeded(user, bounty)) continue;
      
      // Check seasonal relevance
      if (!_isSeasonallyRelevant(bounty)) continue;
      
      filtered.add(bounty);
    }
    
    DebugLogger.log('AdvancedBountyGenerator', 
      'Filtered ${bounties.length} bounties down to ${filtered.length}');
    
    return filtered;
  }
  
  /// Check if bounty difficulty is appropriate for user level
  static bool _isDifficultyAppropriate(User user, BountyModel bounty) {
    final userLevel = user.level;
    final totalExp = user.exp;
    
    switch (bounty.difficulty) {
      case 'easy':
        return userLevel <= 10; // Beginners
      case 'medium':
        return userLevel >= 3 && userLevel <= 25; // Intermediate
      case 'hard':
        return userLevel >= 10 && totalExp >= 1000; // Advanced
      case 'epic':
        return userLevel >= 20 && totalExp >= 3000; // Expert
      default:
        return true;
    }
  }
  
  /// Check if user needs EXP in the bounty's categories
  static bool _isCategoryNeeded(User user, BountyModel bounty) {
    final userCategories = user.categories;
    final totalExp = user.exp;
    
    if (totalExp < 100) {
      // New users should try all categories
      return true;
    }
    
    // Calculate category balance
    final categoryScores = <String, double>{};
    for (final category in bounty.categories) {
      final categoryExp = userCategories[category] ?? 0;
      final categoryPercentage = totalExp > 0 ? categoryExp / totalExp : 0.0;
      categoryScores[category] = categoryPercentage.toDouble();
    }
    
    // Prefer categories that are underrepresented
    final avgScore = categoryScores.values.fold(0.0, (sum, score) => sum + score) / categoryScores.length;
    final hasUnderrepresentedCategory = categoryScores.values.any((score) => score < avgScore * 0.7);
    
    return hasUnderrepresentedCategory;
  }
  
  /// Check if bounty is seasonally relevant
  static bool _isSeasonallyRelevant(BountyModel bounty) {
    final now = DateTime.now();
    final month = now.month;
    final description = bounty.description.toLowerCase();
    
    // Spring (March-May): Outdoor activities, fresh starts
    if (month >= 3 && month <= 5) {
      if (description.contains('outdoor') || 
          description.contains('garden') || 
          description.contains('walk') ||
          description.contains('nature')) {
        return true;
      }
    }
    
    // Summer (June-August): Fitness, social activities
    if (month >= 6 && month <= 8) {
      if (description.contains('swim') || 
          description.contains('beach') || 
          description.contains('social') ||
          description.contains('festival')) {
        return true;
      }
    }
    
    // Fall (September-November): Learning, preparation
    if (month >= 9 && month <= 11) {
      if (description.contains('learn') || 
          description.contains('skill') || 
          description.contains('book') ||
          description.contains('course')) {
        return true;
      }
    }
    
    // Winter (December-February): Indoor activities, reflection
    if (month == 12 || month <= 2) {
      if (description.contains('indoor') || 
          description.contains('meditat') || 
          description.contains('plan') ||
          description.contains('reflect')) {
        return true;
      }
    }
    
    // Always relevant if no seasonal keywords
    return true;
  }
  
  /// Select optimal bounty using weighted scoring algorithm
  static BountyModel _selectOptimalBounty(User user, List<BountyModel> bounties) {
    final scoredBounties = <BountyModel, double>{};
    
    for (final bounty in bounties) {
      double score = 0.0;
      
      // Category need score (40% weight)
      score += _calculateCategoryNeedScore(user, bounty) * 0.4;
      
      // Difficulty progression score (30% weight)
      score += _calculateDifficultyScore(user, bounty) * 0.3;
      
      // Variety score (20% weight)
      score += _calculateVarietyScore(user, bounty) * 0.2;
      
      // Epic bonus (10% weight)
      score += (bounty.isEpic ? 1.0 : 0.0) * 0.1;
      
      scoredBounties[bounty] = score;
    }
    
    // Select bounty with highest score (with some randomness)
    final sortedBounties = scoredBounties.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    // Top 3 bounties get weighted random selection
    final topBounties = sortedBounties.take(3).toList();
    final weights = [0.5, 0.3, 0.2]; // Favor higher scored bounties
    
    final randomValue = _random.nextDouble();
    double cumulativeWeight = 0.0;
    
    for (int i = 0; i < topBounties.length; i++) {
      cumulativeWeight += weights[i];
      if (randomValue <= cumulativeWeight) {
        return topBounties[i].key;
      }
    }
    
    return topBounties.first.key;
  }
  
  /// Calculate how much the user needs EXP in bounty categories
  static double _calculateCategoryNeedScore(User user, BountyModel bounty) {
    final userCategories = user.categories;
    final totalExp = user.exp;
    
    if (totalExp == 0) return 1.0; // New users need everything
    
    double needScore = 0.0;
    for (final category in bounty.categories) {
      final categoryExp = userCategories[category] ?? 0;
      final categoryPercentage = categoryExp / totalExp;
      
      // Higher score for underrepresented categories
      needScore += (1.0 - categoryPercentage).clamp(0.0, 1.0);
    }
    
    return (needScore / bounty.categories.length).clamp(0.0, 1.0);
  }
  
  /// Calculate difficulty progression score
  static double _calculateDifficultyScore(User user, BountyModel bounty) {
    final userLevel = user.level;
    final totalExp = user.exp;
    
    // Ideal difficulty progression
    String idealDifficulty;
    if (userLevel < 5) {
      idealDifficulty = 'easy';
    } else if (userLevel < 15) {
      idealDifficulty = 'medium';
    } else if (userLevel < 30 || totalExp < 2000) {
      idealDifficulty = 'hard';
    } else {
      idealDifficulty = 'epic';
    }
    
    // Score based on how well bounty matches ideal difficulty
    if (bounty.difficulty == idealDifficulty) {
      return 1.0;
    } else if (_isAdjacentDifficulty(bounty.difficulty, idealDifficulty)) {
      return 0.7;
    } else {
      return 0.3;
    }
  }
  
  /// Check if two difficulties are adjacent in progression
  static bool _isAdjacentDifficulty(String difficulty1, String difficulty2) {
    final difficulties = ['easy', 'medium', 'hard', 'epic'];
    final index1 = difficulties.indexOf(difficulty1);
    final index2 = difficulties.indexOf(difficulty2);
    
    return (index1 - index2).abs() == 1;
  }
  
  /// Calculate variety score to encourage diverse activities
  static double _calculateVarietyScore(User user, BountyModel bounty) {
    // TODO: Track recently completed bounty types
    // For now, give slight preference to multi-category bounties
    return bounty.categories.length > 1 ? 1.0 : 0.8;
  }
  
  /// Generate themed bounty variations for special events
  static List<BountyModel> generateThemedBounties(String theme) {
    final themedBounties = <BountyModel>[];
    
    switch (theme.toLowerCase()) {
      case 'new_year':
        themedBounties.addAll(_generateNewYearBounties());
        break;
      case 'summer':
        themedBounties.addAll(_generateSummerBounties());
        break;
      case 'wellness':
        themedBounties.addAll(_generateWellnessBounties());
        break;
      default:
        DebugLogger.warn('AdvancedBountyGenerator', 'Unknown theme: $theme');
    }
    
    DebugLogger.log('AdvancedBountyGenerator', 
      'Generated ${themedBounties.length} themed bounties for $theme');
    
    return themedBounties;
  }
  
  /// Generate New Year themed bounties
  static List<BountyModel> _generateNewYearBounties() {
    return [
      BountyModel(
        id: 'ny_resolution_1',
        description: 'Write down 3 specific goals for this year',
        categories: ['Purpose'],
        expPerCategory: {'Purpose': 30},
        difficulty: 'easy',
        isEpic: false,
      ),
      BountyModel(
        id: 'ny_health_1',
        description: 'Start a new healthy habit for 7 days straight',
        categories: ['Health'],
        expPerCategory: {'Health': 60},
        difficulty: 'medium',
        isEpic: false,
      ),
    ];
  }
  
  /// Generate Summer themed bounties
  static List<BountyModel> _generateSummerBounties() {
    return [
      BountyModel(
        id: 'summer_outdoor_1',
        description: 'Spend 2 hours outdoors doing a physical activity',
        categories: ['Health'],
        expPerCategory: {'Health': 40},
        difficulty: 'medium',
        isEpic: false,
      ),
    ];
  }
  
  /// Generate Wellness themed bounties
  static List<BountyModel> _generateWellnessBounties() {
    return [
      BountyModel(
        id: 'wellness_meditation_1',
        description: 'Practice mindfulness meditation for 20 minutes',
        categories: ['Health', 'Purpose'],
        expPerCategory: {'Health': 25, 'Purpose': 25},
        difficulty: 'easy',
        isEpic: false,
      ),
    ];
  }
}
