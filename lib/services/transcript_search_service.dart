// lib/services/transcript_search_service.dart

import 'dart:async';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'transcript_service.dart';

/// Advanced search service for finding relevant transcript content
/// Provides real-time, context-aware search across all transcript files
class TranscriptSearchService {
  static final Map<String, List<TranscriptSegment>> _segmentCache = {};
  static final Map<String, double> _searchCache = {};
  static bool _isInitialized = false;

  /// Initialize the search service by processing all transcripts
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // SUPERINTELLIGENT FIX: Load actual transcript content, not placeholders
      final transcriptContent = await _loadActualTranscriptContent();
      await _processTranscriptsIntoSegments(transcriptContent);
      _isInitialized = true;

      if (kDebugMode) {
        print('🔍 TranscriptSearchService initialized with ${_segmentCache.length} segments');
        print('📚 Loaded ${transcriptContent.length} actual transcript files');
      }
    } catch (e) {
      if (kDebugMode) print('❌ Failed to initialize TranscriptSearchService: $e');
    }
  }

  /// Load actual transcript content for superintelligent processing (MEMORY OPTIMIZED)
  static Future<List<String>> _loadActualTranscriptContent() async {
    final transcriptContent = <String>[];

    try {
      // Get list of transcript files
      final transcriptFiles = await TranscriptService.getTranscriptFileList();

      if (kDebugMode) {
        print('🔍 Loading ${transcriptFiles.length} transcript files for superintelligent processing...');
      }

      // MEMORY OPTIMIZATION: Load files in smaller batches to reduce memory pressure
      const batchSize = 5; // Process 5 files at a time
      var totalChars = 0;

      for (int i = 0; i < transcriptFiles.length; i += batchSize) {
        final batch = transcriptFiles.skip(i).take(batchSize);

        // Process batch
        for (final filePath in batch) {
          try {
            final content = await TranscriptService.loadTranscriptContent(filePath);

            // Only add if it's actual content, not a placeholder
            if (content.isNotEmpty && !content.startsWith('📄 Transcript Available:')) {
              transcriptContent.add(content);
              totalChars += content.length;

              if (kDebugMode) {
                final fileName = filePath.split('/').last;
                print('✅ Loaded: $fileName (${content.length} chars)');
              }
            }
          } catch (e) {
            if (kDebugMode) {
              print('⚠️ Failed to load transcript: $filePath - $e');
            }
          }
        }

        // MEMORY MANAGEMENT: Small delay between batches to allow garbage collection
        if (i + batchSize < transcriptFiles.length) {
          await Future.delayed(const Duration(milliseconds: 100));
        }
      }

      // Also load downloaded YouTube transcripts (these are smaller)
      final downloadedTranscripts = await TranscriptService.loadDownloadedTranscripts();
      transcriptContent.addAll(downloadedTranscripts);

      if (kDebugMode) {
        print('🧠 SUPERINTELLIGENT TRANSCRIPT LOADING COMPLETE:');
        print('   📚 Files loaded: ${transcriptContent.length}');
        print('   📝 Total characters: $totalChars');
        print('   🚀 Ready for intelligent synthesis!');
      }

    } catch (e) {
      if (kDebugMode) print('❌ Failed to load transcript content: $e');
    }

    return transcriptContent;
  }

  /// Search for relevant transcript content based on user query
  static Future<List<SearchResult>> searchRelevantContent({
    required String userQuery,
    required String category,
    int maxResults = 5,
    double minRelevanceScore = 0.3,
  }) async {
    await initialize();
    
    if (userQuery.trim().isEmpty) return [];
    
    final searchTerms = _extractSearchTerms(userQuery);
    final results = <SearchResult>[];
    
    // Search through all cached segments
    for (final entry in _segmentCache.entries) {
      final segments = entry.value;
      
      for (final segment in segments) {
        final relevanceScore = _calculateRelevanceScore(
          segment: segment,
          searchTerms: searchTerms,
          userQuery: userQuery,
          category: category,
        );
        
        if (relevanceScore >= minRelevanceScore) {
          results.add(SearchResult(
            content: segment.content,
            source: segment.source,
            relevanceScore: relevanceScore,
            keywords: segment.keywords,
            context: segment.context,
          ));
        }
      }
    }
    
    // Sort by relevance and return top results
    results.sort((a, b) => b.relevanceScore.compareTo(a.relevanceScore));
    return results.take(maxResults).toList();
  }

  /// Extract meaningful search terms from user query
  static List<String> _extractSearchTerms(String query) {
    final stopWords = {
      'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for',
      'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'being',
      'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could',
      'should', 'may', 'might', 'can', 'i', 'you', 'he', 'she', 'it', 'we',
      'they', 'me', 'him', 'her', 'us', 'them', 'my', 'your', 'his',
      'its', 'our', 'their', 'this', 'that', 'these', 'those'
    };
    
    return query
        .toLowerCase()
        .replaceAll(RegExp(r'[^\w\s]'), ' ')
        .split(RegExp(r'\s+'))
        .where((word) => word.length > 2 && !stopWords.contains(word))
        .toList();
  }

  /// Process transcripts into searchable segments
  static Future<void> _processTranscriptsIntoSegments(List<String> transcripts) async {
    _segmentCache.clear();
    
    for (int i = 0; i < transcripts.length; i++) {
      final transcript = transcripts[i];
      final source = 'transcript_$i';
      
      // Split transcript into meaningful segments (paragraphs or sentences)
      final segments = _segmentTranscript(transcript, source);
      _segmentCache[source] = segments;
    }
  }

  /// Split transcript into searchable segments
  static List<TranscriptSegment> _segmentTranscript(String transcript, String source) {
    final segments = <TranscriptSegment>[];
    
    // Split by paragraphs first, then by sentences if paragraphs are too long
    final paragraphs = transcript.split(RegExp(r'\n\s*\n'));
    
    for (final paragraph in paragraphs) {
      if (paragraph.trim().isEmpty) continue;
      
      if (paragraph.length <= 500) {
        // Use whole paragraph if it's reasonable length
        segments.add(_createSegment(paragraph.trim(), source));
      } else {
        // Split long paragraphs into sentences
        final sentences = paragraph.split(RegExp(r'[.!?]+\s+'));
        String currentSegment = '';
        
        for (final sentence in sentences) {
          if (currentSegment.length + sentence.length <= 500) {
            currentSegment += (currentSegment.isEmpty ? '' : '. ') + sentence.trim();
          } else {
            if (currentSegment.isNotEmpty) {
              segments.add(_createSegment(currentSegment, source));
            }
            currentSegment = sentence.trim();
          }
        }
        
        if (currentSegment.isNotEmpty) {
          segments.add(_createSegment(currentSegment, source));
        }
      }
    }
    
    return segments;
  }

  /// Create a transcript segment with metadata
  static TranscriptSegment _createSegment(String content, String source) {
    final keywords = _extractKeywords(content);
    final context = _extractContext(content);
    
    return TranscriptSegment(
      content: content,
      source: source,
      keywords: keywords,
      context: context,
    );
  }

  /// Extract keywords from content
  static List<String> _extractKeywords(String content) {
    return _extractSearchTerms(content)
        .where((word) => word.length > 3)
        .take(10)
        .toList();
  }

  /// Extract context/topic from content
  static String _extractContext(String content) {
    // Simple context extraction - could be enhanced with NLP
    final words = content.toLowerCase().split(RegExp(r'\s+'));
    
    // Look for topic indicators
    final topicWords = {
      'health': ['health', 'fitness', 'exercise', 'nutrition', 'diet', 'workout', 'training'],
      'wealth': ['money', 'wealth', 'business', 'investment', 'financial', 'income', 'profit'],
      'purpose': ['purpose', 'meaning', 'goal', 'mission', 'vision', 'passion', 'calling'],
      'connection': ['relationship', 'connection', 'social', 'communication', 'friendship', 'love'],
      'mindset': ['mindset', 'mental', 'psychology', 'thinking', 'belief', 'attitude'],
      'productivity': ['productivity', 'efficiency', 'time', 'focus', 'discipline', 'habit'],
    };
    
    for (final entry in topicWords.entries) {
      final topic = entry.key;
      final indicators = entry.value;
      
      if (indicators.any((indicator) => words.contains(indicator))) {
        return topic;
      }
    }
    
    return 'general';
  }

  /// Calculate relevance score for a segment
  static double _calculateRelevanceScore({
    required TranscriptSegment segment,
    required List<String> searchTerms,
    required String userQuery,
    required String category,
  }) {
    double score = 0.0;
    final contentLower = segment.content.toLowerCase();
    final keywordsLower = segment.keywords.map((k) => k.toLowerCase()).toList();
    
    // Exact phrase match (highest weight)
    if (contentLower.contains(userQuery.toLowerCase())) {
      score += 1.0;
    }
    
    // Individual term matches
    for (final term in searchTerms) {
      // Direct content match
      if (contentLower.contains(term)) {
        score += 0.5;
      }
      
      // Keyword match
      if (keywordsLower.contains(term)) {
        score += 0.3;
      }
    }
    
    // Context relevance bonus
    if (segment.context == category.toLowerCase()) {
      score += 0.2;
    }
    
    // Length penalty for very short segments
    if (segment.content.length < 50) {
      score *= 0.8;
    }
    
    // Quality bonus for longer, substantial content
    if (segment.content.length > 200) {
      score += 0.1;
    }
    
    return min(score, 2.0); // Cap at 2.0
  }

  /// Get search statistics
  static Map<String, dynamic> getSearchStats() {
    return {
      'totalSegments': _segmentCache.values.fold<int>(0, (sum, segments) => sum + segments.length),
      'totalSources': _segmentCache.length,
      'cacheSize': _searchCache.length,
      'isInitialized': _isInitialized,
    };
  }

  /// Get all available content for emergency searches
  static Future<List<SearchResult>> getAllContent() async {
    await initialize();

    final allResults = <SearchResult>[];

    for (final entry in _segmentCache.entries) {
      final segments = entry.value;

      for (final segment in segments) {
        allResults.add(SearchResult(
          content: segment.content,
          source: segment.source,
          relevanceScore: 0.5, // Default relevance for emergency use
          keywords: segment.keywords,
          context: segment.context,
        ));
      }
    }

    return allResults;
  }

  /// Clear cache (useful for testing or memory management)
  static void clearCache() {
    _segmentCache.clear();
    _searchCache.clear();
    _isInitialized = false;
  }
}

/// Represents a searchable segment of transcript content
class TranscriptSegment {
  final String content;
  final String source;
  final List<String> keywords;
  final String context;

  const TranscriptSegment({
    required this.content,
    required this.source,
    required this.keywords,
    required this.context,
  });
}

/// Represents a search result with relevance scoring
class SearchResult {
  final String content;
  final String source;
  final double relevanceScore;
  final List<String> keywords;
  final String context;

  const SearchResult({
    required this.content,
    required this.source,
    required this.relevanceScore,
    required this.keywords,
    required this.context,
  });

  @override
  String toString() {
    return 'SearchResult(score: ${relevanceScore.toStringAsFixed(2)}, context: $context, content: ${content.substring(0, min(50, content.length))}...)';
  }
}
