// lib/services/music_service.dart

import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/foundation.dart';
import 'dart:math';

// Music service debugging removed - service is fully operational

/// Track information for music from Heimanu and Gamera
class Track {
  final String filename;
  final String title;
  final String artist;
  final String assetPath;

  const Track({
    required this.filename,
    required this.title,
    required this.artist,
    required this.assetPath,
  });
}

/// Enhanced music service with Heimanu and Gamera tracks and full player controls
///
/// Features:
/// - Lazy loading for fast app startup
/// - Random track selection on startup
/// - Full player controls (play, pause, next, previous, restart)
/// - Shuffle mode
/// - Track selection
/// - Volume control
/// - Real-time track information
class MusicService with ChangeNotifier {
  static final MusicService _instance = MusicService._internal();
  factory MusicService() => _instance;

  AudioPlayer? _player;
  bool _isInitialized = false;
  bool _isPlaying = false;
  bool _isShuffleEnabled = false;
  double _volume = 0.7; // Default to 70% volume
  int _currentTrackIndex = 0;
  Duration _currentPosition = Duration.zero;
  Duration _totalDuration = Duration.zero;
  bool _initializationInProgress = false;

  // Combined track collection from Heimanu and Gamera
  static const List<Track> _tracks = [
    // Heimanu's tracks
    Track(
      filename: 'Heimanu - After You Left.m4a',
      title: 'After You Left',
      artist: 'Heimanu',
      assetPath: 'sounds/Heimanu_Tracks/Heimanu - After You Left.m4a',
    ),
    Track(
      filename: 'Heimanu - All I Knew.m4a',
      title: 'All I Knew',
      artist: 'Heimanu',
      assetPath: 'sounds/Heimanu_Tracks/Heimanu - All I Knew.m4a',
    ),
    Track(
      filename: 'Heimanu - Away.m4a',
      title: 'Away',
      artist: 'Heimanu',
      assetPath: 'sounds/Heimanu_Tracks/Heimanu - Away.m4a',
    ),
    Track(
      filename: 'Heimanu - Empty.m4a',
      title: 'Empty',
      artist: 'Heimanu',
      assetPath: 'sounds/Heimanu_Tracks/Heimanu - Empty.m4a',
    ),
    Track(
      filename: 'Heimanu - Fixation.m4a',
      title: 'Fixation',
      artist: 'Heimanu',
      assetPath: 'sounds/Heimanu_Tracks/Heimanu - Fixation.m4a',
    ),
    Track(
      filename: 'Heimanu - Grief.m4a',
      title: 'Grief',
      artist: 'Heimanu',
      assetPath: 'sounds/Heimanu_Tracks/Heimanu - Grief.m4a',
    ),
    Track(
      filename: 'Heimanu - I Watch The Moon.m4a',
      title: 'I Watch The Moon',
      artist: 'Heimanu',
      assetPath: 'sounds/Heimanu_Tracks/Heimanu - I Watch The Moon.m4a',
    ),
    Track(
      filename: 'Heimanu - Leave You.m4a',
      title: 'Leave You',
      artist: 'Heimanu',
      assetPath: 'sounds/Heimanu_Tracks/Heimanu - Leave You.m4a',
    ),
    Track(
      filename: 'Heimanu - Night Rider with TWERL.m4a',
      title: 'Night Rider (with TWERL)',
      artist: 'Heimanu',
      assetPath: 'sounds/Heimanu_Tracks/Heimanu - Night Rider with TWERL.m4a',
    ),
    Track(
      filename: 'Heimanu - RED STAR.m4a',
      title: 'RED STAR',
      artist: 'Heimanu',
      assetPath: 'sounds/Heimanu_Tracks/Heimanu - RED STAR.m4a',
    ),
    Track(
      filename: 'Heimanu - Saviour.m4a',
      title: 'Saviour',
      artist: 'Heimanu',
      assetPath: 'sounds/Heimanu_Tracks/Heimanu - Saviour.m4a',
    ),
    Track(
      filename: 'Heimanu - TORMENT.m4a',
      title: 'TORMENT',
      artist: 'Heimanu',
      assetPath: 'sounds/Heimanu_Tracks/Heimanu - TORMENT.m4a',
    ),
    Track(
      filename: 'Heimanu - maybe it gets better.m4a',
      title: 'maybe it gets better',
      artist: 'Heimanu',
      assetPath: 'sounds/Heimanu_Tracks/Heimanu - maybe it gets better.m4a',
    ),

    // Gamera's tracks - Fixed asset paths for Flutter
    Track(
      filename: 'Gamera - Nocturnal - 01 Dark City.m4a',
      title: 'Dark City',
      artist: 'Gamera',
      assetPath: 'sounds/Gamera_Tracks/Gamera - Nocturnal - 01 Dark City.m4a',
    ),
    Track(
      filename: 'Gamera - Nocturnal - 02 Crescent.m4a',
      title: 'Crescent',
      artist: 'Gamera',
      assetPath: 'sounds/Gamera_Tracks/Gamera - Nocturnal - 02 Crescent.m4a',
    ),
    Track(
      filename: 'Gamera - Nocturnal - 03 Turtle Dove.m4a',
      title: 'Turtle Dove',
      artist: 'Gamera',
      assetPath: 'sounds/Gamera_Tracks/Gamera - Nocturnal - 03 Turtle Dove.m4a',
    ),
    Track(
      filename: 'Gamera - Nocturnal - 04 Opera.m4a',
      title: 'Opera',
      artist: 'Gamera',
      assetPath: 'sounds/Gamera_Tracks/Gamera - Nocturnal - 04 Opera.m4a',
    ),
    Track(
      filename: 'Gamera - Nocturnal - 05 Feather Pen.m4a',
      title: 'Feather Pen',
      artist: 'Gamera',
      assetPath: 'sounds/Gamera_Tracks/Gamera - Nocturnal - 05 Feather Pen.m4a',
    ),
    Track(
      filename: 'Gamera - Nocturnal - 06 Nocturnal.m4a',
      title: 'Nocturnal',
      artist: 'Gamera',
      assetPath: 'sounds/Gamera_Tracks/Gamera - Nocturnal - 06 Nocturnal.m4a',
    ),
  ];

  MusicService._internal() {
    // Lazy initialization - player will be created when first needed
  }

  /// Initialize the audio player and set up listeners (lazy loading)
  Future<void> _ensureInitialized() async {
    if (_isInitialized || _initializationInProgress) return;

    _initializationInProgress = true;

    try {
      _player = AudioPlayer();
      await _player!.setVolume(_volume);

      // Listen to player state changes
      _player!.onPlayerStateChanged.listen((state) {
        _isPlaying = state == PlayerState.playing;
        notifyListeners();
      });

      // Listen to position changes
      _player!.onPositionChanged.listen((position) {
        _currentPosition = position;
        notifyListeners();
      });

      // Listen to duration changes
      _player!.onDurationChanged.listen((duration) {
        _totalDuration = duration;
        notifyListeners();
      });

      // Auto-advance to next track when current track completes
      _player!.onPlayerComplete.listen((_) {
        nextTrack();
      });

      _isInitialized = true;
      _initializationInProgress = false;
    } catch (e) {
      _initializationInProgress = false;
      rethrow;
    }
  }

  /// Initialize and play a random track on startup (lazy loading)
  Future<void> initAndPlay() async {
    if (_isPlaying) {
      return;
    }

    try {
      // Ensure player is initialized
      await _ensureInitialized();

      // Select a random track for startup
      _currentTrackIndex = Random().nextInt(_tracks.length);
      await _playCurrentTrack();
    } catch (e) {
      // Silent error handling - music service is fully operational
    }
  }

  /// Play the current track with enhanced Gamera support
  Future<void> _playCurrentTrack() async {
    try {
      await _ensureInitialized();

      // Enhanced asset path handling for both Heimanu and Gamera tracks
      final assetPath = currentTrack.assetPath;
      final isGameraTrack = currentTrack.artist == 'Gamera';

      try {
        await _player!.play(AssetSource(assetPath));
        _isPlaying = true;
        notifyListeners();
        return;
      } catch (assetError) {
        // Try alternative path without "assets/" prefix
        final alternativePath = assetPath.replaceFirst('assets/', '');

        try {
          await _player!.play(AssetSource(alternativePath));
          _isPlaying = true;
          notifyListeners();
          return;
        } catch (altError) {
          // For Gamera tracks, try additional path variations
          if (isGameraTrack) {

            // Try with different path formats for Gamera
            final gameraVariations = [
              'sounds/Gamera_Tracks/${currentTrack.filename}',
              'assets/sounds/Gamera_Tracks/${currentTrack.filename}',
              'Gamera_Tracks/${currentTrack.filename}',
            ];

            for (final variation in gameraVariations) {
              try {
                await _player!.play(AssetSource(variation));
                _isPlaying = true;
                notifyListeners();
                return;
              } catch (variationError) {
                // Continue to next variation
              }
            }
          }

          throw assetError; // Throw original error if all attempts fail
        }
      }
    } catch (e) {
      // Try next track if current fails (but avoid infinite loop)
      if (_currentTrackIndex < _tracks.length - 1) {
        final originalIndex = _currentTrackIndex;
        _currentTrackIndex++;

        // Prevent infinite recursion by limiting attempts
        if (_currentTrackIndex - originalIndex < 5) {
          await _playCurrentTrack();
        }
      }
    }
  }

  // Getters
  Track get currentTrack => _tracks[_currentTrackIndex];
  bool get isPlaying => _isPlaying;
  bool get isShuffleEnabled => _isShuffleEnabled;
  double get volume => _volume;
  Duration get currentPosition => _currentPosition;
  Duration get totalDuration => _totalDuration;
  List<Track> get tracks => _tracks;
  int get currentTrackIndex => _currentTrackIndex;

  Future<void> pause() async {
    await _ensureInitialized();
    if (!_isPlaying) {
      return;
    }
    await _player!.pause();
    _isPlaying = false;
    notifyListeners();
  }

  Future<void> resume() async {
    await _ensureInitialized();
    if (_isPlaying) {
      return;
    }
    await _player!.resume();
    _isPlaying = true;
    notifyListeners();
  }

  Future<void> togglePlayPause() async {
    if (_isPlaying) {
      await pause();
    } else {
      await resume();
    }
  }

  Future<void> setVolume(double value) async {
    _volume = value.clamp(0.0, 1.0);
    if (_isInitialized && _player != null) {
      await _player!.setVolume(_volume);
    }
    notifyListeners();
  }

  // Duration formatting removed - not needed without debugging

  /// Go to next track
  Future<void> nextTrack() async {
    if (_isShuffleEnabled) {
      _currentTrackIndex = Random().nextInt(_tracks.length);
    } else {
      _currentTrackIndex = (_currentTrackIndex + 1) % _tracks.length;
    }
    await _playCurrentTrack();
  }

  /// Go to previous track
  Future<void> previousTrack() async {
    if (_isShuffleEnabled) {
      _currentTrackIndex = Random().nextInt(_tracks.length);
    } else {
      _currentTrackIndex = (_currentTrackIndex - 1 + _tracks.length) % _tracks.length;
    }
    await _playCurrentTrack();
  }

  /// Restart current track
  Future<void> restartTrack() async {
    await _ensureInitialized();
    await _player!.seek(Duration.zero);
  }

  /// Toggle shuffle mode
  void toggleShuffle() {
    _isShuffleEnabled = !_isShuffleEnabled;
    notifyListeners();
  }

  /// Play specific track by index
  Future<void> playTrack(int index) async {
    if (index >= 0 && index < _tracks.length) {
      _currentTrackIndex = index;
      await _playCurrentTrack();
    }
  }

  /// Seek to specific position
  Future<void> seekTo(Duration position) async {
    await _ensureInitialized();
    await _player!.seek(position);
  }

  /// Force play a specific track by index (for testing)
  Future<void> forcePlayTrack(int index) async {
    if (index < 0 || index >= _tracks.length) {
      return;
    }

    _currentTrackIndex = index;
    await _playCurrentTrack();
  }

  /// Test all Gamera tracks specifically
  Future<void> testGameraTracks() async {
    // Music debugging disabled - service is fully operational
    final gameraTracks = _tracks.where((track) => track.artist == 'Gamera').toList();

    for (int i = 0; i < gameraTracks.length; i++) {
      final track = gameraTracks[i];
      final trackIndex = _tracks.indexOf(track);
      await testTrackLoading(trackIndex);
    }
  }

  /// Get all Gamera track indices
  List<int> getGameraTrackIndices() {
    final indices = <int>[];
    for (int i = 0; i < _tracks.length; i++) {
      if (_tracks[i].artist == 'Gamera') {
        indices.add(i);
      }
    }
    return indices;
  }

  /// Force play the first Gamera track for testing
  Future<void> forcePlayGameraTrack() async {
    final gameraIndices = getGameraTrackIndices();
    if (gameraIndices.isNotEmpty) {
      await forcePlayTrack(gameraIndices.first);
    }
  }

  /// Get track info for debugging
  String getTrackInfo(int index) {
    if (index < 0 || index >= _tracks.length) {
      return 'Invalid index: $index';
    }
    final track = _tracks[index];
    return 'Track $index: "${track.title}" by ${track.artist} (${track.filename})';
  }

  /// Debug method to test if a specific track can be loaded
  Future<bool> testTrackLoading(int trackIndex) async {
    if (trackIndex < 0 || trackIndex >= _tracks.length) {
      return false;
    }

    final track = _tracks[trackIndex];

    try {
      await _ensureInitialized();
      final testPlayer = AudioPlayer();

      try {
        await testPlayer.setSource(AssetSource(track.assetPath));
        await testPlayer.dispose();
        return true;
      } catch (e) {
        await testPlayer.dispose();
        return false;
      }
    } catch (e) {
      return false;
    }
  }

  /// Play a one-shot sound effect (e.g. lightning).
  static Future<void> playEffect(String assetPath) async {
    final effectPlayer = AudioPlayer();
    try {
      final src = assetPath.startsWith('assets/') ? assetPath.substring(7) : assetPath;
      await effectPlayer.play(AssetSource(src));
    } catch (e) {
      debugPrint('MusicService: error playing effect: $e');
    }
  }

  Future<void> disposePlayer() async {
    if (_player != null) {
      await _player!.stop();
      _player!.dispose();
      _player = null;
    }
    _isPlaying = false;
    _isInitialized = false;
    notifyListeners();
  }
}
