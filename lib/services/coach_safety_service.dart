// 📁 lib/services/coach_safety_service.dart

import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

import 'feature_flag_service.dart';

/// Enterprise-grade safety service providing circuit breakers, rate limiting,
/// automatic failover, and proactive protection for high-load scenarios.
class CoachSafetyService {
  static final CoachSafetyService _instance = CoachSafetyService._internal();
  factory CoachSafetyService() => _instance;
  CoachSafetyService._internal();

  static const FlutterSecureStorage _storage = FlutterSecureStorage();
  static const String _circuitBreakerKey = 'circuit_breaker_state';
  static const String _rateLimitKey = 'rate_limit_state';
  
  // Circuit breaker configuration
  static const int _failureThreshold = 5; // Failures before opening circuit
  static const Duration _circuitTimeout = Duration(minutes: 5); // Recovery time
  static const int _successThreshold = 3; // Successes needed to close circuit
  
  // Rate limiting configuration
  static const int _globalRateLimit = 1000; // Requests per minute globally
  static const int _userRateLimit = 10; // Requests per minute per user
  static const Duration _rateLimitWindow = Duration(minutes: 1);
  
  // Circuit breaker state
  static CircuitBreakerState _circuitState = CircuitBreakerState.closed;
  static int _failureCount = 0;
  static int _successCount = 0;
  static DateTime? _lastFailureTime;
  static DateTime? _circuitOpenTime;
  
  // Rate limiting state
  static final Map<String, List<DateTime>> _userRequestHistory = {};
  static final List<DateTime> _globalRequestHistory = [];
  
  // Safety metrics
  static int _totalRequests = 0;
  static int _blockedRequests = 0;
  static int _failedRequests = 0;
  
  /// Initialize safety service
  static Future<void> initialize() async {
    try {
      await _loadCircuitBreakerState();
      await _loadRateLimitState();
      _startCleanupTimer();
      
      if (kDebugMode) {
        print('🛡️ Coach safety service initialized');
        print('⚡ Circuit breaker: ${_circuitState.name}');
        print('🚦 Rate limiting: Active');
        print('🔒 Safety thresholds: $_failureThreshold failures, $_globalRateLimit global RPM');
      }
    } catch (e) {
      if (kDebugMode) print('❌ Failed to initialize safety service: $e');
    }
  }

  /// Check if request is safe to proceed (circuit breaker + rate limiting)
  static Future<SafetyCheckResult> checkRequestSafety({
    required String userId,
    required String category,
    required String operation,
  }) async {
    try {
      _totalRequests++;
      
      // Check circuit breaker
      final circuitCheck = _checkCircuitBreaker();
      if (!circuitCheck.allowed) {
        _blockedRequests++;
        return SafetyCheckResult(
          allowed: false,
          reason: circuitCheck.reason,
          retryAfter: circuitCheck.retryAfter,
          safetyLevel: SafetyLevel.blocked,
        );
      }
      
      // Check rate limits
      final rateLimitCheck = await _checkRateLimit(userId);
      if (!rateLimitCheck.allowed) {
        _blockedRequests++;
        return SafetyCheckResult(
          allowed: false,
          reason: rateLimitCheck.reason,
          retryAfter: rateLimitCheck.retryAfter,
          safetyLevel: SafetyLevel.rateLimited,
        );
      }
      
      // Check system load
      final loadCheck = await _checkSystemLoad();
      if (!loadCheck.allowed) {
        _blockedRequests++;
        return SafetyCheckResult(
          allowed: false,
          reason: loadCheck.reason,
          retryAfter: loadCheck.retryAfter,
          safetyLevel: SafetyLevel.overloaded,
        );
      }
      
      // All checks passed
      await _recordRequest(userId);
      
      return SafetyCheckResult(
        allowed: true,
        reason: 'Request approved',
        safetyLevel: SafetyLevel.safe,
      );
      
    } catch (e) {
      if (kDebugMode) print('❌ Safety check failed: $e');
      return SafetyCheckResult(
        allowed: false,
        reason: 'Safety check error: $e',
        safetyLevel: SafetyLevel.error,
      );
    }
  }

  /// Record successful operation
  static Future<void> recordSuccess() async {
    try {
      if (_circuitState == CircuitBreakerState.halfOpen) {
        _successCount++;
        if (_successCount >= _successThreshold) {
          await _closeCircuit();
        }
      }
      
      await _saveCircuitBreakerState();
    } catch (e) {
      if (kDebugMode) print('❌ Failed to record success: $e');
    }
  }

  /// Record failed operation
  static Future<void> recordFailure(String error) async {
    try {
      _failedRequests++;
      _failureCount++;
      _lastFailureTime = DateTime.now();
      
      if (_circuitState == CircuitBreakerState.closed && 
          _failureCount >= _failureThreshold) {
        await _openCircuit();
      } else if (_circuitState == CircuitBreakerState.halfOpen) {
        await _openCircuit();
      }
      
      await _saveCircuitBreakerState();
      
      if (kDebugMode) {
        print('⚠️ Failure recorded: $error');
        print('🔥 Failure count: $_failureCount/$_failureThreshold');
      }
    } catch (e) {
      if (kDebugMode) print('❌ Failed to record failure: $e');
    }
  }

  /// Get safety metrics
  static SafetyMetrics getSafetyMetrics() {
    final successRate = _totalRequests > 0 
        ? (_totalRequests - _failedRequests) / _totalRequests 
        : 1.0;
    
    final blockRate = _totalRequests > 0 
        ? _blockedRequests / _totalRequests 
        : 0.0;
    
    return SafetyMetrics(
      circuitState: _circuitState,
      totalRequests: _totalRequests,
      blockedRequests: _blockedRequests,
      failedRequests: _failedRequests,
      successRate: successRate,
      blockRate: blockRate,
      failureCount: _failureCount,
      lastFailureTime: _lastFailureTime,
      circuitOpenTime: _circuitOpenTime,
      activeUsers: _userRequestHistory.length,
      globalRequestRate: _calculateGlobalRequestRate(),
    );
  }

  /// Force circuit breaker state (for testing/emergency)
  static Future<void> forceCircuitState(CircuitBreakerState state) async {
    _circuitState = state;
    if (state == CircuitBreakerState.closed) {
      _failureCount = 0;
      _successCount = 0;
      _circuitOpenTime = null;
    } else if (state == CircuitBreakerState.open) {
      _circuitOpenTime = DateTime.now();
    }
    
    await _saveCircuitBreakerState();
    
    if (kDebugMode) {
      print('🔧 Circuit breaker forced to: ${state.name}');
    }
  }

  /// Emergency shutdown (blocks all requests)
  static Future<void> emergencyShutdown(String reason) async {
    await forceCircuitState(CircuitBreakerState.open);
    
    if (kDebugMode) {
      print('🚨 EMERGENCY SHUTDOWN: $reason');
    }
  }

  /// Reset safety state (for recovery)
  static Future<void> resetSafetyState() async {
    _circuitState = CircuitBreakerState.closed;
    _failureCount = 0;
    _successCount = 0;
    _lastFailureTime = null;
    _circuitOpenTime = null;
    _userRequestHistory.clear();
    _globalRequestHistory.clear();
    _totalRequests = 0;
    _blockedRequests = 0;
    _failedRequests = 0;
    
    await _saveCircuitBreakerState();
    await _saveRateLimitState();
    
    if (kDebugMode) {
      print('🔄 Safety state reset');
    }
  }

  // Private helper methods
  static CircuitCheckResult _checkCircuitBreaker() {
    switch (_circuitState) {
      case CircuitBreakerState.closed:
        return CircuitCheckResult(allowed: true, reason: 'Circuit closed');
        
      case CircuitBreakerState.open:
        if (_circuitOpenTime != null && 
            DateTime.now().difference(_circuitOpenTime!) > _circuitTimeout) {
          _circuitState = CircuitBreakerState.halfOpen;
          _successCount = 0;
          return CircuitCheckResult(allowed: true, reason: 'Circuit half-open (testing)');
        }
        return CircuitCheckResult(
          allowed: false, 
          reason: 'Circuit breaker open - system recovering',
          retryAfter: _circuitTimeout,
        );
        
      case CircuitBreakerState.halfOpen:
        return CircuitCheckResult(allowed: true, reason: 'Circuit half-open (testing)');
    }
  }

  static Future<RateLimitCheckResult> _checkRateLimit(String userId) async {
    final now = DateTime.now();
    
    // Clean old requests
    _cleanOldRequests(now);
    
    // Check global rate limit
    if (_globalRequestHistory.length >= _globalRateLimit) {
      return RateLimitCheckResult(
        allowed: false,
        reason: 'Global rate limit exceeded ($_globalRateLimit/min)',
        retryAfter: _rateLimitWindow,
      );
    }
    
    // Check user rate limit
    final userRequests = _userRequestHistory[userId] ?? [];
    if (userRequests.length >= _userRateLimit) {
      return RateLimitCheckResult(
        allowed: false,
        reason: 'User rate limit exceeded ($_userRateLimit/min)',
        retryAfter: _rateLimitWindow,
      );
    }
    
    return RateLimitCheckResult(allowed: true, reason: 'Rate limit OK');
  }

  static Future<LoadCheckResult> _checkSystemLoad() async {
    // Check if system load protection is enabled
    final loadProtectionEnabled = FeatureFlagService.isEnabled('system_load_protection');
    if (!loadProtectionEnabled) {
      return LoadCheckResult(allowed: true, reason: 'Load protection disabled');
    }
    
    // Simple load check based on request rate
    final currentLoad = _calculateGlobalRequestRate();
    const maxLoad = 50.0; // Max requests per second
    
    if (currentLoad > maxLoad) {
      return LoadCheckResult(
        allowed: false,
        reason: 'System overloaded (${currentLoad.toStringAsFixed(1)} RPS)',
        retryAfter: const Duration(seconds: 30),
      );
    }
    
    return LoadCheckResult(allowed: true, reason: 'System load OK');
  }

  static Future<void> _recordRequest(String userId) async {
    final now = DateTime.now();
    
    // Record global request
    _globalRequestHistory.add(now);
    
    // Record user request
    _userRequestHistory.putIfAbsent(userId, () => []).add(now);
    
    // Clean old requests periodically
    if (_totalRequests % 100 == 0) {
      _cleanOldRequests(now);
    }
  }

  static void _cleanOldRequests(DateTime now) {
    final cutoff = now.subtract(_rateLimitWindow);
    
    // Clean global requests
    _globalRequestHistory.removeWhere((time) => time.isBefore(cutoff));
    
    // Clean user requests
    for (final userId in _userRequestHistory.keys.toList()) {
      _userRequestHistory[userId]!.removeWhere((time) => time.isBefore(cutoff));
      if (_userRequestHistory[userId]!.isEmpty) {
        _userRequestHistory.remove(userId);
      }
    }
  }

  static double _calculateGlobalRequestRate() {
    if (_globalRequestHistory.isEmpty) return 0.0;
    
    final now = DateTime.now();
    final oneMinuteAgo = now.subtract(const Duration(minutes: 1));
    final recentRequests = _globalRequestHistory
        .where((time) => time.isAfter(oneMinuteAgo))
        .length;
    
    return recentRequests / 60.0; // Requests per second
  }

  static Future<void> _openCircuit() async {
    _circuitState = CircuitBreakerState.open;
    _circuitOpenTime = DateTime.now();
    _successCount = 0;
    
    if (kDebugMode) {
      print('🔴 Circuit breaker OPENED - system protection active');
    }
  }

  static Future<void> _closeCircuit() async {
    _circuitState = CircuitBreakerState.closed;
    _failureCount = 0;
    _successCount = 0;
    _circuitOpenTime = null;
    
    if (kDebugMode) {
      print('🟢 Circuit breaker CLOSED - system recovered');
    }
  }

  static void _startCleanupTimer() {
    Timer.periodic(const Duration(minutes: 1), (timer) {
      _cleanOldRequests(DateTime.now());
    });
  }

  static Future<void> _saveCircuitBreakerState() async {
    try {
      final state = {
        'circuitState': _circuitState.index,
        'failureCount': _failureCount,
        'successCount': _successCount,
        'lastFailureTime': _lastFailureTime?.toIso8601String(),
        'circuitOpenTime': _circuitOpenTime?.toIso8601String(),
      };
      
      await _storage.write(key: _circuitBreakerKey, value: jsonEncode(state));
    } catch (e) {
      if (kDebugMode) print('❌ Failed to save circuit breaker state: $e');
    }
  }

  static Future<void> _loadCircuitBreakerState() async {
    try {
      final data = await _storage.read(key: _circuitBreakerKey);
      if (data != null) {
        final state = jsonDecode(data);
        _circuitState = CircuitBreakerState.values[state['circuitState'] ?? 0];
        _failureCount = state['failureCount'] ?? 0;
        _successCount = state['successCount'] ?? 0;
        _lastFailureTime = state['lastFailureTime'] != null 
            ? DateTime.parse(state['lastFailureTime']) 
            : null;
        _circuitOpenTime = state['circuitOpenTime'] != null 
            ? DateTime.parse(state['circuitOpenTime']) 
            : null;
      }
    } catch (e) {
      if (kDebugMode) print('❌ Failed to load circuit breaker state: $e');
    }
  }

  static Future<void> _saveRateLimitState() async {
    try {
      final state = {
        'totalRequests': _totalRequests,
        'blockedRequests': _blockedRequests,
        'failedRequests': _failedRequests,
      };
      
      await _storage.write(key: _rateLimitKey, value: jsonEncode(state));
    } catch (e) {
      if (kDebugMode) print('❌ Failed to save rate limit state: $e');
    }
  }

  static Future<void> _loadRateLimitState() async {
    try {
      final data = await _storage.read(key: _rateLimitKey);
      if (data != null) {
        final state = jsonDecode(data);
        _totalRequests = state['totalRequests'] ?? 0;
        _blockedRequests = state['blockedRequests'] ?? 0;
        _failedRequests = state['failedRequests'] ?? 0;
      }
    } catch (e) {
      if (kDebugMode) print('❌ Failed to load rate limit state: $e');
    }
  }
}

// Data classes
class SafetyCheckResult {
  final bool allowed;
  final String reason;
  final Duration? retryAfter;
  final SafetyLevel safetyLevel;

  SafetyCheckResult({
    required this.allowed,
    required this.reason,
    this.retryAfter,
    required this.safetyLevel,
  });
}

class CircuitCheckResult {
  final bool allowed;
  final String reason;
  final Duration? retryAfter;

  CircuitCheckResult({
    required this.allowed,
    required this.reason,
    this.retryAfter,
  });
}

class RateLimitCheckResult {
  final bool allowed;
  final String reason;
  final Duration? retryAfter;

  RateLimitCheckResult({
    required this.allowed,
    required this.reason,
    this.retryAfter,
  });
}

class LoadCheckResult {
  final bool allowed;
  final String reason;
  final Duration? retryAfter;

  LoadCheckResult({
    required this.allowed,
    required this.reason,
    this.retryAfter,
  });
}

class SafetyMetrics {
  final CircuitBreakerState circuitState;
  final int totalRequests;
  final int blockedRequests;
  final int failedRequests;
  final double successRate;
  final double blockRate;
  final int failureCount;
  final DateTime? lastFailureTime;
  final DateTime? circuitOpenTime;
  final int activeUsers;
  final double globalRequestRate;

  SafetyMetrics({
    required this.circuitState,
    required this.totalRequests,
    required this.blockedRequests,
    required this.failedRequests,
    required this.successRate,
    required this.blockRate,
    required this.failureCount,
    this.lastFailureTime,
    this.circuitOpenTime,
    required this.activeUsers,
    required this.globalRequestRate,
  });
}

enum CircuitBreakerState { closed, open, halfOpen }
enum SafetyLevel { safe, rateLimited, overloaded, blocked, error }
