// lib/services/permission_manager_service.dart

import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';

/// Comprehensive permission management service for App Store compliance
class PermissionManagerService {
  
  /// Request camera permission with clear explanation
  static Future<bool> requestCameraPermission(BuildContext context) async {
    final status = await Permission.camera.status;

    if (status.isGranted) {
      return true;
    }

    if (status.isDenied) {
      // Show explanation dialog before requesting
      if (!context.mounted) return false;
      final shouldRequest = await _showPermissionExplanationDialog(
        context,
        'Camera Access',
        'MXD needs camera access to let you take photos for your quest progress and achievements. This helps you document your journey and celebrate milestones.',
        Icons.camera_alt,
        Colors.blue,
      );

      if (!shouldRequest) return false;

      final result = await Permission.camera.request();
      return result.isGranted;
    }

    if (status.isPermanentlyDenied) {
      if (!context.mounted) return false;
      await _showPermissionDeniedDialog(
        context,
        'Camera Access Denied',
        'Camera access has been permanently denied. To enable photo features, please go to Settings > Privacy > Camera and enable access for MXD.',
      );
      return false;
    }

    return false;
  }
  
  /// Request photo library permission with clear explanation
  static Future<bool> requestPhotoLibraryPermission(BuildContext context) async {
    final status = await Permission.photos.status;

    if (status.isGranted) {
      return true;
    }

    if (status.isDenied) {
      if (!context.mounted) return false;
      final shouldRequest = await _showPermissionExplanationDialog(
        context,
        'Photo Library Access',
        'MXD needs access to your photo library to let you select images for your quest progress and achievements. This helps you personalize your journey.',
        Icons.photo_library,
        Colors.green,
      );

      if (!shouldRequest) return false;

      final result = await Permission.photos.request();
      return result.isGranted;
    }

    if (status.isPermanentlyDenied) {
      if (!context.mounted) return false;
      await _showPermissionDeniedDialog(
        context,
        'Photo Library Access Denied',
        'Photo library access has been permanently denied. To enable photo selection features, please go to Settings > Privacy > Photos and enable access for MXD.',
      );
      return false;
    }

    return false;
  }
  
  /// Request microphone permission with clear explanation
  static Future<bool> requestMicrophonePermission(BuildContext context) async {
    final status = await Permission.microphone.status;

    if (status.isGranted) {
      return true;
    }

    if (status.isDenied) {
      if (!context.mounted) return false;
      final shouldRequest = await _showPermissionExplanationDialog(
        context,
        'Microphone Access',
        'MXD can use your microphone for voice input features, making it easier to log your progress and interact with AI coaches hands-free.',
        Icons.mic,
        Colors.orange,
      );

      if (!shouldRequest) return false;

      final result = await Permission.microphone.request();
      return result.isGranted;
    }

    if (status.isPermanentlyDenied) {
      if (!context.mounted) return false;
      await _showPermissionDeniedDialog(
        context,
        'Microphone Access Denied',
        'Microphone access has been permanently denied. To enable voice features, please go to Settings > Privacy > Microphone and enable access for MXD.',
      );
      return false;
    }

    return false;
  }
  
  /// Request notification permission with clear explanation
  static Future<bool> requestNotificationPermission(BuildContext context) async {
    final status = await Permission.notification.status;

    if (status.isGranted) {
      return true;
    }

    if (status.isDenied) {
      if (!context.mounted) return false;
      final shouldRequest = await _showPermissionExplanationDialog(
        context,
        'Notification Permission',
        'MXD sends helpful reminders and motivational messages to keep you on track with your goals. Notifications help you stay consistent and motivated.',
        Icons.notifications,
        Colors.purple,
      );

      if (!shouldRequest) return false;

      final result = await Permission.notification.request();
      return result.isGranted;
    }

    if (status.isPermanentlyDenied) {
      if (!context.mounted) return false;
      await _showPermissionDeniedDialog(
        context,
        'Notifications Disabled',
        'Notifications have been disabled. To receive helpful reminders and motivation, please go to Settings > Notifications and enable notifications for MXD.',
      );
      return false;
    }

    return false;
  }
  
  /// Show permission explanation dialog
  static Future<bool> _showPermissionExplanationDialog(
    BuildContext context,
    String title,
    String explanation,
    IconData icon,
    Color color,
  ) async {
    final result = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.black,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
          side: BorderSide(color: color, width: 2),
        ),
        title: Row(
          children: [
            Icon(icon, color: color, size: 28),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                title,
                style: const TextStyle(
                  color: Colors.white,
                  fontFamily: 'Pirulen',
                  fontSize: 18,
                ),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              explanation,
              style: const TextStyle(
                color: Colors.white,
                fontFamily: 'Bitsumishi',
                fontSize: 16,
                height: 1.4,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: color.withValues(alpha: 0.3)),
              ),
              child: Row(
                children: [
                  Icon(Icons.security, color: color, size: 20),
                  const SizedBox(width: 8),
                  const Expanded(
                    child: Text(
                      'Your privacy is protected. We never share your data without permission.',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(
              'Not Now',
              style: TextStyle(color: Colors.grey[400]),
            ),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: color,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text('Allow Access'),
          ),
        ],
      ),
    );
    
    return result ?? false;
  }
  
  /// Show permission denied dialog with settings guidance
  static Future<void> _showPermissionDeniedDialog(
    BuildContext context,
    String title,
    String message,
  ) async {
    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.black,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
          side: const BorderSide(color: Colors.red, width: 2),
        ),
        title: Row(
          children: [
            const Icon(Icons.warning, color: Colors.red, size: 28),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                title,
                style: const TextStyle(
                  color: Colors.white,
                  fontFamily: 'Pirulen',
                  fontSize: 18,
                ),
              ),
            ),
          ],
        ),
        content: Text(
          message,
          style: const TextStyle(
            color: Colors.white,
            fontFamily: 'Bitsumishi',
            fontSize: 16,
            height: 1.4,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text(
              'OK',
              style: TextStyle(color: Colors.white),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              openAppSettings();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text('Open Settings'),
          ),
        ],
      ),
    );
  }
  
  /// Get permission status summary for debugging
  static Future<Map<String, String>> getPermissionStatusSummary() async {
    final permissions = {
      'Camera': await Permission.camera.status,
      'Photos': await Permission.photos.status,
      'Microphone': await Permission.microphone.status,
      'Notifications': await Permission.notification.status,
    };
    
    return permissions.map((key, value) => MapEntry(key, value.toString()));
  }
  
  /// Check if all critical permissions are granted
  static Future<bool> areAllCriticalPermissionsGranted() async {
    final notifications = await Permission.notification.status;

    // Camera and photos are optional, notifications are recommended
    return notifications.isGranted;
  }
}
