// lib/services/coach_memory_service.dart

import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../models/conversation_context.dart';

/// Advanced memory system for coaches to remember and learn from interactions
class CoachMemoryService {
  static const FlutterSecureStorage _storage = FlutterSecureStorage();
  
  // Memory storage keys
  static const String _recentInteractionsKey = 'recent_interactions';
  static const String _userPatternsKey = 'user_patterns';
  static const String _userPersonalityKey = 'user_personality';
  
  // In-memory caches for performance
  static final Map<String, List<ConversationContext>> _recentInteractionsCache = {};
  static final Map<String, UserPatterns> _userPatternsCache = {};
  static final Map<String, UserPersonality> _userPersonalityCache = {};
  
  /// Initialize memory service for a user
  static Future<void> initializeUserMemory(String userId) async {
    try {
      // Load existing data or create empty structures
      await _loadRecentInteractions(userId);
      await _loadUserPatterns(userId);
      await _loadUserPersonality(userId);
      
      if (kDebugMode) print('🧠 Memory initialized for user: $userId');
    } catch (e) {
      if (kDebugMode) print('❌ Failed to initialize memory for $userId: $e');
    }
  }

  /// Record a new conversation for memory learning
  static Future<void> recordConversation({
    required String userId,
    required String coachCategory,
    required String userMessage,
    required String coachResponse,
    bool wasHelpful = true,
  }) async {
    try {
      // Analyze the conversation
      final sentiment = _analyzeSentiment(userMessage);
      final topic = _categorizeMessage(userMessage);
      
      // Create conversation context
      final context = ConversationContext(
        id: _generateId(),
        userId: userId,
        coachCategory: coachCategory,
        userMessage: userMessage,
        coachResponse: coachResponse,
        timestamp: DateTime.now(),
        userSentiment: sentiment,
        topicCategory: topic,
        wasHelpful: wasHelpful,
        metadata: {
          'messageLength': userMessage.length,
          'responseLength': coachResponse.length,
          'timeOfDay': DateTime.now().hour,
          'dayOfWeek': DateTime.now().weekday,
        },
      );
      
      // Store in recent interactions
      await _addRecentInteraction(userId, context);
      
      // Update user patterns
      await _updateUserPatterns(userId, context);
      
      // Update personality insights
      await _updateUserPersonality(userId, context);
      
      if (kDebugMode) print('🧠 Conversation recorded for $userId in $coachCategory');
    } catch (e) {
      if (kDebugMode) print('❌ Failed to record conversation: $e');
    }
  }

  /// Get contextual information for generating responses
  static Future<Map<String, dynamic>> getConversationContext(
    String userId,
    String coachCategory,
  ) async {
    try {
      // Get recent interactions
      final recentInteractions = await _getRecentInteractions(userId, coachCategory);
      
      // Get user patterns
      final patterns = await _getUserPatterns(userId);
      
      // Get personality insights
      final personality = await _getUserPersonality(userId);
      
      // Build context summary
      final context = {
        'recentTopics': _extractRecentTopics(recentInteractions),
        'conversationHistory': _summarizeRecentConversations(recentInteractions),
        'userMood': _analyzeRecentMood(recentInteractions),
        'preferredStyle': _getPreferredCommunicationStyle(patterns, personality),
        'motivationFactors': personality.motivationFactors,
        'effectiveStrategies': _getEffectiveStrategies(patterns, coachCategory),
        'timeContext': _getTimeContext(),
        'userProgress': _analyzeUserProgress(recentInteractions),
      };
      
      return context;
    } catch (e) {
      if (kDebugMode) print('❌ Failed to get conversation context: $e');
      return {};
    }
  }

  /// Get memory-enhanced prompt for AI
  static Future<String> getMemoryEnhancedPrompt({
    required String basePrompt,
    required String userId,
    required String coachCategory,
    required String userMessage,
  }) async {
    try {
      final context = await getConversationContext(userId, coachCategory);
      
      // Build memory-enhanced prompt
      final enhancedPrompt = '''
$basePrompt

MEMORY CONTEXT:
Recent conversation topics: ${context['recentTopics']}
User's current mood: ${context['userMood']}
Preferred communication style: ${context['preferredStyle']}
What motivates this user: ${context['motivationFactors']}
Strategies that work well: ${context['effectiveStrategies']}
User's recent progress: ${context['userProgress']}
Time context: ${context['timeContext']}

CONVERSATION HISTORY:
${context['conversationHistory']}

Current message: "$userMessage"

Remember to:
1. Reference relevant past conversations naturally
2. Use the communication style this user responds to best
3. Build on their recent progress and topics
4. Consider their current mood and motivation factors
5. Apply strategies that have worked well before
''';

      return enhancedPrompt;
    } catch (e) {
      if (kDebugMode) print('❌ Failed to enhance prompt with memory: $e');
      return basePrompt;
    }
  }

  /// Analyze user's communication patterns
  static Future<Map<String, dynamic>> analyzeUserCommunication(String userId) async {
    try {
      final patterns = await _getUserPatterns(userId);
      final personality = await _getUserPersonality(userId);
      
      return {
        'communicationStyle': patterns.communicationStyle,
        'topicPreferences': patterns.topicFrequency,
        'sentimentTrends': patterns.sentimentTrends,
        'motivationFactors': personality.motivationFactors,
        'coreValues': personality.coreValues,
        'preferredTone': _determinePreferredTone(patterns, personality),
        'optimalTiming': _analyzeOptimalTiming(patterns),
      };
    } catch (e) {
      if (kDebugMode) print('❌ Failed to analyze user communication: $e');
      return {};
    }
  }

  /// Private helper methods
  
  static Future<void> _addRecentInteraction(String userId, ConversationContext context) async {
    _recentInteractionsCache[userId] ??= [];
    _recentInteractionsCache[userId]!.add(context);
    
    // Keep only last 50 interactions
    if (_recentInteractionsCache[userId]!.length > 50) {
      _recentInteractionsCache[userId]!.removeAt(0);
    }
    
    await _saveRecentInteractions(userId);
  }

  static Future<List<ConversationContext>> _getRecentInteractions(
    String userId, 
    String? coachCategory,
  ) async {
    await _loadRecentInteractions(userId);
    final interactions = _recentInteractionsCache[userId] ?? [];
    
    if (coachCategory != null) {
      return interactions.where((i) => i.coachCategory == coachCategory).toList();
    }
    
    return interactions;
  }

  static Future<void> _updateUserPatterns(String userId, ConversationContext context) async {
    var patterns = await _getUserPatterns(userId);
    
    // Update communication style
    final messageStyle = _analyzeMessageStyle(context.userMessage);
    for (final entry in messageStyle.entries) {
      patterns.communicationStyle[entry.key] = 
          (patterns.communicationStyle[entry.key] ?? 0.0) * 0.9 + entry.value * 0.1;
    }
    
    // Update topic frequency
    patterns.topicFrequency[context.topicCategory] = 
        (patterns.topicFrequency[context.topicCategory] ?? 0) + 1;
    
    // Update sentiment trends
    final sentimentScore = _sentimentToScore(context.userSentiment);
    patterns.sentimentTrends[context.coachCategory] = 
        (patterns.sentimentTrends[context.coachCategory] ?? 0.5) * 0.8 + sentimentScore * 0.2;
    
    // Update response effectiveness
    if (context.wasHelpful) {
      final responseType = _categorizeResponse(context.coachResponse);
      patterns.responseEffectiveness[responseType] = 
          (patterns.responseEffectiveness[responseType] ?? 0.5) * 0.9 + 0.8 * 0.1;
    }
    
    patterns = patterns.copyWith(lastUpdated: DateTime.now());
    _userPatternsCache[userId] = patterns;
    await _saveUserPatterns(userId);
  }

  static Future<void> _updateUserPersonality(String userId, ConversationContext context) async {
    var personality = await _getUserPersonality(userId);
    
    // Extract personality insights from message
    final traits = _extractPersonalityTraits(context.userMessage);
    for (final entry in traits.entries) {
      personality.traits[entry.key] = 
          (personality.traits[entry.key] ?? 0.5) * 0.95 + entry.value * 0.05;
    }
    
    // Update motivation factors based on what user responds to
    if (context.wasHelpful) {
      final motivationFactors = _extractMotivationFactors(context.coachResponse);
      for (final entry in motivationFactors.entries) {
        personality.motivationFactors[entry.key] = 
            (personality.motivationFactors[entry.key] ?? 0.5) * 0.9 + entry.value * 0.1;
      }
    }
    
    personality = personality.copyWith(lastUpdated: DateTime.now());
    _userPersonalityCache[userId] = personality;
    await _saveUserPersonality(userId);
  }

  static String _analyzeSentiment(String message) {
    final lowerMessage = message.toLowerCase();
    
    // Positive indicators
    final positiveWords = ['great', 'good', 'happy', 'excited', 'motivated', 'confident'];
    final positiveCount = positiveWords.where((word) => lowerMessage.contains(word)).length;
    
    // Negative indicators
    final negativeWords = ['bad', 'sad', 'stressed', 'overwhelmed', 'tired', 'frustrated'];
    final negativeCount = negativeWords.where((word) => lowerMessage.contains(word)).length;
    
    // Neutral indicators
    final questionWords = ['how', 'what', 'when', 'where', 'why', '?'];
    final hasQuestion = questionWords.any((word) => lowerMessage.contains(word));
    
    if (positiveCount > negativeCount) return 'positive';
    if (negativeCount > positiveCount) return 'negative';
    if (hasQuestion) return 'curious';
    return 'neutral';
  }

  static String _categorizeMessage(String message) {
    final lowerMessage = message.toLowerCase();
    
    // Health topics
    if (RegExp(r'\b(exercise|workout|fitness|health|diet|sleep|energy)\b').hasMatch(lowerMessage)) {
      return 'health';
    }
    
    // Wealth topics
    if (RegExp(r'\b(money|finance|budget|income|investment|career|job)\b').hasMatch(lowerMessage)) {
      return 'wealth';
    }
    
    // Purpose topics
    if (RegExp(r'\b(purpose|meaning|goal|dream|passion|vision|future)\b').hasMatch(lowerMessage)) {
      return 'purpose';
    }
    
    // Connection topics
    if (RegExp(r'\b(relationship|friend|family|social|communication|love)\b').hasMatch(lowerMessage)) {
      return 'connection';
    }
    
    return 'general';
  }

  static Map<String, double> _analyzeMessageStyle(String message) {
    final style = <String, double>{};
    
    // Analyze length preference
    style['detailed'] = message.length > 100 ? 1.0 : 0.0;
    style['concise'] = message.length < 50 ? 1.0 : 0.0;
    
    // Analyze question style
    style['questioning'] = message.contains('?') ? 1.0 : 0.0;
    
    // Analyze emotional expression
    style['emotional'] = RegExp(r'[!]{2,}|[.]{3,}|[A-Z]{3,}').hasMatch(message) ? 1.0 : 0.0;
    
    return style;
  }

  static double _sentimentToScore(String sentiment) {
    switch (sentiment) {
      case 'positive': return 0.8;
      case 'negative': return 0.2;
      case 'curious': return 0.6;
      default: return 0.5;
    }
  }

  static String _generateId() {
    return DateTime.now().millisecondsSinceEpoch.toString() + 
           Random().nextInt(1000).toString();
  }

  // Storage methods
  static Future<void> _loadRecentInteractions(String userId) async {
    try {
      final data = await _storage.read(key: '${_recentInteractionsKey}_$userId');
      if (data != null) {
        final List<dynamic> jsonList = jsonDecode(data);
        _recentInteractionsCache[userId] = jsonList
            .map((json) => ConversationContext.fromJson(json))
            .toList();
      } else {
        _recentInteractionsCache[userId] = [];
      }
    } catch (e) {
      _recentInteractionsCache[userId] = [];
    }
  }

  static Future<void> _saveRecentInteractions(String userId) async {
    try {
      final interactions = _recentInteractionsCache[userId] ?? [];
      final jsonList = interactions.map((i) => i.toJson()).toList();
      await _storage.write(
        key: '${_recentInteractionsKey}_$userId',
        value: jsonEncode(jsonList),
      );
    } catch (e) {
      if (kDebugMode) print('❌ Failed to save recent interactions: $e');
    }
  }

  static Future<UserPatterns> _getUserPatterns(String userId) async {
    if (!_userPatternsCache.containsKey(userId)) {
      await _loadUserPatterns(userId);
    }
    return _userPatternsCache[userId] ?? UserPatterns.empty(userId);
  }

  static Future<void> _loadUserPatterns(String userId) async {
    try {
      final data = await _storage.read(key: '${_userPatternsKey}_$userId');
      if (data != null) {
        _userPatternsCache[userId] = UserPatterns.fromJson(jsonDecode(data));
      } else {
        _userPatternsCache[userId] = UserPatterns.empty(userId);
      }
    } catch (e) {
      _userPatternsCache[userId] = UserPatterns.empty(userId);
    }
  }

  static Future<void> _saveUserPatterns(String userId) async {
    try {
      final patterns = _userPatternsCache[userId];
      if (patterns != null) {
        await _storage.write(
          key: '${_userPatternsKey}_$userId',
          value: jsonEncode(patterns.toJson()),
        );
      }
    } catch (e) {
      if (kDebugMode) print('❌ Failed to save user patterns: $e');
    }
  }

  static Future<UserPersonality> _getUserPersonality(String userId) async {
    if (!_userPersonalityCache.containsKey(userId)) {
      await _loadUserPersonality(userId);
    }
    return _userPersonalityCache[userId] ?? UserPersonality.empty(userId);
  }

  static Future<void> _loadUserPersonality(String userId) async {
    try {
      final data = await _storage.read(key: '${_userPersonalityKey}_$userId');
      if (data != null) {
        _userPersonalityCache[userId] = UserPersonality.fromJson(jsonDecode(data));
      } else {
        _userPersonalityCache[userId] = UserPersonality.empty(userId);
      }
    } catch (e) {
      _userPersonalityCache[userId] = UserPersonality.empty(userId);
    }
  }

  static Future<void> _saveUserPersonality(String userId) async {
    try {
      final personality = _userPersonalityCache[userId];
      if (personality != null) {
        await _storage.write(
          key: '${_userPersonalityKey}_$userId',
          value: jsonEncode(personality.toJson()),
        );
      }
    } catch (e) {
      if (kDebugMode) print('❌ Failed to save user personality: $e');
    }
  }

  // Analysis helper methods (simplified implementations)
  static List<String> _extractRecentTopics(List<ConversationContext> interactions) {
    return interactions
        .map((i) => i.topicCategory)
        .toSet()
        .take(5)
        .toList();
  }

  static String _summarizeRecentConversations(List<ConversationContext> interactions) {
    if (interactions.isEmpty) return 'No recent conversations';
    
    final recent = interactions.take(3).map((i) =>
        'User: ${i.userMessage.length > 50 ? "${i.userMessage.substring(0, 50)}..." : i.userMessage}'
    ).join('\n');
    
    return recent;
  }

  static String _analyzeRecentMood(List<ConversationContext> interactions) {
    if (interactions.isEmpty) return 'neutral';
    
    final recentSentiments = interactions.take(5).map((i) => i.userSentiment).toList();
    final positiveCount = recentSentiments.where((s) => s == 'positive').length;
    final negativeCount = recentSentiments.where((s) => s == 'negative').length;
    
    if (positiveCount > negativeCount) return 'positive';
    if (negativeCount > positiveCount) return 'negative';
    return 'neutral';
  }

  static String _getPreferredCommunicationStyle(UserPatterns patterns, UserPersonality personality) {
    final style = patterns.communicationStyle;
    
    if ((style['detailed'] ?? 0.0) > 0.6) return 'detailed and thorough';
    if ((style['concise'] ?? 0.0) > 0.6) return 'brief and to the point';
    if ((style['questioning'] ?? 0.0) > 0.6) return 'interactive and questioning';
    if ((style['emotional'] ?? 0.0) > 0.6) return 'empathetic and supportive';
    
    return 'balanced and adaptive';
  }

  static List<String> _getEffectiveStrategies(UserPatterns patterns, String coachCategory) {
    final effectiveness = patterns.responseEffectiveness;
    
    return effectiveness.entries
        .where((e) => e.value > 0.6)
        .map((e) => e.key)
        .take(3)
        .toList();
  }

  static String _getTimeContext() {
    final now = DateTime.now();
    final hour = now.hour;
    
    if (hour < 6) return 'very early morning';
    if (hour < 12) return 'morning';
    if (hour < 17) return 'afternoon';
    if (hour < 21) return 'evening';
    return 'late evening';
  }

  static String _analyzeUserProgress(List<ConversationContext> interactions) {
    if (interactions.length < 2) return 'just getting started';
    
    final recentHelpful = interactions.take(5).where((i) => i.wasHelpful).length;
    final totalRecent = interactions.take(5).length;
    
    if (recentHelpful / totalRecent > 0.8) return 'making excellent progress';
    if (recentHelpful / totalRecent > 0.6) return 'making good progress';
    return 'working through challenges';
  }

  static Map<String, double> _extractPersonalityTraits(String message) {
    // Simplified personality trait extraction
    final traits = <String, double>{};
    final lowerMessage = message.toLowerCase();
    
    if (lowerMessage.contains(RegExp(r'\b(plan|organize|structure|schedule)\b'))) {
      traits['organized'] = 1.0;
    }
    if (lowerMessage.contains(RegExp(r'\b(feel|emotion|heart|soul)\b'))) {
      traits['emotional'] = 1.0;
    }
    if (lowerMessage.contains(RegExp(r'\b(think|analyze|logic|reason)\b'))) {
      traits['analytical'] = 1.0;
    }
    
    return traits;
  }

  static Map<String, double> _extractMotivationFactors(String response) {
    // Simplified motivation factor extraction
    final factors = <String, double>{};
    final lowerResponse = response.toLowerCase();
    
    if (lowerResponse.contains(RegExp(r'\b(achieve|accomplish|succeed|win)\b'))) {
      factors['achievement'] = 1.0;
    }
    if (lowerResponse.contains(RegExp(r'\b(connect|relationship|together|support)\b'))) {
      factors['connection'] = 1.0;
    }
    if (lowerResponse.contains(RegExp(r'\b(grow|learn|improve|develop)\b'))) {
      factors['growth'] = 1.0;
    }
    
    return factors;
  }

  static String _categorizeResponse(String response) {
    final lowerResponse = response.toLowerCase();
    
    if (lowerResponse.contains(RegExp(r'\b(question|ask|wonder|curious)\b'))) {
      return 'questioning';
    }
    if (lowerResponse.contains(RegExp(r'\b(encourage|motivate|inspire|believe)\b'))) {
      return 'motivational';
    }
    if (lowerResponse.contains(RegExp(r'\b(step|plan|strategy|approach)\b'))) {
      return 'strategic';
    }
    if (lowerResponse.contains(RegExp(r'\b(understand|feel|empathy|support)\b'))) {
      return 'empathetic';
    }
    
    return 'general';
  }

  static String _determinePreferredTone(UserPatterns patterns, UserPersonality personality) {
    final motivationFactors = personality.motivationFactors;
    
    if ((motivationFactors['achievement'] ?? 0.0) > 0.7) return 'motivational and goal-focused';
    if ((motivationFactors['connection'] ?? 0.0) > 0.7) return 'warm and supportive';
    if ((motivationFactors['growth'] ?? 0.0) > 0.7) return 'encouraging and developmental';
    
    return 'balanced and adaptive';
  }

  static Map<String, dynamic> _analyzeOptimalTiming(UserPatterns patterns) {
    // Simplified timing analysis
    return {
      'preferredTimeOfDay': 'morning', // Would be calculated from activity patterns
      'responseFrequency': 'moderate',
      'bestDaysForCheckins': ['Monday', 'Wednesday', 'Friday'],
    };
  }
}
