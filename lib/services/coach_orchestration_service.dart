import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:http/http.dart' as http;
import '../models/user_model.dart';
import '../prompts/mxd_life_coaches.dart';
import 'coach_context_service.dart';
import 'coach_training_context_service.dart';
import 'coach_analytics_service.dart';
import 'coach_safety_service.dart';
import 'model_routing_service.dart';

/// Enhanced Coach Orchestration Service with AI-native intelligence
/// Provides superintelligent coaching without transcript dependencies
class CoachOrchestrationService {
  static const String _fallbackResponse = 'I understand you\'re looking for guidance. Let me help you with that.';

  /// Generate superintelligent coach response using AI-native intelligence
  static Future<String> generateSuperintelligentResponse({
    required String category,
    required String userPrompt,
    required User user,
    String? fileContent,
    String? fileName,
    String interactionType = 'chat',
  }) async {
    try {
      if (kDebugMode) print('🧠 Starting superintelligent response generation...');

      // 🎯 PHASE 2: Email verification check for coach access
      if (!user.isEmailVerified) {
        if (kDebugMode) print('🚨 Coach access blocked - email not verified for user: ${user.username}');
        return 'I appreciate your interest in connecting with me! However, I can only provide my full coaching services after your email has been verified. Please check your email inbox for a verification link, then return to chat with me. I\'m excited to help you on your journey once verification is complete! 🌟';
      }

      if (kDebugMode) print('✅ Email verified - proceeding with coach response generation');
      
      final stopwatch = Stopwatch()..start();
      String selectedModel = 'gpt-4o';

      // Step 1: Route to optimal AI model
      final routing = await ModelRoutingService.routeMessage(
        userId: user.id,
        category: category,
        message: userPrompt,
        user: user,
      );
      if (kDebugMode) print('🔀 Step 1: Model routing complete');

      // Step 2: Build comprehensive user context
      final userContext = await CoachContextService.getUserContext(user);

      // Step 2.5: Add specialized training context for coaches
      final trainingContext = await CoachTrainingContextService.getCoachTrainingContext(category, user);

      // Step 3: Get coach personality (AI-native intelligence)
      final coachPersonality = _getCoachPersonality(category, user.gender);

      // Step 4: Build enhanced prompt with full context (AI-native intelligence)
      final enhancedPrompt = _buildEnhancedPrompt(
        category: category,
        userPrompt: userPrompt,
        user: user,
        userContext: userContext,
        trainingContext: trainingContext,
        coachPersonality: coachPersonality,
        fileContent: fileContent,
        fileName: fileName,
      );

      // Step 5: Add deliberate thinking time (3-22 seconds)
      final thinkingTime = Duration(
        milliseconds: 3000 + (DateTime.now().millisecondsSinceEpoch % 19000)
      );
      await Future.delayed(thinkingTime);

      // Step 6: Get AI response using routed model
      selectedModel = routing.selectedModel!;
      final response = await _getAIResponse(
        prompt: enhancedPrompt,
        model: selectedModel,
      );

      stopwatch.stop();

      // Step 7: Record analytics
      await CoachAnalyticsService.recordInteraction(
        userId: user.id,
        category: category,
        coachName: _getCoachName(category, user.gender, user.assignedCoaches),
        userMessage: userPrompt,
        coachResponse: response,
        responseTimeMs: stopwatch.elapsedMilliseconds,
        model: selectedModel,
        cost: _calculateInteractionCost(selectedModel, userPrompt.length + response.length),
        wasSuccessful: true,
      );

      // Record safety success
      await CoachSafetyService.recordSuccess();

      if (kDebugMode) {
        print('🎭 Coach response generated');
        print('🔀 Model used: $selectedModel');
        print('📊 Context size: ${jsonEncode(userContext).length} chars');
        print('⏱️ Thinking time: ${thinkingTime.inSeconds}s');
        print('📈 Analytics recorded');
      }

      return response.isNotEmpty ? response : _fallbackResponse;

    } catch (e, stackTrace) {
      if (kDebugMode) {
        print('❌ Error in superintelligent response generation: $e');
        print('📍 Stack trace: $stackTrace');
      }

      // Record safety failure
      await CoachSafetyService.recordFailure(e.toString());

      // Return fallback response
      return _fallbackResponse;
    }
  }

  /// Generate response for file analysis
  static Future<String> generateFileAnalysisResponse({
    required User user,
    required String fileContent,
    required String fileName,
  }) async {
    return generateSuperintelligentResponse(
      category: 'Custom Category 1',
      userPrompt: 'Please analyze this file and provide insights.',
      user: user,
      fileContent: fileContent,
      fileName: fileName,
      interactionType: 'file_analysis',
    );
  }

  // ============================================================================
  // PRIVATE HELPER METHODS
  // ============================================================================

  /// Get coach personality based on category and gender
  static String _getCoachPersonality(String category, String gender) {
    try {
      // Handle custom categories specifically
      if (category == 'Custom Category 1') {
        final coach = mxdLifeCoaches.firstWhere(
          (c) => c.category == 'Custom Category 1',
          orElse: () => mxdLifeCoaches.first,
        );
        return coach.description;
      } else if (category == 'Custom Category 2') {
        final coach = mxdLifeCoaches.firstWhere(
          (c) => c.category == 'Custom Category 2',
          orElse: () => mxdLifeCoaches.first,
        );
        return coach.description;
      }

      // Handle standard categories
      final coach = mxdLifeCoaches.firstWhere(
        (c) => c.category.toLowerCase() == category.toLowerCase(),
        orElse: () => mxdLifeCoaches.first,
      );

      return coach.description;
    } catch (e) {
      if (kDebugMode) print('⚠️ Error getting coach personality: $e');
      return 'I am your dedicated AI coach, here to help you achieve your goals with personalized guidance and support.';
    }
  }

  /// Get coach name based on category and gender using authoritative source
  static String _getCoachName(String category, String gender, [Map<String, String>? assignedCoaches]) {
    // Handle non-gender users with assigned coaches
    String effectiveGender = gender;
    if (gender.toLowerCase() == 'non-gender' && assignedCoaches != null) {
      effectiveGender = assignedCoaches[category] ?? 'male';
    }

    // Handle custom categories with hardcoded coach assignments
    if (category == 'Custom Category 1') {
      return effectiveGender.toLowerCase() == 'female' ? 'Luna' : 'Aether';
    } else if (category == 'Custom Category 2') {
      return effectiveGender.toLowerCase() == 'female' ? 'Elysia' : 'Chronos';
    }

    // Find the coach for this category from authoritative source
    final coach = mxdLifeCoaches.firstWhere(
      (c) => c.category.toLowerCase() == category.toLowerCase(),
      orElse: () => mxdLifeCoaches.first,
    );

    return effectiveGender.toLowerCase() == 'female' ? coach.femaleName : coach.maleName;
  }

  /// Build enhanced prompt with AI-native intelligence
  static String _buildEnhancedPrompt({
    required String category,
    required String userPrompt,
    required User user,
    required Map<String, dynamic> userContext,
    required Map<String, dynamic> trainingContext,
    required String coachPersonality,
    String? fileContent,
    String? fileName,
  }) {
    final contextJson = jsonEncode(userContext);
    final trainingJson = jsonEncode(trainingContext);
    final coachName = _getCoachName(category, user.gender, user.assignedCoaches);

    return '''
$coachPersonality

COMPREHENSIVE USER CONTEXT (JSON):
$contextJson

TRAINING & WORKOUT DATA (JSON):
$trainingJson

${fileContent != null ? '''
USER UPLOADED FILE: $fileName
FILE CONTENT:
$fileContent

''' : ''}
COACHING CONVERSATION GUIDELINES:
You are $coachName speaking directly to your player. Write as if you're having a genuine, personal conversation - not giving a formal presentation.

CRITICAL IDENTITY RULES:
- You are $coachName - always refer to yourself as "I" or "me", never in third person
- Never mention any other coach names or refer to yourself by a different name
- Speak as $coachName throughout the entire conversation

PERSONALITY & TONE:
- Speak naturally and conversationally, like a real person would
- Use "I" statements and personal experiences: "In my experience...", "I've seen...", "I recommend..."
- Be warm, encouraging, and genuinely interested in their progress
- Avoid overly structured responses or bullet points unless truly necessary
- Let your unique personality shine through every word

CONVERSATION QUALITY:
- Reference their specific data, progress, and context naturally in conversation
- Share insights as if you're talking to a friend you care about
- Be substantial (800-1500+ words) but conversational, not formal
- Ask follow-up questions or suggest next steps organically
- Celebrate their wins and acknowledge their challenges with genuine empathy

${category.toLowerCase() == 'health' ? '''
HEALTH COACH TRAINING DATA EXPERTISE:
You have complete access to the player's training data including:
- All workout sessions, durations, and EXP earned
- Training programs and cycle preferences
- Bodyweight tracking and trends
- Training consistency patterns and frequency
- Performance analytics and recovery patterns
- Training intensity levels and recommendations

HEALTH COACH REQUIREMENTS:
- Reference specific training sessions, durations, or patterns when relevant
- Acknowledge their training consistency and progress
- Provide workout-specific advice based on their actual training data
- Comment on their training frequency, intensity, or recovery patterns
- Suggest improvements based on their actual training history
- Celebrate their training achievements and EXP gains
- Use their bodyweight data for health insights if available
''' : ''}

USER MESSAGE: $userPrompt

Respond as $coachName in a genuine, conversational way that shows you truly care about their success:''';
  }

  /// Get AI response from selected model
  static Future<String> _getAIResponse({
    required String prompt,
    required String model,
  }) async {
    try {
      final apiKey = dotenv.env['OPENAI_API_KEY'];
      if (apiKey == null || apiKey.isEmpty) {
        if (kDebugMode) print('❌ OpenAI API key not found');
        return _fallbackResponse;
      }

      final response = await http.post(
        Uri.parse('https://api.openai.com/v1/chat/completions'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $apiKey',
        },
        body: jsonEncode({
          'model': model,
          'messages': [
            {'role': 'user', 'content': prompt}
          ],
          'max_tokens': 1500,
          'temperature': 0.9,
        }),
      ).timeout(const Duration(seconds: 30));

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final content = data['choices'][0]['message']['content'] as String;
        return content.trim();
      } else {
        if (kDebugMode) print('❌ OpenAI API error: ${response.statusCode}');
        return _fallbackResponse;
      }
    } catch (e) {
      if (kDebugMode) print('❌ Error getting AI response: $e');
      return _fallbackResponse;
    }
  }

  /// Calculate interaction cost based on model and content length
  static double _calculateInteractionCost(String model, int totalLength) {
    // Simplified cost calculation
    final tokensEstimate = (totalLength / 4).ceil();
    
    switch (model) {
      case 'gpt-4o':
        return tokensEstimate * 0.00003; // $0.03 per 1K tokens
      case 'gpt-4o-mini':
        return tokensEstimate * 0.00015; // $0.15 per 1K tokens
      default:
        return tokensEstimate * 0.00003;
    }
  }

  /// Refresh user context cache
  static Future<void> refreshUserContext(String userId) async {
    await CoachContextService.clearUserCache(userId);
  }
}
