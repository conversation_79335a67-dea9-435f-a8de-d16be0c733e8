import 'dart:convert';
import 'dart:math';
import '../services/bulletproof_secure_storage.dart';
import '../services/comprehensive_logging_service.dart';

/// 📊 Real-time Signup Analytics Service
/// 
/// Tracks comprehensive signup funnel metrics, conversion rates, and failure
/// points in real-time. Provides detailed analytics for monitoring signup
/// health and identifying optimization opportunities.
/// 
/// Features:
/// - Real-time funnel tracking
/// - Conversion rate monitoring
/// - Failure point analysis
/// - Performance metrics
/// - Success rate alerting (<90% threshold)
/// - Detailed event logging
/// - Time-based analytics
class SignupAnalyticsService {
  static final SignupAnalyticsService _instance = SignupAnalyticsService._internal();
  factory SignupAnalyticsService() => _instance;
  SignupAnalyticsService._internal();

  final BulletproofSecureStorage _secureStorage = BulletproofSecureStorage();
  bool _isInitialized = false;

  // Storage keys
  static const String _analyticsDataKey = 'signup_analytics_data';
  static const String _funnelEventsKey = 'signup_funnel_events';
  static const String _performanceMetricsKey = 'signup_performance_metrics';
  static const String _alertHistoryKey = 'signup_alert_history';

  // Analytics configuration
  static const double _successRateThreshold = 90.0;
  static const Duration _analyticsWindow = Duration(hours: 1);
  static const int _maxEventHistory = 1000;

  /// Initialize the analytics service
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      await _secureStorage.initialize();
      
      // Initialize analytics data structure if not exists
      await _initializeAnalyticsData();
      
      _isInitialized = true;
      await ComprehensiveLoggingService.logInfo('📊 SignupAnalyticsService initialized');
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Failed to initialize SignupAnalyticsService: $e');
      rethrow;
    }
  }

  /// Ensure the service is initialized
  Future<void> _ensureInitialized() async {
    if (!_isInitialized) {
      await initialize();
    }
  }

  /// Track signup funnel event
  Future<void> trackFunnelEvent(SignupFunnelEvent event) async {
    await _ensureInitialized();
    
    try {
      await ComprehensiveLoggingService.logInfo('📊 Tracking funnel event: ${event.eventType}');
      
      // Add timestamp and session info
      event.timestamp = DateTime.now();
      event.sessionId ??= _generateSessionId();
      
      // Store event
      await _storeFunnelEvent(event);
      
      // Update analytics data
      await _updateAnalyticsData(event);
      
      // Check for alerts
      await _checkSuccessRateAlert();
      
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Failed to track funnel event: $e');
    }
  }

  /// Get real-time analytics dashboard data
  Future<SignupAnalyticsDashboard> getDashboardData() async {
    await _ensureInitialized();
    
    try {
      final dashboard = SignupAnalyticsDashboard();
      
      // Get current analytics data
      await _getAnalyticsData();
      
      // Calculate funnel metrics
      dashboard.funnelMetrics = await _calculateFunnelMetrics();
      
      // Calculate conversion rates
      dashboard.conversionRates = await _calculateConversionRates();
      
      // Get failure analysis
      dashboard.failureAnalysis = await _getFailureAnalysis();
      
      // Get performance metrics
      dashboard.performanceMetrics = await getPerformanceMetrics();
      
      // Get success rate trend
      dashboard.successRateTrend = await _getSuccessRateTrend();
      
      // Get recent events
      dashboard.recentEvents = await _getRecentEvents(limit: 20);
      
      dashboard.lastUpdated = DateTime.now();
      
      return dashboard;
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Failed to get dashboard data: $e');
      return SignupAnalyticsDashboard();
    }
  }

  /// Get signup success rate for time window
  Future<double> getSuccessRate({Duration? timeWindow}) async {
    await _ensureInitialized();
    
    try {
      timeWindow ??= _analyticsWindow;
      final cutoffTime = DateTime.now().subtract(timeWindow);
      
      final events = await _getFunnelEvents();
      final recentEvents = events.where((event) => 
        event.timestamp != null && event.timestamp!.isAfter(cutoffTime)
      ).toList();
      
      if (recentEvents.isEmpty) {
        return 100.0; // No events = 100% success rate
      }
      
      final successfulSignups = recentEvents.where((event) => 
        event.eventType == SignupEventType.signupCompleted
      ).length;
      
      final totalAttempts = recentEvents.where((event) => 
        event.eventType == SignupEventType.signupStarted
      ).length;
      
      if (totalAttempts == 0) {
        return 100.0;
      }
      
      return (successfulSignups / totalAttempts) * 100;
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Failed to calculate success rate: $e');
      return 0.0;
    }
  }

  /// Get conversion funnel data
  Future<Map<String, int>> getConversionFunnel({Duration? timeWindow}) async {
    await _ensureInitialized();
    
    try {
      timeWindow ??= _analyticsWindow;
      final cutoffTime = DateTime.now().subtract(timeWindow);
      
      final events = await _getFunnelEvents();
      final recentEvents = events.where((event) => 
        event.timestamp != null && event.timestamp!.isAfter(cutoffTime)
      ).toList();
      
      final funnel = <String, int>{};
      
      for (final eventType in SignupEventType.values) {
        funnel[eventType.toString()] = recentEvents.where((event) => 
          event.eventType == eventType
        ).length;
      }
      
      return funnel;
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Failed to get conversion funnel: $e');
      return {};
    }
  }

  /// Get failure breakdown by type
  Future<Map<String, int>> getFailureBreakdown({Duration? timeWindow}) async {
    await _ensureInitialized();
    
    try {
      timeWindow ??= _analyticsWindow;
      final cutoffTime = DateTime.now().subtract(timeWindow);
      
      final events = await _getFunnelEvents();
      final failureEvents = events.where((event) => 
        event.timestamp != null && 
        event.timestamp!.isAfter(cutoffTime) &&
        event.eventType == SignupEventType.signupFailed
      ).toList();
      
      final breakdown = <String, int>{};
      
      for (final event in failureEvents) {
        final reason = event.metadata?['failureReason'] ?? 'Unknown';
        breakdown[reason] = (breakdown[reason] ?? 0) + 1;
      }
      
      return breakdown;
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Failed to get failure breakdown: $e');
      return {};
    }
  }

  /// Get performance metrics
  Future<SignupPerformanceMetrics> getPerformanceMetrics() async {
    await _ensureInitialized();

    try {
      final metricsStr = await _secureStorage.read(key: _performanceMetricsKey);
      if (metricsStr == null) {
        return SignupPerformanceMetrics();
      }

      final metricsData = Map<String, dynamic>.from(jsonDecode(metricsStr));
      return SignupPerformanceMetrics.fromJson(metricsData);
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Failed to get performance metrics: $e');
      return SignupPerformanceMetrics();
    }
  }

  /// Record performance metric
  Future<void> recordPerformanceMetric(String operation, Duration duration) async {
    await _ensureInitialized();
    
    try {
      final metrics = await getPerformanceMetrics();
      
      if (!metrics.operationTimes.containsKey(operation)) {
        metrics.operationTimes[operation] = [];
      }
      
      metrics.operationTimes[operation]!.add(duration.inMilliseconds);
      
      // Keep only last 100 measurements per operation
      if (metrics.operationTimes[operation]!.length > 100) {
        metrics.operationTimes[operation] = 
          metrics.operationTimes[operation]!.sublist(
            metrics.operationTimes[operation]!.length - 100
          );
      }
      
      await _secureStorage.write(
        key: _performanceMetricsKey,
        value: jsonEncode(metrics.toJson()),
      );
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Failed to record performance metric: $e');
    }
  }

  /// Clear analytics data
  Future<void> clearAnalyticsData() async {
    await _ensureInitialized();
    
    try {
      await _secureStorage.delete(key: _analyticsDataKey);
      await _secureStorage.delete(key: _funnelEventsKey);
      await _secureStorage.delete(key: _performanceMetricsKey);
      await _secureStorage.delete(key: _alertHistoryKey);
      
      await _initializeAnalyticsData();
      
      await ComprehensiveLoggingService.logInfo('🗑️ Analytics data cleared');
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Failed to clear analytics data: $e');
    }
  }

  /// Initialize analytics data structure
  Future<void> _initializeAnalyticsData() async {
    try {
      final existingData = await _secureStorage.read(key: _analyticsDataKey);
      if (existingData == null) {
        final initialData = {
          'initialized': DateTime.now().toIso8601String(),
          'totalSignupAttempts': 0,
          'totalSuccessfulSignups': 0,
          'totalFailedSignups': 0,
        };
        
        await _secureStorage.write(
          key: _analyticsDataKey,
          value: jsonEncode(initialData),
        );
      }
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Failed to initialize analytics data: $e');
    }
  }

  /// Store funnel event
  Future<void> _storeFunnelEvent(SignupFunnelEvent event) async {
    try {
      final eventsStr = await _secureStorage.read(key: _funnelEventsKey);
      List<dynamic> events = [];
      
      if (eventsStr != null) {
        events = List<dynamic>.from(jsonDecode(eventsStr));
      }
      
      events.add(event.toJson());
      
      // Keep only recent events
      if (events.length > _maxEventHistory) {
        events = events.sublist(events.length - _maxEventHistory);
      }
      
      await _secureStorage.write(
        key: _funnelEventsKey,
        value: jsonEncode(events),
      );
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Failed to store funnel event: $e');
    }
  }

  /// Update analytics data
  Future<void> _updateAnalyticsData(SignupFunnelEvent event) async {
    try {
      final analyticsData = await _getAnalyticsData();
      
      switch (event.eventType) {
        case SignupEventType.signupStarted:
          analyticsData['totalSignupAttempts'] = (analyticsData['totalSignupAttempts'] ?? 0) + 1;
          break;
        case SignupEventType.signupCompleted:
          analyticsData['totalSuccessfulSignups'] = (analyticsData['totalSuccessfulSignups'] ?? 0) + 1;
          break;
        case SignupEventType.signupFailed:
          analyticsData['totalFailedSignups'] = (analyticsData['totalFailedSignups'] ?? 0) + 1;
          break;
        default:
          break;
      }
      
      analyticsData['lastUpdated'] = DateTime.now().toIso8601String();
      
      await _secureStorage.write(
        key: _analyticsDataKey,
        value: jsonEncode(analyticsData),
      );
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Failed to update analytics data: $e');
    }
  }

  /// Check success rate alert
  Future<void> _checkSuccessRateAlert() async {
    try {
      final successRate = await getSuccessRate();
      
      if (successRate < _successRateThreshold) {
        await _triggerSuccessRateAlert(successRate);
      }
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Failed to check success rate alert: $e');
    }
  }

  /// Trigger success rate alert
  Future<void> _triggerSuccessRateAlert(double successRate) async {
    try {
      await ComprehensiveLoggingService.logWarning(
        '🚨 SIGNUP SUCCESS RATE ALERT: ${successRate.toStringAsFixed(1)}% (threshold: $_successRateThreshold%)'
      );
      
      // Store alert in history
      final alert = {
        'timestamp': DateTime.now().toIso8601String(),
        'successRate': successRate,
        'threshold': _successRateThreshold,
        'type': 'success_rate_alert',
      };
      
      final alertHistoryStr = await _secureStorage.read(key: _alertHistoryKey);
      List<dynamic> alertHistory = [];
      
      if (alertHistoryStr != null) {
        alertHistory = List<dynamic>.from(jsonDecode(alertHistoryStr));
      }
      
      alertHistory.add(alert);
      
      // Keep only last 50 alerts
      if (alertHistory.length > 50) {
        alertHistory = alertHistory.sublist(alertHistory.length - 50);
      }
      
      await _secureStorage.write(
        key: _alertHistoryKey,
        value: jsonEncode(alertHistory),
      );
      
      // TODO: Send email <NAME_EMAIL>
      
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Failed to trigger success rate alert: $e');
    }
  }

  /// Get analytics data
  Future<Map<String, dynamic>> _getAnalyticsData() async {
    try {
      final dataStr = await _secureStorage.read(key: _analyticsDataKey);
      if (dataStr == null) {
        return {};
      }
      return Map<String, dynamic>.from(jsonDecode(dataStr));
    } catch (e) {
      return {};
    }
  }

  /// Get funnel events
  Future<List<SignupFunnelEvent>> _getFunnelEvents() async {
    try {
      final eventsStr = await _secureStorage.read(key: _funnelEventsKey);
      if (eventsStr == null) {
        return [];
      }
      
      final eventsList = List<dynamic>.from(jsonDecode(eventsStr));
      return eventsList.map((event) => SignupFunnelEvent.fromJson(event)).toList();
    } catch (e) {
      return [];
    }
  }

  /// Calculate funnel metrics
  Future<Map<String, int>> _calculateFunnelMetrics() async {
    final funnel = await getConversionFunnel();
    return funnel;
  }

  /// Calculate conversion rates
  Future<Map<String, double>> _calculateConversionRates() async {
    final funnel = await getConversionFunnel();
    final rates = <String, double>{};
    
    final started = funnel['SignupEventType.signupStarted'] ?? 0;
    if (started > 0) {
      for (final entry in funnel.entries) {
        rates[entry.key] = (entry.value / started) * 100;
      }
    }
    
    return rates;
  }

  /// Get failure analysis
  Future<Map<String, dynamic>> _getFailureAnalysis() async {
    final breakdown = await getFailureBreakdown();
    final total = breakdown.values.fold(0, (sum, count) => sum + count);
    
    return {
      'breakdown': breakdown,
      'totalFailures': total,
      'topFailureReason': breakdown.isNotEmpty 
        ? breakdown.entries.reduce((a, b) => a.value > b.value ? a : b).key
        : 'None',
    };
  }

  /// Get success rate trend
  Future<List<Map<String, dynamic>>> _getSuccessRateTrend() async {
    final trend = <Map<String, dynamic>>[];
    final now = DateTime.now();
    
    // Get hourly success rates for last 24 hours
    for (int i = 23; i >= 0; i--) {
      final hourStart = now.subtract(Duration(hours: i + 1));
      final hourEnd = now.subtract(Duration(hours: i));
      
      final events = await _getFunnelEvents();
      final hourEvents = events.where((event) => 
        event.timestamp != null &&
        event.timestamp!.isAfter(hourStart) &&
        event.timestamp!.isBefore(hourEnd)
      ).toList();
      
      final started = hourEvents.where((e) => e.eventType == SignupEventType.signupStarted).length;
      final completed = hourEvents.where((e) => e.eventType == SignupEventType.signupCompleted).length;
      
      final successRate = started > 0 ? (completed / started) * 100 : 100.0;
      
      trend.add({
        'hour': hourStart.hour,
        'successRate': successRate,
        'attempts': started,
        'completions': completed,
      });
    }
    
    return trend;
  }

  /// Get recent events
  Future<List<Map<String, dynamic>>> _getRecentEvents({int limit = 20}) async {
    final events = await _getFunnelEvents();
    return events
      .take(limit)
      .map((event) => event.toJson())
      .toList();
  }

  /// Generate session ID
  String _generateSessionId() {
    final random = Random();
    return DateTime.now().millisecondsSinceEpoch.toString() + 
           random.nextInt(1000).toString().padLeft(3, '0');
  }
}

/// Signup funnel event
class SignupFunnelEvent {
  SignupEventType eventType;
  DateTime? timestamp;
  String? sessionId;
  String? userId;
  Map<String, dynamic>? metadata;

  SignupFunnelEvent({
    required this.eventType,
    this.timestamp,
    this.sessionId,
    this.userId,
    this.metadata,
  });

  Map<String, dynamic> toJson() => {
    'eventType': eventType.toString(),
    'timestamp': timestamp?.toIso8601String(),
    'sessionId': sessionId,
    'userId': userId,
    'metadata': metadata,
  };

  factory SignupFunnelEvent.fromJson(Map<String, dynamic> json) {
    return SignupFunnelEvent(
      eventType: SignupEventType.values.firstWhere(
        (e) => e.toString() == json['eventType'],
        orElse: () => SignupEventType.unknown,
      ),
      timestamp: json['timestamp'] != null ? DateTime.parse(json['timestamp']) : null,
      sessionId: json['sessionId'],
      userId: json['userId'],
      metadata: json['metadata'] != null ? Map<String, dynamic>.from(json['metadata']) : null,
    );
  }
}

/// Signup event types
enum SignupEventType {
  signupStarted,
  emailEntered,
  passwordEntered,
  usernameEntered,
  genderSelected,
  emailVerificationSent,
  emailVerified,
  signupCompleted,
  signupFailed,
  signupAbandoned,
  unknown,
}

/// Analytics dashboard data
class SignupAnalyticsDashboard {
  Map<String, int> funnelMetrics = {};
  Map<String, double> conversionRates = {};
  Map<String, dynamic> failureAnalysis = {};
  SignupPerformanceMetrics performanceMetrics = SignupPerformanceMetrics();
  List<Map<String, dynamic>> successRateTrend = [];
  List<Map<String, dynamic>> recentEvents = [];
  DateTime? lastUpdated;

  SignupAnalyticsDashboard();

  Map<String, dynamic> toJson() => {
    'funnelMetrics': funnelMetrics,
    'conversionRates': conversionRates,
    'failureAnalysis': failureAnalysis,
    'performanceMetrics': performanceMetrics.toJson(),
    'successRateTrend': successRateTrend,
    'recentEvents': recentEvents,
    'lastUpdated': lastUpdated?.toIso8601String(),
  };
}

/// Performance metrics
class SignupPerformanceMetrics {
  Map<String, List<int>> operationTimes = {};

  SignupPerformanceMetrics();

  Map<String, dynamic> toJson() => {
    'operationTimes': operationTimes,
  };

  factory SignupPerformanceMetrics.fromJson(Map<String, dynamic> json) {
    final metrics = SignupPerformanceMetrics();
    if (json['operationTimes'] != null) {
      final times = Map<String, dynamic>.from(json['operationTimes']);
      for (final entry in times.entries) {
        metrics.operationTimes[entry.key] = List<int>.from(entry.value);
      }
    }
    return metrics;
  }
}
