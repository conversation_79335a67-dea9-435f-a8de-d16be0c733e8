// lib/services/proactive_coaching_service.dart

import 'package:flutter/foundation.dart';
import '../models/user_model.dart';
import 'user_spiritual_profile_service.dart';

/// 🔮 PROACTIVE COACHING SERVICE
/// 
/// Makes coaches truly anticipatory and strategic by:
/// - Analyzing user patterns to predict future challenges
/// - Identifying optimization opportunities before they become obvious
/// - Providing life strategy insights for long-term success
/// - Acting as a personal life strategist and guardian angel
/// 
/// This service transforms reactive coaching into proactive guidance.
class ProactiveCoachingService {
  
  /// Analyze user patterns and generate proactive insights
  static Future<ProactiveInsights> generateProactiveInsights({
    required User user,
    required String category,
    required List<String> recentMessages,
    required Map<String, dynamic> userBehaviorData,
  }) async {
    try {
      if (kDebugMode) print('🔮 Generating proactive insights for ${user.username}...');
      
      // 1. Analyze user patterns and trends
      final patternAnalysis = await _analyzeUserPatterns(user, recentMessages, userBehaviorData);
      
      // 2. Predict future challenges and opportunities
      final predictions = await _generatePredictions(patternAnalysis, category, user);
      
      // 3. Create preventive strategies
      final preventiveStrategies = await _generatePreventiveStrategies(predictions, user, category);
      
      // 4. Identify optimization opportunities
      final optimizationOpportunities = await _identifyOptimizationOpportunities(patternAnalysis, user);
      
      // 5. Generate life strategy insights
      final lifeStrategyInsights = await _generateLifeStrategyInsights(user, patternAnalysis, category);
      
      // 6. Create anticipatory guidance
      final anticipatoryGuidance = await _generateAnticipatoryGuidance(
        predictions, 
        preventiveStrategies, 
        optimizationOpportunities,
        user,
        category,
      );
      
      return ProactiveInsights(
        patternAnalysis: patternAnalysis,
        predictions: predictions,
        preventiveStrategies: preventiveStrategies,
        optimizationOpportunities: optimizationOpportunities,
        lifeStrategyInsights: lifeStrategyInsights,
        anticipatoryGuidance: anticipatoryGuidance,
        confidenceScore: _calculateConfidenceScore(patternAnalysis, predictions),
        timeHorizon: _determineTimeHorizon(predictions),
      );
      
    } catch (e) {
      if (kDebugMode) print('❌ Error generating proactive insights: $e');
      return ProactiveInsights.fallback(user, category);
    }
  }
  
  /// Analyze user patterns to understand trends and behaviors
  static Future<UserPatternAnalysis> _analyzeUserPatterns(
    User user,
    List<String> recentMessages,
    Map<String, dynamic> behaviorData,
  ) async {
    // Analyze message patterns
    final messagePatterns = _analyzeMessagePatterns(recentMessages);
    
    // Analyze engagement patterns
    final engagementPatterns = _analyzeEngagementPatterns(behaviorData);
    
    // Analyze progress patterns
    final progressPatterns = _analyzeProgressPatterns(user, behaviorData);
    
    // Analyze challenge patterns
    final challengePatterns = _analyzeChallengePatterns(recentMessages, behaviorData);
    
    // Analyze growth patterns
    final growthPatterns = _analyzeGrowthPatterns(user, behaviorData);
    
    return UserPatternAnalysis(
      messagePatterns: messagePatterns,
      engagementPatterns: engagementPatterns,
      progressPatterns: progressPatterns,
      challengePatterns: challengePatterns,
      growthPatterns: growthPatterns,
      overallTrend: _determineOverallTrend(messagePatterns, progressPatterns, growthPatterns),
      riskFactors: _identifyRiskFactors(challengePatterns, engagementPatterns),
      strengthFactors: _identifyStrengthFactors(progressPatterns, growthPatterns),
    );
  }
  
  /// Generate predictions about future challenges and opportunities
  static Future<FuturePredictions> _generatePredictions(
    UserPatternAnalysis patterns,
    String category,
    User user,
  ) async {
    final predictions = <Prediction>[];
    
    // Predict based on current trends
    if (patterns.overallTrend == UserTrend.declining) {
      predictions.add(Prediction(
        type: PredictionType.challenge,
        category: category,
        description: 'Potential motivation decline detected',
        probability: 0.75,
        timeframe: TimeFrame.shortTerm,
        severity: Severity.medium,
      ));
    }
    
    // Predict based on risk factors
    for (final risk in patterns.riskFactors) {
      predictions.add(Prediction(
        type: PredictionType.challenge,
        category: category,
        description: 'Risk factor: $risk',
        probability: 0.6,
        timeframe: TimeFrame.mediumTerm,
        severity: Severity.low,
      ));
    }
    
    // Predict based on growth patterns
    if (patterns.growthPatterns.isNotEmpty) {
      predictions.add(Prediction(
        type: PredictionType.opportunity,
        category: category,
        description: 'Growth acceleration opportunity',
        probability: 0.8,
        timeframe: TimeFrame.shortTerm,
        severity: Severity.high,
      ));
    }
    
    // Predict based on user level and experience
    if (user.level > 10 && user.level < 20) {
      predictions.add(Prediction(
        type: PredictionType.opportunity,
        category: 'Meta-Coaching',
        description: 'Ready for teaching and mentoring others',
        probability: 0.7,
        timeframe: TimeFrame.mediumTerm,
        severity: Severity.medium,
      ));
    }
    
    return FuturePredictions(
      predictions: predictions,
      overallOutlook: _determineOverallOutlook(predictions),
      keyInsights: _generateKeyInsights(predictions, patterns),
    );
  }
  
  /// Generate preventive strategies for predicted challenges
  static Future<List<PreventiveStrategy>> _generatePreventiveStrategies(
    FuturePredictions predictions,
    User user,
    String category,
  ) async {
    final strategies = <PreventiveStrategy>[];
    
    for (final prediction in predictions.predictions) {
      if (prediction.type == PredictionType.challenge) {
        strategies.add(PreventiveStrategy(
          targetPrediction: prediction,
          strategy: _generateStrategyForChallenge(prediction, category),
          actionSteps: _generateActionSteps(prediction, category),
          timeline: _generateTimeline(prediction.timeframe),
          successMetrics: _generateSuccessMetrics(prediction, category),
        ));
      }
    }
    
    return strategies;
  }
  
  /// Identify optimization opportunities before they become obvious
  static Future<List<OptimizationOpportunity>> _identifyOptimizationOpportunities(
    UserPatternAnalysis patterns,
    User user,
  ) async {
    final opportunities = <OptimizationOpportunity>[];
    
    // Cross-domain optimization opportunities
    opportunities.add(OptimizationOpportunity(
      type: OptimizationType.crossDomain,
      title: 'Health-Wealth Synergy Optimization',
      description: 'Optimize physical energy to boost productivity and earning potential',
      impact: Impact.high,
      effort: Effort.medium,
      domains: ['Health', 'Wealth'],
    ));
    
    opportunities.add(OptimizationOpportunity(
      type: OptimizationType.crossDomain,
      title: 'Purpose-Connection Alignment',
      description: 'Align life purpose with relationship building for deeper fulfillment',
      impact: Impact.high,
      effort: Effort.low,
      domains: ['Purpose', 'Connection'],
    ));
    
    // Level-based opportunities
    if (user.level > 15) {
      opportunities.add(OptimizationOpportunity(
        type: OptimizationType.advancement,
        title: 'Mastery Teaching Opportunity',
        description: 'Share your expertise to accelerate your own growth',
        impact: Impact.medium,
        effort: Effort.medium,
        domains: ['Purpose', 'Connection'],
      ));
    }
    
    // Pattern-based opportunities
    if (patterns.strengthFactors.isNotEmpty) {
      opportunities.add(OptimizationOpportunity(
        type: OptimizationType.leverage,
        title: 'Strength Amplification',
        description: 'Leverage your natural strengths for exponential growth',
        impact: Impact.high,
        effort: Effort.low,
        domains: [_getPrimaryDomain(patterns.strengthFactors)],
      ));
    }
    
    return opportunities;
  }
  
  /// Generate life strategy insights for long-term success
  static Future<List<LifeStrategyInsight>> _generateLifeStrategyInsights(
    User user,
    UserPatternAnalysis patterns,
    String category,
  ) async {
    final insights = <LifeStrategyInsight>[];
    
    // Strategic life planning insights
    insights.add(LifeStrategyInsight(
      type: StrategyType.longTermVision,
      title: 'Life Trajectory Optimization',
      insight: 'Your current growth pattern suggests breakthrough potential in 6-12 months',
      actionable: true,
      timeHorizon: TimeFrame.longTerm,
      domains: ['Health', 'Wealth', 'Purpose', 'Connection'],
    ));
    
    // Personal development strategy
    insights.add(LifeStrategyInsight(
      type: StrategyType.personalDevelopment,
      title: 'Exponential Growth Strategy',
      insight: 'Focus on compound improvements: 1% daily gains = 37x annual growth',
      actionable: true,
      timeHorizon: TimeFrame.mediumTerm,
      domains: [category],
    ));
    
    // Relationship strategy
    if (user.level > 10) {
      insights.add(LifeStrategyInsight(
        type: StrategyType.relationship,
        title: 'Mentorship Transition Strategy',
        insight: 'You\'re ready to transition from student to teacher - this accelerates your own growth',
        actionable: true,
        timeHorizon: TimeFrame.mediumTerm,
        domains: ['Connection', 'Purpose'],
      ));
    }
    
    return insights;
  }
  
  /// Generate anticipatory guidance based on all insights
  static Future<AnticipatoryGuidance> _generateAnticipatoryGuidance(
    FuturePredictions predictions,
    List<PreventiveStrategy> preventiveStrategies,
    List<OptimizationOpportunity> opportunities,
    User user,
    String category,
  ) async {
    // Get spiritual guidance for appropriate wisdom
    final spiritualGuidance = await UserSpiritualProfileService.getSpiritualWisdomGuidance(user.id);
    
    return AnticipatoryGuidance(
      immediateActions: _generateImmediateActions(predictions, opportunities),
      weeklyFocus: _generateWeeklyFocus(preventiveStrategies, opportunities),
      monthlyStrategy: _generateMonthlyStrategy(predictions, opportunities),
      quarterlyVision: _generateQuarterlyVision(opportunities, user),
      wisdomIntegration: _integrateWisdom(spiritualGuidance, predictions, opportunities),
      proactiveQuestions: _generateProactiveQuestions(predictions, opportunities, category),
    );
  }
  
  // Helper methods for pattern analysis
  static MessagePatterns _analyzeMessagePatterns(List<String> messages) {
    return MessagePatterns(
      frequency: messages.length,
      averageLength: messages.isEmpty ? 0 : messages.map((m) => m.length).reduce((a, b) => a + b) / messages.length,
      emotionalTone: _analyzeEmotionalTone(messages),
      topicConsistency: _analyzeTopicConsistency(messages),
      complexityTrend: _analyzeComplexityTrend(messages),
    );
  }
  
  static EngagementPatterns _analyzeEngagementPatterns(Map<String, dynamic> behaviorData) {
    return EngagementPatterns(
      sessionFrequency: behaviorData['sessionFrequency'] ?? 0,
      sessionDuration: behaviorData['sessionDuration'] ?? 0.0,
      responseTime: behaviorData['responseTime'] ?? 0.0,
      featureUsage: behaviorData['featureUsage'] ?? {},
      consistencyScore: behaviorData['consistencyScore'] ?? 0.0,
    );
  }
  
  static ProgressPatterns _analyzeProgressPatterns(User user, Map<String, dynamic> behaviorData) {
    return ProgressPatterns(
      expGrowthRate: _calculateExpGrowthRate(user, behaviorData),
      levelProgression: _analyzeLevelProgression(user, behaviorData),
      goalCompletion: behaviorData['goalCompletion'] ?? 0.0,
      habitConsistency: behaviorData['habitConsistency'] ?? 0.0,
      overallMomentum: _calculateOverallMomentum(user, behaviorData),
    );
  }
  
  static List<String> _analyzeChallengePatterns(List<String> messages, Map<String, dynamic> behaviorData) {
    final challenges = <String>[];
    
    // Analyze messages for challenge indicators
    for (final message in messages) {
      final lowerMessage = message.toLowerCase();
      if (lowerMessage.contains(RegExp(r'\b(stuck|difficult|hard|struggling|problem)\b'))) {
        challenges.add('Difficulty expressing challenges');
      }
      if (lowerMessage.contains(RegExp(r'\b(time|busy|schedule)\b'))) {
        challenges.add('Time management issues');
      }
      if (lowerMessage.contains(RegExp(r'\b(motivation|energy|tired)\b'))) {
        challenges.add('Energy and motivation concerns');
      }
    }
    
    return challenges.toSet().toList(); // Remove duplicates
  }
  
  static List<String> _analyzeGrowthPatterns(User user, Map<String, dynamic> behaviorData) {
    final growthPatterns = <String>[];
    
    if (user.level > 5) growthPatterns.add('Consistent progression');
    if (user.totalExp > 1000) growthPatterns.add('High engagement');
    
    return growthPatterns;
  }
  
  // Helper methods for calculations
  static UserTrend _determineOverallTrend(MessagePatterns messages, ProgressPatterns progress, List<String> growth) {
    if (progress.overallMomentum > 0.7) return UserTrend.ascending;
    if (progress.overallMomentum < 0.3) return UserTrend.declining;
    return UserTrend.stable;
  }
  
  static List<String> _identifyRiskFactors(List<String> challenges, EngagementPatterns engagement) {
    final risks = <String>[];
    if (challenges.length > 2) risks.add('Multiple concurrent challenges');
    if (engagement.consistencyScore < 0.5) risks.add('Inconsistent engagement');
    return risks;
  }
  
  static List<String> _identifyStrengthFactors(ProgressPatterns progress, List<String> growth) {
    final strengths = <String>[];
    if (progress.overallMomentum > 0.6) strengths.add('Strong momentum');
    if (growth.isNotEmpty) strengths.add('Active growth mindset');
    return strengths;
  }
  
  static Outlook _determineOverallOutlook(List<Prediction> predictions) {
    final challenges = predictions.where((p) => p.type == PredictionType.challenge).length;
    final opportunities = predictions.where((p) => p.type == PredictionType.opportunity).length;
    
    if (opportunities > challenges) return Outlook.positive;
    if (challenges > opportunities) return Outlook.cautious;
    return Outlook.balanced;
  }
  
  static List<String> _generateKeyInsights(List<Prediction> predictions, UserPatternAnalysis patterns) {
    return [
      'Pattern analysis reveals ${patterns.overallTrend.name} trajectory',
      'Predicted ${predictions.length} key developments in coming period',
      'Optimization opportunities identified across multiple domains',
    ];
  }
  
  // Placeholder methods for strategy generation
  static String _generateStrategyForChallenge(Prediction prediction, String category) {
    return 'Preventive strategy for ${prediction.description}';
  }
  
  static List<String> _generateActionSteps(Prediction prediction, String category) {
    return ['Action step 1', 'Action step 2', 'Action step 3'];
  }
  
  static String _generateTimeline(TimeFrame timeframe) {
    switch (timeframe) {
      case TimeFrame.shortTerm:
        return '1-4 weeks';
      case TimeFrame.mediumTerm:
        return '1-3 months';
      case TimeFrame.longTerm:
        return '3-12 months';
    }
  }
  
  static List<String> _generateSuccessMetrics(Prediction prediction, String category) {
    return ['Metric 1', 'Metric 2', 'Metric 3'];
  }
  
  static String _getPrimaryDomain(List<String> strengths) {
    return 'Health'; // Placeholder
  }
  
  static double _calculateConfidenceScore(UserPatternAnalysis patterns, FuturePredictions predictions) {
    return 0.75; // Placeholder
  }
  
  static TimeFrame _determineTimeHorizon(FuturePredictions predictions) {
    return TimeFrame.mediumTerm; // Placeholder
  }
  
  // Placeholder methods for detailed analysis
  static EmotionalTone _analyzeEmotionalTone(List<String> messages) => EmotionalTone.neutral;
  static double _analyzeTopicConsistency(List<String> messages) => 0.7;
  static ComplexityTrend _analyzeComplexityTrend(List<String> messages) => ComplexityTrend.stable;
  static double _calculateExpGrowthRate(User user, Map<String, dynamic> data) => 0.1;
  static LevelProgression _analyzeLevelProgression(User user, Map<String, dynamic> data) => LevelProgression.steady;
  static double _calculateOverallMomentum(User user, Map<String, dynamic> data) => 0.6;
  
  // Placeholder methods for guidance generation
  static List<String> _generateImmediateActions(FuturePredictions predictions, List<OptimizationOpportunity> opportunities) {
    return ['Immediate action 1', 'Immediate action 2'];
  }
  
  static String _generateWeeklyFocus(List<PreventiveStrategy> strategies, List<OptimizationOpportunity> opportunities) {
    return 'Weekly focus area';
  }
  
  static String _generateMonthlyStrategy(FuturePredictions predictions, List<OptimizationOpportunity> opportunities) {
    return 'Monthly strategic focus';
  }
  
  static String _generateQuarterlyVision(List<OptimizationOpportunity> opportunities, User user) {
    return 'Quarterly vision and goals';
  }
  
  static String _integrateWisdom(SpiritualWisdomGuidance guidance, FuturePredictions predictions, List<OptimizationOpportunity> opportunities) {
    if (guidance.canShareReligiousContent) {
      return 'Faith-based wisdom for your journey ahead';
    }
    return 'Universal wisdom and Stoic principles for proactive living';
  }
  
  static List<String> _generateProactiveQuestions(FuturePredictions predictions, List<OptimizationOpportunity> opportunities, String category) {
    return [
      'What would you do if you knew you couldn\'t fail?',
      'How might your future self thank you for actions you take today?',
      'What patterns in your life are serving you, and which need to evolve?',
    ];
  }
}

// Data models for proactive coaching
class ProactiveInsights {
  final UserPatternAnalysis patternAnalysis;
  final FuturePredictions predictions;
  final List<PreventiveStrategy> preventiveStrategies;
  final List<OptimizationOpportunity> optimizationOpportunities;
  final List<LifeStrategyInsight> lifeStrategyInsights;
  final AnticipatoryGuidance anticipatoryGuidance;
  final double confidenceScore;
  final TimeFrame timeHorizon;

  ProactiveInsights({
    required this.patternAnalysis,
    required this.predictions,
    required this.preventiveStrategies,
    required this.optimizationOpportunities,
    required this.lifeStrategyInsights,
    required this.anticipatoryGuidance,
    required this.confidenceScore,
    required this.timeHorizon,
  });

  factory ProactiveInsights.fallback(User user, String category) {
    return ProactiveInsights(
      patternAnalysis: UserPatternAnalysis.basic(),
      predictions: FuturePredictions.basic(),
      preventiveStrategies: [],
      optimizationOpportunities: [],
      lifeStrategyInsights: [],
      anticipatoryGuidance: AnticipatoryGuidance.basic(),
      confidenceScore: 0.5,
      timeHorizon: TimeFrame.shortTerm,
    );
  }
}

class UserPatternAnalysis {
  final MessagePatterns messagePatterns;
  final EngagementPatterns engagementPatterns;
  final ProgressPatterns progressPatterns;
  final List<String> challengePatterns;
  final List<String> growthPatterns;
  final UserTrend overallTrend;
  final List<String> riskFactors;
  final List<String> strengthFactors;

  UserPatternAnalysis({
    required this.messagePatterns,
    required this.engagementPatterns,
    required this.progressPatterns,
    required this.challengePatterns,
    required this.growthPatterns,
    required this.overallTrend,
    required this.riskFactors,
    required this.strengthFactors,
  });

  factory UserPatternAnalysis.basic() {
    return UserPatternAnalysis(
      messagePatterns: MessagePatterns.basic(),
      engagementPatterns: EngagementPatterns.basic(),
      progressPatterns: ProgressPatterns.basic(),
      challengePatterns: [],
      growthPatterns: [],
      overallTrend: UserTrend.stable,
      riskFactors: [],
      strengthFactors: [],
    );
  }
}

class MessagePatterns {
  final int frequency;
  final double averageLength;
  final EmotionalTone emotionalTone;
  final double topicConsistency;
  final ComplexityTrend complexityTrend;

  MessagePatterns({
    required this.frequency,
    required this.averageLength,
    required this.emotionalTone,
    required this.topicConsistency,
    required this.complexityTrend,
  });

  factory MessagePatterns.basic() {
    return MessagePatterns(
      frequency: 0,
      averageLength: 0.0,
      emotionalTone: EmotionalTone.neutral,
      topicConsistency: 0.5,
      complexityTrend: ComplexityTrend.stable,
    );
  }
}

class EngagementPatterns {
  final int sessionFrequency;
  final double sessionDuration;
  final double responseTime;
  final Map<String, dynamic> featureUsage;
  final double consistencyScore;

  EngagementPatterns({
    required this.sessionFrequency,
    required this.sessionDuration,
    required this.responseTime,
    required this.featureUsage,
    required this.consistencyScore,
  });

  factory EngagementPatterns.basic() {
    return EngagementPatterns(
      sessionFrequency: 0,
      sessionDuration: 0.0,
      responseTime: 0.0,
      featureUsage: {},
      consistencyScore: 0.5,
    );
  }
}

class ProgressPatterns {
  final double expGrowthRate;
  final LevelProgression levelProgression;
  final double goalCompletion;
  final double habitConsistency;
  final double overallMomentum;

  ProgressPatterns({
    required this.expGrowthRate,
    required this.levelProgression,
    required this.goalCompletion,
    required this.habitConsistency,
    required this.overallMomentum,
  });

  factory ProgressPatterns.basic() {
    return ProgressPatterns(
      expGrowthRate: 0.0,
      levelProgression: LevelProgression.steady,
      goalCompletion: 0.5,
      habitConsistency: 0.5,
      overallMomentum: 0.5,
    );
  }
}

class FuturePredictions {
  final List<Prediction> predictions;
  final Outlook overallOutlook;
  final List<String> keyInsights;

  FuturePredictions({
    required this.predictions,
    required this.overallOutlook,
    required this.keyInsights,
  });

  factory FuturePredictions.basic() {
    return FuturePredictions(
      predictions: [],
      overallOutlook: Outlook.balanced,
      keyInsights: [],
    );
  }
}

class Prediction {
  final PredictionType type;
  final String category;
  final String description;
  final double probability;
  final TimeFrame timeframe;
  final Severity severity;

  Prediction({
    required this.type,
    required this.category,
    required this.description,
    required this.probability,
    required this.timeframe,
    required this.severity,
  });
}

class PreventiveStrategy {
  final Prediction targetPrediction;
  final String strategy;
  final List<String> actionSteps;
  final String timeline;
  final List<String> successMetrics;

  PreventiveStrategy({
    required this.targetPrediction,
    required this.strategy,
    required this.actionSteps,
    required this.timeline,
    required this.successMetrics,
  });
}

class OptimizationOpportunity {
  final OptimizationType type;
  final String title;
  final String description;
  final Impact impact;
  final Effort effort;
  final List<String> domains;

  OptimizationOpportunity({
    required this.type,
    required this.title,
    required this.description,
    required this.impact,
    required this.effort,
    required this.domains,
  });
}

class LifeStrategyInsight {
  final StrategyType type;
  final String title;
  final String insight;
  final bool actionable;
  final TimeFrame timeHorizon;
  final List<String> domains;

  LifeStrategyInsight({
    required this.type,
    required this.title,
    required this.insight,
    required this.actionable,
    required this.timeHorizon,
    required this.domains,
  });
}

class AnticipatoryGuidance {
  final List<String> immediateActions;
  final String weeklyFocus;
  final String monthlyStrategy;
  final String quarterlyVision;
  final String wisdomIntegration;
  final List<String> proactiveQuestions;

  AnticipatoryGuidance({
    required this.immediateActions,
    required this.weeklyFocus,
    required this.monthlyStrategy,
    required this.quarterlyVision,
    required this.wisdomIntegration,
    required this.proactiveQuestions,
  });

  factory AnticipatoryGuidance.basic() {
    return AnticipatoryGuidance(
      immediateActions: [],
      weeklyFocus: '',
      monthlyStrategy: '',
      quarterlyVision: '',
      wisdomIntegration: '',
      proactiveQuestions: [],
    );
  }
}

// Enums for proactive coaching
enum UserTrend { ascending, stable, declining }
enum EmotionalTone { positive, neutral, negative, mixed }
enum ComplexityTrend { increasing, stable, decreasing }
enum LevelProgression { rapid, steady, slow, stagnant }
enum PredictionType { challenge, opportunity, neutral }
enum TimeFrame { shortTerm, mediumTerm, longTerm }
enum Severity { low, medium, high, critical }
enum Outlook { positive, balanced, cautious, concerning }
enum OptimizationType { crossDomain, advancement, leverage, innovation }
enum Impact { low, medium, high, transformative }
enum Effort { low, medium, high, intensive }
enum StrategyType { longTermVision, personalDevelopment, relationship, career, health, wealth }
