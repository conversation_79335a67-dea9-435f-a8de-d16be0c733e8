import 'dart:io';
import 'package:image_picker/image_picker.dart';
import 'package:file_picker/file_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import '../utils/debug_logger.dart';

/// Camera service for bounty photo proof capture
/// 
/// Handles:
/// - Camera access and photo capture
/// - Photo storage and file management
/// - Image compression and optimization
/// - Error handling and permissions
class CameraService {
  static final ImagePicker _picker = ImagePicker();
  
  /// Capture photo for bounty proof
  /// 
  /// Returns the file path of the captured photo or null if cancelled/failed
  static Future<String?> capturePhotoProof({
    required String bountyId,
    ImageSource source = ImageSource.camera,
  }) async {
    try {
      DebugLogger.log('CameraService', 'Starting photo capture for bounty: $bountyId');
      
      // Capture photo
      final XFile? photo = await _picker.pickImage(
        source: source,
        imageQuality: 80, // Compress to 80% quality
        maxWidth: 1920,
        maxHeight: 1080,
      );
      
      if (photo == null) {
        DebugLogger.log('CameraService', 'Photo capture cancelled by user');
        return null;
      }
      
      // Get app documents directory
      final Directory appDir = await getApplicationDocumentsDirectory();
      final String bountyPhotosDir = path.join(appDir.path, 'bounty_photos');
      
      // Create directory if it doesn't exist
      final Directory photosDir = Directory(bountyPhotosDir);
      if (!await photosDir.exists()) {
        await photosDir.create(recursive: true);
        DebugLogger.log('CameraService', 'Created bounty photos directory: $bountyPhotosDir');
      }
      
      // Generate unique filename
      final String timestamp = DateTime.now().millisecondsSinceEpoch.toString();
      final String fileName = 'bounty_${bountyId}_$timestamp.jpg';
      final String filePath = path.join(bountyPhotosDir, fileName);
      
      // Copy photo to app directory
      final File photoFile = File(photo.path);
      final File savedPhoto = await photoFile.copy(filePath);
      
      DebugLogger.log('CameraService', 'Photo saved successfully: $filePath');
      DebugLogger.log('CameraService', 'File size: ${await savedPhoto.length()} bytes');
      
      return filePath;
      
    } catch (e, stackTrace) {
      DebugLogger.error('CameraService', 'Failed to capture photo', e, stackTrace);
      return null;
    }
  }
  
  /// Capture photo from gallery for bounty proof
  static Future<String?> selectPhotoFromGallery({
    required String bountyId,
  }) async {
    return capturePhotoProof(
      bountyId: bountyId,
      source: ImageSource.gallery,
    );
  }

  /// Alternative photo selection using file_picker (more reliable on iOS)
  static Future<String?> selectPhotoWithFilePicker({
    required String bountyId,
  }) async {
    try {
      DebugLogger.log('CameraService', 'Starting file picker for bounty: $bountyId');

      // Use file_picker instead of image_picker to avoid iOS freezing issues
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.image,
        allowMultiple: false,
        allowCompression: true,
      );

      if (result == null || result.files.single.path == null) {
        DebugLogger.log('CameraService', 'File picker cancelled by user');
        return null;
      }

      final String sourcePath = result.files.single.path!;

      // Get app documents directory
      final Directory appDir = await getApplicationDocumentsDirectory();
      final String bountyPhotosDir = path.join(appDir.path, 'bounty_photos');

      // Create directory if it doesn't exist
      final Directory photosDir = Directory(bountyPhotosDir);
      if (!await photosDir.exists()) {
        await photosDir.create(recursive: true);
        DebugLogger.log('CameraService', 'Created bounty photos directory: $bountyPhotosDir');
      }

      // Generate unique filename
      final String timestamp = DateTime.now().millisecondsSinceEpoch.toString();
      final String fileName = 'bounty_${bountyId}_$timestamp.jpg';
      final String filePath = path.join(bountyPhotosDir, fileName);

      // Copy photo to app directory
      final File sourceFile = File(sourcePath);
      final File savedPhoto = await sourceFile.copy(filePath);

      DebugLogger.log('CameraService', 'Photo saved successfully: $filePath');
      DebugLogger.log('CameraService', 'File size: ${await savedPhoto.length()} bytes');

      return filePath;

    } catch (e, stackTrace) {
      DebugLogger.error('CameraService', 'Failed to select photo with file picker', e, stackTrace);
      return null;
    }
  }
  
  /// Check if photo file exists and is valid
  static Future<bool> validatePhotoProof(String photoPath) async {
    try {
      final File photoFile = File(photoPath);
      
      if (!await photoFile.exists()) {
        DebugLogger.warn('CameraService', 'Photo file does not exist: $photoPath');
        return false;
      }
      
      final int fileSize = await photoFile.length();
      if (fileSize == 0) {
        DebugLogger.warn('CameraService', 'Photo file is empty: $photoPath');
        return false;
      }
      
      DebugLogger.log('CameraService', 'Photo validation successful: $photoPath ($fileSize bytes)');
      return true;
      
    } catch (e, stackTrace) {
      DebugLogger.error('CameraService', 'Photo validation failed', e, stackTrace);
      return false;
    }
  }
  
  /// Delete photo file
  static Future<bool> deletePhoto(String photoPath) async {
    try {
      final File photoFile = File(photoPath);
      
      if (await photoFile.exists()) {
        await photoFile.delete();
        DebugLogger.log('CameraService', 'Photo deleted successfully: $photoPath');
        return true;
      } else {
        DebugLogger.warn('CameraService', 'Photo file not found for deletion: $photoPath');
        return false;
      }
      
    } catch (e, stackTrace) {
      DebugLogger.error('CameraService', 'Failed to delete photo', e, stackTrace);
      return false;
    }
  }
  
  /// Get photo file size in bytes
  static Future<int?> getPhotoSize(String photoPath) async {
    try {
      final File photoFile = File(photoPath);
      
      if (await photoFile.exists()) {
        return await photoFile.length();
      }
      
      return null;
      
    } catch (e, stackTrace) {
      DebugLogger.error('CameraService', 'Failed to get photo size', e, stackTrace);
      return null;
    }
  }
  
  /// Clean up old bounty photos (older than 30 days)
  static Future<void> cleanupOldPhotos() async {
    try {
      final Directory appDir = await getApplicationDocumentsDirectory();
      final String bountyPhotosDir = path.join(appDir.path, 'bounty_photos');
      final Directory photosDir = Directory(bountyPhotosDir);
      
      if (!await photosDir.exists()) {
        DebugLogger.log('CameraService', 'Bounty photos directory does not exist, nothing to clean');
        return;
      }
      
      final DateTime cutoffDate = DateTime.now().subtract(const Duration(days: 30));
      final List<FileSystemEntity> files = await photosDir.list().toList();
      
      int deletedCount = 0;
      for (final FileSystemEntity file in files) {
        if (file is File) {
          final FileStat stat = await file.stat();
          if (stat.modified.isBefore(cutoffDate)) {
            await file.delete();
            deletedCount++;
            DebugLogger.log('CameraService', 'Deleted old photo: ${file.path}');
          }
        }
      }
      
      DebugLogger.log('CameraService', 'Cleanup completed: $deletedCount old photos deleted');
      
    } catch (e, stackTrace) {
      DebugLogger.error('CameraService', 'Failed to cleanup old photos', e, stackTrace);
    }
  }
  
  /// Get total storage used by bounty photos
  static Future<int> getTotalStorageUsed() async {
    try {
      final Directory appDir = await getApplicationDocumentsDirectory();
      final String bountyPhotosDir = path.join(appDir.path, 'bounty_photos');
      final Directory photosDir = Directory(bountyPhotosDir);
      
      if (!await photosDir.exists()) {
        return 0;
      }
      
      final List<FileSystemEntity> files = await photosDir.list().toList();
      int totalSize = 0;
      
      for (final FileSystemEntity file in files) {
        if (file is File) {
          final int fileSize = await file.length();
          totalSize += fileSize;
        }
      }
      
      DebugLogger.log('CameraService', 'Total storage used: $totalSize bytes');
      return totalSize;
      
    } catch (e, stackTrace) {
      DebugLogger.error('CameraService', 'Failed to calculate storage usage', e, stackTrace);
      return 0;
    }
  }
  
  /// Format file size for display
  static String formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }
}
