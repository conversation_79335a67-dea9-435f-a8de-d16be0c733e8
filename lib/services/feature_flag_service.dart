// lib/services/feature_flag_service.dart

import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

/// Service for managing feature flags and A/B testing
class FeatureFlagService {
  static const FlutterSecureStorage _storage = FlutterSecureStorage();
  static const String _flagsKey = 'feature_flags';
  static const String _experimentsKey = 'ab_experiments';
  static const String _userGroupKey = 'user_group_assignments';
  
  static Map<String, dynamic> _cachedFlags = {};
  static Map<String, dynamic> _cachedExperiments = {};
  static bool _isInitialized = false;

  /// Check if the feature flag service is initialized
  static bool get isInitialized => _isInitialized;

  /// Initialize feature flag service
  static Future<void> initialize() async {
    try {
      if (kDebugMode) {
        print('🚩 Initializing feature flag service...');
      }
      
      // Load feature flags
      await _loadFeatureFlags();
      
      // Load A/B experiments
      await _loadExperiments();
      
      // Initialize default flags if needed
      await _initializeDefaultFlags();
      
      _isInitialized = true;
      
      if (kDebugMode) {
        print('✅ Feature flag service initialized');
        print('📊 Active flags: ${_cachedFlags.keys.length}');
        print('🧪 Active experiments: ${_cachedExperiments.keys.length}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to initialize feature flags: $e');
      }
    }
  }

  /// Check if a feature flag is enabled
  static bool isEnabled(String flagName, {String? userId}) {
    try {
      if (!_isInitialized) {
        if (kDebugMode) {
          print('⚠️ Feature flags not initialized, returning false for $flagName');
        }
        return false;
      }

      final flag = _cachedFlags[flagName];
      if (flag == null) {
        // Try to recover by checking if we need to reinitialize default flags
        if (_cachedFlags.isEmpty) {
          if (kDebugMode) {
            print('🔄 Flag cache empty, attempting to reinitialize defaults...');
          }
          // Synchronously initialize defaults for critical flags
          _initializeDefaultFlagsSync();

          // Try again after reinitialization
          final retryFlag = _cachedFlags[flagName];
          if (retryFlag != null) {
            return retryFlag['enabled'] == true;
          }
        }

        // Return sensible defaults for known flags to prevent warnings
        if (flagName == 'hybrid_model_routing') return true;
        if (flagName == 'enhanced_ai_responses') return true;
        if (flagName == 'performance_monitoring') return true;
        if (flagName == 'system_load_protection') return true;
        if (flagName == 'offline_mode') return true;
        if (flagName == 'proactive_checkins') return true;
        if (flagName == 'user_feedback_collection') return false;

        if (kDebugMode) {
          print('⚠️ Unknown feature flag: $flagName (using default: false)');
        }
        return false;
      }
      
      // Check if flag is globally enabled
      if (flag['enabled'] != true) {
        return false;
      }
      
      // Check rollout percentage
      final rolloutPercentage = flag['rolloutPercentage'] ?? 100;
      if (rolloutPercentage < 100) {
        final userHash = _getUserHash(userId ?? 'anonymous', flagName);
        if (userHash > rolloutPercentage) {
          return false;
        }
      }
      
      // Check user group targeting
      final targetGroups = flag['targetGroups'] as List<String>?;
      if (targetGroups != null && targetGroups.isNotEmpty && userId != null) {
        final userGroup = _getUserGroup(userId);
        if (!targetGroups.contains(userGroup)) {
          return false;
        }
      }
      
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error checking feature flag $flagName: $e');
      }
      return false;
    }
  }

  /// Get A/B test variant for a user
  static String getExperimentVariant(String experimentName, {String? userId}) {
    try {
      if (!_isInitialized) {
        return 'control';
      }
      
      final experiment = _cachedExperiments[experimentName];
      if (experiment == null || experiment['enabled'] != true) {
        return 'control';
      }
      
      final variants = experiment['variants'] as Map<String, dynamic>?;
      if (variants == null || variants.isEmpty) {
        return 'control';
      }
      
      // Get consistent variant assignment based on user ID
      final userHash = _getUserHash(userId ?? 'anonymous', experimentName);
      final variantNames = variants.keys.toList();
      final variantIndex = userHash % variantNames.length;
      
      return variantNames[variantIndex];
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting experiment variant $experimentName: $e');
      }
      return 'control';
    }
  }

  /// Track experiment exposure
  static Future<void> trackExperimentExposure({
    required String experimentName,
    required String variant,
    String? userId,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final exposure = {
        'experimentName': experimentName,
        'variant': variant,
        'userId': userId,
        'timestamp': DateTime.now().toIso8601String(),
        'metadata': metadata,
      };
      
      // Store exposure for analytics
      final exposureKey = 'experiment_exposure_${experimentName}_${userId ?? 'anonymous'}';
      await _storage.write(key: exposureKey, value: jsonEncode(exposure));
      
      if (kDebugMode) {
        print('🧪 Experiment exposure tracked: $experimentName -> $variant');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to track experiment exposure: $e');
      }
    }
  }

  /// Update feature flag
  static Future<void> updateFeatureFlag({
    required String flagName,
    bool? enabled,
    int? rolloutPercentage,
    List<String>? targetGroups,
    String? description,
  }) async {
    try {
      final flags = await _getStoredFlags();
      
      if (!flags.containsKey(flagName)) {
        flags[flagName] = {};
      }
      
      final flag = flags[flagName] as Map<String, dynamic>;
      
      if (enabled != null) flag['enabled'] = enabled;
      if (rolloutPercentage != null) flag['rolloutPercentage'] = rolloutPercentage;
      if (targetGroups != null) flag['targetGroups'] = targetGroups;
      if (description != null) flag['description'] = description;
      
      flag['lastUpdated'] = DateTime.now().toIso8601String();
      
      await _storage.write(key: _flagsKey, value: jsonEncode(flags));
      _cachedFlags = flags;
      
      if (kDebugMode) {
        print('🚩 Feature flag updated: $flagName');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to update feature flag: $e');
      }
    }
  }

  /// Create A/B experiment
  static Future<void> createExperiment({
    required String experimentName,
    required Map<String, dynamic> variants,
    bool enabled = true,
    String? description,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final experiments = await _getStoredExperiments();
      
      experiments[experimentName] = {
        'enabled': enabled,
        'variants': variants,
        'description': description,
        'startDate': startDate?.toIso8601String(),
        'endDate': endDate?.toIso8601String(),
        'createdAt': DateTime.now().toIso8601String(),
      };
      
      await _storage.write(key: _experimentsKey, value: jsonEncode(experiments));
      _cachedExperiments = experiments;
      
      if (kDebugMode) {
        print('🧪 A/B experiment created: $experimentName');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to create experiment: $e');
      }
    }
  }

  /// Get feature flag status
  static Map<String, dynamic> getFeatureFlagStatus() {
    try {
      return {
        'isInitialized': _isInitialized,
        'totalFlags': _cachedFlags.length,
        'enabledFlags': _cachedFlags.values.where((f) => f['enabled'] == true).length,
        'totalExperiments': _cachedExperiments.length,
        'activeExperiments': _cachedExperiments.values.where((e) => e['enabled'] == true).length,
        'flags': _cachedFlags,
        'experiments': _cachedExperiments,
      };
    } catch (e) {
      return {
        'error': 'Failed to get status: $e',
        'isInitialized': _isInitialized,
      };
    }
  }

  // ═══════════════════════════════════════════════════════════════
  // RELEASE MODE CONFIGURATION
  // ═══════════════════════════════════════════════════════════════

  /// Check if app is in release mode (production)
  static bool get isReleaseMode => !kDebugMode;

  /// Check if debug features should be shown
  static bool get shouldShowDebugFeatures => kDebugMode;

  /// Check if performance monitoring should be enabled
  static bool get shouldEnablePerformanceMonitoring => kDebugMode;

  /// Check if admin features should be accessible
  static bool get shouldShowAdminFeatures => kDebugMode;

  /// Check if secret buttons should be visible
  static bool get shouldShowSecretButtons => kDebugMode;

  /// Check if system monitoring should be enabled
  static bool get shouldEnableSystemMonitoring => kDebugMode;

  /// Check if developer tools should be accessible
  static bool get shouldShowDeveloperTools => kDebugMode;

  /// Get user's experiment assignments
  static Future<Map<String, String>> getUserExperimentAssignments(String userId) async {
    try {
      final assignments = <String, String>{};
      
      for (final experimentName in _cachedExperiments.keys) {
        final variant = getExperimentVariant(experimentName, userId: userId);
        assignments[experimentName] = variant;
      }
      
      return assignments;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to get user experiment assignments: $e');
      }
      return {};
    }
  }

  /// Coach check-in frequency experiment
  static String getCheckinFrequencyVariant(String userId) {
    return getExperimentVariant('checkin_frequency', userId: userId);
  }

  /// AI response style experiment
  static String getResponseStyleVariant(String userId) {
    return getExperimentVariant('response_style', userId: userId);
  }

  /// Notification timing experiment
  static String getNotificationTimingVariant(String userId) {
    return getExperimentVariant('notification_timing', userId: userId);
  }

  /// Load feature flags from storage
  static Future<void> _loadFeatureFlags() async {
    try {
      final flags = await _getStoredFlags();
      _cachedFlags = flags;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to load feature flags: $e');
      }
      _cachedFlags = {};
    }
  }

  /// Load experiments from storage
  static Future<void> _loadExperiments() async {
    try {
      final experiments = await _getStoredExperiments();
      _cachedExperiments = experiments;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to load experiments: $e');
      }
      _cachedExperiments = {};
    }
  }

  /// Initialize default feature flags
  static Future<void> _initializeDefaultFlags() async {
    try {
      if (_cachedFlags.isEmpty) {
        final defaultFlags = {
          'enhanced_ai_responses': {
            'enabled': true,
            'rolloutPercentage': 100,
            'description': 'Enable enhanced AI responses with transcript knowledge',
            'targetGroups': [],
          },
          'proactive_checkins': {
            'enabled': true,
            'rolloutPercentage': 100,
            'description': 'Enable proactive coach check-in notifications',
            'targetGroups': [],
          },
          'offline_mode': {
            'enabled': true,
            'rolloutPercentage': 100,
            'description': 'Enable offline mode with fallback responses',
            'targetGroups': [],
          },
          'performance_monitoring': {
            'enabled': true,
            'rolloutPercentage': 100,
            'description': 'Enable performance monitoring and analytics',
            'targetGroups': [],
          },
          'user_feedback_collection': {
            'enabled': true,
            'rolloutPercentage': 50,
            'description': 'Enable user feedback collection prompts',
            'targetGroups': [],
          },
          'hybrid_model_routing': {
            'enabled': true,
            'rolloutPercentage': 100,
            'description': 'Enable hybrid AI model routing for optimal performance',
            'targetGroups': [],
          },
          'system_load_protection': {
            'enabled': true,
            'rolloutPercentage': 100,
            'description': 'Enable system load protection to prevent overload',
            'targetGroups': [],
          },
        };
        
        await _storage.write(key: _flagsKey, value: jsonEncode(defaultFlags));
        _cachedFlags = defaultFlags;
      }
      
      if (_cachedExperiments.isEmpty) {
        final defaultExperiments = {
          'checkin_frequency': {
            'enabled': true,
            'description': 'Test different check-in frequencies',
            'variants': {
              'control': {'frequency': 'normal', 'description': 'Standard 24-48h frequency'},
              'high_frequency': {'frequency': 'high', 'description': 'More frequent check-ins'},
              'low_frequency': {'frequency': 'low', 'description': 'Less frequent check-ins'},
            },
          },
          'response_style': {
            'enabled': true,
            'description': 'Test different AI response styles',
            'variants': {
              'control': {'style': 'standard', 'description': 'Standard response style'},
              'detailed': {'style': 'detailed', 'description': 'More detailed responses'},
              'concise': {'style': 'concise', 'description': 'More concise responses'},
            },
          },
          'notification_timing': {
            'enabled': true,
            'description': 'Test different notification timing strategies',
            'variants': {
              'control': {'timing': 'random', 'description': 'Random timing within window'},
              'morning_focused': {'timing': 'morning', 'description': 'Prefer morning notifications'},
              'evening_focused': {'timing': 'evening', 'description': 'Prefer evening notifications'},
            },
          },
        };
        
        await _storage.write(key: _experimentsKey, value: jsonEncode(defaultExperiments));
        _cachedExperiments = defaultExperiments;
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to initialize default flags: $e');
      }
    }
  }

  /// Synchronously initialize default flags (for emergency recovery)
  static void _initializeDefaultFlagsSync() {
    try {
      if (_cachedFlags.isEmpty) {
        _cachedFlags = {
          'enhanced_ai_responses': {
            'enabled': true,
            'rolloutPercentage': 100,
            'description': 'Enable enhanced AI responses with transcript knowledge',
            'targetGroups': [],
          },
          'proactive_checkins': {
            'enabled': true,
            'rolloutPercentage': 100,
            'description': 'Enable proactive coach check-in notifications',
            'targetGroups': [],
          },
          'offline_mode': {
            'enabled': true,
            'rolloutPercentage': 100,
            'description': 'Enable offline mode with fallback responses',
            'targetGroups': [],
          },
          'performance_monitoring': {
            'enabled': true,
            'rolloutPercentage': 100,
            'description': 'Enable performance monitoring and analytics',
            'targetGroups': [],
          },
          'user_feedback_collection': {
            'enabled': true,
            'rolloutPercentage': 50,
            'description': 'Enable user feedback collection prompts',
            'targetGroups': [],
          },
          'hybrid_model_routing': {
            'enabled': true,
            'rolloutPercentage': 100,
            'description': 'Enable hybrid AI model routing for optimal performance',
            'targetGroups': [],
          },
          'system_load_protection': {
            'enabled': true,
            'rolloutPercentage': 100,
            'description': 'Enable system load protection to prevent overload',
            'targetGroups': [],
          },
        };

        if (kDebugMode) {
          print('🔄 Emergency flag recovery: ${_cachedFlags.keys.length} flags loaded');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to sync initialize default flags: $e');
      }
    }
  }

  /// Get stored feature flags
  static Future<Map<String, dynamic>> _getStoredFlags() async {
    try {
      final data = await _storage.read(key: _flagsKey);
      if (data != null) {
        return Map<String, dynamic>.from(jsonDecode(data));
      }
    } catch (e) {
      if (kDebugMode) print('❌ Failed to read stored flags: $e');
    }
    return {};
  }

  /// Get stored experiments
  static Future<Map<String, dynamic>> _getStoredExperiments() async {
    try {
      final data = await _storage.read(key: _experimentsKey);
      if (data != null) {
        return Map<String, dynamic>.from(jsonDecode(data));
      }
    } catch (e) {
      if (kDebugMode) print('❌ Failed to read stored experiments: $e');
    }
    return {};
  }

  /// Get consistent hash for user and feature
  static int _getUserHash(String userId, String feature) {
    final combined = '$userId:$feature';
    var hash = 0;
    for (int i = 0; i < combined.length; i++) {
      hash = ((hash << 5) - hash + combined.codeUnitAt(i)) & 0xffffffff;
    }
    return hash.abs() % 100; // Return 0-99
  }

  /// Get user group for targeting
  static String _getUserGroup(String userId) {
    // Simple user grouping based on user ID hash
    final hash = _getUserHash(userId, 'group');
    if (hash < 20) return 'early_adopters';
    if (hash < 50) return 'regular_users';
    if (hash < 80) return 'casual_users';
    return 'new_users';
  }

  /// Clear all feature flag data (for testing)
  static Future<void> clearFeatureFlagData() async {
    try {
      await _storage.delete(key: _flagsKey);
      await _storage.delete(key: _experimentsKey);
      await _storage.delete(key: _userGroupKey);
      
      _cachedFlags.clear();
      _cachedExperiments.clear();
      _isInitialized = false;
      
      if (kDebugMode) {
        print('🗑️ Feature flag data cleared');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to clear feature flag data: $e');
      }
    }
  }

  /// Force refresh flags from storage
  static Future<void> refreshFlags() async {
    try {
      await _loadFeatureFlags();
      await _loadExperiments();
      
      if (kDebugMode) {
        print('🔄 Feature flags refreshed');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to refresh flags: $e');
      }
    }
  }
}
