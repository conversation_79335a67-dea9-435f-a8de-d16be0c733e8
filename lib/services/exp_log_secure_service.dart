// 📁 lib/services/exp_log_secure_service.dart

import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../models/user_model.dart';
import '../services/user_service.dart';
//import 'package:maxed_out_life/perks/perk_engine.dart';
import 'dart:convert';
import 'package:intl/intl.dart';


class ExpLogSecureService {
  static final FlutterSecureStorage _storage = const FlutterSecureStorage();

  /// Save EXP entry securely
  static Future<User> saveEntry(User user, String category, int amount, String note) async {
   final timestamp = DateFormat('dd/MM/yyyy HH:mm').format(DateTime.now());


    // Create new diary entry
    final newEntry = {
      'timestamp': timestamp,
      'category': category,
      'note': note,
      'exp': amount,
    };

    // Append EXP and diary
    User updatedUser = user.copyWithAddedExp(category, amount, note);

    // Regenerate perks
   //final perks = await PerkEngine.generateDynamicPerks(updatedUser);
   //updatedUser = updatedUser.copyWith(dynamicPerks: perks);

    // Save updated user
    await UserService.saveUserByUsername(updatedUser);

    // Handle secure diary storage
    final diaryKey = '${user.username}_diary';
    final existingEncoded = await _storage.read(key: diaryKey);
    List<Map<String, dynamic>> diaryList = [];

    if (existingEncoded != null) {
      try {
        diaryList = List<Map<String, dynamic>>.from(
          (await _safeDecode(existingEncoded)),
        );
      } catch (_) {
        diaryList = [];
      }
    }

    diaryList.add(newEntry);
    await _storage.write(key: diaryKey, value: _safeEncode(diaryList));

    return updatedUser;
  }

  /// Load diary securely
  static Future<List<Map<String, dynamic>>> loadDiary(String username) async {
    final diaryKey = '${username}_diary';
    final encoded = await _storage.read(key: diaryKey);
    if (encoded == null) return [];

    final List<dynamic> decoded = await _safeDecode(encoded);
    return decoded.cast<Map<String, dynamic>>();
  }

  /// Clear secure diary
  static Future<void> clearDiary(String username) async {
    final diaryKey = '${username}_diary';
    await _storage.delete(key: diaryKey);
  }

  /// Safe encoding utility
  static String _safeEncode(dynamic value) {
    try {
      return value == null ? '[]' : value.toString();
    } catch (_) {
      return '[]';
    }
  }

  /// Safe decoding utility
  static Future<dynamic> _safeDecode(String value) async {
    try {
      return value.contains('{') ? List<Map<String, dynamic>>.from(await _decodeJson(value)) : [];
    } catch (_) {
      return [];
    }
  }

  static Future<dynamic> _decodeJson(String value) async {
    try {
      return Future.value(List<Map<String, dynamic>>.from(
        value.startsWith('[') ? List<Map<String, dynamic>>.from(await _forceJsonDecode(value)) : [],
      ));
    } catch (_) {
      return [];
    }
  }

  static Future<dynamic> _forceJsonDecode(String input) async {
    try {
      return input.isNotEmpty ? List<Map<String, dynamic>>.from(await Future.value(jsonDecode(input))) : [];
    } catch (_) {
      return [];
    }
  }
}
