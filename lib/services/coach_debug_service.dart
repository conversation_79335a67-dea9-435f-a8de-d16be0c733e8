// 📁 lib/services/coach_debug_service.dart

import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../models/user_model.dart';
import 'coach_orchestration_service.dart';
import 'coach_context_service.dart';
import 'model_routing_service.dart';

/// Comprehensive debugging service for testing and validating the coach system
/// across all scenarios, edge cases, and performance requirements.
class CoachDebugService {
  static final CoachDebugService _instance = CoachDebugService._internal();
  factory CoachDebugService() => _instance;
  CoachDebugService._internal();

  static const FlutterSecureStorage _storage = FlutterSecureStorage();
  static const String _debugResultsKey = 'debug_test_results';
  
  static final List<DebugTestResult> _testResults = [];
  static bool _isRunning = false;

  /// Run comprehensive debug test suite
  static Future<DebugSummary> runComprehensiveTests({
    required User testUser,
    bool includeStressTests = false,
    bool includeEdgeCases = true,
    bool includePerformanceTests = true,
  }) async {
    if (_isRunning) {
      throw Exception('Debug tests already running');
    }

    _isRunning = true;
    _testResults.clear();

    try {
      if (kDebugMode) {
        print('🧪 Starting comprehensive coach system debug tests...');
        print('👤 Test user: ${testUser.username} (${testUser.gender})');
        print('📊 Test categories: ${testUser.categories.keys.join(', ')}');
      }

      final stopwatch = Stopwatch()..start();

      // Core functionality tests
      await _runCoreFunctionalityTests(testUser);
      
      // Context system tests
      await _runContextSystemTests(testUser);
      
      // Model routing tests
      await _runModelRoutingTests(testUser);
      
      // Safety system tests
      await _runSafetySystemTests(testUser);
      
      // Analytics tests
      await _runAnalyticsTests(testUser);

      if (includeEdgeCases) {
        await _runEdgeCaseTests(testUser);
      }

      if (includePerformanceTests) {
        await _runPerformanceTests(testUser);
      }

      if (includeStressTests) {
        await _runStressTests(testUser);
      }

      stopwatch.stop();

      final summary = _generateDebugSummary(stopwatch.elapsedMilliseconds);
      await _saveDebugResults(summary);

      if (kDebugMode) {
        print('✅ Debug tests completed in ${stopwatch.elapsedMilliseconds}ms');
        print('📊 Results: ${summary.passedTests}/${summary.totalTests} passed');
      }

      return summary;

    } catch (e, stackTrace) {
      if (kDebugMode) {
        print('❌ Debug tests failed: $e');
        print('Stack trace: $stackTrace');
      }
      rethrow;
    } finally {
      _isRunning = false;
    }
  }

  /// Test individual coach response
  static Future<DebugTestResult> testCoachResponse({
    required String category,
    required String message,
    required User user,
    int timeoutSeconds = 30,
  }) async {
    final testName = 'Coach Response Test: $category';
    final stopwatch = Stopwatch()..start();

    try {
      final response = await CoachOrchestrationService.generateSuperintelligentResponse(
        category: category,
        userPrompt: message,
        user: user,
      ).timeout(Duration(seconds: timeoutSeconds));

      stopwatch.stop();

      final result = DebugTestResult(
        testName: testName,
        passed: response.isNotEmpty && !response.contains('error'),
        duration: stopwatch.elapsedMilliseconds,
        details: {
          'category': category,
          'message_length': message.length,
          'response_length': response.length,
          'response_preview': response.length > 100 
              ? '${response.substring(0, 100)}...' 
              : response,
        },
        error: response.contains('error') ? response : null,
      );

      _testResults.add(result);
      return result;

    } catch (e) {
      stopwatch.stop();
      
      final result = DebugTestResult(
        testName: testName,
        passed: false,
        duration: stopwatch.elapsedMilliseconds,
        details: {
          'category': category,
          'message_length': message.length,
          'timeout_seconds': timeoutSeconds,
        },
        error: e.toString(),
      );

      _testResults.add(result);
      return result;
    }
  }

  /// Test context system accuracy
  static Future<DebugTestResult> testContextAccuracy(User user) async {
    const testName = 'Context System Accuracy Test';
    final stopwatch = Stopwatch()..start();

    try {
      final context = await CoachContextService.getUserContext(user);
      stopwatch.stop();

      // Validate context structure
      final hasUserProfile = context.containsKey('user_profile');
      final hasProgressData = context.containsKey('progress_data');
      final hasDiaryFeed = context.containsKey('diary_feed');
      final hasNorthStar = context.containsKey('north_star_quest');
      final hasHabits = context.containsKey('habits_data');

      final contextSize = jsonEncode(context).length;
      final isValidSize = contextSize > 100 && contextSize < 50000; // Reasonable size

      final passed = hasUserProfile && hasProgressData && hasDiaryFeed && 
                    hasNorthStar && hasHabits && isValidSize;

      final result = DebugTestResult(
        testName: testName,
        passed: passed,
        duration: stopwatch.elapsedMilliseconds,
        details: {
          'context_size_chars': contextSize,
          'has_user_profile': hasUserProfile,
          'has_progress_data': hasProgressData,
          'has_diary_feed': hasDiaryFeed,
          'has_north_star': hasNorthStar,
          'has_habits': hasHabits,
          'is_valid_size': isValidSize,
        },
        error: !passed ? 'Context validation failed' : null,
      );

      _testResults.add(result);
      return result;

    } catch (e) {
      stopwatch.stop();
      
      final result = DebugTestResult(
        testName: testName,
        passed: false,
        duration: stopwatch.elapsedMilliseconds,
        details: {},
        error: e.toString(),
      );

      _testResults.add(result);
      return result;
    }
  }

  /// Test usage limits
  static Future<DebugTestResult> testUsageLimits(User user) async {
    const testName = 'Usage Limits Test';
    final stopwatch = Stopwatch()..start();

    try {
      // Test normal routing
      final normalResult = await ModelRoutingService.routeMessage(
        userId: user.id,
        category: 'Health',
        message: 'Test message',
        user: user,
      );

      // Get user stats to check current usage
      final stats = await ModelRoutingService.getUserStats(user.id);

      stopwatch.stop();

      final passed = normalResult.success || normalResult.limitExceeded;
      
      final result = DebugTestResult(
        testName: testName,
        passed: passed,
        duration: stopwatch.elapsedMilliseconds,
        details: {
          'routing_success': normalResult.success,
          'limit_exceeded': normalResult.limitExceeded,
          'daily_message_count': stats.dailyMessageCount,
          'total_messages': stats.totalMessages,
          'total_cost': stats.totalCost,
          'selected_model': normalResult.selectedModel,
          'message': normalResult.message,
        },
        error: !passed ? 'Routing failed unexpectedly' : null,
      );

      _testResults.add(result);
      return result;

    } catch (e) {
      stopwatch.stop();
      
      final result = DebugTestResult(
        testName: testName,
        passed: false,
        duration: stopwatch.elapsedMilliseconds,
        details: {},
        error: e.toString(),
      );

      _testResults.add(result);
      return result;
    }
  }

  /// Get debug test history
  static Future<List<DebugSummary>> getDebugHistory() async {
    try {
      final data = await _storage.read(key: _debugResultsKey);
      if (data != null) {
        final List<dynamic> historyJson = jsonDecode(data);
        return historyJson.map((json) => DebugSummary.fromJson(json)).toList();
      }
    } catch (e) {
      if (kDebugMode) print('❌ Failed to load debug history: $e');
    }
    return [];
  }

  /// Clear debug test history
  static Future<void> clearDebugHistory() async {
    try {
      await _storage.delete(key: _debugResultsKey);
      _testResults.clear();
    } catch (e) {
      if (kDebugMode) print('❌ Failed to clear debug history: $e');
    }
  }

  // Private test methods
  static Future<void> _runCoreFunctionalityTests(User user) async {
    if (kDebugMode) print('🧪 Running core functionality tests...');

    // Test each coach category
    final categories = ['Health', 'Wealth', 'Purpose', 'Connection'];
    for (final category in categories) {
      await testCoachResponse(
        category: category,
        message: 'Hello, I need some advice about my $category goals.',
        user: user,
      );
    }

    // Test custom categories if available
    for (final customCategory in user.customCategories) {
      await testCoachResponse(
        category: customCategory,
        message: 'I want to improve my $customCategory skills.',
        user: user,
      );
    }
  }

  static Future<void> _runContextSystemTests(User user) async {
    if (kDebugMode) print('🧪 Running context system tests...');

    await testContextAccuracy(user);

    // Test context caching
    await _testContextCaching(user);

    // Test context with different user states
    await _testContextWithMinimalData(user);
  }

  static Future<void> _runModelRoutingTests(User user) async {
    if (kDebugMode) print('🧪 Running model routing tests...');

    await testUsageLimits(user);

    // Test cost tracking
    await _testCostTracking(user);

    // Test model selection
    await _testModelSelection(user);
  }

  static Future<void> _runSafetySystemTests(User user) async {
    if (kDebugMode) print('🧪 Running safety system tests...');

    // Test safety checks
    await _testSafetyChecks(user);

    // Test circuit breaker
    await _testCircuitBreaker(user);

    // Test rate limiting
    await _testRateLimiting(user);
  }

  static Future<void> _runAnalyticsTests(User user) async {
    if (kDebugMode) print('🧪 Running analytics tests...');

    // Test analytics recording
    await _testAnalyticsRecording(user);

    // Test dashboard data
    await _testDashboardData();
  }

  static Future<void> _runEdgeCaseTests(User user) async {
    if (kDebugMode) print('🧪 Running edge case tests...');

    // Test with empty message
    await testCoachResponse(
      category: 'Health',
      message: '',
      user: user,
    );

    // Test with very long message
    await testCoachResponse(
      category: 'Health',
      message: 'A' * 5000,
      user: user,
    );

    // Test with special characters
    await testCoachResponse(
      category: 'Health',
      message: '🎯💪🔥 Special chars & symbols! @#\$%^&*()',
      user: user,
    );
  }

  static Future<void> _runPerformanceTests(User user) async {
    if (kDebugMode) print('🧪 Running performance tests...');

    // Test response time consistency
    final responseTimes = <int>[];
    for (int i = 0; i < 5; i++) {
      final result = await testCoachResponse(
        category: 'Health',
        message: 'Performance test message $i',
        user: user,
      );
      responseTimes.add(result.duration);
    }

    final avgResponseTime = responseTimes.reduce((a, b) => a + b) / responseTimes.length;
    final maxResponseTime = responseTimes.reduce((a, b) => a > b ? a : b);

    _testResults.add(DebugTestResult(
      testName: 'Performance Consistency Test',
      passed: avgResponseTime < 25000 && maxResponseTime < 35000, // 25s avg, 35s max
      duration: avgResponseTime.round(),
      details: {
        'average_response_time': avgResponseTime,
        'max_response_time': maxResponseTime,
        'min_response_time': responseTimes.reduce((a, b) => a < b ? a : b),
        'response_times': responseTimes,
      },
    ));
  }

  static Future<void> _runStressTests(User user) async {
    if (kDebugMode) print('🧪 Running stress tests...');

    // Concurrent request test
    final futures = <Future>[];
    for (int i = 0; i < 3; i++) {
      futures.add(testCoachResponse(
        category: 'Health',
        message: 'Concurrent test message $i',
        user: user,
      ));
    }

    final results = await Future.wait(futures);
    final allPassed = results.every((result) => (result as DebugTestResult).passed);

    _testResults.add(DebugTestResult(
      testName: 'Concurrent Requests Test',
      passed: allPassed,
      duration: 0,
      details: {
        'concurrent_requests': futures.length,
        'all_passed': allPassed,
      },
    ));
  }

  // Additional test helper methods
  static Future<void> _testContextCaching(User user) async {
    // Implementation for context caching test
  }

  static Future<void> _testContextWithMinimalData(User user) async {
    // Implementation for minimal data context test
  }

  static Future<void> _testCostTracking(User user) async {
    // Implementation for cost tracking test
  }

  static Future<void> _testModelSelection(User user) async {
    // Implementation for model selection test
  }

  static Future<void> _testSafetyChecks(User user) async {
    // Implementation for safety checks test
  }

  static Future<void> _testCircuitBreaker(User user) async {
    // Implementation for circuit breaker test
  }

  static Future<void> _testRateLimiting(User user) async {
    // Implementation for rate limiting test
  }

  static Future<void> _testAnalyticsRecording(User user) async {
    // Implementation for analytics recording test
  }

  static Future<void> _testDashboardData() async {
    // Implementation for dashboard data test
  }

  static DebugSummary _generateDebugSummary(int totalDuration) {
    final passedTests = _testResults.where((r) => r.passed).length;
    final failedTests = _testResults.where((r) => !r.passed).length;
    
    return DebugSummary(
      timestamp: DateTime.now(),
      totalTests: _testResults.length,
      passedTests: passedTests,
      failedTests: failedTests,
      totalDuration: totalDuration,
      testResults: List.from(_testResults),
      successRate: _testResults.isNotEmpty ? passedTests / _testResults.length : 0.0,
    );
  }

  static Future<void> _saveDebugResults(DebugSummary summary) async {
    try {
      final history = await getDebugHistory();
      history.add(summary);
      
      // Keep only last 10 debug sessions
      if (history.length > 10) {
        history.removeRange(0, history.length - 10);
      }
      
      await _storage.write(
        key: _debugResultsKey,
        value: jsonEncode(history.map((s) => s.toJson()).toList()),
      );
    } catch (e) {
      if (kDebugMode) print('❌ Failed to save debug results: $e');
    }
  }
}

// Data classes
class DebugTestResult {
  final String testName;
  final bool passed;
  final int duration;
  final Map<String, dynamic> details;
  final String? error;

  DebugTestResult({
    required this.testName,
    required this.passed,
    required this.duration,
    required this.details,
    this.error,
  });

  Map<String, dynamic> toJson() => {
    'testName': testName,
    'passed': passed,
    'duration': duration,
    'details': details,
    'error': error,
  };

  factory DebugTestResult.fromJson(Map<String, dynamic> json) => DebugTestResult(
    testName: json['testName'],
    passed: json['passed'],
    duration: json['duration'],
    details: Map<String, dynamic>.from(json['details']),
    error: json['error'],
  );
}

class DebugSummary {
  final DateTime timestamp;
  final int totalTests;
  final int passedTests;
  final int failedTests;
  final int totalDuration;
  final List<DebugTestResult> testResults;
  final double successRate;

  DebugSummary({
    required this.timestamp,
    required this.totalTests,
    required this.passedTests,
    required this.failedTests,
    required this.totalDuration,
    required this.testResults,
    required this.successRate,
  });

  Map<String, dynamic> toJson() => {
    'timestamp': timestamp.toIso8601String(),
    'totalTests': totalTests,
    'passedTests': passedTests,
    'failedTests': failedTests,
    'totalDuration': totalDuration,
    'testResults': testResults.map((r) => r.toJson()).toList(),
    'successRate': successRate,
  };

  factory DebugSummary.fromJson(Map<String, dynamic> json) => DebugSummary(
    timestamp: DateTime.parse(json['timestamp']),
    totalTests: json['totalTests'],
    passedTests: json['passedTests'],
    failedTests: json['failedTests'],
    totalDuration: json['totalDuration'],
    testResults: (json['testResults'] as List)
        .map((r) => DebugTestResult.fromJson(r))
        .toList(),
    successRate: json['successRate'],
  );
}
