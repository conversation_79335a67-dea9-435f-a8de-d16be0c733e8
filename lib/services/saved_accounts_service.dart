// lib/services/saved_accounts_service.dart

import 'dart:convert';
import '../services/comprehensive_logging_service.dart';
import '../services/bulletproof_secure_storage.dart';

/// Service for managing and retrieving saved account information from secure storage.
/// 
/// This service provides access to saved usernames on the device for display
/// in the Sign In modal dropdown info box. It follows existing security patterns
/// and only exposes non-sensitive account information.
/// 
/// Example usage:
/// ```dart
/// final savedAccounts = await SavedAccountsService.getSavedUsernames();
/// if (savedAccounts.isNotEmpty) {
///   // Show saved accounts info box
/// }
/// ```
class SavedAccountsService {
  static final BulletproofSecureStorage _secureStorage = BulletproofSecureStorage();
  
  /// Cache for saved usernames to avoid repeated storage reads
  static List<String>? _cachedUsernames;
  static DateTime? _lastCacheUpdate;
  static const Duration _cacheExpiry = Duration(minutes: 5);

  /// Retrieve list of saved usernames from secure storage.
  ///
  /// Returns a list of usernames that have been saved to the device.
  /// This method only returns usernames (non-sensitive data) and uses
  /// caching to optimize performance.
  ///
  /// Returns empty list if no saved accounts found or on error.
  static Future<List<String>> getSavedUsernames() async {
    try {
      // Check cache first
      if (_isCacheValid()) {
        await ComprehensiveLoggingService.logInfo('📱 Returning cached saved usernames');
        return _cachedUsernames ?? [];
      }

      await ComprehensiveLoggingService.logInfo('🔍 Retrieving saved usernames from secure storage');

      final savedUsernames = <String>[];

      // Read from dedicated saved accounts list (separate from auth data)
      await ComprehensiveLoggingService.logInfo('🔍 Reading from key: saved_accounts_list');
      final savedAccountsJson = await _secureStorage.read(key: 'saved_accounts_list');
      await ComprehensiveLoggingService.logInfo('📄 Raw saved accounts JSON: $savedAccountsJson');

      if (savedAccountsJson != null && savedAccountsJson.isNotEmpty) {
        try {
          final List<dynamic> accountsList = jsonDecode(savedAccountsJson);
          await ComprehensiveLoggingService.logInfo('📋 Parsed accounts list: $accountsList');
          for (final account in accountsList) {
            if (account is String && account.isNotEmpty) {
              savedUsernames.add(account);
              await ComprehensiveLoggingService.logInfo('✅ Added username: $account');
            }
          }
        } catch (e) {
          await ComprehensiveLoggingService.logWarning('⚠️ Failed to parse saved accounts list: $e');
        }
      } else {
        await ComprehensiveLoggingService.logInfo('ℹ️ No saved accounts list found in storage');
      }

      // Fallback: Try to read stored username (current user) for backward compatibility
      final currentUsername = await _secureStorage.read(key: 'user_username');
      if (currentUsername != null &&
          currentUsername.isNotEmpty &&
          !savedUsernames.contains(currentUsername)) {
        savedUsernames.add(currentUsername);
      }

      // Remove duplicates and sort
      final uniqueUsernames = savedUsernames.toSet().toList();
      uniqueUsernames.sort();

      // Update cache
      _cachedUsernames = uniqueUsernames;
      _lastCacheUpdate = DateTime.now();

      await ComprehensiveLoggingService.logInfo(
        '✅ Found ${uniqueUsernames.length} saved username(s): ${uniqueUsernames.join(", ")}'
      );

      return uniqueUsernames;
      
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Error retrieving saved usernames: $e');
      // Return empty list on error to avoid breaking sign-in flow
      return [];
    }
  }

  /// Check if a specific username is saved on the device.
  /// 
  /// This is useful for highlighting the current username in the dropdown
  /// or providing additional context about saved accounts.
  static Future<bool> isUsernameSaved(String username) async {
    try {
      final savedUsernames = await getSavedUsernames();
      return savedUsernames.contains(username);
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Error checking if username is saved: $e');
      return false;
    }
  }

  /// Get count of saved accounts for quick checks.
  /// 
  /// Returns the number of saved accounts without retrieving the full list.
  /// Useful for conditional rendering of the info box.
  static Future<int> getSavedAccountsCount() async {
    try {
      final savedUsernames = await getSavedUsernames();
      return savedUsernames.length;
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Error getting saved accounts count: $e');
      return 0;
    }
  }

  /// Clear the cache to force fresh data retrieval.
  ///
  /// This should be called when account data changes (sign in, sign out, etc.)
  /// to ensure the dropdown shows current information.
  static void clearCache() {
    _cachedUsernames = null;
    _lastCacheUpdate = null;
  }

  /// Debug method to manually test the saved accounts functionality
  static Future<void> debugTestSavedAccounts() async {
    try {
      await ComprehensiveLoggingService.logInfo('🧪 DEBUG: Testing saved accounts functionality...');

      // Test saving a username
      await saveUsername('xyz');
      await ComprehensiveLoggingService.logInfo('🧪 DEBUG: Saved test username "xyz"');

      // Test retrieving usernames
      final usernames = await getSavedUsernames();
      await ComprehensiveLoggingService.logInfo('🧪 DEBUG: Retrieved usernames: $usernames');

      await ComprehensiveLoggingService.logInfo('🧪 DEBUG: Test completed successfully!');
    } catch (e) {
      await ComprehensiveLoggingService.logError('🧪 DEBUG: Test failed: $e');
    }
  }

  /// Save a username to the saved accounts list.
  ///
  /// This method adds a username to the persistent saved accounts list
  /// that survives logout. The list is stored separately from authentication
  /// data to provide convenience for users without compromising security.
  ///
  /// Parameters:
  /// - [username]: The username to save for future convenience
  static Future<void> saveUsername(String username) async {
    try {
      if (username.isEmpty) {
        await ComprehensiveLoggingService.logWarning('⚠️ Attempted to save empty username');
        return;
      }

      await ComprehensiveLoggingService.logInfo('💾 SAVING USERNAME: $username');

      // Get existing saved accounts
      final existingUsernames = await getSavedUsernames();
      await ComprehensiveLoggingService.logInfo('📋 Current saved usernames: $existingUsernames');

      // Add new username if not already present
      if (!existingUsernames.contains(username)) {
        existingUsernames.add(username);

        // Sort for consistent ordering
        existingUsernames.sort();

        // Save updated list
        final accountsJson = jsonEncode(existingUsernames);
        await ComprehensiveLoggingService.logInfo('💾 Writing JSON to storage: $accountsJson');
        await _secureStorage.write(key: 'saved_accounts_list', value: accountsJson);

        // Verify the write worked
        final verifyRead = await _secureStorage.read(key: 'saved_accounts_list');
        await ComprehensiveLoggingService.logInfo('🔍 Verification read: $verifyRead');

        // Clear cache to force refresh
        clearCache();

        await ComprehensiveLoggingService.logInfo('✅ Username saved successfully: $username');
      } else {
        await ComprehensiveLoggingService.logInfo('ℹ️ Username already in saved accounts: $username');
      }

    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Error saving username: $e');
    }
  }

  /// Check if the current cache is still valid.
  static bool _isCacheValid() {
    if (_cachedUsernames == null || _lastCacheUpdate == null) {
      return false;
    }
    
    final now = DateTime.now();
    return now.difference(_lastCacheUpdate!) < _cacheExpiry;
  }

  /// Get additional account information for display purposes.
  /// 
  /// Returns a map with safe-to-display information about saved accounts.
  /// This could include last login time, account status, etc.
  static Future<Map<String, Map<String, dynamic>>> getAccountDisplayInfo() async {
    try {
      final usernames = await getSavedUsernames();
      final accountInfo = <String, Map<String, dynamic>>{};

      for (final username in usernames) {
        // Only include non-sensitive display information
        accountInfo[username] = {
          'username': username,
          'isSaved': true,
          'displayName': username, // Could be enhanced with actual display names
        };
      }

      return accountInfo;
      
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Error getting account display info: $e');
      return {};
    }
  }
}
