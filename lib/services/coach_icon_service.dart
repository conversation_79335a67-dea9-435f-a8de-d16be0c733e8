// lib/services/coach_icon_service.dart

import '../prompts/mxd_life_coaches.dart';

/// Service for managing coach icons and names based on user gender and category
class CoachIconService {
  /// List of coach names that have 128x128 high-resolution icons available
  static const Set<String> _coaches128Available = {
    'aether',
    'chronos',
    'elysia',
    'kai-tholo',
    'luna',
    'marion',
    'seraphina',
    'sterling',
    'zen',
  };

  /// Gets the coach name for a given category and user gender
  static String getCoachName(String category, String userGender, {Map<String, String>? assignedCoaches}) {
    // For non-gender users with assigned coaches, use the assigned gender for this category
    String effectiveGender = userGender;
    if (userGender.toLowerCase() == 'non-gender' && assignedCoaches != null) {
      final assignedGender = assignedCoaches[category];
      if (assignedGender != null) {
        effectiveGender = assignedGender;
      }
    }

    // Handle custom categories with hardcoded coach assignments
    if (category == 'Custom Category 1') {
      return effectiveGender.toLowerCase() == 'female' ? 'Luna' : 'Aether';
    } else if (category == 'Custom Category 2') {
      return effectiveGender.toLowerCase() == 'female' ? 'Elysia' : 'Chronos';
    }

    // Find the coach for this category
    final coach = mxdLifeCoaches.firstWhere(
      (coach) => coach.category == category,
      orElse: () => throw ArgumentError('Unknown category: $category'),
    );

    // Return appropriate name based on gender
    if (effectiveGender.toLowerCase() == 'male') {
      return coach.maleName;
    } else if (effectiveGender.toLowerCase() == 'female') {
      return coach.femaleName;
    } else {
      // Default to female name for unknown/other genders
      return coach.femaleName;
    }
  }
  
  /// Gets the coach icon path for a given category and user gender
  static String getCoachIconPath(String category, String userGender, {Map<String, String>? assignedCoaches}) {
    final coachName = getCoachName(category, userGender, assignedCoaches: assignedCoaches);
    final coachNameLower = coachName.toLowerCase();

    // Use 128x128 version if available, otherwise use standard version
    if (_coaches128Available.contains(coachNameLower)) {
      return 'assets/images/${coachNameLower}_icon_128.png';
    } else {
      return 'assets/images/${coachNameLower}_icon.png';
    }
  }

  /// Gets both coach name and icon path for a given category and user gender
  static CoachInfo getCoachInfo(String category, String userGender, {Map<String, String>? assignedCoaches}) {
    final name = getCoachName(category, userGender, assignedCoaches: assignedCoaches);
    final iconPath = getCoachIconPath(category, userGender, assignedCoaches: assignedCoaches);
    return CoachInfo(name: name, iconPath: iconPath);
  }
  
  /// Gets coach info for custom categories using the actual category name
  /// @deprecated Use getCoachInfo() instead - this method had incorrect logic
  @Deprecated('Use getCoachInfo() instead for consistent coach name resolution')
  static CoachInfo getCustomCoachInfo(String customCategoryName, String userGender, {int? categoryIndex, Map<String, String>? assignedCoaches}) {
    // This method had flawed logic - use getCoachInfo() instead
    // Fallback to standard method
    return getCoachInfo(customCategoryName, userGender, assignedCoaches: assignedCoaches);
  }
  
  /// Gets all available coach names for a given gender
  static List<String> getAllCoachNames(String userGender) {
    return mxdLifeCoaches.map((coach) {
      if (userGender.toLowerCase() == 'male') {
        return coach.maleName;
      } else {
        return coach.femaleName;
      }
    }).toList();
  }
  
  /// Gets all built-in categories
  static List<String> getBuiltInCategories() {
    return ['Health', 'Wealth', 'Purpose', 'Connection'];
  }
  
  /// Checks if a category is a built-in category
  static bool isBuiltInCategory(String category) {
    return getBuiltInCategories().contains(category);
  }
}

/// Data class to hold coach information
class CoachInfo {
  final String name;
  final String iconPath;
  
  const CoachInfo({
    required this.name,
    required this.iconPath,
  });
  
  @override
  String toString() => 'CoachInfo(name: $name, iconPath: $iconPath)';
  
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CoachInfo &&
          runtimeType == other.runtimeType &&
          name == other.name &&
          iconPath == other.iconPath;
  
  @override
  int get hashCode => name.hashCode ^ iconPath.hashCode;
}
