// 📁 lib/services/quality_monitoring_service.dart

import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'response_validator.dart';
import 'content_synthesis_service.dart';


/// Enterprise-grade quality monitoring service that continuously tracks
/// AI coach response quality, immersion maintenance, and user satisfaction.
/// 
/// This service:
/// - Monitors response quality in real-time
/// - Tracks immersion scores and expertise presentation
/// - Alerts on quality degradation
/// - Provides analytics dashboard data
/// - Optimizes system performance based on metrics
/// - Ensures consistent high-quality user experience
class QualityMonitoringService {
  static final QualityMonitoringService _instance = QualityMonitoringService._internal();
  factory QualityMonitoringService() => _instance;
  QualityMonitoringService._internal();

  static const FlutterSecureStorage _storage = FlutterSecureStorage();
  
  // Quality thresholds for alerts
  static const double qualityAlertThreshold = 0.7;
  static const double immersionAlertThreshold = 0.8;
  static const double expertiseAlertThreshold = 0.7;
  static const int alertWindowMinutes = 30;

  // Monitoring data
  static final List<QualityMetric> _qualityHistory = [];
  static final List<QualityAlert> _activeAlerts = [];
  static final Map<String, CategoryMetrics> _categoryMetrics = {};
  static DateTime? _lastAlertCheck;

  /// Initialize quality monitoring
  static Future<void> initialize() async {
    try {
      // Initialize last alert check time
      _lastAlertCheck = DateTime.now();

      await _loadHistoricalData();
      _startPeriodicMonitoring();

      if (kDebugMode) {
        print('📊 Quality Monitoring Service initialized at ${_lastAlertCheck!.toString().substring(11, 19)}');
        print('   Historical metrics: ${_qualityHistory.length}');
        print('   Active alerts: ${_activeAlerts.length}');
      }
    } catch (e) {
      if (kDebugMode) print('❌ Failed to initialize quality monitoring: $e');
    }
  }

  /// Track response quality metrics
  static Future<void> trackResponseQuality({
    required String response,
    required SynthesizedContent synthesisContent,
    required ValidationResult validation,
    required String category,
    required String userMessage,
    required int processingTimeMs,
    required int retryCount,
    String? responseType,
  }) async {
    try {
      // Calculate comprehensive metrics
      final metrics = QualityMetric(
        timestamp: DateTime.now(),
        category: category,
        qualityScore: validation.qualityScore,
        immersionScore: validation.immersionScore,
        expertiseScore: validation.expertiseScore,
        actionabilityScore: validation.actionabilityScore,
        specificityScore: validation.specificityScore,
        transcriptSourcesUsed: synthesisContent.sourceCount,
        relevanceScore: synthesisContent.relevanceScore,
        responseLength: response.length,
        processingTimeMs: processingTimeMs,
        retryCount: retryCount,
        responseType: responseType ?? 'standard',
        userMessage: userMessage,
        passedChecks: validation.passedChecks,
        totalChecks: validation.totalChecks,
      );

      // Store metrics
      _qualityHistory.add(metrics);
      await _updateCategoryMetrics(category, metrics);
      
      // Check for quality alerts
      await _checkQualityAlerts(metrics);
      
      // Persist data
      await _persistMetrics(metrics);

      if (kDebugMode) {
        print('📊 Quality tracked: ${metrics.getSummary()}');
      }

    } catch (e) {
      if (kDebugMode) print('❌ Failed to track quality: $e');
    }
  }

  /// Get quality dashboard data
  static Future<QualityDashboard> getDashboardData() async {
    try {
      final now = DateTime.now();
      final last24h = now.subtract(Duration(hours: 24));
      final last7d = now.subtract(Duration(days: 7));

      // Filter metrics by time periods
      final metrics24h = _qualityHistory.where((m) => m.timestamp.isAfter(last24h)).toList();
      final metrics7d = _qualityHistory.where((m) => m.timestamp.isAfter(last7d)).toList();

      // Calculate aggregate statistics
      final dashboard = QualityDashboard(
        totalResponses: _qualityHistory.length,
        responses24h: metrics24h.length,
        responses7d: metrics7d.length,
        
        // Overall quality metrics
        averageQuality: _calculateAverage(metrics24h.map((m) => m.qualityScore)),
        averageImmersion: _calculateAverage(metrics24h.map((m) => m.immersionScore)),
        averageExpertise: _calculateAverage(metrics24h.map((m) => m.expertiseScore)),
        
        // Performance metrics
        averageProcessingTime: _calculateAverage(metrics24h.map((m) => m.processingTimeMs.toDouble())),
        averageRetryCount: _calculateAverage(metrics24h.map((m) => m.retryCount.toDouble())),
        
        // Quality trends
        qualityTrend: _calculateTrend(metrics7d.map((m) => m.qualityScore)),
        immersionTrend: _calculateTrend(metrics7d.map((m) => m.immersionScore)),
        
        // Category breakdown
        categoryMetrics: Map.from(_categoryMetrics),
        
        // Active alerts
        activeAlerts: List.from(_activeAlerts),
        
        // System health
        systemHealth: _calculateSystemHealth(metrics24h),
        
        // Recent metrics for charts
        recentMetrics: metrics24h.take(50).toList(),
      );

      return dashboard;

    } catch (e) {
      if (kDebugMode) print('❌ Failed to get dashboard data: $e');
      return QualityDashboard.empty();
    }
  }

  /// Check for quality alerts
  static Future<void> _checkQualityAlerts(QualityMetric metric) async {
    final now = DateTime.now();

    // Update last alert check time
    _lastAlertCheck = now;

    final alerts = <QualityAlert>[];

    // Quality score alert
    if (metric.qualityScore < qualityAlertThreshold) {
      alerts.add(QualityAlert(
        type: AlertType.qualityDegradation,
        severity: AlertSeverity.high,
        message: 'Quality score dropped to ${(metric.qualityScore * 100).toStringAsFixed(1)}%',
        category: metric.category,
        timestamp: DateTime.now(),
        metric: metric,
      ));
    }

    // Immersion score alert
    if (metric.immersionScore < immersionAlertThreshold) {
      alerts.add(QualityAlert(
        type: AlertType.immersionBreach,
        severity: AlertSeverity.critical,
        message: 'Immersion broken: ${(metric.immersionScore * 100).toStringAsFixed(1)}%',
        category: metric.category,
        timestamp: DateTime.now(),
        metric: metric,
      ));
    }

    // Expertise presentation alert
    if (metric.expertiseScore < expertiseAlertThreshold) {
      alerts.add(QualityAlert(
        type: AlertType.expertiseWeak,
        severity: AlertSeverity.medium,
        message: 'Weak expertise presentation: ${(metric.expertiseScore * 100).toStringAsFixed(1)}%',
        category: metric.category,
        timestamp: DateTime.now(),
        metric: metric,
      ));
    }

    // High retry count alert
    if (metric.retryCount > 2) {
      alerts.add(QualityAlert(
        type: AlertType.highRetryCount,
        severity: AlertSeverity.medium,
        message: 'High retry count: ${metric.retryCount} attempts',
        category: metric.category,
        timestamp: DateTime.now(),
        metric: metric,
      ));
    }

    // Add new alerts
    _activeAlerts.addAll(alerts);

    // Log alerts
    for (final alert in alerts) {
      if (kDebugMode) {
        print('🚨 Quality Alert: ${alert.type.name} - ${alert.message}');
      }
    }

    // Clean up old alerts
    _cleanupOldAlerts();
  }

  /// Update category-specific metrics
  static Future<void> _updateCategoryMetrics(String category, QualityMetric metric) async {
    final categoryKey = category.toLowerCase();
    
    if (!_categoryMetrics.containsKey(categoryKey)) {
      _categoryMetrics[categoryKey] = CategoryMetrics(category: category);
    }

    final catMetrics = _categoryMetrics[categoryKey]!;
    catMetrics.addMetric(metric);
  }

  /// Calculate system health score
  static SystemHealth _calculateSystemHealth(List<QualityMetric> recentMetrics) {
    if (recentMetrics.isEmpty) {
      return SystemHealth(
        score: 0.5,
        status: HealthStatus.unknown,
        issues: ['No recent data available'],
      );
    }

    final issues = <String>[];
    double healthScore = 1.0;

    // Check quality scores with next-generation standards
    final avgQuality = _calculateAverage(recentMetrics.map((m) => m.qualityScore));
    if (avgQuality < 0.9) {
      issues.add('Average quality below next-generation standard (90%)');
      healthScore -= 0.15;
    } else if (avgQuality < 0.85) {
      issues.add('Average quality below superhuman standard (85%)');
      healthScore -= 0.1;
    } else if (avgQuality < 0.8) {
      issues.add('Average quality below excellence standard (80%)');
      healthScore -= 0.05;
    }

    // Check immersion scores
    final avgImmersion = _calculateAverage(recentMetrics.map((m) => m.immersionScore));
    if (avgImmersion < 0.9) {
      issues.add('Immersion scores below 90%');
      healthScore -= 0.2;
    }

    // Check retry rates
    final avgRetries = _calculateAverage(recentMetrics.map((m) => m.retryCount.toDouble()));
    if (avgRetries > 1.5) {
      issues.add('High retry rate detected');
      healthScore -= 0.1;
    }

    // Check processing times
    final avgProcessingTime = _calculateAverage(recentMetrics.map((m) => m.processingTimeMs.toDouble()));
    if (avgProcessingTime > 20000) {
      issues.add('Processing times approaching limit');
      healthScore -= 0.1;
    }

    // Determine status
    HealthStatus status;
    if (healthScore >= 0.9) {
      status = HealthStatus.excellent;
    } else if (healthScore >= 0.8) {
      status = HealthStatus.good;
    } else if (healthScore >= 0.6) {
      status = HealthStatus.fair;
    } else {
      status = HealthStatus.poor;
    }

    return SystemHealth(
      score: healthScore,
      status: status,
      issues: issues,
    );
  }

  /// Start periodic monitoring (optimized frequency)
  static void _startPeriodicMonitoring() {
    Timer.periodic(Duration(hours: 1), (timer) async { // Changed from 5 minutes to 1 hour
      await _performPeriodicChecks();
    });
  }

  /// Perform periodic system checks
  static Future<void> _performPeriodicChecks() async {
    try {
      // Clean up old data
      _cleanupOldMetrics();
      _cleanupOldAlerts();
      
      // Check system trends
      await _checkSystemTrends();
      
      // Persist current state
      await _persistCurrentState();
      
    } catch (e) {
      if (kDebugMode) print('⚠️ Periodic monitoring check failed: $e');
    }
  }

  /// Check for concerning system trends
  static Future<void> _checkSystemTrends() async {
    final now = DateTime.now();
    final last2h = now.subtract(Duration(hours: 2));
    final recentMetrics = _qualityHistory.where((m) => m.timestamp.isAfter(last2h)).toList();

    if (recentMetrics.length < 5) return; // Not enough data

    // Check for declining quality trend
    final qualityTrend = _calculateTrend(recentMetrics.map((m) => m.qualityScore));
    if (qualityTrend < -0.1) {
      _activeAlerts.add(QualityAlert(
        type: AlertType.qualityTrend,
        severity: AlertSeverity.medium,
        message: 'Declining quality trend detected over last 2 hours',
        category: 'system',
        timestamp: now,
        metric: null,
      ));
    }
  }

  /// Helper methods
  static double _calculateAverage(Iterable<double> values) {
    if (values.isEmpty) return 0.0;
    return values.reduce((a, b) => a + b) / values.length;
  }

  static double _calculateTrend(Iterable<double> values) {
    final list = values.toList();
    if (list.length < 2) return 0.0;
    
    final firstHalf = list.take(list.length ~/ 2);
    final secondHalf = list.skip(list.length ~/ 2);
    
    final firstAvg = _calculateAverage(firstHalf);
    final secondAvg = _calculateAverage(secondHalf);
    
    return secondAvg - firstAvg;
  }

  static void _cleanupOldMetrics() {
    final cutoff = DateTime.now().subtract(Duration(days: 7));
    _qualityHistory.removeWhere((metric) => metric.timestamp.isBefore(cutoff));
  }

  static void _cleanupOldAlerts() {
    final cutoff = DateTime.now().subtract(Duration(hours: 24));
    _activeAlerts.removeWhere((alert) => alert.timestamp.isBefore(cutoff));
  }

  static Future<void> _loadHistoricalData() async {
    try {
      final data = await _storage.read(key: 'quality_metrics');
      if (data != null) {
        final List<dynamic> jsonList = jsonDecode(data);
        _qualityHistory.addAll(
          jsonList.map((json) => QualityMetric.fromJson(json)).toList()
        );
      }
    } catch (e) {
      if (kDebugMode) print('⚠️ Failed to load historical data: $e');
    }
  }

  static Future<void> _persistMetrics(QualityMetric metric) async {
    try {
      // Keep only recent metrics for storage
      final recentMetrics = _qualityHistory.where(
        (m) => m.timestamp.isAfter(DateTime.now().subtract(Duration(days: 7)))
      ).toList();
      
      final jsonData = jsonEncode(recentMetrics.map((m) => m.toJson()).toList());
      await _storage.write(key: 'quality_metrics', value: jsonData);
    } catch (e) {
      if (kDebugMode) print('⚠️ Failed to persist metrics: $e');
    }
  }

  static Future<void> _persistCurrentState() async {
    try {
      final state = {
        'categoryMetrics': _categoryMetrics.map((k, v) => MapEntry(k, v.toJson())),
        'activeAlerts': _activeAlerts.map((a) => a.toJson()).toList(),
        'lastUpdate': DateTime.now().toIso8601String(),
      };
      
      await _storage.write(key: 'quality_state', value: jsonEncode(state));
    } catch (e) {
      if (kDebugMode) print('⚠️ Failed to persist state: $e');
    }
  }

  /// Get quality statistics
  static Map<String, dynamic> getQualityStats() {
    final now = DateTime.now();
    final last24h = _qualityHistory.where(
      (m) => m.timestamp.isAfter(now.subtract(Duration(hours: 24)))
    ).toList();

    return {
      'totalResponses': _qualityHistory.length,
      'responses24h': last24h.length,
      'averageQuality': _calculateAverage(last24h.map((m) => m.qualityScore)),
      'averageImmersion': _calculateAverage(last24h.map((m) => m.immersionScore)),
      'activeAlerts': _activeAlerts.length,
      'categoryCount': _categoryMetrics.length,
    };
  }
}

/// Quality metric data class
class QualityMetric {
  final DateTime timestamp;
  final String category;
  final double qualityScore;
  final double immersionScore;
  final double expertiseScore;
  final double actionabilityScore;
  final double specificityScore;
  final int transcriptSourcesUsed;
  final double relevanceScore;
  final int responseLength;
  final int processingTimeMs;
  final int retryCount;
  final String responseType;
  final String userMessage;
  final int passedChecks;
  final int totalChecks;

  QualityMetric({
    required this.timestamp,
    required this.category,
    required this.qualityScore,
    required this.immersionScore,
    required this.expertiseScore,
    required this.actionabilityScore,
    required this.specificityScore,
    required this.transcriptSourcesUsed,
    required this.relevanceScore,
    required this.responseLength,
    required this.processingTimeMs,
    required this.retryCount,
    required this.responseType,
    required this.userMessage,
    required this.passedChecks,
    required this.totalChecks,
  });

  String getSummary() {
    return 'Q:${(qualityScore * 100).toStringAsFixed(0)}% '
           'I:${(immersionScore * 100).toStringAsFixed(0)}% '
           'E:${(expertiseScore * 100).toStringAsFixed(0)}% '
           'T:${processingTimeMs}ms R:$retryCount';
  }

  Map<String, dynamic> toJson() {
    return {
      'timestamp': timestamp.toIso8601String(),
      'category': category,
      'qualityScore': qualityScore,
      'immersionScore': immersionScore,
      'expertiseScore': expertiseScore,
      'actionabilityScore': actionabilityScore,
      'specificityScore': specificityScore,
      'transcriptSourcesUsed': transcriptSourcesUsed,
      'relevanceScore': relevanceScore,
      'responseLength': responseLength,
      'processingTimeMs': processingTimeMs,
      'retryCount': retryCount,
      'responseType': responseType,
      'userMessage': userMessage,
      'passedChecks': passedChecks,
      'totalChecks': totalChecks,
    };
  }

  factory QualityMetric.fromJson(Map<String, dynamic> json) {
    return QualityMetric(
      timestamp: DateTime.parse(json['timestamp']),
      category: json['category'],
      qualityScore: json['qualityScore'],
      immersionScore: json['immersionScore'],
      expertiseScore: json['expertiseScore'],
      actionabilityScore: json['actionabilityScore'],
      specificityScore: json['specificityScore'],
      transcriptSourcesUsed: json['transcriptSourcesUsed'],
      relevanceScore: json['relevanceScore'],
      responseLength: json['responseLength'],
      processingTimeMs: json['processingTimeMs'],
      retryCount: json['retryCount'],
      responseType: json['responseType'],
      userMessage: json['userMessage'],
      passedChecks: json['passedChecks'],
      totalChecks: json['totalChecks'],
    );
  }
}

/// Category-specific metrics
class CategoryMetrics {
  final String category;
  final List<QualityMetric> metrics = [];
  
  CategoryMetrics({required this.category});

  void addMetric(QualityMetric metric) {
    metrics.add(metric);
    
    // Keep only recent metrics
    final cutoff = DateTime.now().subtract(Duration(days: 7));
    metrics.removeWhere((m) => m.timestamp.isBefore(cutoff));
  }

  double get averageQuality => metrics.isEmpty ? 0.0 : 
    metrics.map((m) => m.qualityScore).reduce((a, b) => a + b) / metrics.length;

  double get averageImmersion => metrics.isEmpty ? 0.0 : 
    metrics.map((m) => m.immersionScore).reduce((a, b) => a + b) / metrics.length;

  int get totalResponses => metrics.length;

  Map<String, dynamic> toJson() {
    return {
      'category': category,
      'totalResponses': totalResponses,
      'averageQuality': averageQuality,
      'averageImmersion': averageImmersion,
    };
  }
}

/// Quality alert data class
class QualityAlert {
  final AlertType type;
  final AlertSeverity severity;
  final String message;
  final String category;
  final DateTime timestamp;
  final QualityMetric? metric;

  QualityAlert({
    required this.type,
    required this.severity,
    required this.message,
    required this.category,
    required this.timestamp,
    this.metric,
  });

  Map<String, dynamic> toJson() {
    return {
      'type': type.name,
      'severity': severity.name,
      'message': message,
      'category': category,
      'timestamp': timestamp.toIso8601String(),
    };
  }
}

/// Quality dashboard data
class QualityDashboard {
  final int totalResponses;
  final int responses24h;
  final int responses7d;
  final double averageQuality;
  final double averageImmersion;
  final double averageExpertise;
  final double averageProcessingTime;
  final double averageRetryCount;
  final double qualityTrend;
  final double immersionTrend;
  final Map<String, CategoryMetrics> categoryMetrics;
  final List<QualityAlert> activeAlerts;
  final SystemHealth systemHealth;
  final List<QualityMetric> recentMetrics;

  QualityDashboard({
    required this.totalResponses,
    required this.responses24h,
    required this.responses7d,
    required this.averageQuality,
    required this.averageImmersion,
    required this.averageExpertise,
    required this.averageProcessingTime,
    required this.averageRetryCount,
    required this.qualityTrend,
    required this.immersionTrend,
    required this.categoryMetrics,
    required this.activeAlerts,
    required this.systemHealth,
    required this.recentMetrics,
  });

  factory QualityDashboard.empty() {
    return QualityDashboard(
      totalResponses: 0,
      responses24h: 0,
      responses7d: 0,
      averageQuality: 0.0,
      averageImmersion: 0.0,
      averageExpertise: 0.0,
      averageProcessingTime: 0.0,
      averageRetryCount: 0.0,
      qualityTrend: 0.0,
      immersionTrend: 0.0,
      categoryMetrics: {},
      activeAlerts: [],
      systemHealth: SystemHealth(score: 0.0, status: HealthStatus.unknown, issues: []),
      recentMetrics: [],
    );
  }
}

/// System health data
class SystemHealth {
  final double score;
  final HealthStatus status;
  final List<String> issues;

  SystemHealth({
    required this.score,
    required this.status,
    required this.issues,
  });
}

/// Enums
enum AlertType { qualityDegradation, immersionBreach, expertiseWeak, highRetryCount, qualityTrend }
enum AlertSeverity { low, medium, high, critical }
enum HealthStatus { excellent, good, fair, poor, unknown }
