// lib/services/training_storage_service.dart

import 'dart:convert';
import '../models/training_session_model.dart';
import '../models/training_timer_model.dart';
import '../services/bulletproof_storage_service.dart';
import '../services/comprehensive_logging_service.dart';
import '../bulletproof/error_handler.dart';

/// Service for managing training session storage with bulletproof persistence
class TrainingStorageService {
  static final TrainingStorageService _instance = TrainingStorageService._internal();
  factory TrainingStorageService() => _instance;
  
  TrainingStorageService._internal() : _errorHandler = ErrorHandler();

  final ErrorHandler _errorHandler;
  final BulletproofStorageService _storage = BulletproofStorageService();
  
  // Storage keys
  static const String _sessionsKey = 'training_sessions';
  static const String _programKey = 'training_program';
  static const String _currentSessionKey = 'current_training_session';
  
  /// Save a completed training session
  Future<bool> saveSession(TrainingSession session) async {
    try {
      await ComprehensiveLoggingService.logInfo('💾 TrainingStorage: Saving session ${session.id}');
      
      // Get existing sessions
      final sessions = await getAllSessions();
      
      // Add new session at the beginning (most recent first)
      sessions.insert(0, session);
      
      // Keep only last 100 sessions to prevent storage bloat
      if (sessions.length > 100) {
        sessions.removeRange(100, sessions.length);
        await ComprehensiveLoggingService.logInfo('🧹 TrainingStorage: Trimmed sessions to 100 most recent');
      }
      
      // Save updated sessions list
      final sessionsJson = sessions.map((s) => s.toJson()).toList();
      final success = await _storage.write(_sessionsKey, jsonEncode(sessionsJson));
      
      if (success) {
        await ComprehensiveLoggingService.logInfo('✅ TrainingStorage: Session saved successfully');
        await ComprehensiveLoggingService.logInfo('📊 TrainingStorage: Session stats - Duration: ${session.formattedDuration}, EXP: ${session.expEarned}');
      } else {
        await ComprehensiveLoggingService.logError('❌ TrainingStorage: Failed to save session');
      }
      
      return success;
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace);
      await ComprehensiveLoggingService.logError('❌ TrainingStorage: Error saving session: $e');
      return false;
    }
  }

  /// Get all training sessions (most recent first)
  Future<List<TrainingSession>> getAllSessions() async {
    try {
      final data = await _storage.read(_sessionsKey);
      if (data == null) {
        await ComprehensiveLoggingService.logInfo('📂 TrainingStorage: No existing sessions found');
        return [];
      }
      
      final List<dynamic> sessionsJson = jsonDecode(data);
      final sessions = sessionsJson
          .map((json) => TrainingSession.fromJson(json as Map<String, dynamic>))
          .toList();
      
      await ComprehensiveLoggingService.logInfo('📂 TrainingStorage: Loaded ${sessions.length} sessions');
      return sessions;
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace);
      await ComprehensiveLoggingService.logError('❌ TrainingStorage: Error loading sessions: $e');
      return [];
    }
  }

  /// Get sessions for a specific label
  Future<List<TrainingSession>> getSessionsByLabel(String label) async {
    try {
      final allSessions = await getAllSessions();
      final labelSessions = allSessions
          .where((session) => session.label.toLowerCase() == label.toLowerCase())
          .toList();
      
      await ComprehensiveLoggingService.logInfo('🔍 TrainingStorage: Found ${labelSessions.length} sessions for label "$label"');
      return labelSessions;
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace);
      await ComprehensiveLoggingService.logError('❌ TrainingStorage: Error getting sessions by label: $e');
      return [];
    }
  }

  /// Get recent sessions (last 7 by default)
  Future<List<TrainingSession>> getRecentSessions({int limit = 7}) async {
    try {
      final allSessions = await getAllSessions();
      final recentSessions = allSessions.take(limit).toList();
      
      await ComprehensiveLoggingService.logInfo('📅 TrainingStorage: Retrieved ${recentSessions.length} recent sessions');
      return recentSessions;
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace);
      await ComprehensiveLoggingService.logError('❌ TrainingStorage: Error getting recent sessions: $e');
      return [];
    }
  }

  /// Update an existing session
  Future<bool> updateSession(TrainingSession updatedSession) async {
    try {
      await ComprehensiveLoggingService.logInfo('✏️ TrainingStorage: Updating session ${updatedSession.id}');
      
      final sessions = await getAllSessions();
      final index = sessions.indexWhere((s) => s.id == updatedSession.id);
      
      if (index == -1) {
        await ComprehensiveLoggingService.logError('❌ TrainingStorage: Session not found for update');
        return false;
      }
      
      sessions[index] = updatedSession;
      
      final sessionsJson = sessions.map((s) => s.toJson()).toList();
      final success = await _storage.write(_sessionsKey, jsonEncode(sessionsJson));
      
      if (success) {
        await ComprehensiveLoggingService.logInfo('✅ TrainingStorage: Session updated successfully');
      }
      
      return success;
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace);
      await ComprehensiveLoggingService.logError('❌ TrainingStorage: Error updating session: $e');
      return false;
    }
  }

  /// Delete a session
  Future<bool> deleteSession(String sessionId) async {
    try {
      await ComprehensiveLoggingService.logInfo('🗑️ TrainingStorage: Deleting session $sessionId');
      
      final sessions = await getAllSessions();
      final initialCount = sessions.length;
      sessions.removeWhere((s) => s.id == sessionId);
      
      if (sessions.length == initialCount) {
        await ComprehensiveLoggingService.logError('❌ TrainingStorage: Session not found for deletion');
        return false;
      }
      
      final sessionsJson = sessions.map((s) => s.toJson()).toList();
      final success = await _storage.write(_sessionsKey, jsonEncode(sessionsJson));
      
      if (success) {
        await ComprehensiveLoggingService.logInfo('✅ TrainingStorage: Session deleted successfully');
      }
      
      return success;
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace);
      await ComprehensiveLoggingService.logError('❌ TrainingStorage: Error deleting session: $e');
      return false;
    }
  }

  /// Save training program configuration
  Future<bool> saveProgram(TrainingProgram program) async {
    try {
      await ComprehensiveLoggingService.logInfo('💾 TrainingStorage: Saving training program');
      
      final success = await _storage.write(_programKey, jsonEncode(program.toJson()));
      
      if (success) {
        await ComprehensiveLoggingService.logInfo('✅ TrainingStorage: Program saved - Type: ${program.programType}, Current: ${program.currentLabel}');
      }
      
      return success;
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace);
      await ComprehensiveLoggingService.logError('❌ TrainingStorage: Error saving program: $e');
      return false;
    }
  }

  /// Load training program configuration
  Future<TrainingProgram> loadProgram() async {
    try {
      final data = await _storage.read(_programKey);
      if (data == null) {
        await ComprehensiveLoggingService.logInfo('📂 TrainingStorage: No program found, using default');
        return TrainingProgram.letters();
      }
      
      final programJson = jsonDecode(data) as Map<String, dynamic>;
      final program = TrainingProgram.fromJson(programJson);
      
      await ComprehensiveLoggingService.logInfo('📂 TrainingStorage: Loaded program - Type: ${program.programType}, Current: ${program.currentLabel}');
      return program;
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace);
      await ComprehensiveLoggingService.logError('❌ TrainingStorage: Error loading program, using default: $e');
      return TrainingProgram.letters();
    }
  }

  /// Save current session (for recovery)
  Future<bool> saveCurrentSession(TrainingSession session) async {
    try {
      final success = await _storage.write(_currentSessionKey, jsonEncode(session.toJson()));
      if (success) {
        await ComprehensiveLoggingService.logInfo('💾 TrainingStorage: Current session saved for recovery');
      }
      return success;
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace);
      return false;
    }
  }

  /// Load current session (for recovery)
  Future<TrainingSession?> loadCurrentSession() async {
    try {
      final data = await _storage.read(_currentSessionKey);
      if (data == null) return null;
      
      final sessionJson = jsonDecode(data) as Map<String, dynamic>;
      final session = TrainingSession.fromJson(sessionJson);
      
      await ComprehensiveLoggingService.logInfo('📂 TrainingStorage: Recovered current session');
      return session;
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace);
      return null;
    }
  }

  /// Clear current session
  Future<bool> clearCurrentSession() async {
    try {
      final success = await _storage.delete(_currentSessionKey);
      if (success) {
        await ComprehensiveLoggingService.logInfo('🧹 TrainingStorage: Current session cleared');
      }
      return success;
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace);
      return false;
    }
  }

  /// Get storage statistics for debugging
  Future<Map<String, dynamic>> getStorageStats() async {
    try {
      final sessions = await getAllSessions();
      final program = await loadProgram();
      final currentSession = await loadCurrentSession();
      
      return {
        'totalSessions': sessions.length,
        'completedSessions': sessions.where((s) => s.isCompleted).length,
        'totalTrainingTime': sessions.fold<int>(0, (sum, s) => sum + s.durationSeconds),
        'totalExpEarned': sessions.fold<double>(0.0, (sum, s) => sum + s.expEarned),
        'programType': program.programType,
        'currentLabel': program.currentLabel,
        'hasCurrentSession': currentSession != null,
        'lastSessionDate': sessions.isNotEmpty ? sessions.first.createdAt.toIso8601String() : null,
      };
    } catch (e) {
      return {'error': e.toString()};
    }
  }
}
