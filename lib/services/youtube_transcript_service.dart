import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

/// 🛡️ BULLETPROOF YouTube Transcript Downloader Service
///
/// FAILSAFE FEATURES:
/// - Comprehensive error handling with detailed logging
/// - Automatic retry mechanisms with exponential backoff
/// - Quota management with real-time tracking
/// - Network connectivity validation
/// - File system integrity checks
/// - Terminal debugging for instant issue diagnosis
/// - Graceful degradation when services are unavailable
/// - Data validation and sanitization
/// - Memory management for large channels
/// - Progress tracking with detailed status updates
class YouTubeTranscriptService {
  static const String _baseApiUrl = 'https://www.googleapis.com/youtube/v3';
  static const int _maxResultsPerPage = 50;
  static const int _maxDailyQuota = 10000;
  static int _currentDailyUsage = 0;

  // 🛡️ FAILSAFE CONSTANTS
  static const int _maxRetries = 3;
  static const int _baseRetryDelayMs = 1000;
  static const int _maxChannelVideos = 1000; // Prevent memory issues
  static const int _networkTimeoutSeconds = 30;
  static const FlutterSecureStorage _storage = FlutterSecureStorage();

  // 🔍 TERMINAL DEBUGGING
  static final List<String> _debugLog = [];
  static final bool _debugMode = kDebugMode;
  
  // 🛡️ FAILSAFE API KEY MANAGEMENT
  static String get _apiKey {
    try {
      final key = dotenv.env['YOUTUBE_API_KEY'];
      if (key == null || key.isEmpty) {
        _logError('CRITICAL: Missing YOUTUBE_API_KEY in .env file');
        throw YouTubeServiceException('Missing YOUTUBE_API_KEY in .env');
      }
      _logDebug('✅ API key loaded successfully');
      return key;
    } catch (e) {
      _logError('CRITICAL: Failed to load API key: $e');
      rethrow;
    }
  }

  // 🔍 TERMINAL DEBUGGING METHODS
  static void _logDebug(String message) {
    final timestamp = DateTime.now().toIso8601String();
    final logEntry = '[$timestamp] 🔍 DEBUG: $message';
    _debugLog.add(logEntry);
    if (_debugMode) print(logEntry);
  }

  static void _logError(String message) {
    final timestamp = DateTime.now().toIso8601String();
    final logEntry = '[$timestamp] ❌ ERROR: $message';
    _debugLog.add(logEntry);
    if (_debugMode) print(logEntry);
  }

  static void _logWarning(String message) {
    final timestamp = DateTime.now().toIso8601String();
    final logEntry = '[$timestamp] ⚠️ WARNING: $message';
    _debugLog.add(logEntry);
    if (_debugMode) print(logEntry);
  }

  static void _logInfo(String message) {
    final timestamp = DateTime.now().toIso8601String();
    final logEntry = '[$timestamp] ℹ️ INFO: $message';
    _debugLog.add(logEntry);
    if (_debugMode) print(logEntry);
  }

  // 🛡️ SYSTEM HEALTH CHECK
  static Future<ServiceHealthStatus> checkServiceHealth() async {
    _logInfo('Starting comprehensive service health check...');

    final healthChecks = <String, bool>{};
    final issues = <String>[];

    try {
      // Check 1: API Key availability
      try {
        final _ = _apiKey;
        healthChecks['api_key'] = true;
        _logDebug('✅ API key check passed');
      } catch (e) {
        healthChecks['api_key'] = false;
        issues.add('API key not available: $e');
        _logError('❌ API key check failed: $e');
      }

      // Check 2: Network connectivity and API access
      try {
        final response = await http.get(
          Uri.parse('$_baseApiUrl/search?part=snippet&q=test&type=channel&maxResults=1&key=$_apiKey'),
        ).timeout(Duration(seconds: _networkTimeoutSeconds));

        if (response.statusCode == 200) {
          healthChecks['network'] = true;
          _logDebug('✅ Network connectivity check passed');
        } else if (response.statusCode == 403) {
          healthChecks['network'] = false;
          final responseBody = response.body;
          if (responseBody.contains('quotaExceeded')) {
            issues.add('API quota exceeded - wait until tomorrow or upgrade plan');
            _logError('❌ API quota exceeded');
          } else if (responseBody.contains('keyInvalid')) {
            issues.add('Invalid API key - check your YouTube Data API v3 key');
            _logError('❌ Invalid API key');
          } else if (responseBody.contains('accessNotConfigured')) {
            issues.add('YouTube Data API v3 not enabled - enable it in Google Cloud Console');
            _logError('❌ YouTube Data API v3 not enabled');
          } else {
            issues.add('API access forbidden (HTTP 403) - check API key restrictions in Google Cloud Console');
            _logError('❌ API access forbidden: HTTP 403 - Response: ${responseBody.substring(0, 200)}...');
          }
        } else {
          healthChecks['network'] = false;
          issues.add('Network connectivity issue: HTTP ${response.statusCode}');
          _logError('❌ Network check failed: HTTP ${response.statusCode}');
        }
      } catch (e) {
        healthChecks['network'] = false;
        issues.add('Network connectivity failed: $e');
        _logError('❌ Network connectivity check failed: $e');
      }

      // Check 3: File system access
      try {
        final directory = await getApplicationDocumentsDirectory();
        final testFile = File('${directory.path}/youtube_health_check.txt');
        await testFile.writeAsString('Health check test');
        await testFile.delete();
        healthChecks['filesystem'] = true;
        _logDebug('✅ File system access check passed');
      } catch (e) {
        healthChecks['filesystem'] = false;
        issues.add('File system access failed: $e');
        _logError('❌ File system check failed: $e');
      }

      // Check 4: Quota status
      try {
        await _loadQuotaUsage();
        final quotaOk = _currentDailyUsage < _maxDailyQuota * 0.9;
        healthChecks['quota'] = quotaOk;
        if (quotaOk) {
          _logDebug('✅ Quota check passed ($_currentDailyUsage/$_maxDailyQuota)');
        } else {
          issues.add('Quota nearly exhausted: $_currentDailyUsage/$_maxDailyQuota');
          _logWarning('⚠️ Quota nearly exhausted: $_currentDailyUsage/$_maxDailyQuota');
        }
      } catch (e) {
        healthChecks['quota'] = false;
        issues.add('Quota check failed: $e');
        _logError('❌ Quota check failed: $e');
      }

      final allHealthy = healthChecks.values.every((check) => check);
      final status = allHealthy ? ServiceHealthStatus.healthy : ServiceHealthStatus.degraded;

      _logInfo('Health check completed: $status (${healthChecks.length} checks)');

      return ServiceHealthStatus(
        isHealthy: allHealthy,
        checks: healthChecks,
        issues: issues,
        timestamp: DateTime.now(),
      );

    } catch (e) {
      _logError('CRITICAL: Health check system failure: $e');
      return ServiceHealthStatus(
        isHealthy: false,
        checks: {'system': false},
        issues: ['Health check system failure: $e'],
        timestamp: DateTime.now(),
      );
    }
  }

  /// Extract channel ID from various YouTube URL formats
  static String? extractChannelId(String url) {
    try {
      final uri = Uri.parse(url);

      // Handle different URL formats
      if (uri.pathSegments.isNotEmpty) {
        // Format: youtube.com/channel/UCxxxxx
        if (uri.pathSegments.contains('channel') && uri.pathSegments.length > 1) {
          final index = uri.pathSegments.indexOf('channel');
          if (index + 1 < uri.pathSegments.length) {
            return uri.pathSegments[index + 1];
          }
        }

        // Format: youtube.com/c/channelname or youtube.com/@username
        if (uri.pathSegments.contains('c') || uri.pathSegments.contains('@')) {
          return null; // Will need API resolution
        }

        // Format: youtube.com/user/username
        if (uri.pathSegments.contains('user')) {
          return null; // Will need API resolution
        }
      }

      return null;
    } catch (e) {
      if (kDebugMode) print('❌ Error parsing URL: $e');
      return null;
    }
  }

  /// Resolve custom URL or username to channel ID using YouTube API
  static Future<String?> _resolveToChannelId(String url) async {
    try {
      final uri = Uri.parse(url);
      String? searchQuery;

      if (uri.pathSegments.isNotEmpty) {
        if (uri.pathSegments.contains('c') && uri.pathSegments.length > 1) {
          final index = uri.pathSegments.indexOf('c');
          searchQuery = uri.pathSegments[index + 1];
        } else if (uri.pathSegments.contains('user') && uri.pathSegments.length > 1) {
          final index = uri.pathSegments.indexOf('user');
          searchQuery = uri.pathSegments[index + 1];
        } else if (uri.pathSegments.isNotEmpty && uri.pathSegments[0].startsWith('@')) {
          searchQuery = uri.pathSegments[0].substring(1);
        }
      }

      if (searchQuery == null) return null;

      // Search for channel using the query
      final searchUrl = '$_baseApiUrl/search?part=snippet&type=channel&q=$searchQuery&key=$_apiKey';
      final response = await http.get(Uri.parse(searchUrl));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['items'] != null && data['items'].isNotEmpty) {
          return data['items'][0]['snippet']['channelId'];
        }
      }

      return null;
    } catch (e) {
      if (kDebugMode) print('❌ Error resolving channel ID: $e');
      return null;
    }
  }

  /// Get all video IDs from a channel
  static Future<List<String>> getChannelVideoIds(String channelId, {Function(String)? onProgress}) async {
    final videoIds = <String>[];
    String? nextPageToken;
    int totalVideos = 0;

    try {
      do {
        final url = '$_baseApiUrl/search?part=id&channelId=$channelId&type=video&maxResults=$_maxResultsPerPage'
            '${nextPageToken != null ? '&pageToken=$nextPageToken' : ''}&key=$_apiKey';

        final response = await http.get(Uri.parse(url));

        if (response.statusCode == 200) {
          final data = json.decode(response.body);

          if (data['items'] != null) {
            for (final item in data['items']) {
              if (item['id'] != null && item['id']['videoId'] != null) {
                videoIds.add(item['id']['videoId']);
                totalVideos++;

                if (onProgress != null) {
                  onProgress('Found $totalVideos videos...');
                }
              }
            }
          }

          nextPageToken = data['nextPageToken'];

          // Quota management: Each search costs 100 units
          _currentDailyUsage += 100;
          if (_currentDailyUsage >= _maxDailyQuota) {
            if (kDebugMode) print('⚠️ Daily quota limit reached');
            break;
          }

        } else {
          if (kDebugMode) print('❌ API Error: ${response.statusCode} - ${response.body}');
          break;
        }

      } while (nextPageToken != null);

    } catch (e) {
      if (kDebugMode) print('❌ Error getting video IDs: $e');
    }

    return videoIds;
  }

  /// Get transcript for a single video
  static Future<String?> getVideoTranscript(String videoId) async {
    try {
      // First, get video details
      final videoUrl = '$_baseApiUrl/videos?part=snippet&id=$videoId&key=$_apiKey';
      final videoResponse = await http.get(Uri.parse(videoUrl));

      if (videoResponse.statusCode != 200) {
        return null;
      }

      final videoData = json.decode(videoResponse.body);
      if (videoData['items'] == null || videoData['items'].isEmpty) {
        return null;
      }

      final videoInfo = videoData['items'][0]['snippet'];
      final title = videoInfo['title'] ?? 'Unknown Title';
      final description = videoInfo['description'] ?? '';
      final publishedAt = videoInfo['publishedAt'] ?? '';

      // 🎯 REAL TRANSCRIPT EXTRACTION
      // Try to get actual transcript using YouTube's transcript endpoint
      String actualTranscript = '[No transcript available]';

      try {
        // Try to fetch transcript from YouTube's transcript endpoint
        final transcriptText = await _fetchYouTubeTranscript(videoId);
        if (transcriptText != null && transcriptText.isNotEmpty) {
          actualTranscript = transcriptText;
          _logDebug('✅ Successfully extracted transcript for video: $videoId');
        } else {
          _logWarning('⚠️ No transcript available for video: $videoId');
        }
      } catch (e) {
        _logWarning('⚠️ Could not extract transcript for $videoId: $e');
      }

      // Create comprehensive transcript with metadata
      final transcript = '''
=== VIDEO: $title ===
Published: $publishedAt
Video ID: $videoId
URL: https://www.youtube.com/watch?v=$videoId

DESCRIPTION:
$description

TRANSCRIPT:
$actualTranscript

=== END VIDEO ===

''';

      return transcript;

    } catch (e) {
      if (kDebugMode) print('❌ Error getting transcript for $videoId: $e');
      return null;
    }
  }

  /// 🎯 REAL TRANSCRIPT FETCHING - Extract actual YouTube transcripts
  static Future<String?> _fetchYouTubeTranscript(String videoId) async {
    try {
      // Method 1: Try to get transcript via YouTube's internal API
      final transcriptUrl = 'https://www.youtube.com/api/timedtext?lang=en&v=$videoId';

      final response = await http.get(
        Uri.parse(transcriptUrl),
        headers: {
          'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        },
      ).timeout(Duration(seconds: 10));

      if (response.statusCode == 200 && response.body.isNotEmpty) {
        // Parse XML transcript
        final transcriptText = _parseTranscriptXML(response.body);
        if (transcriptText.isNotEmpty) {
          return transcriptText;
        }
      }

      // Method 2: Try alternative transcript endpoint
      final altUrl = 'https://www.youtube.com/api/timedtext?lang=en&v=$videoId&fmt=srv3';
      final altResponse = await http.get(
        Uri.parse(altUrl),
        headers: {
          'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        },
      ).timeout(Duration(seconds: 10));

      if (altResponse.statusCode == 200 && altResponse.body.isNotEmpty) {
        final transcriptText = _parseTranscriptXML(altResponse.body);
        if (transcriptText.isNotEmpty) {
          return transcriptText;
        }
      }

      return null;
    } catch (e) {
      _logDebug('Could not fetch transcript for $videoId: $e');
      return null;
    }
  }

  /// Parse XML transcript response
  static String _parseTranscriptXML(String xmlContent) {
    try {
      // Simple XML parsing for transcript text
      final textRegex = RegExp(r'<text[^>]*>(.*?)</text>', multiLine: true, dotAll: true);
      final matches = textRegex.allMatches(xmlContent);

      final transcriptLines = <String>[];
      for (final match in matches) {
        final text = match.group(1)?.trim();
        if (text != null && text.isNotEmpty) {
          // Decode HTML entities
          final decodedText = text
              .replaceAll('&amp;', '&')
              .replaceAll('&lt;', '<')
              .replaceAll('&gt;', '>')
              .replaceAll('&quot;', '"')
              .replaceAll('&#39;', "'");
          transcriptLines.add(decodedText);
        }
      }

      return transcriptLines.join(' ');
    } catch (e) {
      _logDebug('Error parsing transcript XML: $e');
      return '';
    }
  }

  /// 🛡️ BULLETPROOF Download all transcripts from a channel
  static Future<String> downloadChannelTranscripts(
    String channelUrl, {
    Function(String)? onProgress,
    Function(String)? onError,
  }) async {
    _logInfo('🚀 Starting bulletproof channel transcript download');
    _logDebug('Channel URL: $channelUrl');

    try {
      // 🛡️ FAILSAFE 1: Pre-flight health check
      onProgress?.call('🔍 Running system health check...');
      final healthStatus = await checkServiceHealth();
      if (!healthStatus.isHealthy) {
        final issues = healthStatus.issues.join(', ');
        _logWarning('System health issues detected: $issues');
        onProgress?.call('⚠️ System health issues detected, proceeding with caution...');
      }

      // 🛡️ FAILSAFE 2: Load and validate quota
      await _loadQuotaUsage();
      if (_currentDailyUsage >= _maxDailyQuota * 0.8) {
        final warning = 'Quota usage high: $_currentDailyUsage/$_maxDailyQuota';
        _logWarning(warning);
        onProgress?.call('⚠️ $warning');
      }

      onProgress?.call('🔍 Extracting channel ID...');
      _logDebug('Extracting channel ID from URL');

      // 🛡️ FAILSAFE 3: Robust channel ID extraction with retry
      String? channelId = extractChannelId(channelUrl);
      if (channelId == null) {
        _logDebug('Direct extraction failed, attempting API resolution');
        channelId = await _retryWithBackoff(
          () => _resolveToChannelId(channelUrl),
          'Channel ID resolution',
        );
      }

      if (channelId == null) {
        throw YouTubeServiceException('Could not extract channel ID from URL: $channelUrl');
      }

      _logInfo('✅ Channel ID extracted: $channelId');

      onProgress?.call('📺 Getting channel videos...');

      // 🛡️ FAILSAFE 4: Robust video ID collection with memory management
      final videoIds = await _retryWithBackoff(
        () => getChannelVideoIds(channelId!, onProgress: onProgress),
        'Video ID collection',
      );

      if (videoIds.isEmpty) {
        throw YouTubeServiceException('No videos found in channel: $channelId');
      }

      // 🛡️ FAILSAFE 5: Memory management for large channels
      final limitedVideoIds = videoIds.take(_maxChannelVideos).toList();
      if (limitedVideoIds.length < videoIds.length) {
        final warning = 'Channel has ${videoIds.length} videos, limiting to ${limitedVideoIds.length} for memory safety';
        _logWarning(warning);
        onProgress?.call('⚠️ $warning');
      }

      _logInfo('✅ Found ${limitedVideoIds.length} videos to process');

      onProgress?.call('📝 Processing ${limitedVideoIds.length} videos...');

      // 🛡️ FAILSAFE 6: Robust channel details with fallback
      final channelDetails = await _retryWithBackoff(
        () => _getChannelDetails(channelId!),
        'Channel details retrieval',
      );
      final channelName = channelDetails['title'] ?? 'Unknown Channel';
      final channelDescription = channelDetails['description'] ?? '';

      // Build the complete transcript file with enhanced metadata
      final buffer = StringBuffer();

      // Add comprehensive header
      buffer.writeln('='.padRight(80, '='));
      buffer.writeln('🛡️ BULLETPROOF YOUTUBE CHANNEL TRANSCRIPTS');
      buffer.writeln('Channel: $channelName');
      buffer.writeln('Channel ID: $channelId');
      buffer.writeln('Total Videos Found: ${videoIds.length}');
      buffer.writeln('Videos Processed: ${limitedVideoIds.length}');
      buffer.writeln('Downloaded: ${DateTime.now().toIso8601String()}');
      buffer.writeln('Service Health: ${healthStatus.isHealthy ? "HEALTHY" : "DEGRADED"}');
      buffer.writeln('Quota Usage: $_currentDailyUsage/$_maxDailyQuota');
      buffer.writeln('='.padRight(80, '='));
      buffer.writeln();

      if (channelDescription.isNotEmpty) {
        buffer.writeln('CHANNEL DESCRIPTION:');
        buffer.writeln(channelDescription);
        buffer.writeln();
        buffer.writeln('-'.padRight(80, '-'));
        buffer.writeln();
      }

      // 🛡️ FAILSAFE 7: Process videos with comprehensive error handling
      int processedCount = 0;
      int errorCount = 0;

      for (final videoId in limitedVideoIds) {
        processedCount++;
        onProgress?.call('📝 Processing video $processedCount/${limitedVideoIds.length}...');
        _logDebug('Processing video $processedCount: $videoId');

        try {
          final transcript = await _retryWithBackoff(
            () => getVideoTranscript(videoId),
            'Video transcript retrieval for $videoId',
          );

          if (transcript != null) {
            buffer.writeln(transcript);
            _logDebug('✅ Successfully processed video: $videoId');
          } else {
            errorCount++;
            _logWarning('⚠️ No transcript available for video: $videoId');
          }
        } catch (e) {
          errorCount++;
          _logError('❌ Failed to process video $videoId: $e');
          // Continue processing other videos
        }

        // 🛡️ FAILSAFE 8: Rate limiting and quota management
        await Future.delayed(const Duration(milliseconds: 100));
        await _saveQuotaUsage(); // Save quota after each video

        // 🛡️ FAILSAFE 9: Quota protection
        if (_currentDailyUsage >= _maxDailyQuota * 0.9) {
          final warning = 'Approaching quota limit, stopping at video $processedCount';
          _logWarning(warning);
          onProgress?.call('⚠️ $warning');
          break;
        }
      }

      // Add comprehensive footer with statistics
      buffer.writeln('='.padRight(80, '='));
      buffer.writeln('🛡️ BULLETPROOF DOWNLOAD COMPLETE');
      buffer.writeln('Videos Processed: $processedCount');
      buffer.writeln('Successful: ${processedCount - errorCount}');
      buffer.writeln('Errors: $errorCount');
      buffer.writeln('Final Quota Usage: $_currentDailyUsage/$_maxDailyQuota');
      buffer.writeln('Completion Time: ${DateTime.now().toIso8601String()}');
      buffer.writeln('='.padRight(80, '='));

      final result = buffer.toString();
      _logInfo('✅ Bulletproof download completed successfully');
      _logInfo('📊 Statistics: $processedCount processed, $errorCount errors');

      return result;

    } catch (e) {
      final error = 'CRITICAL: Bulletproof download failed: $e';
      _logError(error);
      onError?.call(error);
      rethrow;
    }
  }

  /// Get channel details
  static Future<Map<String, dynamic>> _getChannelDetails(String channelId) async {
    try {
      final url = '$_baseApiUrl/channels?part=snippet&id=$channelId&key=$_apiKey';
      final response = await http.get(Uri.parse(url));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['items'] != null && data['items'].isNotEmpty) {
          return data['items'][0]['snippet'];
        }
      }

      return {};
    } catch (e) {
      if (kDebugMode) print('❌ Error getting channel details: $e');
      return {};
    }
  }

  /// Save transcripts to app documents directory and export
  static Future<String> saveTranscriptsToFile(String content, String channelName) async {
    try {
      // Clean channel name for filename
      final cleanName = channelName.replaceAll(RegExp(r'[^\w\s-]'), '').replaceAll(' ', '_');
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileName = 'youtube_${cleanName}_$timestamp.txt';

      // Get app documents directory
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/$fileName');

      // Write content to file
      await file.writeAsString(content);

      // 🎯 SAVE TO ASSETS/TRANSCRIPTS/ FOR IMMEDIATE COACH ACCESS
      await _saveToAssetsDirectory(content, fileName);

      // Also copy to assets directory for transcript service integration
      await _copyToAssetsDirectory(content, fileName);

      if (kDebugMode) {
        print('✅ Transcripts saved to: ${file.path}');
        print('📁 File size: ${(content.length / 1024).toStringAsFixed(1)} KB');
      }

      return fileName;

    } catch (e) {
      if (kDebugMode) print('❌ Error saving transcripts: $e');
      rethrow;
    }
  }

  /// 🎯 Save directly to assets/transcripts/ directory for immediate coach access
  static Future<void> _saveToAssetsDirectory(String content, String fileName) async {
    try {
      // Get the project root directory (where pubspec.yaml is located)
      final currentDirectory = Directory.current;
      final assetsTranscriptsDir = Directory('${currentDirectory.path}/assets/transcripts');

      // Create assets/transcripts directory if it doesn't exist
      if (!await assetsTranscriptsDir.exists()) {
        await assetsTranscriptsDir.create(recursive: true);
        _logInfo('📁 Created assets/transcripts directory');
      }

      // Save the transcript file directly to assets/transcripts/
      final assetsFile = File('${assetsTranscriptsDir.path}/$fileName');
      await assetsFile.writeAsString(content);

      _logInfo('✅ Transcript saved to assets/transcripts/$fileName');
      _logInfo('🎯 Coaches now have immediate access to this transcript!');

      if (kDebugMode) {
        print('✅ Transcript saved to: ${assetsFile.path}');
        print('🎯 Coaches can now access this transcript immediately!');
      }
    } catch (e) {
      _logError('❌ Failed to save to assets/transcripts/: $e');
      if (kDebugMode) print('❌ Could not save to assets/transcripts/: $e');
    }
  }

  /// Copy transcripts to assets directory for integration with transcript service
  static Future<void> _copyToAssetsDirectory(String content, String fileName) async {
    try {
      // For development, we'll save to a location that can be manually copied to assets
      final directory = await getApplicationDocumentsDirectory();
      final assetsFile = File('${directory.path}/for_assets_$fileName');
      await assetsFile.writeAsString(content);

      if (kDebugMode) {
        print('📋 Backup copy saved to: ${assetsFile.path}');
      }
    } catch (e) {
      if (kDebugMode) print('⚠️ Could not create backup copy: $e');
    }
  }

  /// Export transcripts file for sharing
  static Future<void> exportTranscripts(String fileName) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/$fileName');

      if (await file.exists()) {
        await Share.shareXFiles(
          [XFile(file.path)],
          text: 'YouTube Channel Transcripts',
        );
      } else {
        throw Exception('File not found: $fileName');
      }
    } catch (e) {
      if (kDebugMode) print('❌ Error exporting transcripts: $e');
      rethrow;
    }
  }

  /// Get list of downloaded transcript files with metadata
  static Future<List<Map<String, dynamic>>> getDownloadedTranscripts() async {
    try {
      final transcripts = <Map<String, dynamic>>[];

      // Check both Documents directory and assets/transcripts/
      final documentsDir = await getApplicationDocumentsDirectory();
      final assetsDir = Directory('${Directory.current.path}/assets/transcripts');

      final directories = [documentsDir];
      if (await assetsDir.exists()) {
        directories.add(assetsDir);
      }

      for (final directory in directories) {
        final files = directory.listSync()
            .whereType<File>()
            .where((file) => file.path.contains('youtube_') && file.path.endsWith('.txt'))
            .toList();

        for (final file in files) {
          final fileName = file.path.split('/').last;
          final stat = await file.stat();
          final isInAssets = file.path.contains('assets/transcripts');

          transcripts.add({
            'fileName': fileName,
            'filePath': file.path,
            'size': stat.size,
            'modified': stat.modified,
            'location': isInAssets ? 'assets' : 'documents',
            'isAccessibleToCoaches': isInAssets,
          });
        }
      }

      // Sort by modification date (newest first)
      transcripts.sort((a, b) => (b['modified'] as DateTime).compareTo(a['modified'] as DateTime));

      return transcripts;
    } catch (e) {
      _logError('❌ Error getting downloaded transcripts: $e');
      if (kDebugMode) print('❌ Error getting downloaded transcripts: $e');
      return [];
    }
  }

  /// Rename a downloaded transcript file
  static Future<bool> renameTranscriptFile(String oldFileName, String newFileName) async {
    try {
      // Ensure new filename has .txt extension
      if (!newFileName.endsWith('.txt')) {
        newFileName = '$newFileName.txt';
      }

      // Clean the new filename
      newFileName = newFileName.replaceAll(RegExp(r'[^\w\s.-]'), '').replaceAll(' ', '_');

      // Check both locations
      final documentsDir = await getApplicationDocumentsDirectory();
      final assetsDir = Directory('${Directory.current.path}/assets/transcripts');

      final documentsFile = File('${documentsDir.path}/$oldFileName');
      final assetsFile = File('${assetsDir.path}/$oldFileName');

      bool renamed = false;

      // Rename in documents directory
      if (await documentsFile.exists()) {
        final newDocumentsFile = File('${documentsDir.path}/$newFileName');
        await documentsFile.rename(newDocumentsFile.path);
        renamed = true;
        _logInfo('✅ Renamed in documents: $oldFileName → $newFileName');
      }

      // Rename in assets directory
      if (await assetsFile.exists()) {
        final newAssetsFile = File('${assetsDir.path}/$newFileName');
        await assetsFile.rename(newAssetsFile.path);
        renamed = true;
        _logInfo('✅ Renamed in assets: $oldFileName → $newFileName');
      }

      return renamed;
    } catch (e) {
      _logError('❌ Error renaming transcript file: $e');
      if (kDebugMode) print('❌ Error renaming transcript file: $e');
      return false;
    }
  }

  /// Delete a downloaded transcript file
  static Future<bool> deleteTranscriptFile(String fileName) async {
    try {
      final documentsDir = await getApplicationDocumentsDirectory();
      final assetsDir = Directory('${Directory.current.path}/assets/transcripts');

      final documentsFile = File('${documentsDir.path}/$fileName');
      final assetsFile = File('${assetsDir.path}/$fileName');

      bool deleted = false;

      // Delete from documents directory
      if (await documentsFile.exists()) {
        await documentsFile.delete();
        deleted = true;
        _logInfo('🗑️ Deleted from documents: $fileName');
      }

      // Delete from assets directory
      if (await assetsFile.exists()) {
        await assetsFile.delete();
        deleted = true;
        _logInfo('🗑️ Deleted from assets: $fileName');
      }

      return deleted;
    } catch (e) {
      _logError('❌ Error deleting transcript file: $e');
      if (kDebugMode) print('❌ Error deleting transcript file: $e');
      return false;
    }
  }

  /// Move transcript from documents to assets directory
  static Future<bool> moveToAssetsDirectory(String fileName) async {
    try {
      final documentsDir = await getApplicationDocumentsDirectory();
      final assetsDir = Directory('${Directory.current.path}/assets/transcripts');

      final documentsFile = File('${documentsDir.path}/$fileName');
      final assetsFile = File('${assetsDir.path}/$fileName');

      if (!await documentsFile.exists()) {
        return false;
      }

      // Create assets directory if it doesn't exist
      if (!await assetsDir.exists()) {
        await assetsDir.create(recursive: true);
      }

      // Copy content to assets directory
      final content = await documentsFile.readAsString();
      await assetsFile.writeAsString(content);

      _logInfo('✅ Moved to assets directory: $fileName');
      _logInfo('🎯 Coaches now have access to this transcript!');

      return true;
    } catch (e) {
      _logError('❌ Error moving transcript to assets: $e');
      if (kDebugMode) print('❌ Error moving transcript to assets: $e');
      return false;
    }
  }

  /// Reset daily quota usage (call this once per day)
  static void resetDailyQuota() {
    _currentDailyUsage = 0;
    if (kDebugMode) print('🔄 Daily quota usage reset');
  }

  /// Get current quota usage
  static Map<String, dynamic> getQuotaUsage() {
    return {
      'used': _currentDailyUsage,
      'limit': _maxDailyQuota,
      'remaining': _maxDailyQuota - _currentDailyUsage,
      'percentage': (_currentDailyUsage / _maxDailyQuota * 100).toStringAsFixed(1),
    };
  }

  // 🛡️ FAILSAFE HELPER METHODS

  /// Load quota usage from secure storage
  static Future<void> _loadQuotaUsage() async {
    try {
      final stored = await _storage.read(key: 'youtube_quota_usage');
      if (stored != null) {
        final data = json.decode(stored);
        final lastReset = DateTime.parse(data['lastReset']);
        final now = DateTime.now();

        // Reset if it's a new day
        if (now.day != lastReset.day || now.month != lastReset.month || now.year != lastReset.year) {
          _currentDailyUsage = 0;
          await _saveQuotaUsage();
        } else {
          _currentDailyUsage = data['usage'] ?? 0;
        }
      }
    } catch (e) {
      _logError('Failed to load quota usage: $e');
      _currentDailyUsage = 0;
    }
  }

  /// Save quota usage to secure storage
  static Future<void> _saveQuotaUsage() async {
    try {
      final data = {
        'usage': _currentDailyUsage,
        'lastReset': DateTime.now().toIso8601String(),
      };
      await _storage.write(key: 'youtube_quota_usage', value: json.encode(data));
    } catch (e) {
      _logError('Failed to save quota usage: $e');
    }
  }

  /// 🛡️ RETRY MECHANISM WITH EXPONENTIAL BACKOFF
  static Future<T> _retryWithBackoff<T>(
    Future<T> Function() operation,
    String operationName,
  ) async {
    for (int attempt = 1; attempt <= _maxRetries; attempt++) {
      try {
        _logDebug('Attempting $operationName (attempt $attempt/$_maxRetries)');
        final result = await operation();
        if (attempt > 1) {
          _logInfo('✅ $operationName succeeded on attempt $attempt');
        }
        return result;
      } catch (e) {
        _logWarning('❌ $operationName failed on attempt $attempt: $e');

        if (attempt == _maxRetries) {
          _logError('CRITICAL: $operationName failed after $_maxRetries attempts');
          rethrow;
        }

        // Exponential backoff
        final delayMs = _baseRetryDelayMs * (1 << (attempt - 1));
        _logDebug('⏳ Waiting ${delayMs}ms before retry...');
        await Future.delayed(Duration(milliseconds: delayMs));
      }
    }

    throw Exception('Retry mechanism failed for $operationName');
  }

  /// 🔍 GET COMPREHENSIVE DEBUG LOG
  static List<String> getDebugLog() => List.from(_debugLog);

  /// 🔍 CLEAR DEBUG LOG
  static void clearDebugLog() {
    _debugLog.clear();
    _logInfo('Debug log cleared');
  }

  /// 🔍 EXPORT DEBUG LOG
  static Future<String> exportDebugLog() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileName = 'youtube_debug_log_$timestamp.txt';
      final file = File('${directory.path}/$fileName');

      final logContent = _debugLog.join('\n');
      await file.writeAsString(logContent);

      _logInfo('Debug log exported to: $fileName');
      return fileName;
    } catch (e) {
      _logError('Failed to export debug log: $e');
      rethrow;
    }
  }
}

// 🛡️ FAILSAFE DATA CLASSES

/// Custom exception for YouTube service errors
class YouTubeServiceException implements Exception {
  final String message;
  final String? code;
  final dynamic originalError;

  const YouTubeServiceException(this.message, {this.code, this.originalError});

  @override
  String toString() => 'YouTubeServiceException: $message${code != null ? ' (Code: $code)' : ''}';
}

/// Service health status with comprehensive diagnostics
class ServiceHealthStatus {
  final bool isHealthy;
  final Map<String, bool> checks;
  final List<String> issues;
  final DateTime timestamp;

  const ServiceHealthStatus({
    required this.isHealthy,
    required this.checks,
    required this.issues,
    required this.timestamp,
  });

  static ServiceHealthStatus get healthy => ServiceHealthStatus(
    isHealthy: true,
    checks: const {},
    issues: const [],
    timestamp: DateTime.now(),
  );

  static ServiceHealthStatus get degraded => ServiceHealthStatus(
    isHealthy: false,
    checks: const {},
    issues: const [],
    timestamp: DateTime.now(),
  );

  @override
  String toString() {
    final status = isHealthy ? 'HEALTHY' : 'DEGRADED';
    final checkCount = checks.length;
    final passedChecks = checks.values.where((check) => check).length;
    return 'ServiceHealthStatus: $status ($passedChecks/$checkCount checks passed)';
  }
}