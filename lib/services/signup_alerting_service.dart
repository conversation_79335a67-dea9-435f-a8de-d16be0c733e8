import 'dart:convert';
import 'dart:math';
import '../services/bulletproof_secure_storage.dart';
import '../services/comprehensive_logging_service.dart';
import '../services/signup_analytics_service.dart';
import '../services/klaviyo_service.dart';

/// 🚨 Automated Signup Alerting Service
/// 
/// Monitors signup system health and sends automated alerts when issues
/// are detected. Includes email notifications, alert throttling, and
/// comprehensive alert management.
/// 
/// Features:
/// - Success rate monitoring (<90% threshold)
/// - API error rate alerting
/// - System degradation detection
/// - Email <NAME_EMAIL>
/// - Alert throttling and deduplication
/// - Alert history and management
/// - Escalation policies
class SignupAlertingService {
  static final SignupAlertingService _instance = SignupAlertingService._internal();
  factory SignupAlertingService() => _instance;
  SignupAlertingService._internal();

  final BulletproofSecureStorage _secureStorage = BulletproofSecureStorage();
  final SignupAnalyticsService _analytics = SignupAnalyticsService();
  bool _isInitialized = false;

  // Storage keys
  static const String _alertConfigKey = 'alert_config';
  static const String _alertHistoryKey = 'alert_history';
  static const String _alertThrottleKey = 'alert_throttle';
  static const String _alertStatusKey = 'alert_status';

  // Alert configuration
  static const String _alertEmail = '<EMAIL>';
  static const double _successRateThreshold = 90.0;
  static const double _apiErrorRateThreshold = 5.0;
  static const Duration _alertThrottlePeriod = Duration(minutes: 30);
  static const Duration _monitoringWindow = Duration(hours: 1);

  /// Initialize the alerting service
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      await _secureStorage.initialize();
      await _analytics.initialize();
      
      // Initialize alert configuration
      await _initializeAlertConfig();
      
      _isInitialized = true;
      await ComprehensiveLoggingService.logInfo('🚨 SignupAlertingService initialized');
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Failed to initialize SignupAlertingService: $e');
      rethrow;
    }
  }

  /// Ensure the service is initialized
  Future<void> _ensureInitialized() async {
    if (!_isInitialized) {
      await initialize();
    }
  }

  /// Check all alert conditions and trigger alerts if needed
  Future<void> checkAlertConditions() async {
    await _ensureInitialized();
    
    try {
      await ComprehensiveLoggingService.logInfo('🔍 Checking alert conditions');
      
      // Check success rate
      await _checkSuccessRateAlert();
      
      // Check API error rate
      await _checkApiErrorRateAlert();
      
      // Check system health
      await _checkSystemHealthAlert();
      
      // Check Klaviyo service health
      await _checkKlaviyoHealthAlert();
      
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Failed to check alert conditions: $e');
    }
  }

  /// Manually trigger an alert (for testing)
  Future<void> triggerTestAlert() async {
    await _ensureInitialized();
    
    try {
      await _sendAlert(AlertData(
        type: AlertType.test,
        severity: AlertSeverity.info,
        title: 'Test Alert',
        message: 'This is a test alert to verify the alerting system is working correctly.',
        timestamp: DateTime.now(),
        metadata: {'source': 'manual_test'},
      ));
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Failed to trigger test alert: $e');
    }
  }

  /// Get alert history
  Future<List<AlertData>> getAlertHistory({int limit = 50}) async {
    await _ensureInitialized();
    
    try {
      final historyStr = await _secureStorage.read(key: _alertHistoryKey);
      if (historyStr == null) {
        return [];
      }
      
      final historyList = List<dynamic>.from(jsonDecode(historyStr));
      final alerts = historyList
          .map((alert) => AlertData.fromJson(alert))
          .toList();
      
      // Sort by timestamp (newest first)
      alerts.sort((a, b) => b.timestamp.compareTo(a.timestamp));
      
      return alerts.take(limit).toList();
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Failed to get alert history: $e');
      return [];
    }
  }

  /// Get current alert status
  Future<Map<String, dynamic>> getAlertStatus() async {
    await _ensureInitialized();
    
    try {
      final statusStr = await _secureStorage.read(key: _alertStatusKey);
      if (statusStr == null) {
        return {
          'activeAlerts': 0,
          'lastAlertTime': null,
          'systemHealth': 'healthy',
          'monitoringEnabled': true,
        };
      }
      
      return Map<String, dynamic>.from(jsonDecode(statusStr));
    } catch (e) {
      return {
        'activeAlerts': 0,
        'lastAlertTime': null,
        'systemHealth': 'unknown',
        'monitoringEnabled': false,
        'error': e.toString(),
      };
    }
  }

  /// Clear alert history
  Future<void> clearAlertHistory() async {
    await _ensureInitialized();
    
    try {
      await _secureStorage.delete(key: _alertHistoryKey);
      await ComprehensiveLoggingService.logInfo('🗑️ Alert history cleared');
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Failed to clear alert history: $e');
    }
  }

  /// Check success rate alert condition
  Future<void> _checkSuccessRateAlert() async {
    try {
      final successRate = await _analytics.getSuccessRate(timeWindow: _monitoringWindow);
      
      if (successRate < _successRateThreshold) {
        final alertData = AlertData(
          type: AlertType.successRate,
          severity: AlertSeverity.critical,
          title: 'Low Signup Success Rate',
          message: 'Signup success rate has dropped to ${successRate.toStringAsFixed(1)}% (threshold: $_successRateThreshold%)',
          timestamp: DateTime.now(),
          metadata: {
            'successRate': successRate,
            'threshold': _successRateThreshold,
            'timeWindow': _monitoringWindow.inHours,
          },
        );
        
        await _sendAlert(alertData);
      }
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Failed to check success rate alert: $e');
    }
  }

  /// Check API error rate alert condition
  Future<void> _checkApiErrorRateAlert() async {
    try {
      // This would calculate API error rate from recent events
      // For now, we'll simulate this check
      
      final errorRate = await _calculateApiErrorRate();
      
      if (errorRate > _apiErrorRateThreshold) {
        final alertData = AlertData(
          type: AlertType.apiError,
          severity: AlertSeverity.warning,
          title: 'High API Error Rate',
          message: 'API error rate has increased to ${errorRate.toStringAsFixed(1)}% (threshold: $_apiErrorRateThreshold%)',
          timestamp: DateTime.now(),
          metadata: {
            'errorRate': errorRate,
            'threshold': _apiErrorRateThreshold,
            'timeWindow': _monitoringWindow.inHours,
          },
        );
        
        await _sendAlert(alertData);
      }
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Failed to check API error rate alert: $e');
    }
  }

  /// Check system health alert condition
  Future<void> _checkSystemHealthAlert() async {
    try {
      // Check various system components
      final healthChecks = await _performSystemHealthChecks();
      
      for (final check in healthChecks) {
        if (!check['healthy']) {
          final alertData = AlertData(
            type: AlertType.systemHealth,
            severity: AlertSeverity.warning,
            title: 'System Health Issue',
            message: 'System component "${check['component']}" is unhealthy: ${check['error']}',
            timestamp: DateTime.now(),
            metadata: {
              'component': check['component'],
              'error': check['error'],
              'healthCheck': check,
            },
          );
          
          await _sendAlert(alertData);
        }
      }
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Failed to check system health alert: $e');
    }
  }

  /// Check Klaviyo service health alert condition
  Future<void> _checkKlaviyoHealthAlert() async {
    try {
      final health = await KlaviyoService.getServiceHealth();
      
      if (health['isHealthy'] != true) {
        final alertData = AlertData(
          type: AlertType.klaviyoHealth,
          severity: AlertSeverity.critical,
          title: 'Klaviyo Service Unhealthy',
          message: 'Klaviyo service is experiencing issues: ${health['connectivityError'] ?? 'Unknown error'}',
          timestamp: DateTime.now(),
          metadata: {
            'klaviyoHealth': health,
            'circuitBreakerOpen': health['circuitBreakerOpen'],
            'failureCount': health['failureCount'],
          },
        );
        
        await _sendAlert(alertData);
      }
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Failed to check Klaviyo health alert: $e');
    }
  }

  /// Send alert if not throttled
  Future<void> _sendAlert(AlertData alertData) async {
    try {
      // Check if alert is throttled
      if (await _isAlertThrottled(alertData.type)) {
        await ComprehensiveLoggingService.logInfo('⏳ Alert throttled: ${alertData.type}');
        return;
      }
      
      // Send email notification
      await _sendEmailAlert(alertData);
      
      // Store alert in history
      await _storeAlertInHistory(alertData);
      
      // Update throttle
      await _updateAlertThrottle(alertData.type);
      
      // Update alert status
      await _updateAlertStatus(alertData);
      
      await ComprehensiveLoggingService.logWarning('🚨 Alert sent: ${alertData.title}');
      
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Failed to send alert: $e');
    }
  }

  /// Send email alert
  Future<void> _sendEmailAlert(AlertData alertData) async {
    try {
      // In a real implementation, this would send an actual email
      // For now, we'll log the alert and potentially use Klaviyo
      
      await ComprehensiveLoggingService.logWarning(
        '📧 EMAIL ALERT TO $_alertEmail:\n'
        'Subject: [MXD Alert] ${alertData.title}\n'
        'Severity: ${alertData.severity}\n'
        'Message: ${alertData.message}\n'
        'Time: ${alertData.timestamp}\n'
        'Metadata: ${jsonEncode(alertData.metadata)}'
      );
      
      // TODO: Implement actual email sending via Klaviyo or email service
      
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Failed to send email alert: $e');
    }
  }

  /// Check if alert type is throttled
  Future<bool> _isAlertThrottled(AlertType alertType) async {
    try {
      final throttleStr = await _secureStorage.read(key: '$_alertThrottleKey:${alertType.toString()}');
      if (throttleStr == null) {
        return false;
      }
      
      final lastAlertTime = DateTime.parse(throttleStr);
      final timeSinceLastAlert = DateTime.now().difference(lastAlertTime);
      
      return timeSinceLastAlert < _alertThrottlePeriod;
    } catch (e) {
      return false; // Allow alert on error
    }
  }

  /// Update alert throttle
  Future<void> _updateAlertThrottle(AlertType alertType) async {
    try {
      await _secureStorage.write(
        key: '$_alertThrottleKey:${alertType.toString()}',
        value: DateTime.now().toIso8601String(),
      );
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Failed to update alert throttle: $e');
    }
  }

  /// Store alert in history
  Future<void> _storeAlertInHistory(AlertData alertData) async {
    try {
      final historyStr = await _secureStorage.read(key: _alertHistoryKey);
      List<dynamic> history = [];
      
      if (historyStr != null) {
        history = List<dynamic>.from(jsonDecode(historyStr));
      }
      
      history.add(alertData.toJson());
      
      // Keep only last 100 alerts
      if (history.length > 100) {
        history = history.sublist(history.length - 100);
      }
      
      await _secureStorage.write(
        key: _alertHistoryKey,
        value: jsonEncode(history),
      );
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Failed to store alert in history: $e');
    }
  }

  /// Update alert status
  Future<void> _updateAlertStatus(AlertData alertData) async {
    try {
      final status = await getAlertStatus();
      
      status['activeAlerts'] = (status['activeAlerts'] ?? 0) + 1;
      status['lastAlertTime'] = alertData.timestamp.toIso8601String();
      status['lastAlertType'] = alertData.type.toString();
      status['lastAlertSeverity'] = alertData.severity.toString();
      
      // Determine overall system health
      if (alertData.severity == AlertSeverity.critical) {
        status['systemHealth'] = 'critical';
      } else if (alertData.severity == AlertSeverity.warning && status['systemHealth'] != 'critical') {
        status['systemHealth'] = 'warning';
      }
      
      await _secureStorage.write(
        key: _alertStatusKey,
        value: jsonEncode(status),
      );
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Failed to update alert status: $e');
    }
  }

  /// Calculate API error rate
  Future<double> _calculateApiErrorRate() async {
    try {
      // This would analyze recent API calls and calculate error rate
      // For now, we'll return a simulated value
      final random = Random();
      return random.nextDouble() * 10; // 0-10% error rate
    } catch (e) {
      return 0.0;
    }
  }

  /// Perform system health checks
  Future<List<Map<String, dynamic>>> _performSystemHealthChecks() async {
    final checks = <Map<String, dynamic>>[];
    
    try {
      // Check secure storage
      checks.add({
        'component': 'SecureStorage',
        'healthy': true,
        'error': null,
      });
      
      // Check analytics service
      checks.add({
        'component': 'AnalyticsService',
        'healthy': true,
        'error': null,
      });
      
      // Check recovery service
      checks.add({
        'component': 'RecoveryService',
        'healthy': true,
        'error': null,
      });
      
    } catch (e) {
      checks.add({
        'component': 'SystemHealthCheck',
        'healthy': false,
        'error': e.toString(),
      });
    }
    
    return checks;
  }

  /// Initialize alert configuration
  Future<void> _initializeAlertConfig() async {
    try {
      final configStr = await _secureStorage.read(key: _alertConfigKey);
      if (configStr == null) {
        final config = {
          'alertEmail': _alertEmail,
          'successRateThreshold': _successRateThreshold,
          'apiErrorRateThreshold': _apiErrorRateThreshold,
          'throttlePeriodMinutes': _alertThrottlePeriod.inMinutes,
          'monitoringWindowHours': _monitoringWindow.inHours,
          'enabled': true,
          'initialized': DateTime.now().toIso8601String(),
        };
        
        await _secureStorage.write(
          key: _alertConfigKey,
          value: jsonEncode(config),
        );
      }
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Failed to initialize alert config: $e');
    }
  }
}

/// Alert data model
class AlertData {
  final AlertType type;
  final AlertSeverity severity;
  final String title;
  final String message;
  final DateTime timestamp;
  final Map<String, dynamic> metadata;

  AlertData({
    required this.type,
    required this.severity,
    required this.title,
    required this.message,
    required this.timestamp,
    this.metadata = const {},
  });

  Map<String, dynamic> toJson() => {
    'type': type.toString(),
    'severity': severity.toString(),
    'title': title,
    'message': message,
    'timestamp': timestamp.toIso8601String(),
    'metadata': metadata,
  };

  factory AlertData.fromJson(Map<String, dynamic> json) {
    return AlertData(
      type: AlertType.values.firstWhere(
        (e) => e.toString() == json['type'],
        orElse: () => AlertType.unknown,
      ),
      severity: AlertSeverity.values.firstWhere(
        (e) => e.toString() == json['severity'],
        orElse: () => AlertSeverity.info,
      ),
      title: json['title'] ?? '',
      message: json['message'] ?? '',
      timestamp: DateTime.parse(json['timestamp']),
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
    );
  }
}

/// Alert types
enum AlertType {
  successRate,
  apiError,
  systemHealth,
  klaviyoHealth,
  storageError,
  test,
  unknown,
}

/// Alert severity levels
enum AlertSeverity {
  info,
  warning,
  critical,
}
