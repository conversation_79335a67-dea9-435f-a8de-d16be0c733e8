// 📁 lib/services/guest_conversion_service.dart

import '../models/user_model.dart';
import '../models/guest_user_model.dart';
import '../services/guest_session_service.dart';
import '../services/auth_service.dart';
import '../services/comprehensive_logging_service.dart';

/// Service for converting guest users to full accounts
/// 
/// Handles the seamless transition from guest browsing to authenticated users,
/// preserving relevant session data and providing a smooth upgrade experience.
class GuestConversionService {
  static final GuestConversionService _instance = GuestConversionService._internal();
  factory GuestConversionService() => _instance;
  GuestConversionService._internal();

  final GuestSessionService _guestService = GuestSessionService();
  final AuthService _authService = AuthService();

  /// Convert guest user to full account
  /// 
  /// Takes guest session data and creates a new user account with preserved
  /// preferences and engagement data where appropriate.
  Future<ConversionResult> convertGuestToUser({
    required String username,
    required String email,
    required String password,
    required String gender,
  }) async {
    try {
      await ComprehensiveLoggingService.logInfo('🔄 Starting guest-to-user conversion');
      
      // Get current guest session data
      final guestUser = _guestService.currentGuestUser;
      if (guestUser == null) {
        return ConversionResult.failure('No active guest session found');
      }
      
      // Collect conversion analytics
      final conversionData = _guestService.getConversionData();
      await ComprehensiveLoggingService.logInfo('📊 Guest conversion data: $conversionData');
      
      // Create new user account
      final newUser = await _authService.signUp(
        username: username,
        email: email,
        password: password,
        gender: gender,
      );
      
      // Apply guest preferences to new user
      final enhancedUser = _applyGuestPreferences(newUser, guestUser, conversionData);
      
      // End guest session
      _guestService.endGuestSession();
      
      await ComprehensiveLoggingService.logInfo('✅ Guest conversion completed successfully');
      
      return ConversionResult.success(
        user: enhancedUser,
        conversionData: conversionData,
        message: 'Account created successfully! Your browsing preferences have been preserved.',
      );
      
    } catch (e, stackTrace) {
      await ComprehensiveLoggingService.logError(
        '❌ Guest conversion failed: $e',
        error: e,
        stackTrace: stackTrace,
      );
      return ConversionResult.failure('Failed to create account: $e');
    }
  }

  /// Apply guest session preferences to new user account
  User _applyGuestPreferences(
    User newUser,
    GuestUser guestUser,
    Map<String, dynamic> conversionData,
  ) {
    final now = DateTime.now();
    
    // Determine if user should skip tutorial based on guest experience
    final shouldSkipTutorial = guestUser.hasSeenTutorial || 
        (conversionData['sessionDuration'] as int? ?? 0) > 5;
    
    // Determine if user has seen coach demo
    final hasMetCoaches = guestUser.hasSeenCoachDemo;
    
    // Create enhanced onboarding progress
    final enhancedOnboarding = newUser.onboardingProgress.copyWith(
      hasCompletedTutorial: shouldSkipTutorial,
      hasMetCoaches: hasMetCoaches,
    );
    
    // Apply any demo category preferences if user showed interest
    Map<String, int> enhancedCategories = Map.from(newUser.categories);
    if (conversionData.containsKey('demoCategories')) {
      final demoCategories = conversionData['demoCategories'] as Map<String, int>? ?? {};
      // Give small starting bonus based on demo engagement
      demoCategories.forEach((category, _) {
        enhancedCategories[category] = (enhancedCategories[category] ?? 0) + 5;
      });
    }
    
    return newUser.copyWith(
      categories: enhancedCategories,
      exp: newUser.exp + 10, // Welcome bonus for converting from guest
      onboardingProgress: enhancedOnboarding,
      lastModified: now,
    );
  }

  /// Get conversion incentives based on guest engagement
  ConversionIncentives getConversionIncentives() {
    final guestUser = _guestService.currentGuestUser;
    if (guestUser == null) {
      return ConversionIncentives.defaultIncentives();
    }
    
    final sessionDuration = _guestService.getSessionDuration();
    final featuresViewed = guestUser.viewedFeatures.length;
    
    // Determine incentives based on engagement
    List<String> incentives = [];
    int bonusExp = 10; // Base bonus
    
    if (sessionDuration.inMinutes >= 5) {
      incentives.add('🎯 Skip tutorial - you already know how it works!');
      bonusExp += 5;
    }
    
    if (guestUser.hasSeenCoachDemo) {
      incentives.add('🤖 Instant access to AI coaches');
      bonusExp += 10;
    }
    
    if (featuresViewed >= 3) {
      incentives.add('⚡ Explorer bonus: +$bonusExp EXP to start');
      bonusExp += 5;
    }
    
    if (guestUser.viewedFeatures.contains('music_player')) {
      incentives.add('🎵 Your music preferences saved');
    }
    
    // Always include core benefits
    incentives.addAll([
      '💾 All progress automatically saved',
      '☁️ Sync across all your devices',
      '🏆 Unlock achievements and rewards',
    ]);
    
    return ConversionIncentives(
      incentives: incentives,
      bonusExp: bonusExp,
      personalizedMessage: _getPersonalizedMessage(guestUser, sessionDuration),
    );
  }

  /// Get personalized conversion message
  String _getPersonalizedMessage(GuestUser guestUser, Duration sessionDuration) {
    if (sessionDuration.inMinutes >= 10) {
      return "You've been exploring for ${sessionDuration.inMinutes} minutes - you're clearly interested! Let's make it official.";
    } else if (guestUser.hasSeenCoachDemo) {
      return "Ready to have real conversations with your AI coaches? Create your account now!";
    } else if (guestUser.viewedFeatures.length >= 3) {
      return "You've seen what we offer - now unlock the full experience!";
    } else {
      return "Join thousands of users already maxing out their lives!";
    }
  }

  /// Check if guest is ready for conversion prompt
  bool isReadyForConversion() {
    if (!_guestService.isGuestSession) return false;
    
    final guestUser = _guestService.currentGuestUser!;
    final sessionDuration = _guestService.getSessionDuration();
    
    // Ready if user has been engaged for 3+ minutes and viewed multiple features
    return sessionDuration.inMinutes >= 3 && guestUser.viewedFeatures.length >= 2;
  }

  /// Get conversion analytics for tracking
  Map<String, dynamic> getConversionAnalytics() {
    return _guestService.getSessionAnalytics();
  }
}

/// Result of guest-to-user conversion
class ConversionResult {
  final bool isSuccess;
  final User? user;
  final String message;
  final Map<String, dynamic>? conversionData;

  const ConversionResult({
    required this.isSuccess,
    this.user,
    required this.message,
    this.conversionData,
  });

  factory ConversionResult.success({
    required User user,
    required String message,
    Map<String, dynamic>? conversionData,
  }) {
    return ConversionResult(
      isSuccess: true,
      user: user,
      message: message,
      conversionData: conversionData,
    );
  }

  factory ConversionResult.failure(String message) {
    return ConversionResult(
      isSuccess: false,
      message: message,
    );
  }
}

/// Incentives offered to convert guest to user
class ConversionIncentives {
  final List<String> incentives;
  final int bonusExp;
  final String personalizedMessage;

  const ConversionIncentives({
    required this.incentives,
    required this.bonusExp,
    required this.personalizedMessage,
  });

  factory ConversionIncentives.defaultIncentives() {
    return const ConversionIncentives(
      incentives: [
        '🤖 Chat with AI coaches',
        '📊 Track your progress',
        '💾 Save all your data',
        '☁️ Sync across devices',
      ],
      bonusExp: 10,
      personalizedMessage: 'Join thousands of users already maxing out their lives!',
    );
  }
}
