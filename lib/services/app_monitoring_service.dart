// lib/services/app_monitoring_service.dart

import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
// import 'package:firebase_crashlytics/firebase_crashlytics.dart';
// import 'package:firebase_analytics/firebase_analytics.dart';

/// Comprehensive app monitoring and analytics service
/// 
/// Handles crash reporting, performance monitoring, user analytics,
/// and app health tracking for production deployments
class AppMonitoringService {
  static const FlutterSecureStorage _storage = FlutterSecureStorage();
  static const String _analyticsKey = 'app_analytics';
  static const String _performanceKey = 'performance_metrics';
  static const String _crashLogKey = 'crash_log';
  
  // static FirebaseCrashlytics? _crashlytics;
  // static FirebaseAnalytics? _analytics;
  static bool _isInitialized = false;

  /// Initialize monitoring services
  static Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      if (kDebugMode) {
        print('📊 Initializing app monitoring...');
      }
      
      // Initialize Firebase services (commented out for now)
      // await _initializeFirebase();
      
      // Initialize local monitoring
      await _initializeLocalMonitoring();
      
      // Set up error handling
      _setupErrorHandling();
      
      _isInitialized = true;
      
      if (kDebugMode) {
        print('✅ App monitoring initialized');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to initialize monitoring: $e');
      }
      // Don't let monitoring failure crash the app
    }
  }

  /// Initialize Firebase services (when ready to use)
  // static Future<void> _initializeFirebase() async {
  //   try {
  //     _crashlytics = FirebaseCrashlytics.instance;
  //     _analytics = FirebaseAnalytics.instance;
      
  //     // Enable crash collection in release mode
  //     if (!kDebugMode) {
  //       await _crashlytics!.setCrashlyticsCollectionEnabled(true);
  //     }
      
  //     if (kDebugMode) {
  //       print('✅ Firebase monitoring initialized');
  //     }
  //   } catch (e) {
  //     if (kDebugMode) {
  //       print('⚠️ Firebase initialization failed: $e');
  //     }
  //   }
  // }

  /// Initialize local monitoring systems
  static Future<void> _initializeLocalMonitoring() async {
    try {
      // Initialize analytics storage
      final analytics = await _getAnalytics();
      if (analytics.isEmpty) {
        await _storage.write(
          key: _analyticsKey,
          value: jsonEncode({
            'appLaunches': 0,
            'crashCount': 0,
            'errorCount': 0,
            'userSessions': 0,
            'averageSessionDuration': 0,
            'lastLaunch': DateTime.now().toIso8601String(),
            'version': '1.0.0',
            'platform': Platform.operatingSystem,
          }),
        );
      }
      
      // Initialize performance metrics
      final performance = await _getPerformanceMetrics();
      if (performance.isEmpty) {
        await _storage.write(
          key: _performanceKey,
          value: jsonEncode({
            'appStartupTime': 0,
            'averageResponseTime': 0,
            'memoryUsage': 0,
            'networkRequests': 0,
            'offlineInteractions': 0,
            'lastUpdated': DateTime.now().toIso8601String(),
          }),
        );
      }
      
      // Track app launch
      await trackAppLaunch();
      
      if (kDebugMode) {
        print('✅ Local monitoring initialized');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Local monitoring initialization failed: $e');
      }
    }
  }

  /// Set up global error handling
  static void _setupErrorHandling() {
    // Catch Flutter framework errors
    FlutterError.onError = (FlutterErrorDetails details) {
      if (kDebugMode) {
        FlutterError.presentError(details);
      }
      
      // Log error locally
      _logError(
        'Flutter Framework Error',
        details.exception.toString(),
        details.stack?.toString(),
      );
      
      // Report to Firebase (when enabled)
      // _crashlytics?.recordFlutterFatalError(details);
    };

    // Catch async errors
    PlatformDispatcher.instance.onError = (error, stack) {
      if (kDebugMode) {
        print('Async error: $error\n$stack');
      }
      
      _logError(
        'Async Error',
        error.toString(),
        stack.toString(),
      );
      
      // Report to Firebase (when enabled)
      // _crashlytics?.recordError(error, stack, fatal: false);
      
      return true;
    };
  }

  /// Track app launch
  static Future<void> trackAppLaunch() async {
    try {
      final analytics = await _getAnalytics();
      analytics['appLaunches'] = (analytics['appLaunches'] ?? 0) + 1;
      analytics['lastLaunch'] = DateTime.now().toIso8601String();
      
      await _storage.write(key: _analyticsKey, value: jsonEncode(analytics));
      
      // Track with Firebase (when enabled)
      // await _analytics?.logAppOpen();
      
      if (kDebugMode) {
        print('📱 App launch tracked: ${analytics['appLaunches']} total launches');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to track app launch: $e');
      }
    }
  }

  /// Track user session start
  static Future<void> trackSessionStart(String username) async {
    try {
      final analytics = await _getAnalytics();
      analytics['userSessions'] = (analytics['userSessions'] ?? 0) + 1;
      analytics['lastSessionStart'] = DateTime.now().toIso8601String();
      analytics['currentUser'] = username;
      
      await _storage.write(key: _analyticsKey, value: jsonEncode(analytics));
      
      // Track with Firebase (when enabled)
      // await _analytics?.logLogin(loginMethod: 'username');
      // await _crashlytics?.setUserIdentifier(username);
      
      if (kDebugMode) {
        print('👤 Session started for: $username');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to track session start: $e');
      }
    }
  }

  /// Track feature usage
  static Future<void> trackFeatureUsage(String feature, Map<String, dynamic>? parameters) async {
    try {
      final analytics = await _getAnalytics();
      final featureKey = 'feature_$feature';
      analytics[featureKey] = (analytics[featureKey] ?? 0) + 1;
      analytics['lastFeatureUsed'] = feature;
      analytics['lastFeatureTime'] = DateTime.now().toIso8601String();
      
      await _storage.write(key: _analyticsKey, value: jsonEncode(analytics));
      
      // Track with Firebase (when enabled)
      // await _analytics?.logEvent(
      //   name: 'feature_used',
      //   parameters: {
      //     'feature_name': feature,
      //     ...?parameters,
      //   },
      // );
      
      if (kDebugMode) {
        print('🎯 Feature used: $feature');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to track feature usage: $e');
      }
    }
  }

  /// Track performance metrics
  static Future<void> trackPerformance({
    int? startupTime,
    int? responseTime,
    int? memoryUsage,
    bool? networkRequest,
    bool? offlineInteraction,
  }) async {
    try {
      final performance = await _getPerformanceMetrics();
      
      if (startupTime != null) {
        performance['appStartupTime'] = startupTime;
      }
      
      if (responseTime != null) {
        final currentAvg = performance['averageResponseTime'] ?? 0;
        final count = performance['responseTimeCount'] ?? 0;
        performance['averageResponseTime'] = ((currentAvg * count) + responseTime) / (count + 1);
        performance['responseTimeCount'] = count + 1;
      }
      
      if (memoryUsage != null) {
        performance['memoryUsage'] = memoryUsage;
      }
      
      if (networkRequest == true) {
        performance['networkRequests'] = (performance['networkRequests'] ?? 0) + 1;
      }
      
      if (offlineInteraction == true) {
        performance['offlineInteractions'] = (performance['offlineInteractions'] ?? 0) + 1;
      }
      
      performance['lastUpdated'] = DateTime.now().toIso8601String();
      
      await _storage.write(key: _performanceKey, value: jsonEncode(performance));
      
      if (kDebugMode && startupTime != null) {
        print('⚡ Startup time: ${startupTime}ms');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to track performance: $e');
      }
    }
  }

  /// Log error for monitoring
  static Future<void> _logError(String type, String message, String? stackTrace) async {
    try {
      final analytics = await _getAnalytics();
      analytics['errorCount'] = (analytics['errorCount'] ?? 0) + 1;
      analytics['lastError'] = DateTime.now().toIso8601String();
      
      await _storage.write(key: _analyticsKey, value: jsonEncode(analytics));
      
      // Store detailed error log
      final errorLog = await _getCrashLog();
      errorLog.add({
        'timestamp': DateTime.now().toIso8601String(),
        'type': type,
        'message': message,
        'stackTrace': stackTrace,
        'platform': Platform.operatingSystem,
        'version': '1.0.0',
      });
      
      // Keep only last 50 errors
      if (errorLog.length > 50) {
        errorLog.removeRange(0, errorLog.length - 50);
      }
      
      await _storage.write(key: _crashLogKey, value: jsonEncode(errorLog));
      
      if (kDebugMode) {
        print('🐛 Error logged: $type - $message');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to log error: $e');
      }
    }
  }

  /// Track AI coach interaction
  static Future<void> trackCoachInteraction({
    required String category,
    required String coachName,
    required bool transcriptEnhanced,
    required bool offlineMode,
    int? responseTime,
  }) async {
    try {
      await trackFeatureUsage('coach_interaction', {
        'category': category,
        'coach_name': coachName,
        'transcript_enhanced': transcriptEnhanced,
        'offline_mode': offlineMode,
        'response_time': responseTime,
      });
      
      if (responseTime != null) {
        await trackPerformance(responseTime: responseTime);
      }
      
      if (offlineMode) {
        await trackPerformance(offlineInteraction: true);
      } else {
        await trackPerformance(networkRequest: true);
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to track coach interaction: $e');
      }
    }
  }

  /// Track check-in notification
  static Future<void> trackCheckinNotification({
    required String coachName,
    required String category,
    required bool delivered,
    required bool opened,
  }) async {
    try {
      await trackFeatureUsage('checkin_notification', {
        'coach_name': coachName,
        'category': category,
        'delivered': delivered,
        'opened': opened,
      });
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to track check-in notification: $e');
      }
    }
  }

  /// Get analytics data
  static Future<Map<String, dynamic>> _getAnalytics() async {
    try {
      final data = await _storage.read(key: _analyticsKey);
      if (data != null) {
        return Map<String, dynamic>.from(jsonDecode(data));
      }
    } catch (e) {
      if (kDebugMode) print('❌ Failed to read analytics: $e');
    }
    return {};
  }

  /// Get performance metrics
  static Future<Map<String, dynamic>> _getPerformanceMetrics() async {
    try {
      final data = await _storage.read(key: _performanceKey);
      if (data != null) {
        return Map<String, dynamic>.from(jsonDecode(data));
      }
    } catch (e) {
      if (kDebugMode) print('❌ Failed to read performance metrics: $e');
    }
    return {};
  }

  /// Get crash log
  static Future<List<Map<String, dynamic>>> _getCrashLog() async {
    try {
      final data = await _storage.read(key: _crashLogKey);
      if (data != null) {
        return List<Map<String, dynamic>>.from(jsonDecode(data));
      }
    } catch (e) {
      if (kDebugMode) print('❌ Failed to read crash log: $e');
    }
    return [];
  }

  /// Get monitoring summary for debugging
  static Future<Map<String, dynamic>> getMonitoringSummary() async {
    try {
      final analytics = await _getAnalytics();
      final performance = await _getPerformanceMetrics();
      final crashLog = await _getCrashLog();
      
      return {
        'isInitialized': _isInitialized,
        'analytics': analytics,
        'performance': performance,
        'recentCrashes': crashLog.take(5).toList(),
        'totalCrashes': crashLog.length,
        'healthScore': _calculateHealthScore(analytics, performance, crashLog),
      };
    } catch (e) {
      return {
        'error': 'Failed to get monitoring summary: $e',
        'isInitialized': _isInitialized,
      };
    }
  }

  /// Calculate app health score (0-100)
  static int _calculateHealthScore(
    Map<String, dynamic> analytics,
    Map<String, dynamic> performance,
    List<Map<String, dynamic>> crashLog,
  ) {
    int score = 100;
    
    // Deduct for crashes
    final crashCount = (analytics['crashCount'] ?? 0) as int;
    score -= (crashCount * 10).clamp(0, 50);

    // Deduct for errors
    final errorCount = (analytics['errorCount'] ?? 0) as int;
    score -= (errorCount * 2).clamp(0, 30);
    
    // Deduct for poor performance
    final avgResponseTime = performance['averageResponseTime'] ?? 0;
    if (avgResponseTime > 5000) { // 5 seconds
      score -= 20;
    } else if (avgResponseTime > 2000) { // 2 seconds
      score -= 10;
    }
    
    // Deduct for recent crashes
    final recentCrashes = crashLog.where((crash) {
      final timestamp = DateTime.tryParse(crash['timestamp'] ?? '');
      if (timestamp == null) return false;
      return DateTime.now().difference(timestamp).inDays < 7;
    }).length;
    
    score -= (recentCrashes * 15).clamp(0, 30);

    return score.clamp(0, 100);
  }

  /// Clear monitoring data (for testing)
  static Future<void> clearMonitoringData() async {
    try {
      await _storage.delete(key: _analyticsKey);
      await _storage.delete(key: _performanceKey);
      await _storage.delete(key: _crashLogKey);
      
      if (kDebugMode) {
        print('🗑️ Monitoring data cleared');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to clear monitoring data: $e');
      }
    }
  }
}
