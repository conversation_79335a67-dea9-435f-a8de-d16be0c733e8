// lib/services/comprehensive_logging_service.dart

import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:path_provider/path_provider.dart';

/// Comprehensive logging service for debugging and monitoring
/// 
/// Features:
/// - Structured logging with levels
/// - Persistent log storage
/// - Performance tracking
/// - Error correlation
/// - Debug session management
class ComprehensiveLoggingService {
  static final ComprehensiveLoggingService _instance = ComprehensiveLoggingService._internal();
  factory ComprehensiveLoggingService() => _instance;
  ComprehensiveLoggingService._internal();

  static const FlutterSecureStorage _storage = FlutterSecureStorage();
  static const String _logKey = 'comprehensive_logs';
  static const int _maxLogEntries = 1000;
  
  static final List<LogEntry> _memoryLogs = [];
  static bool _isInitialized = false;

  /// Initialize the logging service
  static Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      // Load existing logs from storage
      await _loadLogsFromStorage();
      _isInitialized = true;
      
      if (kDebugMode) {
        print('📝 Comprehensive logging service initialized');
      }
    } catch (e) {
      if (kDebugMode) print('⚠️ Failed to initialize logging service: $e');
    }
  }

  /// Log an info message
  static Future<void> logInfo(String message, {
    String? category,
    Map<String, dynamic>? metadata,
  }) async {
    await _log(LogLevel.info, message, category: category, metadata: metadata);
  }

  /// Log a warning message
  static Future<void> logWarning(String message, {
    String? category,
    Map<String, dynamic>? metadata,
  }) async {
    await _log(LogLevel.warning, message, category: category, metadata: metadata);
  }

  /// Log an error message
  static Future<void> logError(String message, {
    String? category,
    Map<String, dynamic>? metadata,
    dynamic error,
    StackTrace? stackTrace,
  }) async {
    final errorMetadata = <String, dynamic>{
      ...?metadata,
      if (error != null) 'error': error.toString(),
      if (stackTrace != null) 'stackTrace': stackTrace.toString(),
    };
    
    await _log(LogLevel.error, message, category: category, metadata: errorMetadata);
  }

  /// Log a debug message (only in debug mode)
  static Future<void> logDebug(String message, {
    String? category,
    Map<String, dynamic>? metadata,
  }) async {
    if (kDebugMode) {
      await _log(LogLevel.debug, message, category: category, metadata: metadata);
    }
  }

  /// Log performance metrics
  static Future<void> logPerformance(String operation, int durationMs, {
    Map<String, dynamic>? metadata,
  }) async {
    final perfMetadata = <String, dynamic>{
      'operation': operation,
      'duration_ms': durationMs,
      'performance_category': 'timing',
      ...?metadata,
    };
    
    await _log(LogLevel.performance, 'Performance: $operation took ${durationMs}ms', 
        category: 'performance', metadata: perfMetadata);
  }

  /// Start a debug session
  static Future<String> startDebugSession(String sessionName) async {
    final sessionId = 'debug_${DateTime.now().millisecondsSinceEpoch}';
    
    await logInfo('Debug session started: $sessionName', 
        category: 'debug_session', 
        metadata: {
          'session_id': sessionId,
          'session_name': sessionName,
          'start_time': DateTime.now().toIso8601String(),
        });
    
    return sessionId;
  }

  /// End a debug session
  static Future<void> endDebugSession(String sessionId, {
    Map<String, dynamic>? summary,
  }) async {
    await logInfo('Debug session ended', 
        category: 'debug_session', 
        metadata: {
          'session_id': sessionId,
          'end_time': DateTime.now().toIso8601String(),
          'summary': summary,
        });
  }

  /// Get logs for a specific category
  static List<LogEntry> getLogsByCategory(String category) {
    return _memoryLogs.where((log) => log.category == category).toList();
  }

  /// Get logs by level
  static List<LogEntry> getLogsByLevel(LogLevel level) {
    return _memoryLogs.where((log) => log.level == level).toList();
  }

  /// Get recent logs (last N entries)
  static List<LogEntry> getRecentLogs(int count) {
    final logs = List<LogEntry>.from(_memoryLogs);
    logs.sort((a, b) => b.timestamp.compareTo(a.timestamp));
    return logs.take(count).toList();
  }

  /// Export logs to file
  static Future<String> exportLogsToFile() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final timestamp = DateTime.now().toIso8601String().replaceAll(':', '-');
      final fileName = 'mxd_logs_$timestamp.json';
      final file = File('${directory.path}/$fileName');
      
      final logsJson = _memoryLogs.map((log) => log.toJson()).toList();
      await file.writeAsString(jsonEncode(logsJson));
      
      if (kDebugMode) {
        print('📄 Logs exported to: ${file.path}');
      }
      
      return file.path;
    } catch (e) {
      if (kDebugMode) print('❌ Failed to export logs: $e');
      rethrow;
    }
  }

  /// Clear all logs
  static Future<void> clearLogs() async {
    _memoryLogs.clear();
    await _saveLogsToStorage();
    
    if (kDebugMode) {
      print('🗑️ All logs cleared');
    }
  }

  /// Get logging statistics
  static Map<String, dynamic> getLoggingStatistics() {
    final stats = <String, int>{};
    
    for (final log in _memoryLogs) {
      final key = log.level.name;
      stats[key] = (stats[key] ?? 0) + 1;
    }
    
    return {
      'total_logs': _memoryLogs.length,
      'by_level': stats,
      'oldest_log': _memoryLogs.isNotEmpty 
          ? _memoryLogs.map((l) => l.timestamp).reduce((a, b) => a.isBefore(b) ? a : b).toIso8601String()
          : null,
      'newest_log': _memoryLogs.isNotEmpty 
          ? _memoryLogs.map((l) => l.timestamp).reduce((a, b) => a.isAfter(b) ? a : b).toIso8601String()
          : null,
    };
  }

  /// Internal logging method
  static Future<void> _log(LogLevel level, String message, {
    String? category,
    Map<String, dynamic>? metadata,
  }) async {
    final entry = LogEntry(
      level: level,
      message: message,
      category: category ?? 'general',
      timestamp: DateTime.now(),
      metadata: metadata,
    );
    
    _memoryLogs.add(entry);
    
    // Print to console in debug mode
    if (kDebugMode) {
      final levelIcon = _getLevelIcon(level);
      final categoryText = category != null ? '[$category] ' : '';
      print('$levelIcon $categoryText$message');
    }
    
    // Trim logs if too many
    if (_memoryLogs.length > _maxLogEntries) {
      _memoryLogs.removeRange(0, _memoryLogs.length - _maxLogEntries);
    }
    
    // Save to storage periodically
    if (_memoryLogs.length % 50 == 0) {
      await _saveLogsToStorage();
    }
  }

  /// Load logs from secure storage
  static Future<void> _loadLogsFromStorage() async {
    try {
      final logsJson = await _storage.read(key: _logKey);
      if (logsJson != null) {
        final logsList = jsonDecode(logsJson) as List;
        _memoryLogs.clear();
        _memoryLogs.addAll(logsList.map((json) => LogEntry.fromJson(json)));
      }
    } catch (e) {
      if (kDebugMode) print('⚠️ Failed to load logs from storage: $e');
    }
  }

  /// Save logs to secure storage
  static Future<void> _saveLogsToStorage() async {
    try {
      final logsJson = jsonEncode(_memoryLogs.map((log) => log.toJson()).toList());
      await _storage.write(key: _logKey, value: logsJson);
    } catch (e) {
      if (kDebugMode) print('⚠️ Failed to save logs to storage: $e');
    }
  }

  /// Get icon for log level
  static String _getLevelIcon(LogLevel level) {
    switch (level) {
      case LogLevel.debug:
        return '🐛';
      case LogLevel.info:
        return 'ℹ️';
      case LogLevel.warning:
        return '⚠️';
      case LogLevel.error:
        return '❌';
      case LogLevel.performance:
        return '⚡';
    }
  }
}

/// Log entry model
class LogEntry {
  final LogLevel level;
  final String message;
  final String category;
  final DateTime timestamp;
  final Map<String, dynamic>? metadata;

  LogEntry({
    required this.level,
    required this.message,
    required this.category,
    required this.timestamp,
    this.metadata,
  });

  Map<String, dynamic> toJson() {
    return {
      'level': level.name,
      'message': message,
      'category': category,
      'timestamp': timestamp.toIso8601String(),
      'metadata': metadata,
    };
  }

  factory LogEntry.fromJson(Map<String, dynamic> json) {
    return LogEntry(
      level: LogLevel.values.firstWhere((e) => e.name == json['level']),
      message: json['message'],
      category: json['category'],
      timestamp: DateTime.parse(json['timestamp']),
      metadata: json['metadata'],
    );
  }
}

/// Log levels
enum LogLevel {
  debug,
  info,
  warning,
  error,
  performance,
}
