// lib/services/training_timer_service.dart

import 'dart:async';
import 'package:flutter/foundation.dart';
import '../models/training_timer_model.dart';
import '../bulletproof/error_handler.dart';
import '../services/comprehensive_logging_service.dart';

/// Service for managing training timer functionality with real-time updates
class TrainingTimerService extends ChangeNotifier {
  static final TrainingTimerService _instance = TrainingTimerService._internal();
  factory TrainingTimerService() => _instance;
  
  TrainingTimerService._internal() : _errorHandler = ErrorHandler();

  final ErrorHandler _errorHandler;
  Timer? _timer;
  Timer? _debounceTimer;
  TrainingTimerState _state = TrainingTimerState.initial();
  bool _pendingNotification = false;
  
  /// Current timer state
  TrainingTimerState get state => _state;
  
  /// Whether the timer is currently running
  bool get isRunning => _state.isRunning;
  
  /// Current elapsed time in seconds
  int get elapsedSeconds => _state.elapsedSeconds;
  
  /// Formatted time string for display
  String get formattedTime => _state.formattedTime;
  
  /// Current EXP earned
  double get currentExp => _state.currentExp;
  
  /// Formatted EXP string for display
  String get formattedExp => _state.formattedExp;

  /// Whether the timer can be reset
  bool get canReset => _state.canReset;

  /// Start the timer
  Future<void> start() async {
    try {
      await ComprehensiveLoggingService.logInfo('🏃‍♂️ TrainingTimer: Starting timer');
      
      if (_state.isRunning) {
        await ComprehensiveLoggingService.logWarning('⚠️ TrainingTimer: Timer already running');
        return;
      }

      _state = _state.start();
      _startPeriodicUpdates();
      notifyListeners();
      
      await ComprehensiveLoggingService.logInfo('✅ TrainingTimer: Timer started successfully');
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace);
      await ComprehensiveLoggingService.logError('❌ TrainingTimer: Failed to start timer: $e');
    }
  }

  /// Pause the timer
  Future<void> pause() async {
    try {
      await ComprehensiveLoggingService.logInfo('⏸️ TrainingTimer: Pausing timer');
      
      if (!_state.isRunning) {
        await ComprehensiveLoggingService.logWarning('⚠️ TrainingTimer: Timer not running');
        return;
      }

      _stopPeriodicUpdates();
      _state = _state.pause();
      notifyListeners();
      
      await ComprehensiveLoggingService.logInfo('✅ TrainingTimer: Timer paused at ${_state.formattedTime}');
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace);
      await ComprehensiveLoggingService.logError('❌ TrainingTimer: Failed to pause timer: $e');
    }
  }

  /// Reset the timer to initial state
  Future<void> reset() async {
    try {
      await ComprehensiveLoggingService.logInfo('🔄 TrainingTimer: Resetting timer');
      
      _stopPeriodicUpdates();
      _state = TrainingTimerState.initial();
      notifyListeners();
      
      await ComprehensiveLoggingService.logInfo('✅ TrainingTimer: Timer reset successfully');
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace);
      await ComprehensiveLoggingService.logError('❌ TrainingTimer: Failed to reset timer: $e');
    }
  }

  /// Toggle between start and pause
  Future<void> toggle() async {
    if (_state.isRunning) {
      await pause();
    } else {
      await start();
    }
  }

  /// Get current timer state for saving
  TrainingTimerState getCurrentState() {
    return _state.updateElapsed();
  }

  /// Start periodic updates every second
  void _startPeriodicUpdates() {
    _stopPeriodicUpdates(); // Ensure no duplicate timers

    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      try {
        final oldState = _state;
        _state = _state.updateElapsed();

        // Only notify listeners if the displayed time has actually changed
        // This prevents excessive rebuilds for sub-second updates
        if (oldState.formattedTime != _state.formattedTime) {
          _debouncedNotifyListeners();
        }

        // Log EXP updates every 30 seconds for debugging
        if (_state.elapsedSeconds % 30 == 0 && _state.elapsedSeconds > 0) {
          ComprehensiveLoggingService.logInfo(
            '📊 TrainingTimer: ${_state.formattedTime} elapsed, ${_state.formattedExp} earned'
          );
        }
      } catch (e) {
        ComprehensiveLoggingService.logError('❌ TrainingTimer: Error in periodic update: $e');
      }
    });
  }

  /// Stop periodic updates
  void _stopPeriodicUpdates() {
    _timer?.cancel();
    _timer = null;
    _debounceTimer?.cancel();
    _debounceTimer = null;
    _pendingNotification = false;
  }

  /// Debounced notification to prevent excessive rebuilds
  void _debouncedNotifyListeners() {
    if (_pendingNotification) return;

    _pendingNotification = true;
    _debounceTimer?.cancel();
    _debounceTimer = Timer(const Duration(milliseconds: 100), () {
      if (_pendingNotification) {
        notifyListeners();
        _pendingNotification = false;
      }
    });
  }

  /// Calculate final EXP for a completed session (12.5 EXP per hour with 25% boost)
  /// Base Health EXP: 10 per hour, Training Tracker boost: +25% = 12.5 per hour
  /// Per minute: 12.5 ÷ 60 = 0.208333... EXP per minute
  /// Rounded to nearest half (0, 0.5, 1.0, 1.5, 2.0, etc.)
  static double calculateFinalExp(int durationSeconds) {
    final minutes = durationSeconds / 60.0;
    final rawExp = minutes * (12.5 / 60.0); // 0.208333... EXP per minute
    return _roundToNearestHalf(rawExp);
  }

  /// Round to nearest half number (0, 0.5, 1.0, 1.5, 2.0, etc.)
  static double _roundToNearestHalf(double value) {
    return (value * 2).round() / 2.0;
  }

  /// Get timer statistics for debugging
  Map<String, dynamic> getTimerStats() {
    return {
      'isRunning': _state.isRunning,
      'hasStarted': _state.hasStarted,
      'elapsedSeconds': _state.elapsedSeconds,
      'formattedTime': _state.formattedTime,
      'currentExp': _state.currentExp,
      'canStart': _state.canStart,
      'canPause': _state.canPause,
      'canReset': _state.canReset,
    };
  }

  /// Validate timer state for debugging
  bool validateTimerState() {
    try {
      // Check for logical consistency
      if (_state.isRunning && _state.lastStartTime == null) {
        ComprehensiveLoggingService.logError('❌ TrainingTimer: Invalid state - running but no start time');
        return false;
      }
      
      if (!_state.isRunning && _state.lastStartTime != null) {
        ComprehensiveLoggingService.logError('❌ TrainingTimer: Invalid state - not running but has start time');
        return false;
      }
      
      if (_state.elapsedSeconds < 0) {
        ComprehensiveLoggingService.logError('❌ TrainingTimer: Invalid state - negative elapsed time');
        return false;
      }
      
      return true;
    } catch (e) {
      ComprehensiveLoggingService.logError('❌ TrainingTimer: Error validating state: $e');
      return false;
    }
  }

  @override
  void dispose() {
    _stopPeriodicUpdates();
    super.dispose();
  }
}
