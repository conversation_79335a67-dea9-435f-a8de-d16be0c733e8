import 'dart:math';
import 'package:flutter/foundation.dart';
import 'knowledge_nugget_service.dart';
import '../prompts/mxd_life_coaches.dart';

/// Lightweight coach service that uses knowledge nuggets instead of heavy transcripts
/// 
/// This service provides intelligent, unique coach responses using pre-processed
/// knowledge nuggets (2MB total) instead of loading 176MB of transcripts.
/// 
/// Benefits:
/// - 99% memory reduction
/// - Instant responses
/// - Coach-specific personalities
/// - Maintains intelligence without complexity
class LightweightCoachService {
  static bool _isInitialized = false;
  static final Random _random = Random();

  /// Initialize the lightweight coach service
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Initialize knowledge nuggets
      await KnowledgeNuggetService.initialize();
      _isInitialized = true;
      
      if (kDebugMode) {
        final stats = KnowledgeNuggetService.getStats();
        print('🎭 Lightweight Coach Service initialized');
        print('   📚 Knowledge nuggets: ${stats['nuggets_count']}');
        print('   💾 Memory footprint: ${stats['memory_footprint_kb']}KB');
        print('   🎯 Categories: ${stats['categories'].length}');
      }
    } catch (e) {
      if (kDebugMode) print('❌ Failed to initialize Lightweight Coach Service: $e');
    }
  }

  /// Generate coach response using knowledge nuggets
  static Future<String> generateResponse({
    required String coachName,
    required String category,
    required String userMessage,
    String? userName,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      // Search for relevant knowledge
      final relevantNuggets = KnowledgeNuggetService.search(
        query: userMessage,
        category: category,
        maxResults: 3,
      );

      // Get coach personality
      final personality = _getCoachPersonality(coachName);
      
      // Generate response
      final response = _craftResponse(
        personality: personality,
        nuggets: relevantNuggets,
        userMessage: userMessage,
        userName: userName ?? 'friend',
      );

      if (kDebugMode) {
        print('🎭 Generated response for $coachName ($category)');
        print('   📚 Used ${relevantNuggets.length} knowledge nuggets');
        print('   📝 Response length: ${response.length} chars');
      }

      return response;
    } catch (e) {
      if (kDebugMode) print('❌ Failed to generate coach response: $e');
      return _getFallbackResponse(coachName, userName ?? 'friend');
    }
  }

  /// Get coach personality traits using your real MXD coach definitions
  static CoachPersonality _getCoachPersonality(String coachName) {
    // Find the coach in the authoritative source for validation
    final coachExists = mxdLifeCoaches.any((coach) =>
      coach.maleName.toLowerCase() == coachName.toLowerCase() ||
      coach.femaleName.toLowerCase() == coachName.toLowerCase()
    );

    if (kDebugMode && !coachExists) {
      print('⚠️ Coach name "$coachName" not found in mxdLifeCoaches - using fallback personality');
    }
    switch (coachName.toLowerCase()) {
      // Health Coaches
      case 'kai-tholo':
        return CoachPersonality(
          name: 'Kai-Tholo',
          tone: 'disciplined but light-hearted',
          expertise: 'strength, conditioning, & martial arts',
          catchphrases: ['Become the ultimate version of yourself', 'Warrior mindset, playful spirit'],
          responseStyle: 'relentless strategist who breaks down complex movements into simple drills',
        );
      case 'aria':
        return CoachPersonality(
          name: 'Aria',
          tone: 'disciplined but light-hearted',
          expertise: 'strength, conditioning, & martial arts',
          catchphrases: ['Become the ultimate version of yourself', 'Warrior mindset, playful spirit'],
          responseStyle: 'relentless strategist who breaks down complex movements into simple drills',
        );

      // Wealth Coaches
      case 'sterling':
        return CoachPersonality(
          name: 'Sterling',
          tone: 'wealthy entrepreneurial uncle',
          expertise: 'business and financial freedom',
          catchphrases: ['Richer than me', 'Spot opportunities, avoid risks'],
          responseStyle: 'tough-minded sales & financial advice with high-level strategy',
        );
      case 'marion':
        return CoachPersonality(
          name: 'Marion',
          tone: 'wealthy entrepreneurial aunt',
          expertise: 'business and financial freedom',
          catchphrases: ['Richer than me', 'Spot opportunities, avoid risks'],
          responseStyle: 'tough-minded sales & financial advice with high-level strategy',
        );

      // Purpose Coaches
      case 'ves-ar':
        return CoachPersonality(
          name: 'Ves-ar',
          tone: 'wise space-faring inter-galactic wizard',
          expertise: 'North Star Quest and destiny fulfillment',
          catchphrases: ['The Chosen One', 'Your prophecy unfolds perfectly'],
          responseStyle: 'calm, deeply intuitive guide who speaks in parables',
        );
      case 'seraphina':
        return CoachPersonality(
          name: 'Seraphina',
          tone: 'wise space-faring inter-galactic wizard',
          expertise: 'North Star Quest and destiny fulfillment',
          catchphrases: ['The Chosen One', 'Your prophecy unfolds perfectly'],
          responseStyle: 'calm, deeply intuitive guide who speaks in parables',
        );

      // Connection Coaches
      case 'zen':
        return CoachPersonality(
          name: 'Zen',
          tone: 'neo-monk therapist',
          expertise: 'emotional fluency and authentic connection',
          catchphrases: ['Inner strength through outward connection', 'Vulnerability is the gateway'],
          responseStyle: 'advanced emotional fluency with accountability to authentic communication',
        );
      case 'amara':
        return CoachPersonality(
          name: 'Amara',
          tone: 'neo-monk therapist',
          expertise: 'emotional fluency and authentic connection',
          catchphrases: ['Inner strength through outward connection', 'Vulnerability is the gateway'],
          responseStyle: 'advanced emotional fluency with accountability to authentic communication',
        );

      // Custom Category Coaches
      case 'aether':
        return CoachPersonality(
          name: 'Aether',
          tone: 'immortal master with 1,000,000 hours of practice',
          expertise: 'custom category mastery',
          catchphrases: ['Greatest practitioner in the world', 'Timeless frameworks'],
          responseStyle: 'ethereal teacher who demands precision while nurturing creative spark',
        );
      case 'luna':
        return CoachPersonality(
          name: 'Luna',
          tone: 'immortal master with 1,000,000 hours of practice',
          expertise: 'custom category mastery',
          catchphrases: ['Greatest practitioner in the world', 'Timeless frameworks'],
          responseStyle: 'ethereal teacher who demands precision while nurturing creative spark',
        );
      case 'chronos':
        return CoachPersonality(
          name: 'Chronos',
          tone: 'immortal master with 1,000,000 hours of practice',
          expertise: 'custom category mastery',
          catchphrases: ['Greatest practitioner in the world', 'Timeless frameworks'],
          responseStyle: 'ethereal teacher who demands precision while nurturing creative spark',
        );
      case 'elysia':
        return CoachPersonality(
          name: 'Elysia',
          tone: 'immortal master with 1,000,000 hours of practice',
          expertise: 'custom category mastery',
          catchphrases: ['Greatest practitioner in the world', 'Timeless frameworks'],
          responseStyle: 'ethereal teacher who demands precision while nurturing creative spark',
        );

      default:
        return CoachPersonality(
          name: coachName,
          tone: 'supportive and knowledgeable',
          expertise: 'general life coaching',
          catchphrases: ['You\'ve got this', 'Progress over perfection'],
          responseStyle: 'balanced and encouraging',
        );
    }
  }

  /// Craft personalized response using knowledge nuggets
  static String _craftResponse({
    required CoachPersonality personality,
    required List<KnowledgeNugget> nuggets,
    required String userMessage,
    required String userName,
  }) {
    final buffer = StringBuffer();
    
    // Personalized greeting
    buffer.write(_getPersonalizedGreeting(personality, userName));
    buffer.write('\n\n');

    // Main response based on knowledge
    if (nuggets.isNotEmpty) {
      // Use the most relevant nugget as primary content
      final primaryNugget = nuggets.first;
      buffer.write(_adaptNuggetToPersonality(primaryNugget, personality));
      
      // Add supporting insights from other nuggets
      if (nuggets.length > 1) {
        buffer.write('\n\n');
        buffer.write(_addSupportingInsights(nuggets.skip(1).take(2).toList(), personality));
      }
    } else {
      // Fallback to personality-based response
      buffer.write(_getPersonalityBasedResponse(personality, userMessage));
    }

    // Add motivational closing
    buffer.write('\n\n');
    buffer.write(_getMotivationalClosing(personality, userName));

    return buffer.toString();
  }

  /// Get personalized greeting
  static String _getPersonalizedGreeting(CoachPersonality personality, String userName) {
    final greetings = [
      'Hey $userName!',
      'Great to hear from you, $userName!',
      '$userName, I\'m glad you reached out!',
      'Hello $userName!',
    ];
    
    return greetings[_random.nextInt(greetings.length)];
  }

  /// Adapt knowledge nugget to coach personality
  static String _adaptNuggetToPersonality(KnowledgeNugget nugget, CoachPersonality personality) {
    final buffer = StringBuffer();
    
    // Add personality-specific intro
    switch (personality.responseStyle) {
      case 'scientific yet approachable':
        buffer.write('Based on the latest research, ');
        break;
      case 'no-nonsense and action-oriented':
        buffer.write('Here\'s what you need to do: ');
        break;
      case 'thoughtful and emotionally intelligent':
        buffer.write('I understand this can be challenging. ');
        break;
      case 'deep and thought-provoking':
        buffer.write('Let\'s explore this deeper. ');
        break;
      default:
        buffer.write('Here\'s my take on this: ');
    }
    
    // Add the core knowledge
    buffer.write(nugget.content);
    
    // Add personality-specific insight
    if (personality.catchphrases.isNotEmpty) {
      final catchphrase = personality.catchphrases[_random.nextInt(personality.catchphrases.length)];
      buffer.write(' Remember: $catchphrase.');
    }
    
    return buffer.toString();
  }

  /// Add supporting insights
  static String _addSupportingInsights(List<KnowledgeNugget> nuggets, CoachPersonality personality) {
    if (nuggets.isEmpty) return '';
    
    final buffer = StringBuffer();
    buffer.write('Additionally, ');
    
    for (int i = 0; i < nuggets.length; i++) {
      if (i > 0) buffer.write(' Also, ');
      
      // Extract key insight from nugget
      final sentences = nuggets[i].content.split('. ');
      final keyInsight = sentences.isNotEmpty ? sentences.first : nuggets[i].content;
      buffer.write(keyInsight.toLowerCase());
      if (!keyInsight.endsWith('.')) buffer.write('.');
    }
    
    return buffer.toString();
  }

  /// Get personality-based response when no nuggets found
  static String _getPersonalityBasedResponse(CoachPersonality personality, String userMessage) {
    switch (personality.expertise) {
      case 'health and wellness':
        return 'Focus on the fundamentals: consistent sleep, regular movement, and proper nutrition. Your body responds to consistency more than intensity.';
      case 'wealth and business':
        return 'Success comes from executing consistently on proven strategies. Focus on one thing, do it well, then scale.';
      case 'relationships and connection':
        return 'Authentic connections are built through vulnerability and genuine interest in others. Listen more than you speak.';
      case 'purpose and meaning':
        return 'Meaning comes from taking responsibility and serving something larger than yourself. Start where you are with what you have.';
      default:
        return 'Growth happens when you consistently take action outside your comfort zone. Trust the process and stay committed.';
    }
  }

  /// Get motivational closing
  static String _getMotivationalClosing(CoachPersonality personality, String userName) {
    final closings = [
      'You\'ve got this, $userName! Take action today.',
      'I believe in you, $userName. One step at a time.',
      'Keep pushing forward, $userName. Progress over perfection.',
      'Stay strong, $userName. Your future self will thank you.',
    ];
    
    return closings[_random.nextInt(closings.length)];
  }

  /// Get fallback response for errors
  static String _getFallbackResponse(String coachName, String userName) {
    return 'Hey $userName! I\'m here to support you on your journey. While I gather my thoughts, remember that every small step forward counts. What specific area would you like to focus on today?';
  }

  /// Check if service is ready
  static bool get isReady => _isInitialized;

  /// Get service statistics
  static Map<String, dynamic> getStats() {
    return {
      'is_initialized': _isInitialized,
      'knowledge_stats': KnowledgeNuggetService.getStats(),
    };
  }
}

/// Coach personality data class
class CoachPersonality {
  final String name;
  final String tone;
  final String expertise;
  final List<String> catchphrases;
  final String responseStyle;

  const CoachPersonality({
    required this.name,
    required this.tone,
    required this.expertise,
    required this.catchphrases,
    required this.responseStyle,
  });
}
