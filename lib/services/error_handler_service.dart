// lib/services/error_handler_service.dart

import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:http/http.dart' as http;

/// Comprehensive error handling service for AI operations and network failures
class ErrorHandlerService {
  static const FlutterSecureStorage _storage = FlutterSecureStorage();
  static const String _errorLogKey = 'error_log';
  static const int _maxRetries = 3;
  static const Duration _baseRetryDelay = Duration(seconds: 2);

  /// Execute an operation with comprehensive error handling and retry logic
  static Future<T> executeWithRetry<T>({
    required Future<T> Function() operation,
    required T Function() fallback,
    String operationName = 'Unknown Operation',
    int maxRetries = _maxRetries,
    Duration baseDelay = _baseRetryDelay,
    bool logErrors = true,
  }) async {
    int attempts = 0;
    Exception? lastException;

    while (attempts < maxRetries) {
      try {
        attempts++;
        final result = await operation();
        
        // Log successful recovery if this wasn't the first attempt
        if (attempts > 1 && logErrors) {
          await _logError(
            'SUCCESS_AFTER_RETRY',
            '$operationName succeeded on attempt $attempts',
            null,
          );
        }
        
        return result;
      } catch (e) {
        lastException = e is Exception ? e : Exception(e.toString());
        
        if (logErrors) {
          await _logError(
            operationName,
            'Attempt $attempts failed: ${e.toString()}',
            lastException,
          );
        }

        // Don't retry on certain types of errors
        if (_isNonRetryableError(e)) {
          if (kDebugMode) {
            print('❌ Non-retryable error in $operationName: $e');
          }
          break;
        }

        // Wait before retrying (exponential backoff)
        if (attempts < maxRetries) {
          final delay = Duration(
            milliseconds: (baseDelay.inMilliseconds * (attempts * attempts)).clamp(
              baseDelay.inMilliseconds,
              30000, // Max 30 seconds
            ),
          );
          
          if (kDebugMode) {
            print('⏳ Retrying $operationName in ${delay.inSeconds}s (attempt ${attempts + 1}/$maxRetries)');
          }
          
          await Future.delayed(delay);
        }
      }
    }

    // All retries failed, use fallback
    if (logErrors) {
      await _logError(
        operationName,
        'All $maxRetries attempts failed, using fallback',
        lastException,
      );
    }

    if (kDebugMode) {
      print('🔄 Using fallback for $operationName after $attempts failed attempts');
    }

    return fallback();
  }

  /// Check if an error should not be retried
  static bool _isNonRetryableError(dynamic error) {
    final errorString = error.toString().toLowerCase();
    
    // API key issues
    if (errorString.contains('unauthorized') || 
        errorString.contains('invalid api key') ||
        errorString.contains('authentication')) {
      return true;
    }
    
    // Rate limiting (should be retried, but with longer delays)
    if (errorString.contains('rate limit') || 
        errorString.contains('too many requests')) {
      return false; // Allow retry with backoff
    }
    
    // Client errors (4xx) - usually don't retry
    if (error is HttpException) {
      final statusCode = _extractStatusCode(errorString);
      if (statusCode != null && statusCode >= 400 && statusCode < 500) {
        return statusCode != 429; // 429 is rate limit, should retry
      }
    }
    
    // Network errors - should retry
    if (errorString.contains('network') ||
        errorString.contains('connection') ||
        errorString.contains('timeout') ||
        errorString.contains('socket')) {
      return false;
    }
    
    return false; // Default to retryable
  }

  /// Extract HTTP status code from error message
  static int? _extractStatusCode(String errorString) {
    final regex = RegExp(r'(\d{3})');
    final match = regex.firstMatch(errorString);
    if (match != null) {
      return int.tryParse(match.group(1)!);
    }
    return null;
  }

  /// Get user-friendly error message
  static String getUserFriendlyMessage(dynamic error, String operationContext) {
    final errorString = error.toString().toLowerCase();
    
    // Network issues
    if (errorString.contains('network') || 
        errorString.contains('connection') ||
        errorString.contains('internet')) {
      return 'Please check your internet connection and try again.';
    }
    
    // API key issues
    if (errorString.contains('unauthorized') || 
        errorString.contains('invalid api key')) {
      return 'There\'s a configuration issue. Please contact support.';
    }
    
    // Rate limiting
    if (errorString.contains('rate limit') || 
        errorString.contains('too many requests')) {
      return 'Our AI is busy right now. Please try again in a moment.';
    }
    
    // Server errors
    if (errorString.contains('server error') || 
        errorString.contains('internal error') ||
        errorString.contains('503') ||
        errorString.contains('502') ||
        errorString.contains('500')) {
      return 'Our AI service is temporarily unavailable. Please try again shortly.';
    }
    
    // Timeout
    if (errorString.contains('timeout')) {
      return 'The request took too long. Please try again.';
    }
    
    // Storage errors
    if (errorString.contains('storage') || 
        errorString.contains('permission denied')) {
      return 'Unable to save data. Please check app permissions.';
    }
    
    // Context-specific messages
    switch (operationContext.toLowerCase()) {
      case 'coach_response':
        return 'Your coach is having trouble responding right now. Please try again.';
      case 'transcript_search':
        return 'Unable to access coach knowledge. Using basic responses.';
      case 'check_in':
        return 'Unable to send check-in notification. Will try again later.';
      case 'authentication':
        return 'Sign in failed. Please check your credentials and try again.';
      default:
        return 'Something went wrong. Please try again.';
    }
  }

  /// Log error for debugging and analytics
  static Future<void> _logError(
    String operation,
    String message,
    Exception? exception,
  ) async {
    try {
      final errorEntry = {
        'timestamp': DateTime.now().toIso8601String(),
        'operation': operation,
        'message': message,
        'exception': exception?.toString(),
        'stackTrace': exception != null ? StackTrace.current.toString() : null,
      };

      // Get existing error log
      final existingLog = await _getErrorLog();
      existingLog.add(errorEntry);

      // Keep only last 100 errors to prevent storage bloat
      if (existingLog.length > 100) {
        existingLog.removeRange(0, existingLog.length - 100);
      }

      // Save updated log
      await _storage.write(
        key: _errorLogKey,
        value: existingLog.map((e) => e.toString()).join('\n'),
      );

      if (kDebugMode) {
        print('📝 Logged error: $operation - $message');
      }
    } catch (e) {
      // Don't let error logging crash the app
      if (kDebugMode) {
        print('❌ Failed to log error: $e');
      }
    }
  }

  /// Get error log for debugging
  static Future<List<Map<String, dynamic>>> _getErrorLog() async {
    try {
      final logData = await _storage.read(key: _errorLogKey);
      if (logData != null) {
        // Simple parsing - in production you'd use proper JSON
        return logData.split('\n')
            .where((line) => line.isNotEmpty)
            .map((line) => {'raw': line})
            .toList();
      }
    } catch (e) {
      if (kDebugMode) print('❌ Failed to read error log: $e');
    }
    return [];
  }

  /// Check network connectivity with multiple fallback methods
  static Future<bool> isNetworkAvailable() async {
    try {
      // Method 1: Try multiple reliable hosts
      final hosts = ['google.com', '*******', 'cloudflare.com', '*******'];

      for (final host in hosts) {
        try {
          final result = await InternetAddress.lookup(host)
              .timeout(const Duration(seconds: 5));
          if (result.isNotEmpty && result[0].rawAddress.isNotEmpty) {
            if (kDebugMode) print('✅ Network connectivity confirmed via $host');
            return true;
          }
        } catch (e) {
          if (kDebugMode) print('⚠️ Failed to reach $host: $e');
          continue;
        }
      }

      // Method 2: Try HTTP connectivity test as fallback
      try {
        final response = await http.get(
          Uri.parse('https://www.google.com'),
          headers: {'User-Agent': 'MXD-App-Health-Check'},
        ).timeout(const Duration(seconds: 10));

        if (response.statusCode == 200) {
          if (kDebugMode) print('✅ Network connectivity confirmed via HTTP');
          return true;
        }
      } catch (e) {
        if (kDebugMode) print('⚠️ HTTP connectivity test failed: $e');
      }

      if (kDebugMode) print('❌ All network connectivity tests failed');
      return false;
    } catch (e) {
      if (kDebugMode) print('❌ Network connectivity check error: $e');
      return false;
    }
  }

  /// Validate API configuration
  static Future<bool> validateApiConfiguration() async {
    try {
      // Check if API key exists and has basic format (use dotenv like all other services)
      final apiKey = dotenv.env['OPENAI_API_KEY'];

      if (apiKey == null || apiKey.isEmpty) {
        if (kDebugMode) print('❌ OpenAI API key not found in environment');
        return false;
      }

      // Basic format validation (OpenAI keys start with 'sk-')
      if (!apiKey.startsWith('sk-')) {
        if (kDebugMode) print('❌ OpenAI API key has invalid format');
        return false;
      }

      if (apiKey.length < 20) {
        if (kDebugMode) print('❌ OpenAI API key too short');
        return false;
      }

      return true;
    } catch (e) {
      if (kDebugMode) print('❌ API key validation failed: $e');
      return false;
    }
  }

  /// Clear error log (for testing or privacy)
  static Future<void> clearErrorLog() async {
    try {
      await _storage.delete(key: _errorLogKey);
      if (kDebugMode) print('🗑️ Error log cleared');
    } catch (e) {
      if (kDebugMode) print('❌ Failed to clear error log: $e');
    }
  }

  /// Get error statistics for monitoring
  static Future<Map<String, dynamic>> getErrorStatistics() async {
    try {
      final errorLog = await _getErrorLog();
      final now = DateTime.now();
      final last24Hours = now.subtract(const Duration(hours: 24));
      
      final recentErrors = errorLog.where((error) {
        // Simple timestamp parsing - improve in production
        return error['raw'].toString().contains(last24Hours.year.toString());
      }).toList();

      return {
        'totalErrors': errorLog.length,
        'recentErrors': recentErrors.length,
        'lastErrorTime': errorLog.isNotEmpty ? errorLog.last['raw'] : null,
        'networkAvailable': await isNetworkAvailable(),
        'apiConfigValid': await validateApiConfiguration(),
      };
    } catch (e) {
      return {
        'error': 'Failed to get statistics: $e',
        'networkAvailable': false,
        'apiConfigValid': false,
      };
    }
  }
}
