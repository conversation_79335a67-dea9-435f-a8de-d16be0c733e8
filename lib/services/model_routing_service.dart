// 📁 lib/services/model_routing_service.dart

import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../models/user_model.dart';
import '../prompts/mxd_life_coaches.dart';

import 'feature_flag_service.dart';

/// Enterprise-grade model routing service with intelligent fallback chains,
/// usage tracking, and cost optimization for sustainable AI operations.
class ModelRoutingService {
  static final ModelRoutingService _instance = ModelRoutingService._internal();
  factory ModelRoutingService() => _instance;
  ModelRoutingService._internal();

  static const FlutterSecureStorage _storage = FlutterSecureStorage();
  static const String _usageKey = 'model_usage_stats';
  static const String _limitsKey = 'user_daily_limits';
  
  // Configuration constants
  static const int _dailyMessageLimit = 100; // 100 messages per user per day
  static const Duration _limitResetTime = Duration(hours: 24);
  
  // Model configuration - Updated for reliable coaching
  static const String _primaryModel = 'gpt-4o'; // Reliable primary model for coaching
  static const String _fallbackModel = 'gpt-4o-mini'; // Fallback for when gpt-4o is unavailable
  
  // In-memory caches for performance
  static final Map<String, UserUsageStats> _usageCache = {};
  static final Map<String, DateTime> _lastLimitCheck = {};
  
  /// Initialize the routing service
  static Future<void> initialize() async {
    try {
      await _loadUsageStats();
      await _cleanupOldData();
      
      if (kDebugMode) {
        print('🔀 Model routing service initialized');
        print('📊 Primary model: $_primaryModel');
        print('🔄 Fallback model: $_fallbackModel');
        print('📝 Daily limit: $_dailyMessageLimit messages/user');
      }
    } catch (e) {
      if (kDebugMode) print('❌ Failed to initialize model routing: $e');
    }
  }

  /// Route a message to the appropriate model with intelligent fallback
  static Future<ModelRoutingResult> routeMessage({
    required String userId,
    required String category,
    required String message,
    required User user,
    String? interactionType,
  }) async {
    try {
      // Check if hybrid routing is enabled
      final hybridEnabled = _isHybridRoutingEnabled();
      
      if (!hybridEnabled) {
        // Use primary model only
        final canUse = await _checkUserLimits(userId, category: category, userGender: user.gender);
        if (!canUse.canProceed) {
          return ModelRoutingResult.limitExceeded(canUse.exhaustedMessage ?? 'Daily limit exceeded');
        }
        
        await _recordUsage(userId, _primaryModel, message.length);
        return ModelRoutingResult.success(_primaryModel, 'Primary model (limits OK)');
      }
      
      // Hybrid routing logic (for future use)
      final strategy = await _determineRoutingStrategy(
        userId: userId,
        category: category,
        message: message,
        user: user,
        interactionType: interactionType,
      );
      
      // Check limits for selected model
      final canUse = await _checkUserLimits(userId, category: category, userGender: user.gender);
      if (!canUse.canProceed) {
        return ModelRoutingResult.limitExceeded(canUse.exhaustedMessage ?? 'Daily limit exceeded');
      }
      
      await _recordUsage(userId, strategy.selectedModel, message.length);
      return ModelRoutingResult.success(strategy.selectedModel, strategy.reasoning);
      
    } catch (e) {
      if (kDebugMode) print('❌ Model routing error: $e');
      return ModelRoutingResult.error('Routing failed: $e');
    }
  }

  /// Check if user has exceeded daily limits
  static Future<LimitCheckResult> _checkUserLimits(String userId, {String? category, String? userGender}) async {
    try {
      final stats = await _getUserUsageStats(userId);
      final today = DateTime.now();

      // Update last limit check time
      _lastLimitCheck[userId] = today;

      // Reset daily count if it's a new day (using _limitResetTime)
      if (stats.lastResetDate == null ||
          today.difference(stats.lastResetDate!) >= _limitResetTime) {
        stats.dailyMessageCount = 0;
        stats.lastResetDate = DateTime(today.year, today.month, today.day);
        await _saveUserUsageStats(userId, stats);

        // Store limits data using _limitsKey
        await _storage.write(
          key: '${_limitsKey}_${userId}_reset',
          value: jsonEncode({
            'userId': userId,
            'resetTime': today.toIso8601String(),
            'previousCount': stats.dailyMessageCount,
          }),
        );
      }
      
      if (stats.dailyMessageCount >= _dailyMessageLimit) {
        final coachName = _getCorrectCoachName(category, userGender);
        final exhaustedMessage = '*$coachName is exhausted & must rest, try messaging again tomorrow*';
        
        return LimitCheckResult(
          canProceed: false,
          remainingMessages: 0,
          exhaustedMessage: exhaustedMessage,
        );
      }
      
      return LimitCheckResult(
        canProceed: true,
        remainingMessages: _dailyMessageLimit - stats.dailyMessageCount,
        exhaustedMessage: null,
      );
      
    } catch (e) {
      if (kDebugMode) print('❌ Limit check error: $e');
      // On error, allow the message (fail open)
      return LimitCheckResult(canProceed: true, remainingMessages: _dailyMessageLimit);
    }
  }

  /// Record usage statistics for monitoring and cost tracking
  static Future<void> _recordUsage(String userId, String model, int tokenCount) async {
    try {
      final stats = await _getUserUsageStats(userId);
      
      // Update daily count
      stats.dailyMessageCount += 1;
      stats.totalMessages += 1;
      stats.totalTokens += tokenCount;
      stats.lastUsed = DateTime.now();
      
      // Update model-specific stats
      stats.modelUsage[model] = (stats.modelUsage[model] ?? 0) + 1;
      
      // Calculate estimated cost (simplified)
      final cost = _calculateCost(model, tokenCount);
      stats.totalCost += cost;
      
      await _saveUserUsageStats(userId, stats);
      
      if (kDebugMode) {
        print('📊 Usage recorded: $model, tokens: $tokenCount, cost: \$${cost.toStringAsFixed(4)}');
      }
      
    } catch (e) {
      if (kDebugMode) print('❌ Failed to record usage: $e');
    }
  }

  /// Get usage statistics for a user
  static Future<UserUsageStats> getUserStats(String userId) async {
    return await _getUserUsageStats(userId);
  }

  /// Get global usage statistics (for admin/creator version)
  static Future<GlobalUsageStats> getGlobalStats() async {
    try {
      final allStats = await _loadAllUsageStats();
      
      int totalUsers = allStats.length;
      int totalMessages = 0;
      int totalTokens = 0;
      double totalCost = 0.0;
      Map<String, int> modelBreakdown = {};
      
      for (final stats in allStats.values) {
        totalMessages += stats.totalMessages;
        totalTokens += stats.totalTokens;
        totalCost += stats.totalCost;
        
        for (final entry in stats.modelUsage.entries) {
          modelBreakdown[entry.key] = (modelBreakdown[entry.key] ?? 0) + entry.value;
        }
      }
      
      return GlobalUsageStats(
        totalUsers: totalUsers,
        totalMessages: totalMessages,
        totalTokens: totalTokens,
        totalCost: totalCost,
        modelBreakdown: modelBreakdown,
        averageMessagesPerUser: totalUsers > 0 ? totalMessages / totalUsers : 0.0,
        averageCostPerUser: totalUsers > 0 ? totalCost / totalUsers : 0.0,
      );
      
    } catch (e) {
      if (kDebugMode) print('❌ Failed to get global stats: $e');
      return GlobalUsageStats.empty();
    }
  }

  // Private helper methods
  static bool _isHybridRoutingEnabled() {
    // Check environment variable first
    final envEnabled = dotenv.env['ENABLE_HYBRID_ROUTING']?.toLowerCase() == 'true';
    if (envEnabled) {
      if (kDebugMode) print('🔀 Hybrid routing enabled via environment variable');
      return true;
    }

    // Check feature flag with comprehensive safety checks
    try {
      // Verify service is initialized before checking flag
      if (!FeatureFlagService.isInitialized) {
        if (kDebugMode) print('🚩 Feature flag service not yet initialized, defaulting hybrid routing to false');
        return false;
      }

      if (kDebugMode) print('🔀 Checking hybrid_model_routing feature flag...');
      final result = FeatureFlagService.isEnabled('hybrid_model_routing');
      if (kDebugMode) print('🔀 Hybrid routing flag result: $result');
      return result;
    } catch (e) {
      // If any error occurs, default to false and log it
      if (kDebugMode) print('⚠️ Error checking hybrid_model_routing flag: $e');
      return false;
    }
  }

  static Future<RoutingStrategy> _determineRoutingStrategy({
    required String userId,
    required String category,
    required String message,
    required User user,
    String? interactionType,
  }) async {
    // For now, always use primary model
    // Future: Implement intelligent routing based on:
    // - Message complexity
    // - User tier
    // - Time of day
    // - Category importance
    
    return RoutingStrategy(
      selectedModel: _primaryModel,
      reasoning: 'Primary model selected (hybrid routing ready)',
      confidence: 1.0,
    );
  }

  static double _calculateCost(String model, int tokenCount) {
    // Simplified cost calculation (tokens * rate per 1000)
    switch (model) {
      case 'o4-mini':
        return (tokenCount / 1000) * 0.003; // $3.00 per 1M tokens (superintelligent model)
      case 'gpt-4o-mini':
        return (tokenCount / 1000) * 0.00015; // $0.15 per 1M tokens (input)
      case 'o1-mini':
        return (tokenCount / 1000) * 0.003; // $3.00 per 1M tokens
      case 'gpt-4o':
        return (tokenCount / 1000) * 0.0025; // $2.50 per 1M tokens (input)
      case 'gpt-4-turbo':
        return (tokenCount / 1000) * 0.01; // $10 per 1M tokens
      default:
        return 0.0;
    }
  }

  static String _getCorrectCoachName(String? category, String? userGender) {
    if (category == null || userGender == null) {
      // Fallback to a generic coach name
      return 'Your Coach';
    }

    // Handle custom categories with hardcoded coach assignments
    if (category == 'Custom Category 1') {
      return userGender.toLowerCase() == 'female' ? 'Luna' : 'Aether';
    } else if (category == 'Custom Category 2') {
      return userGender.toLowerCase() == 'female' ? 'Elysia' : 'Chronos';
    }

    // Get the correct coach name based on category and gender using authoritative source
    final coach = mxdLifeCoaches.firstWhere(
      (c) => c.category.toLowerCase() == category.toLowerCase(),
      orElse: () => mxdLifeCoaches.first,
    );

    return userGender.toLowerCase() == 'female' ? coach.femaleName : coach.maleName;
  }

  static Future<UserUsageStats> _getUserUsageStats(String userId) async {
    if (_usageCache.containsKey(userId)) {
      return _usageCache[userId]!;
    }
    
    try {
      final data = await _storage.read(key: '${_usageKey}_$userId');
      if (data != null) {
        final stats = UserUsageStats.fromJson(jsonDecode(data));
        _usageCache[userId] = stats;
        return stats;
      }
    } catch (e) {
      if (kDebugMode) print('❌ Failed to load user stats: $e');
    }
    
    // Return new stats if none found
    final newStats = UserUsageStats(userId: userId);
    _usageCache[userId] = newStats;
    return newStats;
  }

  static Future<void> _saveUserUsageStats(String userId, UserUsageStats stats) async {
    try {
      _usageCache[userId] = stats;
      await _storage.write(
        key: '${_usageKey}_$userId',
        value: jsonEncode(stats.toJson()),
      );
    } catch (e) {
      if (kDebugMode) print('❌ Failed to save user stats: $e');
    }
  }

  static Future<Map<String, UserUsageStats>> _loadAllUsageStats() async {
    try {
      final allKeys = await _storage.readAll();
      final statsMap = <String, UserUsageStats>{};
      
      for (final entry in allKeys.entries) {
        if (entry.key.startsWith(_usageKey)) {
          try {
            final stats = UserUsageStats.fromJson(jsonDecode(entry.value));
            statsMap[stats.userId] = stats;
          } catch (e) {
            if (kDebugMode) print('❌ Failed to parse stats for ${entry.key}: $e');
          }
        }
      }
      
      return statsMap;
    } catch (e) {
      if (kDebugMode) print('❌ Failed to load all stats: $e');
      return {};
    }
  }

  static Future<void> _loadUsageStats() async {
    final allStats = await _loadAllUsageStats();
    _usageCache.addAll(allStats);
  }

  static Future<void> _cleanupOldData() async {
    try {
      final cutoffDate = DateTime.now().subtract(const Duration(days: 30));
      final allKeys = await _storage.readAll();
      
      for (final key in allKeys.keys) {
        if (key.startsWith(_usageKey)) {
          try {
            final data = jsonDecode(allKeys[key]!);
            final lastUsed = DateTime.parse(data['lastUsed'] ?? '1970-01-01');
            
            if (lastUsed.isBefore(cutoffDate)) {
              await _storage.delete(key: key);
              if (kDebugMode) print('🧹 Cleaned up old usage data: $key');
            }
          } catch (e) {
            // If we can't parse it, delete it
            await _storage.delete(key: key);
          }
        }
      }
    } catch (e) {
      if (kDebugMode) print('❌ Cleanup failed: $e');
    }
  }
}

// Data classes
class ModelRoutingResult {
  final bool success;
  final String? selectedModel;
  final String message;
  final bool limitExceeded;

  ModelRoutingResult._({
    required this.success,
    this.selectedModel,
    required this.message,
    this.limitExceeded = false,
  });

  factory ModelRoutingResult.success(String model, String reasoning) {
    return ModelRoutingResult._(
      success: true,
      selectedModel: model,
      message: reasoning,
    );
  }

  factory ModelRoutingResult.limitExceeded(String exhaustedMessage) {
    return ModelRoutingResult._(
      success: false,
      message: exhaustedMessage,
      limitExceeded: true,
    );
  }

  factory ModelRoutingResult.error(String error) {
    return ModelRoutingResult._(
      success: false,
      message: error,
    );
  }
}

class LimitCheckResult {
  final bool canProceed;
  final int remainingMessages;
  final String? exhaustedMessage;

  LimitCheckResult({
    required this.canProceed,
    required this.remainingMessages,
    this.exhaustedMessage,
  });
}

class RoutingStrategy {
  final String selectedModel;
  final String reasoning;
  final double confidence;

  RoutingStrategy({
    required this.selectedModel,
    required this.reasoning,
    required this.confidence,
  });
}

class UserUsageStats {
  final String userId;
  int dailyMessageCount;
  int totalMessages;
  int totalTokens;
  double totalCost;
  DateTime? lastResetDate;
  DateTime? lastUsed;
  Map<String, int> modelUsage;

  UserUsageStats({
    required this.userId,
    this.dailyMessageCount = 0,
    this.totalMessages = 0,
    this.totalTokens = 0,
    this.totalCost = 0.0,
    this.lastResetDate,
    this.lastUsed,
    Map<String, int>? modelUsage,
  }) : modelUsage = modelUsage ?? {};

  Map<String, dynamic> toJson() => {
    'userId': userId,
    'dailyMessageCount': dailyMessageCount,
    'totalMessages': totalMessages,
    'totalTokens': totalTokens,
    'totalCost': totalCost,
    'lastResetDate': lastResetDate?.toIso8601String(),
    'lastUsed': lastUsed?.toIso8601String(),
    'modelUsage': modelUsage,
  };

  factory UserUsageStats.fromJson(Map<String, dynamic> json) {
    return UserUsageStats(
      userId: json['userId'],
      dailyMessageCount: json['dailyMessageCount'] ?? 0,
      totalMessages: json['totalMessages'] ?? 0,
      totalTokens: json['totalTokens'] ?? 0,
      totalCost: (json['totalCost'] ?? 0.0).toDouble(),
      lastResetDate: json['lastResetDate'] != null 
          ? DateTime.parse(json['lastResetDate']) 
          : null,
      lastUsed: json['lastUsed'] != null 
          ? DateTime.parse(json['lastUsed']) 
          : null,
      modelUsage: Map<String, int>.from(json['modelUsage'] ?? {}),
    );
  }
}

class GlobalUsageStats {
  final int totalUsers;
  final int totalMessages;
  final int totalTokens;
  final double totalCost;
  final Map<String, int> modelBreakdown;
  final double averageMessagesPerUser;
  final double averageCostPerUser;

  GlobalUsageStats({
    required this.totalUsers,
    required this.totalMessages,
    required this.totalTokens,
    required this.totalCost,
    required this.modelBreakdown,
    required this.averageMessagesPerUser,
    required this.averageCostPerUser,
  });

  factory GlobalUsageStats.empty() {
    return GlobalUsageStats(
      totalUsers: 0,
      totalMessages: 0,
      totalTokens: 0,
      totalCost: 0.0,
      modelBreakdown: {},
      averageMessagesPerUser: 0.0,
      averageCostPerUser: 0.0,
    );
  }
}
