// 📁 lib/services/user_service.dart

import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:uuid/uuid.dart';
import '../models/user_model.dart';
import '../services/saved_accounts_service.dart';
import '../bulletproof/error_handler.dart';

/// Unified user data service for managing user persistence and retrieval.
///
/// The [UserService] handles all user data operations including:
/// - Creating, loading, updating, and deleting users
/// - Secure passcode management
/// - User session management
/// - Data validation and error handling
///
/// This service uses both [SharedPreferences] for general data and
/// [FlutterSecureStorage] for sensitive information like passcodes.
///
/// Example usage:
/// ```dart
/// final userService = UserService(ErrorHandler());
/// final user = User.blank(id: 'user123', username: 'john');
/// await userService.saveUser(user);
/// final loadedUser = await userService.loadUserByUsername('john');
/// ```
class UserService {
  final FlutterSecureStorage _storage;
  late SharedPreferences _prefs;
  final ErrorHandler _errorHandler;

  static const String _filename = 'user_data.json';
  static const String _userKey = 'user_%s';
  static const String _lastUserKey = 'last_username';
  static const String _passcodeKey = 'passcode_%s';

  static UserService? _staticInstance;

  /// Creates a new instance of UserService.
  /// [errorHandler] is required for error handling.
  UserService(this._errorHandler) : _storage = const FlutterSecureStorage() {
    _staticInstance = this;
    _initPrefs();
  }

  /// Initialize SharedPreferences
  Future<void> _initPrefs() async {
    try {
      _prefs = await SharedPreferences.getInstance();
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'UserService._initPrefs');
      rethrow;
    }
  }

  // --- Static wrappers for legacy code ---
  static Future<void> saveUserByUsername(User user) async {
    if (_staticInstance == null) {
      _staticInstance = UserService(ErrorHandler());
      await _staticInstance!._initPrefs();
    }
    await _staticInstance!.saveUser(user);
  }

  static Future<User?> loadUserByUsernameStatic(String username) async {
    if (_staticInstance == null) {
      _staticInstance = UserService(ErrorHandler());
      await _staticInstance!._initPrefs();
    }
    return _staticInstance!.loadUserByUsername(username);
  }

  /// Load user from local storage (legacy fallback)
  Future<User?> loadUser() async {
    try {
      final dir = await getApplicationDocumentsDirectory();
      final file = File('${dir.path}/$_filename');
      if (await file.exists()) {
        final contents = await file.readAsString();
        final user = User.fromJson(jsonDecode(contents));
        return user;
      }
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'UserService.loadUser');
      debugPrint('❌ Error loading user from file: $e');
    }
    return null;
  }

  /// Saves user data to SharedPreferences storage.
  ///
  /// Serializes the [User] object to JSON and stores it using a username-based
  /// key. Also updates the last active user for session management.
  ///
  /// This is the primary method for persisting user data. All user changes
  /// should go through this method to ensure consistency.
  ///
  /// Parameters:
  /// - [user]: The user object to save
  ///
  /// Throws an exception if serialization or storage fails.
  Future<void> saveUser(User user) async {
    try {
      await _initPrefs();
      final encoded = jsonEncode(user.toJson());
      await _prefs.setString(_userKey.replaceFirst('%s', user.username), encoded);
      await saveLastUser(user.username);
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'UserService.saveUser');
      rethrow;
    }
  }

  /// Loads a user by username from SharedPreferences storage.
  ///
  /// Retrieves the user data associated with the given username,
  /// deserializes it from JSON, and returns a [User] object.
  ///
  /// Parameters:
  /// - [username]: The username to search for
  ///
  /// Returns:
  /// - [User] object if found and valid
  /// - `null` if user doesn't exist or data is corrupted
  ///
  /// All errors are logged but don't throw exceptions to maintain
  /// app stability during user loading.
  Future<User?> loadUserByUsername(String username) async {
    try {
      await _initPrefs();
      final userJson = _prefs.getString(_userKey.replaceFirst('%s', username));
      if (userJson == null) return null;
      return User.fromJson(jsonDecode(userJson));
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'UserService.loadUserByUsername');
      return null;
    }
  }

  /// Delete user and wipe data
  Future<void> deleteUser(String username) async {
    try {
      await _initPrefs();
      await _prefs.remove(_userKey.replaceFirst('%s', username));
      await _storage.delete(key: _passcodeKey.replaceFirst('%s', username));
      if (await getLastUser() == username) {
        await _prefs.remove(_lastUserKey);
      }
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'UserService.deleteUser');
      rethrow;
    }
  }

  /// Get current user
  Future<User?> getCurrentUser() async {
    try {
      await _initPrefs();
      final lastUser = await getLastUser();
      if (lastUser == null) return null;
      return loadUserByUsername(lastUser);
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'UserService.getCurrentUser');
      return null;
    }
  }

  /// Update an existing user
  Future<void> updateUser(User user) async {
    await saveUser(user);
  }

  /// Save last used username
  Future<void> saveLastUser(String username) async {
    try {
      await _initPrefs();
      await _prefs.setString(_lastUserKey, username);
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'UserService.saveLastUser');
      rethrow;
    }
  }

  /// Get last used username
  Future<String?> getLastUser() async {
    try {
      await _initPrefs();
      return _prefs.getString(_lastUserKey);
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'UserService.getLastUser');
      return null;
    }
  }

  /// List all saved usernames
  Future<List<String>> getAllUsernames() async {
    try {
      await _initPrefs();
      final keys = _prefs.getKeys();
      return keys
          .where((k) => k.startsWith('user_'))
          .map((k) => k.replaceFirst('user_', ''))
          .toList();
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'UserService.getAllUsernames');
      return [];
    }
  }

  /// Load all users from shared preferences
  Future<List<User>> listUsers() async {
    try {
      await _initPrefs();
      final users = <User>[];
      for (final key in _prefs.getKeys()) {
        if (key.startsWith('user_')) {
          final data = _prefs.getString(key);
          if (data != null) {
            try {
              users.add(User.fromJson(jsonDecode(data)));
            } catch (e) {
              debugPrint('❌ Corrupt user data found for $key - attempting recovery');

              // Attempt to recover corrupt data
              final recoveredUser = await _attemptDataRecovery(key, data, e);
              if (recoveredUser != null) {
                users.add(recoveredUser);
                debugPrint('✅ Successfully recovered user data for $key');
              } else {
                // Log critical data loss for monitoring
                await _errorHandler.handleError(
                  Exception('Unrecoverable user data corruption for $key: ${e.toString()}'),
                  StackTrace.current,
                  context: 'UserService.listUsers - Data Corruption - Key: $key, DataLength: ${data.length}',
                );
              }
            }
          }
        }
      }
      return users;
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'UserService.listUsers');
      return [];
    }
  }

  /// Clear all user data
  Future<void> clearUserData() async {
    try {
      await _initPrefs();
      final keys = _prefs.getKeys();
      for (final key in keys) {
        if (key.startsWith('user_')) {
          await _prefs.remove(key);
        }
      }
      await _prefs.remove(_lastUserKey);
      await _storage.deleteAll();
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'UserService.clearUserData');
      rethrow;
    }
  }

  /// Update user passcode
  Future<void> updatePasscode(User user, String newPasscode) async {
    try {
      await _initPrefs();
      // Store passcode in secure storage
      await _storage.write(
        key: _passcodeKey.replaceFirst('%s', user.username),
        value: newPasscode,
      );
      
      // Update user's last modified timestamp
      final updatedUser = user.copyWith(
        lastModified: DateTime.now(),
      );
      await saveUser(updatedUser);
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'UserService.updatePasscode');
      rethrow;
    }
  }

  /// Save user to local file (legacy fallback)
  Future<void> saveUserLegacy(User user, {int exp = 0}) async {
    try {
      final dir = await getApplicationDocumentsDirectory();
      final file = File('${dir.path}/$_filename');

      User updatedUser = user;
      if (exp > 0) {
        updatedUser = user.copyWith(
          categories: {
            ...user.categories,
            'Health': (user.categories['Health'] ?? 0) + exp,
          },
        );
      }

      await file.writeAsString(jsonEncode(updatedUser.toJson()));
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'UserService.saveUserLegacy');
      debugPrint('❌ Error saving user to file: $e');
    }
  }

  static Future<void> createUser(String username) async {
    if (_staticInstance == null) {
      _staticInstance = UserService(ErrorHandler());
      await _staticInstance!._initPrefs();
    }
    final id = const Uuid().v4();
    final user = User.blank(id: id, username: username);
    await _staticInstance!.saveUser(user);

    // Save username for future convenience (dropdown feature)
    await SavedAccountsService.saveUsername(username);
    debugPrint('💾 UserService: Username saved for dropdown: $username');
  }

  static Future<List<String>> getAllUsernamesStatic() async {
    if (_staticInstance == null) {
      _staticInstance = UserService(ErrorHandler());
      await _staticInstance!._initPrefs();
    }
    return _staticInstance!.getAllUsernames();
  }

  static Future<void> deleteUserStatic(String username) async {
    if (_staticInstance == null) {
      _staticInstance = UserService(ErrorHandler());
      await _staticInstance!._initPrefs();
    }
    await _staticInstance!.deleteUser(username);
  }

  /// Get all users (alias for listUsers for test compatibility)
  Future<List<User>> getAllUsers() async {
    return listUsers();
  }

  /// Update user progress with experience points
  Future<User> updateUserProgress(User user, int expGain) async {
    try {
      // Calculate new experience
      final newExp = (user.exp + expGain).clamp(0, double.infinity).toInt();

      // Calculate new level based on experience
      final newLevel = _calculateLevel(newExp);

      // Create updated user
      final updatedUser = user.copyWith(
        exp: newExp,
        level: newLevel,
        lastModified: DateTime.now(),
      );

      // Save the updated user
      await saveUser(updatedUser);

      return updatedUser;
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'UserService.updateUserProgress');
      rethrow;
    }
  }

  /// Validate user data
  bool validateUser(User user) {
    try {
      // Check required fields
      if (user.username.isEmpty) return false;
      if (user.exp < 0) return false;
      if (user.level < 1) return false;

      // All validations passed
      return true;
    } catch (e, stackTrace) {
      _errorHandler.handleError(e, stackTrace, context: 'UserService.validateUser');
      return false;
    }
  }

  /// Calculate level based on experience points
  int _calculateLevel(int exp) {
    // Simple level calculation: 1000 exp per level
    return (exp / 1000).floor() + 1;
  }

  /// Attempt to recover corrupt user data
  ///
  /// This method tries various recovery strategies when user data is corrupted:
  /// 1. Partial JSON recovery - extract what we can
  /// 2. Backup data restoration - check for backup copies
  /// 3. Default user creation - create minimal user with preserved username
  Future<User?> _attemptDataRecovery(String key, String corruptData, dynamic error) async {
    try {
      final username = key.replaceFirst('user_', '');

      // Strategy 1: Try to extract partial data
      final partialUser = _attemptPartialRecovery(corruptData, username);
      if (partialUser != null) {
        // Save the recovered data immediately
        await saveUser(partialUser);
        return partialUser;
      }

      // Strategy 2: Check for backup data
      final backupUser = await _attemptBackupRecovery(username);
      if (backupUser != null) {
        await saveUser(backupUser);
        return backupUser;
      }

      // Strategy 3: Create minimal user to preserve username
      final minimalUser = User.blank(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        username: username,
      );

      // Save the minimal user
      await saveUser(minimalUser);

      // Save username for future convenience (dropdown feature)
      await SavedAccountsService.saveUsername(username);
      debugPrint('💾 UserService: Recovery username saved for dropdown: $username');

      debugPrint('🔄 Created minimal user to preserve username: $username');
      return minimalUser;

    } catch (e) {
      debugPrint('❌ All recovery strategies failed for $key: $e');
      return null;
    }
  }

  /// Attempt to recover partial data from corrupt JSON
  User? _attemptPartialRecovery(String corruptData, String username) {
    try {
      // Try to find recognizable patterns in the corrupt data
      final patterns = {
        'id': RegExp(r'"id"\s*:\s*"([^"]*)"'),
        'username': RegExp(r'"username"\s*:\s*"([^"]*)"'),
        'exp': RegExp(r'"exp"\s*:\s*(\d+)'),
        'level': RegExp(r'"level"\s*:\s*(\d+)'),
        'gender': RegExp(r'"gender"\s*:\s*"([^"]*)"'),
      };

      final extractedData = <String, dynamic>{};

      for (final entry in patterns.entries) {
        final match = entry.value.firstMatch(corruptData);
        if (match != null) {
          if (entry.key == 'exp' || entry.key == 'level') {
            extractedData[entry.key] = int.tryParse(match.group(1) ?? '0') ?? 0;
          } else {
            extractedData[entry.key] = match.group(1) ?? '';
          }
        }
      }

      // If we have at least username, create a user
      if (extractedData.containsKey('username') || username.isNotEmpty) {
        return User.blank(
          id: extractedData['id'] ?? DateTime.now().millisecondsSinceEpoch.toString(),
          username: extractedData['username'] ?? username,
        ).copyWith(
          exp: extractedData['exp'] ?? 0,
          level: extractedData['level'] ?? 1,
          gender: extractedData['gender'] ?? 'Unknown',
        );
      }

      return null;
    } catch (e) {
      return null;
    }
  }

  /// Attempt to recover from backup data
  Future<User?> _attemptBackupRecovery(String username) async {
    try {
      // Check if there's a backup key
      final backupKey = 'backup_user_$username';
      final backupData = _prefs.getString(backupKey);

      if (backupData != null) {
        return User.fromJson(jsonDecode(backupData));
      }

      return null;
    } catch (e) {
      return null;
    }
  }
}
