// lib/services/coach_notification_service.dart

import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:timezone/timezone.dart' as tz;
import '../models/user_model.dart';
import '../screens/coach_chat_screen.dart';
import 'coach_checkin_service.dart';
import 'coach_question_generator.dart';

/// Service for managing local notifications for coach check-ins
class CoachNotificationService {
  static final FlutterLocalNotificationsPlugin _notifications = FlutterLocalNotificationsPlugin();
  static const FlutterSecureStorage _storage = FlutterSecureStorage();
  static const String _chatHistoryKeyPrefix = 'chat_history_';
  static bool _isInitialized = false;

  /// Initialize the notification service
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Android initialization
      const androidSettings = AndroidInitializationSettings('@mipmap/mxd_icon');
      
      // iOS initialization
      const iosSettings = DarwinInitializationSettings(
        requestAlertPermission: true,
        requestBadgePermission: true,
        requestSoundPermission: true,
      );

      // macOS initialization
      const macosSettings = DarwinInitializationSettings(
        requestAlertPermission: true,
        requestBadgePermission: true,
        requestSoundPermission: true,
      );

      const initSettings = InitializationSettings(
        android: androidSettings,
        iOS: iosSettings,
        macOS: macosSettings,
      );

      await _notifications.initialize(
        initSettings,
        onDidReceiveNotificationResponse: _onNotificationTapped,
      );

      _isInitialized = true;
      
      if (kDebugMode) {
        print('🔔 Coach notification service initialized');
      }
    } catch (e) {
      if (kDebugMode) print('❌ Failed to initialize notifications: $e');
    }
  }

  /// Request notification permissions
  static Future<bool> requestPermissions() async {
    try {
      if (Platform.isIOS) {
        final result = await _notifications
            .resolvePlatformSpecificImplementation<IOSFlutterLocalNotificationsPlugin>()
            ?.requestPermissions(
              alert: true,
              badge: true,
              sound: true,
            );
        return result ?? false;
      } else if (Platform.isAndroid) {
        final result = await _notifications
            .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
            ?.requestNotificationsPermission();
        return result ?? false;
      }
      return true;
    } catch (e) {
      if (kDebugMode) print('❌ Failed to request notification permissions: $e');
      return false;
    }
  }

  /// Schedule a coach check-in notification
  static Future<void> scheduleCheckinNotification({
    required String category,
    required String coachName,
    required String username,
    required User user,
    required DateTime scheduledTime,
  }) async {
    try {
      await initialize();

      // Generate the check-in question
      final recentMessages = await _getRecentMessages(category);
      final question = await CoachQuestionGenerator.generateCheckinQuestion(
        category: category,
        coachName: coachName,
        username: username,
        user: user,
        recentMessages: recentMessages,
      );

      // Truncate question for notification preview
      final truncatedQuestion = _truncateQuestion(question);
      
      // Get coach icon
      final iconPath = _getCoachIconPath(coachName);

      // Create notification
      final notificationId = _generateNotificationId(category, coachName);
      
      final androidDetails = AndroidNotificationDetails(
        'coach_checkins',
        'Coach Check-ins',
        channelDescription: 'Proactive check-ins from your MXD coaches',
        importance: Importance.high,
        priority: Priority.high,
        icon: iconPath,
        largeIcon: DrawableResourceAndroidBitmap(iconPath),
        styleInformation: BigTextStyleInformation(
          truncatedQuestion,
          contentTitle: 'MXD - $coachName',
          summaryText: category,
        ),
        category: AndroidNotificationCategory.social,
        visibility: NotificationVisibility.public,
      );

      const iosDetails = DarwinNotificationDetails(
        categoryIdentifier: 'coach_checkin',
        threadIdentifier: 'coach_checkins',
        interruptionLevel: InterruptionLevel.active,
      );

      final notificationDetails = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      // Convert DateTime to TZDateTime
      final tzScheduledTime = tz.TZDateTime.from(scheduledTime, tz.local);

      // Schedule the notification
      await _notifications.zonedSchedule(
        notificationId,
        'MXD - $coachName',
        truncatedQuestion,
        tzScheduledTime,
        notificationDetails,
        androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
        payload: json.encode({
          'type': 'coach_checkin',
          'category': category,
          'coachName': coachName,
          'username': username,
          'fullQuestion': question,
          'scheduledTime': scheduledTime.toIso8601String(),
        }),
      );

      if (kDebugMode) {
        print('🔔 Scheduled check-in notification for $coachName at $scheduledTime');
      }
    } catch (e) {
      if (kDebugMode) print('❌ Failed to schedule check-in notification: $e');
    }
  }

  /// Send immediate check-in notification
  static Future<void> sendImmediateCheckin({
    required String category,
    required String coachName,
    required String username,
    required User user,
  }) async {
    try {
      await initialize();

      // Generate the check-in question
      final recentMessages = await _getRecentMessages(category);
      final question = recentMessages.isEmpty
          ? await CoachQuestionGenerator.generateWelcomeMessage(
              category: category,
              coachName: coachName,
              username: username,
              user: user,
            )
          : await CoachQuestionGenerator.generateCheckinQuestion(
              category: category,
              coachName: coachName,
              username: username,
              user: user,
              recentMessages: recentMessages,
            );

      // Add message to chat history
      await _addMessageToChat(category, question, false);

      // Mark check-in as sent
      await CoachCheckinService.markCheckinSent('${category}_$coachName');

      // Truncate question for notification preview
      final truncatedQuestion = _truncateQuestion(question);
      
      // Get coach icon
      final iconPath = _getCoachIconPath(coachName);

      // Create notification
      final notificationId = _generateNotificationId(category, coachName);
      
      final androidDetails = AndroidNotificationDetails(
        'coach_checkins',
        'Coach Check-ins',
        channelDescription: 'Proactive check-ins from your MXD coaches',
        importance: Importance.high,
        priority: Priority.high,
        icon: iconPath,
        largeIcon: DrawableResourceAndroidBitmap(iconPath),
        styleInformation: BigTextStyleInformation(
          truncatedQuestion,
          contentTitle: 'MXD - $coachName',
          summaryText: category,
        ),
        category: AndroidNotificationCategory.social,
        visibility: NotificationVisibility.public,
      );

      const iosDetails = DarwinNotificationDetails(
        categoryIdentifier: 'coach_checkin',
        threadIdentifier: 'coach_checkins',
        interruptionLevel: InterruptionLevel.active,
      );

      final notificationDetails = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      // Show the notification immediately
      await _notifications.show(
        notificationId,
        'MXD - $coachName',
        truncatedQuestion,
        notificationDetails,
        payload: json.encode({
          'type': 'coach_checkin',
          'category': category,
          'coachName': coachName,
          'username': username,
          'fullQuestion': question,
          'scheduledTime': DateTime.now().toIso8601String(),
        }),
      );

      if (kDebugMode) {
        print('🔔 Sent immediate check-in notification for $coachName');
      }
    } catch (e) {
      if (kDebugMode) print('❌ Failed to send immediate check-in: $e');
    }
  }

  /// Process eligible coaches and send check-ins
  static Future<void> processEligibleCoaches(User user) async {
    try {
      final eligibleCoaches = await CoachCheckinService.getEligibleCoaches();
      
      if (eligibleCoaches.isNotEmpty) {
        final selectedCoach = CoachCheckinService.selectRandomEligibleCoach(eligibleCoaches);
        
        if (selectedCoach != null) {
          await sendImmediateCheckin(
            category: selectedCoach['category'],
            coachName: selectedCoach['coachName'],
            username: user.username,
            user: user,
          );
        }
      }
    } catch (e) {
      if (kDebugMode) print('❌ Failed to process eligible coaches: $e');
    }
  }

  /// Cancel all scheduled notifications
  static Future<void> cancelAllNotifications() async {
    try {
      await _notifications.cancelAll();
      if (kDebugMode) print('🔔 Cancelled all notifications');
    } catch (e) {
      if (kDebugMode) print('❌ Failed to cancel notifications: $e');
    }
  }

  /// Cancel notifications for a specific coach
  static Future<void> cancelCoachNotifications(String category, String coachName) async {
    try {
      final notificationId = _generateNotificationId(category, coachName);
      await _notifications.cancel(notificationId);
      if (kDebugMode) print('🔔 Cancelled notifications for $coachName');
    } catch (e) {
      if (kDebugMode) print('❌ Failed to cancel coach notifications: $e');
    }
  }

  /// Handle notification tap
  static void _onNotificationTapped(NotificationResponse response) {
    try {
      if (response.payload != null) {
        final payload = json.decode(response.payload!);
        
        if (payload['type'] == 'coach_checkin') {
          // Navigate to coach chat screen
          // This will be handled by the main app navigation
          if (kDebugMode) {
            print('🔔 Notification tapped for ${payload['coachName']} (${payload['category']})');
          }
        }
      }
    } catch (e) {
      if (kDebugMode) print('❌ Failed to handle notification tap: $e');
    }
  }



  /// Get recent messages for a category
  static Future<List<ChatMessage>> _getRecentMessages(String category) async {
    try {
      final chatKey = '$_chatHistoryKeyPrefix${category.toLowerCase()}';
      final data = await _storage.read(key: chatKey);
      
      if (data != null) {
        final List<dynamic> messageList = json.decode(data);
        final messages = messageList
            .map((m) => ChatMessage.fromJson(m))
            .take(3)
            .toList();
        return messages;
      }
    } catch (e) {
      if (kDebugMode) print('❌ Failed to get recent messages: $e');
    }
    return [];
  }

  /// Add message to chat history
  static Future<void> _addMessageToChat(String category, String message, bool isUser) async {
    try {
      final chatKey = '$_chatHistoryKeyPrefix${category.toLowerCase()}';
      final data = await _storage.read(key: chatKey);
      
      List<dynamic> messages = [];
      if (data != null) {
        messages = json.decode(data);
      }
      
      final newMessage = ChatMessage(
        text: message,
        isUser: isUser,
        timestamp: DateTime.now(),
      );
      
      messages.insert(0, newMessage.toJson());
      
      // Keep only last 50 messages to prevent storage bloat
      if (messages.length > 50) {
        messages = messages.take(50).toList();
      }
      
      await _storage.write(key: chatKey, value: json.encode(messages));
    } catch (e) {
      if (kDebugMode) print('❌ Failed to add message to chat: $e');
    }
  }

  /// Truncate question for notification preview
  static String _truncateQuestion(String question) {
    const maxLength = 80;
    if (question.length <= maxLength) return question;
    
    // Find a good break point (space, punctuation)
    int breakPoint = maxLength;
    for (int i = maxLength - 1; i >= maxLength - 20; i--) {
      if (question[i] == ' ' || question[i] == ',' || question[i] == '.') {
        breakPoint = i;
        break;
      }
    }
    
    return '${question.substring(0, breakPoint)}...';
  }

  /// Get coach icon path for Android notifications
  static String _getCoachIconPath(String coachName) {
    try {
      // Convert coach name to drawable resource name
      final coachNameLower = coachName.toLowerCase().replaceAll('-', '_');

      // Map of available coach icons in drawable resources
      const availableCoachIcons = {
        'aether': 'aether_icon',
        'aria': 'aria_icon',
        'chronos': 'chronos_icon',
        'elysia': 'elysia_icon',
        'kai_tholo': 'kai_tholo_icon',
        'luna': 'luna_icon',
        'marion': 'marion_icon',
        'seraphina': 'seraphina_icon',
        'sterling': 'sterling_icon',
        'zen': 'zen_icon',
        'ves_ar': 'ves_ar_icon',
        'amara': 'amara_icon',
      };

      // Return the drawable resource name if available
      if (availableCoachIcons.containsKey(coachNameLower)) {
        return availableCoachIcons[coachNameLower]!;
      }

      // Fallback to MXD icon for general notifications
      return 'mxd_icon';
    } catch (e) {
      if (kDebugMode) print('❌ Failed to get coach icon: $e');
      return 'mxd_icon';
    }
  }

  /// Generate unique notification ID for coach
  static int _generateNotificationId(String category, String coachName) {
    final combined = '$category$coachName';
    return combined.hashCode.abs() % 2147483647; // Ensure positive 32-bit int
  }
}
