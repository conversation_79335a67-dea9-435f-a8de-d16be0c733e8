// lib/services/proactive_issue_detector.dart

import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'app_health_service.dart';
import 'continuous_monitoring_service.dart';
import 'player_experience_validator.dart';


/// Proactive Issue Detection System
/// 
/// This system uses predictive analytics and pattern recognition to detect
/// and prevent issues BEFORE they can affect players.
class ProactiveIssueDetector {
  static const FlutterSecureStorage _storage = FlutterSecureStorage();
  static const String _detectionDataKey = 'proactive_detection_data';
  
  static Timer? _detectionTimer;
  static bool _isDetecting = false;
  
  static final Map<String, List<double>> _performanceHistory = {};
  static final Map<String, int> _errorPatterns = {};
  static final List<PredictedIssue> _predictedIssues = [];
  static final Map<String, DateTime> _preventionActions = {};

  /// Start proactive issue detection
  static Future<void> startDetection() async {
    if (_isDetecting) return;
    
    try {
      _isDetecting = true;
      await _loadDetectionData();
      
      // Run detection every 5 minutes
      _detectionTimer = Timer.periodic(const Duration(minutes: 5), (timer) {
        _performProactiveDetection();
      });
      
      // Perform initial detection
      await _performProactiveDetection();
      
      if (kDebugMode) print('🔮 Proactive issue detection started');
    } catch (e) {
      if (kDebugMode) print('❌ Failed to start proactive detection: $e');
      _isDetecting = false;
    }
  }

  /// Stop proactive detection
  static void stopDetection() {
    _detectionTimer?.cancel();
    _detectionTimer = null;
    _isDetecting = false;
    
    if (kDebugMode) print('🛑 Proactive detection stopped');
  }

  /// Perform proactive issue detection
  static Future<void> _performProactiveDetection() async {
    try {
      if (kDebugMode) print('🔍 Running proactive issue detection...');
      
      // Clear old predictions
      _predictedIssues.clear();
      
      // Analyze performance trends
      await _analyzePerformanceTrends();
      
      // Detect error patterns
      await _detectErrorPatterns();
      
      // Predict system failures
      await _predictSystemFailures();
      
      // Analyze user experience degradation
      await _analyzeUserExperienceTrends();
      
      // Take preventive actions
      await _takePreventiveActions();
      
      // Save detection data
      await _saveDetectionData();
      
      if (kDebugMode) {
        print('🔮 Proactive detection completed: ${_predictedIssues.length} issues predicted');
      }
      
    } catch (e) {
      if (kDebugMode) print('❌ Proactive detection failed: $e');
      if (kDebugMode) print('❌ Proactive detection error: $e');
    }
  }

  /// Analyze performance trends to predict issues
  static Future<void> _analyzePerformanceTrends() async {
    try {
      // Get current monitoring data
      final monitoringStatus = ContinuousMonitoringService.getMonitoringStatus();
      final performanceMetrics = monitoringStatus['performanceMetrics'] as Map<String, dynamic>? ?? {};
      
      // Track response time trends
      final responseTime = performanceMetrics['responseTime'] as int? ?? 0;
      _addPerformanceDataPoint('responseTime', responseTime.toDouble());
      
      // Analyze response time trend
      if (_hasPerformanceData('responseTime')) {
        final trend = _calculateTrend('responseTime');
        if (trend > 50) { // Response time increasing by >50ms per measurement
          _predictedIssues.add(PredictedIssue(
            type: 'performance_degradation',
            severity: 'high',
            description: 'Response time trending upward - performance degradation likely',
            probability: _calculateProbability(trend, 100),
            timeToImpact: const Duration(hours: 2),
            preventiveActions: ['Optimize performance', 'Clear caches', 'Restart services'],
          ));
        }
      }
      
      // Track memory usage patterns (simplified)
      final memoryCheck = performanceMetrics['memoryCheck'] as String?;
      if (memoryCheck != null) {
        final timeSinceCheck = DateTime.now().difference(DateTime.parse(memoryCheck));
        if (timeSinceCheck.inMinutes > 30) {
          _predictedIssues.add(PredictedIssue(
            type: 'memory_issue',
            severity: 'medium',
            description: 'Memory monitoring gap detected - potential memory leak',
            probability: 0.6,
            timeToImpact: const Duration(hours: 1),
            preventiveActions: ['Force garbage collection', 'Monitor memory usage'],
          ));
        }
      }
      
    } catch (e) {
      if (kDebugMode) print('❌ Performance trend analysis failed: $e');
    }
  }

  /// Detect error patterns that might indicate future issues
  static Future<void> _detectErrorPatterns() async {
    try {
      // Get validation report
      final validationReport = PlayerExperienceValidator.getValidationReport();
      final experienceIssues = validationReport['experienceIssues'] as List? ?? [];
      
      // Track error patterns
      for (final issue in experienceIssues) {
        _errorPatterns[issue] = (_errorPatterns[issue] ?? 0) + 1;
      }
      
      // Detect recurring patterns
      for (final entry in _errorPatterns.entries) {
        if (entry.value >= 3) { // Issue occurred 3+ times
          _predictedIssues.add(PredictedIssue(
            type: 'recurring_error',
            severity: 'high',
            description: 'Recurring error pattern: ${entry.key}',
            probability: min(0.9, entry.value / 10.0),
            timeToImpact: const Duration(minutes: 30),
            preventiveActions: ['Fix root cause', 'Implement workaround', 'Monitor closely'],
          ));
        }
      }
      
    } catch (e) {
      if (kDebugMode) print('❌ Error pattern detection failed: $e');
    }
  }

  /// Predict potential system failures
  static Future<void> _predictSystemFailures() async {
    try {
      // Check system health trends
      final healthResult = await AppHealthService.performStartupHealthCheck();
      
      // Predict storage issues
      if (!healthResult.storageAccessible) {
        _predictedIssues.add(PredictedIssue(
          type: 'storage_failure',
          severity: 'critical',
          description: 'Storage access issues detected - failure imminent',
          probability: 0.95,
          timeToImpact: const Duration(minutes: 15),
          preventiveActions: ['Backup critical data', 'Restart storage service', 'Clear storage cache'],
        ));
      }
      
      // Predict network issues (simplified)
      // Note: networkConnected property doesn't exist in current HealthCheckResult
      // This would be implemented based on actual health check capabilities
      
      // Predict AI service issues
      final monitoringStatus = ContinuousMonitoringService.getMonitoringStatus();
      final performanceMetrics = monitoringStatus['performanceMetrics'] as Map<String, dynamic>? ?? {};
      final aiHealthy = performanceMetrics['aiServiceHealthy'] as bool? ?? true;
      
      if (!aiHealthy) {
        _predictedIssues.add(PredictedIssue(
          type: 'ai_service_failure',
          severity: 'high',
          description: 'AI service showing signs of instability',
          probability: 0.75,
          timeToImpact: const Duration(minutes: 20),
          preventiveActions: ['Switch to fallback responses', 'Restart AI service', 'Check API limits'],
        ));
      }
      
    } catch (e) {
      if (kDebugMode) print('❌ System failure prediction failed: $e');
    }
  }

  /// Analyze user experience trends
  static Future<void> _analyzeUserExperienceTrends() async {
    try {
      final validationReport = PlayerExperienceValidator.getValidationReport();
      final successRate = validationReport['successRate'] as double? ?? 100.0;
      
      // Track success rate trend
      _addPerformanceDataPoint('successRate', successRate);
      
      if (_hasPerformanceData('successRate')) {
        final trend = _calculateTrend('successRate');
        if (trend < -5) { // Success rate declining by >5% per measurement
          _predictedIssues.add(PredictedIssue(
            type: 'user_experience_degradation',
            severity: 'high',
            description: 'User experience success rate declining',
            probability: _calculateProbability(-trend, 20),
            timeToImpact: const Duration(hours: 1),
            preventiveActions: ['Review recent changes', 'Rollback problematic features', 'Increase monitoring'],
          ));
        }
      }
      
      // Check for critical experience issues
      final activeIssues = validationReport['activeIssues'] as int? ?? 0;
      if (activeIssues > 5) {
        _predictedIssues.add(PredictedIssue(
          type: 'experience_crisis',
          severity: 'critical',
          description: 'Multiple user experience issues detected',
          probability: 0.9,
          timeToImpact: const Duration(minutes: 5),
          preventiveActions: ['Emergency rollback', 'Disable problematic features', 'Alert development team'],
        ));
      }
      
    } catch (e) {
      if (kDebugMode) print('❌ User experience trend analysis failed: $e');
    }
  }

  /// Take preventive actions based on predictions
  static Future<void> _takePreventiveActions() async {
    try {
      for (final issue in _predictedIssues) {
        // Check if we've already taken action for this issue type recently
        final lastAction = _preventionActions[issue.type];
        if (lastAction != null && 
            DateTime.now().difference(lastAction).inMinutes < 30) {
          continue; // Don't spam preventive actions
        }
        
        // Take action based on severity and probability
        if (issue.severity == 'critical' || 
            (issue.severity == 'high' && issue.probability > 0.8)) {
          
          await _executePreventiveAction(issue);
          _preventionActions[issue.type] = DateTime.now();
          
          if (kDebugMode) {
            print('🛡️ Preventive action taken for: ${issue.description}');
          }
        }
      }
    } catch (e) {
      if (kDebugMode) print('❌ Preventive action execution failed: $e');
    }
  }

  /// Execute a specific preventive action
  static Future<void> _executePreventiveAction(PredictedIssue issue) async {
    try {
      switch (issue.type) {
        case 'performance_degradation':
          // Clear performance caches
          await _clearPerformanceCaches();
          break;
          
        case 'memory_issue':
          // Force garbage collection (simplified)
          await Future.delayed(const Duration(milliseconds: 100));
          break;
          
        case 'storage_failure':
          // Backup critical data
          await _backupCriticalData();
          break;
          
        case 'network_failure':
          // Enable offline mode
          await _enableOfflineMode();
          break;
          
        case 'ai_service_failure':
          // Switch to fallback responses
          await _enableAIFallbacks();
          break;
          
        case 'user_experience_degradation':
          // Increase monitoring frequency
          await _increaseMonitoringFrequency();
          break;
          
        case 'experience_crisis':
          // Emergency response
          await _triggerEmergencyResponse();
          break;
      }
    } catch (e) {
      if (kDebugMode) print('❌ Failed to execute preventive action: $e');
    }
  }

  /// Helper methods for preventive actions
  static Future<void> _clearPerformanceCaches() async {
    // Implementation would clear various caches
    if (kDebugMode) print('🧹 Performance caches cleared');
  }

  static Future<void> _backupCriticalData() async {
    // Implementation would backup user data
    if (kDebugMode) print('💾 Critical data backed up');
  }

  static Future<void> _enableOfflineMode() async {
    // Implementation would enable offline mode
    if (kDebugMode) print('📱 Offline mode enabled');
  }

  static Future<void> _enableAIFallbacks() async {
    // Implementation would enable AI fallbacks
    if (kDebugMode) print('🤖 AI fallbacks enabled');
  }

  static Future<void> _increaseMonitoringFrequency() async {
    // Implementation would increase monitoring
    if (kDebugMode) print('🔍 Monitoring frequency increased');
  }

  static Future<void> _triggerEmergencyResponse() async {
    // Implementation would trigger emergency protocols
    if (kDebugMode) print('🚨 Emergency response triggered');
  }

  /// Add performance data point for trend analysis
  static void _addPerformanceDataPoint(String metric, double value) {
    _performanceHistory[metric] ??= [];
    _performanceHistory[metric]!.add(value);
    
    // Keep only last 20 data points
    if (_performanceHistory[metric]!.length > 20) {
      _performanceHistory[metric]!.removeAt(0);
    }
  }

  /// Check if we have enough data for trend analysis
  static bool _hasPerformanceData(String metric) {
    return _performanceHistory[metric] != null && 
           _performanceHistory[metric]!.length >= 3;
  }

  /// Calculate trend (positive = increasing, negative = decreasing)
  static double _calculateTrend(String metric) {
    final data = _performanceHistory[metric]!;
    if (data.length < 3) return 0.0;
    
    // Simple linear regression slope
    final n = data.length;
    double sumX = 0, sumY = 0, sumXY = 0, sumX2 = 0;
    
    for (int i = 0; i < n; i++) {
      sumX += i;
      sumY += data[i];
      sumXY += i * data[i];
      sumX2 += i * i;
    }
    
    final slope = (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);
    return slope;
  }

  /// Calculate probability based on trend magnitude
  static double _calculateProbability(double trendMagnitude, double maxTrend) {
    return min(1.0, trendMagnitude.abs() / maxTrend);
  }

  /// Get detection report
  static Map<String, dynamic> getDetectionReport() {
    return {
      'isDetecting': _isDetecting,
      'predictedIssues': _predictedIssues.map((i) => i.toJson()).toList(),
      'errorPatterns': _errorPatterns,
      'performanceHistory': _performanceHistory,
      'preventionActions': _preventionActions.map((k, v) => MapEntry(k, v.toIso8601String())),
      'riskLevel': _calculateOverallRiskLevel(),
      'recommendations': _generateRiskRecommendations(),
    };
  }

  /// Calculate overall risk level
  static String _calculateOverallRiskLevel() {
    if (_predictedIssues.any((i) => i.severity == 'critical')) {
      return 'CRITICAL';
    }
    if (_predictedIssues.any((i) => i.severity == 'high' && i.probability > 0.7)) {
      return 'HIGH';
    }
    if (_predictedIssues.any((i) => i.severity == 'medium')) {
      return 'MEDIUM';
    }
    return 'LOW';
  }

  /// Generate risk-based recommendations
  static List<String> _generateRiskRecommendations() {
    final recommendations = <String>[];
    
    final riskLevel = _calculateOverallRiskLevel();
    switch (riskLevel) {
      case 'CRITICAL':
        recommendations.add('Take immediate action - critical issues predicted');
        break;
      case 'HIGH':
        recommendations.add('Monitor closely and prepare contingency plans');
        break;
      case 'MEDIUM':
        recommendations.add('Review system health and optimize performance');
        break;
      case 'LOW':
        recommendations.add('System is stable - continue normal monitoring');
        break;
    }
    
    return recommendations;
  }

  /// Save detection data
  static Future<void> _saveDetectionData() async {
    try {
      final data = {
        'performanceHistory': _performanceHistory,
        'errorPatterns': _errorPatterns,
        'predictedIssues': _predictedIssues.map((i) => i.toJson()).toList(),
        'preventionActions': _preventionActions.map((k, v) => MapEntry(k, v.toIso8601String())),
        'lastUpdate': DateTime.now().toIso8601String(),
      };

      await _storage.write(key: _detectionDataKey, value: jsonEncode(data));
    } catch (e) {
      if (kDebugMode) print('❌ Failed to save detection data: $e');
    }
  }

  /// Load detection data
  static Future<void> _loadDetectionData() async {
    try {
      final data = await _storage.read(key: _detectionDataKey);
      if (data != null) {
        final decoded = jsonDecode(data) as Map<String, dynamic>;
        
        // Load performance history
        _performanceHistory.clear();
        final perfHistory = decoded['performanceHistory'] as Map<String, dynamic>? ?? {};
        for (final entry in perfHistory.entries) {
          _performanceHistory[entry.key] = (entry.value as List).cast<double>();
        }
        
        // Load error patterns
        _errorPatterns.clear();
        _errorPatterns.addAll((decoded['errorPatterns'] as Map<String, dynamic>? ?? {}).cast<String, int>());
        
        // Load prevention actions
        _preventionActions.clear();
        final actions = decoded['preventionActions'] as Map<String, dynamic>? ?? {};
        for (final entry in actions.entries) {
          _preventionActions[entry.key] = DateTime.parse(entry.value as String);
        }
      }
    } catch (e) {
      if (kDebugMode) print('❌ Failed to load detection data: $e');
    }
  }

  /// Clear detection data
  static Future<void> clearDetectionData() async {
    try {
      await _storage.delete(key: _detectionDataKey);
      _performanceHistory.clear();
      _errorPatterns.clear();
      _predictedIssues.clear();
      _preventionActions.clear();
      
      if (kDebugMode) print('🧹 Detection data cleared');
    } catch (e) {
      if (kDebugMode) print('❌ Failed to clear detection data: $e');
    }
  }
}

/// Represents a predicted issue
class PredictedIssue {
  final String type;
  final String severity;
  final String description;
  final double probability;
  final Duration timeToImpact;
  final List<String> preventiveActions;

  PredictedIssue({
    required this.type,
    required this.severity,
    required this.description,
    required this.probability,
    required this.timeToImpact,
    required this.preventiveActions,
  });

  Map<String, dynamic> toJson() {
    return {
      'type': type,
      'severity': severity,
      'description': description,
      'probability': probability,
      'timeToImpact': timeToImpact.inMinutes,
      'preventiveActions': preventiveActions,
    };
  }
}
