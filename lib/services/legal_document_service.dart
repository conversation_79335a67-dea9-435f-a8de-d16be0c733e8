// lib/services/legal_document_service.dart

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:url_launcher/url_launcher.dart';

/// Service for managing and displaying legal documents
/// 
/// Provides access to Privacy Policy and Terms of Service documents
/// with proper formatting and navigation capabilities.
class LegalDocumentService {
  static const String _privacyPolicyPath = 'assets/legal/privacy_policy.html';
  static const String _termsOfServicePath = 'assets/legal/terms_of_service.html';
  
  /// Load Privacy Policy content
  static Future<String> getPrivacyPolicy() async {
    try {
      return await rootBundle.loadString(_privacyPolicyPath);
    } catch (e) {
      return _getPrivacyPolicyFallback();
    }
  }
  
  /// Load Terms of Service content
  static Future<String> getTermsOfService() async {
    try {
      return await rootBundle.loadString(_termsOfServicePath);
    } catch (e) {
      return _getTermsOfServiceFallback();
    }
  }
  
  /// Show Privacy Policy in a modal
  static Future<void> showPrivacyPolicy(BuildContext context) async {
    final content = await getPrivacyPolicy();
    
    if (!context.mounted) return;
    
    showDialog(
      context: context,
      builder: (context) => LegalDocumentDialog(
        title: 'Privacy Policy',
        htmlContent: content,
      ),
    );
  }
  
  /// Show Terms of Service in a modal
  static Future<void> showTermsOfService(BuildContext context) async {
    final content = await getTermsOfService();
    
    if (!context.mounted) return;
    
    showDialog(
      context: context,
      builder: (context) => LegalDocumentDialog(
        title: 'Terms of Service',
        htmlContent: content,
      ),
    );
  }
  
  /// Open Privacy Policy in external browser
  static Future<void> openPrivacyPolicyInBrowser() async {
    const url = 'https://mxdapp.com/privacy'; // Replace with your actual URL
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
    }
  }
  
  /// Open Terms of Service in external browser
  static Future<void> openTermsOfServiceInBrowser() async {
    const url = 'https://mxdapp.com/terms'; // Replace with your actual URL
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
    }
  }
  
  /// Get data usage summary for App Store declarations
  static Map<String, dynamic> getDataUsageDeclaration() {
    return {
      'dataTypes': [
        {
          'type': 'User ID',
          'linked': true,
          'tracking': false,
          'purposes': ['App Functionality', 'Personalization']
        },
        {
          'type': 'Email Address',
          'linked': true,
          'tracking': false,
          'purposes': ['App Functionality', 'Communication']
        },
        {
          'type': 'Name',
          'linked': true,
          'tracking': false,
          'purposes': ['App Functionality', 'Personalization']
        },
        {
          'type': 'Gameplay Content',
          'linked': true,
          'tracking': false,
          'purposes': ['App Functionality', 'Personalization']
        },
        {
          'type': 'Usage Data',
          'linked': false,
          'tracking': false,
          'purposes': ['Analytics', 'App Functionality']
        },
        {
          'type': 'Diagnostics',
          'linked': false,
          'tracking': false,
          'purposes': ['App Functionality']
        }
      ],
      'thirdPartyServices': [
        {
          'name': 'Firebase',
          'purpose': 'Authentication, Analytics, Crash Reporting',
          'dataShared': ['User ID', 'Usage Data', 'Diagnostics']
        },
        {
          'name': 'Klaviyo',
          'purpose': 'Email Marketing',
          'dataShared': ['Email Address', 'Name']
        },
        {
          'name': 'OpenAI',
          'purpose': 'AI Coaching',
          'dataShared': ['Anonymized Chat Data']
        }
      ]
    };
  }
  
  /// Fallback Privacy Policy content
  static String _getPrivacyPolicyFallback() {
    return '''
    <html>
    <head><title>Privacy Policy</title></head>
    <body>
    <h1>MXD Privacy Policy</h1>
    <p>We are committed to protecting your privacy. This app collects minimal data necessary for functionality.</p>
    <p>For the complete privacy policy, please visit our website or contact support.</p>
    </body>
    </html>
    ''';
  }
  
  /// Fallback Terms of Service content
  static String _getTermsOfServiceFallback() {
    return '''
    <html>
    <head><title>Terms of Service</title></head>
    <body>
    <h1>MXD Terms of Service</h1>
    <p>By using this app, you agree to our terms and conditions.</p>
    <p>For the complete terms of service, please visit our website or contact support.</p>
    </body>
    </html>
    ''';
  }
}

/// Dialog widget for displaying legal documents
class LegalDocumentDialog extends StatelessWidget {
  final String title;
  final String htmlContent;
  
  const LegalDocumentDialog({
    super.key,
    required this.title,
    required this.htmlContent,
  });
  
  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.black,
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            // Header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    fontFamily: 'Pirulen',
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close, color: Colors.white),
                ),
              ],
            ),
            const SizedBox(height: 20),
            
            // Content
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: _buildHtmlContent(),
                ),
              ),
            ),
            
            const SizedBox(height: 20),
            
            // Actions
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton(
                  onPressed: () => Navigator.of(context).pop(),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey[800],
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('Close'),
                ),
                ElevatedButton(
                  onPressed: () {
                    if (title.contains('Privacy')) {
                      LegalDocumentService.openPrivacyPolicyInBrowser();
                    } else {
                      LegalDocumentService.openTermsOfServiceInBrowser();
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('Open in Browser'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildHtmlContent() {
    // For now, we'll display a simplified version
    // In a production app, you might want to use a package like flutter_html
    return Text(
      _stripHtmlTags(htmlContent),
      style: const TextStyle(
        fontSize: 14,
        height: 1.5,
        color: Colors.black87,
      ),
    );
  }
  
  String _stripHtmlTags(String html) {
    return html
        .replaceAll(RegExp(r'<[^>]*>'), '')
        .replaceAll('&nbsp;', ' ')
        .replaceAll('&amp;', '&')
        .replaceAll('&lt;', '<')
        .replaceAll('&gt;', '>')
        .replaceAll('&quot;', '"')
        .trim();
  }
}
