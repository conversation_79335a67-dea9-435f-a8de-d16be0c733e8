// lib/services/firebase_auth_service.dart

import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import '../services/comprehensive_logging_service.dart';
import '../services/klaviyo_service.dart';

/// 🔥 Firebase Authentication Service
///
/// Handles user authentication with Firebase while maintaining
/// Klaviyo integration for marketing emails.
///
/// Features:
/// - Email/password authentication
/// - Email verification
/// - Password reset
/// - User state management
/// - Automatic Klaviyo sync
/// - Test email bypass for development
class FirebaseAuthService {
  static FirebaseAuth? _auth;
  static bool _initialized = false;
  static const String _testEmail = '<EMAIL>';

  /// Initialize Firebase Auth
  static Future<bool> initialize() async {
    if (_initialized) return true;

    try {
      await ComprehensiveLoggingService.logInfo('🔥 Initializing Firebase Auth');
      
      // Initialize Firebase if not already done
      if (Firebase.apps.isEmpty) {
        await Firebase.initializeApp();
      }
      
      _auth = FirebaseAuth.instance;
      _initialized = true;
      
      await ComprehensiveLoggingService.logInfo('✅ Firebase Auth initialized successfully');
      return true;
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Failed to initialize Firebase Auth: $e');
      return false;
    }
  }

  /// Get current Firebase user
  static User? get currentUser => _auth?.currentUser;

  /// Check if user is signed in
  static bool get isSignedIn => currentUser != null;

  /// Check if current user's email is verified
  static bool get isEmailVerified => currentUser?.emailVerified ?? false;

  /// Create account with email and password
  static Future<FirebaseAuthResult> createAccount({
    required String email,
    required String password,
    required String username,
    required String gender,
  }) async {
    // Bypass Firebase for test email
    if (email == _testEmail) {
      await ComprehensiveLoggingService.logInfo('🧪 Test email detected, bypassing Firebase account creation');

      // Simulate successful account creation
      await Future.delayed(const Duration(milliseconds: 500));

      return FirebaseAuthResult(
        success: true,
        user: null, // No actual Firebase user for test email
        message: 'Test account created successfully (bypassed Firebase)',
      );
    }

    if (!_initialized) {
      await initialize();
    }

    try {
      await ComprehensiveLoggingService.logInfo('🔥 Creating Firebase account for: $email');

      // Create Firebase user
      final credential = await _auth!.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (credential.user == null) {
        return FirebaseAuthResult(
          success: false,
          message: 'Failed to create user account',
        );
      }

      // Update display name
      await credential.user!.updateDisplayName(username);

      // Send Firebase email verification
      await ComprehensiveLoggingService.logInfo('📧 Sending Firebase email verification to: $email');
      try {
        await credential.user!.sendEmailVerification();
        await ComprehensiveLoggingService.logInfo('✅ Firebase email verification sent successfully');
      } catch (e) {
        await ComprehensiveLoggingService.logError('❌ Firebase email verification failed: $e');
        // Don't fail account creation if email verification fails
      }

      // Add to Klaviyo for marketing
      try {
        await ComprehensiveLoggingService.logInfo('🔄 Starting Klaviyo integration for: $email');

        // Check if API key is available
        final apiKey = dotenv.env['KLAVIYO_API_KEY'] ?? '';
        final listId = dotenv.env['KLAVIYO_LIST_ID'] ?? '';
        await ComprehensiveLoggingService.logInfo('🔑 Klaviyo config - API Key: ${apiKey.isNotEmpty ? "${apiKey.substring(0, 10)}..." : "MISSING"}, List ID: $listId');

        if (apiKey.isEmpty || listId.isEmpty) {
          throw Exception('Klaviyo configuration missing - API Key: ${apiKey.isEmpty ? "MISSING" : "OK"}, List ID: ${listId.isEmpty ? "MISSING" : "OK"}');
        }

        // Skip pre-testing and just try the actual operation
        await ComprehensiveLoggingService.logInfo('🔄 Proceeding directly with Klaviyo integration...');

        final success = await KlaviyoService.addEmailToList(email, firstName: username);
        if (success) {
          await ComprehensiveLoggingService.logInfo('✅ User successfully added to Klaviyo list');
        } else {
          throw Exception('KlaviyoService.addEmailToList returned false');
        }
      } catch (e) {
        await ComprehensiveLoggingService.logError('⚠️ Failed to add to Klaviyo (continuing): $e');
        // Don't fail the signup if Klaviyo fails - but log the detailed error
        print('🚨 KLAVIYO ERROR DETAILS: $e');
      }

      await ComprehensiveLoggingService.logInfo('✅ Firebase account created successfully');

      return FirebaseAuthResult(
        success: true,
        message: 'Account created successfully. Please verify your email.',
        user: credential.user,
        needsEmailVerification: true,
      );

    } on FirebaseAuthException catch (e) {
      String message = _getFirebaseErrorMessage(e.code);
      await ComprehensiveLoggingService.logError('❌ Firebase auth error: ${e.code} - $message');
      
      return FirebaseAuthResult(
        success: false,
        message: message,
        errorCode: e.code,
      );
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Unexpected error creating account: $e');
      
      return FirebaseAuthResult(
        success: false,
        message: 'An unexpected error occurred. Please try again.',
      );
    }
  }

  /// Sign in with email and password
  static Future<FirebaseAuthResult> signIn({
    required String email,
    required String password,
  }) async {
    // Bypass Firebase for test email
    if (email == _testEmail) {
      await ComprehensiveLoggingService.logInfo('🧪 Test email detected, bypassing Firebase sign-in');

      // Simulate successful sign-in
      await Future.delayed(const Duration(milliseconds: 500));

      return FirebaseAuthResult(
        success: true,
        user: null, // No actual Firebase user for test email
        message: 'Test sign-in successful (bypassed Firebase)',
      );
    }

    if (!_initialized) {
      await initialize();
    }

    try {
      await ComprehensiveLoggingService.logInfo('🔥 Signing in user: $email');

      final credential = await _auth!.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (credential.user == null) {
        return FirebaseAuthResult(
          success: false,
          message: 'Sign in failed',
        );
      }

      await ComprehensiveLoggingService.logInfo('✅ User signed in successfully');

      return FirebaseAuthResult(
        success: true,
        message: 'Signed in successfully',
        user: credential.user,
        needsEmailVerification: !credential.user!.emailVerified,
      );

    } on FirebaseAuthException catch (e) {
      String message = _getFirebaseErrorMessage(e.code);
      await ComprehensiveLoggingService.logError('❌ Sign in error: ${e.code} - $message');
      
      return FirebaseAuthResult(
        success: false,
        message: message,
        errorCode: e.code,
      );
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Unexpected sign in error: $e');
      
      return FirebaseAuthResult(
        success: false,
        message: 'An unexpected error occurred. Please try again.',
      );
    }
  }

  /// Send email verification
  static Future<bool> sendEmailVerification() async {
    try {
      if (currentUser == null) return false;

      // Check if current user is test email (bypass Firebase)
      if (currentUser!.email == _testEmail) {
        await ComprehensiveLoggingService.logInfo('🧪 Test email detected, bypassing Firebase email verification');
        return true;
      }

      await currentUser!.sendEmailVerification();
      await ComprehensiveLoggingService.logInfo('✅ Email verification sent');
      return true;
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Failed to send email verification: $e');

      // Check for Firebase rate limiting
      final errorMessage = e.toString().toLowerCase();
      if (errorMessage.contains('too-many-requests') ||
          errorMessage.contains('quota-exceeded') ||
          errorMessage.contains('rate-limit')) {
        await ComprehensiveLoggingService.logError('🚫 Firebase email rate limit hit');
        // Return false but don't crash - let the calling code handle gracefully
      }

      return false;
    }
  }

  /// Reload user to check verification status
  static Future<bool> reloadUser() async {
    try {
      if (currentUser == null) return false;
      
      await currentUser!.reload();
      return true;
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Failed to reload user: $e');
      return false;
    }
  }

  /// Sign out
  static Future<bool> signOut() async {
    try {
      await _auth?.signOut();
      await ComprehensiveLoggingService.logInfo('✅ User signed out');
      return true;
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Failed to sign out: $e');
      return false;
    }
  }

  /// Send password reset email
  static Future<bool> sendPasswordResetEmail(String email) async {
    try {
      await _auth!.sendPasswordResetEmail(email: email);
      await ComprehensiveLoggingService.logInfo('✅ Password reset email sent to: $email');
      return true;
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Failed to send password reset: $e');
      return false;
    }
  }

  /// Get user-friendly error messages
  static String _getFirebaseErrorMessage(String errorCode) {
    switch (errorCode) {
      case 'email-already-in-use':
        return 'This email is already registered. Try signing in instead.';
      case 'weak-password':
        return 'Password is too weak. Please choose a stronger password.';
      case 'invalid-email':
        return 'Please enter a valid email address.';
      case 'user-not-found':
        return 'No account found with this email. Please sign up first.';
      case 'wrong-password':
        return 'Incorrect password. Please try again.';
      case 'user-disabled':
        return 'This account has been disabled. Please contact support.';
      case 'too-many-requests':
        return 'Too many failed attempts. Please try again later.';
      case 'network-request-failed':
        return 'Network error. Please check your connection and try again.';
      default:
        return 'An error occurred. Please try again.';
    }
  }
}

/// Result class for Firebase Auth operations
class FirebaseAuthResult {
  final bool success;
  final String message;
  final User? user;
  final String? errorCode;
  final bool needsEmailVerification;

  FirebaseAuthResult({
    required this.success,
    required this.message,
    this.user,
    this.errorCode,
    this.needsEmailVerification = false,
  });
}
