// lib/services/user_spiritual_profile_service.dart

import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'dart:convert';

/// CRITICAL: Religious sensitivity service with bulletproof safeguards
/// 
/// This service handles all faith-related data with the utmost care and respect.
/// It ensures coaches NEVER provide inappropriate religious guidance.
/// 
/// SAFETY GUARANTEE: No religious content is shared without explicit user consent.
class UserSpiritualProfileService {
  static const FlutterSecureStorage _secureStorage = FlutterSecureStorage();
  static const String _profileKey = 'user_spiritual_profile';
  
  // Cache for performance
  static final Map<String, SpiritualProfile> _profileCache = {};
  
  /// Get user's spiritual profile with bulletproof safety
  static Future<SpiritualProfile> getUserSpiritualProfile(String userId) async {
    try {
      // Check cache first
      if (_profileCache.containsKey(userId)) {
        return _profileCache[userId]!;
      }
      
      // Load from secure storage
      final profileData = await _secureStorage.read(key: '${_profileKey}_$userId');
      
      if (profileData != null) {
        final profile = SpiritualProfile.fromJson(jsonDecode(profileData));
        _profileCache[userId] = profile;
        return profile;
      }
      
      // Return default profile if none exists
      final defaultProfile = SpiritualProfile.notSet(userId);
      _profileCache[userId] = defaultProfile;
      return defaultProfile;
      
    } catch (e) {
      if (kDebugMode) print('❌ Error loading spiritual profile: $e');
      // SAFETY: Always return safe default on error
      return SpiritualProfile.notSet(userId);
    }
  }
  
  /// Save spiritual profile with validation
  static Future<bool> saveSpiritualProfile(SpiritualProfile profile) async {
    try {
      // Validate profile before saving
      if (!_validateProfile(profile)) {
        if (kDebugMode) print('❌ Invalid spiritual profile - not saving');
        return false;
      }
      
      // Save to secure storage
      final profileJson = jsonEncode(profile.toJson());
      await _secureStorage.write(
        key: '${_profileKey}_${profile.userId}',
        value: profileJson,
      );
      
      // Update cache
      _profileCache[profile.userId] = profile;
      
      if (kDebugMode) print('✅ Spiritual profile saved for user ${profile.userId}');
      return true;
      
    } catch (e) {
      if (kDebugMode) print('❌ Error saving spiritual profile: $e');
      return false;
    }
  }
  
  /// CRITICAL: Check if religious content is appropriate for user
  static Future<bool> isReligiousContentAppropriate(
    String userId,
    ReligiousContentType contentType,
  ) async {
    try {
      final profile = await getUserSpiritualProfile(userId);
      
      // SAFETY: Never show religious content if profile not set
      if (!profile.isProfileSet) {
        return false;
      }
      
      // SAFETY: Never show religious content if user prefers secular
      if (profile.denomination == SpiritualDenomination.secular ||
          profile.denomination == SpiritualDenomination.preferNotToSay) {
        return false;
      }
      
      // Check specific content type appropriateness
      return _isContentTypeAppropriate(profile.denomination, contentType);
      
    } catch (e) {
      if (kDebugMode) print('❌ Error checking religious content appropriateness: $e');
      // SAFETY: Default to false on any error
      return false;
    }
  }
  
  /// Get appropriate spiritual wisdom for user
  static Future<SpiritualWisdomGuidance> getSpiritualWisdomGuidance(String userId) async {
    try {
      final profile = await getUserSpiritualProfile(userId);
      
      if (!profile.isProfileSet) {
        return SpiritualWisdomGuidance.notSet();
      }
      
      switch (profile.denomination) {
        case SpiritualDenomination.christian:
        case SpiritualDenomination.catholic:
        case SpiritualDenomination.orthodox:
          return SpiritualWisdomGuidance.christian(
            allowBibleVerses: profile.comfortLevel.index >= ComfortLevel.comfortable.index,
            allowPrayer: profile.comfortLevel.index >= ComfortLevel.veryComfortable.index,
            allowSaintWisdom: profile.denomination == SpiritualDenomination.catholic,
          );
          
        case SpiritualDenomination.jewish:
          return SpiritualWisdomGuidance.jewish();
          
        case SpiritualDenomination.muslim:
          return SpiritualWisdomGuidance.muslim();
          
        case SpiritualDenomination.buddhist:
          return SpiritualWisdomGuidance.buddhist();
          
        case SpiritualDenomination.hindu:
          return SpiritualWisdomGuidance.hindu();
          
        case SpiritualDenomination.secular:
          return SpiritualWisdomGuidance.secular();
          
        case SpiritualDenomination.preferNotToSay:
          return SpiritualWisdomGuidance.universal();
          
        case SpiritualDenomination.other:
          return SpiritualWisdomGuidance.universal();
      }
    } catch (e) {
      if (kDebugMode) print('❌ Error getting spiritual wisdom guidance: $e');
      // SAFETY: Default to universal wisdom on error
      return SpiritualWisdomGuidance.universal();
    }
  }
  
  /// Update user's comfort level with spiritual content
  static Future<bool> updateComfortLevel(String userId, ComfortLevel newLevel) async {
    try {
      final profile = await getUserSpiritualProfile(userId);
      final updatedProfile = profile.copyWith(comfortLevel: newLevel);
      return await saveSpiritualProfile(updatedProfile);
    } catch (e) {
      if (kDebugMode) print('❌ Error updating comfort level: $e');
      return false;
    }
  }
  
  /// Check if user needs spiritual profile setup
  static Future<bool> needsProfileSetup(String userId) async {
    final profile = await getUserSpiritualProfile(userId);
    return !profile.isProfileSet;
  }
  
  /// SAFETY: Validate profile data
  static bool _validateProfile(SpiritualProfile profile) {
    if (profile.userId.isEmpty) return false;
    if (profile.denomination == SpiritualDenomination.christian ||
        profile.denomination == SpiritualDenomination.catholic ||
        profile.denomination == SpiritualDenomination.orthodox) {
      // Christian denominations should have comfort level set
      return profile.comfortLevel != ComfortLevel.notSet;
    }
    return true;
  }
  
  /// SAFETY: Check if content type is appropriate for denomination
  static bool _isContentTypeAppropriate(
    SpiritualDenomination denomination,
    ReligiousContentType contentType,
  ) {
    switch (contentType) {
      case ReligiousContentType.christianBibleVerse:
      case ReligiousContentType.christianPrayer:
      case ReligiousContentType.christianVirtues:
        return denomination == SpiritualDenomination.christian ||
               denomination == SpiritualDenomination.catholic ||
               denomination == SpiritualDenomination.orthodox;
               
      case ReligiousContentType.catholicSaintWisdom:
      case ReligiousContentType.catholicTeaching:
        return denomination == SpiritualDenomination.catholic;
        
      case ReligiousContentType.orthodoxTeaching:
        return denomination == SpiritualDenomination.orthodox;
        
      case ReligiousContentType.jewishWisdom:
        return denomination == SpiritualDenomination.jewish;
        
      case ReligiousContentType.islamicWisdom:
        return denomination == SpiritualDenomination.muslim;
        
      case ReligiousContentType.buddhistWisdom:
        return denomination == SpiritualDenomination.buddhist;
        
      case ReligiousContentType.hinduWisdom:
        return denomination == SpiritualDenomination.hindu;
        
      case ReligiousContentType.universalSpiritual:
      case ReligiousContentType.stoicWisdom:
        return true; // Always appropriate
    }
  }
  
  /// Clear profile cache (for testing)
  static void clearCache() {
    _profileCache.clear();
  }
}

/// User's spiritual profile with safety guarantees
class SpiritualProfile {
  final String userId;
  final SpiritualDenomination denomination;
  final ComfortLevel comfortLevel;
  final bool isProfileSet;
  final DateTime lastUpdated;
  final String? customDenomination; // For "other" option
  
  SpiritualProfile({
    required this.userId,
    required this.denomination,
    required this.comfortLevel,
    required this.isProfileSet,
    required this.lastUpdated,
    this.customDenomination,
  });
  
  /// Create profile that's not set (safe default)
  factory SpiritualProfile.notSet(String userId) {
    return SpiritualProfile(
      userId: userId,
      denomination: SpiritualDenomination.preferNotToSay,
      comfortLevel: ComfortLevel.notSet,
      isProfileSet: false,
      lastUpdated: DateTime.now(),
    );
  }
  
  /// Create copy with updated fields
  SpiritualProfile copyWith({
    SpiritualDenomination? denomination,
    ComfortLevel? comfortLevel,
    bool? isProfileSet,
    String? customDenomination,
  }) {
    return SpiritualProfile(
      userId: userId,
      denomination: denomination ?? this.denomination,
      comfortLevel: comfortLevel ?? this.comfortLevel,
      isProfileSet: isProfileSet ?? this.isProfileSet,
      lastUpdated: DateTime.now(),
      customDenomination: customDenomination ?? this.customDenomination,
    );
  }
  
  /// Convert to JSON for storage
  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'denomination': denomination.name,
      'comfortLevel': comfortLevel.name,
      'isProfileSet': isProfileSet,
      'lastUpdated': lastUpdated.toIso8601String(),
      'customDenomination': customDenomination,
    };
  }
  
  /// Create from JSON
  factory SpiritualProfile.fromJson(Map<String, dynamic> json) {
    return SpiritualProfile(
      userId: json['userId'] ?? '',
      denomination: SpiritualDenomination.values.firstWhere(
        (e) => e.name == json['denomination'],
        orElse: () => SpiritualDenomination.preferNotToSay,
      ),
      comfortLevel: ComfortLevel.values.firstWhere(
        (e) => e.name == json['comfortLevel'],
        orElse: () => ComfortLevel.notSet,
      ),
      isProfileSet: json['isProfileSet'] ?? false,
      lastUpdated: DateTime.parse(json['lastUpdated'] ?? DateTime.now().toIso8601String()),
      customDenomination: json['customDenomination'],
    );
  }
}

/// Spiritual denominations with safety
enum SpiritualDenomination {
  christian,
  catholic,
  orthodox,
  jewish,
  muslim,
  buddhist,
  hindu,
  secular,
  preferNotToSay,
  other,
}

/// User's comfort level with spiritual content
enum ComfortLevel {
  notSet,
  uncomfortable,
  neutral,
  comfortable,
  veryComfortable,
}

/// Types of religious content for safety checking
enum ReligiousContentType {
  christianBibleVerse,
  christianPrayer,
  christianVirtues,
  catholicSaintWisdom,
  catholicTeaching,
  orthodoxTeaching,
  jewishWisdom,
  islamicWisdom,
  buddhistWisdom,
  hinduWisdom,
  universalSpiritual,
  stoicWisdom,
}

/// Guidance for spiritual wisdom sharing
class SpiritualWisdomGuidance {
  final bool canShareReligiousContent;
  final bool allowBibleVerses;
  final bool allowPrayer;
  final bool allowSaintWisdom;
  final List<String> appropriateWisdomTypes;
  final String fallbackWisdomType;
  
  SpiritualWisdomGuidance({
    required this.canShareReligiousContent,
    required this.allowBibleVerses,
    required this.allowPrayer,
    required this.allowSaintWisdom,
    required this.appropriateWisdomTypes,
    required this.fallbackWisdomType,
  });
  
  factory SpiritualWisdomGuidance.notSet() {
    return SpiritualWisdomGuidance(
      canShareReligiousContent: false,
      allowBibleVerses: false,
      allowPrayer: false,
      allowSaintWisdom: false,
      appropriateWisdomTypes: ['universal', 'stoic'],
      fallbackWisdomType: 'stoic',
    );
  }
  
  factory SpiritualWisdomGuidance.christian({
    required bool allowBibleVerses,
    required bool allowPrayer,
    required bool allowSaintWisdom,
  }) {
    return SpiritualWisdomGuidance(
      canShareReligiousContent: true,
      allowBibleVerses: allowBibleVerses,
      allowPrayer: allowPrayer,
      allowSaintWisdom: allowSaintWisdom,
      appropriateWisdomTypes: ['christian', 'biblical', 'universal'],
      fallbackWisdomType: 'christian',
    );
  }
  
  factory SpiritualWisdomGuidance.jewish() {
    return SpiritualWisdomGuidance(
      canShareReligiousContent: true,
      allowBibleVerses: false,
      allowPrayer: false,
      allowSaintWisdom: false,
      appropriateWisdomTypes: ['jewish', 'universal'],
      fallbackWisdomType: 'universal',
    );
  }
  
  factory SpiritualWisdomGuidance.muslim() {
    return SpiritualWisdomGuidance(
      canShareReligiousContent: true,
      allowBibleVerses: false,
      allowPrayer: false,
      allowSaintWisdom: false,
      appropriateWisdomTypes: ['islamic', 'universal'],
      fallbackWisdomType: 'universal',
    );
  }
  
  factory SpiritualWisdomGuidance.buddhist() {
    return SpiritualWisdomGuidance(
      canShareReligiousContent: true,
      allowBibleVerses: false,
      allowPrayer: false,
      allowSaintWisdom: false,
      appropriateWisdomTypes: ['buddhist', 'universal'],
      fallbackWisdomType: 'universal',
    );
  }
  
  factory SpiritualWisdomGuidance.hindu() {
    return SpiritualWisdomGuidance(
      canShareReligiousContent: true,
      allowBibleVerses: false,
      allowPrayer: false,
      allowSaintWisdom: false,
      appropriateWisdomTypes: ['hindu', 'universal'],
      fallbackWisdomType: 'universal',
    );
  }
  
  factory SpiritualWisdomGuidance.secular() {
    return SpiritualWisdomGuidance(
      canShareReligiousContent: false,
      allowBibleVerses: false,
      allowPrayer: false,
      allowSaintWisdom: false,
      appropriateWisdomTypes: ['stoic', 'philosophical', 'universal'],
      fallbackWisdomType: 'stoic',
    );
  }
  
  factory SpiritualWisdomGuidance.universal() {
    return SpiritualWisdomGuidance(
      canShareReligiousContent: false,
      allowBibleVerses: false,
      allowPrayer: false,
      allowSaintWisdom: false,
      appropriateWisdomTypes: ['universal', 'philosophical'],
      fallbackWisdomType: 'universal',
    );
  }
}
