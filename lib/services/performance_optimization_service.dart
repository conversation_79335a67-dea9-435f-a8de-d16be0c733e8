// lib/services/performance_optimization_service.dart

import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

import 'consolidated_cache_manager.dart';
import '../screens/coach_chat_screen.dart';

/// Service for optimizing app performance and memory usage
class PerformanceOptimizationService {
  static const FlutterSecureStorage _storage = FlutterSecureStorage();
  static const String _cacheKey = 'performance_cache';
  static const String _memoryStatsKey = 'memory_stats';
  
  // Consolidated cache management (memory optimized)
  static final ConsolidatedCacheManager _cacheManager = ConsolidatedCacheManager();
  static const Duration _cacheExpiry = Duration(minutes: 15); // Reduced from 1 hour
  static const Duration _responseCacheExpiry = Duration(minutes: 10); // Reduced from 30 minutes
  
  // Performance monitoring
  static final List<int> _responseTimes = [];
  static Timer? _memoryCleanupTimer;

  /// Initialize performance optimization (lightweight mode)
  static Future<void> initialize() async {
    try {
      if (kDebugMode) {
        print('⚡ Initializing performance optimization (lightweight)...');
      }

      // Initialize consolidated cache manager
      await _cacheManager.initialize();

      // Skip heavy initialization for now to prevent crashes
      // _startMemoryCleanup();
      // await _initializeMemoryTracking();

      if (kDebugMode) {
        print('✅ Performance optimization initialized (lightweight mode)');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Performance optimization initialization failed: $e');
      }
    }
  }

  /// Cache frequently accessed data (using consolidated cache)
  static Future<void> cacheData(String key, dynamic data) async {
    try {
      await _cacheManager.set(key, data, ttl: _cacheExpiry);

      if (kDebugMode) {
        final stats = _cacheManager.getStats();
        print('📦 Cached data: $key (${stats['entries']}/${stats['max_entries']})');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to cache data: $e');
      }
    }
  }

  /// Get cached data (using consolidated cache)
  static Future<T?> getCachedData<T>(String key) async {
    try {
      return await _cacheManager.get<T>(key, ttl: _cacheExpiry);
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to get cached data: $e');
      }
      return null;
    }
  }

  /// Optimize chat message loading
  static List<ChatMessage> optimizeChatMessages(List<ChatMessage> messages) {
    try {
      // Limit to last 100 messages for performance
      if (messages.length > 100) {
        messages = messages.sublist(messages.length - 100);
      }
      
      // Cache optimized messages
      final cacheKey = 'chat_messages_${messages.hashCode}';
      cacheData(cacheKey, messages);
      
      return messages;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to optimize chat messages: $e');
      }
      return messages;
    }
  }

  /// Optimize transcript search with caching
  static Future<List<String>> optimizeTranscriptSearch({
    required String query,
    required List<String> transcriptFiles,
    int maxResults = 10,
  }) async {
    try {
      final cacheKey = 'transcript_search_${query.hashCode}';
      
      // Check cache first
      final cached = await getCachedData<List<String>>(cacheKey);
      if (cached != null) {
        if (kDebugMode) {
          print('📦 Using cached transcript search results');
        }
        return cached;
      }
      
      // Perform search with optimization
      final results = <String>[];
      final queryLower = query.toLowerCase();
      
      // Use parallel processing for large transcript sets
      if (transcriptFiles.length > 10) {
        final futures = transcriptFiles.map((file) async {
          return await _searchInTranscript(file, queryLower);
        });
        
        final searchResults = await Future.wait(futures);
        for (final result in searchResults) {
          if (result != null) {
            results.add(result);
            if (results.length >= maxResults) break;
          }
        }
      } else {
        // Sequential search for smaller sets
        for (final file in transcriptFiles) {
          final result = await _searchInTranscript(file, queryLower);
          if (result != null) {
            results.add(result);
            if (results.length >= maxResults) break;
          }
        }
      }
      
      // Cache results
      cacheData(cacheKey, results);
      
      return results;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Transcript search optimization failed: $e');
      }
      return [];
    }
  }

  /// Search in individual transcript file
  static Future<String?> _searchInTranscript(String filename, String query) async {
    try {
      // This would normally read from assets
      // For now, return a mock result
      if (filename.toLowerCase().contains(query) || 
          query.length > 3 && filename.contains(query.substring(0, 3))) {
        return 'Mock transcript content from $filename containing "$query"';
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// Optimize memory usage by cleaning up unused data (using consolidated cache)
  static Future<void> optimizeMemoryUsage() async {
    try {
      // Consolidated cache manager handles its own cleanup automatically
      // Just trigger a manual cleanup if needed
      final stats = _cacheManager.getStats();

      if (kDebugMode) {
        print('🗑️ Memory optimization: ${stats['entries']} cache entries, ${stats['hit_rate_percent']}% hit rate');
      }

      // Limit response time tracking
      if (_responseTimes.length > 100) {
        _responseTimes.removeRange(0, _responseTimes.length - 100);
      }

    } catch (e) {
      if (kDebugMode) {
        print('❌ Memory optimization failed: $e');
      }
    }
  }

  /// Track response time for performance monitoring
  static void trackResponseTime(int milliseconds) {
    try {
      _responseTimes.add(milliseconds);
      
      // Keep only last 100 response times
      if (_responseTimes.length > 100) {
        _responseTimes.removeAt(0);
      }
      
      if (kDebugMode && milliseconds > 5000) {
        print('⚠️ Slow response detected: ${milliseconds}ms');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to track response time: $e');
      }
    }
  }

  /// Get performance statistics
  static Map<String, dynamic> getPerformanceStats() {
    try {
      final avgResponseTime = _responseTimes.isNotEmpty 
          ? _responseTimes.reduce((a, b) => a + b) / _responseTimes.length
          : 0.0;
      
      final maxResponseTime = _responseTimes.isNotEmpty 
          ? _responseTimes.reduce((a, b) => a > b ? a : b)
          : 0;
      
      final cacheStats = _cacheManager.getStats();

      return {
        'cacheSize': cacheStats['entries'],
        'maxCacheSize': cacheStats['max_entries'],
        'cacheHitRate': cacheStats['hit_rate_percent'],
        'averageResponseTime': avgResponseTime.round(),
        'maxResponseTime': maxResponseTime,
        'responseTimeCount': _responseTimes.length,
        'memoryOptimizationActive': _memoryCleanupTimer?.isActive ?? false,
        'cacheStats': cacheStats,
      };
    } catch (e) {
      return {
        'error': 'Failed to get performance stats: $e',
        'cacheSize': 0,
        'averageResponseTime': 0,
      };
    }
  }

  /// Preload frequently accessed data
  static Future<void> preloadCriticalData() async {
    try {
      if (kDebugMode) {
        print('🚀 Preloading critical data...');
      }
      
      // Preload coach prompts
      final coachCategories = ['Health', 'Wealth', 'Purpose', 'Connection'];
      for (final category in coachCategories) {
        cacheData('coach_category_$category', category);
      }
      
      // Preload common responses
      final commonResponses = [
        'Great question! Let me help you with that.',
        'I understand what you\'re going through.',
        'That\'s a fantastic goal to work towards.',
        'Here\'s what I recommend...',
      ];
      
      for (int i = 0; i < commonResponses.length; i++) {
        cacheData('common_response_$i', commonResponses[i]);
      }
      
      if (kDebugMode) {
        print('✅ Critical data preloaded');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to preload critical data: $e');
      }
    }
  }

  /// Optimize image loading and caching
  static Future<void> optimizeImageCache() async {
    try {
      // This would normally implement image caching optimization
      // For now, just track that it was called
      cacheData('image_cache_optimized', DateTime.now().toIso8601String());
      
      if (kDebugMode) {
        print('🖼️ Image cache optimized');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Image cache optimization failed: $e');
      }
    }
  }











  /// Cleanup resources (using consolidated cache)
  static void dispose() {
    try {
      _memoryCleanupTimer?.cancel();
      _cacheManager.dispose();
      _responseTimes.clear();

      if (kDebugMode) {
        print('🧹 Performance optimization disposed');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to dispose performance optimization: $e');
      }
    }
  }

  /// Clear all performance data (for testing)
  static Future<void> clearPerformanceData() async {
    try {
      await _storage.delete(key: _cacheKey);
      await _storage.delete(key: _memoryStatsKey);
      
      _cacheManager.clear();
      _responseTimes.clear();
      
      if (kDebugMode) {
        print('🗑️ Performance data cleared');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to clear performance data: $e');
      }
    }
  }

  /// Cache AI response for similar queries
  static Future<void> cacheResponse(String queryHash, String response) async {
    try {
      // Using consolidated cache instead
      await _cacheManager.set('response_$queryHash', response, ttl: _responseCacheExpiry);

      if (kDebugMode) {
        print('💾 Cached AI response for query hash: ${queryHash.substring(0, 8)}...');
      }
    } catch (e) {
      if (kDebugMode) print('❌ Response caching failed: $e');
    }
  }

  /// Get cached AI response
  static Future<String?> getCachedResponse(String queryHash) async {
    try {
      return await _cacheManager.get<String>('response_$queryHash', ttl: _responseCacheExpiry);
    } catch (e) {
      if (kDebugMode) print('❌ Response cache retrieval failed: $e');
      return null;
    }
  }


}
