// 📁 lib/services/coach_context_service.dart

import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../models/user_model.dart';
import '../models/training_session_model.dart';

import '../services/training_storage_service.dart';
import '../utils/rank_utils.dart';
import '../utils/date_formatter.dart';

/// Comprehensive coach context service that aggregates all user data
/// into structured JSON format for AI coaches to provide seamless,
/// personalized experiences that touch on all aspects of user's life.
class CoachContextService {
  static final CoachContextService _instance = CoachContextService._internal();
  factory CoachContextService() => _instance;
  CoachContextService._internal();

  static const FlutterSecureStorage _storage = FlutterSecureStorage();
  static const String _contextCacheKey = 'coach_context_cache';
  static const Duration _cacheExpiry = Duration(minutes: 5);
  
  // In-memory cache for performance
  static final Map<String, CachedContext> _contextCache = {};

  /// Get comprehensive user context for coaches in structured JSON format
  static Future<Map<String, dynamic>> getUserContext(User user) async {
    try {
      // Check cache first
      final cached = _getCachedContext(user.id);
      if (cached != null) {
        if (kDebugMode) print('🧠 Using cached context for ${user.username}');
        return cached;
      }

      if (kDebugMode) print('🔄 Building fresh context for ${user.username}');
      
      // Build comprehensive context
      final context = await _buildUserContext(user);
      
      // Cache the result
      await _cacheContext(user.id, context);
      
      if (kDebugMode) {
        print('✅ Context built for ${user.username}');
        print('📊 Context size: ${jsonEncode(context).length} characters');
      }
      
      return context;
      
    } catch (e) {
      if (kDebugMode) print('❌ Failed to build user context: $e');
      return _getFallbackContext(user);
    }
  }

  /// Build comprehensive user context from all available data
  static Future<Map<String, dynamic>> _buildUserContext(User user) async {
    final now = DateTime.now();
    
    return {
      'user_profile': _buildUserProfile(user),
      'progress_data': _buildProgressData(user),
      'diary_feed': _buildDiaryFeed(user),
      'north_star_quest': _buildNorthStarContext(user),
      'habits_data': _buildHabitsData(user),
      'recent_activity': _buildRecentActivity(user),
      'behavioral_patterns': _buildBehavioralPatterns(user),
      'training_data': await _buildTrainingData(user),
      'context_metadata': {
        'generated_at': now.toIso8601String(),
        'data_freshness': 'real_time',
        'context_version': '1.1',
        'user_timezone': now.timeZoneName,
      },
    };
  }

  /// Build user profile section
  static Map<String, dynamic> _buildUserProfile(User user) {
    return {
      'basic_info': {
        'username': user.username,
        'gender': user.gender,
        'account_age_days': DateTime.now().difference(user.createdAt).inDays,
        'last_login': DateFormatter.formatDateTime(user.lastLoginAt),
        'total_exp': user.exp,
        'current_level': user.level,
        'current_rank': user.rank,
        'rank_progress': user.rankProgress,
      },
      'assigned_coaches': user.assignedCoaches ?? {},
      'custom_categories': user.customCategories,
      'preferences': {
        'show_home_level_widget': user.showHomeLevelWidget,
        'show_lock_screen_widget': user.showLockScreenWidget,
      },
    };
  }

  /// Build progress and achievement data
  static Map<String, dynamic> _buildProgressData(User user) {
    final categoryStats = <String, Map<String, dynamic>>{};
    
    for (final entry in user.categories.entries) {
      final category = entry.key;
      final exp = entry.value;
      final level = LevelUtils.getRankLevel(exp);
      final rankName = LevelUtils.getRankName(exp);
      
      categoryStats[category] = {
        'current_exp': exp,
        'level': level,
        'rank': rankName,
        'progress_to_next': LevelUtils.getExpToNextRank(exp),
        'last_week_exp': user.lastWeekExp[category] ?? 0,
      };
    }
    
    return {
      'category_breakdown': categoryStats,
      'streak_data': {
        'current_streak': user.streak,
        'streak_days': user.streakDays,
        'longest_streak': user.streakDays, // Could track separately
      },
      'challenge_data': {
        'challenge_exp': user.challengeExp,
        'completed_quests': user.quests.length,
      },
      'daily_progress': {
        'daily_goal_progress': user.dailyGoalProgress,
        'habits_completed_today': _getHabitsCompletedToday(user),
      },
    };
  }

  /// Build diary feed with recent entries
  static Map<String, dynamic> _buildDiaryFeed(User user) {
    // Combine diary entries and North Star logs
    final allEntries = <Map<String, dynamic>>[];
    
    // Add regular diary entries
    for (final entry in user.diaryEntries) {
      allEntries.add({
        'id': entry.id,
        'type': 'diary_entry',
        'timestamp': entry.timestamp.toIso8601String(),
        'category': entry.category,
        'note': entry.note,
        'exp': entry.exp,
        'days_ago': DateTime.now().difference(entry.timestamp).inDays,
      });
    }
    
    // Add North Star logs if available
    if (user.northStarQuest != null) {
      for (final log in user.northStarQuest!.logs) {
        allEntries.add({
          'id': log.id,
          'type': 'north_star_log',
          'timestamp': log.loggedAt.toIso8601String(),
          'category': log.category,
          'note': log.entryText,
          'hours': log.hours,
          'exp_equivalent': (log.hours * 10).round(),
          'days_ago': DateTime.now().difference(log.loggedAt).inDays,
        });
      }
    }
    
    // Sort by timestamp (most recent first)
    allEntries.sort((a, b) => 
        DateTime.parse(b['timestamp']).compareTo(DateTime.parse(a['timestamp'])));
    
    return {
      'total_entries': allEntries.length,
      'recent_entries': allEntries.take(20).toList(), // Last 20 entries
      'entries_last_7_days': allEntries.where((e) => e['days_ago'] <= 7).length,
      'entries_last_30_days': allEntries.where((e) => e['days_ago'] <= 30).length,
      'most_active_category': _getMostActiveCategory(allEntries),
    };
  }

  /// Build North Star quest context
  static Map<String, dynamic> _buildNorthStarContext(User user) {
    if (user.northStarQuest == null) {
      return {
        'has_quest': false,
        'status': 'no_active_quest',
        'recommendation': 'Consider creating a North Star quest for long-term focus',
      };
    }
    
    final quest = user.northStarQuest!;
    final daysSinceCreated = DateTime.now().difference(quest.createdAt).inDays;
    
    return {
      'has_quest': true,
      'quest_details': {
        'id': quest.id,
        'title': quest.title,
        'summary': quest.summary,
        'category': quest.category,
        'icon': quest.icon,
        'created_days_ago': daysSinceCreated,
      },
      'progress': {
        'total_exp': quest.totalExp,
        'hours_logged': quest.hoursLogged,
        'current_rank': quest.currentRank,
        'total_logs': quest.logs.length,
      },
      'core_values': quest.coreValues,
      'milestones': quest.milestones,
      'recent_activity': quest.logs
          .where((log) => DateTime.now().difference(log.loggedAt).inDays <= 7)
          .length,
      'vision_image': quest.visionImagePath != null ? 'has_vision_image' : 'no_vision_image',
    };
  }

  /// Build habits data
  static Map<String, dynamic> _buildHabitsData(User user) {
    final habitsData = <Map<String, dynamic>>[];

    for (final habit in user.dailyHabits) {
      final isCompletedToday = _isHabitCompletedToday(habit);

      habitsData.add({
        'id': habit.id,
        'name': habit.name,
        'description': habit.description,
        'color': habit.color.toARGB32().toString(), // Convert Color to serializable string
        'streak': habit.streak,
        'completed_today': isCompletedToday,
        'last_completed': habit.lastCompleted?.toIso8601String(),
        'created_days_ago': DateTime.now().difference(habit.createdAt).inDays,
      });
    }
    
    final completedToday = habitsData.where((h) => h['completed_today'] == true).length;
    
    return {
      'total_habits': habitsData.length,
      'habits_list': habitsData,
      'completed_today': completedToday,
      'completion_rate_today': habitsData.isNotEmpty ? completedToday / habitsData.length : 0.0,
      'longest_streak': habitsData.isNotEmpty 
          ? habitsData.map((h) => h['streak'] as int).reduce((a, b) => a > b ? a : b)
          : 0,
    };
  }

  /// Build recent activity summary
  static Map<String, dynamic> _buildRecentActivity(User user) {
    final now = DateTime.now();
    final last7Days = now.subtract(const Duration(days: 7));
    final last24Hours = now.subtract(const Duration(hours: 24));
    
    // Count recent diary entries
    final recentDiaryEntries = user.diaryEntries
        .where((entry) => entry.timestamp.isAfter(last7Days))
        .length;
    
    // Count recent North Star logs
    final recentNSLogs = user.northStarQuest?.logs
        .where((log) => log.loggedAt.isAfter(last7Days))
        .length ?? 0;
    
    // Calculate recent EXP
    final recentExp = user.diaryEntries
        .where((entry) => entry.timestamp.isAfter(last7Days))
        .fold(0, (sum, entry) => sum + entry.exp);
    
    return {
      'last_24_hours': {
        'diary_entries': user.diaryEntries
            .where((entry) => entry.timestamp.isAfter(last24Hours))
            .length,
        'exp_gained': user.diaryEntries
            .where((entry) => entry.timestamp.isAfter(last24Hours))
            .fold(0, (sum, entry) => sum + entry.exp),
      },
      'last_7_days': {
        'diary_entries': recentDiaryEntries,
        'north_star_logs': recentNSLogs,
        'total_exp_gained': recentExp,
        'average_daily_entries': recentDiaryEntries / 7,
      },
      'engagement_level': _calculateEngagementLevel(user),
      'last_activity': _getLastActivityTimestamp(user),
    };
  }

  /// Build behavioral patterns analysis
  static Map<String, dynamic> _buildBehavioralPatterns(User user) {
    final patterns = <String, dynamic>{};
    
    // Analyze category preferences
    final categoryTotals = user.categories.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    patterns['preferred_categories'] = categoryTotals.take(3)
        .map((e) => {'category': e.key, 'exp': e.value})
        .toList();
    
    // Analyze activity timing (simplified)
    final recentEntries = user.diaryEntries
        .where((entry) => DateTime.now().difference(entry.timestamp).inDays <= 30)
        .toList();
    
    if (recentEntries.isNotEmpty) {
      final hourCounts = <int, int>{};
      for (final entry in recentEntries) {
        final hour = entry.timestamp.hour;
        hourCounts[hour] = (hourCounts[hour] ?? 0) + 1;
      }
      
      final mostActiveHour = hourCounts.entries
          .reduce((a, b) => a.value > b.value ? a : b)
          .key;
      
      patterns['activity_timing'] = {
        'most_active_hour': mostActiveHour,
        'activity_distribution': hourCounts,
      };
    }
    
    // Analyze consistency
    patterns['consistency'] = {
      'streak_consistency': user.streakDays > 7 ? 'high' : user.streakDays > 3 ? 'medium' : 'low',
      'category_balance': _calculateCategoryBalance(user.categories),
    };
    
    return patterns;
  }

  /// Build comprehensive training data for coaches (especially Health coaches)
  static Future<Map<String, dynamic>> _buildTrainingData(User user) async {
    try {
      final trainingStorage = TrainingStorageService();

      // Get all training sessions
      final allSessions = await trainingStorage.getAllSessions();
      final recentSessions = await trainingStorage.getRecentSessions(limit: 10);
      final currentProgram = await trainingStorage.loadProgram();

      // Enhanced Training Tracker Data Integration
      final enhancedTrainingData = await _buildEnhancedTrainingData(allSessions, currentProgram);

      // Calculate training statistics
      final now = DateTime.now();
      final last7Days = now.subtract(const Duration(days: 7));
      final last30Days = now.subtract(const Duration(days: 30));

      final sessionsLast7Days = allSessions
          .where((s) => s.completedAt != null && s.completedAt!.isAfter(last7Days))
          .toList();

      final sessionsLast30Days = allSessions
          .where((s) => s.completedAt != null && s.completedAt!.isAfter(last30Days))
          .toList();

      // Calculate total training time and EXP
      final totalTrainingSeconds = allSessions.fold<int>(0, (sum, s) => sum + s.durationSeconds);
      final totalTrainingExp = allSessions.fold<double>(0.0, (sum, s) => sum + s.expEarned);
      final last7DaysSeconds = sessionsLast7Days.fold<int>(0, (sum, s) => sum + s.durationSeconds);
      final last30DaysSeconds = sessionsLast30Days.fold<int>(0, (sum, s) => sum + s.durationSeconds);

      // Analyze training patterns
      final trainingLabels = allSessions.map((s) => s.label).toSet().toList();
      final labelFrequency = <String, int>{};
      for (final session in allSessions) {
        labelFrequency[session.label] = (labelFrequency[session.label] ?? 0) + 1;
      }

      // Find most frequent training type
      final mostFrequentLabel = labelFrequency.isNotEmpty
          ? labelFrequency.entries.reduce((a, b) => a.value > b.value ? a : b).key
          : null;

      // Analyze bodyweight tracking
      final bodyweightSessions = allSessions.where((s) => s.bodyweightKg != null).toList();
      final latestBodyweight = bodyweightSessions.isNotEmpty ? bodyweightSessions.first.bodyweightKg : null;

      // Calculate training consistency
      final trainingDays = allSessions
          .where((s) => s.completedAt != null)
          .map((s) => DateTime(s.completedAt!.year, s.completedAt!.month, s.completedAt!.day))
          .toSet()
          .length;

      return {
        'overview': {
          'total_sessions': allSessions.length,
          'total_training_time_hours': (totalTrainingSeconds / 3600).toStringAsFixed(1),
          'total_training_exp': totalTrainingExp,
          'training_days_count': trainingDays,
          'has_training_data': allSessions.isNotEmpty,
        },
        'recent_activity': {
          'sessions_last_7_days': sessionsLast7Days.length,
          'sessions_last_30_days': sessionsLast30Days.length,
          'hours_last_7_days': (last7DaysSeconds / 3600).toStringAsFixed(1),
          'hours_last_30_days': (last30DaysSeconds / 3600).toStringAsFixed(1),
          'last_session_date': allSessions.isNotEmpty && allSessions.first.completedAt != null
              ? DateFormatter.formatDateTime(allSessions.first.completedAt!)
              : 'No sessions yet',
        },
        'training_program': {
          'program_type': currentProgram.programType,
          'current_cycle': currentProgram.currentLabel,
          'available_cycles': currentProgram.workoutLabels,
          'cycle_index': currentProgram.currentIndex,
        },
        'training_patterns': {
          'unique_training_labels': trainingLabels,
          'most_frequent_training': mostFrequentLabel,
          'label_frequency': labelFrequency,
          'average_session_duration_minutes': allSessions.isNotEmpty
              ? (totalTrainingSeconds / allSessions.length / 60).round()
              : 0,
        },
        'bodyweight_tracking': {
          'tracks_bodyweight': bodyweightSessions.isNotEmpty,
          'latest_bodyweight_kg': latestBodyweight,
          'bodyweight_sessions_count': bodyweightSessions.length,
        },
        'recent_sessions': recentSessions.take(5).map((session) => {
          'label': session.label,
          'duration_minutes': (session.durationSeconds / 60).round(),
          'exp_earned': session.expEarned,
          'date': session.completedAt != null
              ? DateFormatter.formatDateTime(session.completedAt!)
              : 'In progress',
          'has_notes': session.notes.isNotEmpty,
          'notes_preview': session.notes.length > 50
              ? '${session.notes.substring(0, 50)}...'
              : session.notes,
          'current_goal': session.currentGoal,
          'has_goal': session.currentGoal.isNotEmpty,
          'bodyweight_kg': session.bodyweightKg,
        }).toList(),
        'health_insights': {
          'training_consistency': _calculateTrainingConsistency(allSessions),
          'weekly_training_trend': _calculateWeeklyTrend(allSessions),
          'preferred_training_time': _getPreferredTrainingTime(allSessions),
          'training_intensity': _calculateTrainingIntensity(allSessions),
        },
        // Enhanced Training Tracker Data
        'enhanced_features': enhancedTrainingData,
      };
    } catch (e) {
      if (kDebugMode) print('❌ Failed to build training data: $e');
      return {
        'overview': {
          'total_sessions': 0,
          'total_training_time_hours': '0.0',
          'total_training_exp': 0,
          'training_days_count': 0,
          'has_training_data': false,
        },
        'error': 'Failed to load training data',
      };
    }
  }

  /// Build enhanced training data with all new features
  static Future<Map<String, dynamic>> _buildEnhancedTrainingData(
    List<dynamic> allSessions,
    dynamic currentProgram
  ) async {
    try {
      // Convert sessions to proper type
      final sessions = allSessions.cast<TrainingSession>();

      // Build enhanced analytics data (simplified for coach context)
      final now = DateTime.now();
      final weekStart = now.subtract(Duration(days: now.weekday - 1));
      final monthStart = DateTime(now.year, now.month, 1);

      // Weekly analytics
      final weekSessions = sessions.where((s) =>
        s.createdAt.isAfter(weekStart) && s.createdAt.isBefore(weekStart.add(Duration(days: 7)))
      ).toList();

      // Monthly analytics
      final monthSessions = sessions.where((s) =>
        s.createdAt.isAfter(monthStart) && s.createdAt.isBefore(DateTime(now.year, now.month + 1, 1))
      ).toList();

      // Calculate personal records
      final longestSession = sessions.isNotEmpty ?
        sessions.reduce((a, b) => a.durationSeconds > b.durationSeconds ? a : b) : null;
      final mostExpSession = sessions.isNotEmpty ?
        sessions.reduce((a, b) => a.expEarned > b.expEarned ? a : b) : null;

      // Calculate basic metrics
      final weekTotalDuration = weekSessions.fold<int>(0, (sum, s) => sum + s.durationSeconds);
      final weekTotalExp = weekSessions.fold<double>(0, (sum, s) => sum + s.expEarned);
      final monthTotalDuration = monthSessions.fold<int>(0, (sum, s) => sum + s.durationSeconds);
      final monthTotalExp = monthSessions.fold<double>(0, (sum, s) => sum + s.expEarned);

      return {
        'analytics': {
          'weekly': {
            'total_sessions': weekSessions.length,
            'total_duration_minutes': (weekTotalDuration / 60).round(),
            'total_exp': weekTotalExp,
            'average_duration_minutes': weekSessions.isNotEmpty ? (weekTotalDuration / weekSessions.length / 60).round() : 0,
          },
          'monthly': {
            'total_sessions': monthSessions.length,
            'total_duration_minutes': (monthTotalDuration / 60).round(),
            'total_exp': monthTotalExp,
            'average_duration_minutes': monthSessions.isNotEmpty ? (monthTotalDuration / monthSessions.length / 60).round() : 0,
          },
          'personal_records': {
            'longest_session_minutes': longestSession != null ? (longestSession.durationSeconds / 60).round() : 0,
            'most_exp_session': mostExpSession?.expEarned ?? 0,
            'total_sessions_all_time': sessions.length,
          },
        },
        'program_data': {
          'current_program_type': currentProgram?.programType ?? 'Unknown',
          'current_label': currentProgram?.currentLabel ?? 'Unknown',
          'workout_labels': currentProgram?.workoutLabels ?? [],
          'has_notes_templates': currentProgram?.notesTemplates?.isNotEmpty ?? false,
          'notes_templates_count': currentProgram?.notesTemplates?.length ?? 0,
        },
        'enhanced_features_available': {
          'analytics_dashboard': true,
          'performance_metrics': true,
          'training_calendar': true,
          'workout_templates': true,
          'multi_phase_timer': true,
          'smart_goals': true,
          'enhanced_comparison': true,
          'competition_features': true,
        },
      };
    } catch (e) {
      if (kDebugMode) print('❌ Failed to build enhanced training data: $e');
      return {
        'error': 'Enhanced training data unavailable',
        'fallback': 'Using basic training data only',
      };
    }
  }

  // Helper methods
  static Map<String, dynamic>? _getCachedContext(String userId) {
    final cached = _contextCache[userId];
    if (cached != null && !cached.isExpired) {
      return cached.context;
    }
    return null;
  }

  static Future<void> _cacheContext(String userId, Map<String, dynamic> context) async {
    _contextCache[userId] = CachedContext(
      context: context,
      timestamp: DateTime.now(),
    );

    // Also persist to storage for cross-session caching
    try {
      // Create a serializable copy of the context
      final serializableContext = _makeSerializable(context);

      await _storage.write(
        key: '${_contextCacheKey}_$userId',
        value: jsonEncode({
          'context': serializableContext,
          'timestamp': DateTime.now().toIso8601String(),
        }),
      );
    } catch (e) {
      if (kDebugMode) print('❌ Failed to persist context cache: $e');
    }
  }

  /// Make a map fully serializable by converting non-serializable objects
  static Map<String, dynamic> _makeSerializable(Map<String, dynamic> map) {
    final result = <String, dynamic>{};

    for (final entry in map.entries) {
      final key = entry.key;
      final value = entry.value;

      if (value == null) {
        result[key] = null;
      } else if (value is String || value is num || value is bool) {
        result[key] = value;
      } else if (value is DateTime) {
        result[key] = value.toIso8601String();
      } else if (value is List) {
        result[key] = value.map((item) =>
          item is Map<String, dynamic> ? _makeSerializable(item) : item.toString()
        ).toList();
      } else if (value is Map<String, dynamic>) {
        result[key] = _makeSerializable(value);
      } else {
        // Convert any other object to string
        result[key] = value.toString();
      }
    }

    return result;
  }

  static Map<String, dynamic> _getFallbackContext(User user) {
    return {
      'user_profile': {
        'basic_info': {
          'username': user.username,
          'gender': user.gender,
          'total_exp': user.exp,
          'current_level': user.level,
        },
      },
      'error': 'Failed to build full context, using fallback',
      'context_metadata': {
        'generated_at': DateTime.now().toIso8601String(),
        'data_freshness': 'fallback',
        'context_version': '1.0',
      },
    };
  }

  static int _getHabitsCompletedToday(User user) {
    return user.dailyHabits.where(_isHabitCompletedToday).length;
  }

  static bool _isHabitCompletedToday(habit) {
    if (habit.lastCompleted == null) return false;
    final today = DateTime.now();
    final completed = habit.lastCompleted!;
    return completed.year == today.year &&
           completed.month == today.month &&
           completed.day == today.day;
  }

  static String _getMostActiveCategory(List<Map<String, dynamic>> entries) {
    if (entries.isEmpty) return 'none';
    
    final categoryCounts = <String, int>{};
    for (final entry in entries) {
      final category = entry['category'] as String;
      categoryCounts[category] = (categoryCounts[category] ?? 0) + 1;
    }
    
    return categoryCounts.entries
        .reduce((a, b) => a.value > b.value ? a : b)
        .key;
  }

  static String _calculateEngagementLevel(User user) {
    final recentEntries = user.diaryEntries
        .where((entry) => DateTime.now().difference(entry.timestamp).inDays <= 7)
        .length;
    
    if (recentEntries >= 10) return 'high';
    if (recentEntries >= 5) return 'medium';
    if (recentEntries >= 1) return 'low';
    return 'inactive';
  }

  static String _getLastActivityTimestamp(User user) {
    DateTime? lastActivity = user.lastLoginAt;
    
    // Check diary entries
    if (user.diaryEntries.isNotEmpty) {
      final lastDiary = user.diaryEntries.first.timestamp;
      if (lastActivity.isBefore(lastDiary)) {
        lastActivity = lastDiary;
      }
    }
    
    // Check North Star logs
    if (user.northStarQuest?.logs.isNotEmpty == true) {
      final lastNS = user.northStarQuest!.logs.first.loggedAt;
      if (lastActivity.isBefore(lastNS)) {
        lastActivity = lastNS;
      }
    }
    
    return lastActivity.toIso8601String();
  }

  static String _calculateCategoryBalance(Map<String, int> categories) {
    if (categories.isEmpty) return 'none';
    
    final values = categories.values.toList();
    final max = values.reduce((a, b) => a > b ? a : b);
    final min = values.reduce((a, b) => a < b ? a : b);
    
    if (max == 0) return 'none';
    
    final ratio = min / max;
    if (ratio >= 0.7) return 'balanced';
    if (ratio >= 0.4) return 'moderate';
    return 'imbalanced';
  }

  /// Clear cache for a specific user
  static Future<void> clearUserCache(String userId) async {
    _contextCache.remove(userId);
    await _storage.delete(key: '${_contextCacheKey}_$userId');
  }

  /// Clear all cached contexts
  static Future<void> clearAllCache() async {
    _contextCache.clear();
    
    try {
      final allKeys = await _storage.readAll();
      for (final key in allKeys.keys) {
        if (key.startsWith(_contextCacheKey)) {
          await _storage.delete(key: key);
        }
      }
    } catch (e) {
      if (kDebugMode) print('❌ Failed to clear context cache: $e');
    }
  }

  /// Calculate training consistency score (0-100)
  static String _calculateTrainingConsistency(List<TrainingSession> sessions) {
    if (sessions.isEmpty) return 'no_data';

    final completedSessions = sessions.where((s) => s.completedAt != null).toList();
    if (completedSessions.isEmpty) return 'no_data';

    // Calculate days with training in last 30 days
    final now = DateTime.now();
    final last30Days = now.subtract(const Duration(days: 30));
    final recentSessions = completedSessions
        .where((s) => s.completedAt!.isAfter(last30Days))
        .toList();

    final trainingDays = recentSessions
        .map((s) => DateTime(s.completedAt!.year, s.completedAt!.month, s.completedAt!.day))
        .toSet()
        .length;

    final consistencyScore = (trainingDays / 30 * 100).round();

    if (consistencyScore >= 80) return 'excellent';
    if (consistencyScore >= 60) return 'good';
    if (consistencyScore >= 40) return 'moderate';
    if (consistencyScore >= 20) return 'low';
    return 'very_low';
  }

  /// Calculate weekly training trend
  static String _calculateWeeklyTrend(List<TrainingSession> sessions) {
    if (sessions.isEmpty) return 'no_data';

    final now = DateTime.now();
    final thisWeek = now.subtract(const Duration(days: 7));
    final lastWeek = now.subtract(const Duration(days: 14));

    final thisWeekSessions = sessions
        .where((s) => s.completedAt != null && s.completedAt!.isAfter(thisWeek))
        .length;

    final lastWeekSessions = sessions
        .where((s) => s.completedAt != null &&
                     s.completedAt!.isAfter(lastWeek) &&
                     s.completedAt!.isBefore(thisWeek))
        .length;

    if (lastWeekSessions == 0) return thisWeekSessions > 0 ? 'improving' : 'no_data';

    final change = thisWeekSessions - lastWeekSessions;
    if (change > 0) return 'improving';
    if (change < 0) return 'declining';
    return 'stable';
  }

  /// Get preferred training time of day
  static String _getPreferredTrainingTime(List<TrainingSession> sessions) {
    if (sessions.isEmpty) return 'no_data';

    final completedSessions = sessions.where((s) => s.completedAt != null).toList();
    if (completedSessions.isEmpty) return 'no_data';

    final hourCounts = <int, int>{};
    for (final session in completedSessions) {
      final hour = session.completedAt!.hour;
      hourCounts[hour] = (hourCounts[hour] ?? 0) + 1;
    }

    if (hourCounts.isEmpty) return 'no_data';

    final mostActiveHour = hourCounts.entries
        .reduce((a, b) => a.value > b.value ? a : b)
        .key;

    if (mostActiveHour >= 5 && mostActiveHour < 12) return 'morning';
    if (mostActiveHour >= 12 && mostActiveHour < 17) return 'afternoon';
    if (mostActiveHour >= 17 && mostActiveHour < 21) return 'evening';
    return 'night';
  }

  /// Calculate training intensity level
  static String _calculateTrainingIntensity(List<TrainingSession> sessions) {
    if (sessions.isEmpty) return 'no_data';

    final avgDurationMinutes = sessions.fold<int>(0, (sum, s) => sum + s.durationSeconds) /
                              sessions.length / 60;

    if (avgDurationMinutes >= 90) return 'high';
    if (avgDurationMinutes >= 60) return 'moderate';
    if (avgDurationMinutes >= 30) return 'light';
    return 'very_light';
  }
}

/// Cached context with expiry
class CachedContext {
  final Map<String, dynamic> context;
  final DateTime timestamp;

  CachedContext({
    required this.context,
    required this.timestamp,
  });

  bool get isExpired => DateTime.now().difference(timestamp) > CoachContextService._cacheExpiry;
}
