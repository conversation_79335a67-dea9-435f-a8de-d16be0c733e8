import '../services/bulletproof_storage_service.dart';
import '../services/comprehensive_logging_service.dart';

/// 🔐 Bulletproof Secure Storage
/// 
/// A drop-in replacement for FlutterSecureStorage that uses our bulletproof
/// storage system instead of the problematic macOS keychain.
/// 
/// Features:
/// - Same API as FlutterSecureStorage
/// - Zero entitlement dependencies
/// - Cross-platform compatibility
/// - Automatic encryption
/// - Bulletproof reliability
class BulletproofSecureStorage {
  static final BulletproofSecureStorage _instance = BulletproofSecureStorage._internal();
  factory BulletproofSecureStorage() => _instance;
  BulletproofSecureStorage._internal();

  final BulletproofStorageService _storage = BulletproofStorageService();
  bool _isInitialized = false;

  /// Initialize the secure storage
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      if (!_storage.isReady) {
        await _storage.initialize();
      }
      _isInitialized = true;
      await ComprehensiveLoggingService.logInfo('🔐 Bulletproof secure storage initialized');
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Failed to initialize bulletproof secure storage: $e');
    }
  }

  /// Write a key-value pair to secure storage
  Future<void> write({required String key, required String value}) async {
    await _ensureInitialized();
    
    try {
      // Add secure prefix to distinguish from regular storage
      final secureKey = 'secure_$key';
      final success = await _storage.write(secureKey, value);
      
      if (success) {
        await ComprehensiveLoggingService.logInfo('🔐 Secure data written: $key');
      } else {
        await ComprehensiveLoggingService.logWarning('⚠️ Failed to write secure data: $key');
      }
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Error writing secure data: $e');
      rethrow;
    }
  }

  /// Read a value from secure storage
  Future<String?> read({required String key}) async {
    await _ensureInitialized();
    
    try {
      // Add secure prefix to distinguish from regular storage
      final secureKey = 'secure_$key';
      final value = await _storage.read(secureKey);
      
      if (value != null) {
        await ComprehensiveLoggingService.logInfo('🔐 Secure data read: $key');
      }
      
      return value;
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Error reading secure data: $e');
      return null;
    }
  }

  /// Delete a key from secure storage
  Future<void> delete({required String key}) async {
    await _ensureInitialized();
    
    try {
      // Add secure prefix to distinguish from regular storage
      final secureKey = 'secure_$key';
      final success = await _storage.delete(secureKey);
      
      if (success) {
        await ComprehensiveLoggingService.logInfo('🔐 Secure data deleted: $key');
      } else {
        await ComprehensiveLoggingService.logWarning('⚠️ Failed to delete secure data: $key');
      }
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Error deleting secure data: $e');
    }
  }

  /// Check if a key exists in secure storage
  Future<bool> containsKey({required String key}) async {
    await _ensureInitialized();
    
    try {
      // Add secure prefix to distinguish from regular storage
      final secureKey = 'secure_$key';
      return await _storage.exists(secureKey);
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Error checking secure key existence: $e');
      return false;
    }
  }

  /// Get all keys from secure storage
  Future<Set<String>> readAll() async {
    await _ensureInitialized();
    
    try {
      final allKeys = await _storage.getAllKeys();
      
      // Filter only secure keys and remove the prefix
      final secureKeys = allKeys
          .where((key) => key.startsWith('secure_'))
          .map((key) => key.substring(7)) // Remove 'secure_' prefix
          .toSet();
      
      await ComprehensiveLoggingService.logInfo('🔐 Retrieved ${secureKeys.length} secure keys');
      return secureKeys;
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Error reading all secure keys: $e');
      return <String>{};
    }
  }

  /// Delete all data from secure storage
  Future<void> deleteAll() async {
    await _ensureInitialized();
    
    try {
      final allKeys = await _storage.getAllKeys();
      
      // Delete only secure keys
      final secureKeys = allKeys.where((key) => key.startsWith('secure_'));
      
      int deletedCount = 0;
      for (final key in secureKeys) {
        final success = await _storage.delete(key);
        if (success) deletedCount++;
      }
      
      await ComprehensiveLoggingService.logInfo('🔐 Deleted $deletedCount secure keys');
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Error deleting all secure data: $e');
    }
  }

  /// Ensure the storage is initialized
  Future<void> _ensureInitialized() async {
    if (!_isInitialized) {
      await initialize();
    }
    
    if (!_storage.isReady) {
      throw Exception('Bulletproof storage is not ready');
    }
  }

  /// Get storage health status
  Map<String, dynamic> getHealthStatus() {
    return {
      'isInitialized': _isInitialized,
      'storageReady': _storage.isReady,
      'storageHealth': _storage.getHealthStatus(),
    };
  }

  /// Check if secure storage is ready
  bool get isReady => _isInitialized && _storage.isReady;
}

/// 🔄 Migration Helper
/// 
/// Helps migrate data from FlutterSecureStorage to BulletproofSecureStorage
class SecureStorageMigrator {
  static final BulletproofSecureStorage _bulletproofStorage = BulletproofSecureStorage();
  
  /// Migrate a single key from FlutterSecureStorage to BulletproofSecureStorage
  static Future<bool> migrateKey(String key, String? value) async {
    try {
      if (value != null) {
        await _bulletproofStorage.write(key: key, value: value);
        await ComprehensiveLoggingService.logInfo('🔄 Migrated secure key: $key');
        return true;
      }
      return false;
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Failed to migrate key $key: $e');
      return false;
    }
  }
  
  /// Migrate multiple keys at once
  static Future<int> migrateKeys(Map<String, String> keyValuePairs) async {
    int migratedCount = 0;
    
    for (final entry in keyValuePairs.entries) {
      final success = await migrateKey(entry.key, entry.value);
      if (success) migratedCount++;
    }
    
    await ComprehensiveLoggingService.logInfo('🔄 Migration completed: $migratedCount/${keyValuePairs.length} keys migrated');
    return migratedCount;
  }
  
  /// Check migration status
  static Future<Map<String, dynamic>> getMigrationStatus() async {
    try {
      final bulletproofKeys = await _bulletproofStorage.readAll();
      
      return {
        'bulletproofKeysCount': bulletproofKeys.length,
        'migrationComplete': bulletproofKeys.isNotEmpty,
        'availableKeys': bulletproofKeys.toList(),
      };
    } catch (e) {
      return {
        'error': e.toString(),
        'migrationComplete': false,
      };
    }
  }
}

/// 🔧 Compatibility Layer
/// 
/// Provides a const constructor for compatibility with existing code
class FlutterSecureStorage {
  const FlutterSecureStorage();
  
  static final BulletproofSecureStorage _bulletproof = BulletproofSecureStorage();
  
  Future<void> write({required String key, required String value}) async {
    await _bulletproof.write(key: key, value: value);
  }
  
  Future<String?> read({required String key}) async {
    return await _bulletproof.read(key: key);
  }
  
  Future<void> delete({required String key}) async {
    await _bulletproof.delete(key: key);
  }
  
  Future<bool> containsKey({required String key}) async {
    return await _bulletproof.containsKey(key: key);
  }
  
  Future<Map<String, String>> readAll() async {
    final keys = await _bulletproof.readAll();
    final Map<String, String> result = {};
    
    for (final key in keys) {
      final value = await _bulletproof.read(key: key);
      if (value != null) {
        result[key] = value;
      }
    }
    
    return result;
  }
  
  Future<void> deleteAll() async {
    await _bulletproof.deleteAll();
  }
}
