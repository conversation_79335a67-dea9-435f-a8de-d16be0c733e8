// lib/services/content_synthesis_service.dart

import 'dart:math';
import 'package:flutter/foundation.dart';
import 'transcript_search_service.dart';

/// Service for synthesizing multiple transcript insights into coherent coach responses
/// Combines knowledge from various sources while maintaining coach personality
class ContentSynthesisService {
  
  /// Enhanced synthesis for guaranteed high-quality responses
  static Future<SynthesizedContent> synthesizeForCoach({
    required String userMessage,
    required String category,
    required String coachName,
    int maxInsights = 8, // Increased from 3 to 8
    double minRelevanceThreshold = 0.7, // Raised from 0.4 to 0.7
  }) async {
    try {
      // Multi-pass search strategy for comprehensive coverage
      final allResults = <SearchResult>[];

      // Pass 1: Direct search with original query
      final directResults = await TranscriptSearchService.searchRelevantContent(
        userQuery: userMessage,
        category: category,
        maxResults: maxInsights,
        minRelevanceScore: minRelevanceThreshold,
      );
      allResults.addAll(directResults);

      // Pass 2: Enhanced keyword search if we need more content
      if (allResults.length < maxInsights) {
        final enhancedResults = await _performEnhancedKeywordSearch(
          userMessage: userMessage,
          category: category,
          maxResults: maxInsights - allResults.length,
          minRelevanceThreshold: minRelevanceThreshold * 0.8, // Slightly lower threshold
        );
        allResults.addAll(enhancedResults);
      }

      // Pass 3: Cross-domain search for holistic insights
      if (allResults.length < maxInsights) {
        final crossDomainResults = await _performCrossDomainSearch(
          userMessage: userMessage,
          originalCategory: category,
          maxResults: maxInsights - allResults.length,
          minRelevanceThreshold: minRelevanceThreshold * 0.7,
        );
        allResults.addAll(crossDomainResults);
      }

      if (allResults.isEmpty) {
        return SynthesizedContent.empty();
      }

      // Remove duplicates and select best insights
      final uniqueResults = _removeDuplicateResults(allResults);
      final insights = _selectBestInsights(uniqueResults, maxInsights);

      // Enhanced synthesis with cross-domain connections
      final synthesizedKnowledge = _synthesizeInsightsEnhanced(insights, userMessage);

      // Create comprehensive coaching guidance
      final coachingGuidance = _createEnhancedCoachingGuidance(
        insights: insights,
        userMessage: userMessage,
        category: category,
      );

      return SynthesizedContent(
        primaryInsights: insights,
        synthesizedKnowledge: synthesizedKnowledge,
        coachingGuidance: coachingGuidance,
        relevanceScore: _calculateOverallRelevance(insights),
        sourceCount: insights.length,
      );

    } catch (e) {
      if (kDebugMode) print('❌ Enhanced content synthesis failed: $e');
      return SynthesizedContent.empty();
    }
  }

  /// Create synthesis from pre-filtered search results (for guarantee service)
  static Future<SynthesizedContent> createSynthesisFromResults({
    required List<SearchResult> results,
    required String userMessage,
    required String category,
  }) async {
    try {
      if (results.isEmpty) {
        return SynthesizedContent.empty();
      }

      // Convert search results to insights
      final insights = results.map((result) => TranscriptInsight(
        content: result.content,
        source: result.source,
        relevanceScore: result.relevanceScore,
        keywords: result.keywords,
        context: result.context,
      )).toList();

      // Synthesize into coherent knowledge
      final synthesizedKnowledge = _synthesizeInsights(insights, userMessage);

      // Create coaching guidance
      final coachingGuidance = _createCoachingGuidance(
        insights: insights,
        userMessage: userMessage,
        category: category,
      );

      return SynthesizedContent(
        primaryInsights: insights,
        synthesizedKnowledge: synthesizedKnowledge,
        coachingGuidance: coachingGuidance,
        relevanceScore: _calculateOverallRelevance(insights),
        sourceCount: insights.length,
      );

    } catch (e) {
      if (kDebugMode) print('❌ Content synthesis from results failed: $e');
      return SynthesizedContent.empty();
    }
  }

  /// Select the best insights from search results
  static List<TranscriptInsight> _selectBestInsights(
    List<SearchResult> searchResults,
    int maxInsights,
  ) {
    final insights = <TranscriptInsight>[];
    final usedContent = <String>{};

    for (final result in searchResults) {
      if (insights.length >= maxInsights) break;
      
      // Avoid duplicate or very similar content
      if (_isContentUnique(result.content, usedContent)) {
        insights.add(TranscriptInsight(
          content: result.content,
          relevanceScore: result.relevanceScore,
          context: result.context,
          keywords: result.keywords,
          source: result.source,
        ));
        
        usedContent.add(_getContentSignature(result.content));
      }
    }

    return insights;
  }

  /// Check if content is unique enough to include
  static bool _isContentUnique(String content, Set<String> usedContent) {
    final signature = _getContentSignature(content);
    
    // Check for exact duplicates
    if (usedContent.contains(signature)) return false;
    
    // Check for high similarity (simple approach)
    for (final used in usedContent) {
      if (_calculateSimilarity(signature, used) > 0.7) {
        return false;
      }
    }
    
    return true;
  }

  /// Get a signature for content similarity checking
  static String _getContentSignature(String content) {
    return content
        .toLowerCase()
        .replaceAll(RegExp(r'[^\w\s]'), '')
        .split(RegExp(r'\s+'))
        .where((word) => word.length > 3)
        .take(10)
        .join(' ');
  }

  /// Calculate similarity between two content signatures
  static double _calculateSimilarity(String sig1, String sig2) {
    final words1 = sig1.split(' ').toSet();
    final words2 = sig2.split(' ').toSet();
    
    final intersection = words1.intersection(words2).length;
    final union = words1.union(words2).length;
    
    return union > 0 ? intersection / union : 0.0;
  }

  /// Synthesize multiple insights into coherent knowledge
  static String _synthesizeInsights(List<TranscriptInsight> insights, String userMessage) {
    if (insights.isEmpty) return '';
    
    final themes = _extractCommonThemes(insights);
    final keyPoints = _extractKeyPoints(insights);
    
    // Create a synthesized knowledge summary
    final synthesis = StringBuffer();
    
    if (themes.isNotEmpty) {
      synthesis.write('Key themes: ${themes.join(', ')}. ');
    }
    
    if (keyPoints.isNotEmpty) {
      synthesis.write('Important insights: ');
      for (int i = 0; i < keyPoints.length; i++) {
        if (i > 0) synthesis.write(' Additionally, ');
        synthesis.write(keyPoints[i]);
        if (i < keyPoints.length - 1) synthesis.write('.');
      }
    }
    
    return synthesis.toString().trim();
  }

  /// Extract common themes from insights
  static List<String> _extractCommonThemes(List<TranscriptInsight> insights) {
    final themeCount = <String, int>{};
    
    for (final insight in insights) {
      // Count context themes
      themeCount[insight.context] = (themeCount[insight.context] ?? 0) + 1;
      
      // Count keyword themes
      for (final keyword in insight.keywords) {
        if (keyword.length > 4) {
          themeCount[keyword] = (themeCount[keyword] ?? 0) + 1;
        }
      }
    }
    
    // Return themes that appear in multiple insights
    return themeCount.entries
        .where((entry) => entry.value > 1)
        .map((entry) => entry.key)
        .take(3)
        .toList();
  }

  /// Extract key points from insights
  static List<String> _extractKeyPoints(List<TranscriptInsight> insights) {
    final keyPoints = <String>[];
    
    for (final insight in insights) {
      // Extract the most important sentence or concept
      final sentences = insight.content.split(RegExp(r'[.!?]+'));
      
      for (final sentence in sentences) {
        final trimmed = sentence.trim();
        if (trimmed.length > 20 && trimmed.length < 200) {
          // Look for actionable or insightful sentences
          if (_isKeyPoint(trimmed)) {
            keyPoints.add(trimmed);
            break; // One key point per insight
          }
        }
      }
    }
    
    return keyPoints.take(3).toList();
  }

  /// Determine if a sentence contains a key point
  static bool _isKeyPoint(String sentence) {
    final indicators = [
      'important', 'key', 'crucial', 'essential', 'fundamental', 'critical',
      'should', 'must', 'need to', 'have to', 'remember', 'focus on',
      'strategy', 'approach', 'method', 'technique', 'principle', 'rule',
      'because', 'reason', 'why', 'how', 'what', 'when', 'where',
    ];
    
    final lowerSentence = sentence.toLowerCase();
    return indicators.any((indicator) => lowerSentence.contains(indicator));
  }

  /// Create coaching guidance based on insights
  static CoachingGuidance _createCoachingGuidance({
    required List<TranscriptInsight> insights,
    required String userMessage,
    required String category,
  }) {
    final actionableAdvice = <String>[];
    final supportingEvidence = <String>[];
    final encouragement = <String>[];
    
    for (final insight in insights) {
      // Categorize insight content
      if (_containsActionableAdvice(insight.content)) {
        actionableAdvice.add(_extractActionableAdvice(insight.content));
      }
      
      if (_containsEvidence(insight.content)) {
        supportingEvidence.add(_extractEvidence(insight.content));
      }
      
      if (_containsEncouragement(insight.content)) {
        encouragement.add(_extractEncouragement(insight.content));
      }
    }
    
    return CoachingGuidance(
      actionableAdvice: actionableAdvice.take(2).toList(),
      supportingEvidence: supportingEvidence.take(2).toList(),
      encouragement: encouragement.take(1).toList(),
      category: category,
    );
  }

  /// Check if content contains actionable advice
  static bool _containsActionableAdvice(String content) {
    final actionWords = ['do', 'try', 'start', 'stop', 'avoid', 'practice', 'implement', 'focus', 'work on'];
    final lowerContent = content.toLowerCase();
    return actionWords.any((word) => lowerContent.contains(word));
  }

  /// Extract actionable advice from content
  static String _extractActionableAdvice(String content) {
    final sentences = content.split(RegExp(r'[.!?]+'));
    for (final sentence in sentences) {
      if (_containsActionableAdvice(sentence) && sentence.trim().length > 20) {
        return sentence.trim();
      }
    }
    return content.substring(0, min(150, content.length)).trim();
  }

  /// Check if content contains supporting evidence
  static bool _containsEvidence(String content) {
    final evidenceWords = ['research', 'study', 'data', 'evidence', 'proven', 'shows', 'demonstrates'];
    final lowerContent = content.toLowerCase();
    return evidenceWords.any((word) => lowerContent.contains(word));
  }

  /// Extract evidence from content
  static String _extractEvidence(String content) {
    final sentences = content.split(RegExp(r'[.!?]+'));
    for (final sentence in sentences) {
      if (_containsEvidence(sentence) && sentence.trim().length > 20) {
        return sentence.trim();
      }
    }
    return content.substring(0, min(150, content.length)).trim();
  }

  /// Check if content contains encouragement
  static bool _containsEncouragement(String content) {
    final encouragementWords = ['can', 'will', 'able', 'possible', 'achieve', 'success', 'believe', 'confidence'];
    final lowerContent = content.toLowerCase();
    return encouragementWords.any((word) => lowerContent.contains(word));
  }

  /// Extract encouragement from content
  static String _extractEncouragement(String content) {
    final sentences = content.split(RegExp(r'[.!?]+'));
    for (final sentence in sentences) {
      if (_containsEncouragement(sentence) && sentence.trim().length > 20) {
        return sentence.trim();
      }
    }
    return content.substring(0, min(100, content.length)).trim();
  }

  /// Calculate overall relevance score
  static double _calculateOverallRelevance(List<TranscriptInsight> insights) {
    if (insights.isEmpty) return 0.0;

    final totalScore = insights.fold<double>(0.0, (sum, insight) => sum + insight.relevanceScore);
    return totalScore / insights.length;
  }

  /// Enhanced keyword search with expanded terms
  static Future<List<SearchResult>> _performEnhancedKeywordSearch({
    required String userMessage,
    required String category,
    required int maxResults,
    required double minRelevanceThreshold,
  }) async {
    final enhancedTerms = _extractEnhancedSearchTerms(userMessage);
    final allResults = <SearchResult>[];

    for (final term in enhancedTerms) {
      final results = await TranscriptSearchService.searchRelevantContent(
        userQuery: term,
        category: category,
        maxResults: 3,
        minRelevanceScore: minRelevanceThreshold,
      );
      allResults.addAll(results);
    }

    // Sort by relevance and return top results
    allResults.sort((a, b) => b.relevanceScore.compareTo(a.relevanceScore));
    return allResults.take(maxResults).toList();
  }

  /// Cross-domain search for holistic insights
  static Future<List<SearchResult>> _performCrossDomainSearch({
    required String userMessage,
    required String originalCategory,
    required int maxResults,
    required double minRelevanceThreshold,
  }) async {
    final otherCategories = ['health', 'wealth', 'purpose', 'connection']
        .where((cat) => cat != originalCategory.toLowerCase())
        .toList();

    final allResults = <SearchResult>[];

    for (final category in otherCategories) {
      final results = await TranscriptSearchService.searchRelevantContent(
        userQuery: userMessage,
        category: category,
        maxResults: 2,
        minRelevanceScore: minRelevanceThreshold,
      );
      allResults.addAll(results);
    }

    return allResults.take(maxResults).toList();
  }

  /// Remove duplicate search results
  static List<SearchResult> _removeDuplicateResults(List<SearchResult> results) {
    final seen = <String>{};
    return results.where((result) {
      final key = result.content.length > 100
          ? result.content.substring(0, 100)
          : result.content;
      if (seen.contains(key)) {
        return false;
      }
      seen.add(key);
      return true;
    }).toList();
  }

  /// Enhanced synthesis with cross-domain connections
  static String _synthesizeInsightsEnhanced(List<TranscriptInsight> insights, String userMessage) {
    if (insights.isEmpty) return '';

    final synthesis = StringBuffer();

    // Group insights by domain/source for better organization
    final groupedInsights = <String, List<TranscriptInsight>>{};
    for (final insight in insights) {
      final domain = _extractDomain(insight.source);
      groupedInsights.putIfAbsent(domain, () => []).add(insight);
    }

    // Create comprehensive synthesis
    synthesis.writeln('Based on comprehensive analysis across multiple domains:');

    for (final entry in groupedInsights.entries) {
      final domain = entry.key;
      final domainInsights = entry.value;

      synthesis.writeln('\n**$domain Insights:**');
      for (final insight in domainInsights.take(2)) {
        final key = _extractKeyInsight(insight.content);
        synthesis.writeln('• $key');
      }
    }

    // Add cross-domain connections
    if (groupedInsights.length > 1) {
      synthesis.writeln('\n**Integrated Approach:**');
      synthesis.writeln(_createCrossDomainConnections(groupedInsights, userMessage));
    }

    return synthesis.toString();
  }

  /// Create enhanced coaching guidance with more comprehensive advice
  static CoachingGuidance _createEnhancedCoachingGuidance({
    required List<TranscriptInsight> insights,
    required String userMessage,
    required String category,
  }) {
    final actionableAdvice = <String>[];
    final supportingEvidence = <String>[];
    final encouragement = <String>[];

    // Extract comprehensive guidance from all insights
    for (final insight in insights) {
      // Extract actionable advice
      if (_containsActionableAdvice(insight.content)) {
        final advice = _extractActionableAdvice(insight.content);
        if (advice.isNotEmpty && !actionableAdvice.contains(advice)) {
          actionableAdvice.add(advice);
        }
      }

      // Extract supporting evidence
      if (_containsEvidence(insight.content)) {
        final evidence = _extractEvidence(insight.content);
        if (evidence.isNotEmpty && !supportingEvidence.contains(evidence)) {
          supportingEvidence.add(evidence);
        }
      }

      // Extract encouragement
      if (_containsEncouragement(insight.content)) {
        final encourage = _extractEncouragement(insight.content);
        if (encourage.isNotEmpty && !encouragement.contains(encourage)) {
          encouragement.add(encourage);
        }
      }
    }

    // Ensure we have comprehensive guidance
    if (actionableAdvice.length < 3) {
      actionableAdvice.addAll(_generateAdditionalAdvice(insights, userMessage));
    }

    return CoachingGuidance(
      actionableAdvice: actionableAdvice.take(5).toList(), // Top 5 pieces of advice
      supportingEvidence: supportingEvidence.take(3).toList(),
      encouragement: encouragement.take(2).toList(),
      category: category,
    );
  }

  /// Extract enhanced search terms for broader coverage
  static List<String> _extractEnhancedSearchTerms(String userMessage) {
    final words = userMessage.toLowerCase().split(RegExp(r'\s+'));
    final enhancedTerms = <String>[];

    // Add original message
    enhancedTerms.add(userMessage);

    // Add individual important words (longer than 3 characters)
    enhancedTerms.addAll(words.where((word) => word.length > 3));

    // Add word combinations
    for (int i = 0; i < words.length - 1; i++) {
      enhancedTerms.add('${words[i]} ${words[i + 1]}');
    }

    // Add synonyms and related terms
    enhancedTerms.addAll(_generateSynonyms(userMessage));

    return enhancedTerms.take(15).toList(); // Limit for performance
  }

  /// Extract domain from source name
  static String _extractDomain(String source) {
    if (source.toLowerCase().contains('health') ||
        source.toLowerCase().contains('fitness') ||
        source.toLowerCase().contains('athlean') ||
        source.toLowerCase().contains('huberman')) {
      return 'Health & Fitness';
    } else if (source.toLowerCase().contains('business') ||
               source.toLowerCase().contains('wealth') ||
               source.toLowerCase().contains('ovens')) {
      return 'Business & Wealth';
    } else if (source.toLowerCase().contains('purpose') ||
               source.toLowerCase().contains('meaning') ||
               source.toLowerCase().contains('peterson')) {
      return 'Purpose & Meaning';
    } else if (source.toLowerCase().contains('connection') ||
               source.toLowerCase().contains('relationship')) {
      return 'Connection & Relationships';
    }
    return 'General Expertise';
  }

  /// Extract key insight from content
  static String _extractKeyInsight(String content) {
    final sentences = content.split(RegExp(r'[.!?]+'));
    for (final sentence in sentences) {
      if (sentence.trim().length > 30 && sentence.trim().length < 150) {
        return sentence.trim();
      }
    }
    return content.length > 100 ? '${content.substring(0, 100).trim()}...' : content.trim();
  }

  /// Create cross-domain connections
  static String _createCrossDomainConnections(Map<String, List<TranscriptInsight>> groupedInsights, String userMessage) {
    final connections = StringBuffer();
    final domains = groupedInsights.keys.toList();

    if (domains.length >= 2) {
      connections.writeln('The most effective approach combines insights from ${domains.join(' and ')}. ');
      connections.writeln('This holistic strategy addresses multiple aspects of your goal simultaneously.');
    }

    return connections.toString();
  }

  /// Generate additional advice when needed
  static List<String> _generateAdditionalAdvice(List<TranscriptInsight> insights, String userMessage) {
    final additionalAdvice = <String>[];

    // Extract any remaining actionable content
    for (final insight in insights) {
      final sentences = insight.content.split(RegExp(r'[.!?]+'));
      for (final sentence in sentences) {
        if (sentence.contains('should') || sentence.contains('must') ||
            sentence.contains('need to') || sentence.contains('important')) {
          final advice = sentence.trim();
          if (advice.length > 20 && !additionalAdvice.contains(advice)) {
            additionalAdvice.add(advice);
          }
        }
      }
    }

    return additionalAdvice;
  }

  /// Generate synonyms and related terms
  static List<String> _generateSynonyms(String userMessage) {
    final synonyms = <String>[];
    final message = userMessage.toLowerCase();

    // Add common synonyms for fitness/health terms
    if (message.contains('workout') || message.contains('exercise')) {
      synonyms.addAll(['training', 'fitness', 'routine', 'program']);
    }
    if (message.contains('nutrition') || message.contains('diet')) {
      synonyms.addAll(['eating', 'food', 'meal', 'nutrition']);
    }
    if (message.contains('business') || message.contains('money')) {
      synonyms.addAll(['entrepreneurship', 'wealth', 'income', 'profit']);
    }
    if (message.contains('motivation') || message.contains('mindset')) {
      synonyms.addAll(['psychology', 'mental', 'confidence', 'discipline']);
    }

    return synonyms;
  }
}

/// Represents a transcript insight with metadata
class TranscriptInsight {
  final String content;
  final double relevanceScore;
  final String context;
  final List<String> keywords;
  final String source;

  const TranscriptInsight({
    required this.content,
    required this.relevanceScore,
    required this.context,
    required this.keywords,
    required this.source,
  });
}

/// Represents synthesized content for coach responses
class SynthesizedContent {
  final List<TranscriptInsight> primaryInsights;
  final String synthesizedKnowledge;
  final CoachingGuidance coachingGuidance;
  final double relevanceScore;
  final int sourceCount;

  const SynthesizedContent({
    required this.primaryInsights,
    required this.synthesizedKnowledge,
    required this.coachingGuidance,
    required this.relevanceScore,
    required this.sourceCount,
  });

  factory SynthesizedContent.empty() {
    return const SynthesizedContent(
      primaryInsights: [],
      synthesizedKnowledge: '',
      coachingGuidance: CoachingGuidance.empty(),
      relevanceScore: 0.0,
      sourceCount: 0,
    );
  }

  bool get hasContent => primaryInsights.isNotEmpty;
  bool get isHighQuality => relevanceScore > 0.6 && sourceCount >= 2;

  /// Convert to JSON for caching
  Map<String, dynamic> toJson() {
    return {
      'primaryInsights': primaryInsights.map((insight) => {
        'content': insight.content,
        'relevanceScore': insight.relevanceScore,
        'context': insight.context,
        'keywords': insight.keywords,
        'source': insight.source,
      }).toList(),
      'synthesizedKnowledge': synthesizedKnowledge,
      'coachingGuidance': {
        'actionableAdvice': coachingGuidance.actionableAdvice,
        'supportingEvidence': coachingGuidance.supportingEvidence,
        'encouragement': coachingGuidance.encouragement,
        'category': coachingGuidance.category,
      },
      'relevanceScore': relevanceScore,
      'sourceCount': sourceCount,
    };
  }

  /// Create from JSON for caching
  factory SynthesizedContent.fromJson(Map<String, dynamic> json) {
    final insights = (json['primaryInsights'] as List? ?? []).map((insight) =>
      TranscriptInsight(
        content: insight['content'] ?? '',
        relevanceScore: insight['relevanceScore'] ?? 0.0,
        context: insight['context'] ?? '',
        keywords: List<String>.from(insight['keywords'] ?? []),
        source: insight['source'] ?? '',
      )
    ).toList();

    final guidanceJson = json['coachingGuidance'] as Map<String, dynamic>? ?? {};
    final guidance = CoachingGuidance(
      actionableAdvice: List<String>.from(guidanceJson['actionableAdvice'] ?? []),
      supportingEvidence: List<String>.from(guidanceJson['supportingEvidence'] ?? []),
      encouragement: List<String>.from(guidanceJson['encouragement'] ?? []),
      category: guidanceJson['category'] ?? '',
    );

    return SynthesizedContent(
      primaryInsights: insights,
      synthesizedKnowledge: json['synthesizedKnowledge'] ?? '',
      coachingGuidance: guidance,
      relevanceScore: json['relevanceScore'] ?? 0.0,
      sourceCount: json['sourceCount'] ?? 0,
    );
  }
}

/// Represents coaching guidance derived from transcript insights
class CoachingGuidance {
  final List<String> actionableAdvice;
  final List<String> supportingEvidence;
  final List<String> encouragement;
  final String category;

  const CoachingGuidance({
    required this.actionableAdvice,
    required this.supportingEvidence,
    required this.encouragement,
    required this.category,
  });

  const CoachingGuidance.empty()
      : actionableAdvice = const [],
        supportingEvidence = const [],
        encouragement = const [],
        category = '';

  bool get hasGuidance => 
      actionableAdvice.isNotEmpty || 
      supportingEvidence.isNotEmpty || 
      encouragement.isNotEmpty;
}
