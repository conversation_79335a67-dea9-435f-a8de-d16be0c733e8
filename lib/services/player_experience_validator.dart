// lib/services/player_experience_validator.dart

import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../models/user_model.dart';


import 'app_health_service.dart';
import 'continuous_monitoring_service.dart';

/// Player Experience Validator
/// 
/// This service validates EVERY player interaction to guarantee
/// a perfect experience every single time.
class PlayerExperienceValidator {
  static const FlutterSecureStorage _storage = FlutterSecureStorage();
  static const String _validationDataKey = 'player_experience_validation';
  
  static final Map<String, dynamic> _validationMetrics = {};
  static final List<String> _experienceIssues = [];
  static bool _isValidating = false;

  /// Start player experience validation
  static Future<void> startValidation() async {
    if (_isValidating) return;
    
    try {
      _isValidating = true;
      await _loadValidationData();
      
      if (kDebugMode) print('🎯 Player experience validation started');
    } catch (e) {
      if (kDebugMode) print('❌ Failed to start validation: $e');
      _isValidating = false;
    }
  }

  /// Validate player interaction
  static Future<PlayerInteractionResult> validatePlayerInteraction({
    required String interactionType,
    required Map<String, dynamic> interactionData,
    User? user,
  }) async {
    final result = PlayerInteractionResult(
      interactionType: interactionType,
      timestamp: DateTime.now(),
      isValid: true,
      issues: [],
      recommendations: [],
    );

    try {
      switch (interactionType) {
        case 'app_launch':
          await _validateAppLaunch(result, interactionData);
          break;
        case 'user_signup':
          await _validateUserSignup(result, interactionData, user);
          break;
        case 'coach_chat':
          await _validateCoachChat(result, interactionData, user);
          break;
        case 'coach_checkin':
          await _validateCoachCheckin(result, interactionData, user);
          break;
        case 'navigation':
          await _validateNavigation(result, interactionData);
          break;
        case 'data_save':
          await _validateDataSave(result, interactionData, user);
          break;
        default:
          result.issues.add('Unknown interaction type: $interactionType');
          result.isValid = false;
      }

      // Update metrics
      _updateValidationMetrics(result);
      
      // Save validation data
      await _saveValidationData();

      if (kDebugMode && !result.isValid) {
        print('🚨 Player experience issue detected: ${result.issues.join(", ")}');
      }

    } catch (e) {
      result.issues.add('Validation error: $e');
      result.isValid = false;
      
      if (kDebugMode) print('❌ Validation failed for $interactionType: $e');
    }

    return result;
  }

  /// Validate app launch experience
  static Future<void> _validateAppLaunch(
    PlayerInteractionResult result,
    Map<String, dynamic> data,
  ) async {
    // Check app launch time
    final launchTime = data['launchTime'] as int? ?? 0;
    if (launchTime > 5000) { // 5 seconds
      result.issues.add('App launch took too long: ${launchTime}ms');
      result.recommendations.add('Optimize app startup performance');
    }

    // Check system health
    final healthResult = await AppHealthService.performStartupHealthCheck();
    if (healthResult.overallHealth == HealthStatus.critical) {
      result.issues.add('System health is critical at launch');
      result.recommendations.add('Fix critical system issues');
    }

    // Check if monitoring is active
    if (!ContinuousMonitoringService.isSystemReadyForPlayers()) {
      result.issues.add('Monitoring system not ready for players');
      result.recommendations.add('Ensure monitoring system is active');
    }

    if (result.issues.isNotEmpty) {
      result.isValid = false;
    }
  }

  /// Validate user signup experience
  static Future<void> _validateUserSignup(
    PlayerInteractionResult result,
    Map<String, dynamic> data,
    User? user,
  ) async {
    // Check if user data is complete
    if (user == null) {
      result.issues.add('User signup completed but no user data provided');
      result.isValid = false;
      return;
    }

    // Validate required fields
    if (user.username.isEmpty) {
      result.issues.add('Username is empty after signup');
    }

    if (user.gender.isEmpty) {
      result.issues.add('Gender not selected during signup');
    }

    if (user.customCategories.length < 2) {
      result.issues.add('Custom categories not properly set');
    }

    // Check email validation if provided
    if (user.email?.isNotEmpty == true && !user.isEmailVerified) {
      result.issues.add('Email provided but not verified');
      result.recommendations.add('Ensure email verification process works');
    }

    // Check coach assignments
    if (user.assignedCoaches?.isEmpty == true) {
      result.issues.add('No coaches assigned after signup');
      result.recommendations.add('Verify coach assignment logic');
    }

    if (result.issues.isNotEmpty) {
      result.isValid = false;
    }
  }

  /// Validate coach chat experience
  static Future<void> _validateCoachChat(
    PlayerInteractionResult result,
    Map<String, dynamic> data,
    User? user,
  ) async {
    final message = data['message'] as String? ?? '';
    final category = data['category'] as String? ?? '';
    final response = data['response'] as String? ?? '';
    final responseTime = data['responseTime'] as int? ?? 0;

    // Check message input
    if (message.isEmpty) {
      result.issues.add('Empty message sent to coach');
    }

    // Check category
    if (category.isEmpty) {
      result.issues.add('No category specified for coach chat');
    }

    // Check response quality
    if (response.isEmpty) {
      result.issues.add('Coach provided empty response');
      result.isValid = false;
    } else {
      // Check response length (should be meaningful)
      if (response.length < 50) {
        result.issues.add('Coach response too short: ${response.length} characters');
        result.recommendations.add('Improve AI response quality');
      }

      // Check for error messages in response
      if (response.toLowerCase().contains('error') || 
          response.toLowerCase().contains('sorry, i cannot')) {
        result.issues.add('Coach response contains error message');
        result.recommendations.add('Fix AI service issues');
      }
    }

    // Check response time
    if (responseTime > 10000) { // 10 seconds
      result.issues.add('Coach response took too long: ${responseTime}ms');
      result.recommendations.add('Optimize AI response time');
    }

    if (result.issues.isNotEmpty) {
      result.isValid = false;
    }
  }

  /// Validate coach check-in experience
  static Future<void> _validateCoachCheckin(
    PlayerInteractionResult result,
    Map<String, dynamic> data,
    User? user,
  ) async {
    final checkinType = data['checkinType'] as String? ?? '';
    final question = data['question'] as String? ?? '';
    final coachCategory = data['coachCategory'] as String? ?? '';

    // Check check-in type
    if (checkinType.isEmpty) {
      result.issues.add('Check-in type not specified');
    }

    // Check question quality
    if (question.isEmpty) {
      result.issues.add('Check-in question is empty');
      result.isValid = false;
    } else {
      // Check question length
      if (question.length < 20) {
        result.issues.add('Check-in question too short');
        result.recommendations.add('Improve question generation');
      }

      // Check if question is personalized
      if (!question.contains('you') && !question.contains('your')) {
        result.issues.add('Check-in question not personalized');
        result.recommendations.add('Make questions more personal');
      }
    }

    // Check coach category
    if (coachCategory.isEmpty) {
      result.issues.add('Coach category not specified for check-in');
    }

    if (result.issues.isNotEmpty) {
      result.isValid = false;
    }
  }

  /// Validate navigation experience
  static Future<void> _validateNavigation(
    PlayerInteractionResult result,
    Map<String, dynamic> data,
  ) async {
    final fromScreen = data['fromScreen'] as String? ?? '';
    final toScreen = data['toScreen'] as String? ?? '';
    final navigationTime = data['navigationTime'] as int? ?? 0;

    // Check navigation time
    if (navigationTime > 1000) { // 1 second
      result.issues.add('Navigation took too long: ${navigationTime}ms');
      result.recommendations.add('Optimize screen transitions');
    }

    // Check screen names
    if (fromScreen.isEmpty || toScreen.isEmpty) {
      result.issues.add('Navigation screens not properly tracked');
    }

    if (result.issues.isNotEmpty) {
      result.isValid = false;
    }
  }

  /// Validate data save experience
  static Future<void> _validateDataSave(
    PlayerInteractionResult result,
    Map<String, dynamic> data,
    User? user,
  ) async {
    final dataType = data['dataType'] as String? ?? '';
    final saveTime = data['saveTime'] as int? ?? 0;
    final success = data['success'] as bool? ?? false;

    // Check save success
    if (!success) {
      result.issues.add('Data save failed for $dataType');
      result.isValid = false;
      result.recommendations.add('Fix data persistence issues');
    }

    // Check save time
    if (saveTime > 2000) { // 2 seconds
      result.issues.add('Data save took too long: ${saveTime}ms');
      result.recommendations.add('Optimize data storage performance');
    }

    // Check data type
    if (dataType.isEmpty) {
      result.issues.add('Data type not specified for save operation');
    }

    if (result.issues.isNotEmpty) {
      result.isValid = false;
    }
  }

  /// Update validation metrics
  static void _updateValidationMetrics(PlayerInteractionResult result) {
    final type = result.interactionType;
    
    // Initialize metrics for this type if needed
    _validationMetrics[type] ??= {
      'totalValidations': 0,
      'successfulValidations': 0,
      'failedValidations': 0,
      'commonIssues': <String, int>{},
    };

    final metrics = _validationMetrics[type] as Map<String, dynamic>;
    
    // Update counts
    metrics['totalValidations'] = (metrics['totalValidations'] as int) + 1;
    
    if (result.isValid) {
      metrics['successfulValidations'] = (metrics['successfulValidations'] as int) + 1;
    } else {
      metrics['failedValidations'] = (metrics['failedValidations'] as int) + 1;
      
      // Track common issues
      final commonIssues = metrics['commonIssues'] as Map<String, int>;
      for (final issue in result.issues) {
        commonIssues[issue] = (commonIssues[issue] ?? 0) + 1;
      }
    }

    // Update global experience issues
    if (!result.isValid) {
      for (final issue in result.issues) {
        if (!_experienceIssues.contains(issue)) {
          _experienceIssues.add(issue);
        }
      }
    }
  }

  /// Get validation report
  static Map<String, dynamic> getValidationReport() {
    final totalValidations = _validationMetrics.values
        .map((m) => (m as Map<String, dynamic>)['totalValidations'] as int)
        .fold(0, (a, b) => a + b);
    
    final successfulValidations = _validationMetrics.values
        .map((m) => (m as Map<String, dynamic>)['successfulValidations'] as int)
        .fold(0, (a, b) => a + b);

    return {
      'isValidating': _isValidating,
      'totalValidations': totalValidations,
      'successfulValidations': successfulValidations,
      'successRate': totalValidations > 0 ? (successfulValidations / totalValidations * 100) : 100.0,
      'activeIssues': _experienceIssues.length,
      'experienceIssues': _experienceIssues,
      'detailedMetrics': _validationMetrics,
      'playerExperienceScore': _calculatePlayerExperienceScore(),
      'recommendations': _generateRecommendations(),
    };
  }

  /// Calculate player experience score (0-100)
  static double _calculatePlayerExperienceScore() {
    if (_validationMetrics.isEmpty) return 100.0;

    final totalValidations = _validationMetrics.values
        .map((m) => (m as Map<String, dynamic>)['totalValidations'] as int)
        .fold(0, (a, b) => a + b);
    
    final successfulValidations = _validationMetrics.values
        .map((m) => (m as Map<String, dynamic>)['successfulValidations'] as int)
        .fold(0, (a, b) => a + b);

    if (totalValidations == 0) return 100.0;

    final baseScore = (successfulValidations / totalValidations) * 100;
    
    // Deduct points for active issues
    final issueDeduction = _experienceIssues.length * 5; // 5 points per issue
    
    return (baseScore - issueDeduction).clamp(0.0, 100.0);
  }

  /// Generate recommendations for improving player experience
  static List<String> _generateRecommendations() {
    final recommendations = <String>[];

    if (_experienceIssues.isNotEmpty) {
      recommendations.add('Address ${_experienceIssues.length} active player experience issues');
    }

    final score = _calculatePlayerExperienceScore();
    if (score < 95) {
      recommendations.add('Improve player experience score (currently ${score.toStringAsFixed(1)}%)');
    }

    if (recommendations.isEmpty) {
      recommendations.add('Player experience is excellent - maintain current quality');
    }

    return recommendations;
  }

  /// Save validation data
  static Future<void> _saveValidationData() async {
    try {
      final data = {
        'validationMetrics': _validationMetrics,
        'experienceIssues': _experienceIssues,
        'lastUpdate': DateTime.now().toIso8601String(),
      };

      await _storage.write(key: _validationDataKey, value: jsonEncode(data));
    } catch (e) {
      if (kDebugMode) print('❌ Failed to save validation data: $e');
    }
  }

  /// Load validation data
  static Future<void> _loadValidationData() async {
    try {
      final data = await _storage.read(key: _validationDataKey);
      if (data != null) {
        final decoded = jsonDecode(data) as Map<String, dynamic>;
        
        _validationMetrics.clear();
        _validationMetrics.addAll(decoded['validationMetrics'] as Map<String, dynamic>);
        
        _experienceIssues.clear();
        _experienceIssues.addAll((decoded['experienceIssues'] as List).cast<String>());
      }
    } catch (e) {
      if (kDebugMode) print('❌ Failed to load validation data: $e');
    }
  }

  /// Clear validation data
  static Future<void> clearValidationData() async {
    try {
      await _storage.delete(key: _validationDataKey);
      _validationMetrics.clear();
      _experienceIssues.clear();
      
      if (kDebugMode) print('🧹 Validation data cleared');
    } catch (e) {
      if (kDebugMode) print('❌ Failed to clear validation data: $e');
    }
  }

  /// Check if player experience is acceptable
  static bool isPlayerExperienceAcceptable() {
    final score = _calculatePlayerExperienceScore();
    return score >= 90.0 && _experienceIssues.length <= 2;
  }
}

/// Result of a player interaction validation
class PlayerInteractionResult {
  final String interactionType;
  final DateTime timestamp;
  bool isValid;
  final List<String> issues;
  final List<String> recommendations;

  PlayerInteractionResult({
    required this.interactionType,
    required this.timestamp,
    required this.isValid,
    required this.issues,
    required this.recommendations,
  });

  Map<String, dynamic> toJson() {
    return {
      'interactionType': interactionType,
      'timestamp': timestamp.toIso8601String(),
      'isValid': isValid,
      'issues': issues,
      'recommendations': recommendations,
    };
  }
}
