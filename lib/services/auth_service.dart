// 📁 lib/services/auth_service.dart

import 'dart:async';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:uuid/uuid.dart';
import '../models/user_model.dart';
import '../services/user_service.dart';
import '../services/saved_accounts_service.dart';
import '../bulletproof/error_handler.dart';
import '../quests/north_star_model.dart';
import '../security/biometric_auth_service.dart';
import '../security/security_utils.dart';

/// Enhanced authentication service with biometric support and security features.
///
/// Provides comprehensive authentication including:
/// - Traditional username/password authentication
/// - Biometric authentication (fingerprint, face ID)
/// - Secure password hashing and validation
/// - Account lockout protection
/// - Session management
///
/// Example usage:
/// ```dart
/// final authService = AuthService();
/// await authService.initialize();
///
/// final result = await authService.signIn(
///   username: 'john',
///   password: 'password123',
/// );
/// ```
class AuthService {
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;

  final UserService _userService;
  final ErrorHandler _errorHandler;
  final BiometricAuthService _biometricService;
  final FlutterSecureStorage _secureStorage;
  final _uuid = const Uuid();

  static const String _sessionTokenKey = 'session_token';
  static const String _sessionExpiryKey = 'session_expiry';
  static const String _loginAttemptsKey = 'login_attempts';
  static const String _lockoutTimeKey = 'lockout_time';

  bool _isInitialized = false;

  AuthService._internal()
      : _errorHandler = ErrorHandler(),
        _userService = UserService(ErrorHandler()),
        _biometricService = BiometricAuthService(),
        _secureStorage = const FlutterSecureStorage();

  /// Initialize the authentication service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _biometricService.initialize();
      _isInitialized = true;
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'AuthService.initialize');
    }
  }

  /// Attempts to sign a new user up with [email], [username], [password], [gender].
  /// Returns a newly created [User] on success, or throws Exception on failure.
  ///
  /// Enhanced with security validation and password hashing.
  Future<User> signUp({
    required String email,
    required String username,
    required String password,
    required String gender,
  }) async {
    try {
      await initialize();

      // Input validation
      if (email.isEmpty || username.isEmpty || password.isEmpty || gender.isEmpty) {
        throw AuthException('All fields are required.');
      }

      // Sanitize inputs
      email = SecurityUtils.sanitizeInput(email);
      username = SecurityUtils.sanitizeInput(username);
      gender = SecurityUtils.sanitizeInput(gender);

      // Validate email format
      if (!SecurityUtils.isValidEmail(email)) {
        throw AuthException('Please enter a valid email address.');
      }

      // Validate username format
      if (!SecurityUtils.isValidUsername(username)) {
        throw AuthException('Username must be 3-20 characters and contain only letters, numbers, and underscores.');
      }

      // Check password strength
      final passwordStrength = SecurityUtils.getPasswordStrength(password);
      if (!passwordStrength.isAcceptable) {
        throw AuthException('Password is too weak. ${passwordStrength.feedback.join(', ')}');
      }

      // Check if username already exists
      final existing = await _userService.loadUserByUsername(username);
      if (existing != null) {
        throw AuthException('Username "$username" is already taken.');
      }

      final now = DateTime.now();
      // Create a new User object with default/empty fields:
      final newUser = User(
        id: _uuid.v4(),
        username: username,
        gender: gender,
        exp: 0,
        streak: 0,
        categories: {},
        diaryEntries: [],
        northStarQuest: NorthStarQuest.empty(),
        createdAt: now,
        lastLoginAt: now,
        dailyHabits: [],
        customCategories: [],
        lastWeekExp: {},
        passcode: SecurityUtils.hashPassword(password),
        level: 1,
        rank: 'Novice',
        rankProgress: 0.0,
        streakDays: 0,
        challengeExp: 0,
        quests: [],
        lastModified: now,
        diary: {},
        dailyGoalProgress: 0,
        lastNotificationQuote: null,
        lastUpdated: now,
        showHomeLevelWidget: true,
        showLockScreenWidget: true,
      );

      // Save minimal user info for marketing (e.g., send email)  
      // Note: Call your marketing endpoint or send email here.
      print('📧 [AuthService] New signup: email=$email, username=$username, gender=$gender');

      // Persist user via UserService
      await _userService.saveUser(newUser);
      // Also store last logged‐in user
      await _userService.saveLastUser(newUser.username);

      // Save username for future convenience (dropdown feature)
      await SavedAccountsService.saveUsername(newUser.username);
      print('💾 [AuthService] Username saved for dropdown: ${newUser.username}');

      return newUser;
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace);
      rethrow;
    }
  }

  /// Attempts to sign in with [username] and [password].
  /// Returns the existing [User] if credentials match.
  /// Enhanced with security features and account lockout protection.
  Future<AuthResult> signIn({
    required String username,
    required String password,
  }) async {
    try {
      await initialize();

      // Input validation
      if (username.isEmpty || password.isEmpty) {
        throw AuthException('Username and password cannot be empty.');
      }

      // Sanitize inputs
      username = SecurityUtils.sanitizeInput(username);

      // Check if account is locked out
      if (await _isAccountLockedOut(username)) {
        final lockoutTime = await _getRemainingLockoutTime(username);
        throw AuthException('Account temporarily locked. Try again in ${lockoutTime.inMinutes} minutes.');
      }

      final existing = await _userService.loadUserByUsername(username);
      if (existing == null) {
        await _recordFailedLogin(username);
        throw AuthException('Invalid username or password.');
      }

      // Verify password using secure hash comparison
      if (existing.passcode == null || !SecurityUtils.verifyPassword(password, existing.passcode!)) {
        await _recordFailedLogin(username);
        throw AuthException('Invalid username or password.');
      }

      // Successful login - clear failed attempts
      await _clearFailedAttempts(username);

      // Update last logged‐in user
      await _userService.saveLastUser(username);

      // Create session
      final sessionToken = await _createSession(username);

      return AuthResult.success(
        user: existing,
        sessionToken: sessionToken,
        message: 'Login successful',
      );
    } catch (e, stackTrace) {
      if (e is AuthException) {
        return AuthResult.failure(
          error: AuthError.invalidCredentials,
          message: e.message,
        );
      }
      await _errorHandler.handleError(e, stackTrace);
      return AuthResult.failure(
        error: AuthError.unknown,
        message: 'An unexpected error occurred during login',
      );
    }
  }

  /// Authenticate with biometrics
  Future<AuthResult> authenticateWithBiometrics({
    required String username,
    required String reason,
  }) async {
    try {
      await initialize();

      // Check if biometric is enabled for user
      if (!await _biometricService.isBiometricEnabledForUser(username)) {
        return AuthResult.failure(
          error: AuthError.biometricNotEnabled,
          message: 'Biometric authentication is not enabled for this account',
        );
      }

      // Perform biometric authentication
      final biometricResult = await _biometricService.authenticateWithBiometrics(
        reason: reason,
        username: username,
      );

      if (!biometricResult.isSuccess) {
        return AuthResult.failure(
          error: AuthError.biometricFailed,
          message: biometricResult.message,
        );
      }

      // Load user data
      final user = await _userService.loadUserByUsername(username);
      if (user == null) {
        return AuthResult.failure(
          error: AuthError.userNotFound,
          message: 'User account not found',
        );
      }

      // Create session
      final sessionToken = await _createSession(username);

      return AuthResult.success(
        user: user,
        sessionToken: sessionToken,
        message: 'Biometric authentication successful',
      );
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace);
      return AuthResult.failure(
        error: AuthError.unknown,
        message: 'Biometric authentication failed',
      );
    }
  }

  /// Enable biometric authentication for user
  Future<AuthResult> enableBiometricAuth({
    required String username,
    required String password,
  }) async {
    try {
      await initialize();

      // Verify password first
      final user = await _userService.loadUserByUsername(username);
      if (user == null || user.passcode == null || !SecurityUtils.verifyPassword(password, user.passcode!)) {
        return AuthResult.failure(
          error: AuthError.invalidCredentials,
          message: 'Invalid password',
        );
      }

      // Enable biometric authentication
      final result = await _biometricService.enableBiometricAuth(
        username: username,
        reason: 'Enable biometric authentication for your account',
      );

      if (result.isSuccess) {
        return AuthResult.success(
          user: user,
          message: 'Biometric authentication enabled successfully',
        );
      } else {
        return AuthResult.failure(
          error: AuthError.biometricFailed,
          message: result.message,
        );
      }
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace);
      return AuthResult.failure(
        error: AuthError.unknown,
        message: 'Failed to enable biometric authentication',
      );
    }
  }

  /// Get biometric authentication status
  Future<BiometricAuthStatus> getBiometricStatus(String username) async {
    try {
      await initialize();
      return await _biometricService.getAuthStatus(username);
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace);
      return BiometricAuthStatus(
        isEnabled: false,
        isAvailable: false,
        isLockedOut: false,
        biometricType: null,
        availableBiometrics: [],
      );
    }
  }

  /// Create user account without email requirement
  Future<User> createUserWithoutEmail({
    required String username,
    required String gender,
  }) async {
    try {
      await initialize();

      final now = DateTime.now();
      // Create a new User object without email/password requirements:
      final newUser = User(
        id: _uuid.v4(),
        username: username,
        gender: gender,
        exp: 0,
        streak: 0,
        categories: {},
        diaryEntries: [],
        northStarQuest: NorthStarQuest.empty(),
        createdAt: now,
        lastLoginAt: now,
        dailyHabits: [],
        customCategories: [],
        lastWeekExp: {},
        passcode: null, // No password required
        level: 1,
        rank: 'Novice',
        rankProgress: 0.0,
        streakDays: 0,
        challengeExp: 0,
        quests: [],
        lastModified: now,
        diary: {},
        dailyGoalProgress: 0,
        lastNotificationQuote: null,
        lastUpdated: now,
        showHomeLevelWidget: true,
        showLockScreenWidget: true,
        email: null, // No email required
        passwordHash: null, // No password required
        isEmailVerified: false, // Always false for email-less users
        klaviyoSubscribed: false, // Skip Klaviyo registration
      );

      // Log email-less signup
      print('📧 [AuthService] Email-less signup: username=$username, gender=$gender');

      // Persist user via UserService
      await _userService.saveUser(newUser);
      // Also store last logged‐in user
      await _userService.saveLastUser(newUser.username);

      // Save username for future convenience (dropdown feature)
      await SavedAccountsService.saveUsername(newUser.username);
      print('💾 [AuthService] Email-less username saved for dropdown: ${newUser.username}');

      return newUser;
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace);
      rethrow;
    }
  }

  /// Stub for "Guest Mode". Does nothing for now, future‐proof.
  Future<void> continueAsGuest() async {
    try {
      // For now, we do not save any data; simply return.
      print('🚀 Continuing as guest (stub).');
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace);
      rethrow;
    }
  }

  // Private helper methods

  Future<bool> _isAccountLockedOut(String username) async {
    try {
      final lockoutTimeStr = await _secureStorage.read(key: '${_lockoutTimeKey}_$username');
      if (lockoutTimeStr == null) return false;

      final lockoutTime = DateTime.parse(lockoutTimeStr);
      return DateTime.now().isBefore(lockoutTime);
    } catch (e) {
      return false;
    }
  }

  Future<Duration> _getRemainingLockoutTime(String username) async {
    try {
      final lockoutTimeStr = await _secureStorage.read(key: '${_lockoutTimeKey}_$username');
      if (lockoutTimeStr == null) return Duration.zero;

      final lockoutTime = DateTime.parse(lockoutTimeStr);
      final remaining = lockoutTime.difference(DateTime.now());
      return remaining.isNegative ? Duration.zero : remaining;
    } catch (e) {
      return Duration.zero;
    }
  }

  Future<void> _recordFailedLogin(String username) async {
    try {
      final attemptsStr = await _secureStorage.read(key: '${_loginAttemptsKey}_$username') ?? '0';
      final attempts = int.parse(attemptsStr) + 1;

      await _secureStorage.write(key: '${_loginAttemptsKey}_$username', value: attempts.toString());

      // Lock out after 5 failed attempts for 30 minutes
      if (attempts >= 5) {
        final lockoutTime = DateTime.now().add(const Duration(minutes: 30));
        await _secureStorage.write(key: '${_lockoutTimeKey}_$username', value: lockoutTime.toIso8601String());
      }
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'AuthService._recordFailedLogin');
    }
  }

  Future<void> _clearFailedAttempts(String username) async {
    try {
      await _secureStorage.delete(key: '${_loginAttemptsKey}_$username');
      await _secureStorage.delete(key: '${_lockoutTimeKey}_$username');
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'AuthService._clearFailedAttempts');
    }
  }

  Future<String> _createSession(String username) async {
    try {
      final sessionToken = SecurityUtils.generateSecureToken();
      final expiryTime = DateTime.now().add(const Duration(hours: 24));

      await _secureStorage.write(key: '${_sessionTokenKey}_$username', value: sessionToken);
      await _secureStorage.write(key: '${_sessionExpiryKey}_$username', value: expiryTime.toIso8601String());

      return sessionToken;
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'AuthService._createSession');
      return '';
    }
  }
}

/// Authentication result wrapper
class AuthResult {
  final bool isSuccess;
  final User? user;
  final String? sessionToken;
  final AuthError? error;
  final String message;

  const AuthResult._({
    required this.isSuccess,
    this.user,
    this.sessionToken,
    this.error,
    required this.message,
  });

  factory AuthResult.success({
    required User user,
    String? sessionToken,
    required String message,
  }) {
    return AuthResult._(
      isSuccess: true,
      user: user,
      sessionToken: sessionToken,
      message: message,
    );
  }

  factory AuthResult.failure({
    required AuthError error,
    required String message,
  }) {
    return AuthResult._(
      isSuccess: false,
      error: error,
      message: message,
    );
  }
}

/// Authentication error types
enum AuthError {
  invalidCredentials,
  userNotFound,
  accountLocked,
  biometricNotEnabled,
  biometricFailed,
  sessionExpired,
  unknown,
}

/// Authentication exception
class AuthException implements Exception {
  final String message;
  const AuthException(this.message);

  @override
  String toString() => 'AuthException: $message';
}
