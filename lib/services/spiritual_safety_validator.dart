// lib/services/spiritual_safety_validator.dart

import 'package:flutter/foundation.dart';
import 'user_spiritual_profile_service.dart';

/// CRITICAL: Spiritual safety validator with bulletproof safeguards
/// 
/// This service validates ALL spiritual content before it reaches users.
/// It ensures we NEVER make religious mistakes or offend users.
/// 
/// ZERO TOLERANCE for religious content errors.
class SpiritualSafetyValidator {

  /// Initialize spiritual safety validator
  static Future<void> initialize() async {
    // Already initialized through other services
  }

  /// Validate content for spiritual appropriateness (compatibility method)
  static Future<SpiritualValidationResult> validateContent(
    String content,
    String userSpiritualPreference,
  ) async {
    try {
      // Use existing validation logic
      final contentAnalysis = analyzeContentForReligiousReferences(content);
      final riskLevel = _convertRiskLevel(contentAnalysis.riskLevel);

      // Determine if content should be blocked
      final shouldBlock = riskLevel == SpiritualRiskLevel.high;
      final warnings = <String>[];

      if (riskLevel == SpiritualRiskLevel.high) {
        warnings.add('Content contains inappropriate spiritual references');
      } else if (riskLevel == SpiritualRiskLevel.medium) {
        warnings.add('Content contains spiritual references that may not align with user preferences');
      }

      return SpiritualValidationResult(
        status: shouldBlock ? ValidationStatus.blocked : ValidationStatus.approved,
        reason: shouldBlock ? 'High spiritual risk detected' : 'Content approved',
        riskLevel: riskLevel,
        warnings: warnings,
        suggestions: [],
        isAppropriate: !shouldBlock,
      );
    } catch (e) {
      return SpiritualValidationResult(
        status: ValidationStatus.blocked,
        reason: 'Validation failed',
        riskLevel: SpiritualRiskLevel.unknown,
        warnings: ['Validation failed'],
        suggestions: [],
        isAppropriate: false,
      );
    }
  }

  /// Convert RiskLevel to SpiritualRiskLevel
  static SpiritualRiskLevel _convertRiskLevel(RiskLevel riskLevel) {
    switch (riskLevel) {
      case RiskLevel.none:
        return SpiritualRiskLevel.none;
      case RiskLevel.low:
        return SpiritualRiskLevel.low;
      case RiskLevel.medium:
        return SpiritualRiskLevel.medium;
      case RiskLevel.high:
        return SpiritualRiskLevel.high;
    }
  }

  /// CRITICAL: Validate spiritual content before sending to user
  static Future<SpiritualValidationResult> validateSpiritualContent({
    required String userId,
    required String content,
    required String coachName,
    required String category,
  }) async {
    try {
      // Get user's spiritual profile
      final profile = await UserSpiritualProfileService.getUserSpiritualProfile(userId);
      final guidance = await UserSpiritualProfileService.getSpiritualWisdomGuidance(userId);
      
      // Analyze content for religious references
      final contentAnalysis = analyzeContentForReligiousReferences(content);
      
      // Check if content is appropriate
      final validationResult = _validateContentAgainstProfile(
        contentAnalysis,
        profile,
        guidance,
      );
      
      // Log validation for monitoring
      await _logValidation(userId, coachName, category, contentAnalysis, validationResult);
      
      return validationResult;
      
    } catch (e) {
      if (kDebugMode) print('❌ Error validating spiritual content: $e');
      // SAFETY: Default to blocking content on error
      return SpiritualValidationResult.blocked(
        'Validation error - content blocked for safety',
      );
    }
  }
  
  /// Analyze content for religious references
  static SpiritualContentAnalysis analyzeContentForReligiousReferences(String content) {
    final lowerContent = content.toLowerCase();
    final detectedReferences = <ReligiousContentType>[];
    final problematicPhrases = <String>[];
    
    // Check for Christian/Catholic content
    if (_containsChristianContent(lowerContent)) {
      detectedReferences.add(ReligiousContentType.christianBibleVerse);
    }
    if (_containsPrayerReferences(lowerContent)) {
      detectedReferences.add(ReligiousContentType.christianPrayer);
    }
    if (_containsCatholicContent(lowerContent)) {
      detectedReferences.add(ReligiousContentType.catholicSaintWisdom);
    }
    
    // Check for other religious content
    if (_containsJewishContent(lowerContent)) {
      detectedReferences.add(ReligiousContentType.jewishWisdom);
    }
    if (_containsIslamicContent(lowerContent)) {
      detectedReferences.add(ReligiousContentType.islamicWisdom);
    }
    if (_containsBuddhistContent(lowerContent)) {
      detectedReferences.add(ReligiousContentType.buddhistWisdom);
    }
    if (_containsHinduContent(lowerContent)) {
      detectedReferences.add(ReligiousContentType.hinduWisdom);
    }
    
    // Check for problematic cross-religious references
    problematicPhrases.addAll(_findProblematicPhrases(lowerContent));
    
    return SpiritualContentAnalysis(
      detectedReferences: detectedReferences,
      problematicPhrases: problematicPhrases,
      hasReligiousContent: detectedReferences.isNotEmpty,
      riskLevel: _calculateRiskLevel(detectedReferences, problematicPhrases),
    );
  }
  
  /// Validate content against user's spiritual profile
  static SpiritualValidationResult _validateContentAgainstProfile(
    SpiritualContentAnalysis analysis,
    SpiritualProfile profile,
    SpiritualWisdomGuidance guidance,
  ) {
    // If no religious content, always safe
    if (!analysis.hasReligiousContent) {
      return SpiritualValidationResult.approved();
    }
    
    // If user profile not set, block all religious content
    if (!profile.isProfileSet) {
      return SpiritualValidationResult.blocked(
        'User spiritual profile not set - religious content blocked',
      );
    }
    
    // If user prefers secular content, block religious content
    if (!guidance.canShareReligiousContent) {
      return SpiritualValidationResult.blocked(
        'User prefers secular content - religious content blocked',
      );
    }
    
    // Check each detected religious reference
    for (final reference in analysis.detectedReferences) {
      if (!isReferenceAppropriate(reference, profile, guidance)) {
        return SpiritualValidationResult.blocked(
          'Inappropriate religious reference detected: ${reference.name}',
        );
      }
    }
    
    // Check for problematic phrases
    if (analysis.problematicPhrases.isNotEmpty) {
      return SpiritualValidationResult.blocked(
        'Problematic religious phrases detected: ${analysis.problematicPhrases.join(', ')}',
      );
    }
    
    // High risk content requires extra validation
    if (analysis.riskLevel == RiskLevel.high) {
      return SpiritualValidationResult.requiresReview(
        'High-risk religious content requires manual review',
      );
    }
    
    return SpiritualValidationResult.approved();
  }
  
  /// Check if religious reference is appropriate for user
  static bool isReferenceAppropriate(
    ReligiousContentType reference,
    SpiritualProfile profile,
    SpiritualWisdomGuidance guidance,
  ) {
    switch (reference) {
      case ReligiousContentType.christianBibleVerse:
        return guidance.allowBibleVerses;
      case ReligiousContentType.christianPrayer:
        return guidance.allowPrayer;
      case ReligiousContentType.catholicSaintWisdom:
        return guidance.allowSaintWisdom;
      case ReligiousContentType.christianVirtues:
        return profile.denomination == SpiritualDenomination.christian ||
               profile.denomination == SpiritualDenomination.catholic ||
               profile.denomination == SpiritualDenomination.orthodox;
      case ReligiousContentType.jewishWisdom:
        return profile.denomination == SpiritualDenomination.jewish;
      case ReligiousContentType.islamicWisdom:
        return profile.denomination == SpiritualDenomination.muslim;
      case ReligiousContentType.buddhistWisdom:
        return profile.denomination == SpiritualDenomination.buddhist;
      case ReligiousContentType.hinduWisdom:
        return profile.denomination == SpiritualDenomination.hindu;
      case ReligiousContentType.universalSpiritual:
      case ReligiousContentType.stoicWisdom:
        return true; // Always appropriate
      default:
        return false; // Default to blocking unknown content
    }
  }
  
  /// Content analysis methods
  static bool _containsChristianContent(String content) {
    const christianTerms = [
      'jesus', 'christ', 'lord savior', 'lord saviour', 'bible', 'biblical', 'scripture',
      'gospel', 'faith', 'book of matthew', 'book of mark', 'book of luke', 'book of john',
      'romans chapter', 'corinthians', 'ephesians', 'philippians', 'colossians',
      'thessalonians', 'timothy', 'titus', 'philemon', 'hebrews chapter',
      'book of james', 'peter chapter', 'revelation chapter',
      'psalms', 'proverbs', 'ecclesiastes', 'genesis chapter', 'exodus chapter',
      'matthew 5:', 'mark 1:', 'luke 2:', 'john 3:', 'romans 8:', 'philippians 4:',
    ];
    return christianTerms.any((term) => content.contains(term));
  }
  
  static bool _containsPrayerReferences(String content) {
    const prayerTerms = [
      'pray', 'prayer', 'praying', 'our father', 'hail mary', 'amen',
      'blessed', 'holy spirit', 'in jesus name', 'lord\'s prayer',
    ];
    return prayerTerms.any((term) => content.contains(term));
  }
  
  static bool _containsCatholicContent(String content) {
    const catholicTerms = [
      'saint', 'st.', 'pope', 'vatican', 'mary', 'virgin mary', 'rosary',
      'mass', 'eucharist', 'communion', 'confession', 'penance',
      'augustine', 'aquinas', 'francis', 'teresa', 'ignatius',
    ];
    return catholicTerms.any((term) => content.contains(term));
  }
  
  static bool _containsJewishContent(String content) {
    const jewishTerms = [
      'torah', 'talmud', 'rabbi', 'synagogue', 'shabbat', 'kosher',
      'passover', 'yom kippur', 'rosh hashanah', 'hanukkah', 'mitzvah',
      'abraham', 'moses', 'david', 'solomon', 'israel', 'jerusalem',
    ];
    return jewishTerms.any((term) => content.contains(term));
  }
  
  static bool _containsIslamicContent(String content) {
    const islamicTerms = [
      'allah', 'muhammad', 'quran', 'koran', 'mosque', 'imam', 'hajj',
      'ramadan', 'mecca', 'medina', 'salah', 'zakat', 'shahada',
      'inshallah', 'mashallah', 'subhanallah', 'alhamdulillah',
    ];
    return islamicTerms.any((term) => content.contains(term));
  }
  
  static bool _containsBuddhistContent(String content) {
    const buddhistTerms = [
      'buddha', 'buddhism', 'dharma', 'karma', 'nirvana', 'meditation',
      'mindfulness', 'enlightenment', 'samsara', 'bodhisattva',
      'four noble truths', 'eightfold path', 'sangha', 'temple',
    ];
    return buddhistTerms.any((term) => content.contains(term));
  }
  
  static bool _containsHinduContent(String content) {
    const hinduTerms = [
      'hindu', 'hinduism', 'krishna', 'rama', 'shiva', 'vishnu',
      'brahma', 'bhagavad gita', 'vedas', 'upanishads', 'yoga practice', 'chakra',
      'mantra', ' om ', 'namaste', 'dharma', 'moksha', 'reincarnation',
    ];
    return hinduTerms.any((term) => content.contains(term));
  }
  
  static List<String> _findProblematicPhrases(String content) {
    const problematicPhrases = [
      // Cross-religious confusion
      'jesus in islam', 'muhammad in christianity', 'buddha christ',
      'allah jesus', 'torah jesus', 'quran bible',
      // Potentially offensive comparisons
      'false prophet', 'wrong religion', 'only true faith',
      'heretical', 'blasphemous', 'infidel', 'pagan',
    ];
    
    return problematicPhrases.where((phrase) => content.contains(phrase)).toList();
  }
  
  static RiskLevel _calculateRiskLevel(
    List<ReligiousContentType> references,
    List<String> problematicPhrases,
  ) {
    if (problematicPhrases.isNotEmpty) return RiskLevel.high;
    if (references.length > 2) return RiskLevel.medium;
    if (references.isNotEmpty) return RiskLevel.low;
    return RiskLevel.none;
  }
  
  /// Log validation for monitoring
  static Future<void> _logValidation(
    String userId,
    String coachName,
    String category,
    SpiritualContentAnalysis analysis,
    SpiritualValidationResult result,
  ) async {
    if (kDebugMode) {
      print('🛡️ Spiritual Validation Log:');
      print('   User: $userId');
      print('   Coach: $coachName ($category)');
      print('   Religious Content: ${analysis.hasReligiousContent}');
      print('   Risk Level: ${analysis.riskLevel.name}');
      print('   Result: ${result.status.name}');
      if (result.status != ValidationStatus.approved) {
        print('   Reason: ${result.reason}');
      }
    }
  }
}

/// Analysis of spiritual content
class SpiritualContentAnalysis {
  final List<ReligiousContentType> detectedReferences;
  final List<String> problematicPhrases;
  final bool hasReligiousContent;
  final RiskLevel riskLevel;
  
  SpiritualContentAnalysis({
    required this.detectedReferences,
    required this.problematicPhrases,
    required this.hasReligiousContent,
    required this.riskLevel,
  });
}

/// Validation result for spiritual content
class SpiritualValidationResult {
  final ValidationStatus status;
  final String reason;
  final SpiritualRiskLevel riskLevel;
  final List<String> warnings;
  final List<String> suggestions;
  final bool isAppropriate;

  SpiritualValidationResult({
    required this.status,
    required this.reason,
    this.riskLevel = SpiritualRiskLevel.none,
    this.warnings = const [],
    this.suggestions = const [],
    this.isAppropriate = true,
  });
  
  factory SpiritualValidationResult.approved() {
    return SpiritualValidationResult(
      status: ValidationStatus.approved,
      reason: 'Content approved for user',
    );
  }
  
  factory SpiritualValidationResult.blocked(String reason) {
    return SpiritualValidationResult(
      status: ValidationStatus.blocked,
      reason: reason,
    );
  }
  
  factory SpiritualValidationResult.requiresReview(String reason) {
    return SpiritualValidationResult(
      status: ValidationStatus.requiresReview,
      reason: reason,
    );
  }
  
  bool get isApproved => status == ValidationStatus.approved;
  bool get isBlocked => status == ValidationStatus.blocked;
  bool get requiresReview => status == ValidationStatus.requiresReview;
}

/// Validation status
enum ValidationStatus {
  approved,
  blocked,
  requiresReview,
}

/// Risk level for spiritual content
enum RiskLevel {
  none,
  low,
  medium,
  high,
}

/// Spiritual risk level (alias for compatibility)
enum SpiritualRiskLevel {
  none,
  low,
  medium,
  high,
  unknown,
}


