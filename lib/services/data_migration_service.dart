// lib/services/data_migration_service.dart

import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';


/// Service for handling data schema migrations and versioning
class DataMigrationService {
  static const FlutterSecureStorage _storage = FlutterSecureStorage();
  static const String _versionKey = 'app_data_version';
  static const String _migrationLogKey = 'migration_log';
  
  // Current data schema version
  static const int currentDataVersion = 3;
  
  // Version history:
  // v1: Initial user model
  // v2: Added authentication fields (username, email, passwordHash)
  // v3: Added coach check-in system and enhanced analytics

  /// Perform data migration if needed
  static Future<MigrationResult> performMigrationIfNeeded() async {
    try {
      final currentVersion = await _getCurrentDataVersion();
      
      if (currentVersion == currentDataVersion) {
        if (kDebugMode) print('📊 Data schema is up to date (v$currentDataVersion)');
        return MigrationResult.success(currentVersion, currentDataVersion, []);
      }
      
      if (kDebugMode) {
        print('🔄 Data migration needed: v$currentVersion → v$currentDataVersion');
      }
      
      final migrations = <String>[];
      
      // Perform incremental migrations
      for (int version = currentVersion + 1; version <= currentDataVersion; version++) {
        final migrationResult = await _performMigration(version);
        migrations.add(migrationResult);
        
        if (kDebugMode) {
          print('✅ Migration to v$version completed: $migrationResult');
        }
      }
      
      // Update version
      await _setCurrentDataVersion(currentDataVersion);
      
      // Log migration
      await _logMigration(currentVersion, currentDataVersion, migrations);
      
      if (kDebugMode) {
        print('🎉 Data migration completed successfully');
      }
      
      return MigrationResult.success(currentVersion, currentDataVersion, migrations);
    } catch (e) {
      if (kDebugMode) {
        print('❌ Data migration failed: $e');
      }
      
      return MigrationResult.failure(e.toString());
    }
  }

  /// Perform migration to specific version
  static Future<String> _performMigration(int targetVersion) async {
    switch (targetVersion) {
      case 2:
        return await _migrateToV2();
      case 3:
        return await _migrateToV3();
      default:
        throw Exception('Unknown migration version: $targetVersion');
    }
  }

  /// Migrate to version 2: Add authentication fields
  static Future<String> _migrateToV2() async {
    try {
      // Get all user data
      final allKeys = await _storage.readAll();
      int migratedUsers = 0;
      
      for (final entry in allKeys.entries) {
        if (entry.key.startsWith('user_')) {
          try {
            final userData = jsonDecode(entry.value);
            
            // Add new authentication fields if they don't exist
            if (!userData.containsKey('username')) {
              userData['username'] = userData['name'] ?? 'user_${DateTime.now().millisecondsSinceEpoch}';
            }
            
            if (!userData.containsKey('email')) {
              userData['email'] = '';
            }
            
            if (!userData.containsKey('passwordHash')) {
              userData['passwordHash'] = '';
            }
            
            if (!userData.containsKey('isEmailVerified')) {
              userData['isEmailVerified'] = false;
            }
            
            if (!userData.containsKey('klaviyoSubscribed')) {
              userData['klaviyoSubscribed'] = false;
            }
            
            if (!userData.containsKey('assignedCoaches')) {
              userData['assignedCoaches'] = null;
            }
            
            // Save updated user data
            await _storage.write(key: entry.key, value: jsonEncode(userData));
            migratedUsers++;
          } catch (e) {
            if (kDebugMode) print('⚠️ Failed to migrate user ${entry.key}: $e');
          }
        }
      }
      
      return 'Added authentication fields to $migratedUsers users';
    } catch (e) {
      throw Exception('V2 migration failed: $e');
    }
  }

  /// Migrate to version 3: Add coach check-in system
  static Future<String> _migrateToV3() async {
    try {
      // Initialize coach check-in settings for existing users
      final allKeys = await _storage.readAll();
      int migratedUsers = 0;
      
      for (final entry in allKeys.entries) {
        if (entry.key.startsWith('user_')) {
          try {
            final userData = jsonDecode(entry.value);
            final username = userData['username'] ?? 'unknown';
            
            // Initialize coach check-in settings (all enabled by default)
            final checkinSettings = {
              'Health': true,
              'Wealth': true,
              'Purpose': true,
              'Connection': true,
            };
            
            // Add custom categories if they exist
            if (userData['customCategories'] != null) {
              final customCategories = List<String>.from(userData['customCategories']);
              for (int i = 0; i < customCategories.length; i++) {
                checkinSettings['Custom Category ${i + 1}'] = true;
              }
            }
            
            // Save check-in settings
            await _storage.write(
              key: 'checkin_settings_$username',
              value: jsonEncode(checkinSettings),
            );
            
            migratedUsers++;
          } catch (e) {
            if (kDebugMode) print('⚠️ Failed to migrate check-in settings for ${entry.key}: $e');
          }
        }
      }
      
      // Initialize analytics storage
      await _initializeAnalyticsStorage();
      
      return 'Initialized check-in system for $migratedUsers users and analytics storage';
    } catch (e) {
      throw Exception('V3 migration failed: $e');
    }
  }

  /// Initialize analytics storage structure
  static Future<void> _initializeAnalyticsStorage() async {
    try {
      // Initialize transcript analytics
      final analyticsData = {
        'totalInteractions': 0,
        'transcriptEnhancedResponses': 0,
        'averageRelevanceScore': 0.0,
        'topCategories': <String, int>{},
        'lastUpdated': DateTime.now().toIso8601String(),
      };
      
      await _storage.write(
        key: 'transcript_analytics',
        value: jsonEncode(analyticsData),
      );
      
      // Initialize coach check-in analytics
      final checkinAnalytics = {
        'totalCheckins': 0,
        'checkinsByCoach': <String, int>{},
        'averageResponseTime': 0,
        'lastCheckinTime': null,
        'lastUpdated': DateTime.now().toIso8601String(),
      };
      
      await _storage.write(
        key: 'checkin_analytics',
        value: jsonEncode(checkinAnalytics),
      );
      
      if (kDebugMode) print('📊 Analytics storage initialized');
    } catch (e) {
      if (kDebugMode) print('⚠️ Failed to initialize analytics storage: $e');
    }
  }

  /// Get current data version
  static Future<int> _getCurrentDataVersion() async {
    try {
      final versionStr = await _storage.read(key: _versionKey);
      return int.tryParse(versionStr ?? '1') ?? 1;
    } catch (e) {
      if (kDebugMode) print('⚠️ Failed to read data version: $e');
      return 1; // Default to version 1
    }
  }

  /// Set current data version
  static Future<void> _setCurrentDataVersion(int version) async {
    try {
      await _storage.write(key: _versionKey, value: version.toString());
    } catch (e) {
      if (kDebugMode) print('❌ Failed to set data version: $e');
    }
  }

  /// Log migration for debugging and rollback purposes
  static Future<void> _logMigration(
    int fromVersion,
    int toVersion,
    List<String> migrations,
  ) async {
    try {
      final migrationEntry = {
        'timestamp': DateTime.now().toIso8601String(),
        'fromVersion': fromVersion,
        'toVersion': toVersion,
        'migrations': migrations,
        'success': true,
      };
      
      // Get existing log
      final existingLog = await _getMigrationLog();
      existingLog.add(migrationEntry);
      
      // Keep only last 10 migrations
      if (existingLog.length > 10) {
        existingLog.removeRange(0, existingLog.length - 10);
      }
      
      await _storage.write(
        key: _migrationLogKey,
        value: jsonEncode(existingLog),
      );
    } catch (e) {
      if (kDebugMode) print('⚠️ Failed to log migration: $e');
    }
  }

  /// Get migration log
  static Future<List<Map<String, dynamic>>> _getMigrationLog() async {
    try {
      final logData = await _storage.read(key: _migrationLogKey);
      if (logData != null) {
        return List<Map<String, dynamic>>.from(jsonDecode(logData));
      }
    } catch (e) {
      if (kDebugMode) print('⚠️ Failed to read migration log: $e');
    }
    return [];
  }

  /// Backup user data before migration
  static Future<bool> backupUserData() async {
    try {
      final allData = await _storage.readAll();
      final backup = {
        'timestamp': DateTime.now().toIso8601String(),
        'dataVersion': await _getCurrentDataVersion(),
        'data': allData,
      };
      
      await _storage.write(
        key: 'data_backup_${DateTime.now().millisecondsSinceEpoch}',
        value: jsonEncode(backup),
      );
      
      if (kDebugMode) print('💾 User data backed up successfully');
      return true;
    } catch (e) {
      if (kDebugMode) print('❌ Failed to backup user data: $e');
      return false;
    }
  }

  /// Validate data integrity after migration
  static Future<bool> validateDataIntegrity() async {
    try {
      final allKeys = await _storage.readAll();
      int validUsers = 0;
      int invalidUsers = 0;
      
      for (final entry in allKeys.entries) {
        if (entry.key.startsWith('user_')) {
          try {
            final userData = jsonDecode(entry.value);
            
            // Validate required fields
            if (userData['username'] != null && 
                userData['gender'] != null &&
                userData['customCategories'] != null) {
              validUsers++;
            } else {
              invalidUsers++;
              if (kDebugMode) print('⚠️ Invalid user data: ${entry.key}');
            }
          } catch (e) {
            invalidUsers++;
            if (kDebugMode) print('❌ Corrupted user data: ${entry.key}');
          }
        }
      }
      
      if (kDebugMode) {
        print('✅ Data integrity check: $validUsers valid, $invalidUsers invalid');
      }
      
      return invalidUsers == 0;
    } catch (e) {
      if (kDebugMode) print('❌ Data integrity validation failed: $e');
      return false;
    }
  }

  /// Get migration status for debugging
  static Future<Map<String, dynamic>> getMigrationStatus() async {
    try {
      return {
        'currentVersion': await _getCurrentDataVersion(),
        'targetVersion': currentDataVersion,
        'needsMigration': await _getCurrentDataVersion() < currentDataVersion,
        'migrationLog': await _getMigrationLog(),
        'dataIntegrityValid': await validateDataIntegrity(),
      };
    } catch (e) {
      return {
        'error': 'Failed to get migration status: $e',
        'currentVersion': 1,
        'targetVersion': currentDataVersion,
        'needsMigration': true,
      };
    }
  }

  /// Clear all migration data (for testing)
  static Future<void> clearMigrationData() async {
    try {
      await _storage.delete(key: _versionKey);
      await _storage.delete(key: _migrationLogKey);
      
      // Clear backup data
      final allKeys = await _storage.readAll();
      for (final key in allKeys.keys) {
        if (key.startsWith('data_backup_')) {
          await _storage.delete(key: key);
        }
      }
      
      if (kDebugMode) print('🗑️ Migration data cleared');
    } catch (e) {
      if (kDebugMode) print('❌ Failed to clear migration data: $e');
    }
  }
}

/// Migration result container
class MigrationResult {
  final bool success;
  final int fromVersion;
  final int toVersion;
  final List<String> migrations;
  final String? error;

  const MigrationResult._({
    required this.success,
    required this.fromVersion,
    required this.toVersion,
    required this.migrations,
    this.error,
  });

  factory MigrationResult.success(
    int fromVersion,
    int toVersion,
    List<String> migrations,
  ) {
    return MigrationResult._(
      success: true,
      fromVersion: fromVersion,
      toVersion: toVersion,
      migrations: migrations,
    );
  }

  factory MigrationResult.failure(String error) {
    return MigrationResult._(
      success: false,
      fromVersion: 0,
      toVersion: 0,
      migrations: [],
      error: error,
    );
  }

  @override
  String toString() {
    if (success) {
      return 'Migration successful: v$fromVersion → v$toVersion (${migrations.length} steps)';
    } else {
      return 'Migration failed: $error';
    }
  }
}
