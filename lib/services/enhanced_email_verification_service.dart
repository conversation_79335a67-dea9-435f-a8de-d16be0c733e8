import 'dart:convert';
import 'dart:async';
import '../services/bulletproof_storage_service.dart';
import '../services/comprehensive_logging_service.dart';
import '../services/email_template_service.dart';
import '../services/magic_link_service.dart';
import '../services/klaviyo_service.dart';
import '../services/klaviyo_fallback_service.dart';

/// 📧 Enhanced Email Verification Service
/// 
/// Comprehensive email verification system with beautiful MOL-styled templates,
/// magic link authentication, resend functionality with countdown timers,
/// and robust deliverability monitoring.
/// 
/// Features:
/// - Beautiful MOL-styled email templates
/// - Magic link one-click verification
/// - Resend functionality with countdown timers
/// - Email deliverability monitoring
/// - Multiple fallback providers
/// - Comprehensive analytics and tracking
/// - Rate limiting and abuse prevention
class EnhancedEmailVerificationService {
  static final EnhancedEmailVerificationService _instance = EnhancedEmailVerificationService._internal();
  factory EnhancedEmailVerificationService() => _instance;
  EnhancedEmailVerificationService._internal();

  final BulletproofStorageService _secureStorage = BulletproofStorageService();
  final MagicLinkService _magicLinkService = MagicLinkService();
  final KlaviyoFallbackService _fallbackService = KlaviyoFallbackService();
  bool _isInitialized = false;

  // Storage keys
  static const String _verificationDataKey = 'email_verification_data';

  static const String _deliverabilityStatsKey = 'email_deliverability_stats';
  static const String _verificationStatsKey = 'email_verification_stats';

  // Configuration
  static const Duration _resendCooldown = Duration(minutes: 2);
  static const int _maxResendAttempts = 3;
  static const Duration _verificationExpiry = Duration(hours: 24);

  /// Initialize the service
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      await _secureStorage.initialize();
      await _magicLinkService.initialize();
      await _fallbackService.initialize();
      
      _isInitialized = true;
      await ComprehensiveLoggingService.logInfo('📧 EnhancedEmailVerificationService initialized');
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Failed to initialize EnhancedEmailVerificationService: $e');
      rethrow;
    }
  }

  /// Ensure the service is initialized
  Future<void> _ensureInitialized() async {
    if (!_isInitialized) {
      await initialize();
    }
  }

  /// Send verification email with magic link
  Future<EmailVerificationResult> sendVerificationEmail({
    required String email,
    required String username,
    String? firstName,
    Map<String, dynamic>? metadata,
  }) async {
    await _ensureInitialized();
    
    try {
      await ComprehensiveLoggingService.logInfo('📧 Sending verification email to: $email');
      
      // Check if verification already exists and is valid
      final existingVerification = await _getVerificationData(email);
      if (existingVerification != null && !existingVerification.isExpired) {
        return EmailVerificationResult(
          success: false,
          message: 'Verification email already sent. Please check your inbox or wait before requesting another.',
          alreadySent: true,
          nextResendAvailable: existingVerification.nextResendTime,
        );
      }
      
      // Generate magic link
      final magicLinkResult = await _magicLinkService.generateVerificationMagicLink(
        email: email,
        username: username,
        metadata: metadata,
      );
      
      if (!magicLinkResult.success) {
        return EmailVerificationResult(
          success: false,
          message: magicLinkResult.message,
          rateLimited: magicLinkResult.rateLimited,
        );
      }
      
      // Generate traditional verification link (fallback)
      final verificationToken = await _generateVerificationToken(email, username);
      final verificationLink = _buildVerificationLink(verificationToken);
      
      // Generate beautiful email template with Klaviyo variables
      final emailHtml = EmailTemplateService.generateVerificationEmail(
        username: username,
        verificationLink: verificationLink,
        magicLink: magicLinkResult.magicLink!,
        firstName: firstName,
        useKlaviyoVariables: true, // Use Klaviyo template variables
      );
      
      // Send email via Klaviyo with fallback
      final emailSent = await _sendEmailWithFallback(
        email: email,
        subject: 'Welcome to MXD - Verify Your Account! 🚀',
        htmlContent: emailHtml,
        templateType: 'verification',
        verificationLink: verificationLink,
        magicLink: magicLinkResult.magicLink!,
        firstName: firstName,
      );
      
      if (emailSent.success) {
        // Store verification data
        final verificationData = EmailVerificationData(
          email: email,
          username: username,
          verificationToken: verificationToken,
          magicLinkToken: magicLinkResult.token!,
          sentAt: DateTime.now(),
          expiresAt: DateTime.now().add(_verificationExpiry),
          resendCount: 0,
          verified: false,
          metadata: metadata ?? {},
        );
        
        await _storeVerificationData(verificationData);
        
        // Update statistics
        await _updateVerificationStats('sent', true);
        await _updateDeliverabilityStats('sent', emailSent.provider);
        
        return EmailVerificationResult(
          success: true,
          message: 'Verification email sent successfully',
          verificationToken: verificationToken,
          magicLinkToken: magicLinkResult.token,
          expiresAt: verificationData.expiresAt,
          nextResendAvailable: DateTime.now().add(_resendCooldown),
        );
      } else {
        await _updateVerificationStats('failed', false);
        await _updateDeliverabilityStats('failed', emailSent.provider);
        
        return EmailVerificationResult(
          success: false,
          message: 'Failed to send verification email: ${emailSent.error}',
          deliveryFailed: true,
        );
      }
      
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Failed to send verification email: $e');
      await _updateVerificationStats('error', false);
      
      return EmailVerificationResult(
        success: false,
        message: 'Failed to send verification email: $e',
      );
    }
  }

  /// Resend verification email with countdown
  Future<EmailVerificationResult> resendVerificationEmail(String email) async {
    await _ensureInitialized();
    
    try {
      await ComprehensiveLoggingService.logInfo('📧 Resending verification email to: $email');
      
      // Get existing verification data
      final verificationData = await _getVerificationData(email);
      if (verificationData == null) {
        return EmailVerificationResult(
          success: false,
          message: 'No verification request found for this email',
        );
      }
      
      // Check if already verified
      if (verificationData.verified) {
        return EmailVerificationResult(
          success: false,
          message: 'Email is already verified',
          alreadyVerified: true,
        );
      }
      
      // Check resend cooldown
      if (!verificationData.canResend) {
        return EmailVerificationResult(
          success: false,
          message: 'Please wait before requesting another verification email',
          rateLimited: true,
          nextResendAvailable: verificationData.nextResendTime,
        );
      }
      
      // Check max resend attempts
      if (verificationData.resendCount >= _maxResendAttempts) {
        return EmailVerificationResult(
          success: false,
          message: 'Maximum resend attempts reached. Please contact support.',
          maxAttemptsReached: true,
        );
      }
      
      // Generate new magic link
      final magicLinkResult = await _magicLinkService.generateVerificationMagicLink(
        email: email,
        username: verificationData.username,
        metadata: verificationData.metadata,
      );
      
      if (!magicLinkResult.success) {
        return EmailVerificationResult(
          success: false,
          message: magicLinkResult.message,
          rateLimited: magicLinkResult.rateLimited,
        );
      }
      
      // Generate new verification token
      final newVerificationToken = await _generateVerificationToken(email, verificationData.username);
      final verificationLink = _buildVerificationLink(newVerificationToken);
      
      // Generate email template with Klaviyo variables
      final emailHtml = EmailTemplateService.generateVerificationEmail(
        username: verificationData.username,
        verificationLink: verificationLink,
        magicLink: magicLinkResult.magicLink!,
        useKlaviyoVariables: true, // Use Klaviyo template variables
      );
      
      // Send email
      final emailSent = await _sendEmailWithFallback(
        email: email,
        subject: 'MXD Verification - Resent 🔄',
        htmlContent: emailHtml,
        templateType: 'verification_resend',
        verificationLink: verificationLink,
        magicLink: magicLinkResult.magicLink!,
      );
      
      if (emailSent.success) {
        // Update verification data
        verificationData.verificationToken = newVerificationToken;
        verificationData.magicLinkToken = magicLinkResult.token!;
        verificationData.resendCount++;
        verificationData.lastResendAt = DateTime.now();
        
        await _storeVerificationData(verificationData);
        
        // Update statistics
        await _updateVerificationStats('resent', true);
        await _updateDeliverabilityStats('resent', emailSent.provider);
        
        return EmailVerificationResult(
          success: true,
          message: 'Verification email resent successfully',
          verificationToken: newVerificationToken,
          magicLinkToken: magicLinkResult.token,
          resendCount: verificationData.resendCount,
          nextResendAvailable: DateTime.now().add(_resendCooldown),
        );
      } else {
        await _updateVerificationStats('resend_failed', false);
        await _updateDeliverabilityStats('resend_failed', emailSent.provider);
        
        return EmailVerificationResult(
          success: false,
          message: 'Failed to resend verification email: ${emailSent.error}',
          deliveryFailed: true,
        );
      }
      
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Failed to resend verification email: $e');
      return EmailVerificationResult(
        success: false,
        message: 'Failed to resend verification email: $e',
      );
    }
  }

  /// Verify email using token or magic link
  Future<EmailVerificationResult> verifyEmail(String token, {bool isMagicLink = false}) async {
    await _ensureInitialized();
    
    try {
      await ComprehensiveLoggingService.logInfo('📧 Verifying email with token');
      
      if (isMagicLink) {
        // Validate magic link
        final magicResult = await _magicLinkService.validateMagicLink(token);
        if (!magicResult.success) {
          await _updateVerificationStats('magic_link_failed', false);
          return EmailVerificationResult(
            success: false,
            message: magicResult.message,
          );
        }
        
        // Mark email as verified
        await _markEmailAsVerified(magicResult.email!);
        await _updateVerificationStats('magic_link_verified', true);
        
        return EmailVerificationResult(
          success: true,
          message: 'Email verified successfully via magic link',
          email: magicResult.email,
          username: magicResult.username,
          verifiedVia: 'magic_link',
        );
      } else {
        // Validate traditional token
        final verificationData = await _getVerificationDataByToken(token);
        if (verificationData == null) {
          await _updateVerificationStats('token_invalid', false);
          return EmailVerificationResult(
            success: false,
            message: 'Invalid or expired verification token',
          );
        }
        
        if (verificationData.isExpired) {
          await _updateVerificationStats('token_expired', false);
          return EmailVerificationResult(
            success: false,
            message: 'Verification token has expired',
          );
        }
        
        if (verificationData.verified) {
          return EmailVerificationResult(
            success: false,
            message: 'Email is already verified',
            alreadyVerified: true,
          );
        }
        
        // Mark email as verified
        await _markEmailAsVerified(verificationData.email);
        await _updateVerificationStats('token_verified', true);
        
        return EmailVerificationResult(
          success: true,
          message: 'Email verified successfully',
          email: verificationData.email,
          username: verificationData.username,
          verifiedVia: 'token',
        );
      }
      
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Failed to verify email: $e');
      await _updateVerificationStats('verification_error', false);
      
      return EmailVerificationResult(
        success: false,
        message: 'Failed to verify email: $e',
      );
    }
  }

  /// Get verification status
  Future<EmailVerificationStatus> getVerificationStatus(String email) async {
    await _ensureInitialized();
    
    try {
      final verificationData = await _getVerificationData(email);
      if (verificationData == null) {
        return EmailVerificationStatus(
          email: email,
          verified: false,
          exists: false,
        );
      }
      
      return EmailVerificationStatus(
        email: email,
        verified: verificationData.verified,
        exists: true,
        sentAt: verificationData.sentAt,
        expiresAt: verificationData.expiresAt,
        resendCount: verificationData.resendCount,
        canResend: verificationData.canResend,
        nextResendTime: verificationData.nextResendTime,
        isExpired: verificationData.isExpired,
      );
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Failed to get verification status: $e');
      return EmailVerificationStatus(
        email: email,
        verified: false,
        exists: false,
        error: e.toString(),
      );
    }
  }

  /// Get verification statistics
  Future<Map<String, dynamic>> getVerificationStats() async {
    await _ensureInitialized();
    
    try {
      final statsStr = await _secureStorage.read(_verificationStatsKey);
      if (statsStr == null) {
        return {
          'sent': 0,
          'verified': 0,
          'failed': 0,
          'resent': 0,
          'success_rate': 0.0,
        };
      }
      
      final stats = Map<String, dynamic>.from(jsonDecode(statsStr));
      
      // Calculate success rate
      final sent = stats['sent'] ?? 0;
      final verified = (stats['token_verified'] ?? 0) + (stats['magic_link_verified'] ?? 0);
      final successRate = sent > 0 ? (verified / sent) * 100 : 0.0;
      stats['success_rate'] = successRate;
      stats['total_verified'] = verified;
      
      return stats;
    } catch (e) {
      return {'error': e.toString()};
    }
  }

  /// Get deliverability statistics
  Future<Map<String, dynamic>> getDeliverabilityStats() async {
    await _ensureInitialized();
    
    try {
      final statsStr = await _secureStorage.read(_deliverabilityStatsKey);
      if (statsStr == null) {
        return {
          'klaviyo_sent': 0,
          'fallback_sent': 0,
          'total_sent': 0,
          'delivery_rate': 0.0,
        };
      }
      
      return Map<String, dynamic>.from(jsonDecode(statsStr));
    } catch (e) {
      return {'error': e.toString()};
    }
  }

  /// Send email with fallback providers
  Future<EmailSendResult> _sendEmailWithFallback({
    required String email,
    required String subject,
    required String htmlContent,
    required String templateType,
    String? verificationLink,
    String? magicLink,
    String? firstName,
  }) async {
    try {
      // Try Klaviyo first - send event to trigger flow
      if (templateType == 'verification' || templateType == 'verification_resend') {
        await KlaviyoService.sendVerificationEvent(
          email,
          verificationLink: verificationLink,
          magicLink: magicLink,
          firstName: firstName,
        );

        return EmailSendResult(
          success: true,
          provider: 'klaviyo_event',
        );
      }

      // For other email types, use the custom email method
      final klaviyoResult = await KlaviyoService.sendCustomEmail(
        email: email,
        subject: subject,
        htmlContent: htmlContent,
        templateType: templateType,
        verificationLink: verificationLink,
        magicLink: magicLink,
        firstName: firstName,
      );
      
      if (klaviyoResult['success'] == true) {
        return EmailSendResult(
          success: true,
          provider: 'klaviyo',
        );
      }
      
      // Fallback to offline queue
      await _fallbackService.queueEmailForLater(
        email: email,
        subject: subject,
        htmlContent: htmlContent,
        templateType: templateType,
      );
      
      return EmailSendResult(
        success: true,
        provider: 'fallback_queue',
      );
      
    } catch (e) {
      return EmailSendResult(
        success: false,
        provider: 'none',
        error: e.toString(),
      );
    }
  }

  /// Generate verification token
  Future<String> _generateVerificationToken(String email, String username) async {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final data = '$email:$username:$timestamp';
    return base64Encode(utf8.encode(data));
  }

  /// Build verification link
  String _buildVerificationLink(String token) {
    return 'https://mxd.app/verify?token=$token';
  }

  /// Store verification data
  Future<void> _storeVerificationData(EmailVerificationData data) async {
    try {
      await _secureStorage.write(
        '$_verificationDataKey:${data.email}',
        jsonEncode(data.toJson()),
      );
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Failed to store verification data: $e');
      rethrow;
    }
  }

  /// Get verification data
  Future<EmailVerificationData?> _getVerificationData(String email) async {
    try {
      final dataStr = await _secureStorage.read('$_verificationDataKey:$email');
      if (dataStr == null) return null;
      
      return EmailVerificationData.fromJson(jsonDecode(dataStr));
    } catch (e) {
      return null;
    }
  }

  /// Get verification data by token
  Future<EmailVerificationData?> _getVerificationDataByToken(String token) async {
    try {
      // This is a simplified implementation
      // In a real app, you'd have a more efficient token-to-email mapping
      final decoded = utf8.decode(base64Decode(token));
      final parts = decoded.split(':');
      if (parts.length >= 2) {
        return await _getVerificationData(parts[0]);
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// Mark email as verified
  Future<void> _markEmailAsVerified(String email) async {
    try {
      final verificationData = await _getVerificationData(email);
      if (verificationData != null) {
        verificationData.verified = true;
        verificationData.verifiedAt = DateTime.now();
        await _storeVerificationData(verificationData);
      }
      
      // Also update user's email verification status
      await _secureStorage.write('user_email_verified', 'true');
      
      await ComprehensiveLoggingService.logInfo('✅ Email verified: $email');
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Failed to mark email as verified: $e');
    }
  }

  /// Update verification statistics
  Future<void> _updateVerificationStats(String action, bool success) async {
    try {
      final statsStr = await _secureStorage.read(_verificationStatsKey);
      Map<String, dynamic> stats = {};
      
      if (statsStr != null) {
        stats = Map<String, dynamic>.from(jsonDecode(statsStr));
      }
      
      stats[action] = (stats[action] ?? 0) + 1;
      if (success) {
        stats['${action}_success'] = (stats['${action}_success'] ?? 0) + 1;
      }
      
      stats['lastUpdated'] = DateTime.now().toIso8601String();
      
      await _secureStorage.write(
        _verificationStatsKey,
        jsonEncode(stats),
      );
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Failed to update verification stats: $e');
    }
  }

  /// Update deliverability statistics
  Future<void> _updateDeliverabilityStats(String action, String provider) async {
    try {
      final statsStr = await _secureStorage.read(_deliverabilityStatsKey);
      Map<String, dynamic> stats = {};
      
      if (statsStr != null) {
        stats = Map<String, dynamic>.from(jsonDecode(statsStr));
      }
      
      final key = '${provider}_$action';
      stats[key] = (stats[key] ?? 0) + 1;
      stats['total_$action'] = (stats['total_$action'] ?? 0) + 1;
      stats['lastUpdated'] = DateTime.now().toIso8601String();
      
      await _secureStorage.write(
        _deliverabilityStatsKey,
        jsonEncode(stats),
      );
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Failed to update deliverability stats: $e');
    }
  }
}

/// Email verification data model
class EmailVerificationData {
  final String email;
  final String username;
  String verificationToken;
  String magicLinkToken;
  final DateTime sentAt;
  final DateTime expiresAt;
  int resendCount;
  DateTime? lastResendAt;
  bool verified;
  DateTime? verifiedAt;
  final Map<String, dynamic> metadata;

  EmailVerificationData({
    required this.email,
    required this.username,
    required this.verificationToken,
    required this.magicLinkToken,
    required this.sentAt,
    required this.expiresAt,
    this.resendCount = 0,
    this.lastResendAt,
    this.verified = false,
    this.verifiedAt,
    this.metadata = const {},
  });

  bool get isExpired => DateTime.now().isAfter(expiresAt);
  
  bool get canResend {
    if (lastResendAt == null) return true;
    return DateTime.now().difference(lastResendAt!) >= const Duration(minutes: 2);
  }
  
  DateTime get nextResendTime {
    if (lastResendAt == null) return DateTime.now();
    return lastResendAt!.add(const Duration(minutes: 2));
  }

  Map<String, dynamic> toJson() => {
    'email': email,
    'username': username,
    'verificationToken': verificationToken,
    'magicLinkToken': magicLinkToken,
    'sentAt': sentAt.toIso8601String(),
    'expiresAt': expiresAt.toIso8601String(),
    'resendCount': resendCount,
    'lastResendAt': lastResendAt?.toIso8601String(),
    'verified': verified,
    'verifiedAt': verifiedAt?.toIso8601String(),
    'metadata': metadata,
  };

  factory EmailVerificationData.fromJson(Map<String, dynamic> json) {
    return EmailVerificationData(
      email: json['email'],
      username: json['username'],
      verificationToken: json['verificationToken'],
      magicLinkToken: json['magicLinkToken'],
      sentAt: DateTime.parse(json['sentAt']),
      expiresAt: DateTime.parse(json['expiresAt']),
      resendCount: json['resendCount'] ?? 0,
      lastResendAt: json['lastResendAt'] != null ? DateTime.parse(json['lastResendAt']) : null,
      verified: json['verified'] ?? false,
      verifiedAt: json['verifiedAt'] != null ? DateTime.parse(json['verifiedAt']) : null,
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
    );
  }
}

/// Email verification result
class EmailVerificationResult {
  final bool success;
  final String message;
  final String? email;
  final String? username;
  final String? verificationToken;
  final String? magicLinkToken;
  final DateTime? expiresAt;
  final DateTime? nextResendAvailable;
  final int? resendCount;
  final bool alreadySent;
  final bool alreadyVerified;
  final bool rateLimited;
  final bool deliveryFailed;
  final bool maxAttemptsReached;
  final String? verifiedVia;

  EmailVerificationResult({
    required this.success,
    required this.message,
    this.email,
    this.username,
    this.verificationToken,
    this.magicLinkToken,
    this.expiresAt,
    this.nextResendAvailable,
    this.resendCount,
    this.alreadySent = false,
    this.alreadyVerified = false,
    this.rateLimited = false,
    this.deliveryFailed = false,
    this.maxAttemptsReached = false,
    this.verifiedVia,
  });
}

/// Email verification status
class EmailVerificationStatus {
  final String email;
  final bool verified;
  final bool exists;
  final DateTime? sentAt;
  final DateTime? expiresAt;
  final int? resendCount;
  final bool? canResend;
  final DateTime? nextResendTime;
  final bool? isExpired;
  final String? error;

  EmailVerificationStatus({
    required this.email,
    required this.verified,
    required this.exists,
    this.sentAt,
    this.expiresAt,
    this.resendCount,
    this.canResend,
    this.nextResendTime,
    this.isExpired,
    this.error,
  });
}

/// Email send result
class EmailSendResult {
  final bool success;
  final String provider;
  final String? error;

  EmailSendResult({
    required this.success,
    required this.provider,
    this.error,
  });
}
