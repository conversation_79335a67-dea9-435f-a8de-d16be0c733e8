import 'dart:convert';
import '../services/bulletproof_secure_storage.dart';
import '../services/comprehensive_logging_service.dart';
import '../services/klaviyo_service.dart';

/// 🔄 Klaviyo Fallback Service
/// 
/// Provides comprehensive fallback mechanisms when Klavi<PERSON> is unavailable.
/// Implements offline queue, local caching, and automatic reconciliation
/// when service is restored.
/// 
/// Features:
/// - Offline operation queue with persistence
/// - Local email validation cache
/// - Automatic reconciliation when service returns
/// - Graceful degradation strategies
/// - Comprehensive error recovery
class KlaviyoFallbackService {
  static final KlaviyoFallbackService _instance = KlaviyoFallbackService._internal();
  factory KlaviyoFallbackService() => _instance;
  KlaviyoFallbackService._internal();

  final BulletproofSecureStorage _secureStorage = BulletproofSecureStorage();
  bool _isInitialized = false;

  // Storage keys
  static const String _offlineQueueKey = 'klaviyo_offline_queue';
  static const String _emailCacheKey = 'klaviyo_email_cache';
  static const String _fallbackModeKey = 'klaviyo_fallback_mode';
  static const String _lastSyncKey = 'klaviyo_last_sync';

  /// Initialize the fallback service
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      await _secureStorage.initialize();
      
      // Check if we're in fallback mode
      await _checkFallbackMode();
      
      _isInitialized = true;
      await ComprehensiveLoggingService.logInfo('🔄 KlaviyoFallbackService initialized');
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Failed to initialize KlaviyoFallbackService: $e');
      rethrow;
    }
  }

  /// Ensure the service is initialized
  Future<void> _ensureInitialized() async {
    if (!_isInitialized) {
      await initialize();
    }
  }

  /// Check if email exists with fallback logic
  Future<bool> emailExistsWithFallback(String email) async {
    await _ensureInitialized();
    
    try {
      // Try primary Klaviyo service first
      return await KlaviyoService.emailExists(email);
    } catch (e) {
      await ComprehensiveLoggingService.logWarning('⚠️ Klaviyo unavailable, using fallback for email check: $email');
      
      // Enable fallback mode
      await _enableFallbackMode();
      
      // Check local cache
      return await _checkEmailInCache(email);
    }
  }

  /// Add email to list with fallback logic
  Future<bool> addEmailToListWithFallback(
    String email, {
    String? firstName,
    String? lastName,
  }) async {
    await _ensureInitialized();
    
    try {
      // Try primary Klaviyo service first
      final result = await KlaviyoService.addEmailToList(
        email,
        firstName: firstName,
        lastName: lastName,
      );
      
      if (result) {
        // Cache successful addition
        await _cacheEmailAddition(email, firstName, lastName);
      }
      
      return result;
    } catch (e) {
      await ComprehensiveLoggingService.logWarning('⚠️ Klaviyo unavailable, queueing email addition: $email');
      
      // Enable fallback mode
      await _enableFallbackMode();
      
      // Queue for later processing
      await _queueEmailAddition(email, firstName, lastName);
      
      // Return true to allow signup to continue
      return true;
    }
  }

  /// Send verification email with fallback logic
  Future<bool> sendVerificationEmailWithFallback(String email) async {
    await _ensureInitialized();
    
    try {
      // Try primary Klaviyo service first
      return await KlaviyoService.sendVerificationEmail(email);
    } catch (e) {
      await ComprehensiveLoggingService.logWarning('⚠️ Klaviyo unavailable, queueing verification email: $email');
      
      // Enable fallback mode
      await _enableFallbackMode();
      
      // Queue for later processing
      await _queueVerificationEmail(email);
      
      // Return true to allow signup to continue
      return true;
    }
  }

  /// Process offline queue when service is restored
  Future<FallbackSyncResult> processOfflineQueue() async {
    await _ensureInitialized();
    
    final result = FallbackSyncResult();
    
    try {
      await ComprehensiveLoggingService.logInfo('🔄 Processing Klaviyo offline queue');
      
      final queueStr = await _secureStorage.read(key: _offlineQueueKey);
      if (queueStr == null) {
        result.success = true;
        result.message = 'No items in offline queue';
        return result;
      }
      
      final queue = List<dynamic>.from(jsonDecode(queueStr));
      final processedItems = <int>[];
      
      for (int i = 0; i < queue.length; i++) {
        final item = Map<String, dynamic>.from(queue[i]);
        
        try {
          final success = await _processQueueItem(item);
          
          result.processedItems.add({
            'id': item['id'],
            'type': item['type'],
            'email': item['email'],
            'success': success,
            'timestamp': item['timestamp'],
          });
          
          if (success) {
            processedItems.add(i);
            result.successCount++;
          } else {
            result.failureCount++;
          }
        } catch (e) {
          result.processedItems.add({
            'id': item['id'],
            'type': item['type'],
            'email': item['email'],
            'success': false,
            'error': e.toString(),
            'timestamp': item['timestamp'],
          });
          result.failureCount++;
        }
      }
      
      // Remove successfully processed items
      if (processedItems.isNotEmpty) {
        for (int i = processedItems.length - 1; i >= 0; i--) {
          queue.removeAt(processedItems[i]);
        }
        
        if (queue.isEmpty) {
          await _secureStorage.delete(key: _offlineQueueKey);
        } else {
          await _secureStorage.write(key: _offlineQueueKey, value: jsonEncode(queue));
        }
      }
      
      // Update last sync time
      await _secureStorage.write(key: _lastSyncKey, value: DateTime.now().toIso8601String());
      
      result.success = true;
      result.message = 'Processed ${result.successCount} items successfully, ${result.failureCount} failed';
      
      await ComprehensiveLoggingService.logInfo('✅ Offline queue processing complete: ${result.message}');
      
    } catch (e) {
      result.success = false;
      result.message = 'Failed to process offline queue: $e';
      await ComprehensiveLoggingService.logError('❌ Offline queue processing failed: $e');
    }
    
    return result;
  }

  /// Check if we're currently in fallback mode
  Future<bool> isInFallbackMode() async {
    await _ensureInitialized();
    
    try {
      final fallbackModeStr = await _secureStorage.read(key: _fallbackModeKey);
      return fallbackModeStr == 'true';
    } catch (e) {
      return false;
    }
  }

  /// Get offline queue status
  Future<Map<String, dynamic>> getOfflineQueueStatus() async {
    await _ensureInitialized();
    
    try {
      final queueStr = await _secureStorage.read(key: _offlineQueueKey);
      if (queueStr == null) {
        return {'count': 0, 'items': []};
      }
      
      final queue = List<dynamic>.from(jsonDecode(queueStr));
      return {
        'count': queue.length,
        'items': queue.map((item) => {
          'id': item['id'],
          'type': item['type'],
          'email': item['email'],
          'timestamp': item['timestamp'],
        }).toList(),
      };
    } catch (e) {
      return {'count': 0, 'items': [], 'error': e.toString()};
    }
  }

  /// Clear offline queue
  Future<void> clearOfflineQueue() async {
    await _ensureInitialized();
    
    try {
      await _secureStorage.delete(key: _offlineQueueKey);
      await ComprehensiveLoggingService.logInfo('🗑️ Klaviyo offline queue cleared');
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Failed to clear offline queue: $e');
    }
  }

  /// Attempt to restore service connection
  Future<bool> attemptServiceRestore() async {
    await _ensureInitialized();
    
    try {
      await ComprehensiveLoggingService.logInfo('🔄 Attempting to restore Klaviyo service');
      
      // Test service health
      final health = await KlaviyoService.getServiceHealth();
      
      if (health['isHealthy'] == true) {
        await _disableFallbackMode();
        await ComprehensiveLoggingService.logInfo('✅ Klaviyo service restored');
        return true;
      } else {
        await ComprehensiveLoggingService.logWarning('⚠️ Klaviyo service still unhealthy');
        return false;
      }
    } catch (e) {
      await ComprehensiveLoggingService.logWarning('⚠️ Service restore attempt failed: $e');
      return false;
    }
  }

  /// Check fallback mode status
  Future<void> _checkFallbackMode() async {
    try {
      final isInFallback = await isInFallbackMode();
      if (isInFallback) {
        await ComprehensiveLoggingService.logInfo('🔄 Starting in Klaviyo fallback mode');
        
        // Attempt to restore service
        final restored = await attemptServiceRestore();
        if (restored) {
          // Process any queued items
          await processOfflineQueue();
        }
      }
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Failed to check fallback mode: $e');
    }
  }

  /// Enable fallback mode
  Future<void> _enableFallbackMode() async {
    try {
      await _secureStorage.write(key: _fallbackModeKey, value: 'true');
      await ComprehensiveLoggingService.logInfo('🔄 Klaviyo fallback mode enabled');
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Failed to enable fallback mode: $e');
    }
  }

  /// Disable fallback mode
  Future<void> _disableFallbackMode() async {
    try {
      await _secureStorage.write(key: _fallbackModeKey, value: 'false');
      await ComprehensiveLoggingService.logInfo('✅ Klaviyo fallback mode disabled');
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Failed to disable fallback mode: $e');
    }
  }

  /// Check email in local cache
  Future<bool> _checkEmailInCache(String email) async {
    try {
      final cacheStr = await _secureStorage.read(key: _emailCacheKey);
      if (cacheStr == null) return false;
      
      final cache = Map<String, dynamic>.from(jsonDecode(cacheStr));
      return cache.containsKey(email);
    } catch (e) {
      return false;
    }
  }

  /// Cache email addition
  Future<void> _cacheEmailAddition(String email, String? firstName, String? lastName) async {
    try {
      final cacheStr = await _secureStorage.read(key: _emailCacheKey);
      Map<String, dynamic> cache = {};
      
      if (cacheStr != null) {
        cache = Map<String, dynamic>.from(jsonDecode(cacheStr));
      }
      
      cache[email] = {
        'firstName': firstName,
        'lastName': lastName,
        'timestamp': DateTime.now().toIso8601String(),
      };
      
      await _secureStorage.write(key: _emailCacheKey, value: jsonEncode(cache));
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Failed to cache email: $e');
    }
  }

  /// Queue email addition for later processing
  Future<void> _queueEmailAddition(String email, String? firstName, String? lastName) async {
    await _addToQueue({
      'type': 'addEmail',
      'email': email,
      'firstName': firstName,
      'lastName': lastName,
      'timestamp': DateTime.now().toIso8601String(),
      'id': DateTime.now().millisecondsSinceEpoch.toString(),
    });
  }

  /// Queue verification email for later processing
  Future<void> _queueVerificationEmail(String email) async {
    await _addToQueue({
      'type': 'sendVerification',
      'email': email,
      'timestamp': DateTime.now().toIso8601String(),
      'id': DateTime.now().millisecondsSinceEpoch.toString(),
    });
  }

  /// Add item to offline queue
  Future<void> _addToQueue(Map<String, dynamic> item) async {
    try {
      final queueStr = await _secureStorage.read(key: _offlineQueueKey);
      List<dynamic> queue = [];
      
      if (queueStr != null) {
        queue = List<dynamic>.from(jsonDecode(queueStr));
      }
      
      queue.add(item);
      
      await _secureStorage.write(key: _offlineQueueKey, value: jsonEncode(queue));
      await ComprehensiveLoggingService.logInfo('📥 Added item to Klaviyo offline queue: ${item['type']}');
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Failed to add to offline queue: $e');
    }
  }

  /// Queue custom email for later delivery
  Future<void> queueEmailForLater({
    required String email,
    required String subject,
    required String htmlContent,
    required String templateType,
  }) async {
    await _ensureInitialized();

    try {
      await ComprehensiveLoggingService.logInfo('📧 Queueing custom email for later: $email');

      final emailItem = {
        'type': 'sendCustomEmail',
        'email': email,
        'subject': subject,
        'htmlContent': htmlContent,
        'templateType': templateType,
        'timestamp': DateTime.now().toIso8601String(),
        'retryCount': 0,
      };

      await _addToQueue(emailItem);

      await ComprehensiveLoggingService.logInfo('✅ Custom email queued for later delivery: $email');
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Failed to queue custom email: $e');
    }
  }

  /// Process individual queue item
  Future<bool> _processQueueItem(Map<String, dynamic> item) async {
    try {
      switch (item['type']) {
        case 'addEmail':
          return await KlaviyoService.addEmailToList(
            item['email'],
            firstName: item['firstName'],
            lastName: item['lastName'],
          );
        
        case 'sendVerification':
          return await KlaviyoService.sendVerificationEmail(item['email']);

        case 'sendCustomEmail':
          final result = await KlaviyoService.sendCustomEmail(
            email: item['email'],
            subject: item['subject'],
            htmlContent: item['htmlContent'],
            templateType: item['templateType'],
          );
          return result['success'] == true;

        default:
          await ComprehensiveLoggingService.logWarning('⚠️ Unknown queue item type: ${item['type']}');
          return false;
      }
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Failed to process queue item: $e');
      return false;
    }
  }
}

/// Result of fallback sync operation
class FallbackSyncResult {
  bool success = false;
  String message = '';
  int successCount = 0;
  int failureCount = 0;
  List<Map<String, dynamic>> processedItems = [];
  
  Map<String, dynamic> toJson() => {
    'success': success,
    'message': message,
    'successCount': successCount,
    'failureCount': failureCount,
    'processedItems': processedItems,
    'totalProcessed': successCount + failureCount,
  };
}
