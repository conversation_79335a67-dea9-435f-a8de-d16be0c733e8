// 📁 lib/services/coach_training_context_service.dart

import 'package:flutter/foundation.dart';
import '../models/user_model.dart';
import '../models/training_session_model.dart';
import '../models/training_timer_model.dart';
import '../services/training_storage_service.dart';
import '../utils/date_formatter.dart';

/// Specialized service for providing detailed training context to coaches,
/// especially Health coaches who need comprehensive knowledge of player's
/// training activities, patterns, and progress.
class CoachTrainingContextService {
  static final CoachTrainingContextService _instance = CoachTrainingContextService._internal();
  factory CoachTrainingContextService() => _instance;
  CoachTrainingContextService._internal();

  /// Get comprehensive training context for Health coaches
  /// This provides detailed insights into the player's training habits,
  /// progress, and patterns to enable personalized coaching advice.
  static Future<Map<String, dynamic>> getHealthCoachContext(User user) async {
    try {
      if (kDebugMode) print('🏋️ Building Health coach training context for ${user.username}');
      
      final trainingStorage = TrainingStorageService();
      final allSessions = await trainingStorage.getAllSessions();
      final currentProgram = await trainingStorage.loadProgram();
      final currentSession = await trainingStorage.loadCurrentSession();
      
      final context = {
        'training_overview': await _buildTrainingOverview(allSessions),
        'current_program': _buildCurrentProgramContext(currentProgram),
        'active_session': _buildActiveSessionContext(currentSession),
        'performance_analytics': _buildPerformanceAnalytics(allSessions),
        'training_patterns': _buildTrainingPatterns(allSessions),
        'health_insights': _buildHealthInsights(allSessions, user),
        'coaching_recommendations': _buildCoachingRecommendations(allSessions, user),
        'enhanced_training_features': await _buildEnhancedTrainingFeatures(allSessions, currentProgram),
      };
      
      if (kDebugMode) {
        print('✅ Health coach context built successfully');
        print('📊 Training sessions analyzed: ${allSessions.length}');
      }
      
      return context;
    } catch (e) {
      if (kDebugMode) print('❌ Failed to build Health coach context: $e');
      return _getFallbackHealthContext();
    }
  }

  /// Get training context for any coach category
  static Future<Map<String, dynamic>> getCoachTrainingContext(String category, User user) async {
    try {
      final trainingStorage = TrainingStorageService();
      final allSessions = await trainingStorage.getAllSessions();
      final currentProgram = await trainingStorage.loadProgram();
      
      // Base training context for all coaches
      final baseContext = {
        'has_training_data': allSessions.isNotEmpty,
        'total_sessions': allSessions.length,
        'current_program_type': currentProgram.programType,
        'recent_activity': _buildRecentActivitySummary(allSessions),
      };
      
      // Enhanced context for Health coaches
      if (category.toLowerCase() == 'health') {
        final healthContext = await getHealthCoachContext(user);
        return {...baseContext, ...healthContext};
      }
      
      // Relevant training insights for other coaches
      return {
        ...baseContext,
        'training_insights': _buildCategorySpecificInsights(category, allSessions),
      };
    } catch (e) {
      if (kDebugMode) print('❌ Failed to build coach training context: $e');
      return {'has_training_data': false, 'error': 'Failed to load training data'};
    }
  }

  /// Build comprehensive training overview
  static Future<Map<String, dynamic>> _buildTrainingOverview(List<TrainingSession> sessions) async {
    if (sessions.isEmpty) {
      return {
        'status': 'no_training_data',
        'message': 'Player has not started using the Training Tracker yet',
        'total_sessions': 0,
        'total_time_hours': 0.0,
        'total_exp_earned': 0,
      };
    }

    final totalSeconds = sessions.fold<int>(0, (sum, s) => sum + s.durationSeconds);
    final totalExp = sessions.fold<double>(0.0, (sum, s) => sum + s.expEarned);
    final completedSessions = sessions.where((s) => s.isCompleted).length;
    
    final now = DateTime.now();
    final last7Days = sessions.where((s) => 
        s.completedAt != null && 
        s.completedAt!.isAfter(now.subtract(const Duration(days: 7)))
    ).length;
    
    final last30Days = sessions.where((s) => 
        s.completedAt != null && 
        s.completedAt!.isAfter(now.subtract(const Duration(days: 30)))
    ).length;

    return {
      'status': 'active_trainer',
      'total_sessions': sessions.length,
      'completed_sessions': completedSessions,
      'total_time_hours': (totalSeconds / 3600).toStringAsFixed(1),
      'total_exp_earned': totalExp,
      'sessions_last_7_days': last7Days,
      'sessions_last_30_days': last30Days,
      'average_session_minutes': sessions.isNotEmpty 
          ? (totalSeconds / sessions.length / 60).round()
          : 0,
      'training_frequency': _calculateTrainingFrequency(sessions),
      'last_training_date': sessions.isNotEmpty && sessions.first.completedAt != null
          ? DateFormatter.formatDateTime(sessions.first.completedAt!)
          : 'No completed sessions',
    };
  }

  /// Build current program context
  static Map<String, dynamic> _buildCurrentProgramContext(TrainingProgram program) {
    return {
      'program_type': program.programType,
      'current_cycle': program.currentLabel,
      'cycle_index': program.currentIndex,
      'total_cycles': program.workoutLabels.length,
      'available_cycles': program.workoutLabels,
      'program_description': _getProgramDescription(program.programType),
    };
  }

  /// Build active session context
  static Map<String, dynamic> _buildActiveSessionContext(TrainingSession? session) {
    if (session == null) {
      return {
        'has_active_session': false,
        'message': 'No active training session',
      };
    }

    return {
      'has_active_session': true,
      'session_label': session.label,
      'duration_minutes': (session.durationSeconds / 60).round(),
      'current_exp': session.expEarned,
      'has_notes': session.notes.isNotEmpty,
      'notes_preview': session.notes.length > 100 
          ? '${session.notes.substring(0, 100)}...'
          : session.notes,
      'has_bodyweight': session.bodyweightKg != null,
      'bodyweight_kg': session.bodyweightKg,
      'started_at': DateFormatter.formatDateTime(session.createdAt),
    };
  }

  /// Build performance analytics
  static Map<String, dynamic> _buildPerformanceAnalytics(List<TrainingSession> sessions) {
    if (sessions.isEmpty) return {'status': 'no_data'};

    final completedSessions = sessions.where((s) => s.isCompleted).toList();
    if (completedSessions.isEmpty) return {'status': 'no_completed_sessions'};

    // Calculate trends
    final now = DateTime.now();
    final thisWeek = completedSessions.where((s) => 
        s.completedAt!.isAfter(now.subtract(const Duration(days: 7)))
    ).toList();
    
    final lastWeek = completedSessions.where((s) => 
        s.completedAt!.isAfter(now.subtract(const Duration(days: 14))) &&
        s.completedAt!.isBefore(now.subtract(const Duration(days: 7)))
    ).toList();

    final thisWeekTime = thisWeek.fold<int>(0, (sum, s) => sum + s.durationSeconds);
    final lastWeekTime = lastWeek.fold<int>(0, (sum, s) => sum + s.durationSeconds);

    return {
      'weekly_comparison': {
        'this_week_sessions': thisWeek.length,
        'last_week_sessions': lastWeek.length,
        'this_week_hours': (thisWeekTime / 3600).toStringAsFixed(1),
        'last_week_hours': (lastWeekTime / 3600).toStringAsFixed(1),
        'session_trend': _calculateTrend(thisWeek.length, lastWeek.length),
        'time_trend': _calculateTrend(thisWeekTime, lastWeekTime),
      },
      'consistency_score': _calculateConsistencyScore(completedSessions),
      'performance_metrics': {
        'longest_session_minutes': completedSessions.map((s) => s.durationSeconds).reduce((a, b) => a > b ? a : b) ~/ 60,
        'shortest_session_minutes': completedSessions.map((s) => s.durationSeconds).reduce((a, b) => a < b ? a : b) ~/ 60,
        'most_productive_day': _getMostProductiveDay(completedSessions),
        'preferred_training_time': _getPreferredTrainingTime(completedSessions),
      },
    };
  }

  /// Build training patterns analysis
  static Map<String, dynamic> _buildTrainingPatterns(List<TrainingSession> sessions) {
    if (sessions.isEmpty) return {'status': 'no_data'};

    final labelFrequency = <String, int>{};
    final dayOfWeekFrequency = <int, int>{};
    final hourFrequency = <int, int>{};

    for (final session in sessions.where((s) => s.completedAt != null)) {
      // Label frequency
      labelFrequency[session.label] = (labelFrequency[session.label] ?? 0) + 1;
      
      // Day of week frequency (1 = Monday, 7 = Sunday)
      final dayOfWeek = session.completedAt!.weekday;
      dayOfWeekFrequency[dayOfWeek] = (dayOfWeekFrequency[dayOfWeek] ?? 0) + 1;
      
      // Hour frequency
      final hour = session.completedAt!.hour;
      hourFrequency[hour] = (hourFrequency[hour] ?? 0) + 1;
    }

    return {
      'favorite_training_types': labelFrequency.entries
          .toList()
          ..sort((a, b) => b.value.compareTo(a.value)),
      'preferred_days': _getPreferredDays(dayOfWeekFrequency),
      'training_schedule': {
        'most_active_day': _getDayName(_getMostFrequentKey(dayOfWeekFrequency)),
        'most_active_hour': _getMostFrequentKey(hourFrequency),
        'training_time_preference': _categorizeTimePreference(_getMostFrequentKey(hourFrequency)),
      },
      'training_variety': {
        'unique_training_types': labelFrequency.keys.length,
        'most_frequent_type': _getMostFrequentKey(labelFrequency),
        'training_diversity_score': _calculateDiversityScore(labelFrequency),
      },
    };
  }

  /// Build health-specific insights
  static Map<String, dynamic> _buildHealthInsights(List<TrainingSession> sessions, User user) {
    final insights = <String, dynamic>{};
    
    // Bodyweight tracking analysis
    final bodyweightSessions = sessions.where((s) => s.bodyweightKg != null).toList();
    if (bodyweightSessions.isNotEmpty) {
      bodyweightSessions.sort((a, b) => b.completedAt!.compareTo(a.completedAt!));
      insights['bodyweight_tracking'] = {
        'is_tracking': true,
        'latest_weight_kg': bodyweightSessions.first.bodyweightKg,
        'tracking_frequency': '${bodyweightSessions.length}/${sessions.length} sessions',
        'weight_trend': _analyzeWeightTrend(bodyweightSessions),
      };
    } else {
      insights['bodyweight_tracking'] = {
        'is_tracking': false,
        'recommendation': 'Consider tracking bodyweight for better health monitoring',
      };
    }

    // Training intensity and recovery
    insights['training_intensity'] = _analyzeTrainingIntensity(sessions);
    insights['recovery_patterns'] = _analyzeRecoveryPatterns(sessions);
    
    // Health category EXP correlation
    final healthExp = user.categories['Health'] ?? 0;
    final trainingExp = sessions.fold<double>(0.0, (sum, s) => sum + s.expEarned);
    
    insights['exp_analysis'] = {
      'total_health_exp': healthExp,
      'training_tracker_exp': trainingExp,
      'training_contribution_percent': healthExp > 0 
          ? ((trainingExp / healthExp) * 100).round()
          : 0,
    };

    return insights;
  }

  /// Build coaching recommendations
  static Map<String, dynamic> _buildCoachingRecommendations(List<TrainingSession> sessions, User user) {
    final recommendations = <String>[];
    final insights = <String, dynamic>{};

    if (sessions.isEmpty) {
      recommendations.add("Great to see you're interested in the Training Tracker! This tool will help you monitor your workouts and earn bonus EXP.");
      return {
        'recommendations': recommendations,
        'priority': 'getting_started',
      };
    }

    // Analyze recent activity
    final now = DateTime.now();
    final last7Days = sessions.where((s) => 
        s.completedAt != null && 
        s.completedAt!.isAfter(now.subtract(const Duration(days: 7)))
    ).length;

    if (last7Days == 0) {
      recommendations.add("I notice you haven't trained in the past week. How are you feeling? Let's get back into a routine that works for you.");
      insights['priority'] = 're_engagement';
    } else if (last7Days >= 5) {
      recommendations.add("Fantastic training consistency this week! You're really building momentum. How are you feeling about your progress?");
      insights['priority'] = 'maintenance';
    }

    // Analyze session duration
    final avgDuration = sessions.fold<int>(0, (sum, s) => sum + s.durationSeconds) / sessions.length / 60;
    if (avgDuration < 20) {
      recommendations.add("Your sessions are quite short. While any movement is great, consider gradually extending your workouts for better results.");
    } else if (avgDuration > 120) {
      recommendations.add("You're putting in serious training time! Make sure you're allowing adequate recovery between intense sessions.");
    }

    // Bodyweight tracking
    final bodyweightSessions = sessions.where((s) => s.bodyweightKg != null).length;
    if (bodyweightSessions < sessions.length * 0.3) {
      recommendations.add("Consider tracking your bodyweight more consistently - it's a valuable metric for monitoring your health progress.");
    }

    return {
      'recommendations': recommendations,
      'insights': insights,
      'coaching_focus': _determineCoachingFocus(sessions, user),
    };
  }

  // Helper methods for analysis

  static Map<String, dynamic> _buildRecentActivitySummary(List<TrainingSession> sessions) {
    final now = DateTime.now();
    final last7Days = sessions.where((s) =>
        s.completedAt != null &&
        s.completedAt!.isAfter(now.subtract(const Duration(days: 7)))
    ).length;

    return {
      'sessions_last_week': last7Days,
      'last_session': sessions.isNotEmpty && sessions.first.completedAt != null
          ? DateFormatter.formatDateTime(sessions.first.completedAt!)
          : 'No sessions yet',
      'activity_level': last7Days >= 4 ? 'high' : last7Days >= 2 ? 'moderate' : 'low',
    };
  }

  static Map<String, dynamic> _buildCategorySpecificInsights(String category, List<TrainingSession> sessions) {
    switch (category.toLowerCase()) {
      case 'wealth':
        return {
          'training_discipline': sessions.length > 10 ? 'high' : 'developing',
          'consistency_parallel': 'Training consistency often reflects business discipline',
        };
      case 'purpose':
        return {
          'goal_alignment': 'Physical training supports mental clarity and purpose',
          'training_as_meditation': sessions.where((s) => s.notes.toLowerCase().contains('meditation')).length,
        };
      case 'connection':
        return {
          'social_training': sessions.where((s) => s.notes.toLowerCase().contains('partner') ||
                                                  s.notes.toLowerCase().contains('group')).length,
          'community_aspect': 'Consider sharing training achievements with others',
        };
      default:
        return {
          'general_insight': 'Physical training supports overall life performance',
          'cross_category_benefit': 'Training discipline enhances all life areas',
        };
    }
  }

  static String _calculateTrainingFrequency(List<TrainingSession> sessions) {
    if (sessions.length < 7) return 'building_habit';

    final now = DateTime.now();
    final last30Days = sessions.where((s) =>
        s.completedAt != null &&
        s.completedAt!.isAfter(now.subtract(const Duration(days: 30)))
    ).length;

    final weeklyAverage = last30Days / 4.3; // 4.3 weeks in 30 days

    if (weeklyAverage >= 5) return 'very_high';
    if (weeklyAverage >= 3) return 'high';
    if (weeklyAverage >= 2) return 'moderate';
    return 'low';
  }

  static String _getProgramDescription(String programType) {
    switch (programType.toLowerCase()) {
      case 'letters':
        return 'Letter-based training cycles (A, B, C, etc.) - great for structured progression';
      case 'numbers':
        return 'Number-based training cycles (1, 2, 3, etc.) - ideal for sequential workouts';
      case 'custom':
        return 'Custom training program tailored to specific goals';
      default:
        return 'Personalized training program';
    }
  }

  static String _calculateTrend(int current, int previous) {
    if (previous == 0) return current > 0 ? 'improving' : 'no_change';
    if (current > previous) return 'improving';
    if (current < previous) return 'declining';
    return 'stable';
  }

  static int _calculateConsistencyScore(List<TrainingSession> sessions) {
    if (sessions.isEmpty) return 0;

    final now = DateTime.now();
    final last30Days = now.subtract(const Duration(days: 30));
    final recentSessions = sessions.where((s) =>
        s.completedAt != null && s.completedAt!.isAfter(last30Days)
    ).toList();

    final trainingDays = recentSessions
        .map((s) => DateTime(s.completedAt!.year, s.completedAt!.month, s.completedAt!.day))
        .toSet()
        .length;

    return (trainingDays / 30 * 100).round();
  }

  static String _getMostProductiveDay(List<TrainingSession> sessions) {
    final dayTotals = <int, int>{};

    for (final session in sessions) {
      final day = session.completedAt!.weekday;
      dayTotals[day] = (dayTotals[day] ?? 0) + session.durationSeconds;
    }

    if (dayTotals.isEmpty) return 'No data';

    final mostProductiveDay = dayTotals.entries
        .reduce((a, b) => a.value > b.value ? a : b)
        .key;

    return _getDayName(mostProductiveDay);
  }

  static String _getPreferredTrainingTime(List<TrainingSession> sessions) {
    final hourCounts = <int, int>{};

    for (final session in sessions.where((s) => s.completedAt != null)) {
      final hour = session.completedAt!.hour;
      hourCounts[hour] = (hourCounts[hour] ?? 0) + 1;
    }

    if (hourCounts.isEmpty) return 'No data';

    final mostActiveHour = hourCounts.entries
        .reduce((a, b) => a.value > b.value ? a : b)
        .key;

    return _categorizeTimePreference(mostActiveHour);
  }

  static List<String> _getPreferredDays(Map<int, int> dayFrequency) {
    if (dayFrequency.isEmpty) return [];

    final sortedDays = dayFrequency.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    return sortedDays.take(3).map((e) => _getDayName(e.key)).toList();
  }

  static dynamic _getMostFrequentKey(Map<dynamic, int> frequency) {
    if (frequency.isEmpty) return null;
    return frequency.entries.reduce((a, b) => a.value > b.value ? a : b).key;
  }

  static String _getDayName(int? dayOfWeek) {
    if (dayOfWeek == null) return 'Unknown';
    switch (dayOfWeek) {
      case 1: return 'Monday';
      case 2: return 'Tuesday';
      case 3: return 'Wednesday';
      case 4: return 'Thursday';
      case 5: return 'Friday';
      case 6: return 'Saturday';
      case 7: return 'Sunday';
      default: return 'Unknown';
    }
  }

  static String _categorizeTimePreference(int? hour) {
    if (hour == null) return 'Unknown';
    if (hour >= 5 && hour < 12) return 'Morning (5AM-12PM)';
    if (hour >= 12 && hour < 17) return 'Afternoon (12PM-5PM)';
    if (hour >= 17 && hour < 21) return 'Evening (5PM-9PM)';
    return 'Night (9PM-5AM)';
  }

  static double _calculateDiversityScore(Map<String, int> labelFrequency) {
    if (labelFrequency.isEmpty) return 0.0;

    final total = labelFrequency.values.fold<int>(0, (sum, count) => sum + count);
    final uniqueTypes = labelFrequency.keys.length;

    // Higher score for more variety and balanced distribution
    return (uniqueTypes / total * 100).clamp(0.0, 100.0);
  }

  static Map<String, dynamic> _analyzeWeightTrend(List<TrainingSession> bodyweightSessions) {
    if (bodyweightSessions.length < 2) {
      return {'trend': 'insufficient_data', 'message': 'Need more data points'};
    }

    final latest = bodyweightSessions.first.bodyweightKg!;
    final earliest = bodyweightSessions.last.bodyweightKg!;
    final change = latest - earliest;

    return {
      'trend': change > 1 ? 'increasing' : change < -1 ? 'decreasing' : 'stable',
      'change_kg': change.toStringAsFixed(1),
      'latest_kg': latest.toStringAsFixed(1),
      'data_points': bodyweightSessions.length,
    };
  }

  static Map<String, dynamic> _analyzeTrainingIntensity(List<TrainingSession> sessions) {
    if (sessions.isEmpty) return {'level': 'no_data'};

    final avgDuration = sessions.fold<int>(0, (sum, s) => sum + s.durationSeconds) / sessions.length / 60;

    String level;
    if (avgDuration >= 90) {
      level = 'high';
    } else if (avgDuration >= 60) {
      level = 'moderate';
    } else if (avgDuration >= 30) {
      level = 'light';
    } else {
      level = 'very_light';
    }

    return {
      'level': level,
      'average_duration_minutes': avgDuration.round(),
      'recommendation': _getIntensityRecommendation(level),
    };
  }

  static Map<String, dynamic> _analyzeRecoveryPatterns(List<TrainingSession> sessions) {
    if (sessions.length < 2) return {'pattern': 'insufficient_data'};

    final completedSessions = sessions.where((s) => s.completedAt != null).toList()
      ..sort((a, b) => a.completedAt!.compareTo(b.completedAt!));

    if (completedSessions.length < 2) return {'pattern': 'insufficient_data'};

    final gaps = <int>[];
    for (int i = 1; i < completedSessions.length; i++) {
      final gap = completedSessions[i].completedAt!
          .difference(completedSessions[i-1].completedAt!)
          .inDays;
      gaps.add(gap);
    }

    final avgGap = gaps.fold<int>(0, (sum, gap) => sum + gap) / gaps.length;

    return {
      'average_rest_days': avgGap.round(),
      'pattern': avgGap <= 1 ? 'daily' : avgGap <= 2 ? 'every_other_day' : 'weekly_plus',
      'recommendation': _getRecoveryRecommendation(avgGap),
    };
  }

  static String _getIntensityRecommendation(String level) {
    switch (level) {
      case 'very_light':
        return 'Consider gradually increasing session duration for better results';
      case 'light':
        return 'Good foundation! You might benefit from longer sessions as you progress';
      case 'moderate':
        return 'Excellent training duration! This is a sustainable intensity level';
      case 'high':
        return 'Impressive dedication! Ensure adequate recovery between sessions';
      default:
        return 'Keep up the great work!';
    }
  }

  static String _getRecoveryRecommendation(double avgGap) {
    if (avgGap < 1) {
      return 'Training daily is ambitious! Make sure to listen to your body and include rest days';
    } else if (avgGap <= 2) {
      return 'Great training frequency! This allows for good recovery between sessions';
    } else {
      return 'Consider increasing training frequency if your schedule allows';
    }
  }

  static String _determineCoachingFocus(List<TrainingSession> sessions, User user) {
    if (sessions.isEmpty) return 'getting_started';

    final now = DateTime.now();
    final last7Days = sessions.where((s) =>
        s.completedAt != null &&
        s.completedAt!.isAfter(now.subtract(const Duration(days: 7)))
    ).length;

    if (last7Days == 0) return 're_engagement';
    if (last7Days >= 5) return 'optimization';

    final avgDuration = sessions.fold<int>(0, (sum, s) => sum + s.durationSeconds) / sessions.length / 60;
    if (avgDuration < 30) return 'duration_building';

    return 'consistency_building';
  }

  /// Build enhanced training features context for coaches
  static Future<Map<String, dynamic>> _buildEnhancedTrainingFeatures(
    List<TrainingSession> allSessions,
    dynamic currentProgram,
  ) async {
    try {
      if (kDebugMode) print('🚀 Building enhanced training features context');

      final now = DateTime.now();
      final weekStart = now.subtract(Duration(days: now.weekday - 1));
      final monthStart = DateTime(now.year, now.month, 1);

      // Weekly and monthly session analysis
      final weekSessions = allSessions.where((s) =>
        s.createdAt.isAfter(weekStart) && s.createdAt.isBefore(weekStart.add(Duration(days: 7)))
      ).toList();

      final monthSessions = allSessions.where((s) =>
        s.createdAt.isAfter(monthStart) && s.createdAt.isBefore(DateTime(now.year, now.month + 1, 1))
      ).toList();

      // Personal records
      final longestSession = allSessions.isNotEmpty ?
        allSessions.reduce((a, b) => a.durationSeconds > b.durationSeconds ? a : b) : null;
      final mostExpSession = allSessions.isNotEmpty ?
        allSessions.reduce((a, b) => a.expEarned > b.expEarned ? a : b) : null;

      // Training consistency analysis
      final uniqueTrainingDays = allSessions.map((s) =>
        '${s.createdAt.year}-${s.createdAt.month}-${s.createdAt.day}'
      ).toSet().length;

      final totalDays = allSessions.isNotEmpty ?
        allSessions.last.createdAt.difference(allSessions.first.createdAt).inDays + 1 : 1;
      final consistency = (uniqueTrainingDays / totalDays * 100).clamp(0, 100);

      // Bodyweight tracking
      final bodyweightEntries = allSessions.where((s) => s.bodyweightKg != null).toList();
      final bodyweightTrend = bodyweightEntries.length >= 2 ?
        (bodyweightEntries.last.bodyweightKg! - bodyweightEntries.first.bodyweightKg!) : 0.0;

      return {
        'analytics_summary': {
          'weekly_sessions': weekSessions.length,
          'monthly_sessions': monthSessions.length,
          'weekly_total_minutes': weekSessions.fold<int>(0, (sum, s) => sum + s.durationSeconds) ~/ 60,
          'monthly_total_minutes': monthSessions.fold<int>(0, (sum, s) => sum + s.durationSeconds) ~/ 60,
          'weekly_exp': weekSessions.fold<double>(0, (sum, s) => sum + s.expEarned),
          'monthly_exp': monthSessions.fold<double>(0, (sum, s) => sum + s.expEarned),
        },
        'personal_records': {
          'longest_session_minutes': longestSession != null ? (longestSession.durationSeconds / 60).round() : 0,
          'longest_session_date': longestSession?.createdAt.toIso8601String(),
          'most_exp_session': mostExpSession?.expEarned ?? 0,
          'most_exp_session_date': mostExpSession?.createdAt.toIso8601String(),
          'total_lifetime_sessions': allSessions.length,
          'total_lifetime_minutes': allSessions.fold<int>(0, (sum, s) => sum + s.durationSeconds) ~/ 60,
          'total_lifetime_exp': allSessions.fold<double>(0, (sum, s) => sum + s.expEarned),
        },
        'performance_insights': {
          'training_consistency_percentage': consistency.round(),
          'unique_training_days': uniqueTrainingDays,
          'average_session_duration_minutes': allSessions.isNotEmpty ?
            (allSessions.fold<int>(0, (sum, s) => sum + s.durationSeconds) / allSessions.length / 60).round() : 0,
          'completion_rate': allSessions.isNotEmpty ?
            (allSessions.where((s) => s.isCompleted).length / allSessions.length * 100).round() : 0,
        },
        'program_insights': {
          'current_program_type': currentProgram?.programType ?? 'Unknown',
          'current_workout_label': currentProgram?.currentLabel ?? 'Unknown',
          'total_workout_labels': currentProgram?.workoutLabels?.length ?? 0,
          'has_custom_notes_templates': currentProgram?.notesTemplates?.isNotEmpty ?? false,
          'notes_templates_available': currentProgram?.notesTemplates?.keys.toList() ?? [],
        },
        'bodyweight_tracking': {
          'has_bodyweight_data': bodyweightEntries.isNotEmpty,
          'total_bodyweight_entries': bodyweightEntries.length,
          'bodyweight_trend_kg': bodyweightTrend.toStringAsFixed(1),
          'latest_bodyweight_kg': bodyweightEntries.isNotEmpty ? bodyweightEntries.last.bodyweightKg : null,
          'first_bodyweight_kg': bodyweightEntries.isNotEmpty ? bodyweightEntries.first.bodyweightKg : null,
        },
        'enhanced_features_status': {
          'analytics_dashboard_available': true,
          'performance_metrics_available': true,
          'training_calendar_available': true,
          'workout_templates_available': true,
          'multi_phase_timer_available': true,
          'smart_goals_available': true,
          'enhanced_comparison_available': true,
          'competition_features_available': true,
          'export_backup_available': true,
          'theme_customization_available': true,
        },
        'coaching_context': {
          'can_provide_detailed_analytics': true,
          'can_suggest_workout_templates': true,
          'can_help_with_calendar_planning': true,
          'can_analyze_performance_trends': true,
          'can_track_personal_records': true,
          'can_provide_bodyweight_insights': bodyweightEntries.isNotEmpty,
          'can_suggest_training_improvements': allSessions.length >= 3,
        },
      };
    } catch (e) {
      if (kDebugMode) print('❌ Failed to build enhanced training features: $e');
      return {
        'error': 'Enhanced features unavailable',
        'enhanced_features_status': {
          'analytics_dashboard_available': false,
          'performance_metrics_available': false,
          'training_calendar_available': false,
          'workout_templates_available': false,
        },
      };
    }
  }

  static Map<String, dynamic> _getFallbackHealthContext() {
    return {
      'training_overview': {
        'status': 'error',
        'message': 'Unable to load training data',
        'total_sessions': 0,
      },
      'error': 'Failed to build training context',
      'recommendations': ['Please try again later or contact support if the issue persists'],
    };
  }
}
