import 'dart:convert';
import 'dart:math';
import 'package:crypto/crypto.dart';

/// 🔐 Security Utilities
/// 
/// Provides cryptographic utilities for password hashing, verification,
/// and other security operations used throughout the application.
/// 
/// Features:
/// - Secure password hashing with salt
/// - Password verification
/// - Random salt generation
/// - Secure token generation
/// - Input sanitization
class SecurityUtils {
  
  /// Hash a password with salt using SHA-256
  /// TODO: Replace with proper bcrypt implementation when backend is ready
  static String hashPassword(String password) {
    final salt = _generateSalt();
    final saltedPassword = password + salt;
    final bytes = utf8.encode(saltedPassword);
    final digest = sha256.convert(bytes);
    
    // Store salt with hash (salt:hash format)
    return '$salt:${digest.toString()}';
  }
  
  /// Verify a password against a hash
  static bool verifyPassword(String password, String hashedPassword) {
    try {
      final parts = hashedPassword.split(':');
      if (parts.length != 2) {
        return false;
      }
      
      final salt = parts[0];
      final hash = parts[1];
      
      final saltedPassword = password + salt;
      final bytes = utf8.encode(saltedPassword);
      final digest = sha256.convert(bytes);
      
      return digest.toString() == hash;
    } catch (e) {
      return false;
    }
  }
  
  /// Generate a random salt
  static String _generateSalt() {
    final random = Random.secure();
    final bytes = List<int>.generate(16, (i) => random.nextInt(256));
    return base64Encode(bytes);
  }
  
  /// Generate a secure random token
  static String generateSecureToken({int length = 32}) {
    final random = Random.secure();
    final bytes = List<int>.generate(length, (i) => random.nextInt(256));
    return base64Url.encode(bytes).replaceAll('=', '');
  }
  
  /// Sanitize input to prevent injection attacks
  static String sanitizeInput(String input) {
    return input
        .replaceAll('<', '')
        .replaceAll('>', '')
        .replaceAll('"', '')
        .replaceAll("'", '')
        .replaceAll('&', '&amp;')
        .trim();
  }
  
  /// Generate a secure random number
  static int generateSecureRandomNumber({int max = 1000000}) {
    final random = Random.secure();
    return random.nextInt(max);
  }
  
  /// Check if a string is a valid email format
  static bool isValidEmail(String email) {
    return RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$').hasMatch(email);
  }
  
  /// Check if a password meets security requirements
  static bool isValidPassword(String password) {
    // At least 8 characters, contains uppercase, lowercase, number, and special character
    return password.length >= 8 &&
           RegExp(r'[A-Z]').hasMatch(password) &&
           RegExp(r'[a-z]').hasMatch(password) &&
           RegExp(r'[0-9]').hasMatch(password) &&
           RegExp(r'[!@#$%^&*(),.?":{}|<>]').hasMatch(password);
  }
  
  /// Generate a secure username suggestion
  static String generateSecureUsername(String base) {
    final sanitized = base.replaceAll(RegExp(r'[^a-zA-Z0-9_]'), '').toLowerCase();
    final random = Random.secure();
    final suffix = random.nextInt(9999) + 1000;
    return '${sanitized}_$suffix';
  }
}
