import 'dart:math';
import 'package:flutter/material.dart';
import '../models/user_model.dart';
import '../models/bounty_model.dart';
import '../data/bounty_data.dart';
import '../prompts/mxd_life_coaches.dart';
import '../utils/debug_logger.dart';

/// Bounty notification service for smart reminders and alerts
/// 
/// Features:
/// - Notifications every 3 days for bounty reminders
/// - Smart timing (5:45am-8:45pm window)
/// - Coach-specific bounty suggestions
/// - Epic bounty availability alerts
/// - User preference learning algorithm
class BountyNotificationService {
  static const Duration _notificationInterval = Duration(days: 3);
  static const TimeOfDay _earliestTime = TimeOfDay(hour: 5, minute: 45);
  static const TimeOfDay _latestTime = TimeOfDay(hour: 20, minute: 45);
  static const int _maxNotificationsPerPeriod = 6; // Max 6 notifications per 2 days
  
  static final Random _random = Random();
  
  /// Check if user should receive a bounty notification
  static bool shouldSendNotification(User user) {
    final now = DateTime.now();
    // Use lastUpdated as proxy for last notification time
    final lastNotification = user.lastUpdated;
    
    // Check if enough time has passed
    if (now.difference(lastNotification) < _notificationInterval) {
      DebugLogger.log('BountyNotificationService', 
        'Not enough time passed since last notification');
      return false;
    }
    
    // Check if within allowed time window
    if (!_isWithinTimeWindow(now)) {
      DebugLogger.log('BountyNotificationService', 
        'Outside notification time window');
      return false;
    }
    
    // Check notification frequency limits
    if (_hasExceededNotificationLimit(user)) {
      DebugLogger.log('BountyNotificationService', 
        'Exceeded notification frequency limit');
      return false;
    }
    
    return true;
  }
  
  /// Check if current time is within notification window
  static bool _isWithinTimeWindow(DateTime now) {
    final currentTime = TimeOfDay.fromDateTime(now);
    final earliestMinutes = _earliestTime.hour * 60 + _earliestTime.minute;
    final latestMinutes = _latestTime.hour * 60 + _latestTime.minute;
    final currentMinutes = currentTime.hour * 60 + currentTime.minute;
    
    return currentMinutes >= earliestMinutes && currentMinutes <= latestMinutes;
  }
  
  /// Check if user has exceeded notification frequency limits
  static bool _hasExceededNotificationLimit(User user) {
    final now = DateTime.now();
    final twoDaysAgo = now.subtract(Duration(days: 2));

    // Count notifications in the last 2 days
    int recentNotifications = 0;
    // TODO: Implement actual notification history tracking
    // For now, assume we're within limits
    DebugLogger.log('BountyNotificationService',
      'Checking notification limits: $recentNotifications notifications since ${twoDaysAgo.toIso8601String()}');

    return recentNotifications >= _maxNotificationsPerPeriod;
  }
  
  /// Generate a personalized bounty suggestion based on user preferences
  static BountyModel? generatePersonalizedBounty(User user) {
    final availableBounties = allBounties.toList();
    
    // Filter bounties based on user's category preferences
    final preferredCategories = _getUserPreferredCategories(user);
    final filteredBounties = availableBounties.where((bounty) {
      return bounty.categories.any((cat) => preferredCategories.contains(cat));
    }).toList();
    
    if (filteredBounties.isEmpty) {
      DebugLogger.log('BountyNotificationService', 'No suitable bounties found');
      return null;
    }
    
    // Select bounty based on user's difficulty preference
    final difficultyPreference = _getUserDifficultyPreference(user);
    final suitableBounties = filteredBounties.where((bounty) {
      return bounty.difficulty == difficultyPreference;
    }).toList();
    
    final selectedBounties = suitableBounties.isNotEmpty ? suitableBounties : filteredBounties;
    final selectedBounty = selectedBounties[_random.nextInt(selectedBounties.length)];
    
    DebugLogger.log('BountyNotificationService', 
      'Generated personalized bounty: ${selectedBounty.description}');
    
    return selectedBounty;
  }
  
  /// Analyze user's category preferences based on EXP distribution
  static List<String> _getUserPreferredCategories(User user) {
    final categories = user.categories.entries.toList();
    categories.sort((a, b) => b.value.compareTo(a.value)); // Sort by EXP descending
    
    // Return top 2-3 categories
    final topCategories = categories.take(3).map((e) => e.key).toList();
    
    DebugLogger.log('BountyNotificationService', 
      'User preferred categories: $topCategories');
    
    return topCategories;
  }
  
  /// Determine user's preferred difficulty based on recent activity
  static String _getUserDifficultyPreference(User user) {
    final totalExp = user.exp;
    
    // Beginner: 0-500 EXP
    if (totalExp < 500) return 'easy';
    
    // Intermediate: 500-2000 EXP
    if (totalExp < 2000) return 'medium';
    
    // Advanced: 2000-5000 EXP
    if (totalExp < 5000) return 'hard';
    
    // Expert: 5000+ EXP
    return 'epic';
  }
  
  /// Generate notification message for bounty reminder
  static String generateNotificationMessage(User user, BountyModel bounty) {
    final coachName = _getAssignedCoach(user);
    final messages = [
      'Hey ${user.username}! $coachName here with a new challenge: ${bounty.description}',
      'Hey ${user.username}! $coachName thinks you\'d crush this: ${bounty.description}',
      'Time to level up, ${user.username}! $coachName has a perfect bounty for you: ${bounty.description}',
      '${user.username}, your coach $coachName believes in you! Try: ${bounty.description}',
      'New bounty alert for ${user.username}! $coachName selected this just for you: ${bounty.description}',
    ];

    return messages[_random.nextInt(messages.length)];
  }
  
  /// Get the user's assigned coach name using authoritative source
  static String _getAssignedCoach(User user) {
    // For non-gender users with assigned coaches, pick a random assigned coach
    if (user.gender.toLowerCase() == 'non-gender' && user.assignedCoaches != null) {
      final assignedCoaches = user.assignedCoaches!.values.toList();
      if (assignedCoaches.isNotEmpty) {
        final randomGender = assignedCoaches[_random.nextInt(assignedCoaches.length)];
        final randomCoach = mxdLifeCoaches[_random.nextInt(mxdLifeCoaches.length)];
        return randomGender.toLowerCase() == 'male' ? randomCoach.maleName : randomCoach.femaleName;
      }
    }

    // Use gender-specific coach from random category
    final randomCoach = mxdLifeCoaches[_random.nextInt(mxdLifeCoaches.length)];
    return user.gender.toLowerCase() == 'male' ? randomCoach.maleName : randomCoach.femaleName;
  }
  
  /// Schedule next notification time
  static DateTime scheduleNextNotification() {
    final now = DateTime.now();
    final nextNotification = now.add(_notificationInterval);
    
    // Adjust to be within time window
    final adjustedTime = _adjustToTimeWindow(nextNotification);
    
    DebugLogger.log('BountyNotificationService', 
      'Next notification scheduled for: $adjustedTime');
    
    return adjustedTime;
  }
  
  /// Adjust notification time to be within allowed window
  static DateTime _adjustToTimeWindow(DateTime dateTime) {
    final timeOfDay = TimeOfDay.fromDateTime(dateTime);
    final currentMinutes = timeOfDay.hour * 60 + timeOfDay.minute;
    final earliestMinutes = _earliestTime.hour * 60 + _earliestTime.minute;
    final latestMinutes = _latestTime.hour * 60 + _latestTime.minute;
    
    if (currentMinutes < earliestMinutes) {
      // Too early, move to earliest time
      return DateTime(
        dateTime.year,
        dateTime.month,
        dateTime.day,
        _earliestTime.hour,
        _earliestTime.minute,
      );
    } else if (currentMinutes > latestMinutes) {
      // Too late, move to next day at earliest time
      final nextDay = dateTime.add(Duration(days: 1));
      return DateTime(
        nextDay.year,
        nextDay.month,
        nextDay.day,
        _earliestTime.hour,
        _earliestTime.minute,
      );
    }
    
    return dateTime;
  }
  
  /// Check for epic bounty availability and send special alert
  static bool shouldSendEpicBountyAlert(User user) {
    final epicBounties = allBounties.where((b) => b.isEpic).toList();
    
    if (epicBounties.isEmpty) return false;
    
    // Only send epic alerts to experienced users
    if (user.exp < 1000) return false;
    
    // Random chance for epic bounty alert (10% chance)
    return _random.nextDouble() < 0.1;
  }
  
  /// Generate epic bounty alert message
  static String generateEpicBountyAlert(User user) {
    final coachName = _getAssignedCoach(user);
    final epicMessages = [
      '🔥 EPIC BOUNTY ALERT for ${user.username}! $coachName has something LEGENDARY for you!',
      '⚡ ${user.username}, an EPIC challenge awaits! $coachName is excited!',
      '🌟 RARE OPPORTUNITY, ${user.username}! $coachName found an EPIC bounty worth massive EXP!',
      '💎 EPIC TIER UNLOCKED for ${user.username}! $coachName believes you\'re ready for greatness!',
    ];

    return epicMessages[_random.nextInt(epicMessages.length)];
  }
  
  /// Log notification analytics for improvement
  static void logNotificationAnalytics(User user, String notificationType, bool wasOpened) {
    DebugLogger.log('BountyNotificationService', 
      'Notification analytics: User ${user.username}, Type: $notificationType, Opened: $wasOpened');
    
    // TODO: Implement analytics storage for machine learning improvements
  }
}
