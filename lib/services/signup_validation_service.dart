import 'dart:async';
import '../services/klaviyo_service.dart';
import '../services/comprehensive_logging_service.dart';

/// 🔍 Signup Validation Service
/// 
/// Provides comprehensive real-time validation for signup fields including
/// username availability checking, email validation, and password strength.
/// Includes debounced validation to prevent excessive API calls.
/// 
/// Features:
/// - Real-time username availability checking
/// - Email format and availability validation
/// - Password strength validation
/// - Debounced validation to reduce API calls
/// - Offline validation fallbacks
/// - Comprehensive error messaging
class SignupValidationService {
  static final SignupValidationService _instance = SignupValidationService._internal();
  factory SignupValidationService() => _instance;
  SignupValidationService._internal();

  // Debounce timers
  Timer? _usernameDebounceTimer;
  Timer? _emailDebounceTimer;
  
  // Cache for validation results
  final Map<String, ValidationResult> _usernameCache = {};
  final Map<String, ValidationResult> _emailCache = {};
  
  // Reserved usernames (case insensitive)
  static const List<String> _reservedUsernames = [
    'admin', 'test', 'user', 'mxd', 'support', 'help', 'info', 'contact',
    'ADMIN', 'TEST', 'USER', 'MXD', 'SUPPORT', 'HELP', 'INFO', 'CONTACT',
    'root', 'system', 'null', 'undefined', 'api', 'www', 'mail', 'email',
  ];

  /// Validate username with real-time availability checking
  Future<ValidationResult> validateUsername(
    String username, {
    bool checkAvailability = true,
    Duration debounceDelay = const Duration(milliseconds: 500),
  }) async {
    // Cancel previous timer
    _usernameDebounceTimer?.cancel();
    
    // Check cache first
    if (_usernameCache.containsKey(username)) {
      return _usernameCache[username]!;
    }
    
    // Basic validation (immediate)
    final basicResult = _validateUsernameBasic(username);
    if (!basicResult.isValid) {
      _usernameCache[username] = basicResult;
      return basicResult;
    }
    
    if (!checkAvailability) {
      return basicResult;
    }
    
    // Debounced availability check
    final completer = Completer<ValidationResult>();
    
    _usernameDebounceTimer = Timer(debounceDelay, () async {
      try {
        final availabilityResult = await _checkUsernameAvailability(username);
        _usernameCache[username] = availabilityResult;
        completer.complete(availabilityResult);
      } catch (e) {
        final errorResult = ValidationResult(
          isValid: false,
          message: 'Unable to check username availability',
          field: 'username',
          errorType: ValidationErrorType.networkError,
        );
        completer.complete(errorResult);
      }
    });
    
    return completer.future;
  }

  /// Validate email with format and availability checking
  Future<ValidationResult> validateEmail(
    String email, {
    bool checkAvailability = true,
    Duration debounceDelay = const Duration(milliseconds: 500),
  }) async {
    // Cancel previous timer
    _emailDebounceTimer?.cancel();
    
    // Check cache first
    if (_emailCache.containsKey(email)) {
      return _emailCache[email]!;
    }
    
    // Basic validation (immediate)
    final basicResult = _validateEmailBasic(email);
    if (!basicResult.isValid) {
      _emailCache[email] = basicResult;
      return basicResult;
    }
    
    if (!checkAvailability) {
      return basicResult;
    }
    
    // Debounced availability check
    final completer = Completer<ValidationResult>();
    
    _emailDebounceTimer = Timer(debounceDelay, () async {
      try {
        final availabilityResult = await _checkEmailAvailability(email);
        _emailCache[email] = availabilityResult;
        completer.complete(availabilityResult);
      } catch (e) {
        final errorResult = ValidationResult(
          isValid: false,
          message: 'Unable to check email availability',
          field: 'email',
          errorType: ValidationErrorType.networkError,
        );
        completer.complete(errorResult);
      }
    });
    
    return completer.future;
  }

  /// Validate password strength
  ValidationResult validatePassword(String password) {
    if (password.isEmpty) {
      return ValidationResult(
        isValid: false,
        message: 'Password is required',
        field: 'password',
        errorType: ValidationErrorType.required,
      );
    }
    
    if (password.length < 8) {
      return ValidationResult(
        isValid: false,
        message: 'Password must be at least 8 characters',
        field: 'password',
        errorType: ValidationErrorType.tooShort,
      );
    }
    
    // Check for required character types
    final hasUppercase = RegExp(r'[A-Z]').hasMatch(password);
    final hasLowercase = RegExp(r'[a-z]').hasMatch(password);
    final hasNumbers = RegExp(r'[0-9]').hasMatch(password);
    final hasSpecialChars = RegExp(r'[!@#$%^&*(),.?":{}|<>]').hasMatch(password);
    
    if (!hasUppercase) {
      return ValidationResult(
        isValid: false,
        message: 'Password must contain at least one uppercase letter',
        field: 'password',
        errorType: ValidationErrorType.missingUppercase,
      );
    }
    
    if (!hasLowercase) {
      return ValidationResult(
        isValid: false,
        message: 'Password must contain at least one lowercase letter',
        field: 'password',
        errorType: ValidationErrorType.missingLowercase,
      );
    }
    
    if (!hasNumbers) {
      return ValidationResult(
        isValid: false,
        message: 'Password must contain at least one number',
        field: 'password',
        errorType: ValidationErrorType.missingNumbers,
      );
    }
    
    if (!hasSpecialChars) {
      return ValidationResult(
        isValid: false,
        message: 'Password must contain at least one special character',
        field: 'password',
        errorType: ValidationErrorType.missingSpecialChars,
      );
    }
    
    return ValidationResult(
      isValid: true,
      message: 'Password is strong',
      field: 'password',
      errorType: ValidationErrorType.none,
    );
  }

  /// Basic username validation (no network calls)
  ValidationResult _validateUsernameBasic(String username) {
    if (username.isEmpty) {
      return ValidationResult(
        isValid: false,
        message: 'Username is required',
        field: 'username',
        errorType: ValidationErrorType.required,
      );
    }
    
    if (username.length > 15) {
      return ValidationResult(
        isValid: false,
        message: 'Username must be 15 characters or less',
        field: 'username',
        errorType: ValidationErrorType.tooLong,
      );
    }
    
    if (username.length < 3) {
      return ValidationResult(
        isValid: false,
        message: 'Username must be at least 3 characters',
        field: 'username',
        errorType: ValidationErrorType.tooShort,
      );
    }
    
    // Check if starts with special character or number
    if (RegExp(r'^[^a-zA-Z]').hasMatch(username)) {
      return ValidationResult(
        isValid: false,
        message: 'Username must start with a letter',
        field: 'username',
        errorType: ValidationErrorType.invalidFormat,
      );
    }
    
    // Check for invalid characters
    if (!RegExp(r'^[a-zA-Z0-9_\-!$*]*$').hasMatch(username)) {
      return ValidationResult(
        isValid: false,
        message: 'Username can only contain letters, numbers, hyphens, underscores, !, \$, *',
        field: 'username',
        errorType: ValidationErrorType.invalidCharacters,
      );
    }
    
    // Check reserved usernames
    if (_reservedUsernames.contains(username.toLowerCase()) || 
        _reservedUsernames.any((reserved) => username.toLowerCase().contains(reserved.toLowerCase()))) {
      return ValidationResult(
        isValid: false,
        message: 'This username is not available',
        field: 'username',
        errorType: ValidationErrorType.reserved,
      );
    }
    
    return ValidationResult(
      isValid: true,
      message: 'Username format is valid',
      field: 'username',
      errorType: ValidationErrorType.none,
    );
  }

  /// Basic email validation (no network calls)
  ValidationResult _validateEmailBasic(String email) {
    if (email.isEmpty) {
      return ValidationResult(
        isValid: false,
        message: 'Email is required',
        field: 'email',
        errorType: ValidationErrorType.required,
      );
    }
    
    if (!KlaviyoService.isValidEmailFormat(email)) {
      return ValidationResult(
        isValid: false,
        message: 'Please enter a valid email address',
        field: 'email',
        errorType: ValidationErrorType.invalidFormat,
      );
    }
    
    return ValidationResult(
      isValid: true,
      message: 'Email format is valid',
      field: 'email',
      errorType: ValidationErrorType.none,
    );
  }

  /// Check username availability via Klaviyo
  Future<ValidationResult> _checkUsernameAvailability(String username) async {
    try {
      await ComprehensiveLoggingService.logInfo('🔍 Checking username availability: $username');
      
      // For now, we'll use email checking as a proxy for username checking
      // In a real implementation, you'd have a dedicated username checking endpoint
      final isAvailable = !await KlaviyoService.emailExists('$<EMAIL>');
      
      if (isAvailable) {
        return ValidationResult(
          isValid: true,
          message: 'Username is available',
          field: 'username',
          errorType: ValidationErrorType.none,
        );
      } else {
        return ValidationResult(
          isValid: false,
          message: 'Username is already taken',
          field: 'username',
          errorType: ValidationErrorType.alreadyExists,
          suggestions: _generateUsernameSuggestions(username),
        );
      }
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Username availability check failed: $e');
      rethrow;
    }
  }

  /// Check email availability via Klaviyo
  Future<ValidationResult> _checkEmailAvailability(String email) async {
    try {
      await ComprehensiveLoggingService.logInfo('🔍 Checking email availability: $email');
      
      final emailExists = await KlaviyoService.emailExists(email);
      
      if (!emailExists) {
        return ValidationResult(
          isValid: true,
          message: 'Email is available',
          field: 'email',
          errorType: ValidationErrorType.none,
        );
      } else {
        return ValidationResult(
          isValid: false,
          message: 'This email is already registered',
          field: 'email',
          errorType: ValidationErrorType.alreadyExists,
        );
      }
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Email availability check failed: $e');
      rethrow;
    }
  }

  /// Generate username suggestions
  List<String> _generateUsernameSuggestions(String username) {
    final suggestions = <String>[];
    
    // Add numbered variations
    for (int i = 1; i <= 3; i++) {
      suggestions.add('$username$i');
    }
    
    // Add underscore variations
    suggestions.add('${username}_');
    suggestions.add('_$username');
    
    // Add year variation
    final currentYear = DateTime.now().year;
    suggestions.add('$username$currentYear');
    
    return suggestions.take(3).toList(); // Limit to 3 suggestions
  }

  /// Clear validation cache
  void clearCache() {
    _usernameCache.clear();
    _emailCache.clear();
  }

  /// Cancel any pending validations
  void cancelPendingValidations() {
    _usernameDebounceTimer?.cancel();
    _emailDebounceTimer?.cancel();
  }

  /// Dispose of the service
  void dispose() {
    cancelPendingValidations();
    clearCache();
  }
}

/// Validation result model
class ValidationResult {
  final bool isValid;
  final String message;
  final String field;
  final ValidationErrorType errorType;
  final List<String> suggestions;

  ValidationResult({
    required this.isValid,
    required this.message,
    required this.field,
    required this.errorType,
    this.suggestions = const [],
  });

  Map<String, dynamic> toJson() => {
    'isValid': isValid,
    'message': message,
    'field': field,
    'errorType': errorType.toString(),
    'suggestions': suggestions,
  };
}

/// Types of validation errors
enum ValidationErrorType {
  none,
  required,
  tooShort,
  tooLong,
  invalidFormat,
  invalidCharacters,
  reserved,
  alreadyExists,
  networkError,
  missingUppercase,
  missingLowercase,
  missingNumbers,
  missingSpecialChars,
}
