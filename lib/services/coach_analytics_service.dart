// 📁 lib/services/coach_analytics_service.dart

import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';


/// Enterprise-grade analytics service for monitoring coach system performance,
/// user engagement, cost optimization, and proactive issue detection.
class CoachAnalyticsService {
  static final CoachAnalyticsService _instance = CoachAnalyticsService._internal();
  factory CoachAnalyticsService() => _instance;
  CoachAnalyticsService._internal();

  static const FlutterSecureStorage _storage = FlutterSecureStorage();
  static const String _analyticsKey = 'coach_analytics';
  static const String _performanceKey = 'performance_metrics';
  static const String _alertsKey = 'system_alerts';
  
  // Real-time metrics cache
  static final Map<String, dynamic> _realtimeMetrics = {};
  static final List<SystemAlert> _activeAlerts = [];
  static Timer? _metricsTimer;
  
  /// Initialize analytics service with real-time monitoring
  static Future<void> initialize() async {
    try {
      await _loadHistoricalData();
      _startRealtimeMonitoring();
      
      if (kDebugMode) {
        print('📊 Coach analytics service initialized');
        print('🔍 Real-time monitoring: Active');
        print('⚠️ Alert system: Ready');
      }
    } catch (e) {
      if (kDebugMode) print('❌ Failed to initialize analytics: $e');
    }
  }

  /// Record a coach interaction for analytics
  static Future<void> recordInteraction({
    required String userId,
    required String category,
    required String coachName,
    required String userMessage,
    required String coachResponse,
    required int responseTimeMs,
    required String model,
    required double cost,
    bool wasSuccessful = true,
    String? errorMessage,
  }) async {
    try {
      final interaction = CoachInteraction(
        userId: userId,
        category: category,
        coachName: coachName,
        userMessage: userMessage,
        coachResponse: coachResponse,
        responseTimeMs: responseTimeMs,
        model: model,
        cost: cost,
        wasSuccessful: wasSuccessful,
        errorMessage: errorMessage,
        timestamp: DateTime.now(),
      );

      await _storeInteraction(interaction);
      await _updateRealtimeMetrics(interaction);
      await _checkForAlerts(interaction);
      
      if (kDebugMode) {
        print('📊 Interaction recorded: $coachName, ${responseTimeMs}ms, \$${cost.toStringAsFixed(4)}');
      }
      
    } catch (e) {
      if (kDebugMode) print('❌ Failed to record interaction: $e');
    }
  }

  /// Get comprehensive analytics dashboard data
  static Future<AnalyticsDashboard> getDashboardData() async {
    try {
      final interactions = await _getRecentInteractions(days: 7);
      final performance = await _getPerformanceMetrics();
      
      return AnalyticsDashboard(
        totalInteractions: interactions.length,
        successRate: _calculateSuccessRate(interactions),
        averageResponseTime: _calculateAverageResponseTime(interactions),
        totalCost: _calculateTotalCost(interactions),
        costPerInteraction: _calculateCostPerInteraction(interactions),
        modelBreakdown: _getModelBreakdown(interactions),
        coachPopularity: _getCoachPopularity(interactions),
        categoryBreakdown: _getCategoryBreakdown(interactions),
        hourlyDistribution: _getHourlyDistribution(interactions),
        performanceMetrics: performance,
        activeAlerts: List.from(_activeAlerts),
        realtimeMetrics: Map.from(_realtimeMetrics),
      );
    } catch (e) {
      if (kDebugMode) print('❌ Failed to get dashboard data: $e');
      return AnalyticsDashboard.empty();
    }
  }

  /// Get user-specific analytics
  static Future<UserAnalytics> getUserAnalytics(String userId) async {
    try {
      final interactions = await _getUserInteractions(userId, days: 30);
      
      return UserAnalytics(
        userId: userId,
        totalInteractions: interactions.length,
        favoriteCoach: _getFavoriteCoach(interactions),
        favoriteCategory: _getFavoriteCategory(interactions),
        averageSessionLength: _calculateAverageSessionLength(interactions),
        engagementScore: _calculateEngagementScore(interactions),
        costGenerated: _calculateTotalCost(interactions),
        lastInteraction: interactions.isNotEmpty ? interactions.first.timestamp : null,
        weeklyTrend: _getWeeklyTrend(interactions),
        satisfactionScore: _calculateSatisfactionScore(interactions),
      );
    } catch (e) {
      if (kDebugMode) print('❌ Failed to get user analytics: $e');
      return UserAnalytics.empty(userId);
    }
  }

  /// Get system health metrics
  static Future<SystemHealth> getSystemHealth() async {
    try {
      final recentInteractions = await _getRecentInteractions(hours: 1);
      final errorRate = _calculateErrorRate(recentInteractions);
      final avgResponseTime = _calculateAverageResponseTime(recentInteractions);
      
      return SystemHealth(
        status: _determineSystemStatus(errorRate, avgResponseTime),
        errorRate: errorRate,
        averageResponseTime: avgResponseTime,
        activeUsers: _getActiveUserCount(),
        systemLoad: _calculateSystemLoad(),
        costBurnRate: _calculateCostBurnRate(),
        alertCount: _activeAlerts.length,
        uptime: _calculateUptime(),
        lastHealthCheck: DateTime.now(),
      );
    } catch (e) {
      if (kDebugMode) print('❌ Failed to get system health: $e');
      return SystemHealth.critical();
    }
  }

  /// Export analytics data for external analysis
  static Future<String> exportAnalytics({
    required DateTime startDate,
    required DateTime endDate,
    String format = 'json',
  }) async {
    try {
      final interactions = await _getInteractionsInRange(startDate, endDate);
      
      final exportData = {
        'export_info': {
          'generated_at': DateTime.now().toIso8601String(),
          'start_date': startDate.toIso8601String(),
          'end_date': endDate.toIso8601String(),
          'total_interactions': interactions.length,
          'format': format,
        },
        'summary': {
          'total_cost': _calculateTotalCost(interactions),
          'success_rate': _calculateSuccessRate(interactions),
          'average_response_time': _calculateAverageResponseTime(interactions),
          'unique_users': _getUniqueUserCount(interactions),
        },
        'interactions': interactions.map((i) => i.toJson()).toList(),
        'model_breakdown': _getModelBreakdown(interactions),
        'coach_breakdown': _getCoachPopularity(interactions),
        'category_breakdown': _getCategoryBreakdown(interactions),
      };
      
      return jsonEncode(exportData);
    } catch (e) {
      if (kDebugMode) print('❌ Failed to export analytics: $e');
      return '{"error": "Export failed"}';
    }
  }

  // Private helper methods
  static void _startRealtimeMonitoring() {
    _metricsTimer?.cancel();
    _metricsTimer = Timer.periodic(const Duration(minutes: 1), (timer) {
      _updateRealtimeMetrics(null);
    });
  }

  static Future<void> _updateRealtimeMetrics(CoachInteraction? interaction) async {
    try {
      final now = DateTime.now();
      final recentInteractions = await _getRecentInteractions(minutes: 5);
      
      _realtimeMetrics['current_rps'] = recentInteractions.length / 5.0; // Requests per second
      _realtimeMetrics['active_users'] = _getActiveUserCount();
      _realtimeMetrics['avg_response_time'] = _calculateAverageResponseTime(recentInteractions);
      _realtimeMetrics['error_rate'] = _calculateErrorRate(recentInteractions);
      _realtimeMetrics['cost_per_minute'] = _calculateCostBurnRate();
      _realtimeMetrics['last_updated'] = now.toIso8601String();

      // Store performance metrics using the performance key
      await _storage.write(
        key: '${_performanceKey}_${now.millisecondsSinceEpoch}',
        value: jsonEncode(_realtimeMetrics),
      );
      
      if (interaction != null) {
        _realtimeMetrics['last_interaction'] = {
          'coach': interaction.coachName,
          'category': interaction.category,
          'response_time': interaction.responseTimeMs,
          'cost': interaction.cost,
          'timestamp': interaction.timestamp.toIso8601String(),
        };
      }
    } catch (e) {
      if (kDebugMode) print('❌ Failed to update realtime metrics: $e');
    }
  }

  static Future<void> _checkForAlerts(CoachInteraction interaction) async {
    try {
      // High response time alert
      if (interaction.responseTimeMs > 30000) { // 30 seconds
        _addAlert(SystemAlert(
          id: 'high_response_time_${DateTime.now().millisecondsSinceEpoch}',
          type: AlertType.performance,
          severity: AlertSeverity.warning,
          title: 'High Response Time Detected',
          message: '${interaction.coachName} took ${interaction.responseTimeMs}ms to respond',
          timestamp: DateTime.now(),
        ));
      }
      
      // High cost alert
      if (interaction.cost > 0.10) { // $0.10 per interaction
        _addAlert(SystemAlert(
          id: 'high_cost_${DateTime.now().millisecondsSinceEpoch}',
          type: AlertType.cost,
          severity: AlertSeverity.warning,
          title: 'High Cost Interaction',
          message: 'Interaction cost \$${interaction.cost.toStringAsFixed(4)} exceeds threshold',
          timestamp: DateTime.now(),
        ));
      }
      
      // Error alert
      if (!interaction.wasSuccessful) {
        _addAlert(SystemAlert(
          id: 'error_${DateTime.now().millisecondsSinceEpoch}',
          type: AlertType.error,
          severity: AlertSeverity.critical,
          title: 'Coach Interaction Failed',
          message: interaction.errorMessage ?? 'Unknown error occurred',
          timestamp: DateTime.now(),
        ));
      }
      
      // Check for system-wide issues
      await _checkSystemAlerts();
      
    } catch (e) {
      if (kDebugMode) print('❌ Failed to check alerts: $e');
    }
  }

  static void _addAlert(SystemAlert alert) {
    _activeAlerts.add(alert);

    // Keep only last 100 alerts
    if (_activeAlerts.length > 100) {
      _activeAlerts.removeAt(0);
    }

    // Store alert using the alerts key
    _storage.write(
      key: '${_alertsKey}_${alert.id}',
      value: jsonEncode({
        'id': alert.id,
        'type': alert.type.toString(),
        'severity': alert.severity.toString(),
        'title': alert.title,
        'message': alert.message,
        'timestamp': alert.timestamp.toIso8601String(),
      }),
    );

    if (kDebugMode) {
      print('⚠️ ALERT: ${alert.title} - ${alert.message}');
    }
  }

  static Future<void> _checkSystemAlerts() async {
    try {
      final recentInteractions = await _getRecentInteractions(minutes: 10);
      
      // High error rate alert
      final errorRate = _calculateErrorRate(recentInteractions);
      if (errorRate > 0.1) { // 10% error rate
        _addAlert(SystemAlert(
          id: 'high_error_rate_${DateTime.now().millisecondsSinceEpoch}',
          type: AlertType.system,
          severity: AlertSeverity.critical,
          title: 'High System Error Rate',
          message: 'Error rate: ${(errorRate * 100).toStringAsFixed(1)}% over last 10 minutes',
          timestamp: DateTime.now(),
        ));
      }
      
      // High cost burn rate alert
      final costBurnRate = _calculateCostBurnRate();
      if (costBurnRate > 1.0) { // $1 per minute
        _addAlert(SystemAlert(
          id: 'high_burn_rate_${DateTime.now().millisecondsSinceEpoch}',
          type: AlertType.cost,
          severity: AlertSeverity.warning,
          title: 'High Cost Burn Rate',
          message: 'Current burn rate: \$${costBurnRate.toStringAsFixed(2)}/minute',
          timestamp: DateTime.now(),
        ));
      }
      
    } catch (e) {
      if (kDebugMode) print('❌ Failed to check system alerts: $e');
    }
  }

  // Calculation methods
  static double _calculateSuccessRate(List<CoachInteraction> interactions) {
    if (interactions.isEmpty) return 1.0;
    final successful = interactions.where((i) => i.wasSuccessful).length;
    return successful / interactions.length;
  }

  static double _calculateAverageResponseTime(List<CoachInteraction> interactions) {
    if (interactions.isEmpty) return 0.0;
    final total = interactions.fold(0, (sum, i) => sum + i.responseTimeMs);
    return total / interactions.length;
  }

  static double _calculateTotalCost(List<CoachInteraction> interactions) {
    return interactions.fold(0.0, (sum, i) => sum + i.cost);
  }

  static double _calculateCostPerInteraction(List<CoachInteraction> interactions) {
    if (interactions.isEmpty) return 0.0;
    return _calculateTotalCost(interactions) / interactions.length;
  }

  static double _calculateErrorRate(List<CoachInteraction> interactions) {
    if (interactions.isEmpty) return 0.0;
    final errors = interactions.where((i) => !i.wasSuccessful).length;
    return errors / interactions.length;
  }

  static Map<String, int> _getModelBreakdown(List<CoachInteraction> interactions) {
    final breakdown = <String, int>{};
    for (final interaction in interactions) {
      breakdown[interaction.model] = (breakdown[interaction.model] ?? 0) + 1;
    }
    return breakdown;
  }

  static Map<String, int> _getCoachPopularity(List<CoachInteraction> interactions) {
    final breakdown = <String, int>{};
    for (final interaction in interactions) {
      breakdown[interaction.coachName] = (breakdown[interaction.coachName] ?? 0) + 1;
    }
    return breakdown;
  }

  static Map<String, int> _getCategoryBreakdown(List<CoachInteraction> interactions) {
    final breakdown = <String, int>{};
    for (final interaction in interactions) {
      breakdown[interaction.category] = (breakdown[interaction.category] ?? 0) + 1;
    }
    return breakdown;
  }

  static Map<int, int> _getHourlyDistribution(List<CoachInteraction> interactions) {
    final distribution = <int, int>{};
    for (final interaction in interactions) {
      final hour = interaction.timestamp.hour;
      distribution[hour] = (distribution[hour] ?? 0) + 1;
    }
    return distribution;
  }

  static int _getActiveUserCount() {
    // This would be implemented based on your user tracking system
    return _realtimeMetrics['active_users'] ?? 0;
  }

  static double _calculateSystemLoad() {
    // Simplified system load calculation
    final rps = _realtimeMetrics['current_rps'] ?? 0.0;
    return (rps / 10.0).clamp(0.0, 1.0); // Normalize to 0-1
  }

  static double _calculateCostBurnRate() {
    // Calculate cost per minute based on recent interactions
    return _realtimeMetrics['cost_per_minute'] ?? 0.0;
  }

  static Duration _calculateUptime() {
    // This would track actual service uptime
    return const Duration(hours: 24); // Placeholder
  }

  static SystemStatus _determineSystemStatus(double errorRate, double avgResponseTime) {
    if (errorRate > 0.1 || avgResponseTime > 30000) {
      return SystemStatus.critical;
    } else if (errorRate > 0.05 || avgResponseTime > 15000) {
      return SystemStatus.warning;
    }
    return SystemStatus.healthy;
  }

  // Data persistence methods (simplified - would use your existing storage system)
  static Future<void> _storeInteraction(CoachInteraction interaction) async {
    try {
      // Store interaction data using the analytics key
      final interactionData = {
        'userId': interaction.userId,
        'category': interaction.category,
        'coachName': interaction.coachName,
        'timestamp': interaction.timestamp.toIso8601String(),
        'responseTimeMs': interaction.responseTimeMs,
        'cost': interaction.cost,
        'wasSuccessful': interaction.wasSuccessful,
      };

      // Store using the analytics key
      await _storage.write(
        key: '${_analyticsKey}_${interaction.timestamp.millisecondsSinceEpoch}',
        value: jsonEncode(interactionData),
      );

      if (kDebugMode) {
        print('📊 Stored interaction with key: $_analyticsKey');
      }
    } catch (e) {
      if (kDebugMode) print('❌ Failed to store interaction: $e');
    }
  }

  static Future<List<CoachInteraction>> _getRecentInteractions({int? days, int? hours, int? minutes}) async {
    // Implementation would query your database
    return [];
  }

  static Future<List<CoachInteraction>> _getUserInteractions(String userId, {required int days}) async {
    // Implementation would query user-specific interactions
    return [];
  }

  static Future<List<CoachInteraction>> _getInteractionsInRange(DateTime start, DateTime end) async {
    // Implementation would query interactions in date range
    return [];
  }

  static Future<void> _loadHistoricalData() async {
    // Load any cached analytics data
  }

  static Future<PerformanceMetrics> _getPerformanceMetrics() async {
    // Get detailed performance metrics
    return PerformanceMetrics.empty();
  }

  // Additional helper methods for user analytics
  static String _getFavoriteCoach(List<CoachInteraction> interactions) {
    final popularity = _getCoachPopularity(interactions);
    if (popularity.isEmpty) return 'None';
    return popularity.entries.reduce((a, b) => a.value > b.value ? a : b).key;
  }

  static String _getFavoriteCategory(List<CoachInteraction> interactions) {
    final breakdown = _getCategoryBreakdown(interactions);
    if (breakdown.isEmpty) return 'None';
    return breakdown.entries.reduce((a, b) => a.value > b.value ? a : b).key;
  }

  static double _calculateAverageSessionLength(List<CoachInteraction> interactions) {
    // Calculate based on interaction patterns
    return 5.0; // Placeholder
  }

  static double _calculateEngagementScore(List<CoachInteraction> interactions) {
    // Calculate engagement based on frequency, length, etc.
    return 0.8; // Placeholder
  }

  static Map<String, int> _getWeeklyTrend(List<CoachInteraction> interactions) {
    // Calculate weekly interaction trends
    return {};
  }

  static double _calculateSatisfactionScore(List<CoachInteraction> interactions) {
    // Calculate based on success rate, response times, etc.
    return _calculateSuccessRate(interactions);
  }

  static int _getUniqueUserCount(List<CoachInteraction> interactions) {
    return interactions.map((i) => i.userId).toSet().length;
  }

  /// Dispose of resources
  static void dispose() {
    _metricsTimer?.cancel();
    _activeAlerts.clear();
    _realtimeMetrics.clear();
  }
}

// Data classes for analytics
class CoachInteraction {
  final String userId;
  final String category;
  final String coachName;
  final String userMessage;
  final String coachResponse;
  final int responseTimeMs;
  final String model;
  final double cost;
  final bool wasSuccessful;
  final String? errorMessage;
  final DateTime timestamp;

  CoachInteraction({
    required this.userId,
    required this.category,
    required this.coachName,
    required this.userMessage,
    required this.coachResponse,
    required this.responseTimeMs,
    required this.model,
    required this.cost,
    required this.wasSuccessful,
    this.errorMessage,
    required this.timestamp,
  });

  Map<String, dynamic> toJson() => {
    'userId': userId,
    'category': category,
    'coachName': coachName,
    'userMessage': userMessage,
    'coachResponse': coachResponse,
    'responseTimeMs': responseTimeMs,
    'model': model,
    'cost': cost,
    'wasSuccessful': wasSuccessful,
    'errorMessage': errorMessage,
    'timestamp': timestamp.toIso8601String(),
  };
}

class AnalyticsDashboard {
  final int totalInteractions;
  final double successRate;
  final double averageResponseTime;
  final double totalCost;
  final double costPerInteraction;
  final Map<String, int> modelBreakdown;
  final Map<String, int> coachPopularity;
  final Map<String, int> categoryBreakdown;
  final Map<int, int> hourlyDistribution;
  final PerformanceMetrics performanceMetrics;
  final List<SystemAlert> activeAlerts;
  final Map<String, dynamic> realtimeMetrics;

  AnalyticsDashboard({
    required this.totalInteractions,
    required this.successRate,
    required this.averageResponseTime,
    required this.totalCost,
    required this.costPerInteraction,
    required this.modelBreakdown,
    required this.coachPopularity,
    required this.categoryBreakdown,
    required this.hourlyDistribution,
    required this.performanceMetrics,
    required this.activeAlerts,
    required this.realtimeMetrics,
  });

  factory AnalyticsDashboard.empty() => AnalyticsDashboard(
    totalInteractions: 0,
    successRate: 1.0,
    averageResponseTime: 0.0,
    totalCost: 0.0,
    costPerInteraction: 0.0,
    modelBreakdown: {},
    coachPopularity: {},
    categoryBreakdown: {},
    hourlyDistribution: {},
    performanceMetrics: PerformanceMetrics.empty(),
    activeAlerts: [],
    realtimeMetrics: {},
  );
}

class UserAnalytics {
  final String userId;
  final int totalInteractions;
  final String favoriteCoach;
  final String favoriteCategory;
  final double averageSessionLength;
  final double engagementScore;
  final double costGenerated;
  final DateTime? lastInteraction;
  final Map<String, int> weeklyTrend;
  final double satisfactionScore;

  UserAnalytics({
    required this.userId,
    required this.totalInteractions,
    required this.favoriteCoach,
    required this.favoriteCategory,
    required this.averageSessionLength,
    required this.engagementScore,
    required this.costGenerated,
    this.lastInteraction,
    required this.weeklyTrend,
    required this.satisfactionScore,
  });

  factory UserAnalytics.empty(String userId) => UserAnalytics(
    userId: userId,
    totalInteractions: 0,
    favoriteCoach: 'None',
    favoriteCategory: 'None',
    averageSessionLength: 0.0,
    engagementScore: 0.0,
    costGenerated: 0.0,
    weeklyTrend: {},
    satisfactionScore: 0.0,
  );
}

class SystemHealth {
  final SystemStatus status;
  final double errorRate;
  final double averageResponseTime;
  final int activeUsers;
  final double systemLoad;
  final double costBurnRate;
  final int alertCount;
  final Duration uptime;
  final DateTime lastHealthCheck;

  SystemHealth({
    required this.status,
    required this.errorRate,
    required this.averageResponseTime,
    required this.activeUsers,
    required this.systemLoad,
    required this.costBurnRate,
    required this.alertCount,
    required this.uptime,
    required this.lastHealthCheck,
  });

  factory SystemHealth.critical() => SystemHealth(
    status: SystemStatus.critical,
    errorRate: 1.0,
    averageResponseTime: 60000,
    activeUsers: 0,
    systemLoad: 1.0,
    costBurnRate: 0.0,
    alertCount: 0,
    uptime: Duration.zero,
    lastHealthCheck: DateTime.now(),
  );
}

class SystemAlert {
  final String id;
  final AlertType type;
  final AlertSeverity severity;
  final String title;
  final String message;
  final DateTime timestamp;

  SystemAlert({
    required this.id,
    required this.type,
    required this.severity,
    required this.title,
    required this.message,
    required this.timestamp,
  });
}

class PerformanceMetrics {
  final double cpuUsage;
  final double memoryUsage;
  final double networkLatency;
  final double diskUsage;

  PerformanceMetrics({
    required this.cpuUsage,
    required this.memoryUsage,
    required this.networkLatency,
    required this.diskUsage,
  });

  factory PerformanceMetrics.empty() => PerformanceMetrics(
    cpuUsage: 0.0,
    memoryUsage: 0.0,
    networkLatency: 0.0,
    diskUsage: 0.0,
  );
}

enum SystemStatus { healthy, warning, critical }
enum AlertType { performance, cost, error, system }
enum AlertSeverity { info, warning, critical }
