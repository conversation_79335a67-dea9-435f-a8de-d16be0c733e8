// lib/services/transcript_analytics_service.dart

import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

/// Service for tracking transcript usage and coach effectiveness analytics
class TranscriptAnalyticsService {
  static const FlutterSecureStorage _storage = FlutterSecureStorage();
  static const String _analyticsKey = 'transcript_analytics';
  static const String _sessionKey = 'current_session';
  
  /// Track a coach interaction with transcript data
  static Future<void> trackCoachInteraction({
    required String category,
    required String coachName,
    required String userPrompt,
    required bool transcriptContentUsed,
    required double relevanceScore,
    required int sourceCount,
  }) async {
    try {
      final analytics = await _getAnalytics();
      final timestamp = DateTime.now().toIso8601String();
      
      final interaction = {
        'timestamp': timestamp,
        'category': category,
        'coachName': coachName,
        'userPromptLength': userPrompt.length,
        'transcriptContentUsed': transcriptContentUsed,
        'relevanceScore': relevanceScore,
        'sourceCount': sourceCount,
        'sessionId': await _getCurrentSessionId(),
      };
      
      analytics['interactions'] = (analytics['interactions'] as List? ?? [])
        ..add(interaction);
      
      // Update category stats
      final categoryStats = analytics['categoryStats'] as Map<String, dynamic>? ?? {};
      final catKey = category.toLowerCase();
      categoryStats[catKey] = {
        'totalInteractions': (categoryStats[catKey]?['totalInteractions'] ?? 0) + 1,
        'transcriptUsageCount': (categoryStats[catKey]?['transcriptUsageCount'] ?? 0) + 
            (transcriptContentUsed ? 1 : 0),
        'averageRelevanceScore': _updateAverage(
          categoryStats[catKey]?['averageRelevanceScore'] ?? 0.0,
          categoryStats[catKey]?['totalInteractions'] ?? 0,
          relevanceScore,
        ),
        'lastInteraction': timestamp,
      };
      analytics['categoryStats'] = categoryStats;
      
      // Update coach stats
      final coachStats = analytics['coachStats'] as Map<String, dynamic>? ?? {};
      coachStats[coachName] = {
        'totalInteractions': (coachStats[coachName]?['totalInteractions'] ?? 0) + 1,
        'transcriptUsageCount': (coachStats[coachName]?['transcriptUsageCount'] ?? 0) + 
            (transcriptContentUsed ? 1 : 0),
        'averageRelevanceScore': _updateAverage(
          coachStats[coachName]?['averageRelevanceScore'] ?? 0.0,
          coachStats[coachName]?['totalInteractions'] ?? 0,
          relevanceScore,
        ),
        'category': category,
        'lastInteraction': timestamp,
      };
      analytics['coachStats'] = coachStats;
      
      await _saveAnalytics(analytics);
      
      if (kDebugMode) {
        print('📊 Tracked interaction: $coachName ($category) - Transcript: $transcriptContentUsed, Score: ${relevanceScore.toStringAsFixed(2)}');
      }
    } catch (e) {
      if (kDebugMode) print('❌ Failed to track coach interaction: $e');
    }
  }
  
  /// Track response quality metrics
  static Future<void> trackResponseQuality({
    required String category,
    required int responseLength,
    required bool transcriptEnhanced,
  }) async {
    try {
      final analytics = await _getAnalytics();
      
      final qualityStats = analytics['qualityStats'] as Map<String, dynamic>? ?? {};
      final catKey = category.toLowerCase();
      
      qualityStats[catKey] = {
        'totalResponses': (qualityStats[catKey]?['totalResponses'] ?? 0) + 1,
        'averageResponseLength': _updateAverage(
          qualityStats[catKey]?['averageResponseLength'] ?? 0.0,
          qualityStats[catKey]?['totalResponses'] ?? 0,
          responseLength.toDouble(),
        ),
        'transcriptEnhancedCount': (qualityStats[catKey]?['transcriptEnhancedCount'] ?? 0) + 
            (transcriptEnhanced ? 1 : 0),
        'lastUpdate': DateTime.now().toIso8601String(),
      };
      
      analytics['qualityStats'] = qualityStats;
      await _saveAnalytics(analytics);
      
    } catch (e) {
      if (kDebugMode) print('❌ Failed to track response quality: $e');
    }
  }
  
  /// Track user satisfaction (can be called when user rates a response)
  static Future<void> trackUserSatisfaction({
    required String category,
    required String coachName,
    required int rating, // 1-5 scale
    bool? transcriptContentUsed,
  }) async {
    try {
      final analytics = await _getAnalytics();
      
      final satisfactionStats = analytics['satisfactionStats'] as Map<String, dynamic>? ?? {};
      final key = '${category.toLowerCase()}_$coachName';
      
      satisfactionStats[key] = {
        'totalRatings': (satisfactionStats[key]?['totalRatings'] ?? 0) + 1,
        'averageRating': _updateAverage(
          satisfactionStats[key]?['averageRating'] ?? 0.0,
          satisfactionStats[key]?['totalRatings'] ?? 0,
          rating.toDouble(),
        ),
        'transcriptEnhancedRatings': (satisfactionStats[key]?['transcriptEnhancedRatings'] ?? 0) + 
            ((transcriptContentUsed == true) ? 1 : 0),
        'lastRating': DateTime.now().toIso8601String(),
      };
      
      analytics['satisfactionStats'] = satisfactionStats;
      await _saveAnalytics(analytics);
      
      if (kDebugMode) {
        print('📊 User satisfaction: $coachName rated $rating/5');
      }
    } catch (e) {
      if (kDebugMode) print('❌ Failed to track user satisfaction: $e');
    }
  }
  
  /// Get comprehensive analytics summary
  static Future<Map<String, dynamic>> getAnalyticsSummary() async {
    try {
      final analytics = await _getAnalytics();
      
      final summary = <String, dynamic>{
        'overview': _generateOverview(analytics),
        'transcriptUsage': _generateTranscriptUsageStats(analytics),
        'coachEffectiveness': _generateCoachEffectivenessStats(analytics),
        'categoryPerformance': _generateCategoryPerformanceStats(analytics),
        'qualityMetrics': _generateQualityMetrics(analytics),
        'satisfactionMetrics': _generateSatisfactionMetrics(analytics),
        'lastUpdated': DateTime.now().toIso8601String(),
      };
      
      return summary;
    } catch (e) {
      if (kDebugMode) print('❌ Failed to get analytics summary: $e');
      return {};
    }
  }
  
  /// Get current session ID or create new one
  static Future<String> _getCurrentSessionId() async {
    try {
      final sessionData = await _storage.read(key: _sessionKey);
      if (sessionData != null) {
        final session = json.decode(sessionData);
        final sessionTime = DateTime.parse(session['timestamp']);
        
        // Session expires after 1 hour
        if (DateTime.now().difference(sessionTime).inHours < 1) {
          return session['id'];
        }
      }
      
      // Create new session
      final newSessionId = DateTime.now().millisecondsSinceEpoch.toString();
      final newSessionData = {
        'id': newSessionId,
        'timestamp': DateTime.now().toIso8601String(),
      };

      await _storage.write(key: _sessionKey, value: json.encode(newSessionData));
      return newSessionId;
    } catch (e) {
      return DateTime.now().millisecondsSinceEpoch.toString();
    }
  }
  
  /// Load analytics data from storage
  static Future<Map<String, dynamic>> _getAnalytics() async {
    try {
      final data = await _storage.read(key: _analyticsKey);
      if (data != null) {
        return Map<String, dynamic>.from(json.decode(data));
      }
    } catch (e) {
      if (kDebugMode) print('❌ Failed to load analytics: $e');
    }
    
    return {
      'interactions': [],
      'categoryStats': {},
      'coachStats': {},
      'qualityStats': {},
      'satisfactionStats': {},
      'createdAt': DateTime.now().toIso8601String(),
    };
  }
  
  /// Save analytics data to storage
  static Future<void> _saveAnalytics(Map<String, dynamic> analytics) async {
    try {
      analytics['lastUpdated'] = DateTime.now().toIso8601String();
      await _storage.write(key: _analyticsKey, value: json.encode(analytics));
    } catch (e) {
      if (kDebugMode) print('❌ Failed to save analytics: $e');
    }
  }
  
  /// Update running average
  static double _updateAverage(double currentAverage, int currentCount, double newValue) {
    if (currentCount == 0) return newValue;
    return ((currentAverage * currentCount) + newValue) / (currentCount + 1);
  }
  
  /// Generate overview statistics
  static Map<String, dynamic> _generateOverview(Map<String, dynamic> analytics) {
    final interactions = analytics['interactions'] as List? ?? [];
    final transcriptUsedCount = interactions.where((i) => i['transcriptContentUsed'] == true).length;
    
    return {
      'totalInteractions': interactions.length,
      'transcriptUsageRate': interactions.isNotEmpty ? transcriptUsedCount / interactions.length : 0.0,
      'averageRelevanceScore': interactions.isNotEmpty 
          ? interactions.fold<double>(0.0, (sum, i) => sum + (i['relevanceScore'] ?? 0.0)) / interactions.length
          : 0.0,
      'uniqueCoaches': (analytics['coachStats'] as Map? ?? {}).length,
      'activeCategories': (analytics['categoryStats'] as Map? ?? {}).length,
    };
  }
  
  /// Generate transcript usage statistics
  static Map<String, dynamic> _generateTranscriptUsageStats(Map<String, dynamic> analytics) {
    final interactions = analytics['interactions'] as List? ?? [];
    final transcriptInteractions = interactions.where((i) => i['transcriptContentUsed'] == true).toList();
    
    return {
      'totalTranscriptUsage': transcriptInteractions.length,
      'averageSourceCount': transcriptInteractions.isNotEmpty
          ? transcriptInteractions.fold<double>(0.0, (sum, i) => sum + (i['sourceCount'] ?? 0.0)) / transcriptInteractions.length
          : 0.0,
      'highQualityInteractions': transcriptInteractions.where((i) => (i['relevanceScore'] ?? 0.0) > 0.7).length,
      'categoryBreakdown': _getCategoryBreakdown(transcriptInteractions),
    };
  }
  
  /// Generate coach effectiveness statistics
  static Map<String, dynamic> _generateCoachEffectivenessStats(Map<String, dynamic> analytics) {
    final coachStats = analytics['coachStats'] as Map<String, dynamic>? ?? {};
    
    final effectiveness = <String, dynamic>{};
    for (final entry in coachStats.entries) {
      final coachName = entry.key;
      final stats = entry.value as Map<String, dynamic>;
      
      effectiveness[coachName] = {
        'totalInteractions': stats['totalInteractions'] ?? 0,
        'transcriptUsageRate': (stats['totalInteractions'] ?? 0) > 0 
            ? (stats['transcriptUsageCount'] ?? 0) / (stats['totalInteractions'] ?? 1)
            : 0.0,
        'averageRelevanceScore': stats['averageRelevanceScore'] ?? 0.0,
        'category': stats['category'] ?? 'unknown',
      };
    }
    
    return effectiveness;
  }
  
  /// Generate category performance statistics
  static Map<String, dynamic> _generateCategoryPerformanceStats(Map<String, dynamic> analytics) {
    return analytics['categoryStats'] as Map<String, dynamic>? ?? {};
  }
  
  /// Generate quality metrics
  static Map<String, dynamic> _generateQualityMetrics(Map<String, dynamic> analytics) {
    return analytics['qualityStats'] as Map<String, dynamic>? ?? {};
  }
  
  /// Generate satisfaction metrics
  static Map<String, dynamic> _generateSatisfactionMetrics(Map<String, dynamic> analytics) {
    return analytics['satisfactionStats'] as Map<String, dynamic>? ?? {};
  }
  
  /// Get category breakdown for transcript usage
  static Map<String, int> _getCategoryBreakdown(List<dynamic> interactions) {
    final breakdown = <String, int>{};
    for (final interaction in interactions) {
      final category = interaction['category'] as String? ?? 'unknown';
      breakdown[category] = (breakdown[category] ?? 0) + 1;
    }
    return breakdown;
  }
  
  /// Clear all analytics data (useful for testing)
  static Future<void> clearAnalytics() async {
    try {
      await _storage.delete(key: _analyticsKey);
      await _storage.delete(key: _sessionKey);
      if (kDebugMode) print('📊 Analytics data cleared');
    } catch (e) {
      if (kDebugMode) print('❌ Failed to clear analytics: $e');
    }
  }
}
