import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:timezone/timezone.dart' as tz;
import '../models/user_model.dart';
import '../models/bounty_model.dart';
import '../services/bounty_notification_service.dart';
import '../utils/debug_logger.dart';

/// Production-ready notification service with comprehensive debugging
/// 
/// Features:
/// - Real iOS/Android notifications
/// - Permission management with detailed logging
/// - Scheduling system with failure detection
/// - Background processing with health checks
/// - Debug dashboard for monitoring
/// - Comprehensive error handling and recovery
class RealNotificationService {
  static final RealNotificationService _instance = RealNotificationService._internal();
  factory RealNotificationService() => _instance;
  RealNotificationService._internal();

  static const String _debugPrefix = 'RealNotificationService';
  static const String _permissionStatusKey = 'notification_permission_status';
  static const String _lastNotificationKey = 'last_notification_sent';
  static const String _notificationCountKey = 'notification_count_today';
  static const String _failureCountKey = 'notification_failure_count';
  static const String _healthCheckKey = 'notification_health_check';

  FlutterLocalNotificationsPlugin? _flutterLocalNotificationsPlugin;
  bool _isInitialized = false;
  Timer? _healthCheckTimer;
  final Map<String, dynamic> _debugMetrics = {};

  /// Initialize the notification service with comprehensive debugging
  Future<bool> initialize() async {
    try {
      DebugLogger.log(_debugPrefix, '🚀 Initializing notification service...');
      _recordDebugMetric('initialization_started', DateTime.now().toIso8601String());

      // Initialize Flutter Local Notifications
      _flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();

      // Android initialization settings
      const AndroidInitializationSettings initializationSettingsAndroid =
          AndroidInitializationSettings('@mipmap/ic_launcher');

      // iOS initialization settings
      const DarwinInitializationSettings initializationSettingsIOS =
          DarwinInitializationSettings(
        requestAlertPermission: true,
        requestBadgePermission: true,
        requestSoundPermission: true,
      );

      const InitializationSettings initializationSettings = InitializationSettings(
        android: initializationSettingsAndroid,
        iOS: initializationSettingsIOS,
      );

      // Initialize with callback for when notification is tapped
      final bool? initialized = await _flutterLocalNotificationsPlugin!.initialize(
        initializationSettings,
        onDidReceiveNotificationResponse: _onDidReceiveNotificationResponse,
      );

      if (initialized == true) {
        _isInitialized = true;
        _recordDebugMetric('initialization_success', true);
        DebugLogger.log(_debugPrefix, '✅ Notification service initialized successfully');

        // Start health check monitoring
        _startHealthCheckMonitoring();

        // Check and log current permission status
        await _checkAndLogPermissionStatus();

        return true;
      } else {
        _recordDebugMetric('initialization_success', false);
        DebugLogger.error(_debugPrefix, 'Failed to initialize notification service', 
          Exception('Initialization returned false'), StackTrace.current);
        return false;
      }

    } catch (e, stackTrace) {
      _recordDebugMetric('initialization_error', e.toString());
      DebugLogger.error(_debugPrefix, 'Error initializing notification service', e, stackTrace);
      return false;
    }
  }

  /// Request notification permissions with detailed logging
  Future<bool> requestPermissions() async {
    try {
      DebugLogger.log(_debugPrefix, '🔐 Requesting notification permissions...');
      _recordDebugMetric('permission_request_started', DateTime.now().toIso8601String());

      if (Platform.isAndroid) {
        return await _requestAndroidPermissions();
      } else if (Platform.isIOS) {
        return await _requestIOSPermissions();
      } else {
        DebugLogger.warn(_debugPrefix, 'Unsupported platform for notifications');
        return false;
      }

    } catch (e, stackTrace) {
      _recordDebugMetric('permission_request_error', e.toString());
      DebugLogger.error(_debugPrefix, 'Error requesting permissions', e, stackTrace);
      return false;
    }
  }

  /// Request Android-specific permissions
  Future<bool> _requestAndroidPermissions() async {
    try {
      DebugLogger.log(_debugPrefix, '📱 Requesting Android notification permissions...');

      // Check if we already have permission
      final PermissionStatus status = await Permission.notification.status;
      DebugLogger.log(_debugPrefix, 'Current Android permission status: $status');

      if (status.isGranted) {
        _recordDebugMetric('android_permission_already_granted', true);
        await _savePermissionStatus('granted');
        return true;
      }

      // Request permission
      final PermissionStatus requestResult = await Permission.notification.request();
      DebugLogger.log(_debugPrefix, 'Android permission request result: $requestResult');

      final bool granted = requestResult.isGranted;
      _recordDebugMetric('android_permission_granted', granted);
      await _savePermissionStatus(granted ? 'granted' : 'denied');

      if (granted) {
        DebugLogger.log(_debugPrefix, '✅ Android notification permissions granted');
      } else {
        DebugLogger.warn(_debugPrefix, '❌ Android notification permissions denied');
      }

      return granted;

    } catch (e, stackTrace) {
      _recordDebugMetric('android_permission_error', e.toString());
      DebugLogger.error(_debugPrefix, 'Error requesting Android permissions', e, stackTrace);
      return false;
    }
  }

  /// Request iOS-specific permissions
  Future<bool> _requestIOSPermissions() async {
    try {
      DebugLogger.log(_debugPrefix, '🍎 Requesting iOS notification permissions...');

      final bool? granted = await _flutterLocalNotificationsPlugin
          ?.resolvePlatformSpecificImplementation<IOSFlutterLocalNotificationsPlugin>()
          ?.requestPermissions(
            alert: true,
            badge: true,
            sound: true,
          );

      final bool permissionGranted = granted ?? false;
      _recordDebugMetric('ios_permission_granted', permissionGranted);
      await _savePermissionStatus(permissionGranted ? 'granted' : 'denied');

      if (permissionGranted) {
        DebugLogger.log(_debugPrefix, '✅ iOS notification permissions granted');
      } else {
        DebugLogger.warn(_debugPrefix, '❌ iOS notification permissions denied');
      }

      return permissionGranted;

    } catch (e, stackTrace) {
      _recordDebugMetric('ios_permission_error', e.toString());
      DebugLogger.error(_debugPrefix, 'Error requesting iOS permissions', e, stackTrace);
      return false;
    }
  }

  /// Check current permission status and log details
  Future<void> _checkAndLogPermissionStatus() async {
    try {
      if (Platform.isAndroid) {
        final status = await Permission.notification.status;
        DebugLogger.log(_debugPrefix, 'Android permission status: $status');
        _recordDebugMetric('current_android_permission', status.toString());
      } else if (Platform.isIOS) {
        // iOS permission checking is more complex, we'll rely on our saved status
        final prefs = await SharedPreferences.getInstance();
        final savedStatus = prefs.getString(_permissionStatusKey) ?? 'unknown';
        DebugLogger.log(_debugPrefix, 'iOS permission status (saved): $savedStatus');
        _recordDebugMetric('current_ios_permission', savedStatus);
      }
    } catch (e, stackTrace) {
      DebugLogger.error(_debugPrefix, 'Error checking permission status', e, stackTrace);
    }
  }

  /// Save permission status to persistent storage
  Future<void> _savePermissionStatus(String status) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_permissionStatusKey, status);
      await prefs.setString('${_permissionStatusKey}_timestamp', DateTime.now().toIso8601String());
      DebugLogger.log(_debugPrefix, 'Saved permission status: $status');
    } catch (e, stackTrace) {
      DebugLogger.error(_debugPrefix, 'Error saving permission status', e, stackTrace);
    }
  }

  /// Record debug metric for monitoring
  void _recordDebugMetric(String key, dynamic value) {
    _debugMetrics[key] = value;
    _debugMetrics['${key}_timestamp'] = DateTime.now().toIso8601String();
    DebugLogger.log(_debugPrefix, 'Debug metric: $key = $value');
  }

  /// Start health check monitoring
  void _startHealthCheckMonitoring() {
    _healthCheckTimer?.cancel();
    _healthCheckTimer = Timer.periodic(Duration(minutes: 30), (timer) {
      _performHealthCheck();
    });
    DebugLogger.log(_debugPrefix, '🏥 Started health check monitoring (every 30 minutes)');
  }

  /// Perform comprehensive health check
  Future<void> _performHealthCheck() async {
    try {
      DebugLogger.log(_debugPrefix, '🏥 Performing notification service health check...');
      
      final healthStatus = <String, dynamic>{
        'timestamp': DateTime.now().toIso8601String(),
        'is_initialized': _isInitialized,
        'plugin_available': _flutterLocalNotificationsPlugin != null,
      };

      // Check permission status
      await _checkAndLogPermissionStatus();

      // Check pending notifications
      final pendingNotifications = await getPendingNotifications();
      healthStatus['pending_notifications_count'] = pendingNotifications.length;

      // Save health check results
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_healthCheckKey, jsonEncode(healthStatus));

      _recordDebugMetric('last_health_check', healthStatus);
      DebugLogger.log(_debugPrefix, '✅ Health check completed: ${healthStatus.length} metrics recorded');

    } catch (e, stackTrace) {
      _recordDebugMetric('health_check_error', e.toString());
      DebugLogger.error(_debugPrefix, 'Error during health check', e, stackTrace);
    }
  }

  /// Get pending notifications for debugging
  Future<List<PendingNotificationRequest>> getPendingNotifications() async {
    try {
      if (!_isInitialized || _flutterLocalNotificationsPlugin == null) {
        return [];
      }

      final pending = await _flutterLocalNotificationsPlugin!.pendingNotificationRequests();
      DebugLogger.log(_debugPrefix, 'Found ${pending.length} pending notifications');
      
      for (final notification in pending) {
        DebugLogger.log(_debugPrefix, 
          'Pending notification: ID=${notification.id}, Title=${notification.title}');
      }

      return pending;
    } catch (e, stackTrace) {
      DebugLogger.error(_debugPrefix, 'Error getting pending notifications', e, stackTrace);
      return [];
    }
  }

  /// Callback for when notification is tapped
  static void _onDidReceiveNotificationResponse(NotificationResponse response) {
    DebugLogger.log(_debugPrefix, 
      'Notification tapped: ID=${response.id}, Payload=${response.payload}');
  }

  /// Get debug metrics for monitoring dashboard
  Map<String, dynamic> getDebugMetrics() {
    return Map.from(_debugMetrics);
  }

  /// Send immediate bounty notification with comprehensive error handling
  Future<bool> sendBountyNotification({
    required User user,
    required BountyModel bounty,
    String? customMessage,
  }) async {
    try {
      if (!_isInitialized) {
        DebugLogger.error(_debugPrefix, 'Cannot send notification: service not initialized',
          Exception('Service not initialized'), StackTrace.current);
        return false;
      }

      DebugLogger.log(_debugPrefix, '📤 Sending bounty notification to ${user.username}...');
      _recordDebugMetric('notification_send_started', DateTime.now().toIso8601String());

      // Check if we should send notification based on business rules
      if (!BountyNotificationService.shouldSendNotification(user)) {
        DebugLogger.log(_debugPrefix, '⏭️ Skipping notification due to business rules');
        _recordDebugMetric('notification_skipped_business_rules', true);
        return false;
      }

      // Generate notification content
      final message = customMessage ??
          BountyNotificationService.generateNotificationMessage(user, bounty);
      final title = bounty.isEpic ? '⚡ EPIC BOUNTY ALERT!' : '🎯 New Bounty Available!';

      // Create notification payload
      final payload = jsonEncode({
        'type': 'bounty',
        'bounty_id': bounty.id,
        'user_id': user.id,
        'timestamp': DateTime.now().toIso8601String(),
        'is_epic': bounty.isEpic,
      });

      // Send notification
      final success = await _sendNotification(
        id: bounty.id.hashCode,
        title: title,
        body: message,
        payload: payload,
      );

      if (success) {
        await _recordNotificationSent(user.id, bounty.id);
        DebugLogger.log(_debugPrefix, '✅ Bounty notification sent successfully');
      }

      return success;

    } catch (e, stackTrace) {
      _recordDebugMetric('notification_send_error', e.toString());
      DebugLogger.error(_debugPrefix, 'Error sending bounty notification', e, stackTrace);
      return false;
    }
  }

  /// Schedule a bounty notification for later delivery
  Future<bool> scheduleBountyNotification({
    required User user,
    required BountyModel bounty,
    required DateTime scheduledTime,
    String? customMessage,
  }) async {
    try {
      if (!_isInitialized) {
        DebugLogger.error(_debugPrefix, 'Cannot schedule notification: service not initialized',
          Exception('Service not initialized'), StackTrace.current);
        return false;
      }

      DebugLogger.log(_debugPrefix,
        '⏰ Scheduling bounty notification for ${user.username} at $scheduledTime');
      _recordDebugMetric('notification_schedule_started', DateTime.now().toIso8601String());

      // Validate scheduled time
      if (scheduledTime.isBefore(DateTime.now())) {
        DebugLogger.warn(_debugPrefix, 'Cannot schedule notification in the past');
        _recordDebugMetric('notification_schedule_past_time', true);
        return false;
      }

      // Generate notification content
      final message = customMessage ??
          BountyNotificationService.generateNotificationMessage(user, bounty);
      final title = bounty.isEpic ? '⚡ EPIC BOUNTY ALERT!' : '🎯 New Bounty Available!';

      // Create notification payload
      final payload = jsonEncode({
        'type': 'bounty_scheduled',
        'bounty_id': bounty.id,
        'user_id': user.id,
        'scheduled_time': scheduledTime.toIso8601String(),
        'is_epic': bounty.isEpic,
      });

      // Schedule notification
      final success = await _scheduleNotification(
        id: bounty.id.hashCode + scheduledTime.millisecondsSinceEpoch,
        title: title,
        body: message,
        scheduledTime: scheduledTime,
        payload: payload,
      );

      if (success) {
        DebugLogger.log(_debugPrefix, '✅ Bounty notification scheduled successfully');
        _recordDebugMetric('notification_scheduled_success', true);
      }

      return success;

    } catch (e, stackTrace) {
      _recordDebugMetric('notification_schedule_error', e.toString());
      DebugLogger.error(_debugPrefix, 'Error scheduling bounty notification', e, stackTrace);
      return false;
    }
  }

  /// Send immediate notification with detailed error handling
  Future<bool> _sendNotification({
    required int id,
    required String title,
    required String body,
    String? payload,
  }) async {
    try {
      DebugLogger.log(_debugPrefix, '📱 Sending notification: ID=$id, Title=$title');

      const AndroidNotificationDetails androidPlatformChannelSpecifics =
          AndroidNotificationDetails(
        'bounty_channel',
        'Bounty Notifications',
        channelDescription: 'Notifications for new bounty challenges',
        importance: Importance.high,
        priority: Priority.high,
        showWhen: true,
        enableVibration: true,
        playSound: true,
      );

      const DarwinNotificationDetails iOSPlatformChannelSpecifics =
          DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      const NotificationDetails platformChannelSpecifics = NotificationDetails(
        android: androidPlatformChannelSpecifics,
        iOS: iOSPlatformChannelSpecifics,
      );

      await _flutterLocalNotificationsPlugin!.show(
        id,
        title,
        body,
        platformChannelSpecifics,
        payload: payload,
      );

      _recordDebugMetric('notification_sent_success', true);
      _recordDebugMetric('last_notification_id', id);
      _recordDebugMetric('last_notification_title', title);

      return true;

    } catch (e, stackTrace) {
      _recordDebugMetric('notification_send_platform_error', e.toString());
      DebugLogger.error(_debugPrefix, 'Platform error sending notification', e, stackTrace);
      await _incrementFailureCount();
      return false;
    }
  }

  /// Schedule notification for future delivery
  Future<bool> _scheduleNotification({
    required int id,
    required String title,
    required String body,
    required DateTime scheduledTime,
    String? payload,
  }) async {
    try {
      DebugLogger.log(_debugPrefix,
        '⏰ Scheduling notification: ID=$id, Time=$scheduledTime');

      const AndroidNotificationDetails androidPlatformChannelSpecifics =
          AndroidNotificationDetails(
        'bounty_scheduled_channel',
        'Scheduled Bounty Notifications',
        channelDescription: 'Scheduled notifications for bounty challenges',
        importance: Importance.high,
        priority: Priority.high,
        showWhen: true,
        enableVibration: true,
        playSound: true,
      );

      const DarwinNotificationDetails iOSPlatformChannelSpecifics =
          DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      const NotificationDetails platformChannelSpecifics = NotificationDetails(
        android: androidPlatformChannelSpecifics,
        iOS: iOSPlatformChannelSpecifics,
      );

      // Convert DateTime to TZDateTime
      final tz.TZDateTime scheduledTZTime = tz.TZDateTime.from(scheduledTime, tz.local);

      await _flutterLocalNotificationsPlugin!.zonedSchedule(
        id,
        title,
        body,
        scheduledTZTime,
        platformChannelSpecifics,
        payload: payload,
        androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
      );

      _recordDebugMetric('notification_scheduled_platform_success', true);
      _recordDebugMetric('last_scheduled_notification_id', id);
      _recordDebugMetric('last_scheduled_time', scheduledTime.toIso8601String());

      return true;

    } catch (e, stackTrace) {
      _recordDebugMetric('notification_schedule_platform_error', e.toString());
      DebugLogger.error(_debugPrefix, 'Platform error scheduling notification', e, stackTrace);
      await _incrementFailureCount();
      return false;
    }
  }

  /// Record that a notification was sent
  Future<void> _recordNotificationSent(String userId, String bountyId) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Update last notification time
      await prefs.setString(_lastNotificationKey, DateTime.now().toIso8601String());

      // Increment daily count
      final today = DateTime.now().toIso8601String().split('T')[0];
      final countKey = '${_notificationCountKey}_$today';
      final currentCount = prefs.getInt(countKey) ?? 0;
      await prefs.setInt(countKey, currentCount + 1);

      // Record notification details
      final notificationRecord = {
        'user_id': userId,
        'bounty_id': bountyId,
        'timestamp': DateTime.now().toIso8601String(),
        'daily_count': currentCount + 1,
      };

      await prefs.setString('last_notification_record', jsonEncode(notificationRecord));

      DebugLogger.log(_debugPrefix,
        'Recorded notification: User=$userId, Bounty=$bountyId, DailyCount=${currentCount + 1}');

    } catch (e, stackTrace) {
      DebugLogger.error(_debugPrefix, 'Error recording notification', e, stackTrace);
    }
  }

  /// Increment failure count for monitoring
  Future<void> _incrementFailureCount() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final currentCount = prefs.getInt(_failureCountKey) ?? 0;
      await prefs.setInt(_failureCountKey, currentCount + 1);
      await prefs.setString('${_failureCountKey}_last', DateTime.now().toIso8601String());

      DebugLogger.warn(_debugPrefix, 'Notification failure count incremented to ${currentCount + 1}');

      // Alert if failure count is high
      if (currentCount + 1 >= 5) {
        DebugLogger.error(_debugPrefix,
          'HIGH FAILURE COUNT: ${currentCount + 1} notification failures detected!',
          Exception('High failure count'), StackTrace.current);
      }

    } catch (e, stackTrace) {
      DebugLogger.error(_debugPrefix, 'Error incrementing failure count', e, stackTrace);
    }
  }

  /// Dispose resources
  void dispose() {
    _healthCheckTimer?.cancel();
    DebugLogger.log(_debugPrefix, '🧹 Notification service disposed');
  }
}
