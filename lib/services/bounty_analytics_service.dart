import 'dart:math';
import '../models/user_model.dart';
import '../widgets/enhanced_cashed_bounties_modal.dart';
import '../utils/debug_logger.dart';

/// Bounty analytics service for tracking progress and providing insights
/// 
/// Features:
/// - Bounty completion statistics
/// - Category performance analysis
/// - Streak tracking for consecutive completions
/// - Personal records and milestones
/// - Predictive analytics for user behavior
class BountyAnalyticsService {
  
  /// Calculate comprehensive bounty statistics for a user
  static BountyStats calculateBountyStats(User user, List<CashedBounty> cashedBounties) {
    DebugLogger.log('BountyAnalyticsService', 
      'Calculating stats for ${cashedBounties.length} completed bounties');
    
    final stats = BountyStats(
      totalBountiesCompleted: cashedBounties.length,
      totalExpFromBounties: _calculateTotalExpFromBounties(cashedBounties),
      categoryBreakdown: _calculateCategoryBreakdown(cashedBounties),
      difficultyBreakdown: _calculateDifficultyBreakdown(cashedBounties),
      currentStreak: _calculateCurrentStreak(cashedBounties),
      longestStreak: _calculateLongestStreak(cashedBounties),
      averageBountiesPerWeek: _calculateAverageBountiesPerWeek(cashedBounties),
      favoriteCategory: _getFavoriteCategory(cashedBounties),
      completionRate: _calculateCompletionRate(user, cashedBounties),
      epicBountiesCompleted: _countEpicBounties(cashedBounties),
      personalRecords: _calculatePersonalRecords(cashedBounties),
      recentTrends: _analyzeRecentTrends(cashedBounties),
    );
    
    DebugLogger.log('BountyAnalyticsService', 
      'Stats calculated: ${stats.totalBountiesCompleted} bounties, ${stats.currentStreak} day streak');
    
    return stats;
  }
  
  /// Calculate total EXP earned from bounties
  static int _calculateTotalExpFromBounties(List<CashedBounty> bounties) {
    return bounties.fold(0, (total, bounty) {
      return total + bounty.bounty.expPerCategory.values.fold(0, (sum, exp) => sum + exp);
    });
  }
  
  /// Calculate category breakdown of completed bounties
  static Map<String, int> _calculateCategoryBreakdown(List<CashedBounty> bounties) {
    final breakdown = <String, int>{};
    
    for (final cashedBounty in bounties) {
      for (final category in cashedBounty.bounty.categories) {
        breakdown[category] = (breakdown[category] ?? 0) + 1;
      }
    }
    
    return breakdown;
  }
  
  /// Calculate difficulty breakdown of completed bounties
  static Map<String, int> _calculateDifficultyBreakdown(List<CashedBounty> bounties) {
    final breakdown = <String, int>{};
    
    for (final cashedBounty in bounties) {
      final difficulty = cashedBounty.bounty.difficulty;
      breakdown[difficulty] = (breakdown[difficulty] ?? 0) + 1;
    }
    
    return breakdown;
  }
  
  /// Calculate current completion streak
  static int _calculateCurrentStreak(List<CashedBounty> bounties) {
    if (bounties.isEmpty) return 0;
    
    // Sort by completion date (most recent first)
    final sortedBounties = List<CashedBounty>.from(bounties)
      ..sort((a, b) => b.completedAt.compareTo(a.completedAt));
    
    int streak = 0;
    DateTime? lastDate;
    
    for (final bounty in sortedBounties) {
      final completionDate = DateTime(
        bounty.completedAt.year,
        bounty.completedAt.month,
        bounty.completedAt.day,
      );
      
      if (lastDate == null) {
        // First bounty
        lastDate = completionDate;
        streak = 1;
      } else {
        final daysDifference = lastDate.difference(completionDate).inDays;
        
        if (daysDifference == 1) {
          // Consecutive day
          streak++;
          lastDate = completionDate;
        } else if (daysDifference == 0) {
          // Same day, continue
          continue;
        } else {
          // Streak broken
          break;
        }
      }
    }
    
    return streak;
  }
  
  /// Calculate longest streak ever achieved
  static int _calculateLongestStreak(List<CashedBounty> bounties) {
    if (bounties.isEmpty) return 0;
    
    // Sort by completion date
    final sortedBounties = List<CashedBounty>.from(bounties)
      ..sort((a, b) => a.completedAt.compareTo(b.completedAt));
    
    int longestStreak = 0;
    int currentStreak = 0;
    DateTime? lastDate;
    
    for (final bounty in sortedBounties) {
      final completionDate = DateTime(
        bounty.completedAt.year,
        bounty.completedAt.month,
        bounty.completedAt.day,
      );
      
      if (lastDate == null) {
        currentStreak = 1;
        lastDate = completionDate;
      } else {
        final daysDifference = completionDate.difference(lastDate).inDays;
        
        if (daysDifference == 1) {
          currentStreak++;
        } else if (daysDifference == 0) {
          // Same day, continue
          continue;
        } else {
          // Streak broken, check if it was the longest
          longestStreak = max(longestStreak, currentStreak);
          currentStreak = 1;
        }
        
        lastDate = completionDate;
      }
    }
    
    return max(longestStreak, currentStreak);
  }
  
  /// Calculate average bounties completed per week
  static double _calculateAverageBountiesPerWeek(List<CashedBounty> bounties) {
    if (bounties.isEmpty) return 0.0;
    
    final sortedBounties = List<CashedBounty>.from(bounties)
      ..sort((a, b) => a.completedAt.compareTo(b.completedAt));
    
    final firstBounty = sortedBounties.first.completedAt;
    final lastBounty = sortedBounties.last.completedAt;
    final totalDays = lastBounty.difference(firstBounty).inDays + 1;
    final totalWeeks = totalDays / 7.0;
    
    return totalWeeks > 0 ? bounties.length / totalWeeks : 0.0;
  }
  
  /// Get user's favorite category based on completion frequency
  static String _getFavoriteCategory(List<CashedBounty> bounties) {
    final categoryBreakdown = _calculateCategoryBreakdown(bounties);
    
    if (categoryBreakdown.isEmpty) return 'None';
    
    final sortedCategories = categoryBreakdown.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    return sortedCategories.first.key;
  }
  
  /// Calculate completion rate (placeholder - would need total offered bounties)
  static double _calculateCompletionRate(User user, List<CashedBounty> bounties) {
    // For now, estimate based on user activity level
    final daysSinceJoined = DateTime.now().difference(user.createdAt).inDays;
    final expectedBounties = daysSinceJoined * 0.3; // Expect 30% daily completion
    
    return expectedBounties > 0 ? (bounties.length / expectedBounties).clamp(0.0, 1.0) : 0.0;
  }
  
  /// Count epic bounties completed
  static int _countEpicBounties(List<CashedBounty> bounties) {
    return bounties.where((bounty) => bounty.bounty.isEpic).length;
  }
  
  /// Calculate personal records and achievements
  static Map<String, dynamic> _calculatePersonalRecords(List<CashedBounty> bounties) {
    final records = <String, dynamic>{};
    
    if (bounties.isEmpty) return records;
    
    // Most EXP in a single bounty
    int maxExpSingle = 0;
    for (final bounty in bounties) {
      final totalExp = bounty.bounty.expPerCategory.values.fold(0, (sum, exp) => sum + exp);
      maxExpSingle = max(maxExpSingle, totalExp);
    }
    records['maxExpSingleBounty'] = maxExpSingle;
    
    // Most bounties in a single day
    final dailyCounts = <String, int>{};
    for (final bounty in bounties) {
      final dateKey = '${bounty.completedAt.year}-${bounty.completedAt.month}-${bounty.completedAt.day}';
      dailyCounts[dateKey] = (dailyCounts[dateKey] ?? 0) + 1;
    }
    records['maxBountiesInDay'] = dailyCounts.values.fold(0, max);
    
    // First bounty completion date
    final sortedBounties = List<CashedBounty>.from(bounties)
      ..sort((a, b) => a.completedAt.compareTo(b.completedAt));
    records['firstBountyDate'] = sortedBounties.first.completedAt;
    
    return records;
  }
  
  /// Analyze recent trends in bounty completion
  static Map<String, dynamic> _analyzeRecentTrends(List<CashedBounty> bounties) {
    final trends = <String, dynamic>{};
    
    final now = DateTime.now();
    final lastWeek = now.subtract(Duration(days: 7));
    final lastMonth = now.subtract(Duration(days: 30));
    
    // Recent bounties (last 7 days)
    final recentBounties = bounties.where((b) => b.completedAt.isAfter(lastWeek)).toList();
    trends['bountiesLastWeek'] = recentBounties.length;
    
    // Monthly bounties
    final monthlyBounties = bounties.where((b) => b.completedAt.isAfter(lastMonth)).toList();
    trends['bountiesLastMonth'] = monthlyBounties.length;
    
    // Trending categories (most completed in last week)
    final recentCategories = _calculateCategoryBreakdown(recentBounties);
    final trendingCategory = recentCategories.entries.isNotEmpty
        ? recentCategories.entries.reduce((a, b) => a.value > b.value ? a : b).key
        : 'None';
    trends['trendingCategory'] = trendingCategory;
    
    // Activity trend (increasing/decreasing)
    final firstHalfMonth = monthlyBounties.where((b) => 
      b.completedAt.isBefore(now.subtract(Duration(days: 15)))).length;
    final secondHalfMonth = monthlyBounties.length - firstHalfMonth;
    
    if (secondHalfMonth > firstHalfMonth) {
      trends['activityTrend'] = 'increasing';
    } else if (secondHalfMonth < firstHalfMonth) {
      trends['activityTrend'] = 'decreasing';
    } else {
      trends['activityTrend'] = 'stable';
    }
    
    return trends;
  }
  
  /// Generate insights and recommendations based on analytics
  static List<String> generateInsights(BountyStats stats) {
    final insights = <String>[];
    
    // Streak insights
    if (stats.currentStreak >= 7) {
      insights.add('🔥 Amazing! You\'re on a ${stats.currentStreak}-day streak!');
    } else if (stats.currentStreak == 0) {
      insights.add('💪 Ready to start a new streak? Complete a bounty today!');
    }
    
    // Category balance insights
    final categoryEntries = stats.categoryBreakdown.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    if (categoryEntries.length >= 2) {
      final topCategory = categoryEntries.first;
      final bottomCategory = categoryEntries.last;
      
      if (topCategory.value > bottomCategory.value * 3) {
        insights.add('🎯 Consider focusing more on ${bottomCategory.key} to balance your growth!');
      }
    }
    
    // Epic bounty insights
    if (stats.epicBountiesCompleted == 0 && stats.totalBountiesCompleted >= 10) {
      insights.add('⚡ You\'re ready for EPIC bounties! Try one for massive EXP!');
    } else if (stats.epicBountiesCompleted > 0) {
      insights.add('🌟 Epic bounty master! You\'ve completed ${stats.epicBountiesCompleted} epic challenges!');
    }
    
    // Activity insights
    if (stats.averageBountiesPerWeek < 1) {
      insights.add('📈 Try to complete at least 1 bounty per week for steady progress!');
    } else if (stats.averageBountiesPerWeek >= 3) {
      insights.add('🚀 You\'re a bounty hunting machine! ${stats.averageBountiesPerWeek.toStringAsFixed(1)} bounties per week!');
    }
    
    return insights;
  }
}

/// Data class for bounty statistics
class BountyStats {
  final int totalBountiesCompleted;
  final int totalExpFromBounties;
  final Map<String, int> categoryBreakdown;
  final Map<String, int> difficultyBreakdown;
  final int currentStreak;
  final int longestStreak;
  final double averageBountiesPerWeek;
  final String favoriteCategory;
  final double completionRate;
  final int epicBountiesCompleted;
  final Map<String, dynamic> personalRecords;
  final Map<String, dynamic> recentTrends;
  
  const BountyStats({
    required this.totalBountiesCompleted,
    required this.totalExpFromBounties,
    required this.categoryBreakdown,
    required this.difficultyBreakdown,
    required this.currentStreak,
    required this.longestStreak,
    required this.averageBountiesPerWeek,
    required this.favoriteCategory,
    required this.completionRate,
    required this.epicBountiesCompleted,
    required this.personalRecords,
    required this.recentTrends,
  });
}
