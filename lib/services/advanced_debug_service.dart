import 'dart:io';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import '../services/comprehensive_logging_service.dart';
import '../services/bulletproof_storage_service.dart';
import '../services/bulletproof_notification_service.dart';

/// 🔍 Advanced Debug Service
/// 
/// Provides comprehensive debugging, monitoring, and diagnostic capabilities
/// for the MXD app. Includes real-time system health monitoring, error tracking,
/// performance analysis, and intelligent failure detection.
/// 
/// Features:
/// - Real-time system health monitoring
/// - Comprehensive error tracking and analysis
/// - Performance metrics collection
/// - Intelligent failure prediction
/// - Automated diagnostic reports
/// - Debug dashboard integration
class AdvancedDebugService {
  static final AdvancedDebugService _instance = AdvancedDebugService._internal();
  factory AdvancedDebugService() => _instance;
  AdvancedDebugService._internal();

  // Core services
  final BulletproofStorageService _storage = BulletproofStorageService();
  final BulletproofNotificationService _notifications = BulletproofNotificationService();
  
  // Debug state
  bool _isInitialized = false;
  bool _isMonitoring = false;
  DateTime? _lastHealthCheck;
  
  // System metrics
  final Map<String, dynamic> _systemMetrics = {};
  final Map<String, List<dynamic>> _errorHistory = {};
  final Map<String, int> _serviceHealthScores = {};
  
  // Performance tracking
  final List<Map<String, dynamic>> _performanceSnapshots = [];
  final Map<String, DateTime> _lastServiceCheck = {};
  
  /// Initialize the advanced debug service
  Future<bool> initialize() async {
    try {
      await ComprehensiveLoggingService.logInfo('🔍 Initializing advanced debug service...');
      
      // Initialize core dependencies
      await _initializeDependencies();
      
      // Set up monitoring systems
      await _setupMonitoringSystems();
      
      // Create debug storage structure
      await _createDebugStorage();
      
      // Start continuous monitoring
      await _startContinuousMonitoring();
      
      _isInitialized = true;
      await ComprehensiveLoggingService.logInfo('✅ Advanced debug service initialized successfully');
      
      return true;
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Failed to initialize advanced debug service: $e');
      return false;
    }
  }
  
  /// Initialize core dependencies
  Future<void> _initializeDependencies() async {
    // Ensure storage is ready
    if (!_storage.isReady) {
      await _storage.initialize();
    }
    
    // Ensure notifications are ready
    if (!_notifications.isReady) {
      await _notifications.initialize();
    }
    
    await ComprehensiveLoggingService.logInfo('📦 Debug service dependencies initialized');
  }
  
  /// Set up monitoring systems
  Future<void> _setupMonitoringSystems() async {
    // Initialize system metrics
    _systemMetrics.clear();
    _systemMetrics['platform'] = Platform.operatingSystem;
    _systemMetrics['version'] = Platform.operatingSystemVersion;
    _systemMetrics['dart_version'] = Platform.version;
    _systemMetrics['debug_mode'] = kDebugMode;
    _systemMetrics['startup_time'] = DateTime.now().toIso8601String();
    
    // Initialize service health scores
    _serviceHealthScores.clear();
    _serviceHealthScores['storage'] = 0;
    _serviceHealthScores['notifications'] = 0;
    _serviceHealthScores['logging'] = 100; // Assume logging is working if we got here
    _serviceHealthScores['performance'] = 0;
    _serviceHealthScores['ai_services'] = 0;

    // Initialize service check timestamps
    _lastServiceCheck['logging'] = DateTime.now();
    _lastServiceCheck['performance'] = DateTime.now();
    _lastServiceCheck['ai_services'] = DateTime.now();

    await ComprehensiveLoggingService.logInfo('📊 Monitoring systems configured');
  }
  
  /// Create debug storage structure
  Future<void> _createDebugStorage() async {
    try {
      // Create debug metadata
      final debugMetadata = {
        'created': DateTime.now().toIso8601String(),
        'version': '1.0.0',
        'platform': Platform.operatingSystem,
        'debug_mode': kDebugMode,
      };
      
      await _storage.write('debug_metadata', jsonEncode(debugMetadata));
      
      // Initialize error tracking
      await _storage.write('error_history', jsonEncode({}));
      
      // Initialize performance tracking
      await _storage.write('performance_history', jsonEncode([]));
      
      await ComprehensiveLoggingService.logInfo('📁 Debug storage structure created');
    } catch (e) {
      await ComprehensiveLoggingService.logWarning('⚠️ Failed to create debug storage: $e');
    }
  }
  
  /// Start continuous monitoring
  Future<void> _startContinuousMonitoring() async {
    if (_isMonitoring) return;
    
    _isMonitoring = true;
    
    // Start periodic health checks
    _schedulePeriodicHealthChecks();
    
    // Start performance monitoring
    _schedulePerformanceMonitoring();
    
    await ComprehensiveLoggingService.logInfo('🔄 Continuous monitoring started');
  }
  
  /// Schedule periodic health checks (optimized frequency)
  void _schedulePeriodicHealthChecks() {
    Future.delayed(const Duration(minutes: 5), () async { // Changed from 30 seconds to 5 minutes
      if (_isMonitoring) {
        await _performSystemHealthCheck();
        _schedulePeriodicHealthChecks(); // Reschedule
      }
    });
  }
  
  /// Schedule performance monitoring (optimized frequency)
  void _schedulePerformanceMonitoring() {
    Future.delayed(const Duration(minutes: 10), () async { // Changed from 1 minute to 10 minutes
      if (_isMonitoring) {
        await _capturePerformanceSnapshot();
        _schedulePerformanceMonitoring(); // Reschedule
      }
    });
  }
  
  /// Perform comprehensive system health check
  Future<Map<String, dynamic>> _performSystemHealthCheck() async {
    try {
      _lastHealthCheck = DateTime.now();
      final healthReport = <String, dynamic>{};
      
      // Check storage health
      final storageHealth = _storage.getHealthStatus();
      _serviceHealthScores['storage'] = storageHealth['isHealthy'] == true ? 100 : 0;
      healthReport['storage'] = storageHealth;
      
      // Check notification health
      final notificationHealth = _notifications.getStatus();
      _serviceHealthScores['notifications'] = notificationHealth['isHealthy'] == true ? 100 : 0;
      healthReport['notifications'] = notificationHealth;

      // Check performance health (assume healthy if monitoring is active)
      _serviceHealthScores['performance'] = _isMonitoring ? 100 : 0;

      // Check AI services health (assume healthy if no critical errors in last 5 minutes)
      final now = DateTime.now();
      var hasRecentAIErrors = false;

      // Check all error categories for AI-related errors
      for (final category in _errorHistory.keys) {
        final errors = _errorHistory[category] ?? [];
        for (final error in errors) {
          final errorTime = DateTime.tryParse(error['timestamp'] ?? '');
          if (errorTime != null && now.difference(errorTime).inMinutes < 5) {
            final message = error['message']?.toString().toLowerCase() ?? '';
            if (message.contains('api') || message.contains('ai') || message.contains('openai')) {
              hasRecentAIErrors = true;
              break;
            }
          }
        }
        if (hasRecentAIErrors) break;
      }

      _serviceHealthScores['ai_services'] = hasRecentAIErrors ? 50 : 100;

      // Calculate overall health score
      final totalScore = _serviceHealthScores.values.reduce((a, b) => a + b);
      final averageScore = totalScore / _serviceHealthScores.length;
      healthReport['overall_health_score'] = averageScore.round();
      healthReport['timestamp'] = _lastHealthCheck!.toIso8601String();
      
      // Store health report
      await _storage.write('last_health_check', jsonEncode(healthReport));
      
      // Log critical issues - but be more lenient during startup
      if (averageScore < 40) {
        await ComprehensiveLoggingService.logWarning('🚨 System health critical: ${averageScore.round()}%');
      } else if (averageScore < 70) {
        // Only log degraded health if it's been more than 30 seconds since startup
        final timeSinceStartup = DateTime.now().difference(_lastHealthCheck!).inSeconds;
        if (timeSinceStartup > 30) {
          await ComprehensiveLoggingService.logWarning('⚠️ System health degraded: ${averageScore.round()}%');
        } else {
          // During startup, this is normal
          if (kDebugMode) print('ℹ️ System health stabilizing: ${averageScore.round()}% (startup phase)');
        }
      }

      // Add debug information for troubleshooting
      if (kDebugMode && averageScore < 80) {
        print('🔍 Debug: Health score breakdown:');
        _serviceHealthScores.forEach((service, score) {
          print('   $service: $score%');
        });
      }
      
      return healthReport;
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Health check failed: $e');
      return {'error': e.toString()};
    }
  }
  
  /// Capture performance snapshot
  Future<void> _capturePerformanceSnapshot() async {
    try {
      final snapshot = {
        'timestamp': DateTime.now().toIso8601String(),
        'memory_usage': _getMemoryUsage(),
        'storage_stats': await _storage.getStatistics(),
        'service_health_scores': Map.from(_serviceHealthScores),
      };
      
      _performanceSnapshots.add(snapshot);
      
      // Keep only last 100 snapshots
      if (_performanceSnapshots.length > 100) {
        _performanceSnapshots.removeAt(0);
      }
      
      // Store to persistent storage
      await _storage.write('performance_snapshots', jsonEncode(_performanceSnapshots));
      
    } catch (e) {
      await ComprehensiveLoggingService.logWarning('⚠️ Failed to capture performance snapshot: $e');
    }
  }
  
  /// Get memory usage information
  Map<String, dynamic> _getMemoryUsage() {
    try {
      // This is a simplified memory tracking
      // In a real implementation, you might use more sophisticated memory profiling
      return {
        'timestamp': DateTime.now().toIso8601String(),
        'platform': Platform.operatingSystem,
        'available': 'unknown', // Would need platform-specific implementation
      };
    } catch (e) {
      return {'error': e.toString()};
    }
  }
  
  /// Record an error for tracking
  Future<void> recordError(String category, String error, {Map<String, dynamic>? context}) async {
    try {
      final errorRecord = {
        'timestamp': DateTime.now().toIso8601String(),
        'category': category,
        'error': error,
        'context': context ?? {},
      };
      
      // Add to in-memory history
      if (!_errorHistory.containsKey(category)) {
        _errorHistory[category] = [];
      }
      _errorHistory[category]!.add(errorRecord);
      
      // Keep only last 50 errors per category
      if (_errorHistory[category]!.length > 50) {
        _errorHistory[category]!.removeAt(0);
      }
      
      // Store to persistent storage
      await _storage.write('error_history', jsonEncode(_errorHistory));
      
      await ComprehensiveLoggingService.logError('🐛 Error recorded: [$category] $error');
      
    } catch (e) {
      await ComprehensiveLoggingService.logWarning('⚠️ Failed to record error: $e');
    }
  }
  
  /// Generate comprehensive diagnostic report
  Future<Map<String, dynamic>> generateDiagnosticReport() async {
    try {
      final report = <String, dynamic>{};
      
      // System information
      report['system'] = _systemMetrics;
      
      // Health status
      report['health'] = await _performSystemHealthCheck();
      
      // Error summary
      report['errors'] = _generateErrorSummary();
      
      // Performance summary
      report['performance'] = _generatePerformanceSummary();
      
      // Service status
      report['services'] = await _generateServiceStatus();
      
      // Recommendations
      report['recommendations'] = _generateRecommendations();
      
      report['generated_at'] = DateTime.now().toIso8601String();
      
      // Store report
      await _storage.write('last_diagnostic_report', jsonEncode(report));
      
      await ComprehensiveLoggingService.logInfo('📋 Diagnostic report generated');
      
      return report;
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Failed to generate diagnostic report: $e');
      return {'error': e.toString()};
    }
  }
  
  /// Generate error summary
  Map<String, dynamic> _generateErrorSummary() {
    final summary = <String, dynamic>{};
    
    for (final category in _errorHistory.keys) {
      final errors = _errorHistory[category]!;
      summary[category] = {
        'count': errors.length,
        'latest': errors.isNotEmpty ? errors.last : null,
      };
    }
    
    return summary;
  }
  
  /// Generate performance summary
  Map<String, dynamic> _generatePerformanceSummary() {
    if (_performanceSnapshots.isEmpty) {
      return {'status': 'no_data'};
    }
    
    final latest = _performanceSnapshots.last;
    return {
      'latest_snapshot': latest,
      'snapshot_count': _performanceSnapshots.length,
      'monitoring_duration': _performanceSnapshots.length > 1 
          ? 'Started ${_performanceSnapshots.first['timestamp']}'
          : 'Just started',
    };
  }
  
  /// Generate service status
  Future<Map<String, dynamic>> _generateServiceStatus() async {
    return {
      'storage': _storage.getHealthStatus(),
      'notifications': _notifications.getStatus(),
      'health_scores': Map.from(_serviceHealthScores),
      'last_check': _lastHealthCheck?.toIso8601String(),
    };
  }
  
  /// Generate recommendations
  List<String> _generateRecommendations() {
    final recommendations = <String>[];
    
    // Check service health scores
    for (final entry in _serviceHealthScores.entries) {
      if (entry.value < 50) {
        recommendations.add('Critical: ${entry.key} service needs immediate attention');
      } else if (entry.value < 80) {
        recommendations.add('Warning: ${entry.key} service performance is degraded');
      }
    }
    
    // Check error patterns
    for (final category in _errorHistory.keys) {
      final errors = _errorHistory[category]!;
      if (errors.length > 10) {
        recommendations.add('High error rate in $category - investigate root cause');
      }
    }
    
    if (recommendations.isEmpty) {
      recommendations.add('All systems operating normally');
    }
    
    return recommendations;
  }
  
  /// Get current debug status
  Map<String, dynamic> getDebugStatus() {
    return {
      'isInitialized': _isInitialized,
      'isMonitoring': _isMonitoring,
      'lastHealthCheck': _lastHealthCheck?.toIso8601String(),
      'serviceHealthScores': Map.from(_serviceHealthScores),
      'errorCategories': _errorHistory.keys.toList(),
      'performanceSnapshotCount': _performanceSnapshots.length,
    };
  }
  
  /// Stop monitoring
  void stopMonitoring() {
    _isMonitoring = false;
    ComprehensiveLoggingService.logInfo('🛑 Debug monitoring stopped');
  }
  
  /// Check if debug service is ready
  bool get isReady => _isInitialized;
}
