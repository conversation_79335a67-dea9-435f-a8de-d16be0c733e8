// 📁 lib/services/feature_gate_service.dart

import 'package:flutter/material.dart';
import '../models/user_model.dart';
import '../models/guest_user_model.dart';
import '../services/guest_session_service.dart';
import '../widgets/guest_signup_prompt_modal.dart';

/// Service for controlling access to features based on user authentication status
/// 
/// Implements feature gating to ensure guest users can only access appropriate
/// features while providing clear upgrade paths to full accounts.
class FeatureGateService {
  static final FeatureGateService _instance = FeatureGateService._internal();
  factory FeatureGateService() => _instance;
  FeatureGateService._internal();

  final GuestSessionService _guestService = GuestSessionService();

  /// Check if a feature requires an account
  bool requiresAccount(String feature) {
    const accountRequiredFeatures = {
      // AI & Personalization
      'ai_coach_full',
      'ai_coach_memory',
      'personalized_responses',
      
      // Progress & Data
      'exp_tracking',
      'habit_management',
      'diary_entries',
      'north_star_quest',
      'streak_tracking',
      'progress_analytics',
      
      // Gamification
      'bounty_hunter',
      'training_tracker',
      'level_system',
      'achievements',
      
      // Data & Sync
      'cloud_sync',
      'data_backup',
      'profile_management',
      'email_verification',
      
      // Account Features
      'user_settings',
      'notification_preferences',
      'custom_categories',
    };
    
    return accountRequiredFeatures.contains(feature);
  }

  /// Check if a feature is accessible to guest users
  bool isGuestAccessible(String feature) {
    const guestAccessibleFeatures = {
      // App Exploration
      'app_browsing',
      'feature_overview',
      'tutorial',
      'music_player',
      'static_content',
      
      // Limited Demo
      'demo_ai_coach',
      'demo_progress',
      'feature_previews',
      'theme_toggle',
    };
    
    return guestAccessibleFeatures.contains(feature);
  }

  /// Gate a feature based on user authentication status
  /// Returns true if access is allowed, false if blocked
  bool gateFeature({
    required BuildContext context,
    required String feature,
    User? user,
    String? customMessage,
  }) {
    // If user is authenticated, allow all features
    if (user != null && !_guestService.isGuestSession) {
      return true;
    }
    
    // If feature doesn't require account, allow access
    if (!requiresAccount(feature)) {
      _guestService.markFeatureViewed(feature);
      return true;
    }
    
    // Feature requires account but user is guest - show prompt
    _showAccountRequiredPrompt(
      context: context,
      feature: feature,
      customMessage: customMessage,
    );
    
    return false;
  }

  /// Show account required prompt for guests
  void _showAccountRequiredPrompt({
    required BuildContext context,
    required String feature,
    String? customMessage,
  }) {
    _guestService.markFeatureViewed('account_required_$feature');
    
    final featureName = _getFeatureDisplayName(feature);
    final message = customMessage ?? 
        'To access $featureName, you need to create an account. This allows us to save your progress and provide personalized experiences.';
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.grey[900],
        title: const Text(
          'Account Required',
          style: TextStyle(color: Colors.white, fontFamily: 'Pirulen'),
        ),
        content: Text(
          message,
          style: const TextStyle(color: Colors.white70, fontFamily: 'Bitsumishi'),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text(
              'Continue Browsing',
              style: TextStyle(color: Colors.grey),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _navigateToSignup(context);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.cyan,
              foregroundColor: Colors.black,
            ),
            child: const Text('Create Account'),
          ),
        ],
      ),
    );
  }

  /// Get user-friendly display name for features
  String _getFeatureDisplayName(String feature) {
    const featureNames = {
      'ai_coach_full': 'AI Coaches',
      'ai_coach_memory': 'AI Coach Memory',
      'exp_tracking': 'Experience Tracking',
      'habit_management': 'Habit Tracking',
      'diary_entries': 'Diary Entries',
      'north_star_quest': 'North Star Quest',
      'bounty_hunter': 'Bounty Hunter',
      'training_tracker': 'Training Tracker',
      'cloud_sync': 'Cloud Sync',
      'profile_management': 'Profile Settings',
    };
    
    return featureNames[feature] ?? feature.replaceAll('_', ' ').toUpperCase();
  }

  /// Navigate to signup screen
  void _navigateToSignup(BuildContext context) {
    // Import AuthScreen here to avoid circular imports
    Navigator.of(context).pushNamedAndRemoveUntil('/auth', (route) => false);
  }

  /// Check if guest should be prompted for signup based on engagement
  bool shouldPromptForSignup() {
    return _guestService.shouldPromptForSignup();
  }

  /// Show engagement-based signup prompt
  void showEngagementPrompt(BuildContext context) {
    if (!_guestService.isGuestSession) return;
    
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) => GuestSignupPromptModal(
        message: _guestService.getSignupPromptMessage(),
        onSignUp: () {
          Navigator.of(context).pop();
          _navigateToSignup(context);
        },
        onContinueAsGuest: () {
          Navigator.of(context).pop();
        },
      ),
    );
  }

  /// Get demo data for guest users
  Map<String, dynamic> getDemoData(String dataType) {
    switch (dataType) {
      case 'exp_data':
        return {
          'totalExp': 150,
          'categories': {
            'Health': 45,
            'Wealth': 30,
            'Purpose': 40,
            'Connection': 35,
          },
          'level': 3,
          'rank': 'Explorer',
        };
        
      case 'habits_data':
        return {
          'totalHabits': 4,
          'completedToday': 2,
          'currentStreak': 3,
          'longestStreak': 7,
        };
        
      case 'coach_demo':
        return {
          'availableCoaches': ['Health', 'Wealth', 'Purpose', 'Connection'],
          'demoMessage': 'This is a sample AI coach response. Create an account to have full conversations with your personalized coaches!',
        };
        
      default:
        return {};
    }
  }

  /// Check if user is in guest mode
  bool get isGuestMode => _guestService.isGuestSession;

  /// Get current guest user
  GuestUser? get currentGuest => _guestService.currentGuestUser;
}
