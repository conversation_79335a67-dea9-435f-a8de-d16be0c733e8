// 📁 lib/services/guest_session_service.dart

import 'package:flutter/foundation.dart';
import '../models/guest_user_model.dart';
import '../models/user_model.dart';

/// Service for managing guest user sessions
/// 
/// Handles guest user creation, session management, and conversion to full users.
/// Guest sessions are temporary and don't persist between app launches.
class GuestSessionService {
  static final GuestSessionService _instance = GuestSessionService._internal();
  factory GuestSessionService() => _instance;
  GuestSessionService._internal();

  /// Current guest user session (if any)
  GuestUser? _currentGuestUser;
  
  /// Whether the current session is a guest session
  bool get isGuestSession => _currentGuestUser != null;
  
  /// Get the current guest user
  GuestUser? get currentGuestUser => _currentGuestUser;
  
  /// Get the current guest user as a User model for UI compatibility
  User? get currentGuestAsUser => _currentGuestUser?.toUserModel();
  
  /// Create a new guest session
  GuestUser createGuestSession() {
    _currentGuestUser = GuestUser.createSession();
    
    if (kDebugMode) {
      print('🎭 Created guest session: ${_currentGuestUser!.sessionId}');
    }
    
    return _currentGuestUser!;
  }
  
  /// Update the current guest user
  void updateGuestUser(GuestUser updatedGuest) {
    _currentGuestUser = updatedGuest;
    
    if (kDebugMode) {
      print('🎭 Updated guest session: ${updatedGuest.sessionId}');
    }
  }
  
  /// Mark a feature as viewed by the guest user
  void markFeatureViewed(String feature) {
    if (_currentGuestUser != null) {
      _currentGuestUser = _currentGuestUser!.markFeatureViewed(feature);
      
      if (kDebugMode) {
        print('🎭 Guest viewed feature: $feature');
      }
    }
  }
  
  /// Mark tutorial as seen
  void markTutorialSeen() {
    if (_currentGuestUser != null) {
      _currentGuestUser = _currentGuestUser!.markTutorialSeen();
      
      if (kDebugMode) {
        print('🎭 Guest completed tutorial');
      }
    }
  }
  
  /// Mark coach demo as seen
  void markCoachDemoSeen() {
    if (_currentGuestUser != null) {
      _currentGuestUser = _currentGuestUser!.markCoachDemoSeen();
      
      if (kDebugMode) {
        print('🎭 Guest viewed coach demo');
      }
    }
  }
  
  /// Check if a feature has been viewed
  bool hasViewedFeature(String feature) {
    return _currentGuestUser?.viewedFeatures.contains(feature) ?? false;
  }
  
  /// Get session duration
  Duration getSessionDuration() {
    if (_currentGuestUser == null) return Duration.zero;
    return DateTime.now().difference(_currentGuestUser!.sessionStarted);
  }
  
  /// Get session analytics for conversion tracking
  Map<String, dynamic> getSessionAnalytics() {
    if (_currentGuestUser == null) {
      return {'isGuest': false};
    }
    
    return {
      'isGuest': true,
      'sessionId': _currentGuestUser!.sessionId,
      'sessionDuration': getSessionDuration().inMinutes,
      'featuresViewed': _currentGuestUser!.viewedFeatures.toList(),
      'hasSeenTutorial': _currentGuestUser!.hasSeenTutorial,
      'hasSeenCoachDemo': _currentGuestUser!.hasSeenCoachDemo,
      'sessionStarted': _currentGuestUser!.sessionStarted.toIso8601String(),
    };
  }
  
  /// Convert guest session data for account creation
  /// Returns data that can be used to pre-populate a new user account
  Map<String, dynamic> getConversionData() {
    if (_currentGuestUser == null) return {};
    
    return {
      'hasSeenTutorial': _currentGuestUser!.hasSeenTutorial,
      'hasSeenCoachDemo': _currentGuestUser!.hasSeenCoachDemo,
      'viewedFeatures': _currentGuestUser!.viewedFeatures.toList(),
      'sessionDuration': getSessionDuration().inMinutes,
      'demoCategories': _currentGuestUser!.demoCategories,
    };
  }
  
  /// End the guest session (called when user signs up or app closes)
  void endGuestSession() {
    if (_currentGuestUser != null) {
      final sessionDuration = getSessionDuration();
      
      if (kDebugMode) {
        print('🎭 Ending guest session: ${_currentGuestUser!.sessionId}');
        print('   Duration: ${sessionDuration.inMinutes} minutes');
        print('   Features viewed: ${_currentGuestUser!.viewedFeatures.length}');
      }
      
      _currentGuestUser = null;
    }
  }
  
  /// Check if user should be prompted to create an account
  /// Based on engagement level and features accessed
  bool shouldPromptForSignup() {
    if (_currentGuestUser == null) return false;
    
    final sessionDuration = getSessionDuration();
    final featuresViewed = _currentGuestUser!.viewedFeatures.length;
    
    // Prompt if user has been active for 3+ minutes and viewed 2+ features
    return sessionDuration.inMinutes >= 3 && featuresViewed >= 2;
  }
  
  /// Get appropriate signup prompt message based on user engagement
  String getSignupPromptMessage() {
    if (_currentGuestUser == null) return 'Create an account to get started!';
    
    final featuresViewed = _currentGuestUser!.viewedFeatures.length;
    
    if (featuresViewed >= 3) {
      return 'You\'re exploring a lot! Create an account to save your progress and unlock full features.';
    } else if (_currentGuestUser!.hasSeenCoachDemo) {
      return 'Ready to start your journey? Create an account to chat with your AI coaches!';
    } else if (_currentGuestUser!.hasSeenTutorial) {
      return 'Now that you know how it works, create an account to begin tracking your progress!';
    } else {
      return 'Create an account to unlock the full Maxed Out Life experience!';
    }
  }
  
  /// Reset guest session (for testing or cleanup)
  void resetGuestSession() {
    if (kDebugMode) {
      print('🎭 Resetting guest session');
    }
    _currentGuestUser = null;
  }
  
  /// Check if guest has accessed account-required features
  /// Used to determine when to show signup prompts
  bool hasAccessedAccountFeatures() {
    if (_currentGuestUser == null) return false;
    
    final accountFeatures = {
      'ai_coach_full',
      'habit_tracking',
      'progress_save',
      'diary_entry',
      'bounty_hunter',
    };
    
    return _currentGuestUser!.viewedFeatures
        .any((feature) => accountFeatures.contains(feature));
  }
}
