// lib/services/dynamic_personality_service.dart

import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

import 'coach_memory_service.dart';
import 'personalization_engine.dart';

/// Dynamic personality evolution system for coaches
class DynamicPersonalityService {
  static const FlutterSecureStorage _storage = FlutterSecureStorage();
  static const String _personalityDataKey = 'dynamic_personality';
  
  // Cache for performance
  static final Map<String, Map<String, CoachPersonality>> _personalityCache = {};

  /// Initialize dynamic personalities for a user
  static Future<void> initializeDynamicPersonalities(String userId) async {
    try {
      await _loadUserPersonalities(userId);
      if (kDebugMode) print('🎭 Dynamic personalities initialized for user: $userId');
    } catch (e) {
      if (kDebugMode) print('❌ Failed to initialize dynamic personalities: $e');
    }
  }

  /// Get evolved personality prompt for a specific coach and user
  static Future<String> getEvolvedPersonalityPrompt({
    required String userId,
    required String coachCategory,
    required String basePersonalityPrompt,
    required String userMessage,
  }) async {
    try {
      // Get the evolved personality for this user-coach combination
      final personality = await _getCoachPersonality(userId, coachCategory);
      
      // Get user's communication preferences
      final preferences = await PersonalizationEngine.getUserCommunicationPreferences(userId);
      
      // Get memory context for additional personalization
      final memoryContext = await CoachMemoryService.getConversationContext(userId, coachCategory);
      
      // Build evolved personality prompt
      final evolvedPrompt = _buildEvolvedPersonalityPrompt(
        basePersonalityPrompt,
        personality,
        preferences,
        memoryContext,
        userMessage,
      );
      
      if (kDebugMode) print('🎭 Generated evolved personality for $coachCategory coach');
      
      return evolvedPrompt;
    } catch (e) {
      if (kDebugMode) print('❌ Failed to get evolved personality: $e');
      return basePersonalityPrompt;
    }
  }

  /// Record interaction outcome to evolve personality
  static Future<void> recordPersonalityFeedback({
    required String userId,
    required String coachCategory,
    required String coachResponse,
    required bool wasHelpful,
    required String userFeedback,
    Map<String, dynamic>? additionalContext,
  }) async {
    try {
      final personality = await _getCoachPersonality(userId, coachCategory);
      
      // Analyze the response characteristics
      final responseAnalysis = _analyzeResponseCharacteristics(coachResponse);
      
      // Update personality based on effectiveness
      await _updatePersonalityBasedOnFeedback(
        userId,
        coachCategory,
        personality,
        responseAnalysis,
        wasHelpful,
        userFeedback,
        additionalContext ?? {},
      );
      
      if (kDebugMode) print('🎭 Recorded personality feedback for $coachCategory');
    } catch (e) {
      if (kDebugMode) print('❌ Failed to record personality feedback: $e');
    }
  }

  /// Get personality evolution report for a user
  static Future<Map<String, dynamic>> getPersonalityEvolutionReport(String userId) async {
    try {
      final userPersonalities = await _getUserPersonalities(userId);
      
      final report = <String, dynamic>{};
      
      for (final entry in userPersonalities.entries) {
        final coachCategory = entry.key;
        final personality = entry.value;
        
        report[coachCategory] = {
          'currentTraits': personality.traits,
          'communicationStyle': personality.communicationStyle,
          'motivationalApproach': personality.motivationalApproach,
          'supportStyle': personality.supportStyle,
          'adaptationLevel': personality.adaptationLevel,
          'effectivenessScore': personality.effectivenessScore,
          'totalInteractions': personality.totalInteractions,
          'evolutionHistory': personality.evolutionHistory.take(5).toList(),
          'lastEvolution': personality.lastEvolution.toIso8601String(),
        };
      }
      
      return report;
    } catch (e) {
      if (kDebugMode) print('❌ Failed to generate personality evolution report: $e');
      return {};
    }
  }

  /// Predict optimal personality adaptation for current context
  static Future<Map<String, dynamic>> predictOptimalPersonalityAdaptation({
    required String userId,
    required String coachCategory,
    required String userMessage,
  }) async {
    try {
      final personality = await _getCoachPersonality(userId, coachCategory);
      final preferences = await PersonalizationEngine.getUserCommunicationPreferences(userId);
      
      // Analyze current message for context
      final messageAnalysis = _analyzeMessageForPersonality(userMessage);
      
      // Predict optimal adaptations
      final adaptations = {
        'recommendedTone': _predictOptimalTone(personality, preferences, messageAnalysis),
        'recommendedStyle': _predictOptimalStyle(personality, preferences, messageAnalysis),
        'recommendedApproach': _predictOptimalApproach(personality, preferences, messageAnalysis),
        'confidenceLevel': _calculateAdaptationConfidence(personality),
        'reasoning': _generateAdaptationReasoning(personality, messageAnalysis),
      };
      
      return adaptations;
    } catch (e) {
      if (kDebugMode) print('❌ Failed to predict personality adaptation: $e');
      return {};
    }
  }

  /// Private helper methods

  static Future<CoachPersonality> _getCoachPersonality(String userId, String coachCategory) async {
    if (!_personalityCache.containsKey(userId)) {
      await _loadUserPersonalities(userId);
    }
    
    _personalityCache[userId] ??= {};
    _personalityCache[userId]![coachCategory] ??= CoachPersonality.defaultFor(coachCategory);
    
    return _personalityCache[userId]![coachCategory]!;
  }

  static Future<Map<String, CoachPersonality>> _getUserPersonalities(String userId) async {
    if (!_personalityCache.containsKey(userId)) {
      await _loadUserPersonalities(userId);
    }
    return _personalityCache[userId] ?? {};
  }

  static Future<void> _loadUserPersonalities(String userId) async {
    try {
      final data = await _storage.read(key: '${_personalityDataKey}_$userId');
      if (data != null) {
        final Map<String, dynamic> jsonData = jsonDecode(data);
        final personalities = <String, CoachPersonality>{};
        
        for (final entry in jsonData.entries) {
          personalities[entry.key] = CoachPersonality.fromJson(entry.value);
        }
        
        _personalityCache[userId] = personalities;
      } else {
        _personalityCache[userId] = {};
      }
    } catch (e) {
      _personalityCache[userId] = {};
    }
  }

  static Future<void> _saveUserPersonalities(String userId) async {
    try {
      final personalities = _personalityCache[userId] ?? {};
      final jsonData = <String, dynamic>{};
      
      for (final entry in personalities.entries) {
        jsonData[entry.key] = entry.value.toJson();
      }
      
      await _storage.write(
        key: '${_personalityDataKey}_$userId',
        value: jsonEncode(jsonData),
      );
    } catch (e) {
      if (kDebugMode) print('❌ Failed to save user personalities: $e');
    }
  }

  static String _buildEvolvedPersonalityPrompt(
    String basePrompt,
    CoachPersonality personality,
    Map<String, dynamic> preferences,
    Map<String, dynamic> memoryContext,
    String userMessage,
  ) {
    final evolvedPrompt = '''
$basePrompt

EVOLVED PERSONALITY TRAITS (adapted for this specific user):
- Communication Style: ${personality.communicationStyle}
- Motivational Approach: ${personality.motivationalApproach}
- Support Style: ${personality.supportStyle}
- Tone Preference: ${preferences['preferredTone'] ?? 'balanced'}
- Response Length: ${preferences['preferredLength'] ?? 'moderate'}

PERSONALITY ADAPTATIONS:
${_generatePersonalityAdaptations(personality, preferences)}

USER-SPECIFIC CONTEXT:
- This user responds best to: ${personality.effectiveStrategies.keys.take(3).join(', ')}
- Avoid these approaches: ${personality.ineffectiveStrategies.keys.take(2).join(', ')}
- Current mood: ${memoryContext['userMood'] ?? 'neutral'}
- Preferred style: ${memoryContext['preferredStyle'] ?? 'balanced'}

EVOLUTION GUIDANCE:
- Adaptation Level: ${personality.adaptationLevel}/10
- Effectiveness Score: ${(personality.effectivenessScore * 100).toStringAsFixed(1)}%
- Total Interactions: ${personality.totalInteractions}

Remember: You have evolved specifically for this user. Use the personality traits and approaches that have proven most effective in your ${personality.totalInteractions} previous interactions with them.
''';

    return evolvedPrompt;
  }

  static String _generatePersonalityAdaptations(
    CoachPersonality personality,
    Map<String, dynamic> preferences,
  ) {
    final adaptations = <String>[];
    
    // Communication style adaptations
    if (personality.communicationStyle == 'detailed') {
      adaptations.add('- Provide thorough explanations and comprehensive guidance');
    } else if (personality.communicationStyle == 'concise') {
      adaptations.add('- Keep responses brief and to the point');
    }
    
    // Motivational approach adaptations
    if (personality.motivationalApproach == 'challenging') {
      adaptations.add('- Use challenging language and push for growth');
    } else if (personality.motivationalApproach == 'supportive') {
      adaptations.add('- Focus on encouragement and positive reinforcement');
    }
    
    // Support style adaptations
    if (personality.supportStyle == 'analytical') {
      adaptations.add('- Provide logical, step-by-step guidance');
    } else if (personality.supportStyle == 'emotional') {
      adaptations.add('- Focus on emotional support and understanding');
    }
    
    return adaptations.isEmpty ? '- Use balanced, adaptive approach' : adaptations.join('\n');
  }

  static Map<String, dynamic> _analyzeResponseCharacteristics(String response) {
    return {
      'length': response.length,
      'tone': _detectResponseTone(response),
      'style': _detectResponseStyle(response),
      'approach': _detectResponseApproach(response),
      'includesQuestion': response.contains('?'),
      'includesAction': _containsActionItems(response),
      'emotionalSupport': _detectEmotionalSupport(response),
      'motivationalLevel': _detectMotivationalLevel(response),
    };
  }

  static Future<void> _updatePersonalityBasedOnFeedback(
    String userId,
    String coachCategory,
    CoachPersonality personality,
    Map<String, dynamic> responseAnalysis,
    bool wasHelpful,
    String userFeedback,
    Map<String, dynamic> additionalContext,
  ) async {
    final learningRate = 0.1;
    var updatedPersonality = personality;
    
    if (wasHelpful) {
      // Reinforce successful characteristics
      final tone = responseAnalysis['tone'] as String;
      final style = responseAnalysis['style'] as String;
      final approach = responseAnalysis['approach'] as String;
      
      // Update effective strategies
      updatedPersonality.effectiveStrategies[tone] = 
          (updatedPersonality.effectiveStrategies[tone] ?? 0.5) * (1 - learningRate) + 
          1.0 * learningRate;
      
      updatedPersonality.effectiveStrategies[style] =
          (updatedPersonality.effectiveStrategies[style] ?? 0.5) * (1 - learningRate) +
          1.0 * learningRate;

      updatedPersonality.effectiveStrategies[approach] =
          (updatedPersonality.effectiveStrategies[approach] ?? 0.5) * (1 - learningRate) +
          1.0 * learningRate;

      // Adapt personality traits
      if (tone == 'supportive') {
        updatedPersonality.supportStyle = 'emotional';
      } else if (tone == 'challenging') {
        updatedPersonality.motivationalApproach = 'challenging';
      }
      
      if (style == 'detailed') {
        updatedPersonality.communicationStyle = 'detailed';
      } else if (style == 'concise') {
        updatedPersonality.communicationStyle = 'concise';
      }
      
      // Increase effectiveness score
      updatedPersonality.effectivenessScore = 
          updatedPersonality.effectivenessScore * 0.9 + 1.0 * 0.1;
      
    } else {
      // Learn from unsuccessful approaches
      final tone = responseAnalysis['tone'] as String;
      final style = responseAnalysis['style'] as String;
      
      updatedPersonality.ineffectiveStrategies[tone] = 
          (updatedPersonality.ineffectiveStrategies[tone] ?? 0.5) * (1 - learningRate) + 
          1.0 * learningRate;
      
      updatedPersonality.ineffectiveStrategies[style] = 
          (updatedPersonality.ineffectiveStrategies[style] ?? 0.5) * (1 - learningRate) + 
          1.0 * learningRate;
      
      // Decrease effectiveness score slightly
      updatedPersonality.effectivenessScore = 
          updatedPersonality.effectivenessScore * 0.95 + 0.0 * 0.05;
    }
    
    // Record evolution
    updatedPersonality.evolutionHistory.add({
      'timestamp': DateTime.now().toIso8601String(),
      'change': wasHelpful ? 'reinforcement' : 'adaptation',
      'characteristics': responseAnalysis,
      'effectiveness': wasHelpful,
    });
    
    // Keep only last 20 evolution records
    if (updatedPersonality.evolutionHistory.length > 20) {
      updatedPersonality.evolutionHistory.removeAt(0);
    }
    
    // Update adaptation level
    updatedPersonality.adaptationLevel = min(10, updatedPersonality.adaptationLevel + 0.1);
    
    // Update counters
    updatedPersonality.totalInteractions += 1;
    updatedPersonality.lastEvolution = DateTime.now();
    
    // Save updated personality
    _personalityCache[userId]![coachCategory] = updatedPersonality;
    await _saveUserPersonalities(userId);
  }

  static Map<String, dynamic> _analyzeMessageForPersonality(String message) {
    return {
      'emotionalState': _detectEmotionalState(message),
      'complexity': message.length > 100 ? 'high' : 'low',
      'urgency': _detectUrgency(message),
      'supportNeed': _detectSupportNeed(message),
      'questionType': message.contains('?') ? 'questioning' : 'statement',
    };
  }

  // Analysis helper methods
  static String _detectResponseTone(String response) {
    final lowerResponse = response.toLowerCase();

    if (lowerResponse.contains(RegExp(r'\b(amazing|fantastic|incredible|awesome)\b'))) {
      return 'enthusiastic';
    }
    if (lowerResponse.contains(RegExp(r'\b(understand|feel|empathy|support)\b'))) {
      return 'supportive';
    }
    if (lowerResponse.contains(RegExp(r'\b(challenge|push|achieve|excel)\b'))) {
      return 'challenging';
    }
    if (lowerResponse.contains(RegExp(r'\b(gentle|calm|peaceful|patient)\b'))) {
      return 'gentle';
    }
    
    return 'balanced';
  }

  static String _detectResponseStyle(String response) {
    if (response.length > 200) return 'detailed';
    if (response.length < 80) return 'concise';
    return 'moderate';
  }

  static String _detectResponseApproach(String response) {
    final lowerResponse = response.toLowerCase();
    
    if (lowerResponse.contains(RegExp(r'\b(step|plan|strategy|method)\b'))) {
      return 'strategic';
    }
    if (lowerResponse.contains(RegExp(r'\b(feel|emotion|heart|soul)\b'))) {
      return 'emotional';
    }
    if (lowerResponse.contains(RegExp(r'\b(think|analyze|consider|examine)\b'))) {
      return 'analytical';
    }
    
    return 'balanced';
  }

  static bool _containsActionItems(String response) {
    final lowerResponse = response.toLowerCase();
    return lowerResponse.contains(RegExp(r'\b(try|do|start|begin|take|make|create)\b'));
  }

  static double _detectEmotionalSupport(String response) {
    final lowerResponse = response.toLowerCase();
    final supportWords = ['understand', 'feel', 'empathy', 'support', 'care', 'here for you'];
    final supportCount = supportWords.where((word) => lowerResponse.contains(word)).length;
    return (supportCount / supportWords.length).clamp(0.0, 1.0);
  }

  static double _detectMotivationalLevel(String response) {
    final lowerResponse = response.toLowerCase();
    final motivationalWords = ['achieve', 'succeed', 'excel', 'grow', 'improve', 'progress'];
    final motivationalCount = motivationalWords.where((word) => lowerResponse.contains(word)).length;
    return (motivationalCount / motivationalWords.length).clamp(0.0, 1.0);
  }

  static String _detectEmotionalState(String message) {
    final lowerMessage = message.toLowerCase();

    if (lowerMessage.contains(RegExp(r'\b(stressed|overwhelmed|anxious)\b'))) {
      return 'stressed';
    }
    if (lowerMessage.contains(RegExp(r'\b(excited|motivated|energized)\b'))) {
      return 'energized';
    }
    if (lowerMessage.contains(RegExp(r'\b(confused|lost|uncertain)\b'))) {
      return 'confused';
    }
    if (lowerMessage.contains(RegExp(r'\b(sad|down|depressed)\b'))) {
      return 'sad';
    }
    
    return 'neutral';
  }

  static String _detectUrgency(String message) {
    final lowerMessage = message.toLowerCase();

    if (lowerMessage.contains(RegExp(r'\b(urgent|asap|immediately|now)\b'))) {
      return 'high';
    }
    if (lowerMessage.contains(RegExp(r'\b(soon|quickly|fast)\b'))) {
      return 'medium';
    }
    
    return 'low';
  }

  static String _detectSupportNeed(String message) {
    final lowerMessage = message.toLowerCase();

    if (lowerMessage.contains(RegExp(r'\b(help|support|guidance|advice)\b'))) {
      return 'high';
    }
    if (lowerMessage.contains(RegExp(r'\b(question|wonder|curious)\b'))) {
      return 'medium';
    }
    
    return 'low';
  }

  // Prediction methods
  static String _predictOptimalTone(
    CoachPersonality personality,
    Map<String, dynamic> preferences,
    Map<String, dynamic> messageAnalysis,
  ) {
    final emotionalState = messageAnalysis['emotionalState'] as String;
    
    if (emotionalState == 'stressed') return 'gentle';
    if (emotionalState == 'energized') return 'enthusiastic';
    if (emotionalState == 'confused') return 'supportive';
    
    // Use most effective tone from personality
    final effectiveTones = personality.effectiveStrategies.entries
        .where((e) => ['gentle', 'enthusiastic', 'supportive', 'challenging'].contains(e.key))
        .toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    return effectiveTones.isNotEmpty ? effectiveTones.first.key : 'balanced';
  }

  static String _predictOptimalStyle(
    CoachPersonality personality,
    Map<String, dynamic> preferences,
    Map<String, dynamic> messageAnalysis,
  ) {
    final complexity = messageAnalysis['complexity'] as String;
    
    if (complexity == 'high') return 'detailed';
    if (complexity == 'low') return 'concise';
    
    return personality.communicationStyle;
  }

  static String _predictOptimalApproach(
    CoachPersonality personality,
    Map<String, dynamic> preferences,
    Map<String, dynamic> messageAnalysis,
  ) {
    final supportNeed = messageAnalysis['supportNeed'] as String;
    
    if (supportNeed == 'high') return 'emotional';
    
    // Use most effective approach from personality
    final effectiveApproaches = personality.effectiveStrategies.entries
        .where((e) => ['strategic', 'emotional', 'analytical'].contains(e.key))
        .toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    return effectiveApproaches.isNotEmpty ? effectiveApproaches.first.key : 'balanced';
  }

  static double _calculateAdaptationConfidence(CoachPersonality personality) {
    // Higher confidence with more interactions and higher effectiveness
    final interactionFactor = min(1.0, personality.totalInteractions / 50.0);
    final effectivenessFactor = personality.effectivenessScore;
    
    return (interactionFactor * 0.6 + effectivenessFactor * 0.4).clamp(0.0, 1.0);
  }

  static String _generateAdaptationReasoning(
    CoachPersonality personality,
    Map<String, dynamic> messageAnalysis,
  ) {
    final reasons = <String>[];
    
    if (personality.totalInteractions > 10) {
      reasons.add('Based on ${personality.totalInteractions} previous interactions');
    }
    
    if (personality.effectivenessScore > 0.7) {
      reasons.add('High effectiveness score (${(personality.effectivenessScore * 100).toStringAsFixed(1)}%)');
    }
    
    final emotionalState = messageAnalysis['emotionalState'] as String;
    if (emotionalState != 'neutral') {
      reasons.add('Adapted for current emotional state: $emotionalState');
    }
    
    return reasons.isEmpty ? 'Standard personality adaptation' : reasons.join(', ');
  }
}

/// Represents an evolved coach personality for a specific user
class CoachPersonality {
  final String coachCategory;
  String communicationStyle;
  String motivationalApproach;
  String supportStyle;
  double adaptationLevel;
  double effectivenessScore;
  int totalInteractions;
  final Map<String, double> effectiveStrategies;
  final Map<String, double> ineffectiveStrategies;
  final List<Map<String, dynamic>> evolutionHistory;
  DateTime lastEvolution;

  CoachPersonality({
    required this.coachCategory,
    required this.communicationStyle,
    required this.motivationalApproach,
    required this.supportStyle,
    required this.adaptationLevel,
    required this.effectivenessScore,
    required this.totalInteractions,
    required this.effectiveStrategies,
    required this.ineffectiveStrategies,
    required this.evolutionHistory,
    required this.lastEvolution,
  });

  factory CoachPersonality.defaultFor(String coachCategory) {
    return CoachPersonality(
      coachCategory: coachCategory,
      communicationStyle: 'balanced',
      motivationalApproach: 'balanced',
      supportStyle: 'balanced',
      adaptationLevel: 0.0,
      effectivenessScore: 0.5,
      totalInteractions: 0,
      effectiveStrategies: {},
      ineffectiveStrategies: {},
      evolutionHistory: [],
      lastEvolution: DateTime.now(),
    );
  }

  factory CoachPersonality.fromJson(Map<String, dynamic> json) {
    return CoachPersonality(
      coachCategory: json['coachCategory'] as String,
      communicationStyle: json['communicationStyle'] as String,
      motivationalApproach: json['motivationalApproach'] as String,
      supportStyle: json['supportStyle'] as String,
      adaptationLevel: (json['adaptationLevel'] as num).toDouble(),
      effectivenessScore: (json['effectivenessScore'] as num).toDouble(),
      totalInteractions: json['totalInteractions'] as int,
      effectiveStrategies: Map<String, double>.from(json['effectiveStrategies'] as Map),
      ineffectiveStrategies: Map<String, double>.from(json['ineffectiveStrategies'] as Map),
      evolutionHistory: List<Map<String, dynamic>>.from(json['evolutionHistory'] as List),
      lastEvolution: DateTime.parse(json['lastEvolution'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'coachCategory': coachCategory,
      'communicationStyle': communicationStyle,
      'motivationalApproach': motivationalApproach,
      'supportStyle': supportStyle,
      'adaptationLevel': adaptationLevel,
      'effectivenessScore': effectivenessScore,
      'totalInteractions': totalInteractions,
      'effectiveStrategies': effectiveStrategies,
      'ineffectiveStrategies': ineffectiveStrategies,
      'evolutionHistory': evolutionHistory,
      'lastEvolution': lastEvolution.toIso8601String(),
    };
  }

  Map<String, String> get traits {
    return {
      'communicationStyle': communicationStyle,
      'motivationalApproach': motivationalApproach,
      'supportStyle': supportStyle,
    };
  }
}
