// 📁 lib/services/robust_image_service.dart

import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import '../services/comprehensive_logging_service.dart';
import '../utils/debug_logger.dart';

/// Simple and reliable image selection service using standard image_picker
///
/// This service uses the standard image_picker package with proper error handling
/// to avoid freezing issues while still providing camera and photo library access.
class RobustImageService {
  static final ImagePicker _picker = ImagePicker();

  /// Primary method: Select image with camera or photo library access
  static Future<String?> selectImageSafely({
    required String context,
    bool allowCamera = true,
  }) async {
    try {
      await ComprehensiveLoggingService.logInfo('📸 RobustImageService: Starting image selection');
      await ComprehensiveLoggingService.logInfo('📸 Context: $context');
      await ComprehensiveLoggingService.logInfo('📸 Camera allowed: $allowCamera');

      // Show action sheet for user to choose camera or gallery
      if (Platform.isIOS) {
        return await _showImageSourceActionSheet(context, allowCamera);
      } else {
        // For Android, default to gallery
        return await _selectFromGallery(context);
      }

    } catch (e, stackTrace) {
      await ComprehensiveLoggingService.logError('❌ RobustImageService error: $e');
      DebugLogger.error('RobustImageService', 'Failed to select image safely', e, stackTrace);
      return null;
    }
  }

  /// Show action sheet for iOS to choose between camera and gallery
  static Future<String?> _showImageSourceActionSheet(String context, bool allowCamera) async {
    try {
      await ComprehensiveLoggingService.logInfo('📸 Showing image source options');

      // For now, let's try gallery first (most common use case)
      // We can add camera support once gallery is working
      return await _selectFromGallery(context);

    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Action sheet failed: $e');
      return null;
    }
  }

  /// Select image from gallery using image_picker
  static Future<String?> _selectFromGallery(String context) async {
    try {
      await ComprehensiveLoggingService.logInfo('📸 Opening photo gallery');

      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1920,
        maxHeight: 1920,
        imageQuality: 85,
      );

      if (image != null) {
        await ComprehensiveLoggingService.logInfo('✅ Image selected from gallery: ${image.path}');
        return image.path;
      } else {
        await ComprehensiveLoggingService.logInfo('ℹ️ User cancelled gallery selection');
        return null;
      }

    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Gallery selection failed: $e');
      return null;
    }
  }

  /// Select image from camera using image_picker
  static Future<String?> _selectFromCamera(String context) async {
    try {
      await ComprehensiveLoggingService.logInfo('📸 Opening camera');

      final XFile? image = await _picker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1920,
        maxHeight: 1920,
        imageQuality: 85,
      );

      if (image != null) {
        await ComprehensiveLoggingService.logInfo('✅ Image captured from camera: ${image.path}');
        return image.path;
      } else {
        await ComprehensiveLoggingService.logInfo('ℹ️ User cancelled camera');
        return null;
      }

    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Camera capture failed: $e');
      return null;
    }
  }

  /// Validate that an image file exists and is accessible
  static Future<bool> validateImageFile(String imagePath) async {
    try {
      final File imageFile = File(imagePath);

      if (!await imageFile.exists()) {
        await ComprehensiveLoggingService.logInfo('⚠️ Image file does not exist: $imagePath');
        return false;
      }

      final int fileSize = await imageFile.length();
      if (fileSize == 0) {
        await ComprehensiveLoggingService.logInfo('⚠️ Image file is empty: $imagePath');
        return false;
      }

      await ComprehensiveLoggingService.logInfo('✅ Image validation successful: $imagePath ($fileSize bytes)');
      return true;

    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Image validation failed: $e');
      return false;
    }
  }
}