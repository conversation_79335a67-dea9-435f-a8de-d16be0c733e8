// lib/services/offline_mode_service.dart

import 'dart:convert';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

import '../prompts/mxd_life_coaches.dart';
import 'error_handler_service.dart';
import 'lightweight_coach_service.dart';

/// Service for providing offline functionality and intelligent fallback responses
class OfflineModeService {
  static const FlutterSecureStorage _storage = FlutterSecureStorage();
  static const String _offlineCacheKey = 'offline_response_cache';
  static const String _offlineStatsKey = 'offline_usage_stats';
  static bool _isOfflineMode = false;

  /// Check if the app should operate in offline mode
  static Future<bool> shouldUseOfflineMode() async {
    try {
      // SIMULATOR FIX: Be less aggressive about offline mode in debug/simulator
      if (kDebugMode) {
        // In debug mode, prefer online mode unless explicitly failing
        try {
          // Quick API validation first (faster than network check)
          final apiValid = await ErrorHandlerService.validateApiConfiguration();

          if (!apiValid) {
            if (kDebugMode) print('🔧 API configuration invalid, using offline mode');
            await _setOfflineMode(true);
            return true;
          }

          // Only check network if API is valid
          final networkAvailable = await ErrorHandlerService.isNetworkAvailable();

          if (!networkAvailable) {
            if (kDebugMode) print('📡 Network unavailable, using offline mode');
            await _setOfflineMode(true);
            return true;
          }

          // Both API and network are good
          if (kDebugMode) print('🌐 Online mode: API and network both available');
          await _setOfflineMode(false);
          return false;

        } catch (e) {
          if (kDebugMode) print('⚠️ Debug mode offline check error: $e - staying online');
          await _setOfflineMode(false);
          return false; // In debug, prefer online mode on errors
        }
      }

      // Production mode: Original logic
      final networkAvailable = await ErrorHandlerService.isNetworkAvailable();

      if (!networkAvailable) {
        await _setOfflineMode(true);
        return true;
      }

      final apiValid = await ErrorHandlerService.validateApiConfiguration();

      if (!apiValid) {
        await _setOfflineMode(true);
        return true;
      }

      await _setOfflineMode(false);
      return false;
    } catch (e) {
      if (kDebugMode) print('❌ Failed to check offline mode: $e');
      // In debug mode, prefer online; in production, prefer offline for safety
      final useOffline = !kDebugMode;
      await _setOfflineMode(useOffline);
      return useOffline;
    }
  }

  /// Set offline mode status
  static Future<void> _setOfflineMode(bool isOffline) async {
    _isOfflineMode = isOffline;
    
    if (isOffline) {
      await _trackOfflineUsage();
      if (kDebugMode) print('📱 Switched to offline mode');
    } else {
      if (kDebugMode) print('🌐 Switched to online mode');
    }
  }

  /// Get intelligent offline response for coach interaction
  static Future<String> getOfflineCoachResponse({
    required String category,
    required String userPrompt,
    required String userGender,
    required String username,
    Map<String, String>? assignedCoaches,
  }) async {
    try {
      await _trackOfflineUsage();
      
      // Get coach information
      final coachInfo = _getCoachInfo(category, userGender, assignedCoaches);
      
      // Analyze user prompt for context
      final promptContext = _analyzePromptContext(userPrompt);
      
      // Get cached responses if available
      final cachedResponse = await _getCachedResponse(category, promptContext);
      if (cachedResponse != null) {
        return _personalizeResponse(cachedResponse, username, coachInfo.name);
      }
      
      // ENHANCED: Try lightweight coach service first, fallback to contextual response
      String response;
      try {
        if (LightweightCoachService.isReady) {
          response = await LightweightCoachService.generateResponse(
            coachName: coachInfo.name,
            category: category,
            userMessage: userPrompt,
            userName: username,
          );
          if (kDebugMode) print('✅ Used lightweight coach service for offline response');
        } else {
          throw Exception('Lightweight service not ready');
        }
      } catch (e) {
        // Fallback to original contextual response
        response = _generateContextualResponse(
          category: category,
          promptContext: promptContext,
          coachName: coachInfo.name,
          username: username,
          userPrompt: userPrompt,
        );
        if (kDebugMode) print('⚠️ Fallback to contextual response: $e');
      }
      
      // Cache the response for future use
      await _cacheResponse(category, promptContext, response);
      
      return response;
    } catch (e) {
      if (kDebugMode) print('❌ Offline response generation failed: $e');
      return _getEmergencyFallbackResponse(category, username);
    }
  }

  /// Analyze user prompt to understand context and intent
  static PromptContext _analyzePromptContext(String prompt) {
    final lowerPrompt = prompt.toLowerCase();
    
    // Determine intent
    PromptIntent intent = PromptIntent.general;
    if (_containsAny(lowerPrompt, ['help', 'how', 'what', 'why', 'when', 'where'])) {
      intent = PromptIntent.question;
    } else if (_containsAny(lowerPrompt, ['feel', 'feeling', 'emotion', 'sad', 'happy', 'angry', 'stressed'])) {
      intent = PromptIntent.emotional;
    } else if (_containsAny(lowerPrompt, ['goal', 'plan', 'want to', 'trying to', 'working on'])) {
      intent = PromptIntent.goal;
    } else if (_containsAny(lowerPrompt, ['problem', 'issue', 'trouble', 'difficult', 'challenge'])) {
      intent = PromptIntent.problem;
    } else if (_containsAny(lowerPrompt, ['thanks', 'thank you', 'appreciate', 'grateful'])) {
      intent = PromptIntent.gratitude;
    }
    
    // Determine urgency
    PromptUrgency urgency = PromptUrgency.normal;
    if (_containsAny(lowerPrompt, ['urgent', 'emergency', 'crisis', 'immediate', 'asap'])) {
      urgency = PromptUrgency.high;
    } else if (_containsAny(lowerPrompt, ['whenever', 'no rush', 'when you can', 'eventually'])) {
      urgency = PromptUrgency.low;
    }
    
    // Extract key topics
    final topics = <String>[];
    final topicKeywords = {
      'exercise': ['workout', 'exercise', 'gym', 'fitness', 'training'],
      'nutrition': ['food', 'eat', 'diet', 'nutrition', 'meal'],
      'sleep': ['sleep', 'tired', 'rest', 'insomnia', 'bed'],
      'stress': ['stress', 'anxiety', 'overwhelmed', 'pressure'],
      'money': ['money', 'financial', 'budget', 'income', 'expense'],
      'career': ['job', 'work', 'career', 'professional', 'business'],
      'relationships': ['relationship', 'friend', 'family', 'partner', 'social'],
      'habits': ['habit', 'routine', 'daily', 'consistency', 'practice'],
    };
    
    for (final entry in topicKeywords.entries) {
      if (_containsAny(lowerPrompt, entry.value)) {
        topics.add(entry.key);
      }
    }
    
    return PromptContext(
      intent: intent,
      urgency: urgency,
      topics: topics,
      length: prompt.length,
    );
  }

  /// Check if text contains any of the given keywords
  static bool _containsAny(String text, List<String> keywords) {
    return keywords.any((keyword) => text.contains(keyword));
  }

  /// Generate contextual offline response
  static String _generateContextualResponse({
    required String category,
    required PromptContext promptContext,
    required String coachName,
    required String username,
    required String userPrompt,
  }) {
    final responses = _getResponseTemplates(category, promptContext);
    final random = Random();
    final template = responses[random.nextInt(responses.length)];
    
    return template
        .replaceAll('{username}', username)
        .replaceAll('{coachName}', coachName)
        .replaceAll('{category}', category.toLowerCase());
  }

  /// Get response templates based on category and context
  static List<String> _getResponseTemplates(String category, PromptContext context) {
    final baseResponses = _getCategoryResponses(category);
    final contextResponses = _getContextualResponses(context);
    
    // Combine and return relevant responses
    return [...baseResponses, ...contextResponses];
  }

  /// Get category-specific response templates
  static List<String> _getCategoryResponses(String category) {
    switch (category.toLowerCase()) {
      case 'health':
        return [
          "I'm currently offline, but I want you to know that your health journey matters, {username}. Focus on the basics: move your body, nourish yourself well, and prioritize rest.",
          "Even though I can't access my full knowledge right now, {username}, remember that small daily health choices compound into massive results over time.",
          "While I'm offline, {username}, trust your body's wisdom. Listen to what it needs - whether that's movement, nutrition, or recovery.",
        ];
      case 'wealth':
        return [
          "I'm offline right now, {username}, but your wealth-building journey continues. Focus on what you can control: your spending, saving, and learning.",
          "Even without my full capabilities, {username}, remember that wealth is built through consistent daily actions and smart decisions over time.",
          "While I'm offline, {username}, consider this: every dollar you save and invest today is working for your future freedom.",
        ];
      case 'purpose':
        return [
          "I'm currently offline, {username}, but your purpose doesn't pause. Reflect on what truly matters to you and take one small step toward it.",
          "Even though I can't access everything right now, {username}, remember that purpose is found in serving something greater than yourself.",
          "While I'm offline, {username}, trust that your unique gifts and experiences are meant to make a difference in this world.",
        ];
      case 'connection':
        return [
          "I'm offline right now, {username}, but human connection doesn't require technology. Reach out to someone you care about today.",
          "Even without my full capabilities, {username}, remember that authentic relationships are built through presence, not perfection.",
          "While I'm offline, {username}, consider how you can show up more fully for the people who matter most to you.",
        ];
      default:
        return [
          "I'm currently offline, {username}, but your growth journey continues. Trust yourself and take the next right step.",
          "Even though I can't access my full knowledge right now, {username}, remember that you have everything you need within you.",
          "While I'm offline, {username}, focus on progress over perfection and keep moving forward.",
        ];
    }
  }

  /// Get contextual responses based on prompt analysis
  static List<String> _getContextualResponses(PromptContext context) {
    final responses = <String>[];
    
    switch (context.intent) {
      case PromptIntent.question:
        responses.addAll([
          "I'm offline and can't provide detailed answers right now, {username}, but I encourage you to trust your instincts and seek wisdom from trusted sources.",
          "While I can't access my full knowledge, {username}, remember that the best answers often come from within and from your own experience.",
        ]);
        break;
      case PromptIntent.emotional:
        responses.addAll([
          "I'm offline but I want you to know your feelings are valid, {username}. Take a deep breath and be gentle with yourself.",
          "Even though I can't provide my usual support, {username}, remember that emotions are temporary and you're stronger than you know.",
        ]);
        break;
      case PromptIntent.goal:
        responses.addAll([
          "I'm offline, but your goals are still achievable, {username}. Break them down into small, actionable steps you can take today.",
          "While I can't provide detailed guidance right now, {username}, remember that consistency beats perfection every time.",
        ]);
        break;
      case PromptIntent.problem:
        responses.addAll([
          "I'm offline and can't help solve this directly, {username}, but I believe in your ability to find solutions. Start with what you can control.",
          "Even though I can't provide my usual problem-solving support, {username}, remember that challenges are opportunities for growth.",
        ]);
        break;
      case PromptIntent.gratitude:
        responses.addAll([
          "I'm offline but grateful to be part of your journey, {username}. Your appreciation means everything.",
          "Even though I can't respond fully, {username}, your gratitude reminds me why this work matters. Keep shining.",
        ]);
        break;
      default:
        responses.addAll([
          "I'm currently offline, {username}, but I'm here in spirit. Keep being awesome.",
          "While I can't provide my usual support, {username}, remember that you're capable of amazing things.",
        ]);
    }
    
    return responses;
  }

  /// Get coach information
  static ({String name, String description}) _getCoachInfo(
    String category,
    String userGender,
    Map<String, String>? assignedCoaches,
  ) {
    // Handle non-gender users
    String effectiveGender = userGender;
    if (userGender.toLowerCase() == 'non-gender' && assignedCoaches != null) {
      effectiveGender = assignedCoaches[category] ?? 'male';
    }
    
    final coach = mxdLifeCoaches.firstWhere(
      (c) => c.category == category,
      orElse: () => mxdLifeCoaches.first,
    );
    
    final coachName = effectiveGender.toLowerCase() == 'male' 
        ? coach.maleName 
        : coach.femaleName;
    
    return (name: coachName, description: coach.description);
  }

  /// Personalize response with user and coach details
  static String _personalizeResponse(String template, String username, String coachName) {
    return template
        .replaceAll('{username}', username)
        .replaceAll('{coachName}', coachName);
  }

  /// Get emergency fallback response
  static String _getEmergencyFallbackResponse(String category, String username) {
    return "Hi $username, I'm having technical difficulties right now, but I want you to know that I believe in your ability to grow and succeed. Keep moving forward with your ${category.toLowerCase()} journey.";
  }

  /// Cache response for future use
  static Future<void> _cacheResponse(String category, PromptContext context, String response) async {
    try {
      final cache = await _getResponseCache();
      final cacheKey = '${category}_${context.intent}_${context.topics.join('_')}';
      
      cache[cacheKey] = {
        'response': response,
        'timestamp': DateTime.now().toIso8601String(),
        'useCount': 0,
      };
      
      // Keep cache size manageable
      if (cache.length > 100) {
        final oldestKey = cache.entries
            .reduce((a, b) => DateTime.parse(a.value['timestamp'])
                .isBefore(DateTime.parse(b.value['timestamp'])) ? a : b)
            .key;
        cache.remove(oldestKey);
      }
      
      await _storage.write(key: _offlineCacheKey, value: jsonEncode(cache));
    } catch (e) {
      if (kDebugMode) print('❌ Failed to cache response: $e');
    }
  }

  /// Get cached response if available
  static Future<String?> _getCachedResponse(String category, PromptContext context) async {
    try {
      final cache = await _getResponseCache();
      final cacheKey = '${category}_${context.intent}_${context.topics.join('_')}';
      
      if (cache.containsKey(cacheKey)) {
        final cachedData = cache[cacheKey];
        cachedData['useCount'] = (cachedData['useCount'] ?? 0) + 1;
        
        // Update cache with new use count
        await _storage.write(key: _offlineCacheKey, value: jsonEncode(cache));
        
        return cachedData['response'];
      }
    } catch (e) {
      if (kDebugMode) print('❌ Failed to get cached response: $e');
    }
    return null;
  }

  /// Get response cache
  static Future<Map<String, dynamic>> _getResponseCache() async {
    try {
      final cacheData = await _storage.read(key: _offlineCacheKey);
      if (cacheData != null) {
        return Map<String, dynamic>.from(jsonDecode(cacheData));
      }
    } catch (e) {
      if (kDebugMode) print('❌ Failed to load response cache: $e');
    }
    return {};
  }

  /// Track offline usage for analytics
  static Future<void> _trackOfflineUsage() async {
    try {
      final stats = await _getOfflineStats();
      stats['totalOfflineInteractions'] = (stats['totalOfflineInteractions'] ?? 0) + 1;
      stats['lastOfflineUse'] = DateTime.now().toIso8601String();
      
      await _storage.write(key: _offlineStatsKey, value: jsonEncode(stats));
    } catch (e) {
      if (kDebugMode) print('❌ Failed to track offline usage: $e');
    }
  }

  /// Get offline usage statistics
  static Future<Map<String, dynamic>> _getOfflineStats() async {
    try {
      final statsData = await _storage.read(key: _offlineStatsKey);
      if (statsData != null) {
        return Map<String, dynamic>.from(jsonDecode(statsData));
      }
    } catch (e) {
      if (kDebugMode) print('❌ Failed to load offline stats: $e');
    }
    return {};
  }

  /// Get offline mode status
  static bool get isOfflineMode => _isOfflineMode;

  /// Force online mode (for testing/debugging)
  static Future<void> forceOnlineMode() async {
    try {
      await _setOfflineMode(false);
      if (kDebugMode) print('🔧 Forced online mode for testing');
    } catch (e) {
      if (kDebugMode) print('❌ Failed to force online mode: $e');
    }
  }

  /// Clear offline cache (for testing or storage management)
  static Future<void> clearOfflineCache() async {
    try {
      await _storage.delete(key: _offlineCacheKey);
      await _storage.delete(key: _offlineStatsKey);
      if (kDebugMode) print('🗑️ Offline cache cleared');
    } catch (e) {
      if (kDebugMode) print('❌ Failed to clear offline cache: $e');
    }
  }
}

/// Context analysis for user prompts
class PromptContext {
  final PromptIntent intent;
  final PromptUrgency urgency;
  final List<String> topics;
  final int length;

  const PromptContext({
    required this.intent,
    required this.urgency,
    required this.topics,
    required this.length,
  });
}

/// Types of user prompt intents
enum PromptIntent {
  question,
  emotional,
  goal,
  problem,
  gratitude,
  general,
}

/// Urgency levels for prompts
enum PromptUrgency {
  low,
  normal,
  high,
}
