// lib/services/coach_checkin_service.dart

import 'dart:convert';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../models/user_model.dart';
import '../prompts/mxd_life_coaches.dart';


/// Service for managing proactive coach check-ins with smart randomized scheduling
class CoachCheckinService {
  static const FlutterSecureStorage _storage = FlutterSecureStorage();
  static const String _checkinDataKey = 'coach_checkin_data';
  static const String _checkinHistoryKey = 'coach_checkin_history';
  static const String _settingsKey = 'coach_checkin_settings';
  
  // Check-in window: 5:45 AM to 8:45 PM (15 hours)
  static const int _windowStartHour = 5;
  static const int _windowStartMinute = 45;
  static const int _windowEndHour = 20;
  static const int _windowEndMinute = 45;
  
  // Eligibility window: 24-48 hours
  static const int _minHoursSinceLastMessage = 24;
  static const int _maxHoursSinceLastMessage = 48;
  
  // Rate limiting: max 6 check-ins per 2 days
  static const int _maxCheckinsPerTwoDays = 6;
  static const int _rateLimitHours = 48;

  /// Initialize check-in system for a user
  static Future<void> initializeForUser(User user) async {
    try {
      final checkinData = await _getCheckinData();
      
      // Initialize coach data if not exists
      final coaches = _getAllCoachesForUser(user);
      for (final coach in coaches) {
        final coachKey = '${coach.category}_${coach.name}';
        if (!checkinData.containsKey(coachKey)) {
          checkinData[coachKey] = {
            'category': coach.category,
            'coachName': coach.name,
            'lastMessageTime': null,
            'lastCheckinTime': null,
            'nextEligibleTime': DateTime.now().add(Duration(hours: _minHoursSinceLastMessage)).toIso8601String(),
            'isEnabled': true,
            'totalCheckinsCount': 0,
          };
        }
      }
      
      await _saveCheckinData(checkinData);
      
      if (kDebugMode) {
        print('🔔 Coach check-in system initialized for ${coaches.length} coaches');
      }
    } catch (e) {
      if (kDebugMode) print('❌ Failed to initialize check-in system: $e');
    }
  }

  /// Update last message time for a coach
  static Future<void> updateLastMessageTime(String category, String coachName) async {
    try {
      final checkinData = await _getCheckinData();
      final coachKey = '${category}_$coachName';
      
      if (checkinData.containsKey(coachKey)) {
        final now = DateTime.now();
        checkinData[coachKey]['lastMessageTime'] = now.toIso8601String();
        
        // Set next eligible time (24-48 hours from now, randomized)
        final hoursUntilEligible = _minHoursSinceLastMessage + 
            Random().nextInt(_maxHoursSinceLastMessage - _minHoursSinceLastMessage);
        checkinData[coachKey]['nextEligibleTime'] = 
            now.add(Duration(hours: hoursUntilEligible)).toIso8601String();
        
        await _saveCheckinData(checkinData);
        
        if (kDebugMode) {
          print('🔔 Updated last message time for $coachName ($category)');
        }
      }
    } catch (e) {
      if (kDebugMode) print('❌ Failed to update last message time: $e');
    }
  }

  /// Get eligible coaches for check-in
  static Future<List<Map<String, dynamic>>> getEligibleCoaches() async {
    try {
      final checkinData = await _getCheckinData();
      final now = DateTime.now();
      final eligible = <Map<String, dynamic>>[];
      
      // Check if we're within the notification window
      if (!_isWithinNotificationWindow(now)) {
        return eligible;
      }
      
      // Check rate limiting
      if (await _isRateLimited()) {
        if (kDebugMode) print('🔔 Rate limited: max check-ins reached for 48h period');
        return eligible;
      }
      
      for (final entry in checkinData.entries) {
        final coachData = entry.value as Map<String, dynamic>;
        
        // Skip if disabled
        if (coachData['isEnabled'] != true) continue;
        
        // Check if eligible based on time
        final nextEligibleTimeStr = coachData['nextEligibleTime'] as String?;
        if (nextEligibleTimeStr != null) {
          final nextEligibleTime = DateTime.parse(nextEligibleTimeStr);
          if (now.isAfter(nextEligibleTime)) {
            eligible.add({
              'coachKey': entry.key,
              'category': coachData['category'],
              'coachName': coachData['coachName'],
              'lastMessageTime': coachData['lastMessageTime'],
              'lastCheckinTime': coachData['lastCheckinTime'],
            });
          }
        }
      }
      
      if (kDebugMode) {
        print('🔔 Found ${eligible.length} eligible coaches for check-in');
      }
      
      return eligible;
    } catch (e) {
      if (kDebugMode) print('❌ Failed to get eligible coaches: $e');
      return [];
    }
  }

  /// Select a random coach from eligible coaches
  static Map<String, dynamic>? selectRandomEligibleCoach(List<Map<String, dynamic>> eligible) {
    if (eligible.isEmpty) return null;
    
    final random = Random();
    return eligible[random.nextInt(eligible.length)];
  }

  /// Mark a coach as having sent a check-in
  static Future<void> markCheckinSent(String coachKey) async {
    try {
      final checkinData = await _getCheckinData();
      final now = DateTime.now();
      
      if (checkinData.containsKey(coachKey)) {
        checkinData[coachKey]['lastCheckinTime'] = now.toIso8601String();
        checkinData[coachKey]['totalCheckinsCount'] = 
            (checkinData[coachKey]['totalCheckinsCount'] ?? 0) + 1;
        
        // Set next eligible time for this coach
        final hoursUntilEligible = _minHoursSinceLastMessage + 
            Random().nextInt(_maxHoursSinceLastMessage - _minHoursSinceLastMessage);
        checkinData[coachKey]['nextEligibleTime'] = 
            now.add(Duration(hours: hoursUntilEligible)).toIso8601String();
        
        await _saveCheckinData(checkinData);
        
        // Add to history for rate limiting
        await _addToHistory(coachKey, now);
        
        if (kDebugMode) {
          print('🔔 Marked check-in sent for $coachKey');
        }
      }
    } catch (e) {
      if (kDebugMode) print('❌ Failed to mark check-in sent: $e');
    }
  }

  /// Toggle check-in enabled/disabled for a coach
  static Future<void> toggleCoachCheckins(String category, String coachName, bool enabled) async {
    try {
      final checkinData = await _getCheckinData();
      final coachKey = '${category}_$coachName';
      
      if (checkinData.containsKey(coachKey)) {
        checkinData[coachKey]['isEnabled'] = enabled;
        await _saveCheckinData(checkinData);
        
        if (kDebugMode) {
          print('🔔 ${enabled ? 'Enabled' : 'Disabled'} check-ins for $coachName ($category)');
        }
      }
    } catch (e) {
      if (kDebugMode) print('❌ Failed to toggle coach check-ins: $e');
    }
  }

  /// Get check-in enabled status for a coach
  static Future<bool> isCoachCheckinEnabled(String category, String coachName) async {
    try {
      final checkinData = await _getCheckinData();
      final coachKey = '${category}_$coachName';
      
      if (checkinData.containsKey(coachKey)) {
        return checkinData[coachKey]['isEnabled'] ?? true;
      }
      
      return true; // Default to enabled
    } catch (e) {
      if (kDebugMode) print('❌ Failed to get coach check-in status: $e');
      return true;
    }
  }

  /// Check if current time is within notification window (5:45 AM - 8:45 PM)
  static bool _isWithinNotificationWindow(DateTime time) {
    final hour = time.hour;
    final minute = time.minute;
    
    // Convert to minutes for easier comparison
    final currentMinutes = hour * 60 + minute;
    final startMinutes = _windowStartHour * 60 + _windowStartMinute;
    final endMinutes = _windowEndHour * 60 + _windowEndMinute;
    
    return currentMinutes >= startMinutes && currentMinutes <= endMinutes;
  }

  /// Check if rate limited (max 6 check-ins per 48 hours)
  static Future<bool> _isRateLimited() async {
    try {
      final history = await _getCheckinHistory();
      final now = DateTime.now();
      final cutoff = now.subtract(Duration(hours: _rateLimitHours));
      
      // Count check-ins in the last 48 hours
      int recentCheckinsCount = 0;
      for (final entry in history) {
        final timestamp = DateTime.parse(entry['timestamp']);
        if (timestamp.isAfter(cutoff)) {
          recentCheckinsCount++;
        }
      }
      
      return recentCheckinsCount >= _maxCheckinsPerTwoDays;
    } catch (e) {
      if (kDebugMode) print('❌ Failed to check rate limit: $e');
      return false;
    }
  }

  /// Get all coaches for a user
  static List<({String category, String name})> _getAllCoachesForUser(User user) {
    final coaches = <({String category, String name})>[];
    
    // Add core coaches
    for (final coach in mxdLifeCoaches.take(4)) { // First 4 are core coaches
      final coachName = user.gender.toLowerCase() == 'male' ? coach.maleName : coach.femaleName;
      coaches.add((category: coach.category, name: coachName));
    }
    
    // Add custom category coaches
    if (user.assignedCoaches != null) {
      for (final entry in user.assignedCoaches!.entries) {
        final category = entry.key;
        final assignedGender = entry.value;
        
        if (category.contains('Custom Category')) {
          final coach = mxdLifeCoaches.firstWhere(
            (c) => c.category == category,
            orElse: () => mxdLifeCoaches.last,
          );
          
          final coachName = assignedGender.toLowerCase() == 'male' ? coach.maleName : coach.femaleName;
          coaches.add((category: category, name: coachName));
        }
      }
    }
    
    return coaches;
  }

  /// Load check-in data from storage
  static Future<Map<String, dynamic>> _getCheckinData() async {
    try {
      final data = await _storage.read(key: _checkinDataKey);
      if (data != null) {
        return Map<String, dynamic>.from(json.decode(data));
      }
    } catch (e) {
      if (kDebugMode) print('❌ Failed to load check-in data: $e');
    }
    return {};
  }

  /// Save check-in data to storage
  static Future<void> _saveCheckinData(Map<String, dynamic> data) async {
    try {
      await _storage.write(key: _checkinDataKey, value: json.encode(data));
    } catch (e) {
      if (kDebugMode) print('❌ Failed to save check-in data: $e');
    }
  }

  /// Get check-in history
  static Future<List<Map<String, dynamic>>> _getCheckinHistory() async {
    try {
      final data = await _storage.read(key: _checkinHistoryKey);
      if (data != null) {
        return List<Map<String, dynamic>>.from(json.decode(data));
      }
    } catch (e) {
      if (kDebugMode) print('❌ Failed to load check-in history: $e');
    }
    return [];
  }

  /// Add check-in to history
  static Future<void> _addToHistory(String coachKey, DateTime timestamp) async {
    try {
      final history = await _getCheckinHistory();
      
      history.add({
        'coachKey': coachKey,
        'timestamp': timestamp.toIso8601String(),
      });
      
      // Keep only last 100 entries to prevent storage bloat
      if (history.length > 100) {
        history.removeRange(0, history.length - 100);
      }
      
      await _storage.write(key: _checkinHistoryKey, value: json.encode(history));
    } catch (e) {
      if (kDebugMode) print('❌ Failed to add to check-in history: $e');
    }
  }

  /// Clear all check-in data (useful for testing)
  static Future<void> clearAllData() async {
    try {
      await _storage.delete(key: _checkinDataKey);
      await _storage.delete(key: _checkinHistoryKey);
      await _storage.delete(key: _settingsKey);
      if (kDebugMode) print('🔔 Cleared all check-in data');
    } catch (e) {
      if (kDebugMode) print('❌ Failed to clear check-in data: $e');
    }
  }
}
