// lib/services/adaptive_response_system.dart

import 'dart:math';
import 'package:flutter/foundation.dart';
import '../models/user_model.dart';
import 'coach_memory_service.dart';

/// Adaptive response system for superintelligent AI coaches
/// 
/// Determines optimal response length and depth based on user needs,
/// message complexity, and context. Supports comprehensive 500-1000+ word
/// responses while adapting to user preferences.
class AdaptiveResponseSystem {
  
  /// Determine optimal response strategy for user message
  static Future<ResponseStrategy> determineResponseStrategy({
    required String userMessage,
    required String category,
    required User user,
    Map<String, dynamic>? conversationContext,
  }) async {
    try {
      // Analyze message characteristics
      final messageAnalysis = await _analyzeMessage(userMessage);
      
      // Get user preferences and patterns
      final userPreferences = await _getUserResponsePreferences(user.id);
      
      // Get conversation context
      final context = conversationContext ?? 
          await CoachMemoryService.getConversationContext(user.id, category);
      
      // Calculate optimal response parameters
      final strategy = await _calculateOptimalStrategy(
        messageAnalysis: messageAnalysis,
        userPreferences: userPreferences,
        context: context,
        category: category,
      );
      
      return strategy;
      
    } catch (e) {
      if (kDebugMode) print('❌ Failed to determine response strategy: $e');
      return ResponseStrategy.defaultStrategy();
    }
  }
  
  /// Analyze user message to determine response needs
  static Future<MessageAnalysis> _analyzeMessage(String message) async {
    final wordCount = message.split(' ').length;
    final sentenceCount = message.split('.').length;
    final questionCount = message.split('?').length;
    
    // Complexity analysis
    final complexity = _calculateComplexity(message);
    
    // Emotional analysis
    final emotionalState = _analyzeEmotionalState(message);
    
    // Topic analysis
    final topics = _extractTopics(message);
    
    // Intent analysis
    final intent = _analyzeIntent(message);
    
    return MessageAnalysis(
      wordCount: wordCount,
      sentenceCount: sentenceCount,
      questionCount: questionCount,
      complexity: complexity,
      emotionalState: emotionalState,
      topics: topics,
      intent: intent,
      requiresDeepThinking: _requiresDeepThinking(message, complexity),
      isEmergency: _isEmergencyMessage(message, emotionalState),
    );
  }
  
  /// Get user's response preferences from history
  static Future<UserResponsePreferences> _getUserResponsePreferences(String userId) async {
    try {
      // Get user communication patterns
      final patterns = await CoachMemoryService.analyzeUserCommunication(userId);
      
      return UserResponsePreferences(
        preferredLength: _inferPreferredLength(patterns),
        preferredDepth: _inferPreferredDepth(patterns),
        likesDetailedExplanations: _likesDetailedExplanations(patterns),
        prefersActionableAdvice: _prefersActionableAdvice(patterns),
        respondsToQuestions: _respondsToQuestions(patterns),
        needsEmotionalSupport: _needsEmotionalSupport(patterns),
        learningStyle: _identifyLearningStyle(patterns),
      );
      
    } catch (e) {
      if (kDebugMode) print('❌ Failed to get user preferences: $e');
      return UserResponsePreferences.defaultPreferences();
    }
  }
  
  /// Calculate optimal response strategy
  static Future<ResponseStrategy> _calculateOptimalStrategy({
    required MessageAnalysis messageAnalysis,
    required UserResponsePreferences userPreferences,
    required Map<String, dynamic> context,
    required String category,
  }) async {
    
    // Base response length calculation
    int targetWordCount = _calculateBaseWordCount(messageAnalysis, userPreferences);
    
    // Adjust for complexity
    targetWordCount = _adjustForComplexity(targetWordCount, messageAnalysis.complexity);
    
    // Adjust for emotional state
    targetWordCount = _adjustForEmotionalState(targetWordCount, messageAnalysis.emotionalState);
    
    // Adjust for user preferences
    targetWordCount = _adjustForUserPreferences(targetWordCount, userPreferences);
    
    // Ensure minimum comprehensive response for complex topics
    if (messageAnalysis.requiresDeepThinking) {
      targetWordCount = max(targetWordCount, 500); // Minimum 500 words for complex topics
    }
    
    // Cap at maximum for readability
    targetWordCount = min(targetWordCount, 1200); // Maximum 1200 words
    
    // Determine response structure
    final structure = _determineResponseStructure(
      messageAnalysis,
      userPreferences,
      targetWordCount,
    );
    
    // Determine thinking time
    final thinkingTime = _calculateThinkingTime(messageAnalysis, targetWordCount);
    
    return ResponseStrategy(
      targetWordCount: targetWordCount,
      responseType: _determineResponseType(messageAnalysis, userPreferences),
      structure: structure,
      includeQuestions: _shouldIncludeQuestions(messageAnalysis, userPreferences),
      includeActionItems: _shouldIncludeActionItems(messageAnalysis, userPreferences),
      emotionalTone: _determineEmotionalTone(messageAnalysis, userPreferences),
      thinkingTimeSeconds: thinkingTime,
      priority: _determinePriority(messageAnalysis),
    );
  }
  
  /// Calculate base word count based on message and preferences
  static int _calculateBaseWordCount(MessageAnalysis analysis, UserResponsePreferences preferences) {
    int baseCount = 300; // Default base
    
    // Adjust for message length
    if (analysis.wordCount > 50) baseCount += 200;
    if (analysis.wordCount > 100) baseCount += 300;
    
    // Adjust for questions
    baseCount += analysis.questionCount * 100;
    
    // Adjust for user preferences
    switch (preferences.preferredLength) {
      case ResponseLength.brief:
        baseCount = (baseCount * 0.7).round();
        break;
      case ResponseLength.detailed:
        baseCount = (baseCount * 1.5).round();
        break;
      case ResponseLength.comprehensive:
        baseCount = (baseCount * 2.0).round();
        break;
      default:
        break;
    }
    
    return baseCount;
  }
  
  /// Adjust word count for message complexity
  static int _adjustForComplexity(int baseCount, MessageComplexity complexity) {
    switch (complexity) {
      case MessageComplexity.low:
        return (baseCount * 0.8).round();
      case MessageComplexity.medium:
        return baseCount;
      case MessageComplexity.high:
        return (baseCount * 1.8).round();
      case MessageComplexity.veryHigh:
        return (baseCount * 2.5).round();
    }
  }
  
  /// Adjust word count for emotional state
  static int _adjustForEmotionalState(int baseCount, EmotionalState state) {
    switch (state) {
      case EmotionalState.distressed:
        return (baseCount * 1.4).round(); // More support needed
      case EmotionalState.confused:
        return (baseCount * 1.6).round(); // More explanation needed
      case EmotionalState.motivated:
        return (baseCount * 1.2).round(); // Capitalize on motivation
      case EmotionalState.neutral:
        return baseCount;
      case EmotionalState.excited:
        return (baseCount * 1.1).round();
    }
  }

  /// Adjust word count for user preferences
  static int _adjustForUserPreferences(int baseCount, UserResponsePreferences preferences) {
    switch (preferences.preferredLength) {
      case ResponseLength.brief:
        return (baseCount * 0.7).round();
      case ResponseLength.standard:
        return baseCount;
      case ResponseLength.detailed:
        return (baseCount * 1.3).round();
      case ResponseLength.comprehensive:
        return (baseCount * 1.8).round();
    }
  }
  
  /// Determine response structure based on analysis
  static ResponseStructure _determineResponseStructure(
    MessageAnalysis analysis,
    UserResponsePreferences preferences,
    int targetWordCount,
  ) {
    final sections = <ResponseSection>[];
    
    // Always start with acknowledgment
    sections.add(ResponseSection.acknowledgment);
    
    // Add analysis section for complex topics
    if (analysis.complexity.index >= MessageComplexity.medium.index) {
      sections.add(ResponseSection.analysis);
    }
    
    // Add insights section (core content)
    sections.add(ResponseSection.insights);
    
    // Add cross-domain connections for comprehensive responses
    if (targetWordCount >= 500) {
      sections.add(ResponseSection.crossDomainConnections);
    }
    
    // Add practical guidance
    if (preferences.prefersActionableAdvice) {
      sections.add(ResponseSection.practicalGuidance);
    }
    
    // Add strategic framework for complex topics
    if (analysis.requiresDeepThinking) {
      sections.add(ResponseSection.strategicFramework);
    }
    
    // Add questions for engagement
    if (preferences.respondsToQuestions) {
      sections.add(ResponseSection.probingQuestions);
    }
    
    // Add encouragement for emotional support
    if (analysis.emotionalState == EmotionalState.distressed || 
        preferences.needsEmotionalSupport) {
      sections.add(ResponseSection.encouragement);
    }
    
    return ResponseStructure(sections: sections);
  }
  
  /// Calculate thinking time based on complexity
  static int _calculateThinkingTime(MessageAnalysis analysis, int targetWordCount) {
    int baseTime = 5; // 5 seconds base
    
    // Add time for complexity
    baseTime += analysis.complexity.index * 8;
    
    // Add time for word count
    baseTime += (targetWordCount / 100).round() * 2;
    
    // Add time for deep thinking
    if (analysis.requiresDeepThinking) {
      baseTime += 15;
    }
    
    // No artificial caps - let coaches think as long as needed
    return baseTime;
  }
  
  // Helper methods for analysis
  static MessageComplexity _calculateComplexity(String message) {
    int complexityScore = 0;
    
    // Word count factor
    final wordCount = message.split(' ').length;
    if (wordCount > 20) complexityScore++;
    if (wordCount > 50) complexityScore++;
    if (wordCount > 100) complexityScore++;
    
    // Complex concepts
    final complexConcepts = ['strategy', 'philosophy', 'psychology', 'neuroscience', 'investment', 'relationship'];
    if (complexConcepts.any((concept) => message.toLowerCase().contains(concept))) {
      complexityScore += 2;
    }
    
    // Multiple questions
    if (message.split('?').length > 2) complexityScore++;
    
    // Emotional complexity
    if (_containsEmotionalComplexity(message)) complexityScore++;
    
    if (complexityScore >= 5) return MessageComplexity.veryHigh;
    if (complexityScore >= 3) return MessageComplexity.high;
    if (complexityScore >= 2) return MessageComplexity.medium;
    return MessageComplexity.low;
  }
  
  static EmotionalState _analyzeEmotionalState(String message) {
    final lowerMessage = message.toLowerCase();
    
    if (_containsDistressWords(lowerMessage)) return EmotionalState.distressed;
    if (_containsConfusionWords(lowerMessage)) return EmotionalState.confused;
    if (_containsMotivationWords(lowerMessage)) return EmotionalState.motivated;
    if (_containsExcitementWords(lowerMessage)) return EmotionalState.excited;
    
    return EmotionalState.neutral;
  }
  
  static List<String> _extractTopics(String message) {
    final topics = <String>[];
    final lowerMessage = message.toLowerCase();
    
    if (lowerMessage.contains('health') || lowerMessage.contains('fitness')) topics.add('health');
    if (lowerMessage.contains('money') || lowerMessage.contains('wealth')) topics.add('wealth');
    if (lowerMessage.contains('purpose') || lowerMessage.contains('meaning')) topics.add('purpose');
    if (lowerMessage.contains('relationship') || lowerMessage.contains('connection')) topics.add('connection');
    
    return topics;
  }
  
  static MessageIntent _analyzeIntent(String message) {
    final lowerMessage = message.toLowerCase();
    
    if (lowerMessage.contains('help') || lowerMessage.contains('advice')) return MessageIntent.seekingAdvice;
    if (lowerMessage.contains('how') || lowerMessage.contains('what')) return MessageIntent.seekingInformation;
    if (lowerMessage.contains('feel') || lowerMessage.contains('emotion')) return MessageIntent.seekingSupport;
    if (lowerMessage.contains('plan') || lowerMessage.contains('strategy')) return MessageIntent.seekingStrategy;
    
    return MessageIntent.general;
  }
  
  static bool _requiresDeepThinking(String message, MessageComplexity complexity) {
    return complexity.index >= MessageComplexity.high.index ||
           message.length > 200 ||
           message.split('?').length > 2;
  }
  
  static bool _isEmergencyMessage(String message, EmotionalState state) {
    return state == EmotionalState.distressed && 
           (message.toLowerCase().contains('crisis') || 
            message.toLowerCase().contains('emergency') ||
            message.toLowerCase().contains('urgent'));
  }
  
  // Placeholder methods for user preference inference
  static ResponseLength _inferPreferredLength(Map<String, dynamic> patterns) => ResponseLength.detailed;
  static ResponseDepth _inferPreferredDepth(Map<String, dynamic> patterns) => ResponseDepth.comprehensive;
  static bool _likesDetailedExplanations(Map<String, dynamic> patterns) => true;
  static bool _prefersActionableAdvice(Map<String, dynamic> patterns) => true;
  static bool _respondsToQuestions(Map<String, dynamic> patterns) => true;
  static bool _needsEmotionalSupport(Map<String, dynamic> patterns) => false;
  static LearningStyle _identifyLearningStyle(Map<String, dynamic> patterns) => LearningStyle.comprehensive;
  
  static ResponseType _determineResponseType(MessageAnalysis analysis, UserResponsePreferences preferences) => ResponseType.comprehensive;
  static bool _shouldIncludeQuestions(MessageAnalysis analysis, UserResponsePreferences preferences) => true;
  static bool _shouldIncludeActionItems(MessageAnalysis analysis, UserResponsePreferences preferences) => true;
  static EmotionalTone _determineEmotionalTone(MessageAnalysis analysis, UserResponsePreferences preferences) => EmotionalTone.supportive;
  static ResponsePriority _determinePriority(MessageAnalysis analysis) => ResponsePriority.normal;
  
  static bool _containsEmotionalComplexity(String message) => false;
  static bool _containsDistressWords(String message) => false;
  static bool _containsConfusionWords(String message) => false;
  static bool _containsMotivationWords(String message) => false;
  static bool _containsExcitementWords(String message) => false;
}

// Data classes for adaptive response system
class ResponseStrategy {
  final int targetWordCount;
  final ResponseType responseType;
  final ResponseStructure structure;
  final bool includeQuestions;
  final bool includeActionItems;
  final EmotionalTone emotionalTone;
  final int thinkingTimeSeconds;
  final ResponsePriority priority;
  
  ResponseStrategy({
    required this.targetWordCount,
    required this.responseType,
    required this.structure,
    required this.includeQuestions,
    required this.includeActionItems,
    required this.emotionalTone,
    required this.thinkingTimeSeconds,
    required this.priority,
  });
  
  factory ResponseStrategy.defaultStrategy() {
    return ResponseStrategy(
      targetWordCount: 400,
      responseType: ResponseType.balanced,
      structure: ResponseStructure(sections: [ResponseSection.insights]),
      includeQuestions: true,
      includeActionItems: true,
      emotionalTone: EmotionalTone.supportive,
      thinkingTimeSeconds: 10,
      priority: ResponsePriority.normal,
    );
  }
}

class MessageAnalysis {
  final int wordCount;
  final int sentenceCount;
  final int questionCount;
  final MessageComplexity complexity;
  final EmotionalState emotionalState;
  final List<String> topics;
  final MessageIntent intent;
  final bool requiresDeepThinking;
  final bool isEmergency;
  
  MessageAnalysis({
    required this.wordCount,
    required this.sentenceCount,
    required this.questionCount,
    required this.complexity,
    required this.emotionalState,
    required this.topics,
    required this.intent,
    required this.requiresDeepThinking,
    required this.isEmergency,
  });
}

class UserResponsePreferences {
  final ResponseLength preferredLength;
  final ResponseDepth preferredDepth;
  final bool likesDetailedExplanations;
  final bool prefersActionableAdvice;
  final bool respondsToQuestions;
  final bool needsEmotionalSupport;
  final LearningStyle learningStyle;
  
  UserResponsePreferences({
    required this.preferredLength,
    required this.preferredDepth,
    required this.likesDetailedExplanations,
    required this.prefersActionableAdvice,
    required this.respondsToQuestions,
    required this.needsEmotionalSupport,
    required this.learningStyle,
  });
  
  factory UserResponsePreferences.defaultPreferences() {
    return UserResponsePreferences(
      preferredLength: ResponseLength.detailed,
      preferredDepth: ResponseDepth.comprehensive,
      likesDetailedExplanations: true,
      prefersActionableAdvice: true,
      respondsToQuestions: true,
      needsEmotionalSupport: false,
      learningStyle: LearningStyle.comprehensive,
    );
  }
}

class ResponseStructure {
  final List<ResponseSection> sections;
  
  ResponseStructure({required this.sections});
}

// Enums for response system
enum MessageComplexity { low, medium, high, veryHigh }
enum EmotionalState { neutral, excited, motivated, confused, distressed }
enum MessageIntent { general, seekingAdvice, seekingInformation, seekingSupport, seekingStrategy }
enum ResponseLength { brief, standard, detailed, comprehensive }
enum ResponseDepth { surface, moderate, deep, comprehensive }
enum LearningStyle { visual, auditory, kinesthetic, comprehensive }
enum ResponseType { brief, balanced, detailed, comprehensive }
enum EmotionalTone { neutral, supportive, motivational, empathetic }
enum ResponsePriority { low, normal, high, urgent }
enum ResponseSection { 
  acknowledgment, 
  analysis, 
  insights, 
  crossDomainConnections, 
  practicalGuidance, 
  strategicFramework, 
  probingQuestions, 
  encouragement 
}
