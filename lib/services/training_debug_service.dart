// lib/services/training_debug_service.dart

import 'dart:convert';
import 'package:flutter/foundation.dart';
import '../models/training_session_model.dart';

import '../services/training_timer_service.dart';
import '../services/training_storage_service.dart';
import '../services/comprehensive_logging_service.dart';
import '../services/release_config_service.dart';

/// Debug service for Training Tracker system with comprehensive monitoring
class TrainingDebugService {
  static final TrainingDebugService _instance = TrainingDebugService._internal();
  factory TrainingDebugService() => _instance;
  
  TrainingDebugService._internal();

  static final Map<String, dynamic> _debugMetrics = {};
  static final List<String> _debugLogs = [];
  static DateTime? _lastHealthCheck;

  /// Initialize debug monitoring
  static Future<void> initialize() async {
    if (!ReleaseConfigService.shouldShowDebugOverlays) return;
    
    try {
      await ComprehensiveLoggingService.logInfo('🔧 TrainingDebug: Initializing debug monitoring');
      
      _debugMetrics.clear();
      _debugLogs.clear();
      _lastHealthCheck = DateTime.now();
      
      // Start periodic health checks
      _startPeriodicHealthChecks();
      
      await ComprehensiveLoggingService.logInfo('✅ TrainingDebug: Debug monitoring initialized');
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ TrainingDebug: Failed to initialize: $e');
    }
  }

  /// Start periodic health checks
  static void _startPeriodicHealthChecks() {
    if (!ReleaseConfigService.shouldShowDebugOverlays) return;

    // Run health check every 2 minutes to reduce log noise
    Future.delayed(const Duration(minutes: 2), () async {
      await performHealthCheck();
      _startPeriodicHealthChecks(); // Schedule next check
    });
  }

  /// Perform comprehensive health check
  static Future<Map<String, dynamic>> performHealthCheck() async {
    if (!ReleaseConfigService.shouldShowDebugOverlays) {
      return {'status': 'disabled', 'message': 'Debug mode disabled'};
    }

    // Throttle health checks to prevent excessive calls (max once per 10 seconds)
    final now = DateTime.now();
    if (_lastHealthCheck != null && now.difference(_lastHealthCheck!).inSeconds < 10) {
      return _debugMetrics['lastHealthCheck'] ?? {'status': 'throttled', 'message': 'Health check throttled'};
    }

    try {
      await ComprehensiveLoggingService.logInfo('🔍 TrainingDebug: Performing health check');
      
      final healthReport = <String, dynamic>{
        'timestamp': DateTime.now().toIso8601String(),
        'timerService': await _checkTimerService(),
        'storageService': await _checkStorageService(),
        'dataIntegrity': await _checkDataIntegrity(),
        'performance': await _checkPerformance(),
        'memoryUsage': await _checkMemoryUsage(),
      };
      
      _debugMetrics['lastHealthCheck'] = healthReport;
      _lastHealthCheck = DateTime.now();
      
      // Log any critical issues
      final criticalIssues = _extractCriticalIssues(healthReport);
      if (criticalIssues.isNotEmpty) {
        await ComprehensiveLoggingService.logError('🚨 TrainingDebug: Critical issues found: $criticalIssues');
      }
      
      // Only log completion in verbose debug mode to reduce noise
      if (ReleaseConfigService.shouldShowDebugOverlays) {
        await ComprehensiveLoggingService.logDebug('✅ TrainingDebug: Health check completed');
      }
      return healthReport;
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ TrainingDebug: Health check failed: $e');
      return {'status': 'error', 'error': e.toString()};
    }
  }

  /// Check timer service health
  static Future<Map<String, dynamic>> _checkTimerService() async {
    try {
      final timerService = TrainingTimerService();
      final stats = timerService.getTimerStats();
      final isValid = timerService.validateTimerState();
      
      return {
        'status': isValid ? 'healthy' : 'warning',
        'isRunning': stats['isRunning'],
        'elapsedSeconds': stats['elapsedSeconds'],
        'currentExp': stats['currentExp'],
        'stateValid': isValid,
        'canStart': stats['canStart'],
        'canPause': stats['canPause'],
        'canReset': stats['canReset'],
      };
    } catch (e) {
      return {'status': 'error', 'error': e.toString()};
    }
  }

  /// Check storage service health
  static Future<Map<String, dynamic>> _checkStorageService() async {
    try {
      final storageService = TrainingStorageService();
      final stats = await storageService.getStorageStats();
      
      return {
        'status': 'healthy',
        'totalSessions': stats['totalSessions'],
        'completedSessions': stats['completedSessions'],
        'totalTrainingTime': stats['totalTrainingTime'],
        'totalExpEarned': stats['totalExpEarned'],
        'programType': stats['programType'],
        'currentLabel': stats['currentLabel'],
        'hasCurrentSession': stats['hasCurrentSession'],
        'lastSessionDate': stats['lastSessionDate'],
      };
    } catch (e) {
      return {'status': 'error', 'error': e.toString()};
    }
  }

  /// Check data integrity
  static Future<Map<String, dynamic>> _checkDataIntegrity() async {
    try {
      final storageService = TrainingStorageService();
      final sessions = await storageService.getAllSessions();
      
      int corruptedSessions = 0;
      int invalidExpCalculations = 0;
      int missingData = 0;
      
      for (final session in sessions) {
        // Check for corrupted data
        if (session.id.isEmpty || session.label.isEmpty) {
          corruptedSessions++;
        }
        
        // Validate EXP calculation (handle both int and double values)
        final expectedExp = TrainingSession.calculateExp(session.durationSeconds);
        final actualExp = session.expEarned;
        // Allow small tolerance for floating point comparison
        if ((actualExp - expectedExp).abs() > 0.1) {
          invalidExpCalculations++;
        }
        
        // Check for missing critical data
        if (session.createdAt.year < 2020 || session.durationSeconds < 0) {
          missingData++;
        }
      }
      
      final status = (corruptedSessions + invalidExpCalculations + missingData) == 0 
          ? 'healthy' 
          : 'warning';
      
      return {
        'status': status,
        'totalSessions': sessions.length,
        'corruptedSessions': corruptedSessions,
        'invalidExpCalculations': invalidExpCalculations,
        'missingData': missingData,
        'integrityScore': sessions.isEmpty ? 100.0 : 
            ((sessions.length - corruptedSessions - invalidExpCalculations - missingData) / sessions.length * 100),
      };
    } catch (e) {
      return {'status': 'error', 'error': e.toString()};
    }
  }

  /// Check performance metrics
  static Future<Map<String, dynamic>> _checkPerformance() async {
    try {
      final startTime = DateTime.now();
      
      // Test storage read performance
      final storageService = TrainingStorageService();
      final storageStart = DateTime.now();
      await storageService.getAllSessions();
      final storageTime = DateTime.now().difference(storageStart).inMilliseconds;
      
      // Test timer service performance
      final timerService = TrainingTimerService();
      final timerStart = DateTime.now();
      timerService.getTimerStats();
      final timerTime = DateTime.now().difference(timerStart).inMilliseconds;
      
      final totalTime = DateTime.now().difference(startTime).inMilliseconds;
      
      return {
        'status': totalTime < 5000 ? 'healthy' : 'warning', // More reasonable 5s threshold
        'totalResponseTime': totalTime,
        'storageReadTime': storageTime,
        'timerServiceTime': timerTime,
        'performanceScore': totalTime < 100 ? 100 : (1000 / totalTime * 100).clamp(0, 100),
      };
    } catch (e) {
      return {'status': 'error', 'error': e.toString()};
    }
  }

  /// Check memory usage (simplified)
  static Future<Map<String, dynamic>> _checkMemoryUsage() async {
    try {
      // Get approximate memory usage
      final debugMetricsSize = jsonEncode(_debugMetrics).length;
      final debugLogsSize = _debugLogs.join().length;
      
      return {
        'status': 'healthy',
        'debugMetricsSize': debugMetricsSize,
        'debugLogsSize': debugLogsSize,
        'totalDebugMemory': debugMetricsSize + debugLogsSize,
        'memoryScore': 100, // Simplified - would need platform-specific implementation for real memory usage
      };
    } catch (e) {
      return {'status': 'error', 'error': e.toString()};
    }
  }

  /// Extract critical issues from health report
  static List<String> _extractCriticalIssues(Map<String, dynamic> healthReport) {
    final issues = <String>[];

    healthReport.forEach((key, value) {
      if (value is Map<String, dynamic>) {
        final status = value['status'] as String?;
        if (status == 'error') {
          issues.add('$key: ${value['error']}');
        } else if (status == 'warning') {
          // Provide more specific warning details
          final responseTime = value['totalResponseTime'];
          final performanceScore = value['performanceScore'];
          final corruptedSessions = value['corruptedSessions'];
          final invalidExpCalculations = value['invalidExpCalculations'];
          final missingData = value['missingData'];

          if (responseTime != null) {
            issues.add('$key: Slow response (${responseTime}ms, score: ${performanceScore?.toStringAsFixed(1) ?? 'N/A'})');
          } else if (corruptedSessions != null || invalidExpCalculations != null || missingData != null) {
            final details = <String>[];
            if (corruptedSessions > 0) details.add('$corruptedSessions corrupted');
            if (invalidExpCalculations > 0) details.add('$invalidExpCalculations invalid EXP');
            if (missingData > 0) details.add('$missingData missing data');
            issues.add('$key: ${details.join(', ')}');
          } else {
            issues.add('$key: Warning detected');
          }
        }
      }
    });

    return issues;
  }

  /// Log debug event
  static Future<void> logDebugEvent(String event, Map<String, dynamic>? data) async {
    if (!ReleaseConfigService.shouldShowDebugOverlays) return;
    
    try {
      final timestamp = DateTime.now().toIso8601String();
      final logEntry = '$timestamp: $event${data != null ? ' - ${jsonEncode(data)}' : ''}';
      
      _debugLogs.add(logEntry);
      
      // Keep only last 100 debug logs
      if (_debugLogs.length > 100) {
        _debugLogs.removeAt(0);
      }
      
      await ComprehensiveLoggingService.logInfo('🔧 TrainingDebug: $event');
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ TrainingDebug: Failed to log event: $e');
    }
  }

  /// Get debug dashboard data
  static Map<String, dynamic> getDebugDashboard() {
    if (!ReleaseConfigService.shouldShowDebugOverlays) {
      return {'status': 'disabled'};
    }
    
    return {
      'status': 'active',
      'lastHealthCheck': _lastHealthCheck?.toIso8601String(),
      'debugMetrics': _debugMetrics,
      'recentLogs': _debugLogs.take(20).toList(),
      'totalLogs': _debugLogs.length,
      'systemInfo': {
        'isDebugMode': kDebugMode,
        'platform': defaultTargetPlatform.name,
        'timestamp': DateTime.now().toIso8601String(),
      },
    };
  }

  /// Clear debug data
  static Future<void> clearDebugData() async {
    if (!ReleaseConfigService.shouldShowDebugOverlays) return;
    
    try {
      _debugMetrics.clear();
      _debugLogs.clear();
      _lastHealthCheck = null;
      
      await ComprehensiveLoggingService.logInfo('🧹 TrainingDebug: Debug data cleared');
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ TrainingDebug: Failed to clear debug data: $e');
    }
  }

  /// Validate training session data
  static Future<Map<String, dynamic>> validateSession(TrainingSession session) async {
    try {
      final issues = <String>[];
      
      // Validate basic data
      if (session.id.isEmpty) issues.add('Missing session ID');
      if (session.label.isEmpty) issues.add('Missing session label');
      if (session.durationSeconds < 0) issues.add('Invalid duration');
      if (session.expEarned < 0) issues.add('Invalid EXP amount');
      
      // Validate EXP calculation
      final expectedExp = TrainingSession.calculateExp(session.durationSeconds);
      if (session.expEarned != expectedExp) {
        issues.add('EXP calculation mismatch: expected $expectedExp, got ${session.expEarned}');
      }
      
      // Validate timestamps
      if (session.createdAt.isAfter(DateTime.now())) {
        issues.add('Future creation date');
      }
      
      if (session.completedAt != null && session.completedAt!.isBefore(session.createdAt)) {
        issues.add('Completion date before creation date');
      }
      
      return {
        'isValid': issues.isEmpty,
        'issues': issues,
        'sessionId': session.id,
        'validationScore': issues.isEmpty ? 100 : ((5 - issues.length) / 5 * 100).clamp(0, 100),
      };
    } catch (e) {
      return {
        'isValid': false,
        'issues': ['Validation error: $e'],
        'sessionId': session.id,
        'validationScore': 0,
      };
    }
  }
}
