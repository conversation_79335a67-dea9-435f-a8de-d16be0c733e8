// 📁 lib/theme/colors.dart

import 'package:flutter/material.dart';

/// A collection of static neon color constants.
class MolColors {
  static const black   = Color(0xFF0D0D0D);
  static const blue    = Color(0xFF00BFFF);
  static const purple  = Color(0xFF9C27B0);
  static const green   = Color(0xFF00FFAA);
  static const red     = Color(0xFFFF1744);
  static const yellow  = Color(0xFFFFD600);
  static const orange  = Color(0xFFFFA500);
  static const pink    = Color(0xFFE91E63);
  static const cyan    = Color(0xFF00BCD4);
  static const lime    = Color(0xFF8BC34A);
  static const magenta = Color.fromARGB(255, 234, 0, 255);
  static const gray    = Color(0xFF666666);
}

/// Internal map of your four built‐in categories → neon colors.
/// If you ever rename your built‐in labels, update this map accordingly.
const Map<String, Color> _builtInCategoryColors = {
  'Health'    : MolColors.blue,   // Neon blue
  'Wealth'    : Color.fromARGB(255, 0, 255, 26),  // Neon green
  'Purpose'   : MolColors.purple, // Neon purple
  'Connection': MolColors.yellow, // Neon gold
};

/// A list of preset neon colors used to assign to whichever custom categories
/// the user defines. The first custom category gets _presetCustomColors[0] (red),
/// the second gets _presetCustomColors[1] (orange), the third pink, etc. Once you
/// run out of colors, you cycle back (index % length).
const List<Color> _presetCustomColors = [
  MolColors.red,
  MolColors.orange,
  MolColors.pink,
  MolColors.cyan,
  MolColors.lime,
];

/// Returns the neon accent color for any category (built‐in or custom).
///
/// - If [category] matches one of your four built‐ins (Health, Wealth, Purpose,
///   Connection), returns its entry from `_builtInCategoryColors`.  
/// - Otherwise, if [customCategories] is provided and [category] matches one of
///   those user‐defined labels, this will return `_presetCustomColors[index]`,
///   where `index = customCategories.indexOf(category)`.  
///   • E.g. if `customCategories = ['Combatant','Languages']`, then  
///        • `getCategoryColor('Combatant', customCategories: customCategories)` → `MolColors.red`  
///        • `getCategoryColor('Languages', customCategories: customCategories)` → `MolColors.orange`  
///   • If the user ever adds a third custom category (e.g. `customCategories[2]`),
///     it would be colored `MolColors.pink`, and so on (cycling through the list).  
/// - If no match is found in either built‐ins or the provided custom list, falls
///   back to `MolColors.black`.
///
/// **Usage** (e.g., in a widget):
/// ```dart
/// final customCats = user.customCategories; // e.g. ['Combatant','Languages']
/// final myColor = getCategoryColor('Combatant', customCategories: customCats);
/// ```
Color getCategoryColor(
  String category, {
  List<String>? customCategories,
}) {
  // 1) Check built‐in map first:
  final builtIn = _builtInCategoryColors[category];
  if (builtIn != null) {
    return builtIn;
  }

  // 2) If provided, check custom categories:
  if (customCategories != null) {
    final idx = customCategories.indexOf(category);
    if (idx >= 0) {
      // Cycle through preset colors if idx exceeds length
      return _presetCustomColors[idx % _presetCustomColors.length];
    }
  }

  // 3) Default fallback:
  return MolColors.black;
}
