import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../design_system/design_tokens.dart';
import '../design_system/typography.dart';
import 'colors.dart';

/// Theme provider for the Maxed Out Life app.
///
/// Manages light and dark themes with consistent styling across the app.
/// Uses Material 3 design system with custom fonts and colors.
/// Integrates with the comprehensive design system for consistency.
class ThemeProvider extends ChangeNotifier {
  static const String _themeKey = 'theme_mode';
  bool _isDarkMode = false;
  late SharedPreferences _prefs;

  ThemeProvider() {
    _loadTheme();
  }

  bool get isDarkMode => _isDarkMode;
  ThemeData get lightTheme => _lightTheme;
  ThemeData get darkTheme => _darkTheme;
  ThemeData get themeData => _isDarkMode ? _darkTheme : _lightTheme;

  Future<void> _loadTheme() async {
    _prefs = await SharedPreferences.getInstance();
    _isDarkMode = _prefs.getBool(_themeKey) ?? false;
    notifyListeners();
  }

  Future<void> toggleTheme() async {
    _isDarkMode = !_isDarkMode;
    await _prefs.setBool(_themeKey, _isDarkMode);
    notifyListeners();
  }

  static final ThemeData _lightTheme = ThemeData(
    useMaterial3: true,
    brightness: Brightness.light,
    colorScheme: ColorScheme.fromSeed(
      seedColor: MolColors.cyan,
      brightness: Brightness.light,
    ),
    fontFamily: AppTypography.fontFamilyPrimary,
    scaffoldBackgroundColor: Colors.white,
    textTheme: ThemeData.light().textTheme.apply(
      fontFamily: AppTypography.fontFamilyPrimary,
      bodyColor: Colors.black,
      displayColor: Colors.black,
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
        textStyle: AppTypography.buttonMedium,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(DesignTokens.radius2xl),
        ),
        elevation: DesignTokens.elevationMd,
      ),
    ),
    cardTheme: CardThemeData(
      color: Colors.grey[100],
      elevation: DesignTokens.elevationSm,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(DesignTokens.radiusXl),
      ),
    ),
  );

  static final ThemeData _darkTheme = ThemeData(
    useMaterial3: true,
    brightness: Brightness.dark,
    colorScheme: ColorScheme.fromSeed(
      seedColor: MolColors.cyan,
      brightness: Brightness.dark,
    ),
    fontFamily: AppTypography.fontFamilyPrimary,
    scaffoldBackgroundColor: Colors.black,
    textTheme: ThemeData.dark().textTheme.apply(
      fontFamily: AppTypography.fontFamilyPrimary,
      bodyColor: Colors.white,
      displayColor: Colors.white,
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: MolColors.cyan,
        foregroundColor: Colors.black,
        textStyle: AppTypography.buttonMedium,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(DesignTokens.radius2xl),
        ),
        elevation: DesignTokens.elevationMd,
      ),
    ),
    cardTheme: CardThemeData(
      color: Colors.grey[900],
      elevation: DesignTokens.elevationSm,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(DesignTokens.radiusXl),
      ),
    ),
  );
} 