// 📁 lib/data/user_storage.dart

import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user_model.dart';
import '../bulletproof/error_handler.dart';
import '../performance/optimized_storage.dart';
import '../performance/performance_monitor.dart';

class UserStorage {
  static const _userPrefix = 'mol_user_';
  static const _userListKey = 'mol_user_ids';
  static const _activeUserKey = 'mol_active_user';
  static final _errorHandler = ErrorHandler();
  static final OptimizedStorage _storage = OptimizedStorage();
  static final PerformanceMonitor _monitor = PerformanceMonitor();

  // Initialize optimized storage
  static Future<void> initialize() async {
    await _storage.initialize();
  }

  /// Save user data under unique ID with optimized storage
  static Future<void> saveUser(User user) async {
    try {
      final stopwatch = _monitor.startTimer('save_user');

      final userKey = '$_userPrefix${user.id}';
      final userJson = json.encode(user.toJson());

      // Use optimized storage with caching and compression for large data
      await _storage.setString(
        userKey,
        userJson,
        useCache: true,
        useCompression: userJson.length > 1024,
      );

      // Maintain list of IDs using batch operations
      final ids = await _storage.getString(_userListKey) ?? '[]';
      final idsList = List<String>.from(json.decode(ids));

      if (!idsList.contains(user.id)) {
        idsList.add(user.id);
        await _storage.batchWrite({
          _userListKey: json.encode(idsList),
          _activeUserKey: user.id,
        });
      } else {
        await _storage.setString(_activeUserKey, user.id, useCache: true);
      }

      _monitor.endTimer(stopwatch, 'save_user');
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'Saving user');
    }
  }

  /// Load user by ID with optimized caching
  static Future<User?> loadUser(String id) async {
    try {
      final stopwatch = _monitor.startTimer('load_user');

      final userKey = '$_userPrefix$id';
      final raw = await _storage.getString(userKey, useCache: true);

      if (raw == null) {
        _monitor.endTimer(stopwatch, 'load_user');
        return null;
      }

      final user = User.fromJson(json.decode(raw));
      _monitor.endTimer(stopwatch, 'load_user');
      return user;
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'Loading user');
      return null;
    }
  }
  
  /// Loads the most recently active user from shared preferences
  static Future<User?> loadActiveUser() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final activeId = prefs.getString(_activeUserKey);
      if (activeId == null) return null;
      return await loadUser(activeId);
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'Loading active user');
      return null;
    }
  }

  /// Delete user by ID
  static Future<void> deleteUser(String id) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('$_userPrefix$id');

      final ids = prefs.getStringList(_userListKey) ?? [];
      ids.remove(id);
      await prefs.setStringList(_userListKey, ids);

      final activeId = prefs.getString(_activeUserKey);
      if (activeId == id) await prefs.remove(_activeUserKey);
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'Deleting user');
    }
  }

  /// Load all saved user IDs
  static Future<List<String>> getUserIds() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getStringList(_userListKey) ?? [];
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'Getting user IDs');
      return [];
    }
  }

  /// Get currently active user
  static Future<User?> getActiveUser() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final id = prefs.getString(_activeUserKey);
      if (id == null) return null;
      return loadUser(id);
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'Getting active user');
      return null;
    }
  }

  /// Set currently active user
  static Future<void> setActiveUser(String id) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_activeUserKey, id);
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'Setting active user');
    }
  }

  /// Clear all stored users
  static Future<void> wipeAllUsers() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final ids = prefs.getStringList(_userListKey) ?? [];

      for (final id in ids) {
        await prefs.remove('$_userPrefix$id');
      }

      await prefs.remove(_userListKey);
      await prefs.remove(_activeUserKey);
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'Wiping all users');
    }
  }
}
