import '../models/reward_model.dart';

/// All built-in and custom categories.
const List<String> allCategories = [
  'Health',
  'Wealth',
  'Purpose',
  'Connection',
  'Custom1',
  'Custom2',
];

/// Quest difficulty levels with XP ranges
enum QuestDifficulty {
  easy(5, 15),      // 5-15 XP
  medium(15, 35),   // 15-35 XP  
  hard(35, 60),     // 35-60 XP
  epic(60, 100);    // 60-100 XP

  const QuestDifficulty(this.minXp, this.maxXp);
  final int minXp;
  final int maxXp;
}

/// Quest categories for better organization
enum QuestCategory {
  health,
  wealth,
  purpose,
  connection,
  //mindfulness,
  //productivity,
}

/// Enhanced quest model with more metadata
class EnhancedQuest {
  final String id;
  final String description;
  final int rewardXp;
  final QuestDifficulty difficulty;
  final QuestCategory category;
  final List<String> tags;
  final String? specialRequirement;
  final bool requiresPhotoProof;

  const EnhancedQuest({
    required this.id,
    required this.description,
    required this.rewardXp,
    required this.difficulty,
    required this.category,
    this.tags = const [],
    this.specialRequirement,
    this.requiresPhotoProof = false,
  });
}

/// 365 diverse quests for the Bounty Hunter system
const List<EnhancedQuest> allEnhancedQuests = [
  // HEALTH QUESTS (60 quests)
  EnhancedQuest(
    id: 'h001',
    description: 'Go to a new fighting gym and train for two hours',
    rewardXp: 40,
    difficulty: QuestDifficulty.hard,
    category: QuestCategory.health,
    tags: ['gym', 'fighting', 'strength'],
    requiresPhotoProof: true,
  ),
  EnhancedQuest(
    id: 'h002', 
    description: 'Cook a healthy meal from scratch',
    rewardXp: 20,
    difficulty: QuestDifficulty.medium,
    category: QuestCategory.health,
    tags: ['cooking', 'nutrition'],
    requiresPhotoProof: true,
  ),
  EnhancedQuest(
    id: 'h003',
    description: 'Run 5km at a steady pace',
    rewardXp: 30,
    difficulty: QuestDifficulty.medium,
    category: QuestCategory.health,
    tags: ['running', 'cardio'],
    requiresPhotoProof: true,
  ),
  EnhancedQuest(
    id: 'h004',
    description: 'Meditate for 30 minutes',
    rewardXp: 15,
    difficulty: QuestDifficulty.easy,
    category: QuestCategory.connection,
    tags: ['meditation', 'mental-health'],
  ),
  EnhancedQuest(
    id: 'h005',
    description: 'Complete a full body workout',
    rewardXp: 25,
    difficulty: QuestDifficulty.medium,
    category: QuestCategory.health,
    tags: ['workout', 'strength'],
    requiresPhotoProof: true,
  ),
  EnhancedQuest(
    id: 'h006',
    description: 'Drink 8 glasses of water today',
    rewardXp: 10,
    difficulty: QuestDifficulty.easy,
    category: QuestCategory.health,
    tags: ['hydration', 'daily'],
  ),
  EnhancedQuest(
    id: 'h007',
    description: 'Take a cold shower for 3 minutes',
    rewardXp: 20,
    difficulty: QuestDifficulty.medium,
    category: QuestCategory.health,
    tags: ['cold-exposure', 'discipline'],
  ),
  EnhancedQuest(
    id: 'h008',
    description: 'Go for a 1-hour walk in nature',
    rewardXp: 15,
    difficulty: QuestDifficulty.easy,
    category: QuestCategory.health,
    tags: ['walking', 'nature', 'outdoors'],
    requiresPhotoProof: true,
  ),
  EnhancedQuest(
    id: 'h009',
    description: 'Do 100 push-ups throughout the day',
    rewardXp: 25,
    difficulty: QuestDifficulty.medium,
    category: QuestCategory.health,
    tags: ['push-ups', 'strength'],
  ),
  EnhancedQuest(
    id: 'h010',
    description: 'Get 8 hours of quality sleep',
    rewardXp: 15,
    difficulty: QuestDifficulty.easy,
    category: QuestCategory.health,
    tags: ['sleep', 'recovery'],
  ),

  // WEALTH QUESTS (60 quests)
  EnhancedQuest(
    id: 'w001',
    description: 'Create a detailed budget for the month',
    rewardXp: 25,
    difficulty: QuestDifficulty.medium,
    category: QuestCategory.wealth,
    tags: ['budgeting', 'planning'],
  ),
  EnhancedQuest(
    id: 'w002',
    description: 'Invest \$100 in a new asset class',
    rewardXp: 40,
    difficulty: QuestDifficulty.hard,
    category: QuestCategory.wealth,
    tags: ['investing', 'finance'],
  ),
  EnhancedQuest(
    id: 'w003',
    description: 'Read a personal finance book for 1 hour',
    rewardXp: 20,
    difficulty: QuestDifficulty.medium,
    category: QuestCategory.purpose,
    tags: ['reading', 'finance', 'education'],
  ),
  EnhancedQuest(
    id: 'w004',
    description: 'Negotiate a better deal on a service',
    rewardXp: 30,
    difficulty: QuestDifficulty.hard,
    category: QuestCategory.wealth,
    tags: ['negotiation', 'savings'],
  ),
  EnhancedQuest(
    id: 'w005',
    description: 'Start a side hustle or freelance project',
    rewardXp: 50,
    difficulty: QuestDifficulty.hard,
    category: QuestCategory.wealth,
    tags: ['entrepreneurship', 'income'],
    requiresPhotoProof: true,
  ),
  EnhancedQuest(
    id: 'w006',
    description: 'Track all expenses for a week',
    rewardXp: 20,
    difficulty: QuestDifficulty.medium,
    category: QuestCategory.wealth,
    tags: ['tracking', 'awareness'],
  ),
  EnhancedQuest(
    id: 'w007',
    description: 'Learn about cryptocurrency for 2 hours',
    rewardXp: 25,
    difficulty: QuestDifficulty.medium,
    category: QuestCategory.purpose,
    tags: ['crypto', 'education'],
  ),
  EnhancedQuest(
    id: 'w008',
    description: 'Sell something you no longer need',
    rewardXp: 15,
    difficulty: QuestDifficulty.easy,
    category: QuestCategory.wealth,
    tags: ['decluttering', 'income'],
  ),
  EnhancedQuest(
    id: 'w009',
    description: 'Research and compare insurance options',
    rewardXp: 20,
    difficulty: QuestDifficulty.medium,
    category: QuestCategory.wealth,
    tags: ['insurance', 'protection'],
  ),
  EnhancedQuest(
    id: 'w010',
    description: 'Set up an emergency fund',
    rewardXp: 30,
    difficulty: QuestDifficulty.medium,
    category: QuestCategory.wealth,
    tags: ['savings', 'security'],
  ),

  // PURPOSE QUESTS (60 quests)
  EnhancedQuest(
    id: 'p001',
    description: 'Write your life mission statement',
    rewardXp: 40,
    difficulty: QuestDifficulty.hard,
    category: QuestCategory.purpose,
    tags: ['reflection', 'clarity'],
  ),
  EnhancedQuest(
    id: 'p002',
    description: 'Volunteer for a cause you care about',
    rewardXp: 35,
    difficulty: QuestDifficulty.medium,
    category: QuestCategory.purpose,
    tags: ['volunteering', 'giving-back'],
    requiresPhotoProof: true,
  ),
  EnhancedQuest(
    id: 'p003',
    description: 'Learn a new skill that aligns with your goals',
    rewardXp: 30,
    difficulty: QuestDifficulty.medium,
    category: QuestCategory.purpose,
    tags: ['learning', 'growth'],
  ),
  EnhancedQuest(
    id: 'p004',
    description: 'Create a 5-year vision board',
    rewardXp: 25,
    difficulty: QuestDifficulty.medium,
    category: QuestCategory.purpose,
    tags: ['vision', 'planning'],
    requiresPhotoProof: true,
  ),
  EnhancedQuest(
    id: 'p005',
    description: 'Mentor someone in your field',
    rewardXp: 45,
    difficulty: QuestDifficulty.hard,
    category: QuestCategory.purpose,
    tags: ['mentoring', 'leadership'],
  ),
  EnhancedQuest(
    id: 'p006',
    description: 'Write down your core values',
    rewardXp: 20,
    difficulty: QuestDifficulty.easy,
    category: QuestCategory.purpose,
    tags: ['values', 'self-awareness'],
  ),
  EnhancedQuest(
    id: 'p007',
    description: 'Start a passion project',
    rewardXp: 40,
    difficulty: QuestDifficulty.hard,
    category: QuestCategory.connection,
    tags: ['passion', 'creation'],
    requiresPhotoProof: true,
  ),
  EnhancedQuest(
    id: 'p008',
    description: 'Read a biography of someone you admire',
    rewardXp: 25,
    difficulty: QuestDifficulty.medium,
    category: QuestCategory.connection,
    tags: ['reading', 'inspiration'],
  ),
  EnhancedQuest(
    id: 'p009',
    description: 'Set 3 major goals for the next 6 months',
    rewardXp: 20,
    difficulty: QuestDifficulty.medium,
    category: QuestCategory.purpose,
    tags: ['goal-setting', 'planning'],
  ),
  EnhancedQuest(
    id: 'p010',
    description: 'Create a daily routine that supports your purpose',
    rewardXp: 30,
    difficulty: QuestDifficulty.medium,
    category: QuestCategory.purpose,
    tags: ['routine', 'discipline'],
  ),

  // CONNECTION QUESTS (60 quests)
  EnhancedQuest(
    id: 'c001',
    description: 'Call an old friend you haven\'t spoken to in months',
    rewardXp: 15,
    difficulty: QuestDifficulty.easy,
    category: QuestCategory.connection,
    tags: ['friendship', 'reconnection'],
  ),
  EnhancedQuest(
    id: 'c002',
    description: 'Have a deep conversation with a family member',
    rewardXp: 20,
    difficulty: QuestDifficulty.medium,
    category: QuestCategory.connection,
    tags: ['family', 'communication'],
  ),
  EnhancedQuest(
    id: 'c003',
    description: 'Join a new social group or club',
    rewardXp: 30,
    difficulty: QuestDifficulty.medium,
    category: QuestCategory.connection,
    tags: ['social', 'community'],
    requiresPhotoProof: true,
  ),
  EnhancedQuest(
    id: 'c004',
    description: 'Write a heartfelt letter to someone important',
    rewardXp: 25,
    difficulty: QuestDifficulty.medium,
    category: QuestCategory.connection,
    tags: ['gratitude', 'expression'],
  ),
  EnhancedQuest(
    id: 'c005',
    description: 'Host a dinner party for friends',
    rewardXp: 35,
    difficulty: QuestDifficulty.hard,
    category: QuestCategory.connection,
    tags: ['hosting', 'entertaining'],
    requiresPhotoProof: true,
  ),
  EnhancedQuest(
    id: 'c006',
    description: 'Reach out to a professional mentor',
    rewardXp: 25,
    difficulty: QuestDifficulty.medium,
    category: QuestCategory.connection,
    tags: ['mentoring', 'networking'],
  ),
  EnhancedQuest(
    id: 'c007',
    description: 'Apologize to someone you\'ve wronged',
    rewardXp: 40,
    difficulty: QuestDifficulty.hard,
    category: QuestCategory.connection,
    tags: ['apology', 'healing'],
  ),
  EnhancedQuest(
    id: 'c008',
    description: 'Attend a networking event',
    rewardXp: 30,
    difficulty: QuestDifficulty.medium,
    category: QuestCategory.connection,
    tags: ['networking', 'professional'],
    requiresPhotoProof: true,
  ),
  EnhancedQuest(
    id: 'c009',
    description: 'Give someone a genuine compliment',
    rewardXp: 10,
    difficulty: QuestDifficulty.easy,
    category: QuestCategory.connection,
    tags: ['kindness', 'positivity'],
  ),
  EnhancedQuest(
    id: 'c010',
    description: 'Plan a weekend trip with friends',
    rewardXp: 25,
    difficulty: QuestDifficulty.medium,
    category: QuestCategory.connection,
    tags: ['planning', 'adventure'],
  ),

  // LEARNING QUESTS (60 quests)
  EnhancedQuest(
    id: 'l001',
    description: 'Read a book for 1 hour',
    rewardXp: 20,
    difficulty: QuestDifficulty.medium,
    category: QuestCategory.connection,
    tags: ['reading', 'education'],
  ),
  EnhancedQuest(
    id: 'l002',
    description: 'Take an online course in a new subject',
    rewardXp: 45,
    difficulty: QuestDifficulty.hard,
    category: QuestCategory.purpose,
    tags: ['course', 'education'],
  ),
  EnhancedQuest(
    id: 'l003',
    description: 'Learn to cook a new cuisine',
    rewardXp: 30,
    difficulty: QuestDifficulty.medium,
    category: QuestCategory.purpose,
    tags: ['cooking', 'culture'],
    requiresPhotoProof: true,
  ),
  EnhancedQuest(
    id: 'l004',
    description: 'Watch an educational documentary',
    rewardXp: 15,
    difficulty: QuestDifficulty.easy,
    category: QuestCategory.purpose,
    tags: ['documentary', 'knowledge'],
  ),
  EnhancedQuest(
    id: 'l005',
    description: 'Learn a new language for 30 minutes',
    rewardXp: 20,
    difficulty: QuestDifficulty.medium,
    category: QuestCategory.purpose,
    tags: ['language', 'communication'],
  ),
  EnhancedQuest(
    id: 'l006',
    description: 'Research a topic you\'re curious about',
    rewardXp: 15,
    difficulty: QuestDifficulty.easy,
    category: QuestCategory.purpose,
    tags: ['research', 'curiosity'],
  ),
  EnhancedQuest(
    id: 'l007',
    description: 'Attend a workshop or seminar',
    rewardXp: 35,
    difficulty: QuestDifficulty.medium,
    category: QuestCategory.purpose,
    tags: ['workshop', 'skill-building'],
    requiresPhotoProof: true,
  ),
  EnhancedQuest(
    id: 'l008',
    description: 'Learn to play a musical instrument',
    rewardXp: 40,
    difficulty: QuestDifficulty.hard,
    category: QuestCategory.purpose,
    tags: ['music', 'creativity'],
  ),
  EnhancedQuest(
    id: 'l009',
    description: 'Study a historical event in detail',
    rewardXp: 20,
    difficulty: QuestDifficulty.medium,
    category: QuestCategory.connection,
    tags: ['history', 'knowledge'],
  ),
  EnhancedQuest(
    id: 'l010',
    description: 'Learn a new software or tool',
    rewardXp: 25,
    difficulty: QuestDifficulty.medium,
    category: QuestCategory.purpose,
    tags: ['technology', 'productivity'],
  ),

  // CREATIVITY QUESTS (30 quests)
  EnhancedQuest(
    id: 'cr001',
    description: 'Write a short story or poem',
    rewardXp: 25,
    difficulty: QuestDifficulty.medium,
    category: QuestCategory.connection,
    tags: ['writing', 'expression'],
  ),
  EnhancedQuest(
    id: 'cr002',
    description: 'Draw or paint something',
    rewardXp: 20,
    difficulty: QuestDifficulty.medium,
    category: QuestCategory.connection,
    tags: ['art', 'visual'],
    requiresPhotoProof: true,
  ),
  EnhancedQuest(
    id: 'cr003',
    description: 'Create a playlist of meaningful songs',
    rewardXp: 15,
    difficulty: QuestDifficulty.easy,
    category: QuestCategory.connection,
    tags: ['music', 'curation'],
  ),
  EnhancedQuest(
    id: 'cr004',
    description: 'Design something (logo, room, outfit)',
    rewardXp: 30,
    difficulty: QuestDifficulty.medium,
    category: QuestCategory.connection,
    tags: ['design', 'aesthetics'],
    requiresPhotoProof: true,
  ),
  EnhancedQuest(
    id: 'cr005',
    description: 'Start a creative project',
    rewardXp: 35,
    difficulty: QuestDifficulty.hard,
    category: QuestCategory.connection,
    tags: ['project', 'creation'],
  ),

  // ADVENTURE QUESTS (30 quests)
  EnhancedQuest(
    id: 'a001',
    description: 'Visit a new city or town',
    rewardXp: 45,
    difficulty: QuestDifficulty.hard,
    category: QuestCategory.connection,
    tags: ['travel', 'exploration'],
    requiresPhotoProof: true,
  ),
  EnhancedQuest(
    id: 'a002',
    description: 'Try a new extreme sport',
    rewardXp: 60,
    difficulty: QuestDifficulty.epic,
    category: QuestCategory.purpose,
    tags: ['extreme', 'thrill'],
    requiresPhotoProof: true,
  ),
  EnhancedQuest(
    id: 'a003',
    description: 'Go hiking on a new trail',
    rewardXp: 30,
    difficulty: QuestDifficulty.medium,
    category: QuestCategory.health,
    tags: ['hiking', 'nature'],
    requiresPhotoProof: true,
  ),
  EnhancedQuest(
    id: 'a004',
    description: 'Try a new restaurant cuisine',
    rewardXp: 20,
    difficulty: QuestDifficulty.medium,
    category: QuestCategory.health,
    tags: ['food', 'culture'],
    requiresPhotoProof: true,
  ),
  EnhancedQuest(
    id: 'a005',
    description: 'Take a spontaneous road trip',
    rewardXp: 50,
    difficulty: QuestDifficulty.hard,
    category: QuestCategory.connection,
    tags: ['road-trip', 'spontaneity'],
    requiresPhotoProof: true,
  ),

  // MINDFULNESS QUESTS (25 quests)
  EnhancedQuest(
    id: 'm001',
    description: 'Practice gratitude journaling',
    rewardXp: 15,
    difficulty: QuestDifficulty.easy,
    category: QuestCategory.connection,
    tags: ['gratitude', 'reflection'],
  ),
  EnhancedQuest(
    id: 'm002',
    description: 'Do a digital detox for 24 hours',
    rewardXp: 30,
    difficulty: QuestDifficulty.medium,
    category: QuestCategory.health,
    tags: ['detox', 'awareness'],
  ),
  EnhancedQuest(
    id: 'm003',
    description: 'Practice deep breathing for 20 minutes',
    rewardXp: 10,
    difficulty: QuestDifficulty.easy,
    category: QuestCategory.health,
    tags: ['breathing', 'calm'],
  ),
  EnhancedQuest(
    id: 'm004',
    description: 'Spend 1 hour in complete silence - no technology allowed',
    rewardXp: 25,
    difficulty: QuestDifficulty.medium,
    category: QuestCategory.connection,
    tags: ['silence', 'presence'],
  ),
  EnhancedQuest(
    id: 'm005',
    description: 'Write down 30 things you\'re grateful for',
    rewardXp: 15,
    difficulty: QuestDifficulty.easy,
    category: QuestCategory.connection,
    tags: ['gratitude', 'positivity'],
  ),

  // PRODUCTIVITY QUESTS (20 quests)
  EnhancedQuest(
    id: 'pr001',
    description: 'Organize your workspace completely',
    rewardXp: 20,
    difficulty: QuestDifficulty.medium,
    category: QuestCategory.purpose,
    tags: ['organization', 'clarity'],
    requiresPhotoProof: true,
  ),
  EnhancedQuest(
    id: 'pr002',
    description: 'Create a morning routine and stick to it',
    rewardXp: 25,
    difficulty: QuestDifficulty.medium,
    category: QuestCategory.connection,
    tags: ['routine', 'discipline'],
  ),
  EnhancedQuest(
    id: 'pr003',
    description: 'Learn a new productivity method',
    rewardXp: 20,
    difficulty: QuestDifficulty.medium,
    category: QuestCategory.purpose,
    tags: ['method', 'efficiency'],
  ),
  EnhancedQuest(
    id: 'pr004',
    description: 'Complete a major project ahead of deadline',
    rewardXp: 45,
    difficulty: QuestDifficulty.hard,
    category: QuestCategory.purpose,
    tags: ['project', 'achievement'],
  ),
  EnhancedQuest(
    id: 'pr005',
    description: 'Automate a repetitive task',
    rewardXp: 30,
    difficulty: QuestDifficulty.medium,
    category: QuestCategory.wealth,
    tags: ['automation', 'efficiency'],
  ),
];

/// Legacy quests for backward compatibility
const List<MicroQuest> allQuests = [
  MicroQuest(id: 'q1', description: 'Go to a new fighting gym and train for two hours', rewardXp: 40),
  MicroQuest(id: 'q2', description: 'Read a book for 1 hour', rewardXp: 20),
  MicroQuest(id: 'q3', description: 'Cook a healthy meal', rewardXp: 15),
  MicroQuest(id: 'q4', description: 'Call an old friend', rewardXp: 10),
  MicroQuest(id: 'q5', description: 'Write a journal entry', rewardXp: 12),
];

/// Helper functions for quest management
class QuestUtils {
  /// Get quests by category
  static List<EnhancedQuest> getQuestsByCategory(QuestCategory category) {
    return allEnhancedQuests.where((quest) => quest.category == category).toList();
  }

  /// Get quests by difficulty
  static List<EnhancedQuest> getQuestsByDifficulty(QuestDifficulty difficulty) {
    return allEnhancedQuests.where((quest) => quest.difficulty == difficulty).toList();
  }

  /// Get quests by tags
  static List<EnhancedQuest> getQuestsByTags(List<String> tags) {
    return allEnhancedQuests.where((quest) => 
      quest.tags.any((tag) => tags.contains(tag))).toList();
  }

  /// Get random quests (excluding completed ones)
  static List<EnhancedQuest> getRandomQuests(int count, List<String> completedIds) {
    final availableQuests = allEnhancedQuests
        .where((quest) => !completedIds.contains(quest.id))
        .toList();
    availableQuests.shuffle();
    return availableQuests.take(count).toList();
  }

  /// Convert EnhancedQuest to MicroQuest for compatibility
  static MicroQuest toMicroQuest(EnhancedQuest quest) {
    return MicroQuest(
      id: quest.id,
      description: quest.description,
      rewardXp: quest.rewardXp,
    );
  }
} 