import '../models/bounty_model.dart';

/// List of all available bounties (365+)
final List<BountyModel> allBounties = [
  // Health + Purpose (Hard)
  BountyModel(
    id: 'b1',
    description: 'Go to a new fighting gym and train for two hours',
    categories: ['Health', 'Purpose'],
    expPerCategory: {'Health': 40, 'Purpose': 40},
    difficulty: 'hard',
    isEpic: false,
  ),
  
  // Health + Connection (Medium)
  BountyModel(
    id: 'b2',
    description: 'Organize a group workout session with 3+ friends',
    categories: ['Health', 'Connection'],
    expPerCategory: {'Health': 30, 'Connection': 30},
    difficulty: 'medium',
    isEpic: false,
  ),
  
  // Purpose + Wealth (Epic)
  BountyModel(
    id: 'b3',
    description: 'Launch your first digital product or online course',
    categories: ['Purpose', 'Wealth'],
    expPerCategory: {'Purpose': 50, 'Wealth': 50},
    difficulty: 'epic',
    isEpic: true,
  ),
  
  // Health (Easy)
  BountyModel(
    id: 'b4',
    description: 'Try a new healthy recipe you\'ve never cooked before',
    categories: ['Health'],
    expPerCategory: {'Health': 20},
    difficulty: 'easy',
    isEpic: false,
  ),
  
  // Connection + Purpose (Medium)
  BountyModel(
    id: 'b5',
    description: 'Host a skill-sharing workshop for your community',
    categories: ['Connection', 'Purpose'],
    expPerCategory: {'Connection': 30, 'Purpose': 30},
    difficulty: 'medium',
    isEpic: false,
  ),
  
  // Wealth (Hard)
  BountyModel(
    id: 'b6',
    description: 'Create a detailed 5-year financial plan with milestones',
    categories: ['Wealth'],
    expPerCategory: {'Wealth': 40},
    difficulty: 'hard',
    isEpic: false,
  ),
  
  // Health + Wealth + Purpose (Epic)
  BountyModel(
    id: 'b7',
    description: 'Complete a marathon while raising funds for a cause',
    categories: ['Health', 'Wealth', 'Purpose'],
    expPerCategory: {'Health': 50, 'Wealth': 50, 'Purpose': 50},
    difficulty: 'epic',
    isEpic: true,
  ),
  
  // Connection (Easy)
  BountyModel(
    id: 'b8',
    description: 'Write appreciation letters to 3 people who inspired you',
    categories: ['Connection'],
    expPerCategory: {'Connection': 20},
    difficulty: 'easy',
    isEpic: false,
  ),
  
  // Purpose (Medium)
  BountyModel(
    id: 'b9',
    description: 'Shadow someone in your dream career field for a day',
    categories: ['Purpose'],
    expPerCategory: {'Purpose': 30},
    difficulty: 'medium',
    isEpic: false,
  ),
  
  // Health + Connection + Wealth (Hard)
  BountyModel(
    id: 'b10',
    description: 'Organize a charity sports tournament',
    categories: ['Health', 'Connection', 'Wealth'],
    expPerCategory: {'Health': 40, 'Connection': 40, 'Wealth': 40},
    difficulty: 'hard',
    isEpic: false,
  ),
  
  // Add more bounties here...
];

/// Distribution of bounty categories
/// Single category: 25%
/// Two categories: 50%
/// Three categories: 25%
const bountyDistribution = {
  'single': 0.25,
  'double': 0.50,
  'triple': 0.25,
};

/// Distribution of bounty difficulties
const difficultyDistribution = {
  'easy': 0.30,
  'medium': 0.40,
  'hard': 0.20,
  'epic': 0.10,
};

/// EXP rewards per difficulty
const expPerDifficulty = {
  'easy': 20,
  'medium': 30,
  'hard': 40,
  'epic': 50,
};