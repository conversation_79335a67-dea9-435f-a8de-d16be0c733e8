import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/reward_model.dart';
import '../models/bounty_model.dart';
import '../widgets/cashed_bounties_modal.dart';

class RewardStorage {
  static final RewardStorage instance = RewardStorage._();
  RewardStorage._();

  static const String _rewardModelKey = 'reward_model';
  static const String _cashedBountiesKey = 'cashed_bounties';

  /// Saves the reward model to SharedPreferences
  Future<void> saveRewardModel(RewardModel model) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_rewardModelKey, jsonEncode(model.toJson()));
  }

  /// Loads the reward model from SharedPreferences
  Future<RewardModel> loadRewardModel() async {
    final prefs = await SharedPreferences.getInstance();
    final json = prefs.getString(_rewardModelKey);
    if (json == null) {
      return RewardModel(
        totalXp: 0,
        dailyEffortHours: 0,
        bonusUnlocked: false,
        bonusCategory: null,
        completedQuestIds: [],
        lastResetDate: DateTime.now(),
      );
    }
    return RewardModel.fromJson(jsonDecode(json));
  }

  /// Resets daily state if needed
  Future<RewardModel> resetDailyIfNeeded() async {
    final model = await loadRewardModel();
    final now = DateTime.now();
    if (model.lastResetDate.day != now.day ||
        model.lastResetDate.month != now.month ||
        model.lastResetDate.year != now.year) {
      final newModel = model.copyWith(
        dailyEffortHours: 0,
        bonusUnlocked: false,
        bonusCategory: null,
        lastResetDate: now,
      );
      await saveRewardModel(newModel);
      return newModel;
    }
    return model;
  }

  /// Adds a cashed bounty to storage
  Future<void> addCashedBounty(CashedBounty bounty) async {
    final prefs = await SharedPreferences.getInstance();
    final bounties = await getCashedBounties();
    bounties.add(bounty);

    // Convert to JSON
    final jsonList = bounties.map((b) => {
      'bounty': b.bounty.toJson(),
      'photoPath': b.photoPath,
      'completedAt': b.completedAt.toIso8601String(),
    }).toList();

    await prefs.setString(_cashedBountiesKey, jsonEncode(jsonList));
  }

  /// Gets all cashed bounties from storage
  Future<List<CashedBounty>> getCashedBounties() async {
    final prefs = await SharedPreferences.getInstance();
    final json = prefs.getString(_cashedBountiesKey);
    if (json == null) return [];

    final List<dynamic> jsonList = jsonDecode(json);
    return jsonList.map((j) => CashedBounty(
      bounty: BountyModel.fromJson(j['bounty']),
      photoPath: j['photoPath'],
      completedAt: DateTime.parse(j['completedAt']),
    )).toList();
  }
}