// 📁 lib/data/coach_prompts.dart

import 'package:flutter/material.dart';
import '../models/user_model.dart';

class CoachPrompt {
  final String label;
  final String description;
  final String systemPrompt;
  final Color themeColor;

  CoachPrompt({
    required this.label,
    required this.description,
    required this.systemPrompt,
    required this.themeColor,
  });
}

// Core Category Coaches (static)
final Map<String, CoachPrompt> coreCoaches = {
  'Health': CoachPrompt(
    label: 'Health',
    description: 'Train your body, mind, and recovery habits.',
    systemPrompt:
        'You are a motivational health and fitness coach. Help the user improve their physical wellness, nutrition, recovery, and movement.',
    themeColor: const Color(0xFF34A6E2),
  ),
  'Wealth': CoachPrompt(
    label: 'Wealth',
    description: 'Increase discipline, money management, and skills.',
    systemPrompt:
        'You are a practical finance and career coach. Help the user with budgeting, side hustles, skill-building, and time leverage.',
    themeColor: const Color(0xFF4BFF4B),
  ),
  'Purpose': CoachPrompt(
    label: 'Purpose',
    description: 'Get clear on your values, vision, and daily direction.',
    systemPrompt:
        'You are a high-performance clarity and mindset coach. Help the user refine their goals, build resilience, and stay aligned with long-term purpose.',
    themeColor: const Color(0xFF9128FF),
  ),
  'Connection': CoachPrompt(
    label: 'Connection',
    description: 'Improve your relationships, self-trust, and character.',
    systemPrompt:
        'You are a compassionate social coach. Help the user strengthen communication, deepen their self-awareness, and build authentic relationships.',
    themeColor: const Color(0xFFFFD700),
  ),
};

// Custom Category Coaches (based on user)
Map<String, CoachPrompt> getCustomCoaches(User user) {
  final customCategories = user.customCategories;

  final List<Color> customColors = [
    const Color(0xFFFF3D3D), // Fire Red
    const Color(0xFFFFA500), // Neon Orange
  ];

  Map<String, CoachPrompt> customCoaches = {};

  for (int i = 0; i < customCategories.length && i < 2; i++) {
    final catName = customCategories[i];
    final color = customColors[i];

    customCoaches[catName] = CoachPrompt(
      label: catName,
      description: 'Your coach for $catName goals and progression.',
      systemPrompt:
          'You are a kind, honest, and experienced expert in the domain of $catName. Help the user set goals, stay motivated, and overcome challenges related to this field.',
      themeColor: color,
    );
  }

  return customCoaches;
}

// Unified Coach Map
Map<String, CoachPrompt> getAllCoaches(User user) {
  final custom = getCustomCoaches(user);
  return {...coreCoaches, ...custom};
}
