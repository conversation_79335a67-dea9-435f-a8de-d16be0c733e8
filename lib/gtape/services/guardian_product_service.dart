import '../models/guardian_product_model.dart';

/// Service for managing Guardian Tape products
class GuardianProductService {
  // Singleton instance
  static final GuardianProductService _instance = GuardianProductService._internal();
  factory GuardianProductService() => _instance;
  GuardianProductService._internal();

  /// Get all available Guardian Tape products
  List<GuardianProduct> getAllProducts() {
    return [
      GuardianProduct(
        id: 'mouth-tape',
        name: 'Guardian Tape Mouth Tape',
        shortDescription: 'Improve sleep quality with proper nasal breathing',
        description: 'Guardian Tape Mouth Tape helps you maintain nasal breathing during sleep, '
            'improving oxygen intake, reducing snoring, and enhancing recovery. '
            'A 20% improvement in breathing quality can double your recovery rate over time.',
        price: 19.99,
        imageAsset: 'assets/images/gtape/mouth_tape.png',
        benefits: [
          'Promotes nasal breathing during sleep',
          'Reduces mouth dryness and snoring',
          'Improves sleep quality and recovery',
          'Enhances oxygen intake during rest',
        ],
        relatedCategories: ['Health', 'Purpose'],
        shopifyProductId: 'gid://shopify/Product/**********',
      ),
      GuardianProduct(
        id: 'nose-strips',
        name: 'Guardian Tape Nose Strips',
        shortDescription: 'Breathe better, perform better',
        description: 'Guardian Tape Nose Strips open nasal passages for improved airflow, '
            'making breathing easier during exercise and daily activities. '
            'Small 20% improvements compound to 2x better results over a year.',
        price: 14.99,
        imageAsset: 'assets/images/gtape/nose_strips.png',
        benefits: [
          'Opens nasal passages for better airflow',
          'Improves breathing during exercise',
          'Reduces breathing effort',
          'Enhances oxygen intake during activity',
        ],
        relatedCategories: ['Health', 'Purpose', 'Wealth'],
        shopifyProductId: 'gid://shopify/Product/**********',
      ),
      GuardianProduct(
        id: 'ultra-nose-strips',
        name: 'Guardian Tape Ultra Nose Strips',
        shortDescription: 'Maximum breathing enhancement',
        description: 'Guardian Tape Ultra Nose Strips provide maximum nasal passage opening '
            'for serious athletes and those with restricted breathing. '
            'Experience the compound effect of 20% better breathing.',
        price: 24.99,
        imageAsset: 'assets/images/gtape/ultra_nose_strips.png',
        benefits: [
          'Maximum nasal passage opening',
          'Enhanced oxygen intake for peak performance',
          'Ideal for athletes and intense workouts',
          'Premium adhesive for secure fit',
        ],
        relatedCategories: ['Health', 'Purpose'],
        shopifyProductId: 'gid://shopify/Product/**********',
      ),
      GuardianProduct(
        id: 'team-pack',
        name: 'Guardian Tape Team Pack',
        shortDescription: 'Complete breathing solution for you and friends',
        description: 'Guardian Tape Team Pack includes mouth tape and nose strips for multiple users. '
            'Share the benefits of optimal breathing with friends and family. '
            'Help your entire team achieve the 1.2x improvement advantage.',
        price: 49.99,
        imageAsset: 'assets/images/gtape/team_pack.png',
        benefits: [
          'Complete breathing solution for multiple users',
          'Includes mouth tape and nose strips',
          'Perfect for families and teams',
          'Bulk savings on premium products',
        ],
        relatedCategories: ['Health', 'Connection', 'Purpose'],
        shopifyProductId: 'gid://shopify/Product/**********',
      ),
    ];
  }

  /// Get products related to a specific category
  List<GuardianProduct> getProductsByCategory(String category) {
    return getAllProducts()
        .where((product) => product.relatedCategories.contains(category))
        .toList();
  }

  /// Get a product by ID
  GuardianProduct? getProductById(String id) {
    try {
      return getAllProducts().firstWhere((product) => product.id == id);
    } catch (e) {
      return null;
    }
  }
}