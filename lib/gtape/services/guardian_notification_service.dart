import 'dart:math';
import '../../notify/notify_bridge.dart';
import 'guardian_product_service.dart';

/// Service for managing Guardian Tape product notifications
class GuardianNotificationService {
  // Singleton instance
  static final GuardianNotificationService _instance = GuardianNotificationService._internal();
  factory GuardianNotificationService() => _instance;
  GuardianNotificationService._internal();

  final GuardianProductService _productService = GuardianProductService();
  final Random _random = Random();
  final NotifyBridge _notifyBridge = NotifyBridge();
  
  // Notification content templates
  final List<String> _morningTitles = [
    'Start Your Day Right',
    'Morning Breathing Tip',
    'Optimize Your Morning',
    'Breathe Better Today',
  ];
  
  final List<String> _eveningTitles = [
    'Enhance Your Sleep Tonight',
    'Recover Better Tonight',
    'Optimize Your Sleep',
    'Breathe Better, Sleep Better',
  ];
  
  final List<String> _productMessages = [
    'Small improvements compound over time. Guardian Tape helps you breathe 20% better for 2x the results.',
    'Did you know? Proper nasal breathing during sleep can improve recovery by up to 30%.',
    'Elite performers optimize their breathing. Guardian Tape helps you breathe like a pro.',
    'The 1.2x advantage: Just 20% better breathing compounds to double the results over a year.',
  ];

  /// Schedule a morning notification about breathing and Guardian Tape
  Future<void> scheduleMorningNotification() async {
    final title = _morningTitles[_random.nextInt(_morningTitles.length)];
    final product = _productService.getAllProducts()[_random.nextInt(_productService.getAllProducts().length)];
    final message = '${_productMessages[_random.nextInt(_productMessages.length)]} Try ${product.name}.';
    
    await _notifyBridge.scheduleNotification(
      id: 1001,
      title: title,
      body: message,
      scheduledDate: _getNextMorningTime(),
    );
  }
  
  /// Schedule an evening notification about sleep and Guardian Tape
  Future<void> scheduleEveningNotification() async {
    final title = _eveningTitles[_random.nextInt(_eveningTitles.length)];
    final product = _productService.getProductsByCategory('Health').first;
    final message = '${_productMessages[_random.nextInt(_productMessages.length)]} Try ${product.name} tonight.';
    
    await _notifyBridge.scheduleNotification(
      id: 1002,
      title: title,
      body: message,
      scheduledDate: _getNextEveningTime(),
    );
  }
  
  /// Schedule a notification after a streak milestone
  Future<void> scheduleStreakMilestoneNotification(int streakDays) async {
    final title = 'Celebrate $streakDays Days of Progress!';
    final message = 'You\'ve been consistent for $streakDays days! Enhance your results with Guardian Tape products.';
    
    await _notifyBridge.sendCustomNotification(title, message);
  }
  
  /// Schedule a notification after a level up
  Future<void> scheduleLevelUpNotification(int newLevel) async {
    final title = 'Level $newLevel Achieved!';
    final message = 'Congratulations on reaching Level $newLevel! Maximize your progress with Guardian Tape breathing products.';
    
    await _notifyBridge.sendCustomNotification(title, message);
  }
  
  /// Get the next morning time (8:00 AM tomorrow)
  DateTime _getNextMorningTime() {
    final now = DateTime.now();
    final tomorrow = now.add(const Duration(days: 1));
    return DateTime(tomorrow.year, tomorrow.month, tomorrow.day, 8, 0);
  }
  
  /// Get the next evening time (9:00 PM today or tomorrow)
  DateTime _getNextEveningTime() {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day, 21, 0);
    
    if (now.isBefore(today)) {
      return today;
    } else {
      final tomorrow = now.add(const Duration(days: 1));
      return DateTime(tomorrow.year, tomorrow.month, tomorrow.day, 21, 0);
    }
  }
}