import 'dart:math';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/guardian_product_model.dart';
import '../models/referral_model.dart';
import '../../controller/user_controller2.dart';
import 'guardian_product_service.dart';
import '../../notify/notify_bridge.dart';

/// Service for managing Guardian Tape integration with the app
class GuardianService {
  // Singleton instance
  static final GuardianService _instance = GuardianService._internal();
  factory GuardianService() => _instance;
  GuardianService._internal();

  final GuardianProductService _productService = GuardianProductService();
  final Random _random = Random();
  
  // Probability controls for showing product mentions
  static const double _habitCompletionProbability = 0.3;
  static const double _timerCompletionProbability = 0.5;
  static const double _levelUpProbability = 0.7;
  static const double _northStarRankUpProbability = 0.8;
  
  // Track shown products to avoid repetition
  final Set<String> _recentlyShownProductIds = {};
  DateTime _lastProductShown = DateTime(2000);

  /// Check if we should show a product based on probability and frequency limits
  bool shouldShowProduct(String trigger) {
    // Don't show products more than once per hour
    final now = DateTime.now();
    if (now.difference(_lastProductShown).inMinutes < 60) {
      return false;
    }
    
    // Determine probability based on trigger
    double probability;
    switch (trigger) {
      case 'habit_completion':
        probability = _habitCompletionProbability;
        break;
      case 'timer_completion':
        probability = _timerCompletionProbability;
        break;
      case 'level_up':
        probability = _levelUpProbability;
        break;
      case 'north_star_rank_up':
        probability = _northStarRankUpProbability;
        break;
      default:
        probability = 0.2;
    }
    
    return _random.nextDouble() < probability;
  }
  
  /// Get a product to show, avoiding recently shown ones
  GuardianProduct getProductToShow(String category) {
    final products = _productService.getProductsByCategory(category);
    
    // Filter out recently shown products if possible
    final availableProducts = products
        .where((p) => !_recentlyShownProductIds.contains(p.id))
        .toList();
    
    // If all products have been shown recently, reset and show any
    final productsToChooseFrom = availableProducts.isEmpty ? products : availableProducts;
    
    // Select random product
    final product = productsToChooseFrom[_random.nextInt(productsToChooseFrom.length)];
    
    // Update tracking
    _recentlyShownProductIds.add(product.id);
    if (_recentlyShownProductIds.length > 3) {
      _recentlyShownProductIds.remove(_recentlyShownProductIds.first);
    }
    _lastProductShown = DateTime.now();
    
    return product;
  }
  
  /// Generate a referral link for the current user
  String generateReferralLink(String userId) {
    return 'https://guardiantape.com/refer?ref=$userId';
  }
  
  /// Create a referral for a friend
  Future<GuardianReferral> createReferral(String referrerUserId, String friendEmail) async {
    // In a real implementation, this would call an API
    final referral = GuardianReferral(
      id: 'ref_${DateTime.now().millisecondsSinceEpoch}',
      referrerUserId: referrerUserId,
      referredEmail: friendEmail,
      createdAt: DateTime.now(),
      discountCode: 'FRIEND10',
    );
    
    // Here you would save the referral to your backend
    
    return referral;
  }
  
  /// Send a notification about Guardian Tape products
  Future<void> sendProductNotification(BuildContext context, String title, String body) async {
    final notifyBridge = NotifyBridge();
    await notifyBridge.sendCustomNotification(title, body);
  }
  
  /// Update user's Guardian data after a purchase
  Future<void> recordPurchase(BuildContext context, String productId) async {
    final userController = Provider.of<UserController2>(context, listen: false);
    final user = userController.user;
    
    if (user == null) return;
    
    // In a real implementation, you would update the user model with purchase data
    // This is a placeholder for that logic
    
    // For now, we'll just show a success message
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Purchase recorded! Thank you for supporting Guardian Tape.'),
        duration: Duration(seconds: 3),
      ),
    );
  }
}