

/// Model representing a Guardian Tape product
class GuardianProduct {
  final String id;
  final String name;
  final String description;
  final String shortDescription;
  final double price;
  final String imageAsset;
  final List<String> benefits;
  final List<String> relatedCategories;
  final String shopifyProductId;

  const GuardianProduct({
    required this.id,
    required this.name,
    required this.description,
    required this.shortDescription,
    required this.price,
    required this.imageAsset,
    required this.benefits,
    required this.relatedCategories,
    required this.shopifyProductId,
  });
}

/// Model for tracking user's Guardian Tape engagement
class GuardianUserData {
  final int purchaseCount;
  final List<String> purchasedProductIds;
  final int referralCount;
  final List<String> referredUserIds;
  final DateTime lastProductView;
  final int totalDiscountEarned;

  GuardianUserData({
    this.purchaseCount = 0,
    this.purchasedProductIds = const [],
    this.referralCount = 0,
    this.referredUserIds = const [],
    DateTime? lastProductView,
    this.totalDiscountEarned = 0,
  }) : lastProductView = lastProductView ?? DateTime(2000);

  GuardianUserData copyWith({
    int? purchaseCount,
    List<String>? purchasedProductIds,
    int? referralCount,
    List<String>? referredUserIds,
    DateTime? lastProductView,
    int? totalDiscountEarned,
  }) {
    return GuardianUserData(
      purchaseCount: purchaseCount ?? this.purchaseCount,
      purchasedProductIds: purchasedProductIds ?? this.purchasedProductIds,
      referralCount: referralCount ?? this.referralCount,
      referredUserIds: referredUserIds ?? this.referredUserIds,
      lastProductView: lastProductView ?? this.lastProductView,
      totalDiscountEarned: totalDiscountEarned ?? this.totalDiscountEarned,
    );
  }

  /// Get the Guardian status level based on purchase count
  String get guardianLevel {
    if (purchaseCount >= 5) return 'Champion';
    if (purchaseCount >= 2) return 'Adept';
    return 'Initiate';
  }

  /// Calculate the current referral discount percentage
  int get currentReferralDiscount => referralCount * 5 > 50 ? 50 : referralCount * 5;
}