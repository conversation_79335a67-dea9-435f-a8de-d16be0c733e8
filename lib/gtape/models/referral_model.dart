/// Model representing a referral in the Guardian Tape program
class GuardianReferral {
  final String id;
  final String referrerUserId;
  final String referredEmail;
  final DateTime createdAt;
  final bool claimed;
  final bool converted;
  final DateTime? claimedAt;
  final String? discountCode;

  const GuardianReferral({
    required this.id,
    required this.referrerUserId,
    required this.referredEmail,
    required this.createdAt,
    this.claimed = false,
    this.converted = false,
    this.claimedAt,
    this.discountCode,
  });

  GuardianReferral copyWith({
    String? id,
    String? referrerUserId,
    String? referredEmail,
    DateTime? createdAt,
    bool? claimed,
    bool? converted,
    DateTime? claimedAt,
    String? discountCode,
  }) {
    return GuardianReferral(
      id: id ?? this.id,
      referrerUserId: referrerUserId ?? this.referrerUserId,
      referredEmail: referredEmail ?? this.referredEmail,
      createdAt: createdAt ?? this.createdAt,
      claimed: claimed ?? this.claimed,
      converted: converted ?? this.converted,
      claimedAt: claimedAt ?? this.claimedAt,
      discountCode: discountCode ?? this.discountCode,
    );
  }
}