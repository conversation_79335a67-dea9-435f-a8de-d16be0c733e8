import 'package:flutter/material.dart';
import '../../theme/colors.dart';
import '../models/guardian_product_model.dart';
import 'package:url_launcher/url_launcher.dart';

/// A card widget that displays a Guardian Tape product with glow effect
class GuardianProductCard extends StatefulWidget {
  final GuardianProduct product;
  final Color glowColor;
  final VoidCallback? onTap;
  final bool showBuyButton;
  final bool isCompact;

  const GuardianProductCard({
    super.key,
    required this.product,
    this.glowColor = MolColors.purple,
    this.onTap,
    this.showBuyButton = true,
    this.isCompact = false,
  });

  @override
  State<GuardianProductCard> createState() => _GuardianProductCardState();
}

class _GuardianProductCardState extends State<GuardianProductCard> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _glowAnimation;
  
  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 2),
    )..repeat(reverse: true);
    
    _glowAnimation = Tween<double>(begin: 2.0, end: 4.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
  }
  
  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _launchProductUrl() async {
    final url = Uri.parse('https://guardiantape.com/products/${widget.product.id}');
    if (await canLaunchUrl(url)) {
      await launchUrl(url);
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _glowAnimation,
      builder: (context, child) {
        return GestureDetector(
          onTap: widget.onTap ?? _launchProductUrl,
          child: Container(
            margin: EdgeInsets.symmetric(
              vertical: 8,
              horizontal: widget.isCompact ? 4 : 16,
            ),
            width: widget.isCompact ? 160 : double.infinity,
            decoration: BoxDecoration(
              color: Colors.black,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: widget.glowColor.withValues(alpha: 0.5),
                  blurRadius: _glowAnimation.value,
                  spreadRadius: _glowAnimation.value / 2,
                ),
              ],
              border: Border.all(
                color: widget.glowColor.withValues(alpha: 0.7),
                width: 1.5,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                // Product image with glow
                ClipRRect(
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(14),
                    topRight: Radius.circular(14),
                  ),
                  child: Container(
                    height: widget.isCompact ? 100 : 180,
                    width: double.infinity,
                    color: Colors.black,
                    child: Stack(
                      fit: StackFit.expand,
                      children: [
                        // Placeholder for product image
                        Center(
                          child: Icon(
                            Icons.fitness_center,
                            size: widget.isCompact ? 40 : 80,
                            color: widget.glowColor,
                          ),
                        ),
                        // Glow overlay
                        Positioned.fill(
                          child: Container(
                            decoration: BoxDecoration(
                              gradient: RadialGradient(
                                colors: [
                                  widget.glowColor.withValues(alpha: 0.3),
                                  Colors.transparent,
                                ],
                                center: Alignment.center,
                                radius: 0.8,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                
                // Product details
                Padding(
                  padding: const EdgeInsets.all(12),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Product name
                      Text(
                        widget.product.name,
                        style: TextStyle(
                          fontSize: widget.isCompact ? 14 : 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      
                      // Short description
                      Text(
                        widget.product.shortDescription,
                        style: TextStyle(
                          fontSize: widget.isCompact ? 12 : 14,
                          color: Colors.white70,
                        ),
                        maxLines: widget.isCompact ? 2 : 3,
                        overflow: TextOverflow.ellipsis,
                      ),
                      
                      if (!widget.isCompact) ...[
                        const SizedBox(height: 8),
                        // Benefits
                        ...widget.product.benefits.take(2).map((benefit) => Padding(
                          padding: const EdgeInsets.only(bottom: 4),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Icon(
                                Icons.check_circle_outline,
                                size: 16,
                                color: widget.glowColor,
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  benefit,
                                  style: const TextStyle(
                                    fontSize: 12,
                                    color: Colors.white70,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                        )),
                      ],
                      
                      const SizedBox(height: 12),
                      
                      // Price and buy button
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            '\$${widget.product.price.toStringAsFixed(2)}',
                            style: TextStyle(
                              fontSize: widget.isCompact ? 14 : 18,
                              fontWeight: FontWeight.bold,
                              color: widget.glowColor,
                            ),
                          ),
                          if (widget.showBuyButton)
                            ElevatedButton(
                              onPressed: _launchProductUrl,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: widget.glowColor,
                                foregroundColor: Colors.black,
                                padding: EdgeInsets.symmetric(
                                  horizontal: widget.isCompact ? 8 : 16,
                                  vertical: widget.isCompact ? 4 : 8,
                                ),
                                textStyle: TextStyle(
                                  fontSize: widget.isCompact ? 12 : 14,
                                  fontWeight: FontWeight.bold,
                                ),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                              child: Text(widget.isCompact ? 'Buy' : 'Buy Now'),
                            ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}