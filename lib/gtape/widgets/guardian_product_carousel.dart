import 'package:flutter/material.dart';
import '../models/guardian_product_model.dart';
import '../services/guardian_product_service.dart';
import 'guardian_product_card.dart';
import '../../theme/colors.dart';

/// A horizontal carousel of Guardian Tape products
class GuardianProductCarousel extends StatefulWidget {
  final String title;
  final String? category;
  final Color glowColor;
  final VoidCallback? onSeeAllTap;
  final bool showTitle;

  const GuardianProductCarousel({
    super.key,
    this.title = 'Boost Your Performance',
    this.category,
    this.glowColor = MolColors.purple,
    this.onSeeAllTap,
    this.showTitle = true,
  });

  @override
  State<GuardianProductCarousel> createState() => _GuardianProductCarouselState();
}

class _GuardianProductCarouselState extends State<GuardianProductCarousel> {
  final GuardianProductService _productService = GuardianProductService();
  late List<GuardianProduct> _products;
  
  @override
  void initState() {
    super.initState();
    _loadProducts();
  }
  
  void _loadProducts() {
    if (widget.category != null) {
      _products = _productService.getProductsByCategory(widget.category!);
    } else {
      _products = _productService.getAllProducts();
    }
    
    // If no products found for category, show all
    if (_products.isEmpty) {
      _products = _productService.getAllProducts();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.showTitle) ...[
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  widget.title,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                    shadows: [
                      Shadow(
                        color: widget.glowColor.withValues(alpha: 0.7),
                        blurRadius: 4,
                      ),
                    ],
                  ),
                ),
                if (widget.onSeeAllTap != null)
                  TextButton(
                    onPressed: widget.onSeeAllTap,
                    child: Text(
                      'See All',
                      style: TextStyle(
                        color: widget.glowColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ],
        
        SizedBox(
          height: 220,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
            itemCount: _products.length,
            itemBuilder: (context, index) {
              return GuardianProductCard(
                product: _products[index],
                glowColor: widget.glowColor,
                isCompact: true,
              );
            },
          ),
        ),
      ],
    );
  }
}