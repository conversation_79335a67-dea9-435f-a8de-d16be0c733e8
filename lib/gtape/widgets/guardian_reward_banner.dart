import 'package:flutter/material.dart';
import '../../theme/colors.dart';
import '../models/guardian_product_model.dart';

/// A non-intrusive banner that appears after completing activities
/// to offer Guardian Tape product discounts
class GuardianRewardBanner extends StatefulWidget {
  final GuardianProduct product;
  final String discountCode;
  final String discountAmount;
  final VoidCallback? onDismiss;
  final VoidCallback? onTap;
  final Color glowColor;

  const GuardianRewardBanner({
    super.key,
    required this.product,
    required this.discountCode,
    required this.discountAmount,
    this.onDismiss,
    this.onTap,
    this.glowColor = MolColors.purple,
  });

  @override
  State<GuardianRewardBanner> createState() => _GuardianRewardBannerState();
}

class _GuardianRewardBannerState extends State<GuardianRewardBanner> {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: MolColors.black,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: widget.glowColor, width: 2),
        boxShadow: [
          BoxShadow(
            color: widget.glowColor.withValues(alpha: 0.3),
            blurRadius: 8,
            spreadRadius: 2,
          ),
        ],
      ),
      child: Row(
        children: [
          Icon(
            Icons.local_offer,
            color: widget.glowColor,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  widget.product.name,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '${widget.discountAmount} off with code: ${widget.discountCode}',
                  style: TextStyle(
                    color: widget.glowColor,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          if (widget.onDismiss != null)
            IconButton(
              onPressed: widget.onDismiss,
              icon: const Icon(Icons.close, color: Colors.white70),
            ),
        ],
      ),
    );
  }
}