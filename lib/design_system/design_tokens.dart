import 'package:flutter/material.dart';

/// Design tokens for the Maxed Out Life design system.
/// 
/// This file contains all the foundational design values including:
/// - Spacing and sizing scales
/// - Border radius values
/// - Shadow definitions
/// - Animation durations
/// - Typography scales
/// 
/// These tokens ensure consistency across the entire application.
class DesignTokens {
  // ═══════════════════════════════════════════════════════════════════════════
  // SPACING SCALE
  // ═══════════════════════════════════════════════════════════════════════════
  
  /// Extra small spacing (2px) - For tight layouts
  static const double spaceXs = 2.0;
  
  /// Small spacing (4px) - For minimal gaps
  static const double spaceSm = 4.0;
  
  /// Medium spacing (8px) - Standard small gap
  static const double spaceMd = 8.0;
  
  /// Large spacing (12px) - Standard medium gap
  static const double spaceLg = 12.0;
  
  /// Extra large spacing (16px) - Standard large gap
  static const double spaceXl = 16.0;
  
  /// 2X large spacing (24px) - Section spacing
  static const double space2xl = 24.0;
  
  /// 3X large spacing (32px) - Major section spacing
  static const double space3xl = 32.0;
  
  /// 4X large spacing (48px) - Screen padding
  static const double space4xl = 48.0;
  
  /// 5X large spacing (64px) - Large screen sections
  static const double space5xl = 64.0;

  // ═══════════════════════════════════════════════════════════════════════════
  // BORDER RADIUS SCALE
  // ═══════════════════════════════════════════════════════════════════════════
  
  /// No radius (0px)
  static const double radiusNone = 0.0;
  
  /// Small radius (4px) - For small elements
  static const double radiusSm = 4.0;
  
  /// Medium radius (8px) - Standard radius
  static const double radiusMd = 8.0;
  
  /// Large radius (12px) - Cards and containers
  static const double radiusLg = 12.0;
  
  /// Extra large radius (16px) - Major containers
  static const double radiusXl = 16.0;
  
  /// 2X large radius (24px) - Buttons and prominent elements
  static const double radius2xl = 24.0;
  
  /// 3X large radius (32px) - Special containers
  static const double radius3xl = 32.0;
  
  /// Full radius (9999px) - Pills and circular elements
  static const double radiusFull = 9999.0;

  // ═══════════════════════════════════════════════════════════════════════════
  // ELEVATION & SHADOWS
  // ═══════════════════════════════════════════════════════════════════════════
  
  /// No elevation
  static const double elevationNone = 0.0;
  
  /// Small elevation (2dp)
  static const double elevationSm = 2.0;
  
  /// Medium elevation (4dp)
  static const double elevationMd = 4.0;
  
  /// Large elevation (8dp)
  static const double elevationLg = 8.0;
  
  /// Extra large elevation (12dp)
  static const double elevationXl = 12.0;
  
  /// 2X large elevation (16dp)
  static const double elevation2xl = 16.0;
  
  /// 3X large elevation (24dp)
  static const double elevation3xl = 24.0;

  // ═══════════════════════════════════════════════════════════════════════════
  // ANIMATION DURATIONS
  // ═══════════════════════════════════════════════════════════════════════════
  
  /// Instant animation (0ms)
  static const Duration durationInstant = Duration.zero;
  
  /// Fast animation (150ms) - Quick transitions
  static const Duration durationFast = Duration(milliseconds: 150);
  
  /// Normal animation (300ms) - Standard transitions
  static const Duration durationNormal = Duration(milliseconds: 300);
  
  /// Slow animation (500ms) - Deliberate transitions
  static const Duration durationSlow = Duration(milliseconds: 500);
  
  /// Extra slow animation (800ms) - Dramatic effects
  static const Duration durationXSlow = Duration(milliseconds: 800);
  
  /// Loading animation (1200ms) - Loading states
  static const Duration durationLoading = Duration(milliseconds: 1200);

  // ═══════════════════════════════════════════════════════════════════════════
  // TYPOGRAPHY SCALE
  // ═══════════════════════════════════════════════════════════════════════════
  
  /// Extra small text (10px)
  static const double fontSizeXs = 10.0;
  
  /// Small text (12px)
  static const double fontSizeSm = 12.0;
  
  /// Base text (14px)
  static const double fontSizeBase = 14.0;
  
  /// Large text (16px)
  static const double fontSizeLg = 16.0;
  
  /// Extra large text (18px)
  static const double fontSizeXl = 18.0;
  
  /// 2X large text (20px)
  static const double fontSize2xl = 20.0;
  
  /// 3X large text (24px)
  static const double fontSize3xl = 24.0;
  
  /// 4X large text (28px)
  static const double fontSize4xl = 28.0;
  
  /// 5X large text (32px)
  static const double fontSize5xl = 32.0;
  
  /// 6X large text (36px)
  static const double fontSize6xl = 36.0;

  // ═══════════════════════════════════════════════════════════════════════════
  // COMPONENT SIZES
  // ═══════════════════════════════════════════════════════════════════════════
  
  /// Small button height (44px) - meets accessibility guidelines
  static const double buttonHeightSm = 44.0;
  
  /// Medium button height (40px)
  static const double buttonHeightMd = 40.0;
  
  /// Large button height (48px)
  static const double buttonHeightLg = 48.0;
  
  /// Extra large button height (56px)
  static const double buttonHeightXl = 56.0;
  
  /// Small icon size (16px)
  static const double iconSizeSm = 16.0;
  
  /// Medium icon size (20px)
  static const double iconSizeMd = 20.0;
  
  /// Large icon size (24px)
  static const double iconSizeLg = 24.0;
  
  /// Extra large icon size (32px)
  static const double iconSizeXl = 32.0;
  
  /// 2X large icon size (40px)
  static const double iconSize2xl = 40.0;
  
  /// 3X large icon size (48px)
  static const double iconSize3xl = 48.0;

  // ═══════════════════════════════════════════════════════════════════════════
  // OPACITY SCALE
  // ═══════════════════════════════════════════════════════════════════════════
  
  /// Disabled opacity (0.4)
  static const double opacityDisabled = 0.4;
  
  /// Muted opacity (0.6)
  static const double opacityMuted = 0.6;
  
  /// Secondary opacity (0.8)
  static const double opacitySecondary = 0.8;
  
  /// Full opacity (1.0)
  static const double opacityFull = 1.0;

  // ═══════════════════════════════════════════════════════════════════════════
  // BORDER WIDTHS
  // ═══════════════════════════════════════════════════════════════════════════
  
  /// Thin border (1px)
  static const double borderThin = 1.0;
  
  /// Medium border (2px)
  static const double borderMedium = 2.0;
  
  /// Thick border (3px)
  static const double borderThick = 3.0;
  
  /// Extra thick border (4px)
  static const double borderXThick = 4.0;

  // ═══════════════════════════════════════════════════════════════════════════
  // BREAKPOINTS
  // ═══════════════════════════════════════════════════════════════════════════
  
  /// Small screen breakpoint (480px)
  static const double breakpointSm = 480.0;
  
  /// Medium screen breakpoint (768px)
  static const double breakpointMd = 768.0;
  
  /// Large screen breakpoint (1024px)
  static const double breakpointLg = 1024.0;
  
  /// Extra large screen breakpoint (1280px)
  static const double breakpointXl = 1280.0;
}

/// Predefined shadow styles for consistent elevation effects.
class DesignShadows {
  /// Small shadow for subtle elevation
  static List<BoxShadow> small(Color color) => [
    BoxShadow(
      color: color.withValues(alpha: 0.1),
      blurRadius: 4,
      offset: const Offset(0, 2),
    ),
  ];
  
  /// Medium shadow for standard elevation
  static List<BoxShadow> medium(Color color) => [
    BoxShadow(
      color: color.withValues(alpha: 0.15),
      blurRadius: 8,
      offset: const Offset(0, 4),
    ),
  ];
  
  /// Large shadow for prominent elevation
  static List<BoxShadow> large(Color color) => [
    BoxShadow(
      color: color.withValues(alpha: 0.2),
      blurRadius: 16,
      offset: const Offset(0, 8),
    ),
  ];
  
  /// Glow effect for neon elements
  static List<BoxShadow> glow(Color color) => [
    BoxShadow(
      color: color.withValues(alpha: 0.5),
      blurRadius: 20,
      spreadRadius: 2,
    ),
  ];
  
  /// Intense glow for active states
  static List<BoxShadow> glowIntense(Color color) => [
    BoxShadow(
      color: color.withValues(alpha: 0.7),
      blurRadius: 32,
      spreadRadius: 4,
    ),
  ];
}
