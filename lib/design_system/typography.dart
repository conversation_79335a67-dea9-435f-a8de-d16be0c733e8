import 'package:flutter/material.dart';
import 'design_tokens.dart';

/// Typography system for the Maxed Out Life design system.
/// 
/// Provides consistent text styles across the application with:
/// - Semantic naming (heading, body, caption, etc.)
/// - Font family management
/// - Responsive sizing
/// - Consistent line heights and letter spacing
/// 
/// Example usage:
/// ```dart
/// Text('Hello World', style: AppTypography.headingLarge)
/// Text('Body text', style: AppTypography.bodyMedium)
/// ```
class AppTypography {
  // ═══════════════════════════════════════════════════════════════════════════
  // FONT FAMILIES
  // ═══════════════════════════════════════════════════════════════════════════
  
  /// Primary font family for headings and important text
  static const String fontFamilyPrimary = 'Pirulen';
  
  /// Secondary font family for body text and UI elements
  static const String fontFamilySecondary = 'BITSUMISHI';
  
  /// Monospace font family for code and technical content
  static const String fontFamilyMono = 'Digital-7';
  
  /// Display font family for special occasions
  static const String fontFamilyDisplay = 'Orbitron';

  // ═══════════════════════════════════════════════════════════════════════════
  // HEADING STYLES
  // ═══════════════════════════════════════════════════════════════════════════
  
  /// Extra large heading (36px) - For hero sections and main titles
  static const TextStyle headingXLarge = TextStyle(
    fontFamily: fontFamilyPrimary,
    fontSize: DesignTokens.fontSize6xl,
    fontWeight: FontWeight.bold,
    height: 1.2,
    letterSpacing: -0.5,
  );
  
  /// Large heading (32px) - For page titles
  static const TextStyle headingLarge = TextStyle(
    fontFamily: fontFamilyPrimary,
    fontSize: DesignTokens.fontSize5xl,
    fontWeight: FontWeight.bold,
    height: 1.25,
    letterSpacing: -0.25,
  );
  
  /// Medium heading (28px) - For section titles
  static const TextStyle headingMedium = TextStyle(
    fontFamily: fontFamilyPrimary,
    fontSize: DesignTokens.fontSize4xl,
    fontWeight: FontWeight.bold,
    height: 1.3,
  );
  
  /// Small heading (24px) - For subsection titles
  static const TextStyle headingSmall = TextStyle(
    fontFamily: fontFamilyPrimary,
    fontSize: DesignTokens.fontSize3xl,
    fontWeight: FontWeight.bold,
    height: 1.35,
  );
  
  /// Extra small heading (20px) - For card titles
  static const TextStyle headingXSmall = TextStyle(
    fontFamily: fontFamilyPrimary,
    fontSize: DesignTokens.fontSize2xl,
    fontWeight: FontWeight.bold,
    height: 1.4,
  );

  // ═══════════════════════════════════════════════════════════════════════════
  // BODY STYLES
  // ═══════════════════════════════════════════════════════════════════════════
  
  /// Large body text (18px) - For important content
  static const TextStyle bodyLarge = TextStyle(
    fontFamily: fontFamilySecondary,
    fontSize: DesignTokens.fontSizeXl,
    fontWeight: FontWeight.normal,
    height: 1.5,
  );
  
  /// Medium body text (16px) - Standard body text
  static const TextStyle bodyMedium = TextStyle(
    fontFamily: fontFamilySecondary,
    fontSize: DesignTokens.fontSizeLg,
    fontWeight: FontWeight.normal,
    height: 1.5,
  );
  
  /// Small body text (14px) - Secondary content
  static const TextStyle bodySmall = TextStyle(
    fontFamily: fontFamilySecondary,
    fontSize: DesignTokens.fontSizeBase,
    fontWeight: FontWeight.normal,
    height: 1.5,
  );
  
  /// Extra small body text (12px) - Tertiary content
  static const TextStyle bodyXSmall = TextStyle(
    fontFamily: fontFamilySecondary,
    fontSize: DesignTokens.fontSizeSm,
    fontWeight: FontWeight.normal,
    height: 1.5,
  );

  // ═══════════════════════════════════════════════════════════════════════════
  // LABEL STYLES
  // ═══════════════════════════════════════════════════════════════════════════
  
  /// Large label (16px) - For prominent labels
  static const TextStyle labelLarge = TextStyle(
    fontFamily: fontFamilySecondary,
    fontSize: DesignTokens.fontSizeLg,
    fontWeight: FontWeight.w600,
    height: 1.4,
    letterSpacing: 0.5,
  );
  
  /// Medium label (14px) - Standard labels
  static const TextStyle labelMedium = TextStyle(
    fontFamily: fontFamilySecondary,
    fontSize: DesignTokens.fontSizeBase,
    fontWeight: FontWeight.w600,
    height: 1.4,
    letterSpacing: 0.5,
  );
  
  /// Small label (12px) - Secondary labels
  static const TextStyle labelSmall = TextStyle(
    fontFamily: fontFamilySecondary,
    fontSize: DesignTokens.fontSizeSm,
    fontWeight: FontWeight.w600,
    height: 1.4,
    letterSpacing: 0.75,
  );

  // ═══════════════════════════════════════════════════════════════════════════
  // BUTTON STYLES
  // ═══════════════════════════════════════════════════════════════════════════
  
  /// Large button text (18px)
  static const TextStyle buttonLarge = TextStyle(
    fontFamily: fontFamilySecondary,
    fontSize: DesignTokens.fontSizeXl,
    fontWeight: FontWeight.bold,
    height: 1.2,
    letterSpacing: 0.5,
  );
  
  /// Medium button text (16px)
  static const TextStyle buttonMedium = TextStyle(
    fontFamily: fontFamilySecondary,
    fontSize: DesignTokens.fontSizeLg,
    fontWeight: FontWeight.bold,
    height: 1.2,
    letterSpacing: 0.5,
  );
  
  /// Small button text (14px)
  static const TextStyle buttonSmall = TextStyle(
    fontFamily: fontFamilySecondary,
    fontSize: DesignTokens.fontSizeBase,
    fontWeight: FontWeight.bold,
    height: 1.2,
    letterSpacing: 0.75,
  );

  // ═══════════════════════════════════════════════════════════════════════════
  // CAPTION STYLES
  // ═══════════════════════════════════════════════════════════════════════════
  
  /// Large caption (14px) - For important captions
  static const TextStyle captionLarge = TextStyle(
    fontFamily: fontFamilySecondary,
    fontSize: DesignTokens.fontSizeBase,
    fontWeight: FontWeight.normal,
    height: 1.4,
    letterSpacing: 0.25,
  );
  
  /// Medium caption (12px) - Standard captions
  static const TextStyle captionMedium = TextStyle(
    fontFamily: fontFamilySecondary,
    fontSize: DesignTokens.fontSizeSm,
    fontWeight: FontWeight.normal,
    height: 1.4,
    letterSpacing: 0.25,
  );
  
  /// Small caption (10px) - Fine print
  static const TextStyle captionSmall = TextStyle(
    fontFamily: fontFamilySecondary,
    fontSize: DesignTokens.fontSizeXs,
    fontWeight: FontWeight.normal,
    height: 1.4,
    letterSpacing: 0.5,
  );

  // ═══════════════════════════════════════════════════════════════════════════
  // SPECIAL STYLES
  // ═══════════════════════════════════════════════════════════════════════════
  
  /// Display text for hero sections
  static const TextStyle display = TextStyle(
    fontFamily: fontFamilyDisplay,
    fontSize: DesignTokens.fontSize6xl,
    fontWeight: FontWeight.bold,
    height: 1.1,
    letterSpacing: -1.0,
  );
  
  /// Monospace text for code and technical content
  static const TextStyle monospace = TextStyle(
    fontFamily: fontFamilyMono,
    fontSize: DesignTokens.fontSizeBase,
    fontWeight: FontWeight.normal,
    height: 1.4,
  );
  
  /// Overline text for categories and tags
  static const TextStyle overline = TextStyle(
    fontFamily: fontFamilySecondary,
    fontSize: DesignTokens.fontSizeXs,
    fontWeight: FontWeight.w600,
    height: 1.6,
    letterSpacing: 1.5,
  );

  // ═══════════════════════════════════════════════════════════════════════════
  // UTILITY METHODS
  // ═══════════════════════════════════════════════════════════════════════════
  
  /// Apply color to any text style
  static TextStyle withColor(TextStyle style, Color color) {
    return style.copyWith(color: color);
  }
  
  /// Apply opacity to any text style
  static TextStyle withOpacity(TextStyle style, double opacity) {
    return style.copyWith(color: style.color?.withValues(alpha: opacity));
  }
  
  /// Apply shadow to any text style
  static TextStyle withShadow(TextStyle style, Color shadowColor) {
    return style.copyWith(
      shadows: [
        Shadow(
          color: shadowColor.withValues(alpha: 0.5),
          blurRadius: 4,
          offset: const Offset(0, 2),
        ),
      ],
    );
  }
  
  /// Apply glow effect to any text style
  static TextStyle withGlow(TextStyle style, Color glowColor) {
    return style.copyWith(
      shadows: [
        Shadow(
          color: glowColor.withValues(alpha: 0.8),
          blurRadius: 12,
          offset: Offset.zero,
        ),
      ],
    );
  }
  
  /// Make any text style uppercase
  static TextStyle uppercase(TextStyle style) {
    return style.copyWith(letterSpacing: (style.letterSpacing ?? 0) + 0.5);
  }
}
