/// Maxed Out Life Design System
///
/// A comprehensive design system providing consistent styling, components,
/// and patterns across the entire application.
library;

import 'package:flutter/material.dart';
/// 
/// ## Features
/// 
/// ### Design Tokens
/// - Consistent spacing, sizing, and typography scales
/// - Standardized border radius and elevation values
/// - Animation duration constants
/// - Opacity and color scales
/// 
/// ### Typography System
/// - Semantic text styles (heading, body, label, caption)
/// - Multiple font families with proper fallbacks
/// - Responsive sizing and line heights
/// - Utility methods for color, shadow, and glow effects
/// 
/// ### Component Library
/// - **AppButton**: Comprehensive button component with multiple variants
/// - **AppCard**: Flexible card component with glow effects
/// - **CategoryCard**: Specialized cards for category-based content
/// - **BountyCard**: Specialized cards for bounties and rewards
/// 
/// ### Theme Integration
/// - Seamless integration with Flutter's ThemeData
/// - Light and dark theme support
/// - Consistent Material 3 styling
/// 
/// ## Usage
/// 
/// ### Import the design system:
/// ```dart
/// import 'package:maxed_out_life/design_system/design_system.dart';
/// ```
/// 
/// ### Use design tokens:
/// ```dart
/// Container(
///   padding: EdgeInsets.all(DesignTokens.spaceXl),
///   margin: EdgeInsets.symmetric(vertical: DesignTokens.spaceMd),
///   decoration: BoxDecoration(
///     borderRadius: BorderRadius.circular(DesignTokens.radiusLg),
///   ),
/// )
/// ```
/// 
/// ### Use typography:
/// ```dart
/// Text(
///   'Heading Text',
///   style: AppTypography.headingLarge,
/// )
/// 
/// Text(
///   'Body text with glow effect',
///   style: AppTypography.withGlow(
///     AppTypography.bodyMedium,
///     MolColors.cyan,
///   ),
/// )
/// ```
/// 
/// ### Use components:
/// ```dart
/// AppButton.primary(
///   text: 'Get Started',
///   icon: Icons.arrow_forward,
///   onPressed: () => print('Pressed'),
/// )
/// 
/// AppCard.glow(
///   glowColor: MolColors.cyan,
///   child: Text('Card content'),
/// )
/// 
/// CategoryCard(
///   category: 'Health',
///   child: Text('Health-related content'),
///   onTap: () => print('Tapped'),
/// )
/// ```
/// 
/// ## Design Principles
/// 
/// ### Consistency
/// All components follow the same design patterns and use shared tokens
/// for spacing, colors, and typography.
/// 
/// ### Accessibility
/// Components include proper semantic markup, touch targets, and
/// support for screen readers.
/// 
/// ### Performance
/// Optimized animations and efficient rendering for smooth 60fps
/// performance across all devices.
/// 
/// ### Customization
/// Flexible component APIs allow for customization while maintaining
/// design consistency.
/// 
/// ### Neon Aesthetic
/// Special support for the app's signature neon glow effects and
/// cyberpunk-inspired visual design.

// Core design tokens and constants
export 'design_tokens.dart';

// Typography system
export 'typography.dart';

// Component library
export 'components/app_button.dart';
export 'components/app_card.dart';

// Theme integration
export '../theme/colors.dart';

/// Design system version for tracking compatibility
const String designSystemVersion = '1.0.0';

/// Design system metadata
class DesignSystemInfo {
  static const String name = 'Maxed Out Life Design System';
  static const String version = designSystemVersion;
  static const String description = 'Comprehensive design system for consistent UI/UX';
  
  /// Supported component variants
  static const List<String> buttonVariants = [
    'primary',
    'secondary', 
    'tertiary',
    'destructive',
  ];
  
  static const List<String> cardVariants = [
    'standard',
    'elevated',
    'outlined',
    'glow',
  ];
  
  /// Available font families
  static const List<String> fontFamilies = [
    'Pirulen',      // Primary - headings
    'BITSUMISHI',   // Secondary - body text
    'Digital-7',    // Monospace - technical content
    'Orbitron',     // Display - special occasions
  ];
  
  /// Color palette categories
  static const List<String> colorCategories = [
    'Primary Colors',    // Main brand colors
    'Category Colors',   // Health, Wealth, Purpose, Connection
    'Custom Colors',     // User-defined category colors
    'System Colors',     // Success, error, warning, info
  ];
  
  /// Animation categories
  static const List<String> animationTypes = [
    'Micro-interactions', // Button presses, hover effects
    'Transitions',        // Page changes, modal appearances
    'Loading States',     // Progress indicators, spinners
    'Feedback',          // Success/error animations
  ];
}

/// Utility class for design system validation and debugging
class DesignSystemValidator {
  /// Validates that a color has sufficient contrast for accessibility
  static bool hasValidContrast(Color foreground, Color background) {
    final fgLuminance = foreground.computeLuminance();
    final bgLuminance = background.computeLuminance();
    
    final contrast = (fgLuminance > bgLuminance)
        ? (fgLuminance + 0.05) / (bgLuminance + 0.05)
        : (bgLuminance + 0.05) / (fgLuminance + 0.05);
    
    return contrast >= 4.5; // WCAG AA standard
  }
  
  /// Validates that a touch target meets minimum size requirements
  static bool hasValidTouchTarget(double width, double height) {
    const minSize = 44.0; // iOS/Android minimum touch target
    return width >= minSize && height >= minSize;
  }
  
  /// Validates that an animation duration is within recommended ranges
  static bool hasValidAnimationDuration(Duration duration) {
    const minDuration = Duration(milliseconds: 100);
    const maxDuration = Duration(milliseconds: 1000);
    return duration >= minDuration && duration <= maxDuration;
  }
}

/// Helper functions for common design system operations
class DesignSystemHelpers {
  /// Creates a glow effect box shadow with the specified color
  static List<BoxShadow> createGlow(Color color, {double intensity = 1.0}) {
    return [
      BoxShadow(
        color: color.withValues(alpha: 0.5 * intensity),
        blurRadius: 20 * intensity,
        spreadRadius: 2 * intensity,
      ),
      BoxShadow(
        color: color.withValues(alpha: 0.3 * intensity),
        blurRadius: 40 * intensity,
        spreadRadius: 4 * intensity,
      ),
    ];
  }

  /// Creates a subtle elevation shadow
  static List<BoxShadow> createElevation(double elevation) {
    return [
      BoxShadow(
        color: Colors.black.withValues(alpha: 0.1),
        blurRadius: elevation * 2,
        offset: Offset(0, elevation),
      ),
    ];
  }
  
  /// Interpolates between two colors based on a progress value (0.0 to 1.0)
  static Color interpolateColor(Color start, Color end, double progress) {
    return Color.lerp(start, end, progress.clamp(0.0, 1.0))!;
  }
  
  /// Calculates responsive size based on screen width
  static double responsiveSize(double baseSize, double screenWidth) {
    const baseWidth = 375.0; // iPhone SE width as baseline
    final scale = (screenWidth / baseWidth).clamp(0.8, 1.5);
    return baseSize * scale;
  }
}
