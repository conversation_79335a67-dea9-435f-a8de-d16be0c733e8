import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../design_tokens.dart';
import '../typography.dart';
import '../../theme/colors.dart';

/// Comprehensive button component for the Maxed Out Life design system.
/// 
/// Provides consistent button styling with multiple variants:
/// - Primary, Secondary, Tertiary styles
/// - Small, Medium, Large sizes
/// - Loading states and disabled states
/// - Icon support and custom styling
/// 
/// Example usage:
/// ```dart
/// AppButton.primary(
///   text: 'Get Started',
///   onPressed: () => print('Pressed'),
/// )
/// 
/// AppButton.secondary(
///   text: 'Cancel',
///   size: AppButtonSize.small,
///   icon: Icons.close,
///   onPressed: () => Navigator.pop(context),
/// )
/// ```
class AppButton extends StatefulWidget {
  /// Button text
  final String text;
  
  /// Callback when button is pressed
  final VoidCallback? onPressed;
  
  /// Button variant style
  final AppButtonVariant variant;
  
  /// Button size
  final AppButtonSize size;
  
  /// Optional icon to display
  final IconData? icon;
  
  /// Icon position relative to text
  final AppButtonIconPosition iconPosition;
  
  /// Whether button is in loading state
  final bool isLoading;
  
  /// Custom width (null for auto-sizing)
  final double? width;
  
  /// Custom color override
  final Color? customColor;
  
  /// Whether button should expand to fill available width
  final bool fullWidth;

  const AppButton({
    super.key,
    required this.text,
    required this.onPressed,
    this.variant = AppButtonVariant.primary,
    this.size = AppButtonSize.medium,
    this.icon,
    this.iconPosition = AppButtonIconPosition.left,
    this.isLoading = false,
    this.width,
    this.customColor,
    this.fullWidth = false,
  });

  /// Primary button with filled background
  const AppButton.primary({
    super.key,
    required this.text,
    required this.onPressed,
    this.size = AppButtonSize.medium,
    this.icon,
    this.iconPosition = AppButtonIconPosition.left,
    this.isLoading = false,
    this.width,
    this.customColor,
    this.fullWidth = false,
  }) : variant = AppButtonVariant.primary;

  /// Secondary button with outline style
  const AppButton.secondary({
    super.key,
    required this.text,
    required this.onPressed,
    this.size = AppButtonSize.medium,
    this.icon,
    this.iconPosition = AppButtonIconPosition.left,
    this.isLoading = false,
    this.width,
    this.customColor,
    this.fullWidth = false,
  }) : variant = AppButtonVariant.secondary;

  /// Tertiary button with text-only style
  const AppButton.tertiary({
    super.key,
    required this.text,
    required this.onPressed,
    this.size = AppButtonSize.medium,
    this.icon,
    this.iconPosition = AppButtonIconPosition.left,
    this.isLoading = false,
    this.width,
    this.customColor,
    this.fullWidth = false,
  }) : variant = AppButtonVariant.tertiary;

  /// Destructive button for dangerous actions
  const AppButton.destructive({
    super.key,
    required this.text,
    required this.onPressed,
    this.size = AppButtonSize.medium,
    this.icon,
    this.iconPosition = AppButtonIconPosition.left,
    this.isLoading = false,
    this.width,
    this.fullWidth = false,
  }) : variant = AppButtonVariant.destructive,
       customColor = null;

  @override
  State<AppButton> createState() => _AppButtonState();
}

class _AppButtonState extends State<AppButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: DesignTokens.durationFast,
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handleTapDown(TapDownDetails details) {
    if (widget.onPressed != null && !widget.isLoading) {
      setState(() => _isPressed = true);
      _animationController.forward();
      HapticFeedback.lightImpact();
    }
  }

  void _handleTapUp(TapUpDetails details) {
    _handleTapEnd();
  }

  void _handleTapCancel() {
    _handleTapEnd();
  }

  void _handleTapEnd() {
    if (_isPressed) {
      setState(() => _isPressed = false);
      _animationController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    final config = _getButtonConfig();
    final isDisabled = widget.onPressed == null || widget.isLoading;
    
    Widget button = AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Container(
            width: widget.fullWidth ? double.infinity : widget.width,
            height: config.height,
            decoration: BoxDecoration(
              color: isDisabled ? config.disabledBackgroundColor : config.backgroundColor,
              borderRadius: BorderRadius.circular(config.borderRadius),
              border: config.borderColor != null
                  ? Border.all(
                      color: isDisabled ? config.disabledBorderColor! : config.borderColor!,
                      width: DesignTokens.borderMedium,
                    )
                  : null,
              boxShadow: isDisabled ? null : config.shadows,
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: isDisabled ? null : widget.onPressed,
                onTapDown: _handleTapDown,
                onTapUp: _handleTapUp,
                onTapCancel: _handleTapCancel,
                borderRadius: BorderRadius.circular(config.borderRadius),
                child: Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: config.horizontalPadding,
                    vertical: config.verticalPadding,
                  ),
                  child: _buildButtonContent(config, isDisabled),
                ),
              ),
            ),
          ),
        );
      },
    );

    return button;
  }

  Widget _buildButtonContent(_ButtonConfig config, bool isDisabled) {
    if (widget.isLoading) {
      return _buildLoadingContent(config);
    }

    final textColor = isDisabled ? config.disabledTextColor : config.textColor;
    final iconColor = isDisabled ? config.disabledTextColor : config.textColor;

    if (widget.icon == null) {
      return Center(
        child: Text(
          widget.text,
          style: config.textStyle.copyWith(color: textColor),
          textAlign: TextAlign.center,
        ),
      );
    }

    final iconWidget = Icon(
      widget.icon,
      size: config.iconSize,
      color: iconColor,
    );

    final textWidget = Text(
      widget.text,
      style: config.textStyle.copyWith(color: textColor),
    );

    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: widget.iconPosition == AppButtonIconPosition.left
          ? [
              iconWidget,
              SizedBox(width: DesignTokens.spaceMd),
              Flexible(child: textWidget),
            ]
          : [
              Flexible(child: textWidget),
              SizedBox(width: DesignTokens.spaceMd),
              iconWidget,
            ],
    );
  }

  Widget _buildLoadingContent(_ButtonConfig config) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        SizedBox(
          width: config.iconSize,
          height: config.iconSize,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(config.textColor),
          ),
        ),
        SizedBox(width: DesignTokens.spaceMd),
        Text(
          'Loading...',
          style: config.textStyle.copyWith(color: config.textColor),
        ),
      ],
    );
  }

  _ButtonConfig _getButtonConfig() {
    final baseColor = widget.customColor ?? _getVariantColor();
    
    switch (widget.variant) {
      case AppButtonVariant.primary:
        return _ButtonConfig.primary(baseColor, widget.size);
      case AppButtonVariant.secondary:
        return _ButtonConfig.secondary(baseColor, widget.size);
      case AppButtonVariant.tertiary:
        return _ButtonConfig.tertiary(baseColor, widget.size);
      case AppButtonVariant.destructive:
        return _ButtonConfig.destructive(widget.size);
    }
  }

  Color _getVariantColor() {
    switch (widget.variant) {
      case AppButtonVariant.primary:
        return MolColors.cyan;
      case AppButtonVariant.secondary:
        return MolColors.blue;
      case AppButtonVariant.tertiary:
        return MolColors.purple;
      case AppButtonVariant.destructive:
        return MolColors.red;
    }
  }
}

/// Button variant styles
enum AppButtonVariant {
  primary,
  secondary,
  tertiary,
  destructive,
}

/// Button sizes
enum AppButtonSize {
  small,
  medium,
  large,
}

/// Icon position relative to text
enum AppButtonIconPosition {
  left,
  right,
}

/// Internal button configuration
class _ButtonConfig {
  final double height;
  final double horizontalPadding;
  final double verticalPadding;
  final double borderRadius;
  final Color backgroundColor;
  final Color textColor;
  final Color? borderColor;
  final Color disabledBackgroundColor;
  final Color disabledTextColor;
  final Color? disabledBorderColor;
  final TextStyle textStyle;
  final double iconSize;
  final List<BoxShadow>? shadows;

  const _ButtonConfig({
    required this.height,
    required this.horizontalPadding,
    required this.verticalPadding,
    required this.borderRadius,
    required this.backgroundColor,
    required this.textColor,
    this.borderColor,
    required this.disabledBackgroundColor,
    required this.disabledTextColor,
    this.disabledBorderColor,
    required this.textStyle,
    required this.iconSize,
    this.shadows,
  });

  factory _ButtonConfig.primary(Color color, AppButtonSize size) {
    final sizeConfig = _getSizeConfig(size);
    return _ButtonConfig(
      height: sizeConfig.height,
      horizontalPadding: sizeConfig.horizontalPadding,
      verticalPadding: sizeConfig.verticalPadding,
      borderRadius: DesignTokens.radius2xl,
      backgroundColor: color,
      textColor: Colors.black,
      disabledBackgroundColor: Colors.grey[800]!,
      disabledTextColor: Colors.grey[600]!,
      textStyle: sizeConfig.textStyle,
      iconSize: sizeConfig.iconSize,
      shadows: [
        BoxShadow(
          color: color.withValues(alpha: 0.3),
          blurRadius: 8,
          offset: const Offset(0, 4),
        ),
      ],
    );
  }

  factory _ButtonConfig.secondary(Color color, AppButtonSize size) {
    final sizeConfig = _getSizeConfig(size);
    return _ButtonConfig(
      height: sizeConfig.height,
      horizontalPadding: sizeConfig.horizontalPadding,
      verticalPadding: sizeConfig.verticalPadding,
      borderRadius: DesignTokens.radius2xl,
      backgroundColor: Colors.transparent,
      textColor: color,
      borderColor: color,
      disabledBackgroundColor: Colors.transparent,
      disabledTextColor: Colors.grey[600]!,
      disabledBorderColor: Colors.grey[600]!,
      textStyle: sizeConfig.textStyle,
      iconSize: sizeConfig.iconSize,
    );
  }

  factory _ButtonConfig.tertiary(Color color, AppButtonSize size) {
    final sizeConfig = _getSizeConfig(size);
    return _ButtonConfig(
      height: sizeConfig.height,
      horizontalPadding: sizeConfig.horizontalPadding,
      verticalPadding: sizeConfig.verticalPadding,
      borderRadius: DesignTokens.radiusLg,
      backgroundColor: Colors.transparent,
      textColor: color,
      disabledBackgroundColor: Colors.transparent,
      disabledTextColor: Colors.grey[600]!,
      textStyle: sizeConfig.textStyle,
      iconSize: sizeConfig.iconSize,
    );
  }

  factory _ButtonConfig.destructive(AppButtonSize size) {
    final sizeConfig = _getSizeConfig(size);
    return _ButtonConfig(
      height: sizeConfig.height,
      horizontalPadding: sizeConfig.horizontalPadding,
      verticalPadding: sizeConfig.verticalPadding,
      borderRadius: DesignTokens.radius2xl,
      backgroundColor: MolColors.red,
      textColor: Colors.white,
      disabledBackgroundColor: Colors.grey[800]!,
      disabledTextColor: Colors.grey[600]!,
      textStyle: sizeConfig.textStyle,
      iconSize: sizeConfig.iconSize,
      shadows: [
        BoxShadow(
          color: MolColors.red.withValues(alpha: 0.3),
          blurRadius: 8,
          offset: const Offset(0, 4),
        ),
      ],
    );
  }

  static _SizeConfig _getSizeConfig(AppButtonSize size) {
    switch (size) {
      case AppButtonSize.small:
        return const _SizeConfig(
          height: DesignTokens.buttonHeightSm,
          horizontalPadding: DesignTokens.spaceLg,
          verticalPadding: DesignTokens.spaceMd,
          textStyle: AppTypography.buttonSmall,
          iconSize: DesignTokens.iconSizeSm,
        );
      case AppButtonSize.medium:
        return const _SizeConfig(
          height: DesignTokens.buttonHeightMd,
          horizontalPadding: DesignTokens.spaceXl,
          verticalPadding: DesignTokens.spaceLg,
          textStyle: AppTypography.buttonMedium,
          iconSize: DesignTokens.iconSizeMd,
        );
      case AppButtonSize.large:
        return const _SizeConfig(
          height: DesignTokens.buttonHeightLg,
          horizontalPadding: DesignTokens.space2xl,
          verticalPadding: DesignTokens.spaceXl,
          textStyle: AppTypography.buttonLarge,
          iconSize: DesignTokens.iconSizeLg,
        );
    }
  }
}

class _SizeConfig {
  final double height;
  final double horizontalPadding;
  final double verticalPadding;
  final TextStyle textStyle;
  final double iconSize;

  const _SizeConfig({
    required this.height,
    required this.horizontalPadding,
    required this.verticalPadding,
    required this.textStyle,
    required this.iconSize,
  });
}
