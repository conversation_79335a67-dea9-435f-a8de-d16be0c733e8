import 'package:flutter/material.dart';
import '../design_tokens.dart';
import '../../theme/colors.dart';

/// Comprehensive card component for the Maxed Out Life design system.
/// 
/// Provides consistent card styling with multiple variants:
/// - Standard, Elevated, Outlined styles
/// - Glow effects for neon aesthetic
/// - Customizable padding and margins
/// - Optional header and footer sections
/// 
/// Example usage:
/// ```dart
/// AppCard(
///   child: Text('Card content'),
/// )
/// 
/// AppCard.elevated(
///   glowColor: MolColors.cyan,
///   header: Text('Header'),
///   child: Text('Content'),
///   footer: AppButton.primary(text: 'Action', onPressed: () {}),
/// )
/// ```
class AppCard extends StatelessWidget {
  /// Card content
  final Widget child;
  
  /// Card variant style
  final AppCardVariant variant;
  
  /// Optional header widget
  final Widget? header;
  
  /// Optional footer widget
  final Widget? footer;
  
  /// Custom background color
  final Color? backgroundColor;
  
  /// Glow color for neon effect
  final Color? glowColor;
  
  /// Custom padding inside the card
  final EdgeInsets? padding;
  
  /// Custom margin around the card
  final EdgeInsets? margin;
  
  /// Custom border radius
  final double? borderRadius;
  
  /// Custom width
  final double? width;
  
  /// Custom height
  final double? height;
  
  /// Callback when card is tapped
  final VoidCallback? onTap;
  
  /// Whether the card should be clickable
  final bool isClickable;

  const AppCard({
    super.key,
    required this.child,
    this.variant = AppCardVariant.standard,
    this.header,
    this.footer,
    this.backgroundColor,
    this.glowColor,
    this.padding,
    this.margin,
    this.borderRadius,
    this.width,
    this.height,
    this.onTap,
    this.isClickable = false,
  });

  /// Standard card with subtle background
  const AppCard.standard({
    super.key,
    required this.child,
    this.header,
    this.footer,
    this.backgroundColor,
    this.padding,
    this.margin,
    this.borderRadius,
    this.width,
    this.height,
    this.onTap,
    this.isClickable = false,
  }) : variant = AppCardVariant.standard,
       glowColor = null;

  /// Elevated card with shadow
  const AppCard.elevated({
    super.key,
    required this.child,
    this.header,
    this.footer,
    this.backgroundColor,
    this.glowColor,
    this.padding,
    this.margin,
    this.borderRadius,
    this.width,
    this.height,
    this.onTap,
    this.isClickable = false,
  }) : variant = AppCardVariant.elevated;

  /// Outlined card with border
  const AppCard.outlined({
    super.key,
    required this.child,
    this.header,
    this.footer,
    this.backgroundColor,
    this.glowColor,
    this.padding,
    this.margin,
    this.borderRadius,
    this.width,
    this.height,
    this.onTap,
    this.isClickable = false,
  }) : variant = AppCardVariant.outlined;

  /// Glow card with neon effect
  const AppCard.glow({
    super.key,
    required this.child,
    required this.glowColor,
    this.header,
    this.footer,
    this.backgroundColor,
    this.padding,
    this.margin,
    this.borderRadius,
    this.width,
    this.height,
    this.onTap,
    this.isClickable = false,
  }) : variant = AppCardVariant.glow;

  @override
  Widget build(BuildContext context) {
    final config = _getCardConfig();
    
    Widget card = Container(
      width: width,
      height: height,
      margin: margin ?? config.margin,
      decoration: BoxDecoration(
        color: backgroundColor ?? config.backgroundColor,
        borderRadius: BorderRadius.circular(borderRadius ?? config.borderRadius),
        border: config.border,
        boxShadow: config.shadows,
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(borderRadius ?? config.borderRadius),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Header section
            if (header != null)
              Container(
                padding: EdgeInsets.fromLTRB(
                  (padding ?? config.padding).left,
                  (padding ?? config.padding).top,
                  (padding ?? config.padding).right,
                  DesignTokens.spaceMd,
                ),
                decoration: BoxDecoration(
                  color: config.headerBackgroundColor,
                  border: config.headerBorder,
                ),
                child: header!,
              ),
            
            // Main content
            Flexible(
              child: Container(
                padding: padding ?? config.padding,
                child: child,
              ),
            ),
            
            // Footer section
            if (footer != null)
              Container(
                padding: EdgeInsets.fromLTRB(
                  (padding ?? config.padding).left,
                  DesignTokens.spaceMd,
                  (padding ?? config.padding).right,
                  (padding ?? config.padding).bottom,
                ),
                decoration: BoxDecoration(
                  color: config.footerBackgroundColor,
                  border: config.footerBorder,
                ),
                child: footer!,
              ),
          ],
        ),
      ),
    );

    // Add tap functionality if needed
    if (isClickable || onTap != null) {
      card = Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(borderRadius ?? config.borderRadius),
          child: card,
        ),
      );
    }

    return card;
  }

  _CardConfig _getCardConfig() {
    switch (variant) {
      case AppCardVariant.standard:
        return _CardConfig.standard();
      case AppCardVariant.elevated:
        return _CardConfig.elevated(glowColor);
      case AppCardVariant.outlined:
        return _CardConfig.outlined(glowColor);
      case AppCardVariant.glow:
        return _CardConfig.glow(glowColor!);
    }
  }
}

/// Card variant styles
enum AppCardVariant {
  standard,
  elevated,
  outlined,
  glow,
}

/// Internal card configuration
class _CardConfig {
  final Color backgroundColor;
  final Color? headerBackgroundColor;
  final Color? footerBackgroundColor;
  final double borderRadius;
  final EdgeInsets padding;
  final EdgeInsets margin;
  final Border? border;
  final Border? headerBorder;
  final Border? footerBorder;
  final List<BoxShadow>? shadows;

  const _CardConfig({
    required this.backgroundColor,
    // ignore: unused_element_parameter
    this.headerBackgroundColor, // Reserved for future header styling
    // ignore: unused_element_parameter
    this.footerBackgroundColor, // Reserved for future footer styling
    required this.borderRadius,
    required this.padding,
    required this.margin,
    this.border,
    // ignore: unused_element_parameter
    this.headerBorder, // Reserved for future header border styling
    // ignore: unused_element_parameter
    this.footerBorder, // Reserved for future footer border styling
    this.shadows,
  });

  factory _CardConfig.standard() {
    return _CardConfig(
      backgroundColor: Colors.grey[900]!,
      borderRadius: DesignTokens.radiusXl,
      padding: const EdgeInsets.all(DesignTokens.spaceXl),
      margin: const EdgeInsets.all(DesignTokens.spaceMd),
    );
  }

  factory _CardConfig.elevated(Color? glowColor) {
    final shadowColor = glowColor ?? Colors.black;
    return _CardConfig(
      backgroundColor: Colors.grey[900]!,
      borderRadius: DesignTokens.radiusXl,
      padding: const EdgeInsets.all(DesignTokens.spaceXl),
      margin: const EdgeInsets.all(DesignTokens.spaceMd),
      shadows: [
        BoxShadow(
          color: shadowColor.withValues(alpha: 0.2),
          blurRadius: 16,
          offset: const Offset(0, 8),
        ),
        if (glowColor != null)
          BoxShadow(
            color: glowColor.withValues(alpha: 0.1),
            blurRadius: 32,
            spreadRadius: 2,
          ),
      ],
    );
  }

  factory _CardConfig.outlined(Color? glowColor) {
    final borderColor = glowColor ?? Colors.grey[700]!;
    return _CardConfig(
      backgroundColor: Colors.grey[900]!,
      borderRadius: DesignTokens.radiusXl,
      padding: const EdgeInsets.all(DesignTokens.spaceXl),
      margin: const EdgeInsets.all(DesignTokens.spaceMd),
      border: Border.all(
        color: borderColor,
        width: DesignTokens.borderMedium,
      ),
    );
  }

  factory _CardConfig.glow(Color glowColor) {
    return _CardConfig(
      backgroundColor: Colors.black.withValues(alpha: 0.8),
      borderRadius: DesignTokens.radiusXl,
      padding: const EdgeInsets.all(DesignTokens.spaceXl),
      margin: const EdgeInsets.all(DesignTokens.spaceMd),
      border: Border.all(
        color: glowColor,
        width: DesignTokens.borderMedium,
      ),
      shadows: [
        BoxShadow(
          color: glowColor.withValues(alpha: 0.5),
          blurRadius: 20,
          spreadRadius: 2,
        ),
        BoxShadow(
          color: glowColor.withValues(alpha: 0.3),
          blurRadius: 40,
          spreadRadius: 4,
        ),
      ],
    );
  }
}

/// Specialized card for category-based content
class CategoryCard extends StatelessWidget {
  final String category;
  final Widget child;
  final List<String>? customCategories;
  final VoidCallback? onTap;
  final Widget? header;
  final Widget? footer;

  const CategoryCard({
    super.key,
    required this.category,
    required this.child,
    this.customCategories,
    this.onTap,
    this.header,
    this.footer,
  });

  @override
  Widget build(BuildContext context) {
    // Use the existing category color system
    final color = _getCategoryColor(category, customCategories);
    
    return AppCard.glow(
      glowColor: color,
      header: header,
      footer: footer,
      onTap: onTap,
      isClickable: onTap != null,
      child: child,
    );
  }

  Color _getCategoryColor(String category, List<String>? customCategories) {
    // Use the centralized color system for consistency
    return getCategoryColor(category, customCategories: customCategories);
  }
}

/// Specialized card for bounties and rewards
class BountyCard extends StatelessWidget {
  final String title;
  final String description;
  final bool isEpic;
  final bool isCompleted;
  final Widget? child;
  final VoidCallback? onTap;

  const BountyCard({
    super.key,
    required this.title,
    required this.description,
    this.isEpic = false,
    this.isCompleted = false,
    this.child,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final glowColor = isEpic ? MolColors.purple : MolColors.blue;
    final opacity = isCompleted ? 0.6 : 1.0;
    
    return Opacity(
      opacity: opacity,
      child: AppCard.glow(
        glowColor: glowColor,
        onTap: onTap,
        isClickable: onTap != null,
        header: Container(
          padding: const EdgeInsets.symmetric(
            horizontal: DesignTokens.spaceMd,
            vertical: DesignTokens.spaceSm,
          ),
          decoration: BoxDecoration(
            color: glowColor.withValues(alpha: 0.2),
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(DesignTokens.radiusXl),
              topRight: Radius.circular(DesignTokens.radiusXl),
            ),
          ),
          child: Row(
            children: [
              Icon(
                isEpic ? Icons.star : Icons.flash_on,
                color: glowColor,
                size: DesignTokens.iconSizeMd,
              ),
              const SizedBox(width: DesignTokens.spaceMd),
              Expanded(
                child: Text(
                  title,
                  style: const TextStyle(
                    fontFamily: 'BITSUMISHI',
                    fontSize: DesignTokens.fontSizeLg,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ),
              if (isCompleted)
                const Icon(
                  Icons.check_circle,
                  color: MolColors.green,
                  size: DesignTokens.iconSizeMd,
                ),
            ],
          ),
        ),
        child: child ?? Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              description,
              style: const TextStyle(
                fontFamily: 'BITSUMISHI',
                fontSize: DesignTokens.fontSizeBase,
                color: Colors.white70,
                height: 1.5,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
