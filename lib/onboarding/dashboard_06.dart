import 'package:flutter/material.dart';
import '../models/user_model.dart';
import '../models/onboarding_progress.dart';
import '../widgets/onboarding_progress_indicator.dart';
import '../widgets/interactive_tutorial_overlay.dart';
import '../home/<USER>';

class Dashboard extends StatefulWidget {
  final User user;
  final void Function(User?) onComplete;

  const Dashboard({
    super.key,
    required this.user,
    required this.onComplete,
  });

  @override
  State<Dashboard> createState() => _DashboardState();
}

class _DashboardState extends State<Dashboard> {
  late User _user;

  @override
  void initState() {
    super.initState();
    _user = widget.user;
  }

  void _openMusicModal() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (_) => const MusicControlModal(),
      isScrollControlled: false,
    );
  }

  void _completeOnboarding() {
    final updatedUser = _user.copyWith(
      onboardingProgress: _user.onboardingProgress.copyWith(
        hasCompletedWelcome: true,
        hasCreatedNorthStar: true,
        hasSetCategories: true,
        hasMetCoaches: true,
        hasSetHabits: true,
      ),
    );

    // Update the local user state
    _user = updatedUser;

    // Show interactive tutorial after onboarding completion
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _showInteractiveTutorial();
    });
  }

  void _showInteractiveTutorial() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => InteractiveTutorialOverlay(
        user: _user,
        onComplete: () {
          Navigator.of(context).pop();
          // Mark tutorial as completed
          final tutorialCompletedUser = _user.copyWith(
            onboardingProgress: _user.onboardingProgress.copyWith(
              hasCompletedTutorial: true,
            ),
          );
          widget.onComplete(tutorialCompletedUser);
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Your Dashboard',
                style: TextStyle(
                  color: Colors.white,
                  fontFamily: 'Pirulen',
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              const Text(
                'Track your progress and stay motivated',
                style: TextStyle(
                  color: Colors.white70,
                  fontFamily: 'Bitsumishi',
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 40),
              
              // Progress Indicator
              OnboardingProgressIndicator(
                progress: OnboardingProgress(
                  hasCompletedWelcome: _user.onboardingProgress.hasCompletedWelcome,
                  hasCreatedNorthStar: _user.onboardingProgress.hasCreatedNorthStar,
                  hasSetCategories: _user.onboardingProgress.hasSetCategories,
                  hasMetCoaches: _user.onboardingProgress.hasMetCoaches,
                  hasSetHabits: _user.onboardingProgress.hasSetHabits,
                  hasCompletedTutorial: _user.onboardingProgress.hasCompletedTutorial,
                ),
                onStepTap: () {
                  // Show tooltip with step details
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('You\'ve completed all onboarding steps!'),
                      duration: Duration(seconds: 2),
                    ),
                  );
                },
              ),
              
              const SizedBox(height: 40),
              
              // Dashboard Content
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildSection(
                        title: 'Your North Star',
                        content: _buildNorthStarSection(),
                      ),
                      const SizedBox(height: 24),
                      _buildSection(
                        title: 'Daily Habits',
                        content: _buildHabitsSection(),
                      ),
                      const SizedBox(height: 24),
                      _buildSection(
                        title: 'Categories',
                        content: _buildCategoriesSection(),
                      ),
                    ],
                  ),
                ),
              ),

              // Complete Button
              const SizedBox(height: 24),
              Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    // Primary glow
                    BoxShadow(
                      color: (_user.northStarQuest?.glowColor ?? Colors.cyan).withValues(alpha: 0.6),
                      blurRadius: 20,
                      spreadRadius: 2,
                    ),
                    // Secondary glow
                    BoxShadow(
                      color: (_user.northStarQuest?.glowColor ?? Colors.cyan).withValues(alpha: 0.4),
                      blurRadius: 30,
                      spreadRadius: 4,
                    ),
                  ],
                ),
                child: ElevatedButton(
                  onPressed: _completeOnboarding,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _user.northStarQuest?.glowColor ?? Colors.cyan,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: Text(
                    'Complete Onboarding',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      fontFamily: 'Bitsumishi',
                      color: Colors.white,
                      shadows: [
                        // Black border effect
                        Shadow(
                          offset: const Offset(-1, -1),
                          color: Colors.black,
                        ),
                        Shadow(
                          offset: const Offset(1, -1),
                          color: Colors.black,
                        ),
                        Shadow(
                          offset: const Offset(1, 1),
                          color: Colors.black,
                        ),
                        Shadow(
                          offset: const Offset(-1, 1),
                          color: Colors.black,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      // ─── Floating "Music" Button at bottom right ───
      floatingActionButton: SizedBox(
        width: 56,
        height: 56,
        child: FloatingActionButton(
          heroTag: "dashboard_music_fab", // Unique hero tag
          backgroundColor: Colors.black87,
          onPressed: _openMusicModal,
          child: const Icon(Icons.music_note, color: Colors.cyanAccent, size: 24),
        ),
      ),
    );
  }

  Widget _buildSection({
    required String title,
    required Widget content,
  }) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.grey[800]!,
          width: 2,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              color: Colors.white,
              fontFamily: 'Pirulen',
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          content,
        ],
      ),
    );
  }

  Widget _buildNorthStarSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _user.northStarQuest?.glowColor ?? Colors.blue,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                _user.northStarQuest?.icon ?? '⭐',
                style: const TextStyle(fontSize: 24),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Text(
                _user.northStarQuest?.title ?? 'No North Star Quest Set',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildHabitsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ..._user.dailyHabits.map((habit) => Padding(
          padding: const EdgeInsets.only(bottom: 12.0),
          child: Row(
            children: [
              Icon(
                Icons.check_circle_outline,
                color: Colors.green,
                size: 20,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  habit.name,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                  ),
                ),
              ),
            ],
          ),
        )),
      ],
    );
  }

  Widget _buildCategoriesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ..._user.categories.entries.map((entry) => Padding(
          padding: const EdgeInsets.only(bottom: 12.0),
          child: Row(
            children: [
              Icon(
                Icons.category,
                color: Colors.cyanAccent,
                size: 20,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  '${entry.key}: ${entry.value} XP',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                  ),
                ),
              ),
            ],
          ),
        )),
      ],
    );
  }
} 