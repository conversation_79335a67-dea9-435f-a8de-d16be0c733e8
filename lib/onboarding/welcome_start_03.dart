import 'package:flutter/material.dart';
import 'dart:async';
import 'dart:convert';
import '../models/user_model.dart';
import '../models/onboarding_progress.dart';
import '../widgets/onboarding_progress_indicator.dart';
import '../widgets/auth_username_gender_modal.dart';
import '../widgets/firebase_auth_email_password_modal.dart';
import '../widgets/awaiting_email_verification_modal.dart';
import '../widgets/confirm_email_verification_modal.dart';
import '../widgets/auth_signin_modal.dart';
import '../controller/user_controller2.dart';
import '../controller/firebase_auth_controller.dart';
import '../services/comprehensive_logging_service.dart';
import '../services/bulletproof_storage_service.dart';
import '../services/firebase_auth_service.dart';
import '../services/saved_accounts_service.dart';
import '../home/<USER>';

import 'package:provider/provider.dart';

class WelcomeStartPage extends StatefulWidget {
  final User user;
  final void Function(User) onContinue;

  const WelcomeStartPage({
    super.key,
    required this.user,
    required this.onContinue,
  });

  @override
  State<WelcomeStartPage> createState() => _WelcomeStartPageState();
}

class _WelcomeStartPageState extends State<WelcomeStartPage> {

  // 🛡️ Bulletproof onboarding state tracking
  final BulletproofStorageService _storage = BulletproofStorageService();
  String? _onboardingSessionId;
  final List<String> _onboardingLog = [];
  bool _onboardingInProgress = false;

  // 🛡️ THIRD LAYER: Advanced onboarding failsafes
  bool _firebaseInitialized = false;
  bool _controllerInitialized = false;
  bool _storageInitialized = false;
  int _onboardingAttempts = 0;
  final Map<String, dynamic> _onboardingStateSnapshot = {};
  Timer? _onboardingWatchdog;
  DateTime? _onboardingStartTime;

  // 🎯 PHASE 1: Signup attempt tracking & debugging
  int _signupAttempts = 0;
  final List<Map<String, dynamic>> _signupHistory = [];
  String? _currentSignupSessionId;

  void _openMusicModal() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (_) => const MusicControlModal(),
      isScrollControlled: true,
      enableDrag: true,
      showDragHandle: true,
    );
  }

  /// Check if the current user is a guest user
  bool _isGuestUser() {
    return widget.user.id.startsWith('guest_');
  }

  @override
  void initState() {
    super.initState();
    _initializeOnboardingSessionWithThirdLayer();

    // Check if this is a guest user and auto-proceed
    if (_isGuestUser()) {
      _addOnboardingLog('🎭 Guest user detected, auto-proceeding to next step');
      // Delay to allow UI to render first
      Future.delayed(const Duration(milliseconds: 500), () {
        if (mounted) {
          widget.onContinue(widget.user);
        }
      });
    }
  }

  /// 🚀 Initialize onboarding session with THIRD LAYER debugging
  void _initializeOnboardingSessionWithThirdLayer() {
    _onboardingSessionId = DateTime.now().millisecondsSinceEpoch.toString();
    _onboardingStartTime = DateTime.now();
    _addOnboardingLog('🚀 ONBOARDING SESSION STARTED WITH THIRD LAYER FAILSAFES');
    _addOnboardingLog('👤 User ID: ${widget.user.id}');
    _addOnboardingLog('🆔 Session ID: $_onboardingSessionId');
    _addOnboardingLog('⏰ Start Time: ${_onboardingStartTime!.toIso8601String()}');

    // THIRD LAYER: Initialize all systems
    _initializeThirdLayerSystems();

    // THIRD LAYER: Start onboarding watchdog
    _startOnboardingWatchdog();
  }

  /// 🛡️ THIRD LAYER: Initialize all systems
  void _initializeThirdLayerSystems() {
    _addOnboardingLog('🛡️ THIRD LAYER: Initializing all systems');

    // Initialize Firebase
    FirebaseAuthService.initialize().then((success) {
      _firebaseInitialized = success;
      _addOnboardingLog('🔥 Firebase initialized: $success');
      _captureOnboardingStateSnapshot('firebase_init');
    }).catchError((e) {
      _addOnboardingLog('💥 Firebase initialization failed: $e');
      _firebaseInitialized = false;
    });

    // Initialize controller
    try {
      if (mounted) {
        final firebaseController = Provider.of<FirebaseAuthController>(context, listen: false);
        firebaseController.initialize().then((_) {
          _controllerInitialized = true;
          _addOnboardingLog('🎮 Controller initialized: true');
          _captureOnboardingStateSnapshot('controller_init');
        }).catchError((e) {
          _addOnboardingLog('💥 Controller initialization failed: $e');
          _controllerInitialized = false;
        });
      }
    } catch (e) {
      _addOnboardingLog('💥 Controller initialization exception: $e');
      _controllerInitialized = false;
    }

    // Initialize storage
    try {
      _storage.write('init_test_$_onboardingSessionId', 'test').then((_) {
        _storageInitialized = true;
        _addOnboardingLog('💾 Storage initialized: true');
        _captureOnboardingStateSnapshot('storage_init');
      }).catchError((e) {
        _addOnboardingLog('💥 Storage initialization failed: $e');
        _storageInitialized = false;
      });
    } catch (e) {
      _addOnboardingLog('💥 Storage initialization exception: $e');
      _storageInitialized = false;
    }
  }

  /// 🛡️ THIRD LAYER: Start onboarding watchdog
  void _startOnboardingWatchdog() {
    _addOnboardingLog('🛡️ THIRD LAYER: Starting onboarding watchdog');

    _onboardingWatchdog = Timer.periodic(const Duration(seconds: 30), (timer) {
      if (!mounted) {
        timer.cancel();
        return;
      }

      _addOnboardingLog('🐕 Watchdog check - Onboarding in progress: $_onboardingInProgress');
      _addOnboardingLog('🐕 Firebase initialized: $_firebaseInitialized');
      _addOnboardingLog('🐕 Controller initialized: $_controllerInitialized');
      _addOnboardingLog('🐕 Storage initialized: $_storageInitialized');

      // Check for stuck onboarding
      if (_onboardingInProgress && _onboardingStartTime != null) {
        final elapsed = DateTime.now().difference(_onboardingStartTime!);
        if (elapsed.inMinutes > 20) {
          _addOnboardingLog('🚨 Onboarding stuck for ${elapsed.inMinutes} minutes - resetting');
          _resetOnboardingState();
        }
      }
    });
  }

  /// 🛡️ THIRD LAYER: Reset onboarding state
  void _resetOnboardingState() {
    _addOnboardingLog('🔄 THIRD LAYER: Resetting onboarding state');
    _onboardingInProgress = false;
    _onboardingAttempts = 0;
    _captureOnboardingStateSnapshot('state_reset');
  }

  /// 📸 THIRD LAYER: Capture onboarding state snapshot
  void _captureOnboardingStateSnapshot(String phase) {
    try {
      _onboardingStateSnapshot[phase] = {
        'timestamp': DateTime.now().toIso8601String(),
        'onboarding_in_progress': _onboardingInProgress,
        'onboarding_attempts': _onboardingAttempts,
        'firebase_initialized': _firebaseInitialized,
        'controller_initialized': _controllerInitialized,
        'storage_initialized': _storageInitialized,
        'widget_mounted': mounted,
      };

      _addOnboardingLog('📸 State snapshot captured: $phase');

    } catch (e) {
      _addOnboardingLog('⚠️ Failed to capture state snapshot: $e');
    }
  }

  /// 📝 Add onboarding log entry
  void _addOnboardingLog(String message) {
    final timestamp = DateTime.now().toIso8601String();
    final logEntry = '[$timestamp] $message';
    _onboardingLog.add(logEntry);
    print('🔍 Onboarding: $logEntry');
    ComprehensiveLoggingService.logInfo('Onboarding: $message');
  }

  /// 💾 Save onboarding debug log
  void _saveOnboardingLog() {
    try {
      final debugData = {
        'session_id': _onboardingSessionId,
        'user_id': widget.user.id,
        'onboarding_log': _onboardingLog,
        'timestamp': DateTime.now().toIso8601String(),
      };

      _storage.write('onboarding_debug_$_onboardingSessionId', debugData.toString());
      _addOnboardingLog('💾 Onboarding log saved to storage');
    } catch (e) {
      _addOnboardingLog('❌ Failed to save onboarding log: $e');
    }
  }

  /// 🎯 PHASE 1: Track signup attempt with comprehensive debugging
  void _trackSignupAttempt(String stage) {
    _signupAttempts++;
    _currentSignupSessionId = DateTime.now().millisecondsSinceEpoch.toString();

    final attemptData = {
      'attempt_number': _signupAttempts,
      'session_id': _currentSignupSessionId,
      'stage': stage,
      'timestamp': DateTime.now().toIso8601String(),
      'firebase_initialized': _firebaseInitialized,
      'controller_initialized': _controllerInitialized,
      'storage_initialized': _storageInitialized,
    };

    _signupHistory.add(attemptData);

    _addOnboardingLog('🎯 SIGNUP ATTEMPT #$_signupAttempts: $stage');
    _addOnboardingLog('📊 Session ID: $_currentSignupSessionId');
    _addOnboardingLog('🔧 System Status: Firebase=$_firebaseInitialized, Controller=$_controllerInitialized, Storage=$_storageInitialized');

    // Store signup history for debugging
    _storage.write('signup_history', jsonEncode(_signupHistory));
  }

  /// 🎯 PHASE 1: Log signup error with context
  void _logSignupError(String stage, String error, {Map<String, dynamic>? context}) {
    final errorData = {
      'session_id': _currentSignupSessionId,
      'stage': stage,
      'error': error,
      'timestamp': DateTime.now().toIso8601String(),
      'context': context ?? {},
    };

    _addOnboardingLog('💥 SIGNUP ERROR at $stage: $error');
    if (context != null) {
      _addOnboardingLog('📋 Error Context: $context');
    }

    // Store error for debugging
    _storage.write('signup_errors', jsonEncode(errorData));
  }

  void _showSignUp(BuildContext context) {
    _addOnboardingLog('🔄 Starting signup flow with THIRD LAYER failsafes');

    // 🎯 PHASE 1: Track signup attempt
    _trackSignupAttempt('signup_start');

    // THIRD LAYER: Pre-signup validation
    if (!_performPreSignupValidation()) {
      return;
    }

    // Failsafe 1: Check if onboarding already in progress
    if (_onboardingInProgress) {
      _addOnboardingLog('⚠️ Onboarding already in progress, ignoring duplicate request');
      return;
    }

    _onboardingInProgress = true;
    _onboardingAttempts++;
    _captureOnboardingStateSnapshot('signup_start');

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Align(
        alignment: Alignment.topCenter,
        child: Padding(
          padding: const EdgeInsets.only(top: 100),
          child: AuthUsernameGenderModal(
            onBack: () {
              _addOnboardingLog('❌ Sign up modal closed by user');
              _onboardingInProgress = false; // Reset onboarding state
              Navigator.of(context).pop(); // Close sign up modal
            },
            onContinue: (username, gender) {
              _addOnboardingLog('✅ Username/gender modal completed - username: $username, gender: $gender');

              // Failsafe 2: Validate username and gender
              if (username.isEmpty || gender.isEmpty) {
                _addOnboardingLog('❌ Invalid username or gender received');
                _showErrorDialog(context, 'Invalid username or gender. Please try again.');
                return;
              }

              // THIRD LAYER: Deep validation
              if (!_performDeepUsernameGenderValidation(username, gender)) {
                return;
              }

              Navigator.of(context).pop();
              _showEmailPasswordModalWithFailsafes(context, username, gender);
            },
          ),
        ),
      ),
    );
  }

  /// 🛡️ THIRD LAYER: Pre-signup validation
  bool _performPreSignupValidation() {
    _addOnboardingLog('🛡️ THIRD LAYER: Performing pre-signup validation');

    // Check if systems are initialized
    if (!_firebaseInitialized) {
      _addOnboardingLog('❌ Firebase not initialized');
      _showBulletproofErrorDialog(context, 'System not ready. Please wait a moment and try again.');
      return false;
    }

    if (!_controllerInitialized) {
      _addOnboardingLog('❌ Controller not initialized');
      _showBulletproofErrorDialog(context, 'System not ready. Please wait a moment and try again.');
      return false;
    }

    if (!_storageInitialized) {
      _addOnboardingLog('❌ Storage not initialized');
      _showBulletproofErrorDialog(context, 'System not ready. Please wait a moment and try again.');
      return false;
    }

    // Check if too many attempts
    if (_onboardingAttempts >= 5) {
      _addOnboardingLog('❌ Too many onboarding attempts');
      _showBulletproofErrorDialog(context, 'Too many attempts. Please restart the app and try again.');
      return false;
    }

    _addOnboardingLog('✅ Pre-signup validation passed');
    return true;
  }

  /// 🛡️ THIRD LAYER: Deep username/gender validation
  bool _performDeepUsernameGenderValidation(String username, String gender) {
    _addOnboardingLog('🛡️ THIRD LAYER: Performing deep username/gender validation');

    // Validate username format
    if (username.length < 3) {
      _addOnboardingLog('❌ Username too short');
      _showBulletproofErrorDialog(context, 'Username must be at least 3 characters long.');
      return false;
    }

    if (username.length > 15) {
      _addOnboardingLog('❌ Username too long');
      _showBulletproofErrorDialog(context, 'Username must be 15 characters or less.');
      return false;
    }

    // Check for invalid characters
    if (!RegExp(r'^[a-zA-Z0-9_]+$').hasMatch(username)) {
      _addOnboardingLog('❌ Username contains invalid characters');
      _showBulletproofErrorDialog(context, 'Username can only contain letters, numbers, and underscores.');
      return false;
    }

    // Validate gender
    if (!['Male', 'Female', 'Non-Gender'].contains(gender)) {
      _addOnboardingLog('❌ Invalid gender value');
      _showBulletproofErrorDialog(context, 'Invalid gender selection. Please try again.');
      return false;
    }

    _addOnboardingLog('✅ Deep username/gender validation passed');
    return true;
  }

  /// 🛡️ Show email/password modal with bulletproof failsafes
  void _showEmailPasswordModalWithFailsafes(BuildContext context, String username, String gender) {
    _addOnboardingLog('🔄 Showing email/password modal with failsafes');

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Align(
        alignment: Alignment.topCenter,
        child: Padding(
          padding: const EdgeInsets.only(top: 100),
          child: FirebaseAuthEmailPasswordModal(
            onContinue: (email, password) async {
              _addOnboardingLog('✅ Email/password modal completed - email: $email');
              _trackSignupAttempt('email_password_completed');

              // Failsafe 1: Validate email and password
              if (email.isEmpty || password.isEmpty) {
                _addOnboardingLog('❌ Invalid email or password received');
                _logSignupError('email_password_validation', 'Empty email or password', context: {
                  'email_empty': email.isEmpty,
                  'password_empty': password.isEmpty,
                });
                _showErrorDialog(context, 'Invalid email or password. Please try again.');
                return;
              }

              if (!email.contains('@')) {
                _addOnboardingLog('❌ Invalid email format');
                _logSignupError('email_validation', 'Invalid email format', context: {
                  'email': email,
                });
                _showErrorDialog(context, 'Invalid email format. Please try again.');
                return;
              }

              Navigator.of(context).pop();

              // Failsafe 2: Special handling for test email
              if (email == '<EMAIL>') {
                _addOnboardingLog('🧪 Test email detected, using bypass flow');
                _trackSignupAttempt('test_email_bypass');
                await _handleTestEmailFlow(username, email, gender);
                return;
              }

              // Failsafe 3: Show awaiting email verification modal with bulletproof mechanisms
              _trackSignupAttempt('proceeding_to_verification');
              _showAwaitingEmailVerificationModalWithFailsafes(context, username, email, password, gender);
            },
            onContinueWithoutEmail: () async {
              _addOnboardingLog('✅ Continue without email selected');
              _trackSignupAttempt('email_less_signup');

              Navigator.of(context).pop();

              // Handle email-less signup flow
              await _handleEmailLessSignupFlow(username, gender);
            },
          ),
        ),
      ),
    );
  }

  /// 🧪 Handle test email flow with failsafes
  Future<void> _handleTestEmailFlow(String username, String email, String gender) async {
    try {
      _addOnboardingLog('🧪 Creating test user immediately');

      final testUser = User.blank(
        id: username,
        username: username,
      ).copyWith(
        email: email,
        gender: gender,
        isEmailVerified: true,
        klaviyoSubscribed: true,
      );

      // Save username for future convenience (dropdown feature)
      await SavedAccountsService.saveUsername(username);
      _addOnboardingLog('💾 Test user username saved for dropdown: $username');

      _addOnboardingLog('✅ Test user created: ${testUser.username}');
      _saveOnboardingLog();
      widget.onContinue(testUser);

    } catch (e) {
      _addOnboardingLog('💥 Error creating test user: $e');
      if (mounted) {
        _showErrorDialog(context, 'Failed to create test user: $e');
      }
    }
  }

  void _showSignIn(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Align(
        alignment: Alignment.topCenter,
        child: Padding(
          padding: const EdgeInsets.only(top: 100),
          child: AuthSignInModal(
            onSignIn: (username, password) async {
              Navigator.of(context).pop();
              await _signIn(username, password);
            },
            onBack: () {
              _addOnboardingLog('❌ Sign in modal closed by user');
              Navigator.of(context).pop(); // Close sign in modal
            },
          ),
        ),
      ),
    );
  }

  /// 🛡️ Show awaiting email verification modal with bulletproof failsafes
  void _showAwaitingEmailVerificationModalWithFailsafes(BuildContext context, String username, String email, String password, String gender) {
    _addOnboardingLog('🛡️ Showing awaiting email verification modal with failsafes');

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AwaitingEmailVerificationModal(
        email: email,
        password: password,
        username: username,
        gender: gender,
        onSuccess: () async {
          _addOnboardingLog('🎉 Email verification successful!');
          Navigator.of(context).pop(); // Close verification modal
          await _handleEmailVerificationSuccessWithFailsafes(username, email, password, gender);
        },
        onError: (error) {
          _addOnboardingLog('💥 Email verification error: $error');
          Navigator.of(context).pop(); // Close verification modal
          _showBulletproofErrorDialog(context, error);
        },
        onContinue: () {
          _addOnboardingLog('🔄 User clicked Continue - showing confirmation modal');
          Navigator.of(context).pop(); // Close awaiting modal
          _showConfirmEmailVerificationModal(context, username, email, password, gender);
        },
      ),
    );
  }

  /// 🎯 Show confirmation email verification modal (NEW FLOW)
  void _showConfirmEmailVerificationModal(BuildContext context, String username, String email, String password, String gender) {
    _addOnboardingLog('🎯 Showing confirmation email verification modal');

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => ConfirmEmailVerificationModal(
        email: email,
        password: password,
        username: username,
        gender: gender,
        onSuccess: () async {
          _addOnboardingLog('✅ Confirmation modal completed - proceeding to onboarding');
          Navigator.of(context).pop(); // Close confirmation modal
          await _handleEmailVerificationSuccessWithFailsafes(username, email, password, gender);
        },
        onBack: () {
          _addOnboardingLog('🔙 User went back from confirmation modal');
          Navigator.of(context).pop(); // Close confirmation modal
          _showAwaitingEmailVerificationModalWithFailsafes(context, username, email, password, gender);
        },
      ),
    );
  }

  /// 🛡️ Handle email verification success with bulletproof failsafes
  Future<void> _handleEmailVerificationSuccessWithFailsafes(String username, String email, String password, String gender) async {
    _addOnboardingLog('🎉 Email verification successful, proceeding with bulletproof user creation');

    try {
      final firebaseController = Provider.of<FirebaseAuthController>(context, listen: false);

      // Failsafe 1: Multiple attempts to get verified user
      User? finalUser;
      int attempts = 0;
      const maxAttempts = 3;

      while (attempts < maxAttempts && finalUser == null) {
        attempts++;
        _addOnboardingLog('🔄 User creation attempt $attempts/$maxAttempts');

        try {
          // Method 1: Use Firebase controller's local user
          if (firebaseController.localUser != null) {
            _addOnboardingLog('✅ Using verified Firebase user from controller');

            finalUser = firebaseController.localUser!.copyWith(
              username: username,
              gender: gender,
              isEmailVerified: true,
            );

            // Failsafe 2: Validate the user data
            if (finalUser.email != email) {
              _addOnboardingLog('⚠️ Email mismatch detected, correcting...');
              finalUser = finalUser.copyWith(email: email);
            }

            break;
          }

          // Method 2: Reload and try again
          _addOnboardingLog('🔄 Reloading Firebase user...');
          await firebaseController.reloadUser();

          if (firebaseController.localUser != null) {
            finalUser = firebaseController.localUser!.copyWith(
              username: username,
              gender: gender,
              isEmailVerified: true,
            );
            break;
          }

          // Method 3: Create fallback user (last resort)
          if (attempts >= maxAttempts) {
            _addOnboardingLog('⚠️ Creating fallback user after verification');
            finalUser = User.blank(
              id: username,
              username: username,
            ).copyWith(
              email: email,
              gender: gender,
              isEmailVerified: true,
              klaviyoSubscribed: true,
            );
          }

        } catch (e) {
          _addOnboardingLog('💥 Error in user creation attempt $attempts: $e');

          if (attempts >= maxAttempts) {
            rethrow;
          }

          // Wait before retry
          await Future.delayed(Duration(seconds: attempts));
        }
      }

      // Failsafe 3: Final validation
      if (finalUser == null) {
        throw Exception('Failed to create user after $maxAttempts attempts');
      }

      // Failsafe 4: Validate final user data
      if (finalUser.username != username) {
        _addOnboardingLog('⚠️ Username mismatch, correcting...');
        finalUser = finalUser.copyWith(username: username);
      }

      if (finalUser.gender != gender) {
        _addOnboardingLog('⚠️ Gender mismatch, correcting...');
        finalUser = finalUser.copyWith(gender: gender);
      }

      if (finalUser.email != email) {
        _addOnboardingLog('⚠️ Email mismatch, correcting...');
        finalUser = finalUser.copyWith(email: email);
      }

      if (!finalUser.isEmailVerified) {
        _addOnboardingLog('⚠️ Email verification flag missing, correcting...');
        finalUser = finalUser.copyWith(isEmailVerified: true);
      }

      _addOnboardingLog('✅ Final user validated: ${finalUser.username}, ${finalUser.email}, ${finalUser.gender}');

      // Save username for future convenience (dropdown feature)
      await SavedAccountsService.saveUsername(finalUser.username);
      _addOnboardingLog('💾 Username saved for dropdown: ${finalUser.username}');

      _saveOnboardingLog();

      // Success!
      widget.onContinue(finalUser);

    } catch (e) {
      _addOnboardingLog('💥 CRITICAL ERROR in verification success handler: $e');
      _saveOnboardingLog();

      if (mounted) {
        _showBulletproofErrorDialog(context, 'Failed to complete account setup: $e');
      }
    }
  }

  /// 🛡️ Show bulletproof error dialog with debugging info
  void _showBulletproofErrorDialog(BuildContext context, String error) {
    _addOnboardingLog('🚨 Showing bulletproof error dialog: $error');
    _onboardingInProgress = false; // Reset onboarding state

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF1A1A1A),
        title: const Text(
          '🛡️ Onboarding Error',
          style: TextStyle(
            color: Colors.red,
            fontFamily: 'Pirulen',
            fontSize: 16,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              error,
              style: const TextStyle(
                color: Colors.white70,
                fontFamily: 'Bitsumishi',
                fontSize: 14,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'Session ID: $_onboardingSessionId',
              style: const TextStyle(
                color: Colors.grey,
                fontFamily: 'Bitsumishi',
                fontSize: 10,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'Debug information has been saved for troubleshooting.',
              style: TextStyle(
                color: Colors.grey,
                fontFamily: 'Bitsumishi',
                fontSize: 10,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _onboardingInProgress = false; // Ensure state is reset
            },
            child: const Text(
              'Try Again',
              style: TextStyle(
                color: Colors.purple,
                fontFamily: 'Pirulen',
                fontSize: 14,
              ),
            ),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _saveOnboardingLog(); // Save one more time
            },
            child: const Text(
              'OK',
              style: TextStyle(
                color: Colors.red,
                fontFamily: 'Pirulen',
                fontSize: 14,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showErrorDialog(BuildContext context, String error) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF1A1A1A),
        title: const Text(
          'Account Creation Error',
          style: TextStyle(
            color: Colors.white,
            fontFamily: 'Pirulen',
            fontSize: 16,
          ),
        ),
        content: Text(
          error,
          style: const TextStyle(
            color: Colors.white70,
            fontFamily: 'Bitsumishi',
            fontSize: 14,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text(
              'OK',
              style: TextStyle(
                color: Colors.purple,
                fontFamily: 'Pirulen',
                fontSize: 14,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _signIn(String username, String password) async {
    final controller = Provider.of<UserController2>(context, listen: false);

    final success = await controller.signIn(
      username: username,
      password: password,
    );

    if (success && controller.user != null) {
      print('🔄 WelcomeStartPage: Sign in successful - calling onContinue');
      widget.onContinue(controller.user!);
    } else {
      // Show error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(controller.error ?? 'Sign in failed'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }



  @override
  Widget build(BuildContext context) {
    const Color electricBlue = Color(0xFF00FFFF);
    const Color electricPurple = Color(0xFFB621FE);
    const Color neonPink = Color(0xFFFF2D55);
    const Color neonGreen = Color(0xFF39FF14);
    const Color white = Colors.white;
    const Color black = Colors.black;

    return Scaffold(
      extendBodyBehindAppBar: true,
      backgroundColor: black,
      body: Stack(
        children: [
          // Full-screen background image
          Positioned.fill(
            child: Image.asset(
              'assets/images/mxd_logo_2.jpg',
              fit: BoxFit.cover,
            ),
          ),
          // Black gradient overlay for readability
          Positioned.fill(
            child: Container(
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.black87,
                    Colors.black54,
                    Colors.transparent,
                  ],
                  stops: [0.0, 0.7, 1.0],
                ),
              ),
            ),
          ),
          // Content
          SafeArea(
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 24.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    // Top App Bar with back arrow
                    Row(
                      children: [
                        Container(
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            gradient: LinearGradient(
                              colors: [electricBlue.withValues(alpha: 0.7), electricPurple.withValues(alpha: 0.7)],
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: electricBlue.withValues(alpha: 0.3),
                                blurRadius: 8,
                                spreadRadius: 1,
                              ),
                            ],
                          ),
                          child: IconButton(
                            icon: const Icon(Icons.arrow_back, color: white),
                            iconSize: 28,
                            onPressed: () => Navigator.of(context).pop(),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 24),
                    // Headline
                    Text(
                      'Welcome to your',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontFamily: 'Pirulen',
                        fontSize: 22,
                        color: white.withValues(alpha: 0.85),
                        fontWeight: FontWeight.w400,
                        letterSpacing: 1.5,
                        shadows: [
                          Shadow(
                            color: electricBlue.withValues(alpha: 0.5),
                            blurRadius: 8,
                          ),
                        ],
                      ),
                    ),
                    ShaderMask(
                      shaderCallback: (Rect bounds) {
                        return const LinearGradient(
                          colors: [Color(0xFF00FFFF), Color(0xFFB621FE)],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ).createShader(bounds);
                      },
                      child: const Text(
                        'MXD OUT LIFE',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontFamily: 'Pirulen',
                          fontSize: 36,
                          fontWeight: FontWeight.bold,
                          letterSpacing: 2,
                          color: Colors.white,
                          shadows: [
                            Shadow(
                              color: Colors.black,
                              blurRadius: 12,
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 12),
                    Text(
                      'Your journey to greatness \nstarts here.',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontFamily: 'Bitsumishi',
                        fontSize: 15,
                        color: white.withValues(alpha: 0.7),
                        fontWeight: FontWeight.w400,
                        letterSpacing: 1.2,
                      ),
                    ),
                    const SizedBox(height: 28),
                    // Progress Indicator (horizontal bar style)
                    Align(
                      alignment: Alignment.centerLeft,
                      child: SizedBox(
                        width: 370,
                        child: OnboardingProgressIndicator(
                          progress: const OnboardingProgress(),
                          onStepTap: () {
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text('Complete each step to unlock your full potential!'),
                                duration: Duration(seconds: 2),
                              ),
                            );
                          },
                        ),
                      ),
                    ),
                    const SizedBox(height: 32),
                    // Steps as neon cards
                    _buildStepCard(
                      context,
                      number: 1,
                      icon: Icons.star,
                      text: 'Create your North Star Quest to define your ultimate goal.',
                      color: electricBlue,
                    ),
                    const SizedBox(height: 16),
                    _buildStepCard(
                      context,
                      number: 2,
                      icon: Icons.track_changes,
                      text: 'Track daily habits and earn XP to level up.',
                      color: neonGreen,
                    ),
                    const SizedBox(height: 16),
                    _buildStepCard(
                      context,
                      number: 3,
                      icon: Icons.feed,
                      text: 'Reflect on your progress in the Super Diary Feed.',
                      color: neonPink,
                    ),
                    SizedBox(height: 24),
                    // Authentication buttons
                    Padding(
                      padding: const EdgeInsets.only(bottom: 24.0),
                      child: Column(
                        children: [
                          // Sign Up button
                          SizedBox(
                            width: double.infinity,
                            height: 54,
                            child: ElevatedButton(
                              style: ElevatedButton.styleFrom(
                                padding: EdgeInsets.zero,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(16),
                                ),
                                elevation: 0,
                                backgroundColor: Colors.transparent,
                              ),
                              onPressed: () {
                                print('🔄 WelcomeStartPage: SIGN UP button pressed');
                                _showSignUp(context);
                              },
                              child: Ink(
                                decoration: BoxDecoration(
                                  gradient: const LinearGradient(
                                    colors: [Color(0xFF00FFFF), Color(0xFFB621FE)],
                                    begin: Alignment.centerLeft,
                                    end: Alignment.centerRight,
                                  ),
                                  borderRadius: BorderRadius.circular(16),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Color(0xFF00FFFF),
                                      blurRadius: 16,
                                      spreadRadius: 1,
                                    ),
                                  ],
                                ),
                                child: Container(
                                  alignment: Alignment.center,
                                  child: const Text(
                                    'SIGN UP',
                                    style: TextStyle(
                                      fontFamily: 'Pirulen',
                                      fontSize: 22,
                                      color: Colors.white,
                                      fontWeight: FontWeight.bold,
                                      letterSpacing: 2,
                                      shadows: [
                                        Shadow(
                                          color: Colors.black,
                                          blurRadius: 8,
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(height: 16),
                          // Sign In button
                          SizedBox(
                            width: double.infinity,
                            height: 54,
                            child: ElevatedButton(
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.grey[800],
                                foregroundColor: Colors.white,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(16),
                                ),
                                elevation: 0,
                              ),
                              onPressed: () => _showSignIn(context),
                              child: const Text(
                                'SIGN IN',
                                style: TextStyle(
                                  fontFamily: 'Pirulen',
                                  fontSize: 22,
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                  letterSpacing: 2,
                                  shadows: [
                                    Shadow(
                                      color: Colors.black,
                                      blurRadius: 8,
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
      // ─── Floating "Music" Button at bottom right ───
      floatingActionButton: SizedBox(
        width: 56,
        height: 56,
        child: FloatingActionButton(
          heroTag: "welcome_music_fab", // Unique hero tag
          backgroundColor: Colors.black87,
          onPressed: _openMusicModal,
          child: const Icon(Icons.music_note, color: Colors.cyanAccent, size: 24),
        ),
      ),
    );
  }

  Widget _buildStepCard(BuildContext context, {required int number, required IconData icon, required String text, required Color color}) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(14),
        gradient: LinearGradient(
          colors: [color.withValues(alpha: 0.18), color.withValues(alpha: 0.08)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        border: Border.all(color: color.withValues(alpha: 0.7), width: 2),
        boxShadow: [
          BoxShadow(
            color: color.withValues(alpha: 0.25),
            blurRadius: 12,
            spreadRadius: 1,
          ),
        ],
      ),
      padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: LinearGradient(
                colors: [color, Colors.white.withValues(alpha: 0.7)],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              boxShadow: [
                BoxShadow(
                  color: color.withValues(alpha: 0.5),
                  blurRadius: 8,
                  spreadRadius: 1,
                ),
              ],
            ),
            child: Icon(icon, color: Colors.white, size: 18),
          ),
          const SizedBox(height: 10),
          Text(
            text,
            textAlign: TextAlign.center,
            softWrap: true,
            maxLines: 3,
            overflow: TextOverflow.ellipsis,
            style: const TextStyle(
              fontFamily: 'Pirulen',
              fontSize: 15,
              color: Colors.white,
              fontWeight: FontWeight.w500,
              letterSpacing: 1.1,
              height: 1.3,
            ),
          ),
        ],
      ),
    );
  }

  /// Handle email-less signup flow
  Future<void> _handleEmailLessSignupFlow(String username, String gender) async {
    try {
      _addOnboardingLog('🚀 Starting email-less signup flow for: $username');

      // Create user without email using Firebase controller
      final firebaseController = Provider.of<FirebaseAuthController>(context, listen: false);
      final success = await firebaseController.createAccountWithoutEmail(
        username: username,
        gender: gender,
      );

      if (success) {
        _addOnboardingLog('✅ Email-less account created successfully');

        // Get the created user
        final finalUser = firebaseController.localUser;

        if (finalUser != null) {
          _addOnboardingLog('✅ Email-less user retrieved successfully');
          _addOnboardingLog('✅ Final user validated: ${finalUser.username}, email-less, ${finalUser.gender}');
          _saveOnboardingLog();

          // Complete signup - same pattern as email verification success
          widget.onContinue(finalUser);
        } else {
          _addOnboardingLog('❌ Failed to retrieve email-less user');
          if (mounted) {
            _showErrorDialog(context, 'Account creation failed. Please try again.');
          }
        }
      } else {
        _addOnboardingLog('❌ Email-less account creation failed');
        if (mounted) {
          _showErrorDialog(context, 'Account creation failed. Please try again.');
        }
      }

    } catch (e) {
      _addOnboardingLog('❌ Email-less signup flow exception: $e');
      if (mounted) {
        _showErrorDialog(context, 'An error occurred during account creation. Please try again.');
      }
    }
  }

  @override
  void dispose() {
    _addOnboardingLog('🧹 Disposing onboarding page');
    _onboardingWatchdog?.cancel();
    _saveOnboardingLog();
    super.dispose();
  }
}