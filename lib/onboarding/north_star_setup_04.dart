import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../models/user_model.dart';
import '../controller/user_controller2.dart';
import '../quests/north_star_model.dart';
import '../theme/colors.dart';
import '../home/<USER>';

class NorthStarSetup extends StatefulWidget {
  final User user;
  final Function(User) onContinue;

  const NorthStarSetup({
    super.key,
    required this.user,
    required this.onContinue,
  });

  @override
  State<NorthStarSetup> createState() => _NorthStarSetupState();
}

class _NorthStarSetupState extends State<NorthStarSetup> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _summaryController = TextEditingController();
  final _scrollController = ScrollController(); // Added scroll controller for step positioning
  Color _selectedColor = Colors.cyanAccent;
  bool _isLoading = false;
  bool _questSaved = false;
  int _currentStep = 0;

  // Category reading state
  Set<String> _readCategories = {};
  bool get _allCategoriesRead => _readCategories.length >= _categories.length;

  final List<String> _categories = [
    'Health',
    'Wealth', 
    'Purpose',
    'Connection'
  ];

  void _openMusicModal() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (_) => const MusicControlModal(),
      isScrollControlled: true,
      enableDrag: true,
      showDragHandle: true,
    );
  }

  void _markCategoryAsRead(String category) {
    if (!_readCategories.contains(category)) {
      setState(() {
        _readCategories.add(category);
      });
      // Haptic feedback for successful swipe
      HapticFeedback.lightImpact();
      print('✅ Category "$category" marked as read. Progress: ${_readCategories.length}/${_categories.length}');
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _summaryController.dispose();
    _scrollController.dispose(); // Dispose scroll controller
    super.dispose();
  }

  void _nextStep() {
    if (_currentStep < 4) {
      if (_currentStep == 0) {
        // Show meditation prompt after explanation
        _showMeditationPrompt();
      } else if (_currentStep == 1) {
        // Validate title before proceeding to summary step
        final title = _titleController.text.trim();
        print('🔍 Validating title: "$title" (length: ${title.length})');

        if (title.isEmpty) {
          print('❌ Title is empty');
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Please enter your quest title')),
          );
          return;
        } else if (title.length < 10) {
          print('❌ Title too short: ${title.length} characters (need 10+)');
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Please provide a more detailed title (at least 10 characters)')),
          );
          return;
        }
        print('✅ Title validation passed, advancing to step 2');
        setState(() {
          _currentStep++;
        });
        _scrollToTop(); // Scroll to top when step changes
      } else {
        setState(() {
          _currentStep++;
        });
        _scrollToTop(); // Scroll to top when step changes
      }
    }
  }

  void _previousStep() {
    if (_currentStep > 0) {
      setState(() {
        _currentStep--;
      });
      _scrollToTop(); // Scroll to top when step changes
    }
  }

  // Scroll to top of the step content when step changes
  void _scrollToTop() {
    print('📍 Scrolling to top for step $_currentStep');
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          0.0,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
        print('✅ Scroll animation started');
      } else {
        print('⚠️ ScrollController has no clients');
      }
    });
  }

  Future<void> _saveQuest() async {
    // Validate only the fields that are relevant for the current step
    bool isValid = true;

    // Validate title (from step 2)
    if (_titleController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter your quest title')),
      );
      isValid = false;
    } else if (_titleController.text.trim().length < 10) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please provide a more detailed title (at least 10 characters)')),
      );
      isValid = false;
    }

    // Validate summary (from step 3)
    if (_summaryController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please describe your quest')),
      );
      isValid = false;
    } else if (_summaryController.text.trim().length < 50) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please provide a more detailed description (at least 50 characters)')),
      );
      isValid = false;
    }

    if (!isValid) return;

    setState(() => _isLoading = true);

    try {
      print('🚀 Starting quest creation...');
      print('Title: "${_titleController.text.trim()}"');
      print('Summary: "${_summaryController.text.trim()}"');

      final userController = Provider.of<UserController2>(context, listen: false);
      print('✅ Got user controller');

      final quest = NorthStarQuest.create(
        title: _titleController.text.trim(),
        summary: _summaryController.text.trim(),
        category: 'North Star', // North Star Quest is category-agnostic
        glowColor: _selectedColor,
        coreValues: [],
        icon: '⭐',
        visionImagePath: null,
      );
      print('✅ Created quest: ${quest.title}');

      final updatedUser = widget.user.copyWith(northStarQuest: quest);
      print('✅ Created updated user with quest');

      await userController.updateUser(updatedUser);
      print('✅ Updated user in controller');

      await userController.save();
      print('✅ Saved user to storage');

      if (!mounted) return;
      print('✅ Calling onContinue...');

      // Add a small delay to ensure the UI is ready
      await Future.delayed(const Duration(milliseconds: 100));

      if (!mounted) return;
      print('🚀 About to call widget.onContinue with user: ${updatedUser.username}');
      widget.onContinue(updatedUser);
      print('🎯 widget.onContinue called successfully');

      // Mark quest as saved to prevent multiple saves
      setState(() {
        _questSaved = true;
      });
    } catch (e, stackTrace) {
      print('❌ Error saving quest: $e');
      print('Stack trace: $stackTrace');
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error saving quest: $e')),
      );
    } finally {
      if (mounted) setState(() => _isLoading = false);
    }
  }

  void _showMeditationPrompt() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Align(
        alignment: Alignment.topCenter,
        child: Padding(
          padding: const EdgeInsets.only(top: 100),
          child: Dialog(
            backgroundColor: Colors.black.withValues(alpha: 0.95),
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        child: Container(
          width: MediaQuery.of(context).size.width * 0.85,
          padding: const EdgeInsets.all(32),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Meditation icon
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.cyanAccent.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                  border: Border.all(color: Colors.cyanAccent.withValues(alpha: 0.3), width: 2),
                ),
                child: const Icon(
                  Icons.self_improvement,
                  color: Colors.cyanAccent,
                  size: 48,
                ),
              ),
              const SizedBox(height: 24),
              
              // Title
              const Text(
                'P.S.',
                style: TextStyle(
                  color: Colors.cyanAccent,
                  fontFamily: 'Pirulen',
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              
              // Main message
              const Text(
                'Take your time - meditate for 30 minutes and let yourself discover what you truly desire.',
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Colors.white,
                  fontFamily: 'Pirulen',
                  fontSize: 18,
                  height: 1.4,
                ),
              ),
              const SizedBox(height: 20),
              
              // Second message
              const Text(
                'And remember, your life is about what you DO, not what you are - how do you want to spend your days on this Earth?',
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Colors.white70,
                  fontFamily: 'Bitsumishi',
                  fontSize: 16,
                  height: 1.4,
                ),
              ),
              const SizedBox(height: 32),
              
              // Continue button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                    setState(() {
                      _currentStep++;
                    });
                    _scrollToTop(); // Scroll to top when step changes
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.cyanAccent,
                    foregroundColor: Colors.black,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: const Text(
                    'Continue',
                    style: TextStyle(
                      fontFamily: 'Pirulen',
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
          ),
        ),
      ),
    );
  }

  Widget _buildStepIndicator() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(5, (index) {
        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 4),
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: index <= _currentStep ? Colors.cyanAccent : Colors.grey[600],
          ),
        );
      }),
    );
  }

  Widget _buildStepContent() {
    switch (_currentStep) {
      case 0:
        return _buildExplanationPage();
      case 1:
        return _buildQuestTitle();
      case 2:
        return _buildQuestSummary();
      case 3:
        return _buildColorSelection();
      case 4:
        return _buildCategorySelection();
      default:
        return _buildExplanationPage();
    }
  }

  Widget _buildExplanationPage() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'MXD OUT LIFE // NORTH STAR QUEST SETUP',
            style: TextStyle(
              color: Colors.cyanAccent,
              fontFamily: 'Pirulen',
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 24),
          const Text(
            'This is not just another goal. This is your life\'s campaign.',
            style: TextStyle(
              color: Colors.white,
              fontFamily: 'Pirulen',
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          const Text(
            'In your MXD Out Life, your North Star Quest is the master objective.\n'
            'It is the big, bold mission that everything else in your life levels up toward.\n'
            'It\'s not about ticking boxes.\n'
            'It\'s about clarity, purpose, and control over chaos.\n'
            'You\'re not here to wander. You\'re here to win.',
            style: TextStyle(
              color: Colors.white70,
              fontFamily: 'Bitsumishi',
              fontSize: 16,
              height: 1.5,
            ),
          ),
          const SizedBox(height: 24),
          const Text(
            '🧭 What Is A North Star Quest?',
            style: TextStyle(
              color: Colors.cyanAccent,
              fontFamily: 'Pirulen',
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          const Text(
            'Your North Star Quest is your life\'s campaign. It is a clear, spirit-aligned direction that drives every choice you make, both in game and real life.\n'
            'It\'s bigger than a goal.\n'
            'It\'s the reason behind your discipline, your training, your motivation.\n'
            'It\'s the mission that demands your attention, especially when no one\'s watching.\n\n'
            'Think of it like this:\n'
            'North Star = the ultimate target\n\n'
            'North Star Quest = the journey to achieve it',
            style: TextStyle(
              color: Colors.white70,
              fontFamily: 'Bitsumishi',
              fontSize: 16,
              height: 1.5,
            ),
          ),
          const SizedBox(height: 24),
          const Text(
            '🔥 Why it matters:',
            style: TextStyle(
              color: Colors.amberAccent,
              fontFamily: 'Pirulen',
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          const Text(
            'In life, distraction is the enemy.\n'
            'Your North Star cuts through the noise.\n\n'
            'Defining your North Star:\n'
            '• Clarifies what matters and what doesn\'t.\n\n'
            '• Aligns your habits, actions, and rewards.\n\n'
            '• Inspires you to keep pushing when life hurts.\n\n'
            '• Focuses your energy on results, your work ethic, and your performance.\n\n'
            '• Eliminates the pull of comfort, and helps you evade creating excuses.',
            style: TextStyle(
              color: Colors.white70,
              fontFamily: 'Bitsumishi',
              fontSize: 16,
              height: 1.5,
            ),
          ),
          const SizedBox(height: 24),
          const Text(
            '🧠 Example:',
            style: TextStyle(
              color: Colors.cyanAccent,
              fontFamily: 'Pirulen',
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.cyanAccent.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.cyanAccent.withValues(alpha: 0.3)),
            ),
            child: const Text(
              '"Help 1 million people unlock high-performance sleep and breathing through my brand and platform."\n\n'
              'This Quest would influence everything: your training, your content, your mindset, your recovery—even your business decisions.\n\n'
              'Or maybe it\'s:\n'
              '"Become a pro fighter, inspire millions, and stay true to my family\'s code."\n'
              '"Build a legacy with my partner that blends love, strength, and service."\n\n'
              'The quest is yours to set.\n'
              'But it has to be real.\n'
              'No fluff.\n'
              'No filters.\n'
              'Just purpose and pressure.',
              style: TextStyle(
                color: Colors.white70,
                fontFamily: 'Bitsumishi',
                fontSize: 16,
                height: 1.5,
              ),
            ),
          ),
          const SizedBox(height: 24),
          const Text(
            '💥 Final Words:',
            style: TextStyle(
              color: Colors.amberAccent,
              fontFamily: 'Pirulen',
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          const Text(
            'Lock in your North Star Quest now.\n'
            'You can update it as you evolve, but every action, habit, and statistic in this game, and your life, should serve it.\n'
            'You should have no limits on the scope of your dreams.\n'
            'To sell yourself short means to underestimate your own power, and the power of your Determination x Consistent Execution.\n'
            'Do not settle for anything less than extraordinary, because you are, and only you can be, The Chosen One in your life.\n'
            'The mission is alive.\n'
            'Choose your Quest.\n'
            'Then get to work.\n\n'
            'Maxed Out Life is not a vibe. It\'s a campaign.\n\n'
            'Let\'s go.',
            style: TextStyle(
              color: Colors.white70,
              fontFamily: 'Bitsumishi',
              fontSize: 16,
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategorySelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Complete Your Setup',
          style: TextStyle(
            color: Colors.white,
            fontFamily: 'Pirulen',
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        const Text(
          'Your North Star Quest is ready! Swipe each life category below to understand how your quest can draw progress from multiple areas of your life.',
           style: TextStyle(
            color: Colors.white70,
            fontFamily: 'Bitsumishi',
            fontSize: 16,
          ),
        ),
        const SizedBox(height: 16),
        // Progress indicator
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: _allCategoriesRead ? Colors.green.withValues(alpha: 0.1) : Colors.orange.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: _allCategoriesRead ? Colors.green.withValues(alpha: 0.3) : Colors.orange.withValues(alpha: 0.3),
            ),
          ),
          child: Row(
            children: [
              Icon(
                _allCategoriesRead ? Icons.check_circle : Icons.swipe,
                color: _allCategoriesRead ? Colors.green : Colors.orange,
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  _allCategoriesRead
                    ? 'All categories reviewed! Ready to create your quest.'
                    : 'Swipe to review: ${_readCategories.length}/${_categories.length} categories completed',
                  style: TextStyle(
                    color: _allCategoriesRead ? Colors.green : Colors.orange,
                    fontFamily: 'Bitsumishi',
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 24),
        ..._categories.map((category) => _buildCategoryInfoCard(category)),
        const SizedBox(height: 24),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.cyanAccent.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.cyanAccent.withValues(alpha: 0.3)),
          ),
          child: const Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '🌟 Your North Star Quest',
                style: TextStyle(
                  color: Colors.cyanAccent,
                  fontFamily: 'Pirulen',
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 8),
              Text(
                'Your North Star Quest is your ultimate life goal that can incorporate progress from many or all of these categories. It\'s not limited to just one area - it\'s about your overall life transformation.',
                style: TextStyle(
                  color: Colors.white70,
                  fontFamily: 'Bitsumishi',
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildCategoryInfoCard(String category) {
    final bool isRead = _readCategories.contains(category);
    final Color categoryColor = _getCategoryColor(category);

    return GestureDetector(
      onHorizontalDragEnd: (details) {
        // Detect swipe (left or right) - optimized for both iPhone and iPad
        final double velocityThreshold = MediaQuery.of(context).size.width > 600 ? 200 : 300;
        if (details.primaryVelocity!.abs() > velocityThreshold) {
          _markCategoryAsRead(category);
        }
      },
      onTap: () {
        // Also allow tap to mark as read for accessibility
        _markCategoryAsRead(category);
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        margin: const EdgeInsets.only(bottom: 16),
        // Ensure minimum touch target of 44pt for accessibility
        constraints: const BoxConstraints(minHeight: 88),
        padding: EdgeInsets.all(MediaQuery.of(context).size.width > 600 ? 24 : 20),
        decoration: BoxDecoration(
          color: isRead ? categoryColor.withValues(alpha: 0.1) : Colors.grey[900],
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isRead ? categoryColor : Colors.grey[800]!,
            width: isRead ? 3 : 2,
          ),
          boxShadow: isRead ? [
            BoxShadow(
              color: categoryColor.withValues(alpha: 0.4),
              blurRadius: 12,
              spreadRadius: 2,
            ),
          ] : null,
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: categoryColor,
                borderRadius: BorderRadius.circular(12),
                boxShadow: isRead ? [
                  BoxShadow(
                    color: categoryColor.withValues(alpha: 0.6),
                    blurRadius: 8,
                    spreadRadius: 1,
                  ),
                ] : null,
              ),
              child: Icon(
                _getCategoryIcon(category),
                color: Colors.white,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    category,
                    style: TextStyle(
                      color: isRead ? categoryColor : Colors.white,
                      fontFamily: 'Pirulen',
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    _getCategoryDescription(category),
                    style: TextStyle(
                      color: isRead ? Colors.white : Colors.grey[400],
                      fontFamily: 'Bitsumishi',
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
            // Status indicator
            AnimatedSwitcher(
              duration: const Duration(milliseconds: 300),
              child: isRead
                ? Icon(
                    Icons.check_circle,
                    color: categoryColor,
                    size: 24,
                    key: ValueKey('checked_$category'),
                  )
                : Icon(
                    Icons.swipe,
                    color: Colors.grey[600],
                    size: 20,
                    key: ValueKey('swipe_$category'),
                  ),
            ),
          ],
        ),
      ),
    );
  }

  IconData _getCategoryIcon(String category) {
    switch (category) {
      case 'Health':
        return Icons.favorite;
      case 'Wealth':
        return Icons.attach_money;
      case 'Purpose':
        return Icons.psychology;
      case 'Connection':
        return Icons.people;
      default:
        return Icons.star;
    }
  }

  String _getCategoryDescription(String category) {
    switch (category) {
      case 'Health':
        return 'Physical fitness, mental wellness, and overall vitality';
      case 'Wealth':
        return 'Financial freedom, career growth, and abundance';
      case 'Purpose':
        return 'Life mission, personal growth, and meaning';
      case 'Connection':
        return 'Relationships, community, and social bonds';
      case 'Custom One':
        return 'Your highest priority genre of improvement';
      case 'Custom Two':
        return 'Your next highest priority genre of improvement';
      default:
        return 'Your personal quest category';
    }
  }

  Color _getCategoryColor(String category) {
    // Use the proper theme colors from colors.dart
    return getCategoryColor(category, customCategories: widget.user.customCategories);
  }

 Widget _buildColorSelection() {
  final availableColors = [
    // Gems & Minerals
    {'name': 'Ruby',           'color': Colors.red.shade700},
    {'name': 'Emerald',        'color': Colors.green.shade500},
    {'name': 'Sapphire',       'color': Colors.blue.shade900},
    {'name': 'Topaz',          'color': Colors.yellow.shade700},
    {'name': 'Amethyst',       'color': Colors.purple.shade700},
    {'name': 'Garnet',         'color': const Color.fromARGB(244, 255, 0, 0)},
    {'name': 'Jade',           'color': Colors.green.shade400},
    {'name': 'Onyx',           'color': Colors.black},
    {'name': 'Obsidian',       'color': Colors.grey.shade800},
    {'name': 'Jasper',         'color': Colors.red.shade900},
    {'name': 'Opal',           'color': Colors.white},
    {'name': 'Moonstone',      'color': Colors.blue.shade100},
    {'name': 'Sunstone',       'color': Colors.orange.shade900},
    {'name': 'Cobalt',         'color': Colors.blue.shade700},
    {'name': 'Copper',         'color': Colors.orange.shade700},
    {'name': 'Gold',           'color': Colors.yellow.shade700},
    {'name': 'Silver',         'color': Colors.grey.shade300},
    {'name': 'Bronze',         'color': Colors.brown.shade300},
    {'name': 'Platinum',       'color': Colors.grey.shade400},
    {'name': 'Diamond',        'color': Colors.grey.shade500},
    {'name': 'Ruby 2',      'color': Colors.red.shade300},
    {'name': 'Sapphire 2',  'color': Colors.blue.shade300},
    {'name': 'Emerald 2',   'color': Colors.green.shade300},
    {'name': 'Topaz 2',     'color': Colors.yellow.shade300},
    {'name': 'Amethyst 2',  'color': Colors.purple.shade300},
    {'name': 'Opal 2',      'color': Colors.white.withAlpha(150)},  
  ];


    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Choose Your Quest Color',
            style: TextStyle(
              color: Colors.white,
              fontFamily: 'Pirulen',
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'This color will represent your North Star Quest throughout the app. Choose one that resonates with your journey.',
            style: TextStyle(
              color: Colors.white70,
              fontFamily: 'Bitsumishi',
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 32),
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              childAspectRatio: 3,
            ),
            itemCount: availableColors.length,
            itemBuilder: (context, index) {
              final colorData = availableColors[index];
              final color = colorData['color'] as Color;
              final name = colorData['name'] as String;
              final isSelected = _selectedColor == color;

              return GestureDetector(
                onTap: () {
                  setState(() {
                    _selectedColor = color;
                  });
                },
                child: Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: isSelected ? color.withValues(alpha: 0.2) : Colors.grey[900],
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: isSelected ? color : Colors.grey[800]!,
                      width: isSelected ? 3 : 1,
                    ),
                    boxShadow: isSelected ? [
                      BoxShadow(
                        color: color.withValues(alpha: 0.4),
                        blurRadius: 8,
                        spreadRadius: 2,
                      ),
                    ] : null,
                  ),
                  child: Row(
                    children: [
                      Container(
                        width: 24,
                        height: 24,
                        decoration: BoxDecoration(
                          color: color,
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: color.withValues(alpha: 0.6),
                              blurRadius: 4,
                              spreadRadius: 1,
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          name,
                          style: TextStyle(
                            color: isSelected ? color : Colors.white,
                            fontFamily: 'Bitsumishi',
                            fontSize: 16,
                            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildQuestTitle() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Define Your North Star Quest',
          style: TextStyle(
            color: Colors.white,
            fontFamily: 'Pirulen',
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        const Text(
          'What is your ultimate life goal that will transform who you are?',
          style: TextStyle(
            color: Colors.white70,
            fontFamily: 'Bitsumishi',
            fontSize: 16,
          ),
        ),
        const SizedBox(height: 32),
        TextFormField(
          controller: _titleController,
          style: const TextStyle(color: Colors.white, fontSize: 18),
          decoration: InputDecoration(
            labelText: 'Quest Title',
            labelStyle: const TextStyle(color: Colors.white70),
            hintText: 'e.g., Become a Master of Physical Fitness',
            hintStyle: TextStyle(color: Colors.grey[600]),
            enabledBorder: OutlineInputBorder(
              borderSide: BorderSide(color: Colors.grey[800]!),
              borderRadius: BorderRadius.circular(12),
            ),
            focusedBorder: OutlineInputBorder(
              borderSide: const BorderSide(color: Colors.cyanAccent, width: 2),
              borderRadius: BorderRadius.circular(12),
            ),
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter your quest title';
            }
            if (value.length < 10) {
              return 'Please provide a more detailed title (at least 10 characters)';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.cyanAccent.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.cyanAccent.withValues(alpha: 0.3)),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                '💡 Quest Title Tips:',
                style: TextStyle(
                  color: Colors.cyanAccent,
                  fontFamily: 'Pirulen',
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              const Text(
                '• Be specific and measurable\n• Use action-oriented language\n• Make it inspiring and meaningful\n• Focus on the outcome you want',
                style: TextStyle(
                  color: Colors.white70,
                  fontFamily: 'Bitsumishi',
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildQuestSummary() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Describe Your Quest Journey',
          style: TextStyle(
            color: Colors.white,
            fontFamily: 'Pirulen',
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        const Text(
          'Explain why this quest matters and what success looks like',
          style: TextStyle(
            color: Colors.white70,
            fontFamily: 'Bitsumishi',
            fontSize: 16,
          ),
        ),
        const SizedBox(height: 32),
        TextFormField(
          controller: _summaryController,
          style: const TextStyle(color: Colors.white, fontSize: 16),
          maxLines: 6,
          decoration: InputDecoration(
            labelText: 'Quest Summary',
            labelStyle: const TextStyle(color: Colors.white70),
            hintText: 'Describe your vision, motivation, and what achieving this quest will mean for your life...',
            hintStyle: TextStyle(color: Colors.grey[600]),
            enabledBorder: OutlineInputBorder(
              borderSide: BorderSide(color: Colors.grey[800]!),
              borderRadius: BorderRadius.circular(12),
            ),
            focusedBorder: OutlineInputBorder(
              borderSide: const BorderSide(color: Colors.cyanAccent, width: 2),
              borderRadius: BorderRadius.circular(12),
            ),
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please describe your quest';
            }
            if (value.length < 50) {
              return 'Please provide a more detailed description (at least 50 characters)';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.amberAccent.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.amberAccent.withValues(alpha: 0.3)),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                '🌟 Quest Summary Guide:',
                style: TextStyle(
                  color: Colors.amberAccent,
                  fontFamily: 'Pirulen',
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              const Text(
                '• Why is this quest important to you?\n• What will your life look like when you achieve it?\n• What challenges might you face?\n• How will this quest transform you?',
                style: TextStyle(
                  color: Colors.white70,
                  fontFamily: 'Bitsumishi',
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        title: const Text(
          'Create Your North Star Quest',
          style: TextStyle(
            color: Colors.cyanAccent,
            fontFamily: 'Pirulen',
            fontSize: 20,
          ),
        ),
        backgroundColor: Colors.grey[900],
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Form(
            key: _formKey,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // Step indicator
                _buildStepIndicator(),
                const SizedBox(height: 24),
                
                // Progress text
                Text(
                  'Step ${_currentStep + 1} of 5',
                  style: TextStyle(
                    color: Colors.grey[400],
                    fontFamily: 'Bitsumishi',
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 32),
                
                // Step content
                Expanded(
                  child: SingleChildScrollView(
                    controller: _scrollController, // Added scroll controller for step positioning
                    child: _buildStepContent(),
                  ),
                ),
                
                const SizedBox(height: 24),
                
                // Navigation buttons
                _currentStep == 4
                    ? Center(
                        child: SizedBox(
                          width: 200,
                          child: ElevatedButton(
                            onPressed: _allCategoriesRead && !_isLoading && !_questSaved ? () {
                              print('🔘 Button tapped! Current step: $_currentStep, isLoading: $_isLoading, allRead: $_allCategoriesRead');
                              if (_isLoading) {
                                print('⏳ Button disabled - loading in progress');
                              } else if (_questSaved) {
                                print('✅ Quest already saved - ignoring tap');
                              } else if (!_allCategoriesRead) {
                                print('❌ Not all categories read yet');
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    content: Text('Please swipe all ${_categories.length} categories to continue'),
                                    backgroundColor: Colors.orange,
                                  ),
                                );
                              } else {
                                print('💾 Calling _saveQuest()');
                                _saveQuest();
                              }
                            } : null,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: _allCategoriesRead ? Colors.cyanAccent : Colors.grey[700],
                              foregroundColor: _allCategoriesRead ? Colors.black : Colors.grey[500],
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                            child: _isLoading
                                ? const SizedBox(
                                    width: 24,
                                    height: 24,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      valueColor: AlwaysStoppedAnimation<Color>(Colors.black),
                                    ),
                                  )
                                : Text(
                                    _allCategoriesRead
                                      ? 'Create Quest'
                                      : 'Swipe Categories (${_readCategories.length}/${_categories.length})',
                                    style: TextStyle(
                                      fontFamily: 'Pirulen',
                                      fontSize: _allCategoriesRead ? 16 : 14,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                          ),
                        ),
                      )
                    : Row(
                        children: [
                          if (_currentStep > 0)
                            Expanded(
                              child: ElevatedButton(
                                onPressed: _previousStep,
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.grey[800],
                                  foregroundColor: Colors.white,
                                  padding: const EdgeInsets.symmetric(vertical: 16),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                ),
                                child: const Text(
                                  'Previous',
                                  style: TextStyle(
                                    fontFamily: 'Pirulen',
                                    fontSize: 16,
                                  ),
                                ),
                              ),
                            ),
                          if (_currentStep > 0) const SizedBox(width: 16),
                          Expanded(
                            child: ElevatedButton(
                              onPressed: () {
                                print('🔘 Button tapped! Current step: $_currentStep, isLoading: $_isLoading');
                                print('📍 Calling _nextStep()');
                                _nextStep();
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.cyanAccent,
                                foregroundColor: Colors.black,
                                padding: const EdgeInsets.symmetric(vertical: 16),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                              ),
                              child: const Text(
                                'Continue',
                                style: TextStyle(
                                  fontFamily: 'Pirulen',
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
              ],
            ),
          ),
        ),
      ),
      // ─── Floating "Music" Button at bottom right ───
      floatingActionButton: SizedBox(
        width: 56,
        height: 56,
        child: FloatingActionButton(
          heroTag: "north_star_music_fab", // Unique hero tag
          backgroundColor: Colors.black87,
          onPressed: _openMusicModal,
          child: const Icon(Icons.music_note, color: Colors.cyanAccent, size: 24),
        ),
      ),
    );
  }
} 