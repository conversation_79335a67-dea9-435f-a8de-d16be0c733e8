import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/user_model.dart';
import '../controller/user_controller2.dart';
import '../cstcat/custom_category_setup_widget.dart';
import '../prompts/mxd_life_coach_explanations.dart';
import '../theme/colors.dart';
import '../dhabits/daily_habits_onboarding.dart';
import '../widgets/daily_habits_intro_modal.dart';
import '../services/coach_icon_service.dart';

import '../services/comprehensive_logging_service.dart';
import '../home/<USER>';

class OnboardingPage extends StatefulWidget {
  final User user;
  final void Function(User?) onComplete;

  const OnboardingPage({
    super.key,
    required this.user,
    required this.onComplete,
  });

  @override
  State<OnboardingPage> createState() => _OnboardingPageState();
}

class _OnboardingPageState extends State<OnboardingPage> {
  bool _hasSetCategories = false;
  bool _hasMetCoaches = false;
  bool _hasSetHabits = false;
  User? _updatedUser;

  void _openMusicModal() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (_) => const MusicControlModal(),
      isScrollControlled: true,
      enableDrag: true,
      showDragHandle: true,
    );
  }

  void _showCustomCategorySetup() async {
    // Use Navigator.push instead of showDialog to avoid navigation stack issues
    final result = await Navigator.push<User?>(
      context,
      MaterialPageRoute(
        builder: (context) => Scaffold(
          backgroundColor: Colors.black,
          body: CustomCategorySetupWidget(
            user: widget.user,
            onUserUpdated: (updatedUser) {
              print('🎯 OnboardingPage: Custom categories updated, popping back');
              Navigator.of(context).pop(updatedUser);
            },
            isInitialSetup: false, // Not initial setup
          ),
        ),
      ),
    );

    if (result != null) {
      setState(() {
        _hasSetCategories = true;
        _updatedUser = result;
      });
      print('✅ OnboardingPage: Custom categories setup completed');
    } else {
      print('⚠️ OnboardingPage: Custom categories returned null');
    }
  }

  void _showCoachIntroductions() {
    final currentUser = _updatedUser ?? widget.user;

    // 🎯 PHASE 2: Check email verification before showing coaches
    ComprehensiveLoggingService.logInfo('🎯 Coach introductions requested, verified: ${currentUser.isEmailVerified}');

    // Allow direct access to coach introductions during onboarding
    ComprehensiveLoggingService.logInfo('🎯 Showing coach introductions during onboarding');
    _showCoachIntroductionsModal(currentUser);
  }

  void _showCoachIntroductionsModal(User currentUser) {
    final List<MapEntry<String, String>> coachList = [
      const MapEntry('Health', 'Health'),
      const MapEntry('Wealth', 'Wealth'),
      const MapEntry('Purpose', 'Purpose'),
      const MapEntry('Connection', 'Connection'),
      // Map custom categories with their proper indices
      ...currentUser.customCategories.asMap().entries.map((entry) {
        final index = entry.key;
        final categoryName = entry.value;
        final categoryType = index == 0 ? 'Custom Category 1' : 'Custom Category 2';
        return MapEntry(categoryName, categoryType);
      }),
    ];

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Align(
        alignment: Alignment.topCenter,
        child: Padding(
          padding: const EdgeInsets.only(top: 100),
          child: Dialog(
            backgroundColor: Colors.black.withValues(alpha: 0.97),
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(18)),
        child: Container(
          width: 350,
          padding: const EdgeInsets.all(20),
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Bulletproof header with no overflow possibility
                LayoutBuilder(
                  builder: (context, constraints) {
                    return SizedBox(
                      width: constraints.maxWidth,
                      height: 36,
                      child: Row(
                        children: [
                          // Flexible title that can shrink
                          Expanded(
                            child: Container(
                              padding: const EdgeInsets.only(right: 8),
                              child: FittedBox(
                                fit: BoxFit.scaleDown,
                                alignment: Alignment.center,
                                child: Text(
                                  'Meet Your MXD Life Coaches',
                                  style: TextStyle(
                                    fontFamily: 'Pirulen',
                                    fontSize: 8,
                                    color: Colors.cyanAccent,
                                    fontWeight: FontWeight.bold,
                                  ),
                                  textAlign: TextAlign.center,
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ),
                          ),
                          // Fixed-size close button
                          SizedBox(
                            width: 20,
                            height: 20,
                            child: Material(
                              color: Colors.transparent,
                              child: InkWell(
                                onTap: () {
                                  setState(() => _hasMetCoaches = true);
                                  Navigator.of(context).pop();
                                },
                                borderRadius: BorderRadius.circular(10),
                                child: const Icon(
                                  Icons.close,
                                  color: Colors.white,
                                  size: 12,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
                const SizedBox(height: 16),
                ...coachList.map((entry) {
                  final displayName = entry.key;
                  final explanation = mxdLifeCoachExplanations[entry.value]?.replaceAll('Custom Category', displayName) ?? '';

                  // Get coach info for icon and name
                  final coachInfo = CoachIconService.isBuiltInCategory(entry.value)
                      ? CoachIconService.getCoachInfo(entry.value, widget.user.gender, assignedCoaches: widget.user.assignedCoaches)
                      : CoachIconService.getCoachInfo(entry.value, widget.user.gender, assignedCoaches: widget.user.assignedCoaches); // entry.value now contains 'Custom Category 1' or 'Custom Category 2'

                  return Padding(
                    padding: const EdgeInsets.only(bottom: 18.0),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Coach icon (64x64)
                        Container(
                          width: 64,
                          height: 64,
                          margin: const EdgeInsets.only(right: 16),
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            border: Border.all(color: Colors.amberAccent.withValues(alpha: 0.3), width: 2),
                          ),
                          child: ClipOval(
                            child: Image.asset(
                              coachInfo.iconPath,
                              width: 64,
                              height: 64,
                              fit: BoxFit.cover, // Changed to cover to fill the entire circle
                              alignment: Alignment.center,
                              errorBuilder: (context, error, stackTrace) {
                                // Fallback if icon is missing
                                return Container(
                                  width: 64,
                                  height: 64,
                                  decoration: BoxDecoration(
                                    color: Colors.amberAccent.withValues(alpha: 0.3),
                                    shape: BoxShape.circle,
                                  ),
                                  child: const Center(
                                    child: Icon(
                                      Icons.person,
                                      color: Colors.white,
                                      size: 32,
                                    ),
                                  ),
                                );
                              },
                            ),
                          ),
                        ),
                        // Coach info
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Category name
                              Text(
                                displayName,
                                style: const TextStyle(
                                  fontFamily: 'Pirulen',
                                  fontSize: 16,
                                  color: Colors.amberAccent,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 4),
                              // Coach name
                              Text(
                                coachInfo.name,
                                style: const TextStyle(
                                  fontFamily: 'Pirulen',
                                  fontSize: 12,
                                  color: Colors.cyanAccent,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 8),
                              // Explanation
                              Text(
                                explanation,
                                style: const TextStyle(
                                  color: Colors.white70,
                                  fontSize: 14,
                                  fontFamily: 'Bitsumishi',
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  );
                }),
                const SizedBox(height: 12),
                Center(
                  child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.cyanAccent,
                      foregroundColor: Colors.black,
                    ),
                    onPressed: () {
                      setState(() => _hasMetCoaches = true);
                      Navigator.of(context).pop();
                    },
                    child: const Text('GOT IT!', style: TextStyle(fontFamily: 'Pirulen', fontSize: 16)),
                  ),
                ),
              ],
            ),
          ),
        ),
          ),
        ),
      ),
    );
  }

  void _showDailyHabitsOnboarding() async {
    // First show the intro modal
    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Align(
        alignment: Alignment.topCenter,
        child: Padding(
          padding: const EdgeInsets.only(top: 100),
          child: DailyHabitsIntroModal(
            onContinue: () {
              Navigator.of(context).pop(); // Close the modal
            },
          ),
        ),
      ),
    );

    // Then navigate to the habits setup page
    if (!mounted) return;
    final result = await Navigator.push<User?>(
      context,
      MaterialPageRoute(
        builder: (context) => DailyHabitsOnboardingPage(
          user: _updatedUser ?? widget.user,
        ),
      ),
    );
    if (result != null) {
      setState(() {
        _hasSetHabits = true;
        _updatedUser = result;
      });
      widget.onComplete(result);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Complete Your Setup',
                style: TextStyle(
                  color: Colors.white,
                  fontFamily: 'Pirulen',
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              const Text(
                'Let\'s personalize your experience',
                style: TextStyle(
                  color: Colors.white70,
                  fontFamily: 'Bitsumishi',
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 40),
              
              // Custom Categories Section
              _buildSection(
                title: 'Set Your Custom Categories',
                description: 'Add up to 2 custom categories to track your progress',
                icon: Icons.category,
                isCompleted: _hasSetCategories,
                onTap: _showCustomCategorySetup,
              ),
              
              const SizedBox(height: 24),
              
              // Meet Coaches Section
              _buildSection(
                title: 'Meet Your Life Coaches',
                description: _hasSetCategories
                    ? 'Learn about your AI coaches and their roles'
                    : 'Complete custom categories first to meet all your coaches',
                icon: Icons.people,
                isCompleted: _hasMetCoaches,
                onTap: _hasSetCategories ? _showCoachIntroductions : null,
              ),
              
              const SizedBox(height: 24),
              
              // Daily Habits Section
              _buildSection(
                title: 'Set Your 10 Daily Habits',
                description: 'Lock in your 10 non-negotiable daily habits',
                icon: Icons.bolt,
                isCompleted: _hasSetHabits,
                onTap: _showDailyHabitsOnboarding,
              ),
              
              const Spacer(),
              
              // Continue Button
              if (_hasSetCategories && _hasMetCoaches && _hasSetHabits)
                Center(
                  child: ElevatedButton(
                    onPressed: () {
                      final userController = Provider.of<UserController2>(context, listen: false);
                      if (_updatedUser != null) {
                        userController.user = _updatedUser!;
                        widget.onComplete(_updatedUser);
                      } else {
                        widget.onComplete(null);
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: MolColors.green,
                      foregroundColor: Colors.black,
                      padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: const Text(
                      "Continue",
                      style: TextStyle(
                        fontFamily: 'Pirulen',
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
      // ─── Floating "Music" Button at bottom right ───
      floatingActionButton: SizedBox(
        width: 56,
        height: 56,
        child: FloatingActionButton(
          heroTag: "onboarding_music_fab", // Unique hero tag
          backgroundColor: Colors.black87,
          onPressed: _openMusicModal,
          child: const Icon(Icons.music_note, color: Colors.cyanAccent, size: 24),
        ),
      ),
    );
  }

  Widget _buildSection({
    required String title,
    required String description,
    required IconData icon,
    required bool isCompleted,
    required VoidCallback? onTap,
  }) {
    final bool isDisabled = onTap == null;
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: isDisabled ? Colors.grey[850] : Colors.grey[900],
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isCompleted ? MolColors.green : (isDisabled ? Colors.grey[700]! : Colors.grey[800]!),
            width: 2,
          ),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: isCompleted ? MolColors.green : (isDisabled ? Colors.grey[700] : Colors.grey[800]),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon,
                color: isCompleted ? Colors.black : (isDisabled ? Colors.grey[500] : Colors.white),
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    softWrap: true,
                    style: TextStyle(
                      color: isCompleted ? MolColors.green : (isDisabled ? Colors.grey[500] : Colors.white),
                      fontFamily: 'Pirulen',
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      height: 1.2,
                    ),
                  ),
                  const SizedBox(height: 6),
                  Text(
                    description,
                    softWrap: true,
                    style: TextStyle(
                      color: isDisabled ? Colors.grey[600] : Colors.grey[400],
                      fontFamily: 'Bitsumishi',
                      fontSize: 13,
                      height: 1.3,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              isCompleted ? Icons.check_circle : (isDisabled ? Icons.lock : Icons.arrow_forward_ios),
              color: isCompleted ? MolColors.green : (isDisabled ? Colors.grey[600] : Colors.grey[400]),
              size: 24,
            ),
          ],
        ),
      ),
    );
  }
} 