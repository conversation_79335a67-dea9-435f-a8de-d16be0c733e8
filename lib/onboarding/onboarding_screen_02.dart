import 'package:flutter/material.dart';
import '../models/user_model.dart';
import 'welcome_start_03.dart';
import 'north_star_setup_04.dart';
import 'onboarding_page_05.dart';
import 'dashboard_06.dart';

class OnboardingScreen extends StatefulWidget {
  final User? initialUser;

  const OnboardingScreen({super.key, this.initialUser});

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen> {
  int _step = 0;
  User? _user;

  @override
  void initState() {
    super.initState();
    // If an initial user is provided (e.g., guest user), use it
    if (widget.initialUser != null) {
      _user = widget.initialUser;
    }
  }

  void _onWelcomeContinue(User user) {
    print('🔄 OnboardingScreen: _onWelcomeContinue called with user: ${user.username}');
    if (!mounted) {
      print('🔄 OnboardingScreen: Widget not mounted, skipping setState');
      return;
    }
    setState(() {
      _user = user;
      _step = 1;
    });
    print('🔄 OnboardingScreen: State updated - step: $_step, user: ${_user?.username}');
  }

  void _onNorthStarContinue(User user) {
    print('🔄 OnboardingScreen: _onNorthStarContinue called with user: ${user.username}');
    if (!mounted) {
      print('🔄 OnboardingScreen: Widget not mounted, skipping setState');
      return;
    }
    setState(() {
      _user = user;
      _step = 2;
    });
    print('🔄 OnboardingScreen: State updated - step: $_step, user: ${_user?.username}');
  }

  void _onOnboardingComplete(User? user) {
    print('🔄 OnboardingScreen: _onOnboardingComplete called with user: ${user?.username}');
    if (!mounted) {
      print('🔄 OnboardingScreen: Widget not mounted, skipping setState');
      return;
    }
    setState(() {
      _user = user;
      _step = 3;
    });
    print('🔄 OnboardingScreen: State updated - step: $_step, user: ${_user?.username}');
  }

  void _onDashboardComplete(User? user) {
    // Onboarding finished, pop or navigate as needed
    Navigator.of(context).pop(user);
  }

  @override
  Widget build(BuildContext context) {
    if (_user == null && _step > 0) {
      // Defensive: shouldn't happen, but fallback to step 0
      _step = 0;
    }

    // If we have a guest user, skip welcome step and go to North Star
    if (_user != null && _user!.id.startsWith('guest_') && _step == 0) {
      _step = 1;
    }

    switch (_step) {
      case 0:
        // WelcomeStartPage expects a user, so create a blank one
        return WelcomeStartPage(
          user: _user ?? User.blank(id: '', username: ''),
          onContinue: _onWelcomeContinue,
        );
      case 1:
        return NorthStarSetup(
          user: _user!,
          onContinue: _onNorthStarContinue,
        );
      case 2:
        return OnboardingPage(
          user: _user!,
          onComplete: _onOnboardingComplete,
        );
      case 3:
        return Dashboard(
          user: _user!,
          onComplete: _onDashboardComplete,
        );
      default:
        return const Center(child: CircularProgressIndicator());
    }
  }
} 