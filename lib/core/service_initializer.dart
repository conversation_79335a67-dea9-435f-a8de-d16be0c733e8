// 📁 lib/core/service_initializer.dart

import 'package:flutter/foundation.dart';
import '../services/comprehensive_logging_service.dart';

/// Consolidated service initialization pattern to reduce duplicate initialization code.
/// 
/// Provides a consistent pattern for service initialization with error handling,
/// logging, and status tracking across all services in the app.
class ServiceInitializer {
  static final Map<String, bool> _initializationStatus = {};
  static final Map<String, DateTime> _initializationTimes = {};
  static final Map<String, String> _initializationErrors = {};

  /// Initialize a service with consistent error handling and logging
  static Future<bool> initializeService(
    String serviceName, {
    required Future<void> Function() initFunction,
    bool isRequired = false,
    Duration? timeout,
    String? description,
  }) async {
    final stopwatch = Stopwatch()..start();
    
    try {
      if (kDebugMode) {
        final desc = description ?? serviceName;
        debugPrint('🔄 Initializing $desc...');
      }

      // Apply timeout if specified
      if (timeout != null) {
        await initFunction().timeout(timeout);
      } else {
        await initFunction();
      }

      stopwatch.stop();
      _initializationStatus[serviceName] = true;
      _initializationTimes[serviceName] = DateTime.now();
      
      if (kDebugMode) {
        debugPrint('✅ $serviceName initialized in ${stopwatch.elapsedMilliseconds}ms');
      }

      // Log to comprehensive logging service if available
      try {
        await ComprehensiveLoggingService.logInfo(
          '$serviceName initialized successfully',
          category: 'service_initialization',
          metadata: {
            'service': serviceName,
            'duration_ms': stopwatch.elapsedMilliseconds,
            'required': isRequired,
          },
        );
      } catch (e) {
        // Ignore logging errors during service initialization
      }

      return true;

    } catch (e, stackTrace) {
      stopwatch.stop();
      _initializationStatus[serviceName] = false;
      _initializationErrors[serviceName] = e.toString();
      
      final errorMessage = isRequired 
          ? '❌ CRITICAL: Failed to initialize $serviceName: $e'
          : '⚠️ Failed to initialize $serviceName (continuing without it): $e';
      
      debugPrint(errorMessage);

      // Log error to comprehensive logging service if available
      try {
        await ComprehensiveLoggingService.logError(
          '$serviceName initialization failed',
          category: 'service_initialization',
          error: e,
          stackTrace: stackTrace,
          metadata: {
            'service': serviceName,
            'required': isRequired,
            'duration_ms': stopwatch.elapsedMilliseconds,
          },
        );
      } catch (logError) {
        // Ignore logging errors during service initialization
      }

      // For required services, rethrow the error
      if (isRequired) {
        rethrow;
      }

      return false;
    }
  }

  /// Initialize multiple services in sequence
  static Future<Map<String, bool>> initializeServices(
    Map<String, ServiceConfig> services,
  ) async {
    final results = <String, bool>{};
    
    for (final entry in services.entries) {
      final serviceName = entry.key;
      final config = entry.value;
      
      final success = await initializeService(
        serviceName,
        initFunction: config.initFunction,
        isRequired: config.isRequired,
        timeout: config.timeout,
        description: config.description,
      );
      
      results[serviceName] = success;
      
      // Add delay between services if specified
      if (config.delayAfter != null) {
        await Future.delayed(config.delayAfter!);
      }
    }
    
    return results;
  }

  /// Initialize services in parallel (for independent services)
  static Future<Map<String, bool>> initializeServicesParallel(
    Map<String, ServiceConfig> services,
  ) async {
    final futures = services.entries.map((entry) async {
      final serviceName = entry.key;
      final config = entry.value;
      
      final success = await initializeService(
        serviceName,
        initFunction: config.initFunction,
        isRequired: config.isRequired,
        timeout: config.timeout,
        description: config.description,
      );
      
      return MapEntry(serviceName, success);
    });

    final results = await Future.wait(futures);
    return Map.fromEntries(results);
  }

  /// Get initialization status for a service
  static bool isServiceInitialized(String serviceName) {
    return _initializationStatus[serviceName] ?? false;
  }

  /// Get initialization time for a service
  static DateTime? getServiceInitializationTime(String serviceName) {
    return _initializationTimes[serviceName];
  }

  /// Get initialization error for a service
  static String? getServiceInitializationError(String serviceName) {
    return _initializationErrors[serviceName];
  }

  /// Get overall initialization status
  static Map<String, bool> getAllInitializationStatus() {
    return Map.from(_initializationStatus);
  }

  /// Get initialization summary for debugging
  static String getInitializationSummary() {
    final total = _initializationStatus.length;
    final successful = _initializationStatus.values.where((s) => s).length;
    final failed = total - successful;
    
    return '📊 Service Initialization: $successful/$total successful ($failed failed)';
  }

  /// Get failed services list
  static List<String> getFailedServices() {
    return _initializationStatus.entries
        .where((entry) => !entry.value)
        .map((entry) => entry.key)
        .toList();
  }

  /// Clear initialization status (for testing)
  static void clearStatus() {
    _initializationStatus.clear();
    _initializationTimes.clear();
    _initializationErrors.clear();
  }
}

/// Configuration for service initialization
class ServiceConfig {
  final Future<void> Function() initFunction;
  final bool isRequired;
  final Duration? timeout;
  final String? description;
  final Duration? delayAfter;

  const ServiceConfig({
    required this.initFunction,
    this.isRequired = false,
    this.timeout,
    this.description,
    this.delayAfter,
  });

  /// Create a required service configuration
  factory ServiceConfig.required({
    required Future<void> Function() initFunction,
    Duration? timeout,
    String? description,
    Duration? delayAfter,
  }) {
    return ServiceConfig(
      initFunction: initFunction,
      isRequired: true,
      timeout: timeout,
      description: description,
      delayAfter: delayAfter,
    );
  }

  /// Create an optional service configuration
  factory ServiceConfig.optional({
    required Future<void> Function() initFunction,
    Duration? timeout,
    String? description,
    Duration? delayAfter,
  }) {
    return ServiceConfig(
      initFunction: initFunction,
      isRequired: false,
      timeout: timeout,
      description: description,
      delayAfter: delayAfter,
    );
  }
}
