// 📁 lib/core/app_initializer.dart

import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:firebase_core/firebase_core.dart';

// Services
import '../services/comprehensive_logging_service.dart';
import '../services/bulletproof_storage_service.dart';
import '../services/bulletproof_notification_service.dart';
import '../services/advanced_debug_service.dart';
import '../services/auth_service.dart';
import '../services/release_config_service.dart';

import '../services/app_health_service.dart';
import '../services/data_migration_service.dart';
import '../services/feature_flag_service.dart';

// Core systems
import '../bulletproof/error_handler.dart';
import 'service_initializer.dart';

/// Centralized app initialization system.
/// 
/// Handles all app startup logic including:
/// - Service initialization in correct dependency order
/// - Error handling and graceful degradation
/// - Environment configuration
/// - Platform-specific setup
class AppInitializer {
  static final Map<String, bool> _initSteps = <String, bool>{};
  static ErrorHandler? _errorHandler;

  /// Initialize service dependencies to prevent circular dependencies
  static Future<void> initializeServiceDependencies(ErrorHandler errorHandler) async {
    try {
      // Initialize services in dependency order

      // 1. Core services (no dependencies)
      final storageService = BulletproofStorageService();
      await storageService.initialize();

      // 2. User service initialization will be handled when needed
      // (UserService constructor handles its own initialization)

      // 3. Auth service initialization
      final authService = AuthService();
      await authService.initialize();

      // 4. Other services can now safely depend on core services
      debugPrint('✅ Service dependencies initialized in correct order');

    } catch (e, stackTrace) {
      debugPrint('❌ Failed to initialize service dependencies: $e');
      await errorHandler.handleError(e, stackTrace, context: 'ServiceDependencyInitialization');
    }
  }

  /// Initialize core app systems
  static Future<Map<String, bool>> initializeCoreServices() async {
    _initSteps.clear();
    
    // Initialize core error handling first
    _errorHandler = ErrorHandler();

    // Initialize service dependency injection
    await initializeServiceDependencies(_errorHandler!);

    // ── 1) Initialize logging system ───────────────
    await _initializeLogging();

    // ── 1.5) Initialize bulletproof storage system ───────────────
    await _initializeBulletproofStorage();

    // ── 1.6) Initialize bulletproof notification system ───────────────
    await _initializeBulletproofNotifications();

    // ── 1.7) Initialize advanced debug service ───────────────
    await _initializeAdvancedDebug();

    // ── 2) Skip performance systems for faster startup ───────────────
    // Defer heavy performance systems to prevent memory exhaustion
    _initSteps['performance'] = true; // Skip for now
    debugPrint('⚡ Performance systems deferred for faster startup');

    // ── 1.4) Configure release mode settings ───────────────
    await _initializeReleaseConfig();

    return Map.from(_initSteps);
  }

  static Future<void> _initializeLogging() async {
    try {
      await ComprehensiveLoggingService.initialize();
      await ComprehensiveLoggingService.logInfo('App startup initiated',
          category: 'startup',
          metadata: {'version': '1.0.0', 'platform': Platform.operatingSystem});
      _initSteps['logging'] = true;
      debugPrint('✅ Comprehensive logging initialized');
    } catch (e) {
      _initSteps['logging'] = false;
      debugPrint('⚠️ Failed to initialize logging (continuing without it): $e');
    }
  }

  static Future<void> _initializeBulletproofStorage() async {
    try {
      final bulletproofStorage = BulletproofStorageService();
      final storageInitialized = await bulletproofStorage.initialize();
      _initSteps['bulletproof_storage'] = storageInitialized;
      if (storageInitialized) {
        debugPrint('✅ Bulletproof storage system initialized');
      } else {
        debugPrint('⚠️ Bulletproof storage system failed to initialize (continuing with fallback)');
      }
    } catch (e) {
      _initSteps['bulletproof_storage'] = false;
      await ComprehensiveLoggingService.logError('Bulletproof storage initialization failed',
          category: 'startup', error: e);
      debugPrint('⚠️ Failed to initialize bulletproof storage (continuing with fallback): $e');
    }
  }

  static Future<void> _initializeBulletproofNotifications() async {
    try {
      final bulletproofNotifications = BulletproofNotificationService();
      final notificationsInitialized = await bulletproofNotifications.initialize();
      _initSteps['bulletproof_notifications'] = notificationsInitialized;
      if (notificationsInitialized) {
        debugPrint('✅ Bulletproof notification system initialized');
      } else {
        debugPrint('⚠️ Bulletproof notification system failed to initialize (continuing with fallback)');
      }
    } catch (e) {
      _initSteps['bulletproof_notifications'] = false;
      await ComprehensiveLoggingService.logError('Bulletproof notifications initialization failed',
          category: 'startup', error: e);
      debugPrint('⚠️ Failed to initialize bulletproof notifications (continuing with fallback): $e');
    }
  }

  static Future<void> _initializeAdvancedDebug() async {
    try {
      final advancedDebug = AdvancedDebugService();
      final debugInitialized = await advancedDebug.initialize();
      _initSteps['advanced_debug'] = debugInitialized;
      if (debugInitialized) {
        debugPrint('✅ Advanced debug service initialized');
      } else {
        debugPrint('⚠️ Advanced debug service failed to initialize (continuing without advanced debugging)');
      }
    } catch (e) {
      _initSteps['advanced_debug'] = false;
      await ComprehensiveLoggingService.logError('Advanced debug initialization failed',
          category: 'startup', error: e);
      debugPrint('⚠️ Failed to initialize advanced debug service (continuing without advanced debugging): $e');
    }
  }

  static Future<void> _initializeReleaseConfig() async {
    try {
      if (kDebugMode) {
        ReleaseConfigService.configureForDebug();
        debugPrint('🔧 Debug mode configuration applied');
      } else {
        ReleaseConfigService.configureForRelease();
        debugPrint('🚀 Release mode configuration applied');
      }

      // Validate configuration
      final isValid = ReleaseConfigService.validateReleaseConfiguration();
      if (!isValid && !kDebugMode) {
        debugPrint('⚠️ Release configuration validation failed - some debug features may still be visible');
      } else {
        debugPrint('✅ Release configuration validated successfully');
      }

      // Log configuration summary
      final config = ReleaseConfigService.getConfigurationSummary();
      debugPrint('📊 Configuration: ${config['mode']} mode');
      debugPrint('   Debug features: ${config['debugFeatures']}');
      debugPrint('   Admin controls: ${config['adminControls']}');
      debugPrint('   Secret buttons: ${config['secretButtons']}');

      _initSteps['release_config'] = true;
    } catch (e) {
      _initSteps['release_config'] = false;
      await ComprehensiveLoggingService.logError('Release configuration failed',
          category: 'startup', error: e);
      debugPrint('⚠️ Failed to configure release settings (continuing with defaults): $e');
    }
  }

  /// Set up platform-specific configurations
  static Future<void> setupPlatformConfiguration() async {
    // Ensure Flutter binding is initialized
    WidgetsFlutterBinding.ensureInitialized();

    // Set preferred orientations for mobile devices
    await SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
  }

  /// Get initialization status
  static Map<String, bool> getInitializationStatus() {
    return Map.from(_initSteps);
  }

  /// Get error handler instance
  static ErrorHandler? getErrorHandler() {
    return _errorHandler;
  }

  /// Initialize environment configuration
  static Future<void> initializeEnvironment() async {
    try {
      await dotenv.load(fileName: ".env");
      _initSteps['environment'] = true;
      debugPrint('✅ Environment configuration loaded');
    } catch (e) {
      _initSteps['environment'] = false;
      debugPrint('⚠️ Failed to load environment configuration (continuing with defaults): $e');
    }
  }

  /// Initialize Firebase
  static Future<void> initializeFirebase() async {
    try {
      await Firebase.initializeApp();
      _initSteps['firebase'] = true;
      debugPrint('✅ Firebase initialized');
    } catch (e) {
      _initSteps['firebase'] = false;
      await ComprehensiveLoggingService.logError('Firebase initialization failed',
          category: 'startup', error: e);
      debugPrint('⚠️ Failed to initialize Firebase (continuing without Firebase features): $e');
    }
  }

  /// Initialize additional services using consolidated ServiceInitializer
  static Future<void> initializeAdditionalServices() async {
    final services = {
      'app_health': ServiceConfig.optional(
        initFunction: () async {
          final healthResult = await AppHealthService.performStartupHealthCheck();
          final isHealthy = healthResult.overallHealth == HealthStatus.healthy;
          if (!isHealthy) {
            debugPrint('⚠️ App health check shows: ${healthResult.overallHealth}');
          }
        },
        description: 'App Health Service',
        timeout: const Duration(seconds: 10),
      ),
      'data_migration': ServiceConfig.optional(
        initFunction: () async {
          final migrationResult = await DataMigrationService.performMigrationIfNeeded();
          if (!migrationResult.success) {
            debugPrint('⚠️ Data migration failed: ${migrationResult.error}');
          }
        },
        description: 'Data Migration Service',
        timeout: const Duration(seconds: 15),
      ),
      'feature_flags': ServiceConfig.optional(
        initFunction: () async {
          await FeatureFlagService.initialize();
        },
        description: 'Feature Flag Service',
        timeout: const Duration(seconds: 5),
      ),
    };

    final results = await ServiceInitializer.initializeServices(services);

    // Update internal status tracking
    for (final entry in results.entries) {
      _initSteps[entry.key] = entry.value;
    }

    if (kDebugMode) {
      debugPrint(ServiceInitializer.getInitializationSummary());
    }
  }

  /// Complete initialization sequence (critical services only for fast startup)
  static Future<Map<String, bool>> completeInitialization() async {
    await setupPlatformConfiguration();
    await initializeEnvironment();
    await initializeFirebase();
    await initializeCoreServices();

    // Skip additional services for faster startup - they'll be loaded lazily
    debugPrint('⚡ Fast startup complete - non-critical services will load in background');

    return getInitializationStatus();
  }

  /// Initialize non-critical services lazily in background
  static Future<void> initializeNonCriticalServicesLazily() async {
    // Delay to allow UI to load first
    await Future.delayed(const Duration(milliseconds: 500));

    debugPrint('🔄 Starting lazy initialization of non-critical services...');
    await initializeAdditionalServices();
    debugPrint('✅ Lazy initialization complete');
  }

  /// Initialize services that are needed immediately for app functionality
  static Future<void> initializeCriticalServicesOnly() async {
    await setupPlatformConfiguration();
    await initializeEnvironment();
    await initializeFirebase();

    // Only initialize the most critical services
    _initSteps.clear();
    _errorHandler = ErrorHandler();
    await initializeServiceDependencies(_errorHandler!);
    await _initializeLogging();
    await _initializeBulletproofStorage();
    await _initializeReleaseConfig();

    debugPrint('⚡ Critical services initialized for ultra-fast startup');
  }
}
