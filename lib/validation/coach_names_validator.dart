// lib/validation/coach_names_validator.dart

import '../prompts/mxd_life_coaches.dart';

/// Validator to ensure all coach names are correctly implemented
class CoachNamesValidator {
  
  /// Validate all coach names match the authoritative source
  static Map<String, dynamic> validateAllCoachNames() {
    final results = <String, dynamic>{
      'isValid': true,
      'errors': <String>[],
      'coaches': <Map<String, dynamic>>[],
    };
    
    print('🧠 VALIDATING ALL 12 SUPERINTELLIGENT COACHES...\n');
    
    for (final coach in mxdLifeCoaches) {
      final coachErrors = <String>[];
      var isValidCoach = true;

      final coachData = {
        'category': coach.category,
        'maleName': coach.maleName,
        'femaleName': coach.femaleName,
        'description': coach.description,
        'isValid': isValidCoach,
        'errors': coachErrors,
      };
      
      // Validate coach names are not empty
      if (coach.maleName.isEmpty) {
        coachErrors.add('Male name is empty');
        isValidCoach = false;
        results['isValid'] = false;
      }

      if (coach.femaleName.isEmpty) {
        coachErrors.add('Female name is empty');
        isValidCoach = false;
        results['isValid'] = false;
      }

      // Validate category is not empty
      if (coach.category.isEmpty) {
        coachErrors.add('Category is empty');
        isValidCoach = false;
        results['isValid'] = false;
      }

      // Update the coach data
      coachData['isValid'] = isValidCoach;
      coachData['errors'] = coachErrors;
      
      results['coaches'].add(coachData);
      
      // Print coach info
      print('${_getCategoryEmoji(coach.category)} ${coach.category.toUpperCase()} COACHES:');
      print('   👨 Male: ${coach.maleName}');
      print('   👩 Female: ${coach.femaleName}');
      print('   📝 Description: ${coach.description.substring(0, 50)}...');
      print('   ✅ Status: ${isValidCoach ? 'VALID' : 'INVALID'}');
      if (coachErrors.isNotEmpty) {
        print('   ❌ Errors: ${coachErrors.join(', ')}');
      }
      print('');
    }
    
    return results;
  }
  
  /// Get the correct coach name for given parameters
  static String getCoachName(String category, String userGender, [Map<String, String>? assignedCoaches]) {
    // Handle non-gender users with assigned coaches
    String effectiveGender = userGender;
    if (userGender.toLowerCase() == 'non-gender' && assignedCoaches != null && assignedCoaches.containsKey(category)) {
      effectiveGender = assignedCoaches[category]!;
    }

    // Handle custom categories with hardcoded coach assignments
    if (category == 'Custom Category 1') {
      return effectiveGender.toLowerCase() == 'female' ? 'Luna' : 'Aether';
    } else if (category == 'Custom Category 2') {
      return effectiveGender.toLowerCase() == 'female' ? 'Elysia' : 'Chronos';
    }

    // Get coach from authoritative source
    final coach = mxdLifeCoaches.firstWhere(
      (c) => c.category.toLowerCase() == category.toLowerCase(),
      orElse: () => mxdLifeCoaches.first,
    );

    // Gender-based selection
    return effectiveGender.toLowerCase() == 'female' ? coach.femaleName : coach.maleName;
  }
  
  /// Test coach selection for all scenarios
  static void testCoachSelection() {
    print('🧪 TESTING COACH SELECTION LOGIC...\n');
    
    final categories = mxdLifeCoaches.map((c) => c.category).toList();
    final genders = ['male', 'female', 'non-binary'];
    
    for (final category in categories) {
      print('📂 Testing $category coaches:');
      
      for (final gender in genders) {
        final coachName = getCoachName(category, gender);
        print('   $gender → $coachName');
      }
      
      // Test with assigned coaches (non-gender users)
      final assignedCoaches = <String, String>{
        category: 'CustomAssignedCoach',
      };
      final assignedName = getCoachName(category, 'non-binary', assignedCoaches);
      print('   assigned → $assignedName');
      print('');
    }
  }
  
  /// Validate icon naming convention
  static void validateIconNaming() {
    print('🎨 VALIDATING ICON NAMING CONVENTION...\n');
    
    for (final coach in mxdLifeCoaches) {
      final maleIconName = '${coach.maleName.toLowerCase().replaceAll('-', '_')}_icon.png';
      final femaleIconName = '${coach.femaleName.toLowerCase().replaceAll('-', '_')}_icon.png';
      
      print('${_getCategoryEmoji(coach.category)} ${coach.category}:');
      print('   👨 ${coach.maleName} → $maleIconName');
      print('   👩 ${coach.femaleName} → $femaleIconName');
      print('');
    }
  }
  
  /// Get emoji for category
  static String _getCategoryEmoji(String category) {
    switch (category.toLowerCase()) {
      case 'health':
        return '💪';
      case 'wealth':
        return '💰';
      case 'purpose':
        return '✨';
      case 'connection':
        return '❤️';
      case 'custom category 1':
        return '🔥';
      case 'custom category 2':
        return '⚡';
      default:
        return '🎯';
    }
  }
  
  /// Run complete validation
  static void runCompleteValidation() {
    print('🚀 SUPERINTELLIGENT COACHES VALIDATION\n');
    print('=' * 60);
    
    // 1. Validate coach names
    final validation = validateAllCoachNames();
    
    // 2. Test coach selection
    testCoachSelection();
    
    // 3. Validate icon naming
    validateIconNaming();
    
    // 4. Summary
    print('📊 VALIDATION SUMMARY:');
    print('   Total Coaches: ${mxdLifeCoaches.length}');
    final coaches = validation['coaches'] as List<Map<String, dynamic>>;
    print('   Valid Coaches: ${coaches.where((c) => c['isValid'] as bool).length}');
    print('   Invalid Coaches: ${coaches.where((c) => !(c['isValid'] as bool)).length}');
    print('   Overall Status: ${validation['isValid'] ? '✅ PASSED' : '❌ FAILED'}');
    
    if (!(validation['isValid'] as bool)) {
      print('\n❌ ERRORS FOUND:');
      for (final coach in coaches) {
        if (!(coach['isValid'] as bool)) {
          final errors = coach['errors'] as List<String>;
          print('   ${coach['category']}: ${errors.join(', ')}');
        }
      }
    } else {
      print('\n🎉 ALL COACHES VALIDATED SUCCESSFULLY!');
      print('🧠 Your 12 superintelligent coaches are ready to transform lives!');
    }
  }
}

/// Quick test function
void main() {
  CoachNamesValidator.runCompleteValidation();
}
