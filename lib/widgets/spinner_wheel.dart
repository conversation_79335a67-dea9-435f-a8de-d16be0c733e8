import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/user_model.dart';
import '../services/reward_engine.dart';
import '../utils/debug_logger.dart';
import '../theme/colors.dart';

/// Data class for roulette wheel categories
class RouletteCategory {
  final String name;
  final Color color;

  const RouletteCategory(this.name, this.color);
}

/// Animated spinner wheel widget with neon retrofuturistic design
/// 
/// Features:
/// - Gold spinner button with neon glow effects
/// - Animated wheel rotation with smooth easing
/// - Progress indicator showing EXP toward next unlock
/// - Success/failure modals with appropriate celebrations
/// - Integration with RewardEngine for bonus mechanics
class SpinnerWheel extends StatefulWidget {
  final User user;
  final Function(User) onUserUpdate;
  final VoidCallback? onSpinComplete;
  
  const SpinnerWheel({
    super.key,
    required this.user,
    required this.onUserUpdate,
    this.onSpinComplete,
  });

  @override
  State<SpinnerWheel> createState() => _SpinnerWheelState();
}

class _SpinnerWheelState extends State<SpinnerWheel>
    with TickerProviderStateMixin {
  late AnimationController _rotationController;
  late AnimationController _glowController;
  late Animation<double> _rotationAnimation;
  late Animation<double> _glowAnimation;
  
  bool _isSpinning = false;
  SpinResult? _lastResult;
  
  @override
  void initState() {
    super.initState();

    // Rotation animation for the wheel (duration will be dynamic based on spin distance)
    _rotationController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );

    // Glow animation for the button
    _glowController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    )..repeat(reverse: true);

    // Initialize with a default animation (will be overridden during actual spins)
    _rotationAnimation = Tween<double>(
      begin: 0,
      end: 0, // Will be set dynamically during spin
    ).animate(CurvedAnimation(
      parent: _rotationController,
      curve: Curves.easeOutCubic,
    ));

    _glowAnimation = Tween<double>(
      begin: 0.3,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _glowController,
      curve: Curves.easeInOut,
    ));
  }
  
  @override
  void dispose() {
    _rotationController.dispose();
    _glowController.dispose();
    super.dispose();
  }
  
  void _spinWheel() async {
    if (_isSpinning || !RewardEngine.instance.canSpin(widget.user)) {
      DebugLogger.log('SpinnerWheel', 'Spin blocked - already spinning or no plays available');
      return;
    }

    setState(() {
      _isSpinning = true;
    });

    // Haptic feedback
    HapticFeedback.mediumImpact();

    // AUTHENTIC RANDOM SPINNING: Generate random landing position
    final random = Random();
    final categories = _getCategories();
    final selectedCategoryIndex = random.nextInt(categories.length);
    final selectedCategory = categories[selectedCategoryIndex];

    // Random number of spins (3-8 full rotations for dramatic effect)
    final fullSpins = 3 + random.nextInt(6);

    // Calculate precise landing angle for the selected category
    final anglePerSegment = 2 * pi / categories.length;
    final baseAngleForCategory = selectedCategoryIndex * anglePerSegment;

    // Add random offset within the segment (±25% of segment width for natural variation)
    final segmentVariation = (random.nextDouble() - 0.5) * anglePerSegment * 0.5;
    final finalAngle = (fullSpins * 2 * pi) + baseAngleForCategory + segmentVariation;

    DebugLogger.log('SpinnerWheel',
      'Random spin: $fullSpins rotations, landing on ${selectedCategory.name} '
      '(index $selectedCategoryIndex, angle ${(finalAngle * 180 / pi).toStringAsFixed(1)}°)');

    // Dynamic animation duration based on number of spins (more spins = longer duration)
    final animationDuration = Duration(milliseconds: 2000 + (fullSpins * 300));
    _rotationController.duration = animationDuration;

    // Create dynamic animation with random target
    _rotationAnimation = Tween<double>(
      begin: 0,
      end: finalAngle,
    ).animate(CurvedAnimation(
      parent: _rotationController,
      curve: Curves.easeOutCubic,
    ));

    // Start the authentic spinning animation
    await _rotationController.forward();

    // Get spin result from RewardEngine with the landed category
    final result = RewardEngine.instance.spinWheelWithCategory(widget.user, selectedCategory.name);
    final updatedUser = RewardEngine.instance.useSpinnerPlay(widget.user, result);

    DebugLogger.log('SpinnerWheel', 'Spin completed: $result');

    setState(() {
      _lastResult = result;
      _isSpinning = false;
    });

    // Update user state
    widget.onUserUpdate(updatedUser);

    // Reset rotation for next spin
    _rotationController.reset();

    // Show result modal
    _showResultModal(result);

    // Notify completion
    widget.onSpinComplete?.call();
  }
  
  void _showResultModal(SpinResult result) {
    // Store the last result for potential debugging or analytics
    _lastResult = result;

    // Debug log for analytics and comparison (reduced logging)
    if (kDebugMode) {
      final previousResult = _lastResult;
      // print('🎰 Spinner result: ${result.success ? "WIN" : "LOSE"} - ${result.bonusExp}XP');
      if (previousResult != null) {
        // print('📊 Previous result comparison: ${previousResult.success ? "WIN" : "LOSE"} vs ${result.success ? "WIN" : "LOSE"}');
      }
    }

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => _SpinResultModal(result: result),
    );
  }
  
  @override
  Widget build(BuildContext context) {
    final canSpin = RewardEngine.instance.canSpin(widget.user);
    final expToNext = RewardEngine.instance.getExpToNextSpinner(widget.user.dailyExpTotal);
    final progress = RewardEngine.instance.getSpinnerProgress(widget.user.dailyExpTotal);
    
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          // Progress indicator
          _buildProgressIndicator(expToNext, progress),
          
          const SizedBox(height: 20),
          
          // Spinner wheel
          _buildSpinnerWheel(canSpin),
          
          const SizedBox(height: 20),
          
          // Spin button
          _buildSpinButton(canSpin),
          
          const SizedBox(height: 10),
          
          // Available plays counter
          _buildPlaysCounter(),
        ],
      ),
    );
  }
  
  Widget _buildProgressIndicator(int expToNext, double progress) {
    return Column(
      children: [
        Text(
          expToNext > 0 ? '$expToNext EXP until next spin' : 'Spinner unlocked!',
          style: const TextStyle(
            color: Colors.white,
            fontSize: 14,
            fontFamily: 'Bitsumishi',
          ),
        ),
        const SizedBox(height: 8),
        Container(
          height: 8,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
            border: Border.all(color: Colors.cyan, width: 1),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(4),
            child: LinearProgressIndicator(
              value: progress,
              backgroundColor: Colors.transparent,
              valueColor: AlwaysStoppedAnimation<Color>(
                Colors.cyan.withValues(alpha: 0.8),
              ),
            ),
          ),
        ),
      ],
    );
  }
  
  Widget _buildSpinnerWheel(bool canSpin) {
    return Stack(
      alignment: Alignment.center,
      children: [
        // Roulette wheel with 6 segments
        AnimatedBuilder(
          animation: _rotationAnimation,
          builder: (context, child) {
            return Transform.rotate(
              angle: _rotationAnimation.value,
              child: SizedBox(
                width: 200,
                height: 200,
                child: CustomPaint(
                  painter: RouletteWheelPainter(
                    categories: _getCategories(),
                    canSpin: canSpin,
                  ),
                  size: const Size(200, 200),
                ),
              ),
            );
          },
        ),

        // Lightning bolt pointer at top
        Positioned(
          top: -5,
          child: SizedBox(
            width: 20,
            height: 30,
            child: CustomPaint(
              painter: LightningBoltPainter(
                color: canSpin ? Colors.yellow : Colors.grey,
                glowing: canSpin,
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// Get the 6 categories for the roulette wheel
  List<RouletteCategory> _getCategories() {
    // Get user's custom categories or use defaults
    final custom1 = widget.user.customCategories.isNotEmpty
        ? widget.user.customCategories[0]
        : 'Custom 1';
    final custom2 = widget.user.customCategories.length > 1
        ? widget.user.customCategories[1]
        : 'Custom 2';

    // Debug: Custom categories (reduced logging)
    // print('🎰 SpinnerWheel: Using custom categories - Custom 1: "$custom1", Custom 2: "$custom2"');

    return [
      RouletteCategory('Health', getCategoryColor('Health')),
      RouletteCategory('Wealth', getCategoryColor('Wealth')),
      RouletteCategory('Purpose', getCategoryColor('Purpose')),
      RouletteCategory('Connection', getCategoryColor('Connection')),
      RouletteCategory(custom1, MolColors.red),
      RouletteCategory(custom2, MolColors.orange),
    ];
  }
  
  Widget _buildSpinButton(bool canSpin) {
    return AnimatedBuilder(
      animation: _glowAnimation,
      builder: (context, child) {
        return GestureDetector(
          onTap: canSpin ? _spinWheel : null,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 30, vertical: 15),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(25),
              gradient: LinearGradient(
                colors: canSpin ? [
                  Colors.amber.withValues(alpha: 0.8),
                  Colors.orange.withValues(alpha: 0.6),
                ] : [
                  Colors.grey.withValues(alpha: 0.5),
                  Colors.grey.withValues(alpha: 0.3),
                ],
              ),
              border: Border.all(
                color: canSpin ? Colors.amber : Colors.grey,
                width: 2,
              ),
              boxShadow: canSpin ? [
                BoxShadow(
                  color: Colors.amber.withValues(alpha: _glowAnimation.value * 0.6),
                  blurRadius: 15,
                  spreadRadius: 3,
                ),
              ] : null,
            ),
            child: Text(
              _isSpinning ? 'SPINNING...' : 'SPIN WHEEL',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontFamily: 'Pirulen',
                fontWeight: FontWeight.bold,
                shadows: [
                  Shadow(
                    color: Colors.black.withValues(alpha: 0.8),
                    offset: const Offset(1, 1),
                    blurRadius: 2,
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
  
  Widget _buildPlaysCounter() {
    return Text(
      'Available Spins: ${widget.user.availableSpinnerPlays}',
      style: const TextStyle(
        color: Colors.white70,
        fontSize: 12,
        fontFamily: 'Bitsumishi',
      ),
    );
  }
}

/// Modal showing spin result with appropriate animations
class _SpinResultModal extends StatelessWidget {
  final SpinResult result;
  
  const _SpinResultModal({required this.result});
  
  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.9),
          borderRadius: BorderRadius.circular(15),
          border: Border.all(
            color: result.success ? Colors.green : Colors.red,
            width: 2,
          ),
          boxShadow: [
            BoxShadow(
              color: (result.success ? Colors.green : Colors.red).withValues(alpha: 0.5),
              blurRadius: 20,
              spreadRadius: 5,
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              result.success ? Icons.celebration : Icons.sentiment_dissatisfied,
              size: 60,
              color: result.success ? Colors.green : Colors.red,
            ),
            const SizedBox(height: 15),
            Text(
              result.success ? 'SUCCESS!' : 'TRY AGAIN!',
              style: TextStyle(
                color: result.success ? Colors.green : Colors.red,
                fontSize: 24,
                fontFamily: 'Pirulen',
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 10),
            Text(
              result.message,
              textAlign: TextAlign.center,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontFamily: 'Bitsumishi',
              ),
            ),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(),
              style: ElevatedButton.styleFrom(
                backgroundColor: result.success ? Colors.green : Colors.red,
                foregroundColor: Colors.white,
              ),
              child: const Text('CONTINUE'),
            ),
          ],
        ),
      ),
    );
  }
}

/// Custom painter for the roulette wheel with 6 segments
class RouletteWheelPainter extends CustomPainter {
  final List<RouletteCategory> categories;
  final bool canSpin;

  RouletteWheelPainter({
    required this.categories,
    required this.canSpin,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // Debug logging for spinner wheel (reduced logging)
    // print('🎰 SpinnerWheel: Painting wheel with ${categories.length} categories');
    // for (int i = 0; i < categories.length; i++) {
    //   print('🎰 Category $i: ${categories[i].name} (${categories[i].color})');
    // }

    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2;

    // Draw each segment (6 equal segments = 60 degrees each)
    const segmentAngle = 2 * pi / 6;

    for (int i = 0; i < categories.length; i++) {
      final category = categories[i];
      final startAngle = i * segmentAngle - pi / 2; // Start from top

      // Create segment path
      final path = Path();
      path.moveTo(center.dx, center.dy);
      path.arcTo(
        Rect.fromCircle(center: center, radius: radius),
        startAngle,
        segmentAngle,
        false,
      );
      path.close();

      // Fill segment with category color
      final paint = Paint()
        ..color = category.color
        ..style = PaintingStyle.fill;

      canvas.drawPath(path, paint);

      // Add neon glow effect if can spin
      if (canSpin) {
        final glowPaint = Paint()
          ..color = category.color.withValues(alpha: 0.3)
          ..style = PaintingStyle.stroke
          ..strokeWidth = 3
          ..maskFilter = const MaskFilter.blur(BlurStyle.outer, 5);

        canvas.drawPath(path, glowPaint);
      }

      // Draw segment border
      final borderPaint = Paint()
        ..color = Colors.white.withValues(alpha: 0.8)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2;

      canvas.drawPath(path, borderPaint);

      // Draw category text character-by-character along the radial spine
      _drawRadialText(
        canvas,
        center,
        radius,
        category.name.toUpperCase(),
        startAngle + segmentAngle / 2,
      );

      // Debug: Segment text rendering (reduced logging)
      // print('🎰 Drawing character-by-character radial text: "${category.name}" at segment angle ${(startAngle + segmentAngle / 2) * 180 / pi}°');
    }

    // Draw outer border
    final outerBorderPaint = Paint()
      ..color = canSpin ? Colors.cyan : Colors.grey
      ..style = PaintingStyle.stroke
      ..strokeWidth = 4;

    canvas.drawCircle(center, radius, outerBorderPaint);

    // Add outer glow if can spin
    if (canSpin) {
      final outerGlowPaint = Paint()
        ..color = Colors.cyan.withValues(alpha: 0.5)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 8
        ..maskFilter = const MaskFilter.blur(BlurStyle.outer, 10);

      canvas.drawCircle(center, radius, outerGlowPaint);
    }
  }

  /// Draw text following the curved arc of a segment for optimal readability
  void _drawRadialText(Canvas canvas, Offset center, double radius, String text, double segmentCenterAngle) {
    if (text.isEmpty) return;

    // Calculate optimal text positioning for readability
    const segmentAngle = 2 * pi / 6; // 60 degrees per segment
    final textRadius = radius * 0.75; // Position at 75% of radius for better visibility

    // Calculate the arc length available for text (90% of segment width for better spacing)
    final availableArcLength = textRadius * segmentAngle * 0.9;

    // Calculate character spacing based on available arc length
    final charSpacing = availableArcLength / text.length;

    // Start angle for first character (centered around segment center)
    final startAngle = segmentCenterAngle - (text.length - 1) * charSpacing / (2 * textRadius);

    // Debug: Curved arc text calculation (reduced logging)
    // print('🎰 Curved arc text: "$text" with ${text.length} chars, arc length: ${availableArcLength.toStringAsFixed(2)}, char spacing: ${charSpacing.toStringAsFixed(2)}');

    // Draw each character along the curved path
    for (int i = 0; i < text.length; i++) {
      final char = text[i];
      final charAngle = startAngle + (i * charSpacing / textRadius);

      // Calculate position on the arc
      final charX = center.dx + textRadius * cos(charAngle);
      final charY = center.dy + textRadius * sin(charAngle);

      // Save canvas state for each character
      canvas.save();

      // Translate to character position
      canvas.translate(charX, charY);

      // Rotate character to be tangent to the arc (perpendicular to radius)
      // Add 90 degrees to make text readable from outside the circle
      canvas.rotate(charAngle + pi / 2);

      // Create text painter for single character
      final charPainter = TextPainter(
        text: TextSpan(
          text: char,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 6, // 20% smaller (11 * 0.8 = 8.8)
            fontFamily: 'Pirulen',
            fontWeight: FontWeight.bold,
            shadows: [
              Shadow(
                color: Colors.black,
                offset: Offset(1, 1),
                blurRadius: 3,
              ),
            ],
          ),
        ),
        textAlign: TextAlign.center,
        textDirection: TextDirection.ltr,
      )..layout();

      // Center the character at its position
      final charPosition = Offset(
        -charPainter.width / 2,
        -charPainter.height / 2,
      );

      charPainter.paint(canvas, charPosition);

      // Debug: Character positioning (reduced logging)
      // print('🎰 Arc character "$char" at angle ${charAngle * 180 / pi}°, position (${charX.toStringAsFixed(1)}, ${charY.toStringAsFixed(1)})');

      // Restore canvas state
      canvas.restore();
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// Custom painter for the lightning bolt pointer
class LightningBoltPainter extends CustomPainter {
  final Color color;
  final bool glowing;

  LightningBoltPainter({
    required this.color,
    required this.glowing,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final path = Path();
    final width = size.width;
    final height = size.height;

    // Create lightning bolt shape
    path.moveTo(width * 0.3, 0);
    path.lineTo(width * 0.7, 0);
    path.lineTo(width * 0.5, height * 0.4);
    path.lineTo(width * 0.8, height * 0.4);
    path.lineTo(width * 0.2, height);
    path.lineTo(width * 0.4, height * 0.6);
    path.lineTo(width * 0.1, height * 0.6);
    path.close();

    // Fill lightning bolt
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    canvas.drawPath(path, paint);

    // Add glow effect if glowing
    if (glowing) {
      final glowPaint = Paint()
        ..color = color.withValues(alpha: 0.6)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2
        ..maskFilter = const MaskFilter.blur(BlurStyle.outer, 3);

      canvas.drawPath(path, glowPaint);
    }

    // Add border
    final borderPaint = Paint()
      ..color = Colors.white.withValues(alpha: 0.8)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1;

    canvas.drawPath(path, borderPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
