import 'package:flutter/material.dart';
import '../design_system/design_system.dart';

/// Widget for displaying empty states with engaging visuals and clear actions.
/// 
/// Provides consistent empty state display across the app with:
/// - Contextual illustrations and messages
/// - Clear call-to-action buttons
/// - Animated entrance effects
/// - Accessibility support
/// 
/// Example usage:
/// ```dart
/// EmptyStateWidget(
///   type: EmptyStateType.habits,
///   onAction: () => _createFirstHabit(),
/// )
/// ```
class EmptyStateWidget extends StatefulWidget {
  /// The type of empty state to display
  final EmptyStateType type;
  
  /// Callback for the primary action
  final VoidCallback? onAction;
  
  /// Custom title override
  final String? customTitle;
  
  /// Custom message override
  final String? customMessage;
  
  /// Custom action label override
  final String? customActionLabel;
  
  /// Custom icon override
  final IconData? customIcon;
  
  /// Whether to show the action button
  final bool showAction;

  const EmptyStateWidget({
    super.key,
    required this.type,
    this.onAction,
    this.customTitle,
    this.customMessage,
    this.customActionLabel,
    this.customIcon,
    this.showAction = true,
  });

  @override
  State<EmptyStateWidget> createState() => _EmptyStateWidgetState();
}

class _EmptyStateWidgetState extends State<EmptyStateWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: DesignTokens.durationXSlow,
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.0, 0.6, curve: Curves.easeOut),
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.2, 1.0, curve: Curves.elasticOut),
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final emptyStateInfo = _getEmptyStateInfo();
    
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: ScaleTransition(
            scale: _scaleAnimation,
            child: Center(
              child: Padding(
                padding: const EdgeInsets.all(32),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Illustration
                    _buildIllustration(emptyStateInfo),
                    const SizedBox(height: 32),
                    
                    // Title
                    Text(
                      widget.customTitle ?? emptyStateInfo.title,
                      style: const TextStyle(
                        fontFamily: 'Pirulen',
                        fontSize: 24,
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    
                    // Message
                    Text(
                      widget.customMessage ?? emptyStateInfo.message,
                      style: const TextStyle(
                        fontFamily: 'Bitsumishi',
                        fontSize: 16,
                        color: Colors.white70,
                        height: 1.5,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 32),
                    
                    // Action Button
                    if (widget.showAction && widget.onAction != null)
                      _buildActionButton(emptyStateInfo),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildIllustration(EmptyStateInfo info) {
    return Container(
      width: 120,
      height: 120,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: RadialGradient(
          colors: [
            info.color.withValues(alpha: 0.2),
            info.color.withValues(alpha: 0.05),
          ],
        ),
        border: Border.all(
          color: info.color.withValues(alpha: 0.3),
          width: 2,
        ),
      ),
      child: Icon(
        widget.customIcon ?? info.icon,
        size: 60,
        color: info.color,
      ),
    );
  }

  Widget _buildActionButton(EmptyStateInfo info) {
    return SizedBox(
      width: 200,
      height: 48,
      child: ElevatedButton(
        onPressed: widget.onAction,
        style: ElevatedButton.styleFrom(
          backgroundColor: info.color,
          foregroundColor: Colors.black,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(24),
          ),
          elevation: 4,
          shadowColor: info.color.withValues(alpha: 0.3),
        ),
        child: Text(
          widget.customActionLabel ?? info.actionLabel,
          style: const TextStyle(
            fontFamily: 'Bitsumishi',
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  EmptyStateInfo _getEmptyStateInfo() {
    switch (widget.type) {
      case EmptyStateType.habits:
        return const EmptyStateInfo(
          title: 'No Habits Yet',
          message: 'Start building better habits to level up your life. Create your first habit and begin your journey!',
          icon: Icons.track_changes,
          color: MolColors.green,
          actionLabel: 'Create First Habit',
        );
        
      case EmptyStateType.diary:
        return const EmptyStateInfo(
          title: 'Empty Diary',
          message: 'Your diary is waiting for your first entry. Start logging your experiences and track your progress!',
          icon: Icons.book,
          color: MolColors.blue,
          actionLabel: 'Add Entry',
        );
        
      case EmptyStateType.bounties:
        return const EmptyStateInfo(
          title: 'No Bounties Available',
          message: 'Complete your daily habits to unlock exciting bounties and earn bonus experience points!',
          icon: Icons.star,
          color: MolColors.yellow,
          actionLabel: 'Complete Habits',
        );
        
      case EmptyStateType.northStar:
        return const EmptyStateInfo(
          title: 'No North Star Quest',
          message: 'Set your North Star quest to define your long-term vision and track meaningful progress.',
          icon: Icons.star,
          color: MolColors.purple,
          actionLabel: 'Create Quest',
        );
        
      case EmptyStateType.categories:
        return const EmptyStateInfo(
          title: 'No Custom Categories',
          message: 'Create custom categories to track progress in areas that matter most to you.',
          icon: Icons.category,
          color: MolColors.orange,
          actionLabel: 'Add Category',
        );
        
      case EmptyStateType.users:
        return const EmptyStateInfo(
          title: 'No Users Found',
          message: 'Get started by creating your first user account and begin your personal development journey.',
          icon: Icons.person_add,
          color: MolColors.cyan,
          actionLabel: 'Create User',
        );
        
      case EmptyStateType.search:
        return const EmptyStateInfo(
          title: 'No Results Found',
          message: 'Try adjusting your search terms or filters to find what you\'re looking for.',
          icon: Icons.search_off,
          color: Colors.grey,
          actionLabel: 'Clear Search',
        );
        
      case EmptyStateType.network:
        return const EmptyStateInfo(
          title: 'No Connection',
          message: 'Check your internet connection and try again. Some features require an active connection.',
          icon: Icons.cloud_off,
          color: Colors.orange,
          actionLabel: 'Retry',
        );
        
      case EmptyStateType.generic:
        return const EmptyStateInfo(
          title: 'Nothing Here Yet',
          message: 'This area is empty right now. Check back later or take action to add content.',
          icon: Icons.inbox,
          color: Colors.grey,
          actionLabel: 'Get Started',
        );
    }
  }
}

/// Types of empty states supported by the widget
enum EmptyStateType {
  habits,
  diary,
  bounties,
  northStar,
  categories,
  users,
  search,
  network,
  generic,
}

/// Information about an empty state for display purposes
class EmptyStateInfo {
  final String title;
  final String message;
  final IconData icon;
  final Color color;
  final String actionLabel;

  const EmptyStateInfo({
    required this.title,
    required this.message,
    required this.icon,
    required this.color,
    required this.actionLabel,
  });
}
