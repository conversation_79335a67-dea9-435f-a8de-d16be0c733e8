// lib/widgets/auth_username_gender_modal.dart

import 'package:flutter/material.dart';
import '../theme/colors.dart';
import 'non_gender_dialog.dart';

/// Combined modal for username entry and gender selection during signup
class AuthUsernameGenderModal extends StatefulWidget {
  final Function(String username, String gender) onContinue;
  final VoidCallback? onBack;

  const AuthUsernameGenderModal({
    super.key,
    required this.onContinue,
    this.onBack,
  });

  @override
  State<AuthUsernameGenderModal> createState() => _AuthUsernameGenderModalState();
}

class _AuthUsernameGenderModalState extends State<AuthUsernameGenderModal> {
  final TextEditingController _usernameController = TextEditingController();
  String? _selectedGender;
  String? _usernameError;
  bool _showNonGenderOption = false;
  bool _isValidating = false;

  // Reserved usernames (case insensitive)
  static const List<String> _reservedUsernames = [
    'admin', 'test', 'user', 'mxd', 'support',
    'ADMIN', 'TEST', 'USER', 'MXD', 'SUPPORT',
  ];

  @override
  void dispose() {
    _usernameController.dispose();
    super.dispose();
  }

  bool get _canContinue {
    final canContinue = _usernameError == null &&
           _usernameController.text.isNotEmpty &&
           _selectedGender != null &&
           !_isValidating;
    print('🔄 AuthUsernameGenderModal: _canContinue = $canContinue (username: "${_usernameController.text}", gender: $_selectedGender, error: $_usernameError, validating: $_isValidating)');
    return canContinue;
  }

  void _validateUsername(String username) async {
    if (username.isEmpty) {
      setState(() {
        _usernameError = null;
        _isValidating = false;
      });
      return;
    }

    setState(() {
      _isValidating = true;
      _usernameError = null;
    });

    // Add a small delay to prevent excessive validation calls
    await Future.delayed(const Duration(milliseconds: 300));

    // Check if the username is still the same (user hasn't changed it)
    if (_usernameController.text == username) {
      setState(() {
        _usernameError = _getUsernameError(username);
        _isValidating = false;
      });
    }
  }

  String? _getUsernameError(String username) {
    if (username.isEmpty) return null;
    
    if (username.length > 15) {
      return 'Username must be 15 characters or less';
    }
    
    // Check if starts with special character or number
    if (username.isNotEmpty && RegExp(r'^[^a-zA-Z]').hasMatch(username)) {
      return 'Username cannot start with special characters or numbers';
    }
    
    // Check for invalid characters
    if (!RegExp(r'^[a-zA-Z0-9_\-!$*]*$').hasMatch(username)) {
      return 'Username can only contain letters, numbers, hyphens, underscores, !, \$, *';
    }
    
    // Check reserved usernames
    if (_reservedUsernames.contains(username.toLowerCase()) || 
        _reservedUsernames.any((reserved) => username.toLowerCase().contains(reserved.toLowerCase()))) {
      return 'This username is not available';
    }
    
    return null;
  }

  void _selectGender(String gender) {
    setState(() {
      _selectedGender = gender;
      _showNonGenderOption = false;
    });
  }

  void _showNonGenderDialog() {
    showDialog(
      context: context,
      builder: (context) => NonGenderDialog(
        onConfirm: () {
          Navigator.of(context).pop();
          _selectGender('Non-Gender');
        },
      ),
    );
  }

  void _attemptContinue() {
    if (_selectedGender == null) {
      setState(() {
        _showNonGenderOption = true;
      });
      return;
    }
    
    if (_canContinue) {
      widget.onContinue(_usernameController.text, _selectedGender!);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.black.withValues(alpha: 0.95),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.85,
        constraints: const BoxConstraints(maxHeight: 500), // Add height constraint
        padding: const EdgeInsets.all(32),
        child: Stack(
          children: [
            // Close button (X) - positioned absolutely in top-left with proper spacing
            if (widget.onBack != null)
              Positioned(
                top: 8,
                left: 8,
                child: GestureDetector(
                  onTap: () {
                    print('❌ Sign Up close button pressed');
                    widget.onBack?.call();
                  },
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.black.withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: const Icon(
                      Icons.close,
                      color: Colors.white70,
                      size: 18,
                    ),
                  ),
                ),
              ),

            // Main content - scrollable with top padding to avoid close button
            Padding(
              padding: const EdgeInsets.only(top: 40), // Space for close button
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Title - moved to top with minimal spacing
                    const Text(
                    'Create Your Account',
                    style: TextStyle(
                      color: Colors.cyanAccent,
                      fontFamily: 'Pirulen',
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
            const SizedBox(height: 8),
            const Text(
              'Choose your username and gender',
              style: TextStyle(
                color: Colors.white70,
                fontFamily: 'Bitsumishi',
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            
            // Username field
            TextField(
              controller: _usernameController,
              onChanged: _validateUsername,
              style: const TextStyle(
                color: Colors.white,
                fontFamily: 'Bitsumishi',
                fontSize: 16,
              ),
              decoration: InputDecoration(
                labelText: 'Username',
                labelStyle: const TextStyle(
                  color: Colors.cyanAccent,
                  fontFamily: 'Pirulen',
                  fontSize: 14,
                ),
                hintText: 'Enter your username',
                hintStyle: TextStyle(
                  color: Colors.grey[400],
                  fontFamily: 'Bitsumishi',
                ),
                filled: true,
                fillColor: Colors.grey[900],
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Colors.grey[700]!),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Colors.grey[700]!),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(color: Colors.cyanAccent),
                ),
                errorBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(color: Colors.red),
                ),
                focusedErrorBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(color: Colors.red),
                ),
              ),
            ),
            
            // Username error
            if (_usernameError != null) ...[
              const SizedBox(height: 8),
              Align(
                alignment: Alignment.centerLeft,
                child: Text(
                  _usernameError!,
                  style: const TextStyle(
                    color: Colors.red,
                    fontFamily: 'Bitsumishi',
                    fontSize: 12,
                  ),
                ),
              ),
            ],
            
            const SizedBox(height: 32),
            
            // Gender selection
            const Text(
              'Select Your Gender',
              style: TextStyle(
                color: Colors.cyanAccent,
                fontFamily: 'Pirulen',
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'This helps us personalize your coach experience',
              style: TextStyle(
                color: Colors.white70,
                fontFamily: 'Bitsumishi',
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            
            // Gender buttons
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => _selectGender('Male'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: _selectedGender == 'Male' ? MolColors.blue : Colors.grey[800],
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: const Text(
                      'Male',
                      style: TextStyle(
                        fontFamily: 'Pirulen',
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => _selectGender('Female'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: _selectedGender == 'Female' ? MolColors.purple : Colors.grey[800],
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: const Text(
                      'Female',
                      style: TextStyle(
                        fontFamily: 'Pirulen',
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ],
            ),
            
            // Non-gender option (shown when user tries to continue without selection)
            if (_showNonGenderOption) ...[
              const SizedBox(height: 16),
              TextButton(
                onPressed: _showNonGenderDialog,
                child: const Text(
                  "If you'd prefer not to choose gender",
                  style: TextStyle(
                    color: Colors.cyanAccent,
                    fontFamily: 'Bitsumishi',
                    fontSize: 14,
                    decoration: TextDecoration.underline,
                  ),
                ),
              ),
            ],
            
            const SizedBox(height: 32),
            
            // Continue button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _canContinue ? _attemptContinue : _attemptContinue,
                style: ElevatedButton.styleFrom(
                  backgroundColor: _canContinue ? Colors.cyanAccent : Colors.grey[700],
                  foregroundColor: Colors.black,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: _isValidating
                    ? Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                _canContinue ? Colors.black : Colors.white54,
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          const Text(
                            'Validating...',
                            style: TextStyle(
                              fontFamily: 'Pirulen',
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      )
                    : const Text(
                        'Continue',
                        style: TextStyle(
                          fontFamily: 'Pirulen',
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
              ),
            ),
                ],
              ),
            ),
            ),
          ],
        ),
      ),
    );
  }
}
