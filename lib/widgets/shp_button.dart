import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import '../theme/colors.dart';

class SHPButton extends StatelessWidget {
  const SHPButton({super.key});

  void _launchGuardianTeamPack() async {
    const url = 'https://www.guardian-tape.com/products/guardian-team-pack';
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
    }
  }

  @override
  Widget build(BuildContext context) {
    return ElevatedButton.icon(
      icon: const Icon(Icons.bolt, color: Color.fromRGBO(217, 255, 0, 1)),
      label: const Text(
        'Supercharge Your Game',
        style: TextStyle(fontFamily: 'Bitsumishi', fontSize: 22),
      ),
      onPressed: _launchGuardianTeamPack,
      style: ElevatedButton.styleFrom(
        backgroundColor: MolColors.blue,
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 14),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.0)),
      ),
    );
  }
}
