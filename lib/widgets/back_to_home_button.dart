// 📁 lib/widgets/back_to_home_button.dart

import 'package:flutter/material.dart';

class BackToHomeButton extends StatelessWidget {
  const BackToHomeButton({super.key});

  @override
  Widget build(BuildContext context) {
    return ElevatedButton.icon(
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.blueAccent,
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
      ),
      onPressed: () {
        Navigator.of(context).popUntil((route) => route.isFirst);
      },
      icon: const Icon(Icons.home, color: Colors.white),
      label: const Text(
        'To Home Screen',
        style: TextStyle(
          fontFamily: '<PERSON><PERSON><PERSON>',
          fontSize: 16,
          color: Colors.white,
        ),
      ),
    );
  }
}
