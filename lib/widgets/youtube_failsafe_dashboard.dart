import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import '../services/youtube_transcript_service.dart';

/// 🛡️ BULLETPROOF YouTube Transcript Downloader Failsafe Dashboard
/// 
/// This dashboard provides real-time monitoring and debugging capabilities
/// for the YouTube transcript downloader service, ensuring administrators
/// can instantly diagnose and resolve any issues.
class YouTubeFailsafeDashboard extends StatefulWidget {
  const YouTubeFailsafeDashboard({super.key});

  @override
  State<YouTubeFailsafeDashboard> createState() => _YouTubeFailsafeDashboardState();
}

class _YouTubeFailsafeDashboardState extends State<YouTubeFailsafeDashboard> {
  ServiceHealthStatus? _healthStatus;
  Map<String, dynamic>? _quotaUsage;
  List<String> _debugLog = [];
  bool _isLoading = true;
  bool _autoRefresh = true;

  @override
  void initState() {
    super.initState();
    _loadDashboardData();
    if (_autoRefresh) {
      _startAutoRefresh();
    }
  }

  void _startAutoRefresh() {
    Future.delayed(const Duration(seconds: 30), () {
      if (mounted && _autoRefresh) {
        _loadDashboardData();
        _startAutoRefresh();
      }
    });
  }

  Future<void> _loadDashboardData() async {
    try {
      final healthStatus = await YouTubeTranscriptService.checkServiceHealth();
      final quotaUsage = YouTubeTranscriptService.getQuotaUsage();
      final debugLog = YouTubeTranscriptService.getDebugLog();

      if (mounted) {
        setState(() {
          _healthStatus = healthStatus;
          _quotaUsage = quotaUsage;
          _debugLog = debugLog.reversed.take(50).toList(); // Latest 50 entries
          _isLoading = false;
        });
      }
    } catch (e) {
      if (kDebugMode) print('❌ Failed to load dashboard data: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Widget _outlinedText(
    String text, {
    required double fontSize,
    required Color fillColor,
    Color outlineColor = Colors.black,
    double outlineWidth = 1.5,
    String fontFamily = 'Bitsumishi',
  }) {
    return Stack(
      alignment: Alignment.center,
      children: [
        Text(
          text,
          style: TextStyle(
            fontFamily: fontFamily,
            fontSize: fontSize,
            foreground: Paint()
              ..style = PaintingStyle.stroke
              ..strokeWidth = outlineWidth
              ..color = outlineColor,
          ),
        ),
        Text(
          text,
          style: TextStyle(
            fontFamily: fontFamily,
            fontSize: fontSize,
            color: fillColor,
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    final titleFontSize = (width / 1080 * 28).clamp(24.0, 36.0);
    final bodyFontSize = (width / 1080 * 16).clamp(14.0, 20.0);

    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        title: _outlinedText(
          '🛡️ YouTube Failsafe Dashboard',
          fontSize: titleFontSize,
          fillColor: Colors.cyan,
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          IconButton(
            icon: Icon(
              _autoRefresh ? Icons.pause : Icons.play_arrow,
              color: _autoRefresh ? Colors.green : Colors.grey,
            ),
            onPressed: () {
              setState(() {
                _autoRefresh = !_autoRefresh;
              });
              if (_autoRefresh) _startAutoRefresh();
            },
          ),
          IconButton(
            icon: const Icon(Icons.refresh, color: Colors.white),
            onPressed: _loadDashboardData,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(color: Colors.cyan),
            )
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // System Health Status
                  _buildHealthStatusCard(bodyFontSize),
                  const SizedBox(height: 20),
                  
                  // Quota Usage
                  _buildQuotaUsageCard(bodyFontSize),
                  const SizedBox(height: 20),
                  
                  // Quick Actions
                  _buildQuickActionsCard(bodyFontSize),
                  const SizedBox(height: 20),
                  
                  // Debug Log
                  _buildDebugLogCard(bodyFontSize),
                ],
              ),
            ),
    );
  }

  Widget _buildHealthStatusCard(double fontSize) {
    final isHealthy = _healthStatus?.isHealthy ?? false;
    final statusColor = isHealthy ? Colors.green : Colors.red;
    final statusText = isHealthy ? 'HEALTHY' : 'DEGRADED';

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: statusColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: statusColor, width: 2),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                isHealthy ? Icons.check_circle : Icons.error,
                color: statusColor,
                size: 24,
              ),
              const SizedBox(width: 8),
              _outlinedText(
                'System Health: $statusText',
                fontSize: fontSize + 2,
                fillColor: statusColor,
              ),
            ],
          ),
          
          if (_healthStatus?.checks.isNotEmpty == true) ...[
            const SizedBox(height: 12),
            ..._healthStatus!.checks.entries.map((entry) {
              final checkColor = entry.value ? Colors.green : Colors.red;
              final checkIcon = entry.value ? Icons.check : Icons.close;
              
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 2),
                child: Row(
                  children: [
                    Icon(checkIcon, color: checkColor, size: 16),
                    const SizedBox(width: 8),
                    Text(
                      entry.key.replaceAll('_', ' ').toUpperCase(),
                      style: TextStyle(
                        color: checkColor,
                        fontSize: fontSize - 2,
                        fontFamily: 'Digital-7',
                      ),
                    ),
                  ],
                ),
              );
            }),
          ],
          
          if (_healthStatus?.issues.isNotEmpty == true) ...[
            const SizedBox(height: 12),
            _outlinedText(
              'Issues:',
              fontSize: fontSize,
              fillColor: Colors.red,
            ),
            const SizedBox(height: 4),
            ..._healthStatus!.issues.map((issue) => Padding(
              padding: const EdgeInsets.symmetric(vertical: 1),
              child: Text(
                '• $issue',
                style: TextStyle(
                  color: Colors.red[300],
                  fontSize: fontSize - 2,
                  fontFamily: 'Digital-7',
                ),
              ),
            )),
          ],
        ],
      ),
    );
  }

  Widget _buildQuotaUsageCard(double fontSize) {
    if (_quotaUsage == null) return const SizedBox.shrink();

    final used = _quotaUsage!['used'] as int;
    final limit = _quotaUsage!['limit'] as int;
    final percentage = double.parse(_quotaUsage!['percentage'] as String);
    
    Color quotaColor;
    if (percentage < 50) {
      quotaColor = Colors.green;
    } else if (percentage < 80) {
      quotaColor = Colors.orange;
    } else {
      quotaColor = Colors.red;
    }

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: quotaColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: quotaColor, width: 2),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _outlinedText(
            'API Quota Usage',
            fontSize: fontSize + 2,
            fillColor: quotaColor,
          ),
          const SizedBox(height: 12),
          
          LinearProgressIndicator(
            value: percentage / 100,
            backgroundColor: Colors.grey[800],
            valueColor: AlwaysStoppedAnimation<Color>(quotaColor),
          ),
          const SizedBox(height: 8),
          
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '$used / $limit',
                style: TextStyle(
                  color: quotaColor,
                  fontSize: fontSize,
                  fontFamily: 'Digital-7',
                ),
              ),
              Text(
                '${percentage.toStringAsFixed(1)}%',
                style: TextStyle(
                  color: quotaColor,
                  fontSize: fontSize,
                  fontFamily: 'Digital-7',
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActionsCard(double fontSize) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue, width: 2),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _outlinedText(
            'Quick Actions',
            fontSize: fontSize + 2,
            fillColor: Colors.blue,
          ),
          const SizedBox(height: 12),
          
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () async {
                    YouTubeTranscriptService.clearDebugLog();
                    await _loadDashboardData();
                  },
                  icon: const Icon(Icons.clear_all, color: Colors.white),
                  label: Text(
                    'Clear Log',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: fontSize - 2,
                      fontFamily: 'Digital-7',
                    ),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange,
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () async {
                    try {
                      final fileName = await YouTubeTranscriptService.exportDebugLog();
                      if (mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('Debug log exported: $fileName'),
                            backgroundColor: Colors.green,
                          ),
                        );
                      }
                    } catch (e) {
                      if (mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('Export failed: $e'),
                            backgroundColor: Colors.red,
                          ),
                        );
                      }
                    }
                  },
                  icon: const Icon(Icons.download, color: Colors.white),
                  label: Text(
                    'Export Log',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: fontSize - 2,
                      fontFamily: 'Digital-7',
                    ),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDebugLogCard(double fontSize) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey, width: 2),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _outlinedText(
                'Debug Log (Latest 50)',
                fontSize: fontSize + 2,
                fillColor: Colors.grey[300]!,
              ),
              Text(
                '${_debugLog.length} entries',
                style: TextStyle(
                  color: Colors.grey[500],
                  fontSize: fontSize - 2,
                  fontFamily: 'Digital-7',
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          
          Container(
            height: 300,
            width: double.infinity,
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.black,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey[700]!),
            ),
            child: _debugLog.isEmpty
                ? Center(
                    child: Text(
                      'No debug entries yet',
                      style: TextStyle(
                        color: Colors.grey[500],
                        fontSize: fontSize,
                        fontFamily: 'Digital-7',
                      ),
                    ),
                  )
                : ListView.builder(
                    itemCount: _debugLog.length,
                    itemBuilder: (context, index) {
                      final entry = _debugLog[index];
                      Color entryColor = Colors.white;
                      
                      if (entry.contains('ERROR')) {
                        entryColor = Colors.red[300]!;
                      } else if (entry.contains('WARNING')) {
                        entryColor = Colors.orange[300]!;
                      } else if (entry.contains('DEBUG')) {
                        entryColor = Colors.blue[300]!;
                      } else if (entry.contains('INFO')) {
                        entryColor = Colors.green[300]!;
                      }
                      
                      return Padding(
                        padding: const EdgeInsets.symmetric(vertical: 1),
                        child: Text(
                          entry,
                          style: TextStyle(
                            color: entryColor,
                            fontSize: fontSize - 4,
                            fontFamily: 'Courier',
                          ),
                        ),
                      );
                    },
                  ),
          ),
        ],
      ),
    );
  }
}
