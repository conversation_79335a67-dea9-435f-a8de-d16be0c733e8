import 'package:flutter/material.dart';

class CancelButton extends StatelessWidget {
  final VoidCallback onCancel;

  const CancelButton({super.key, required this.onCancel});

  @override
  Widget build(BuildContext context) {
    return Positioned(
      bottom: 40,
      left: 0,
      right: 0,
      child: Center(
        child: ElevatedButton(
          onPressed: onCancel,
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.red,
            padding: const EdgeInsets.symmetric(horizontal: 40, vertical: 16),
          ),
          child: const Text(
            'CANCEL',
            style: TextStyle(
              fontFamily: 'Bitsumishi',
              fontSize: 20,
              color: Colors.white,
            ),
          ),
        ),
      ),
    );
  }
}
