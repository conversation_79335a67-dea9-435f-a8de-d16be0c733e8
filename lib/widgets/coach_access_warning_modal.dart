// lib/widgets/coach_access_warning_modal.dart

import 'package:flutter/material.dart';
import '../services/comprehensive_logging_service.dart';

/// Modal that informs users about account benefits for AI coaches
/// Shows when user.isEmailVerified == false and they try to access coaches
class CoachAccessWarningModal extends StatelessWidget {
  final VoidCallback onContinue;
  final VoidCallback? onBack;

  const CoachAccessWarningModal({
    super.key,
    required this.onContinue,
    this.onBack,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: const Color(0xFF1A1A1A),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: Colors.orange.withValues(alpha: 0.5),
            width: 2,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.orange.withValues(alpha: 0.3),
              blurRadius: 20,
              spreadRadius: 2,
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header with info icon
            Row(
              children: [
                Icon(
                  Icons.info_outline_rounded,
                  color: Colors.cyanAccent,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'AI Coach Access',
                    style: TextStyle(
                      color: Colors.cyanAccent,
                      fontFamily: 'Pirulen',
                      fontSize: 15,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 2,
                  ),
                ),
                const SizedBox(width: 12),
                if (onBack != null)
                  GestureDetector(
                    onTap: () {
                      _logModalAction('back_pressed');
                      Navigator.of(context).pop();
                      onBack?.call();
                    },
                    child: Container(
                      width: 24,
                      height: 24,
                      decoration: BoxDecoration(
                        color: Colors.grey[800],
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Icon(
                        Icons.close,
                        color: Colors.white70,
                        size: 16,
                      ),
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 24),
            
            // Info message
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.cyanAccent.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Colors.cyanAccent.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: Column(
                children: [
                  const Icon(
                    Icons.psychology_alt,
                    color: Colors.cyanAccent,
                    size: 48,
                  ),
                  const SizedBox(height: 16),

                  const Text(
                    'You can access MXD Coaches now!',
                    style: TextStyle(
                      color: Colors.white,
                      fontFamily: 'Bitsumishi',
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),

                  const Text(
                    'For the best experience, consider verifying your email to save your progress and get personalized responses.',
                    style: TextStyle(
                      color: Colors.white70,
                      fontFamily: 'Bitsumishi',
                      fontSize: 14,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 12),

                  const Text(
                    'You can always verify later to unlock full personalization.',
                    style: TextStyle(
                      color: Colors.cyanAccent,
                      fontFamily: 'Bitsumishi',
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),
            
            // Action buttons
            Row(
              children: [
                // Continue to coaches button (prominent)
                Expanded(
                  flex: 3,
                  child: ElevatedButton(
                    onPressed: () {
                      _logModalAction('continue_to_coaches');
                      Navigator.of(context).pop();
                      onContinue();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.cyanAccent,
                      foregroundColor: Colors.black,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: const Text(
                      'Access Coaches',
                      style: TextStyle(
                        fontFamily: 'Pirulen',
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),

                // Verify email button (secondary)
                Expanded(
                  flex: 2,
                  child: TextButton(
                    onPressed: () {
                      _logModalAction('verify_email_requested');
                      Navigator.of(context).pop();
                      // 🛡️ PHASE 3: Show email verification instructions
                      _showEmailVerificationInstructions(context);
                    },
                    style: TextButton.styleFrom(
                      foregroundColor: Colors.white70,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                        side: BorderSide(color: Colors.white30, width: 1),
                      ),
                    ),
                    child: const Text(
                      'Verify Later',
                      style: TextStyle(
                        fontFamily: 'Bitsumishi',
                        fontSize: 12,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Log modal actions for debugging
  void _logModalAction(String action) {
    // 🛡️ PHASE 3: Remove async to prevent context issues
    ComprehensiveLoggingService.logInfo('🚨 CoachAccessWarning: $action');
  }

  /// Show email verification instructions
  void _showEmailVerificationInstructions(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF1A1A1A),
        title: const Text(
          'Email Verification',
          style: TextStyle(
            color: Colors.orange,
            fontFamily: 'Pirulen',
          ),
        ),
        content: const Text(
          'Please check your email inbox for a verification link. Click the link to verify your email and unlock full coach access.',
          style: TextStyle(
            color: Colors.white,
            fontFamily: 'Bitsumishi',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text(
              'Got it',
              style: TextStyle(
                color: Colors.orange,
                fontFamily: 'Pirulen',
              ),
            ),
          ),
        ],
      ),
    );
  }
}
