// ✅ DAILY HABIT MANAGER POPUP - Updated for Exp + Emoji Reminder ✅

import 'package:flutter/material.dart';

class DailyHabitManagerPopup extends StatefulWidget {
  final List<String> habits;
  final Function(List<String>) onUpdate;

  const DailyHabitManagerPopup({
    super.key,
    required this.habits,
    required this.onUpdate,
  });

  @override
  State<DailyHabitManagerPopup> createState() => _DailyHabitManagerPopupState();
}

class _DailyHabitManagerPopupState extends State<DailyHabitManagerPopup> {
  late List<String> _habits;
  final TextEditingController _controller = TextEditingController();

  @override
  void initState() {
    super.initState();
    _habits = List.from(widget.habits);
  }

  void _addHabit() {
    final newHabit = _controller.text.trim();
    if (newHabit.isNotEmpty && !_habits.contains(newHabit) && _habits.length < 10) {
      setState(() {
        _habits.add(newHabit);
        _controller.clear();
      });
    }
  }

  void _removeHabit(String habit) {
    setState(() {
      _habits.remove(habit);
    });
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: Colors.black,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      title: const Text(
        'Daily Habit Manager',
        style: TextStyle(
          color: Colors.white,
          fontFamily: 'Bitsumishi',
          fontSize: 22,
        ),
      ),
      content: SizedBox(
        width: 400,
        height: 480,
        child: Column(
          children: [
            const Padding(
              padding: EdgeInsets.only(bottom: 6.0),
              child: Text(
                'Add an emoji to each habit for visual glow ✨',
                style: TextStyle(color: Colors.amber, fontSize: 13, fontFamily: 'Bitsumishi'),
              ),
            ),
            TextField(
              controller: _controller,
              style: const TextStyle(color: Colors.white),
              decoration: InputDecoration(
                hintText: 'e.g. ✍️ Journal or 💧 Hydrate',
                hintStyle: const TextStyle(color: Colors.white70),
                filled: true,
                fillColor: const Color(0xFF34A6E2),
                contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
                border: OutlineInputBorder(borderRadius: BorderRadius.circular(10)),
              ),
            ),
            const SizedBox(height: 10),
            ElevatedButton(
              onPressed: _addHabit,
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF9DCB3C),
                padding: const EdgeInsets.symmetric(horizontal: 30, vertical: 10),
              ),
              child: const Text(
                'Add Habit',
                style: TextStyle(fontFamily: 'Bitsumishi'),
              ),
            ),
            const SizedBox(height: 20),
            Expanded(
              child: ListView.builder(
                itemCount: _habits.length,
                itemBuilder: (context, index) {
                  final habit = _habits[index];
                  return Card(
                    color: Colors.white10,
                    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                    child: ListTile(
                      title: Text(
                        habit,
                        style: const TextStyle(color: Colors.white, fontFamily: 'Bitsumishi'),
                      ),
                      trailing: SizedBox(
                        width: 44,
                        height: 44,
                        child: IconButton(
                          icon: const Icon(Icons.delete, color: Color(0xFFFF3D3D)),
                          onPressed: () => _removeHabit(habit),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
      actions: [
        Center(
          child: TextButton(
            onPressed: () {
              widget.onUpdate(_habits);
              Navigator.pop(context);
            },
            child: const Text(
              'SAVE',
              style: TextStyle(color: Colors.amber, fontFamily: 'Bitsumishi'),
            ),
          ),
        ),
      ],
    );
  }
}
