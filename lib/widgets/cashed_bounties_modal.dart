import 'dart:io';
import 'package:flutter/material.dart';
import '../models/bounty_model.dart';
import '../utils/debug_logger.dart';

class CashedBounty {
  final BountyModel bounty;
  final String photoPath;
  final DateTime completedAt;
  CashedBounty({required this.bounty, required this.photoPath, required this.completedAt});
}

class CashedBountiesModal extends StatefulWidget {
  final List<CashedBounty> bounties;
  final Color glowColor;
  final VoidCallback onClose;

  const CashedBountiesModal({
    super.key,
    required this.bounties,
    required this.glowColor,
    required this.onClose,
  });

  @override
  State<CashedBountiesModal> createState() => _CashedBountiesModalState();
}

class _CashedBountiesModalState extends State<CashedBountiesModal> {
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  List<CashedBounty> _filteredBounties = [];
  int _currentPage = 0;
  static const int _itemsPerPage = 20;
  bool _isLoadingMore = false;

  @override
  void initState() {
    super.initState();
    _filteredBounties = List.from(widget.bounties);
    _sortBountiesByDate();
    _setupScrollListener();
    DebugLogger.log('CashedBountiesModal', 'Initialized with ${widget.bounties.length} bounties');
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _setupScrollListener() {
    _scrollController.addListener(() {
      if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent - 200) {
        _loadMoreBounties();
      }
    });
  }

  void _sortBountiesByDate() {
    _filteredBounties.sort((a, b) => b.completedAt.compareTo(a.completedAt));
  }

  void _loadMoreBounties() {
    if (_isLoadingMore) return;

    final int totalPages = (_filteredBounties.length / _itemsPerPage).ceil();
    if (_currentPage >= totalPages - 1) return;

    setState(() {
      _isLoadingMore = true;
      _currentPage++;
    });

    // Simulate loading delay
    Future.delayed(Duration(milliseconds: 500), () {
      setState(() {
        _isLoadingMore = false;
      });
    });

    DebugLogger.log('CashedBountiesModal', 'Loaded page $_currentPage');
  }

  // Note: Filtering functionality removed for simplicity
  // Future enhancement: Add comprehensive filtering when needed

  // Note: Filter methods removed as they were unused
  // Future enhancement: Add filtering capabilities when needed

  Widget outlinedText(String text, {double fontSize = 14, FontWeight? fontWeight}) {
    return Stack(
      children: [
        Text(text,
            style: TextStyle(
              fontFamily: 'Pirulen',
              fontSize: fontSize,
              fontWeight: fontWeight,
              foreground: Paint()
                ..style = PaintingStyle.stroke
                ..strokeWidth = 3
                ..color = Colors.black,
            )),
        Text(text,
            style: TextStyle(
              fontFamily: 'Pirulen',
              fontSize: fontSize,
              fontWeight: fontWeight,
              color: Colors.white,
            )),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.black,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                outlinedText('CASHED BOUNTIES', fontSize: 20, fontWeight: FontWeight.bold),
                SizedBox(
                  width: 44,
                  height: 44,
                  child: IconButton(
                    icon: const Icon(Icons.close, color: Colors.white),
                    onPressed: widget.onClose,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 10),
            widget.bounties.isEmpty
                ? outlinedText('No bounties yet!', fontSize: 16)
                : SizedBox(
                    height: 400,
                    width: 340,
                    child: ListView.builder(
                      reverse: true,
                      itemCount: widget.bounties.length,
                      itemBuilder: (context, i) {
                        final bounty = widget.bounties[i];
                        final file = File(bounty.photoPath);
                        final photoExists = file.existsSync();
                        return Container(
                          margin: const EdgeInsets.symmetric(vertical: 10),
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.black.withValues(alpha: 0.85),
                            borderRadius: BorderRadius.circular(14),
                            border: Border.all(color: widget.glowColor, width: 1.5),
                            boxShadow: [BoxShadow(color: widget.glowColor, blurRadius: 12, spreadRadius: 2)],
                          ),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              if (photoExists)
                                ClipRRect(
                                  borderRadius: BorderRadius.circular(8),
                                  child: Image.file(
                                    file,
                                    height: 60,
                                    width: 60,
                                    fit: BoxFit.cover,
                                  ),
                                )
                              else
                                Container(
                                  height: 60,
                                  width: 60,
                                  decoration: BoxDecoration(
                                    color: Colors.grey[900],
                                    borderRadius: BorderRadius.circular(8),
                                    border: Border.all(color: widget.glowColor, width: 1),
                                  ),
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Icon(Icons.broken_image, color: widget.glowColor, size: 28),
                                      const SizedBox(height: 4),
                                      outlinedText('Photo not found', fontSize: 8),
                                    ],
                                  ),
                                ),
                              const SizedBox(width: 14),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    outlinedText(bounty.bounty.description, fontSize: 14),
                                    const SizedBox(height: 4),
                                    Row(
                                      children: bounty.bounty.categories.map((cat) => Padding(
                                        padding: const EdgeInsets.only(right: 6),
                                        child: outlinedText(
                                          '[${bounty.bounty.expPerCategory[cat] ?? 0} EXP - $cat]',
                                          fontSize: 12,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      )).toList(),
                                    ),
                                    const SizedBox(height: 4),
                                    outlinedText(
                                      'Completed: '
                                      '${bounty.completedAt.year}-${bounty.completedAt.month.toString().padLeft(2, '0')}-${bounty.completedAt.day.toString().padLeft(2, '0')} '
                                      '${bounty.completedAt.hour.toString().padLeft(2, '0')}:${bounty.completedAt.minute.toString().padLeft(2, '0')}',
                                      fontSize: 10,
                                    ),
                                    if (bounty.bounty.isEpic)
                                      Padding(
                                        padding: const EdgeInsets.only(top: 4),
                                        child: Row(
                                          children: [
                                            Icon(Icons.flash_on, color: Colors.blueAccent, size: 18, shadows: [
                                              BoxShadow(color: widget.glowColor, blurRadius: 8, spreadRadius: 1),
                                            ]),
                                            Icon(Icons.flash_on, color: Colors.purpleAccent, size: 18, shadows: [
                                              BoxShadow(color: widget.glowColor, blurRadius: 8, spreadRadius: 1),
                                            ]),
                                            outlinedText('EPIC!', fontSize: 12, fontWeight: FontWeight.bold),
                                          ],
                                        ),
                                      ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                  ),
          ],
        ),
      ),
    );
  }
} 