// 📁 lib/widgets/daily_habits_intro_modal.dart

import 'package:flutter/material.dart';

class DailyHabitsIntroModal extends StatelessWidget {
  final VoidCallback onContinue;

  const DailyHabitsIntroModal({
    super.key,
    required this.onContinue,
  });

  void _showExpandedImage(BuildContext context, String imagePath) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: Container(
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.8,
            maxWidth: MediaQuery.of(context).size.width * 0.9,
          ),
          decoration: BoxDecoration(
            color: Colors.black,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.cyanAccent, width: 2),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header with close button
              Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'Progress Chart',
                      style: TextStyle(
                        color: Colors.cyanAccent,
                        fontSize: 18,
                        fontFamily: 'Pirulen',
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    IconButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: const Icon(Icons.close, color: Colors.white),
                    ),
                  ],
                ),
              ),
              // Expanded image
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Image.asset(
                    imagePath,
                    fit: BoxFit.contain,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        color: Colors.grey[800],
                        child: const Center(
                          child: Text(
                            'Image not found',
                            style: TextStyle(color: Colors.white),
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.85,
          maxWidth: MediaQuery.of(context).size.width * 0.9,
        ),
        decoration: BoxDecoration(
          color: Colors.black,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: Colors.cyanAccent, width: 2),
          boxShadow: [
            BoxShadow(
              color: Colors.cyanAccent.withValues(alpha: 0.3),
              blurRadius: 20,
              spreadRadius: 2,
            ),
          ],
        ),
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header - North Star Quest style
              const Text(
                'Daily Habits',
                style: TextStyle(
                  color: Colors.white,
                  fontFamily: 'Pirulen',
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              const Text(
                'The Foundation of Your Transformation',
                style: TextStyle(
                  color: Colors.white70,
                  fontFamily: 'Bitsumishi',
                  fontSize: 16,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),

              // Main content - North Star Quest style
              const Text(
                'At its core, success is the product of compounding discipline:',
                style: TextStyle(
                  color: Colors.white,
                  fontFamily: 'Bitsumishi',
                  fontSize: 16,
                  height: 1.5,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              
              // Math section - North Star Quest style
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.cyanAccent.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.cyanAccent.withValues(alpha: 0.3)),
                ),
                child: const Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '💡 The Math of Transformation:',
                      style: TextStyle(
                        color: Colors.cyanAccent,
                        fontFamily: 'Pirulen',
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      '• Improve by 1% daily = 37.78x growth in a year (≈3678% gain)\n• Strive for 1.05% daily = 45.26x growth (≈4426% increase)\n• Push to 1.20% daily = 77.78x explosion (≈7678% transformation)',
                      style: TextStyle(
                        color: Colors.white70,
                        fontFamily: 'Bitsumishi',
                        fontSize: 14,
                        height: 1.4,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              
              const Text(
                "This math isn't magic, it's the relentless power of tiny wins.",
                style: TextStyle(
                  color: Colors.white,
                  fontFamily: 'Bitsumishi',
                  fontSize: 16,
                  height: 1.5,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              
              // Progress image 1 - Clickable
              GestureDetector(
                onTap: () => _showExpandedImage(context, 'assets/images/progress.png'),
                child: Container(
                  height: 120,
                  width: double.infinity,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.grey[600]!),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Image.asset(
                      'assets/images/progress.png',
                      fit: BoxFit.contain,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          color: Colors.grey[800],
                          child: const Center(
                            child: Text(
                              'Progress Chart 1\n(Tap to expand)',
                              style: TextStyle(color: Colors.grey, fontSize: 14),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 16),
              
              const Column(
                children: [
                  Text(
                    'Every time you improve just a fraction more, you harness the incredible power of compounding.',
                    style: TextStyle(
                      color: Colors.white70,
                      fontFamily: 'Bitsumishi',
                      fontSize: 16,
                      height: 1.5,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 16),
                  Text(
                    '1.20% daily gains turn into nearly 78× growth after a year, and over 8,800× after two, while 1.00% only reaches about 1,962×.',
                    style: TextStyle(
                      color: Colors.white70,
                      fontFamily: 'Bitsumishi',
                      fontSize: 16,
                      height: 1.5,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 16),
                  Text(
                    'That small 0.20% advantage might sound trivial, but over time it snowballs into a huge gap between somebody average, or exceptional.',
                    style: TextStyle(
                      color: Colors.white70,
                      fontFamily: 'Bitsumishi',
                      fontSize: 16,
                      height: 1.5,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 16),
                  Text(
                    'Consistent daily improvements are the true secret behind exponential success.',
                    style: TextStyle(
                      color: Colors.white70,
                      fontFamily: 'Bitsumishi',
                      fontSize: 16,
                      height: 1.5,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
              const SizedBox(height: 16),
              
              // Progress image 2 - Clickable
              GestureDetector(
                onTap: () => _showExpandedImage(context, 'assets/images/progress2.png'),
                child: Container(
                  height: 120,
                  width: double.infinity,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.grey[600]!),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Image.asset(
                      'assets/images/progress2.png',
                      fit: BoxFit.contain,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          color: Colors.grey[800],
                          child: const Center(
                            child: Text(
                              'Progress Chart 2\n(Tap to expand)',
                              style: TextStyle(color: Colors.grey, fontSize: 14),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 16),
              
              const Column(
                children: [
                  Text(
                    'Make these daily gains habitual, and watch as your discipline builds your achievement.',
                    style: TextStyle(
                      color: Colors.white70,
                      fontFamily: 'Bitsumishi',
                      fontSize: 16,
                      height: 1.5,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 16),
                  Text(
                    'Comparatively, over 762 days, crawling forwards at 0.5% daily nets a meager 44.7× boost, while a full-throttle 1.2% push rockets you to an astonishing 8,862×.',
                    style: TextStyle(
                      color: Colors.white70,
                      fontFamily: 'Bitsumishi',
                      fontSize: 16,
                      height: 1.5,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 16),
                  Text(
                    'That 198-fold chasm, nearly 20,000% more gain over the same period, proves half-assing your efforts forfeits the explosive progress built by unwavering, mission-focussed discipline.',
                    style: TextStyle(
                      color: Colors.white70,
                      fontFamily: 'Bitsumishi',
                      fontSize: 16,
                      height: 1.5,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Progress image 3 - Clickable
              GestureDetector(
                onTap: () => _showExpandedImage(context, 'assets/images/progress3.png'),
                child: Container(
                  height: 120,
                  width: double.infinity,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.grey[600]!),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Image.asset(
                      'assets/images/progress3.png',
                      fit: BoxFit.contain,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          color: Colors.grey[800],
                          child: const Center(
                            child: Text(
                              'Progress Chart 3\n(Tap to expand)',
                              style: TextStyle(color: Colors.grey, fontSize: 14),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 20),
              
              // Quotes section - North Star Quest style
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.amberAccent.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.amberAccent.withValues(alpha: 0.3)),
                ),
                child: const Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '🌟 The Power of Compounding:',
                      style: TextStyle(
                        color: Colors.amberAccent,
                        fontFamily: 'Pirulen',
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      '"We are what we repeatedly do. Excellence, then, is not an act, but a habit." — Aristotle\n\nThat\'s the power of compounding. Tiny gains, ruthlessly repeated.',
                      style: TextStyle(
                        color: Colors.white70,
                        fontFamily: 'Bitsumishi',
                        fontSize: 14,
                        height: 1.4,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              
              const Text(
                'Your habits are your anchor.\n\n'
                'In the Mxd Out Life system, you will choose 10 core habits, your daily non-negotiables.\n\n'
                'These aren\'t just tasks.\n\n'
                'They\'re actions that sharpen your focus, build resilience, and forge your future.',
                style: TextStyle(
                  color: Colors.white70,
                  fontFamily: 'Bitsumishi',
                  fontSize: 16,
                  height: 1.5,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              
              // More quotes
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red, width: 1),
                ),
                child: const Text(
                  '"Discipline is doing what you hate to do, but doing it like you love it."\n— Mike Tyson',
                  style: TextStyle(
                    color: Colors.red,
                    fontSize: 13,
                    fontStyle: FontStyle.italic,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              const SizedBox(height: 12),
              
              const Column(
                children: [
                  Text(
                    'Each one of your habits is a decision to rise above average.',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      height: 1.4,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 8),
                  Text(
                    'To build the kind of life most people aren\'t willing to suffer for.',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      height: 1.4,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
              const SizedBox(height: 12),
              
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.orange.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.orange, width: 1),
                ),
                child: const Text(
                  '"I hated every minute of training, but I said, Don\'t quit. Suffer now and live the rest of your life as a champion."\n— Muhammad Ali',
                  style: TextStyle(
                    color: Colors.orange,
                    fontSize: 13,
                    fontStyle: FontStyle.italic,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              const SizedBox(height: 16),
              
              const Text(
                'For a MXD Out Life, your 10 daily habits are not checkboxes but daily missions, your anchors for a healthy, intelligent, and supercharged life.\n\n'
                'They wire your mind for clarity, condition your body for resilience, and sculpt your character for unshakeable resolve. Each day\'s reps compound invisibly until your year-end self is unrecognizable.\n\n'
                'Sharper, stronger, wiser - Your MXD Out self is just one year of focussed effort away.',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  height: 1.4,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              
              const Text(
                'Embrace these ten non-negotiables, execute with precision and intent, then watch as small, consistent actions yield extraordinary, exponential results.\n\n'
                'These 10 habits are where you carve yourself from stone. Your daily battles give you strength. This is how you Sharpen Your Edge.',
                style: TextStyle(
                  color: Colors.white70,
                  fontFamily: 'Bitsumishi',
                  fontSize: 16,
                  height: 1.5,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 20),
              
              // Call to action
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [Color(0xFF00FFFF), Color(0xFFB621FE)],
                    begin: Alignment.centerLeft,
                    end: Alignment.centerRight,
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Column(
                  children: [
                    Text(
                      '"We cannot live better than in seeking to become better." — Socrates',
                      style: TextStyle(
                        color: Colors.black,
                        fontSize: 16,
                        fontFamily: 'Pirulen',
                        fontWeight: FontWeight.bold,
                        fontStyle: FontStyle.italic,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    SizedBox(height: 12),
                    Text(
                      'Start now. Lock in your 10.',
                      style: TextStyle(
                        color: Colors.black,
                        fontSize: 18,
                        fontFamily: 'Pirulen',
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              
              // Final quote - North Star Quest style
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.purple.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.purple.withValues(alpha: 0.3)),
                ),
                child: const Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '🎯 Remember:', //This is the text that I want to keep, it looks good!
                      style: TextStyle(
                        color: Colors.purple,
                        fontFamily: 'Pirulen',
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      'Even if you fall short some days, or have long periods of chaos, the path back to excellence is always through your daily habits.',
                      style: TextStyle(
                        color: Colors.white70,
                        fontFamily: 'Bitsumishi',
                        fontSize: 14,
                        height: 1.4,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 24),
              
              // Continue button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: onContinue,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.cyanAccent,
                    foregroundColor: Colors.black,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: const Text(
                    'LOCK IN MY 10 HABITS',
                    style: TextStyle(
                      fontFamily: 'Pirulen',
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
