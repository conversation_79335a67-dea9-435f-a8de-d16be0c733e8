// lib/widgets/training_tracker_section.dart

import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/robust_image_service.dart';
import '../models/user_model.dart';
import '../models/training_session_model.dart';
import '../models/training_timer_model.dart';
import '../services/training_timer_service.dart';
import '../services/training_storage_service.dart';
import '../services/super_entry_service.dart';
import '../controller/user_controller2.dart';
import '../theme/colors.dart';
import '../services/comprehensive_logging_service.dart';
import '../bulletproof/error_handler.dart';
import '../services/training_debug_service.dart';
import '../services/release_config_service.dart';
import '../prompts/mxd_life_coaches.dart';
import 'training_history_modal.dart';
import 'training_comparison_modal.dart';
import '../training_tracker/widgets/training_calendar_widget.dart';
import 'training_coach_chat_modal.dart';
import 'training_program_settings_modal.dart';
import '../training_tracker/widgets/enhanced_features_button.dart';

/// Training Tracker section for the home screen
/// 
/// Features:
/// - Digital-7 timer with start/pause/reset
/// - Free-form workout notes
/// - Bodyweight tracking
/// - Save and compare functionality
/// - Auto-named sessions with date/label
/// - Integration with Health EXP system
class TrainingTrackerSection extends StatefulWidget {
  final User user;
  final Function(User) onUserUpdate;

  const TrainingTrackerSection({
    super.key,
    required this.user,
    required this.onUserUpdate,
  });

  @override
  State<TrainingTrackerSection> createState() => _TrainingTrackerSectionState();
}

class _TrainingTrackerSectionState extends State<TrainingTrackerSection> {
  final TrainingTimerService _timerService = TrainingTimerService();
  final TrainingStorageService _storageService = TrainingStorageService();
  final SuperEntryService _entryService = SuperEntryService();
  final ErrorHandler _errorHandler = ErrorHandler();
  
  final TextEditingController _labelController = TextEditingController();
  final TextEditingController _notesController = TextEditingController();
  final TextEditingController _bodyweightController = TextEditingController();
  final TextEditingController _goalController = TextEditingController();

  String? _selectedImagePath;

  TrainingProgram _program = TrainingProgram.letters();
  List<TrainingSession> _recentSessions = [];
  bool _isLoading = false;
  bool _isExpanded = true;
  bool? _lastCanSaveState; // Track state changes to reduce rebuilds
  Timer? _uiUpdateTimer; // Timer to update UI when timer is running

  @override
  void initState() {
    super.initState();
    _timerService.addListener(_onTimerStateChanged);
    _initializeTrainingTracker();
  }

  @override
  void dispose() {
    _timerService.removeListener(_onTimerStateChanged);
    _labelController.dispose();
    _notesController.dispose();
    _bodyweightController.dispose();
    _goalController.dispose();
    _uiUpdateTimer?.cancel();
    super.dispose();
  }

  /// Handle timer state changes
  void _onTimerStateChanged() {
    if (mounted) {
      setState(() {
        // Update UI when timer state changes
      });
    }
  }

  /// Initialize the training tracker with saved data
  Future<void> _initializeTrainingTracker() async {
    try {
      await ComprehensiveLoggingService.logInfo('🏃‍♂️ TrainingTracker: Initializing');
      await TrainingDebugService.logDebugEvent('TrainingTracker initialization started', null);

      if (mounted) setState(() => _isLoading = true);

      // Initialize debug monitoring
      if (ReleaseConfigService.shouldShowDebugOverlays) {
        await TrainingDebugService.initialize();
      }

      // Load training program
      _program = await _storageService.loadProgram();

      // Load recent sessions
      _recentSessions = await _storageService.getRecentSessions();

      // Set current label from program
      _labelController.text = _program.currentLabel;

      // Try to recover any current session
      final currentSession = await _storageService.loadCurrentSession();
      if (currentSession != null && !currentSession.isCompleted) {
        _labelController.text = currentSession.label;
        _notesController.text = currentSession.notes;
        _bodyweightController.text = currentSession.bodyweightKg?.toString() ?? '';
        _goalController.text = currentSession.currentGoal;

        await ComprehensiveLoggingService.logInfo('🔄 TrainingTracker: Recovered session: ${currentSession.label}');
        await TrainingDebugService.logDebugEvent('Session recovered', {
          'sessionId': currentSession.id,
          'label': currentSession.label,
          'duration': currentSession.durationSeconds,
          'goal': currentSession.currentGoal,
        });
      } else {
        // Load notes template for current workout if no session to recover
        final notesTemplate = _program.currentNotesTemplate;
        _notesController.text = notesTemplate;
      }

      if (mounted) setState(() => _isLoading = false);

      await ComprehensiveLoggingService.logInfo('✅ TrainingTracker: Initialized successfully');
      await TrainingDebugService.logDebugEvent('TrainingTracker initialization completed', {
        'programType': _program.programType,
        'currentLabel': _program.currentLabel,
        'recentSessionsCount': _recentSessions.length,
      });

      // Start UI update timer to refresh Save Training button state
      _startUIUpdateTimer();
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace);
      if (mounted) setState(() => _isLoading = false);
      await ComprehensiveLoggingService.logError('❌ TrainingTracker: Initialization failed: $e');
      await TrainingDebugService.logDebugEvent('TrainingTracker initialization failed', {'error': e.toString()});
    }
  }

  /// Start UI update timer to refresh button states when timer is running
  void _startUIUpdateTimer() {
    _uiUpdateTimer?.cancel();
    _uiUpdateTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (mounted && _timerService.isRunning) {
        // Only trigger rebuild if save state actually changed
        final currentCanSave = _canSaveTraining();
        if (_lastCanSaveState != currentCanSave) {
          setState(() {
            _lastCanSaveState = currentCanSave;
          });
        }
      }
    });
  }

  /// Get the current glow color based on user's Health category
  Color get _glowColor {
    return getCategoryColor('Health', customCategories: widget.user.customCategories);
  }

  /// Get the correct Health coach name based on user gender
  String get _healthCoachName {
    // Get Health coach from authoritative source
    final healthCoach = mxdLifeCoaches.firstWhere(
      (coach) => coach.category.toLowerCase() == 'health',
      orElse: () => mxdLifeCoaches.first,
    );

    // Debug logging to help identify gender issues
    if (kDebugMode) {
      print('🔍 TrainingTracker: User gender: "${widget.user.gender}"');
      print('🔍 TrainingTracker: Assigned coaches: ${widget.user.assignedCoaches}');
    }

    // Handle non-gender users with assigned coaches
    if (widget.user.gender.toLowerCase() == 'non-gender' &&
        widget.user.assignedCoaches != null &&
        widget.user.assignedCoaches!.containsKey('Health')) {
      final assignedGender = widget.user.assignedCoaches!['Health']!;
      final coachName = assignedGender.toLowerCase() == 'female' ? healthCoach.femaleName : healthCoach.maleName;
      if (kDebugMode) print('🔍 TrainingTracker: Non-gender user, assigned Health coach: $coachName');
      return coachName;
    }

    // Handle unknown/unset gender - default to female coach for better UX
    if (widget.user.gender.toLowerCase() == 'unknown' || widget.user.gender.isEmpty) {
      if (kDebugMode) print('🔍 TrainingTracker: Unknown gender, defaulting to female coach: ${healthCoach.femaleName}');
      return healthCoach.femaleName;
    }

    // Standard gender-based selection
    final coachName = widget.user.gender.toLowerCase() == 'female' ? healthCoach.femaleName : healthCoach.maleName;
    if (kDebugMode) print('🔍 TrainingTracker: Standard gender selection, coach: $coachName');
    return coachName;
  }

  /// Format session time for multiple sessions per day
  String _formatSessionTime(TrainingSession session) {
    final now = DateTime.now();
    final sessionDate = DateTime(session.createdAt.year, session.createdAt.month, session.createdAt.day);
    final today = DateTime(now.year, now.month, now.day);

    // Only show time for today's sessions to distinguish multiple sessions
    if (sessionDate == today) {
      final hour = session.createdAt.hour;
      final minute = session.createdAt.minute;
      return '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}';
    }

    return ''; // Don't show time for older sessions
  }

  /// Format EXP display to show half numbers properly
  String _formatExpDisplay(double exp) {
    // If it's a whole number, show without decimal
    if (exp == exp.roundToDouble()) {
      return exp.round().toString();
    }
    // Otherwise show with one decimal place
    return exp.toStringAsFixed(1);
  }

  /// Check if session is from today
  bool _isSessionFromToday(TrainingSession session) {
    final now = DateTime.now();
    final sessionDate = DateTime(session.createdAt.year, session.createdAt.month, session.createdAt.day);
    final today = DateTime(now.year, now.month, now.day);
    return sessionDate == today;
  }

  /// Get count of sessions for the same day as the given session
  int _getSessionCountForDay(TrainingSession session) {
    final sessionDate = DateTime(session.createdAt.year, session.createdAt.month, session.createdAt.day);
    return _recentSessions.where((s) {
      final sDate = DateTime(s.createdAt.year, s.createdAt.month, s.createdAt.day);
      return sDate == sessionDate;
    }).length;
  }

  /// Get session number for the day (1st, 2nd, 3rd session of the day)
  int _getSessionNumberForDay(TrainingSession session) {
    final sessionDate = DateTime(session.createdAt.year, session.createdAt.month, session.createdAt.day);
    final sameDaySessions = _recentSessions.where((s) {
      final sDate = DateTime(s.createdAt.year, s.createdAt.month, s.createdAt.day);
      return sDate == sessionDate;
    }).toList();

    // Sort by creation time (most recent first)
    sameDaySessions.sort((a, b) => b.createdAt.compareTo(a.createdAt));

    // Find the index of this session (add 1 for 1-based numbering)
    return sameDaySessions.indexWhere((s) => s.id == session.id) + 1;
  }

  /// Build Ask Coach button
  Widget _buildAskCoachButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: _showAskCoachModal,
        icon: Icon(Icons.psychology, size: 18, color: _glowColor),
        label: Text(
          'ASK $_healthCoachName',
          style: TextStyle(
            fontFamily: 'Pirulen',
            fontSize: 12,
            fontWeight: FontWeight.bold,
            color: _glowColor,
          ),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: _glowColor.withValues(alpha: 0.15),
          foregroundColor: _glowColor,
          side: BorderSide(color: _glowColor.withValues(alpha: 0.5), width: 1),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          padding: const EdgeInsets.symmetric(vertical: 12),
          elevation: 2,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        // Unfocus any active text fields when tapping outside
        FocusScope.of(context).unfocus();
      },
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.8),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: _glowColor.withValues(alpha: 0.3), width: 1),
          boxShadow: [
            BoxShadow(
              color: _glowColor.withValues(alpha: 0.2),
              blurRadius: 12,
              spreadRadius: 2,
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildHeader(),
            if (_isExpanded) ...[
              if (_isLoading)
                Container(
                  padding: const EdgeInsets.all(32),
                  child: Center(
                    child: CircularProgressIndicator(color: _glowColor),
                  ),
                )
              else ...[
                _buildTimerSection(),
                _buildWorkoutSection(),
                _buildActionButtons(),
                if (_recentSessions.isNotEmpty) _buildRecentSessions(),
              ],
            ],
          ],
        ),
      ),
    );
  }

  /// Build the collapsible header
  Widget _buildHeader() {
    return InkWell(
      onTap: () => setState(() => _isExpanded = !_isExpanded),
      borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
      child: Container(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                boxShadow: [
                  BoxShadow(
                    color: _glowColor.withValues(alpha: 0.6),
                    blurRadius: 8,
                    spreadRadius: 2,
                  ),
                ],
              ),
              child: Icon(
                Icons.fitness_center,
                color: _glowColor,
                size: 20,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'TRAINING TRACKER',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontFamily: 'Pirulen',
                      fontWeight: FontWeight.bold,
                      shadows: [
                        Shadow(
                          color: _glowColor.withValues(alpha: 0.8),
                          offset: const Offset(0, 0),
                          blurRadius: 10,
                        ),
                      ],
                    ),
                  ),
                  if (_timerService.isRunning || _timerService.elapsedSeconds > 0)
                    Text(
                      '${_timerService.formattedTime} • ${_timerService.formattedExp}',
                      style: TextStyle(
                        color: _glowColor.withValues(alpha: 0.8),
                        fontSize: 12,
                        fontFamily: 'Digital-7',
                      ),
                    ),
                ],
              ),
            ),
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButton(
                  onPressed: _showProgramSettings,
                  icon: Icon(
                    Icons.settings,
                    color: Colors.white70,
                    size: 20,
                  ),
                  tooltip: 'Training Cycle Settings',
                ),
                Icon(
                  _isExpanded ? Icons.expand_less : Icons.expand_more,
                  color: Colors.white70,
                  size: 24,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Build the timer section with Digital-7 display
  Widget _buildTimerSection() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Column(
        children: [
          // Timer display
          Container(
            padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
            decoration: BoxDecoration(
              shape: BoxShape.rectangle,
              borderRadius: BorderRadius.circular(20),
              color: Colors.black.withValues(alpha: 0.6),
              border: Border.all(color: _glowColor.withValues(alpha: 0.4), width: 2),
              boxShadow: [
                BoxShadow(
                  color: _glowColor.withValues(alpha: 0.3),
                  blurRadius: 16,
                  spreadRadius: 4,
                ),
              ],
            ),
            child: Column(
              children: [
                // Timer display
                Text(
                  _timerService.formattedTime,
                  style: TextStyle(
                    fontFamily: 'Digital-7',
                    fontSize: 48,
                    color: _glowColor,
                    fontWeight: FontWeight.bold,
                    shadows: [
                      Shadow(
                        color: _glowColor.withValues(alpha: 0.8),
                        offset: const Offset(0, 0),
                        blurRadius: 12,
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 8),
                // EXP display
                Text(
                  _timerService.formattedExp,
                  style: TextStyle(
                    fontFamily: 'Bitsumishi',
                    fontSize: 14,
                    color: _glowColor.withValues(alpha: 0.8),
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          // Timer controls
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildTimerButton(
                icon: _timerService.isRunning ? Icons.pause : Icons.play_arrow,
                label: _timerService.isRunning ? 'PAUSE' : 'START',
                onPressed: _timerService.toggle,
                isPrimary: true,
              ),
              const SizedBox(width: 16),
              _buildTimerButton(
                icon: Icons.refresh,
                label: 'RESET',
                onPressed: _timerService.canReset ? _timerService.reset : null,
                isPrimary: false,
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build timer control button
  Widget _buildTimerButton({
    required IconData icon,
    required String label,
    required VoidCallback? onPressed,
    required bool isPrimary,
  }) {
    final isEnabled = onPressed != null;
    final buttonColor = isPrimary ? _glowColor : Colors.white70;

    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, size: 18),
      label: Text(
        label,
        style: const TextStyle(
          fontFamily: 'Pirulen',
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
      style: ElevatedButton.styleFrom(
        backgroundColor: isEnabled
            ? buttonColor.withValues(alpha: 0.2)
            : Colors.grey.withValues(alpha: 0.2),
        foregroundColor: isEnabled ? buttonColor : Colors.grey,
        side: BorderSide(
          color: isEnabled ? buttonColor.withValues(alpha: 0.6) : Colors.grey.withValues(alpha: 0.6),
          width: 1,
        ),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        elevation: isEnabled ? 4 : 0,
      ),
    );
  }

  /// Build the workout section with notes and bodyweight
  Widget _buildWorkoutSection() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Current workout label
          Row(
            children: [
              Text(
                'Current Training: ',
                style: TextStyle(
                  color: Colors.white70,
                  fontSize: 14,
                  fontFamily: 'Bitsumishi',
                ),
              ),
              Expanded(
                child: TextField(
                  controller: _labelController,
                  textInputAction: TextInputAction.done,
                  onSubmitted: (_) => FocusScope.of(context).unfocus(),
                  style: TextStyle(
                    color: _glowColor,
                    fontSize: 16,
                    fontFamily: 'Pirulen',
                    fontWeight: FontWeight.bold,
                  ),
                  decoration: InputDecoration(
                    hintText: 'Enter workout name...',
                    hintStyle: TextStyle(
                      color: Colors.white30,
                      fontFamily: 'Bitsumishi',
                    ),
                    border: InputBorder.none,
                    contentPadding: const EdgeInsets.symmetric(horizontal: 8),
                  ),
                  maxLength: 30,
                  buildCounter: (context, {required currentLength, required isFocused, maxLength}) => null,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),

          // Current Goal section
          Text(
            'Current Goal:',
            style: TextStyle(
              color: Colors.white70,
              fontSize: 12,
              fontFamily: 'Bitsumishi',
            ),
          ),
          const SizedBox(height: 4),
          Container(
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: _glowColor.withValues(alpha: 0.2), width: 1),
            ),
            child: TextField(
              controller: _goalController,
              textInputAction: TextInputAction.done,
              onSubmitted: (_) => FocusScope.of(context).unfocus(),
              style: TextStyle(
                color: _glowColor.withValues(alpha: 0.9),
                fontSize: 12,
                fontFamily: 'Bitsumishi',
              ),
              decoration: InputDecoration(
                hintText: 'e.g., Build strength, Improve endurance...',
                hintStyle: TextStyle(
                  color: Colors.white30,
                  fontFamily: 'Bitsumishi',
                  fontSize: 12,
                ),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
              maxLength: 100,
              buildCounter: (context, {required currentLength, required isFocused, maxLength}) => null,
            ),
          ),
          const SizedBox(height: 16),

          // Workout notes
          Text(
            'Workout Notes:',
            style: TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontFamily: 'Bitsumishi',
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Container(
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.4),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: _glowColor.withValues(alpha: 0.3), width: 1),
            ),
            child: TextField(
              controller: _notesController,
              maxLines: 6,
              textInputAction: TextInputAction.newline,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontFamily: 'Bitsumishi',
              ),
              decoration: InputDecoration(
                hintText: 'Enter your workout details...\ne.g., 3×12 lunges, 2×8 squats, 1×30s plank',
                hintStyle: TextStyle(
                  color: Colors.white30,
                  fontFamily: 'Bitsumishi',
                ),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.all(12),
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Ask Coach section
          _buildAskCoachButton(),
          const SizedBox(height: 16),

          // Bodyweight tracker and Save Image
          Row(
            children: [
              // Bodyweight section (left-justified)
              Row(
                children: [
                  Text(
                    'Bodyweight: ',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontFamily: 'Bitsumishi',
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(
                    width: 60,
                    child: TextField(
                      controller: _bodyweightController,
                      keyboardType: TextInputType.numberWithOptions(decimal: true),
                      textInputAction: TextInputAction.done,
                      onSubmitted: (_) => FocusScope.of(context).unfocus(),
                      style: TextStyle(
                        color: _glowColor,
                        fontSize: 14,
                        fontFamily: 'Digital-7',
                      ),
                      decoration: InputDecoration(
                        hintText: '0.0',
                        hintStyle: TextStyle(
                          color: Colors.white30,
                          fontFamily: 'Digital-7',
                        ),
                        border: UnderlineInputBorder(
                          borderSide: BorderSide(color: _glowColor.withValues(alpha: 0.3)),
                        ),
                        enabledBorder: UnderlineInputBorder(
                          borderSide: BorderSide(color: _glowColor.withValues(alpha: 0.3)),
                        ),
                        focusedBorder: UnderlineInputBorder(
                          borderSide: BorderSide(color: _glowColor),
                        ),
                        contentPadding: const EdgeInsets.symmetric(horizontal: 4, vertical: 8),
                      ),
                    ),
                  ),
                  const SizedBox(width: 6),
                  Text(
                    'kg',
                    style: TextStyle(
                      color: Colors.white70,
                      fontSize: 14,
                      fontFamily: 'Bitsumishi',
                    ),
                  ),
                ],
              ),
              const Spacer(),
              // Save Image section (center-justified on right)
              GestureDetector(
                onTap: _pickImage,
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
                  decoration: BoxDecoration(
                    color: _selectedImagePath != null
                        ? _glowColor.withValues(alpha: 0.2)
                        : Colors.grey.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: _selectedImagePath != null
                          ? _glowColor.withValues(alpha: 0.5)
                          : Colors.grey.withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        _selectedImagePath != null ? Icons.photo : Icons.add_a_photo,
                        color: _selectedImagePath != null ? _glowColor : Colors.white70,
                        size: 16,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        _selectedImagePath != null ? 'Saved' : 'Save Image',
                        style: TextStyle(
                          color: _selectedImagePath != null ? _glowColor : Colors.white70,
                          fontSize: 11,
                          fontFamily: 'Bitsumishi',
                          fontWeight: _selectedImagePath != null ? FontWeight.bold : FontWeight.normal,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build action buttons (Save, Compare, History)
  Widget _buildActionButtons() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Column(
        children: [
          // Top row: Save and Compare
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _canSaveTraining() ? _saveTraining : null,
                  icon: Icon(
                    Icons.save,
                    size: 18,
                    color: _canSaveTraining() ? _glowColor : Colors.grey,
                  ),
                  label: Text(
                    'SAVE TRAINING',
                    style: TextStyle(
                      fontFamily: 'Pirulen',
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: _canSaveTraining() ? _glowColor : Colors.grey,
                    ),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _canSaveTraining()
                        ? _glowColor.withValues(alpha: 0.2)
                        : Colors.grey.withValues(alpha: 0.1),
                    foregroundColor: _canSaveTraining() ? _glowColor : Colors.grey,
                    side: BorderSide(
                      color: _canSaveTraining()
                          ? _glowColor.withValues(alpha: 0.6)
                          : Colors.grey.withValues(alpha: 0.3),
                      width: 1
                    ),
                    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    elevation: _canSaveTraining() ? 2 : 0,
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _showCompareModal,
                  icon: const Icon(Icons.compare_arrows, size: 18),
                  label: const Text(
                    'COMPARE WITH',
                    style: TextStyle(
                      fontFamily: 'Pirulen',
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _recentSessions.isNotEmpty
                        ? Colors.white.withValues(alpha: 0.1)
                        : _glowColor.withValues(alpha: 0.1),
                    foregroundColor: _recentSessions.isNotEmpty
                        ? Colors.white70
                        : _glowColor.withValues(alpha: 0.7),
                    side: BorderSide(
                      color: _recentSessions.isNotEmpty
                          ? Colors.white.withValues(alpha: 0.3)
                          : _glowColor.withValues(alpha: 0.3),
                      width: 1
                    ),
                    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          // Training Calendar (full width)
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _showTrainingCalendar,
              icon: const Icon(Icons.calendar_month, size: 18),
              label: const Text(
                'TRAINING \nCALENDAR',
                style: TextStyle(
                  fontFamily: 'Pirulen',
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: _glowColor.withValues(alpha: 0.15),
                foregroundColor: _glowColor,
                side: BorderSide(color: _glowColor.withValues(alpha: 0.5), width: 1),
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
          const SizedBox(height: 8),
          // Bottom row: Training History (full width)
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _showTrainingHistory,
              icon: const Icon(Icons.history, size: 18),
              label: const Text(
                'TRAINING HISTORY',
                style: TextStyle(
                  fontFamily: 'Pirulen',
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: _glowColor.withValues(alpha: 0.1),
                foregroundColor: _glowColor.withValues(alpha: 0.8),
                side: BorderSide(color: _glowColor.withValues(alpha: 0.4), width: 1),
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
          const SizedBox(height: 12),
          // Enhanced Training Features
          FutureBuilder<List<TrainingSession>>(
            future: _storageService.getAllSessions(),
            builder: (context, snapshot) {
              final allSessions = snapshot.data ?? [];
              return EnhancedFeaturesButton(
                sessions: allSessions,
                glowColor: _glowColor,
              );
            },
          ),
        ],
      ),
    );
  }

  /// Build recent sessions list
  Widget _buildRecentSessions() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Recent Sessions',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontFamily: 'Bitsumishi',
                  fontWeight: FontWeight.bold,
                ),
              ),
              TextButton(
                onPressed: _showTrainingHistory,
                child: Text(
                  'View All',
                  style: TextStyle(
                    color: _glowColor,
                    fontSize: 12,
                    fontFamily: 'Bitsumishi',
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          ...(_recentSessions.take(5).map((session) => _buildSessionTile(session))),
        ],
      ),
    );
  }

  /// Build individual session tile
  Widget _buildSessionTile(TrainingSession session) {
    final isToday = _isSessionFromToday(session);
    final sessionCount = _getSessionCountForDay(session);

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.4),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isToday && sessionCount > 1
              ? _glowColor.withValues(alpha: 0.4)
              : Colors.white.withValues(alpha: 0.1),
          width: isToday && sessionCount > 1 ? 2 : 1
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      session.label,
                      style: TextStyle(
                        color: _glowColor,
                        fontSize: 14,
                        fontFamily: 'Pirulen',
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    if (isToday && sessionCount > 1) ...[
                      const SizedBox(width: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: _glowColor.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(10),
                          border: Border.all(color: _glowColor.withValues(alpha: 0.5), width: 1),
                        ),
                        child: Text(
                          'Session ${_getSessionNumberForDay(session)}',
                          style: TextStyle(
                            color: _glowColor,
                            fontSize: 10,
                            fontFamily: 'Digital-7',
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
                Text(
                  '${session.formattedDuration} • ${_formatExpDisplay(session.expEarned)} EXP • ${session.formattedDate} ${_formatSessionTime(session)}',
                  style: const TextStyle(
                    color: Colors.white70,
                    fontSize: 12,
                    fontFamily: 'Digital-7',
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () => _loadSessionForComparison(session),
            icon: Icon(
              Icons.compare_arrows,
              color: _glowColor.withValues(alpha: 0.7),
              size: 18,
            ),
          ),
        ],
      ),
    );
  }

  /// Check if training can be saved
  bool _canSaveTraining() {
    // Allow saving if there's a label and either:
    // 1. Timer has been used (any time elapsed) OR timer is currently running
    // 2. Notes have been entered
    // 3. Bodyweight has been recorded
    final hasLabel = _labelController.text.trim().isNotEmpty;
    final hasTime = _timerService.elapsedSeconds > 0 || _timerService.isRunning;
    final hasNotes = _notesController.text.trim().isNotEmpty;
    final hasBodyweight = _bodyweightController.text.trim().isNotEmpty;

    final canSave = hasLabel && (hasTime || hasNotes || hasBodyweight);

    // Reduced debug logging to prevent excessive rebuilds and semantics errors
    // Only log when state changes to reduce widget tree corruption
    if (ReleaseConfigService.shouldShowDebugOverlays && _lastCanSaveState != canSave) {
      print('🔧 TrainingTracker: _canSaveTraining state changed to: $canSave');
      _lastCanSaveState = canSave;
    }

    return canSave;
  }

  /// Save the current training session
  Future<void> _saveTraining() async {
    try {
      await ComprehensiveLoggingService.logInfo('💾 TrainingTracker: Saving training session');
      await TrainingDebugService.logDebugEvent('Training session save started', null);

      setState(() => _isLoading = true);

      final label = _labelController.text.trim();
      final notes = _notesController.text.trim();
      final goal = _goalController.text.trim();
      final bodyweight = double.tryParse(_bodyweightController.text.trim());
      final duration = _timerService.elapsedSeconds;

      await ComprehensiveLoggingService.logInfo('💾 TrainingTracker: Session data - Label: "$label", Duration: ${duration}s, Notes: "${notes.length} chars", Goal: "$goal", Bodyweight: $bodyweight');

      // Create completed session
      final session = TrainingSession.create(
        label: label,
        notes: notes,
        currentGoal: goal,
        bodyweightKg: bodyweight,
        imagePath: _selectedImagePath,
      ).complete(
        finalDurationSeconds: duration,
        finalNotes: notes,
        finalCurrentGoal: goal,
        finalBodyweightKg: bodyweight,
        finalImagePath: _selectedImagePath,
      );

      // Validate session data in debug mode
      if (ReleaseConfigService.shouldShowDebugOverlays) {
        final validation = await TrainingDebugService.validateSession(session);
        if (!validation['isValid']) {
          await TrainingDebugService.logDebugEvent('Session validation failed', validation);
        }
      }

      // Save session to storage
      await _storageService.saveSession(session);

      // Add Health EXP to user via SuperEntryService
      if (session.expEarned > 0 && mounted) {
        final userController = context.read<UserController2>();

        // Create a more descriptive diary note for multiple sessions per day
        final timeStamp = '${session.createdAt.hour.toString().padLeft(2, '0')}:${session.createdAt.minute.toString().padLeft(2, '0')}';
        final diaryNote = '${session.label} Training (${session.formattedDuration}) - $timeStamp';

        await ComprehensiveLoggingService.logInfo('💪 TrainingTracker: Adding Health EXP via SuperEntryService');
        await ComprehensiveLoggingService.logInfo('   Session: ${session.label}');
        await ComprehensiveLoggingService.logInfo('   Duration: ${session.formattedDuration}');
        await ComprehensiveLoggingService.logInfo('   EXP: ${session.expEarned}');
        await ComprehensiveLoggingService.logInfo('   Diary Note: "$diaryNote"');
        await ComprehensiveLoggingService.logInfo('   Coach: $_healthCoachName');

        final updatedUser = await _entryService.addDiaryEntry(
          userController: userController,
          category: 'Health',
          note: diaryNote,
          exp: session.expEarned,
        );

        // Verify the update worked
        final oldHealthExp = widget.user.getExp('Health');
        final newHealthExp = updatedUser.getExp('Health');
        final oldTotalExp = widget.user.exp;
        final newTotalExp = updatedUser.exp;

        await ComprehensiveLoggingService.logInfo('✅ TrainingTracker: EXP update verification:');
        await ComprehensiveLoggingService.logInfo('   Health EXP: $oldHealthExp → $newHealthExp (+${newHealthExp - oldHealthExp})');
        await ComprehensiveLoggingService.logInfo('   Total EXP: $oldTotalExp → $newTotalExp (+${newTotalExp - oldTotalExp})');
        await ComprehensiveLoggingService.logInfo('   Diary entries: ${widget.user.diaryEntries.length} → ${updatedUser.diaryEntries.length}');

        widget.onUserUpdate(updatedUser);

        await ComprehensiveLoggingService.logInfo('✅ TrainingTracker: Added ${session.expEarned} Health EXP with $_healthCoachName');
        await TrainingDebugService.logDebugEvent('Health EXP added', {
          'expAmount': session.expEarned,
          'coach': _healthCoachName,
          'category': 'Health',
          'diaryNote': diaryNote,
          'oldHealthExp': oldHealthExp,
          'newHealthExp': newHealthExp,
          'oldTotalExp': oldTotalExp,
          'newTotalExp': newTotalExp,
        });
      }

      // Advance program to next workout
      _program = _program.advance();
      await _storageService.saveProgram(_program);

      // Reset for next session
      await _resetForNextSession();

      // Refresh recent sessions
      _recentSessions = await _storageService.getRecentSessions();

      if (mounted) setState(() => _isLoading = false);

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Training saved! +${_formatExpDisplay(session.expEarned)} Health EXP earned with $_healthCoachName',
              style: const TextStyle(fontFamily: 'Bitsumishi'),
            ),
            backgroundColor: _glowColor,
            duration: const Duration(seconds: 3),
          ),
        );

        // Debug confirmation
        print('✅ TrainingTracker: Success message shown to user');
        print('   Session: ${session.label}');
        print('   Duration: ${session.formattedDuration}');
        print('   EXP: ${session.expEarned}');
      }

      await ComprehensiveLoggingService.logInfo('✅ TrainingTracker: Training session saved successfully');
      await TrainingDebugService.logDebugEvent('Training session saved successfully', {
        'sessionId': session.id,
        'label': session.label,
        'duration': session.formattedDuration,
        'expEarned': session.expEarned,
        'nextWorkout': _program.currentLabel,
      });
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace);
      if (mounted) setState(() => _isLoading = false);

      await TrainingDebugService.logDebugEvent('Training session save failed', {'error': e.toString()});

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(
              'Failed to save training session',
              style: TextStyle(fontFamily: 'Bitsumishi'),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Reset UI for next training session
  Future<void> _resetForNextSession() async {
    await _timerService.reset();
    _labelController.text = _program.currentLabel;

    // Load notes template for current workout if available
    final notesTemplate = _program.currentNotesTemplate;
    _notesController.text = notesTemplate;

    _goalController.clear();
    _bodyweightController.clear();
    _selectedImagePath = null;
    await _storageService.clearCurrentSession();
  }

  /// Show comparison modal
  void _showCompareModal() {
    if (_recentSessions.isEmpty) {
      // Show message for first-time users
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          backgroundColor: Colors.black.withValues(alpha: 0.9),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          title: Row(
            children: [
              Icon(Icons.compare_arrows, color: _glowColor, size: 24),
              const SizedBox(width: 12),
              Text(
                'No Sessions Yet',
                style: TextStyle(
                  color: Colors.white,
                  fontFamily: 'Pirulen',
                  fontSize: 16,
                ),
              ),
            ],
          ),
          content: Text(
            'Complete your first training session to unlock comparison features. Save this workout to start building your training history!',
            style: TextStyle(
              color: Colors.white70,
              fontFamily: 'Bitsumishi',
              fontSize: 14,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'Got it!',
                style: TextStyle(
                  color: _glowColor,
                  fontFamily: 'Pirulen',
                  fontSize: 12,
                ),
              ),
            ),
          ],
        ),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => TrainingComparisonSelector(
        currentLabel: _labelController.text.trim(),
        availableSessions: _recentSessions,
        glowColor: _glowColor,
        onSessionSelected: (selectedSession) {
          Navigator.of(context).pop();
          _showSessionComparison(selectedSession);
        },
        onClose: () => Navigator.of(context).pop(),
      ),
    );
  }

  /// Show training program settings modal
  void _showProgramSettings() {
    showDialog(
      context: context,
      builder: (context) => TrainingProgramSettingsModal(
        currentProgram: _program,
        glowColor: _glowColor,
        onProgramChanged: (newProgram) {
          setState(() {
            _program = newProgram;
            _labelController.text = newProgram.currentLabel;
          });
          _saveTrainingProgram(newProgram);
        },
        onClose: () => Navigator.of(context).pop(),
      ),
    );
  }

  /// Save training program to persistent storage
  Future<void> _saveTrainingProgram(TrainingProgram program) async {
    try {
      await ComprehensiveLoggingService.logInfo('💾 TrainingTracker: Saving training program: ${program.programType}');
      await _storageService.saveProgram(program);
      await ComprehensiveLoggingService.logInfo('✅ TrainingTracker: Training program saved successfully');
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ TrainingTracker: Failed to save training program: $e');
    }
  }

  /// Show training history
  void _showTrainingHistory() {
    showDialog(
      context: context,
      builder: (context) => TrainingHistoryModal(
        glowColor: _glowColor,
        onClose: () => Navigator.of(context).pop(),
        onSessionSelected: (session) {
          Navigator.of(context).pop();
          _loadSessionForComparison(session);
        },
      ),
    );
  }

  /// Show training calendar
  void _showTrainingCalendar() async {
    await ComprehensiveLoggingService.logInfo('📅 Opening Training Calendar');

    if (!mounted) return;

    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (dialogContext) => TrainingCalendarWidget(
        glowColor: _glowColor,
        onClose: () {
          Navigator.of(dialogContext).pop();
          ComprehensiveLoggingService.logInfo('📅 Training Calendar closed');
        },
        onSessionPlanned: (session) async {
          await ComprehensiveLoggingService.logInfo('📅 Session planned: ${session.workoutType}');
        },
        onRestDayPlanned: (restDay) async {
          await ComprehensiveLoggingService.logInfo('📅 Rest day planned: ${restDay.type}');
        },
      ),
    );
  }

  /// Load session for comparison
  void _loadSessionForComparison(TrainingSession session) {
    _showSessionComparison(session);
  }

  /// Show session comparison modal
  void _showSessionComparison(TrainingSession previousSession) {
    // Create a current session snapshot for comparison
    final currentSession = TrainingSession.create(
      label: _labelController.text.trim(),
      notes: _notesController.text.trim(),
      currentGoal: _goalController.text.trim(),
      bodyweightKg: double.tryParse(_bodyweightController.text.trim()),
      imagePath: _selectedImagePath,
    ).complete(
      finalDurationSeconds: _timerService.elapsedSeconds,
      finalNotes: _notesController.text.trim(),
      finalCurrentGoal: _goalController.text.trim(),
      finalBodyweightKg: double.tryParse(_bodyweightController.text.trim()),
      finalImagePath: _selectedImagePath,
    );

    showDialog(
      context: context,
      builder: (context) => TrainingComparisonModal(
        currentSession: currentSession,
        previousSession: previousSession,
        glowColor: _glowColor,
        onClose: () => Navigator.of(context).pop(),
      ),
    );
  }

  /// Show Ask Coach modal
  void _showAskCoachModal() {
    showDialog(
      context: context,
      builder: (context) => TrainingCoachChatModal(
        user: widget.user,
        coachName: _healthCoachName,
        glowColor: _glowColor,
        onClose: () => Navigator.of(context).pop(),
      ),
    );
  }

  /// Pick image using robust image service (completely avoids iOS photo picker)
  Future<void> _pickImage() async {
    try {
      await ComprehensiveLoggingService.logInfo('�️ Opening robust image picker');

      if (!mounted) return;

      // Use robust image service that completely bypasses iOS photo picker
      final String? imagePath = await RobustImageService.selectImageSafely(
        context: 'training_tracker',
        allowCamera: false,
      );

      if (imagePath != null) {
        // Validate the selected image
        final bool isValid = await RobustImageService.validateImageFile(imagePath);

        if (isValid && mounted) {
          setState(() {
            _selectedImagePath = imagePath;
          });

          await ComprehensiveLoggingService.logInfo('✅ Image selected successfully: $imagePath');

          // Show success feedback
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Image saved successfully!',
                style: const TextStyle(
                  fontFamily: 'Bitsumishi',
                  fontSize: 14,
                ),
              ),
              backgroundColor: _glowColor,
              duration: const Duration(seconds: 2),
            ),
          );
          }
        } else {
          throw Exception('Selected image file is not valid or accessible');
        }
      } else {
        await ComprehensiveLoggingService.logInfo('📸 Image selection cancelled');
      }
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Error picking image: $e');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Failed to select image. Please try again.',
              style: const TextStyle(
                fontFamily: 'Bitsumishi',
                fontSize: 14,
              ),
            ),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }
}
