// lib/widgets/critical_error_app.dart

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// Critical error app shown when the main app cannot start safely
/// 
/// This widget is displayed when critical systems fail during initialization
/// and it's not safe to continue with normal app operation. It provides
/// users with clear information and recovery options.
class CriticalErrorApp extends StatelessWidget {
  final String error;
  final String? details;
  final VoidCallback? onRetry;

  const CriticalErrorApp({
    super.key,
    required this.error,
    this.details,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'MXD - Critical Error',
      theme: ThemeData.dark().copyWith(
        colorScheme: ColorScheme.dark(
          primary: Colors.red,
          secondary: Colors.red,
        ),
        scaffoldBackgroundColor: Colors.black,
      ),
      home: Scaffold(
        backgroundColor: Colors.black,
        body: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Error icon
                Container(
                  width: 120,
                  height: 120,
                  decoration: BoxDecoration(
                    color: Colors.red.withValues(alpha: 0.1),
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: Colors.red,
                      width: 2,
                    ),
                  ),
                  child: const Icon(
                    Icons.error_outline,
                    size: 60,
                    color: Colors.red,
                  ),
                ),
                
                const SizedBox(height: 32),
                
                // Title
                Text(
                  'Critical System Error',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.red[300],
                  ),
                  textAlign: TextAlign.center,
                ),
                
                const SizedBox(height: 16),
                
                // Error message
                Text(
                  error,
                  style: const TextStyle(
                    fontSize: 16,
                    color: Colors.white70,
                  ),
                  textAlign: TextAlign.center,
                ),
                
                if (details != null) ...[
                  const SizedBox(height: 12),
                  Text(
                    details!,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Colors.white54,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
                
                const SizedBox(height: 48),
                
                // Action buttons
                Column(
                  children: [
                    // Retry button (if callback provided)
                    if (onRetry != null)
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: onRetry,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.blue[400],
                            foregroundColor: Colors.black,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: const Text(
                            'Retry',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                    
                    if (onRetry != null) const SizedBox(height: 16),
                    
                    // Restart app button
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: () {
                          // Force restart the app
                          SystemNavigator.pop();
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.grey[800],
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: const Text(
                          'Restart App',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 32),
                
                // Support information
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.grey[900],
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: Colors.grey[700]!,
                      width: 1,
                    ),
                  ),
                  child: Column(
                    children: [
                      Text(
                        'Need Help?',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.grey[300],
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'If this problem persists, please contact <NAME_EMAIL>',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[400],
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// Recovery error app for when main app fails but recovery might be possible
class RecoveryErrorApp extends StatefulWidget {
  final String error;
  final Future<bool> Function() recoveryAction;

  const RecoveryErrorApp({
    super.key,
    required this.error,
    required this.recoveryAction,
  });

  @override
  State<RecoveryErrorApp> createState() => _RecoveryErrorAppState();
}

class _RecoveryErrorAppState extends State<RecoveryErrorApp> {
  bool _isRecovering = false;
  String? _recoveryError;

  Future<void> _attemptRecovery() async {
    setState(() {
      _isRecovering = true;
      _recoveryError = null;
    });

    try {
      final success = await widget.recoveryAction();
      if (success) {
        // Recovery successful - restart the main app
        // This would typically involve restarting the entire app
        SystemNavigator.pop();
      } else {
        setState(() {
          _recoveryError = 'Recovery failed. Please restart the app manually.';
          _isRecovering = false;
        });
      }
    } catch (e) {
      setState(() {
        _recoveryError = 'Recovery error: ${e.toString()}';
        _isRecovering = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return CriticalErrorApp(
      error: widget.error,
      details: _recoveryError,
      onRetry: _isRecovering ? null : _attemptRecovery,
    );
  }
}
