// lib/widgets/auth_email_password_modal.dart

import 'package:flutter/material.dart';

import '../services/klaviyo_service.dart';
import 'email_verification_loading_modal.dart';
import '../services/signup_validation_service.dart';
import 'password_requirements_checklist.dart';

/// Combined modal for email entry and password creation during signup
class AuthEmailPasswordModal extends StatefulWidget {
  final Function(String email, String password) onContinue;

  const AuthEmailPasswordModal({
    super.key,
    required this.onContinue,
  });

  @override
  State<AuthEmailPasswordModal> createState() => _AuthEmailPasswordModalState();
}

class _AuthEmailPasswordModalState extends State<AuthEmailPasswordModal> {
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _confirmPasswordController = TextEditingController();
  
  String? _emailError;
  String? _passwordError;
  bool _isPasswordVisible = false;
  bool _isConfirmPasswordVisible = false;
  bool _isLoading = false;
  bool _showPasswordChecklist = false;
  final _validationService = SignupValidationService();

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    // 🛡️ PHASE 3: Dispose validation service to prevent timer leaks
    _validationService.dispose();
    super.dispose();
  }

  bool get _canContinue {
    return _emailError == null &&
           _passwordError == null &&
           _emailController.text.isNotEmpty &&
           _passwordController.text.isNotEmpty &&
           _confirmPasswordController.text.isNotEmpty &&
           PasswordRequirementsChecklist.isPasswordValid(_passwordController.text) &&
           _passwordController.text == _confirmPasswordController.text &&
           !_isLoading;
  }

  void _validateEmail(String email) {
    print('🔄 AuthEmailPasswordModal: Validating email: "$email"');
    setState(() {
      if (email.isEmpty) {
        _emailError = null;
      } else if (!KlaviyoService.isValidEmailFormat(email)) {
        _emailError = 'Please enter a valid email address';
        print('🔄 AuthEmailPasswordModal: Email validation failed for: "$email"');
      } else {
        _emailError = null;
        print('🔄 AuthEmailPasswordModal: Email validation passed for: "$email"');
      }
    });
  }

  void _validatePassword(String password) {
    setState(() {
      _showPasswordChecklist = password.isNotEmpty;
      
      if (password.isEmpty) {
        _passwordError = null;
      } else if (!PasswordRequirementsChecklist.isPasswordValid(password)) {
        _passwordError = null; // Let the checklist handle validation display
      } else {
        _passwordError = null;
      }
      
      // Check password confirmation match
      if (_confirmPasswordController.text.isNotEmpty && 
          _confirmPasswordController.text != password) {
        _passwordError = 'Passwords do not match';
      }
    });
  }

  void _validateConfirmPassword(String confirmPassword) {
    setState(() {
      if (confirmPassword.isNotEmpty && confirmPassword != _passwordController.text) {
        _passwordError = 'Passwords do not match';
      } else {
        _passwordError = null;
      }
    });
  }

  Future<void> _attemptContinue() async {
    if (!_canContinue) {
      print('🔄 AuthEmailPasswordModal: Cannot continue - validation failed');
      return;
    }

    print('🔄 AuthEmailPasswordModal: Attempting to continue with email: ${_emailController.text}');
    setState(() {
      _isLoading = true;
      _emailError = null;
    });

    try {
      // Validate email format first
      print('🔄 AuthEmailPasswordModal: Validating email format...');
      final emailValidation = await _validationService.validateEmail(_emailController.text, checkAvailability: false);
      if (!emailValidation.isValid) {
        print('❌ AuthEmailPasswordModal: Email format invalid');
        setState(() {
          _emailError = emailValidation.message;
          _isLoading = false;
        });
        return;
      }
      print('✅ AuthEmailPasswordModal: Email format valid');

      // Check if email already exists in Klaviyo
      print('🔄 AuthEmailPasswordModal: Checking email existence in Klaviyo...');
      final emailExists = await KlaviyoService.emailExists(_emailController.text);

      if (emailExists) {
        print('❌ AuthEmailPasswordModal: Email already exists in Klaviyo');
        setState(() {
          _emailError = 'This email is already registered. Try a different email or use \'Forgot Password\'';
          _isLoading = false;
        });
        return;
      }
      print('✅ AuthEmailPasswordModal: Email is available');

      // Validate password strength
      print('🔄 AuthEmailPasswordModal: Validating password strength...');
      final passwordValidation = _validationService.validatePassword(_passwordController.text);
      if (!passwordValidation.isValid) {
        print('❌ AuthEmailPasswordModal: Password validation failed');
        setState(() {
          _passwordError = passwordValidation.message;
          _isLoading = false;
        });
        return;
      }
      print('✅ AuthEmailPasswordModal: Password validation passed');

      // Add email to Klaviyo list
      print('🔄 AuthEmailPasswordModal: Adding email to Klaviyo list...');
      final success = await KlaviyoService.addEmailToList(_emailController.text);

      if (!success) {
        print('❌ AuthEmailPasswordModal: Failed to add email to Klaviyo');
        setState(() {
          _emailError = 'Connection failed. Please check your internet and try again';
          _isLoading = false;
        });
        return;
      }
      print('✅ AuthEmailPasswordModal: Email successfully added to Klaviyo');

      // Verify email was actually added (double-check for reliability)
      print('🔄 AuthEmailPasswordModal: Verifying email was added to Klaviyo...');
      final verifyExists = await KlaviyoService.emailExists(_emailController.text);
      if (!verifyExists) {
        print('❌ AuthEmailPasswordModal: Email verification failed - not found after adding');
        setState(() {
          _emailError = 'Email registration failed. Please try again';
          _isLoading = false;
        });
        return;
      }
      print('✅ AuthEmailPasswordModal: Email verification successful');

      // Success - show email verification loading modal
      print('✅ AuthEmailPasswordModal: Email validation complete, showing verification loading modal');
      _showEmailVerificationLoadingModal();

    } catch (e) {
      print('❌ AuthEmailPasswordModal: Error during email validation: $e');
      setState(() {
        _emailError = 'Connection failed. Please check your internet and try again';
        _isLoading = false;
      });
    }
  }

  /// Show email verification loading modal
  void _showEmailVerificationLoadingModal() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => EmailVerificationLoadingModal(
        email: _emailController.text,
        password: _passwordController.text,
        onSuccess: () {
          Navigator.of(context).pop(); // Close loading modal
          Navigator.of(context).pop(); // Close email/password modal
          widget.onContinue(_emailController.text, _passwordController.text);
        },
        onError: (error) {
          Navigator.of(context).pop(); // Close loading modal
          setState(() {
            _emailError = error;
            _isLoading = false;
          });
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.black.withValues(alpha: 0.95),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.85,
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.8,
        ),
        padding: const EdgeInsets.all(32),
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Title
              const Text(
                'Complete Your Account',
                style: TextStyle(
                  color: Colors.cyanAccent,
                  fontFamily: 'Pirulen',
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              const Text(
                'Enter your email and create a secure password',
                style: TextStyle(
                  color: Colors.white70,
                  fontFamily: 'Bitsumishi',
                  fontSize: 16,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),

              // Email verification warning
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.orange.withValues(alpha: 0.1),
                  border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.warning_amber_rounded,
                      color: Colors.orange,
                      size: 20,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        'Important: You MUST verify your email on the same device you plan to use for this app!',
                        style: TextStyle(
                          color: Colors.orange,
                          fontFamily: 'Bitsumishi',
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 24),
              
              // Email field
              TextField(
                controller: _emailController,
                onChanged: (value) {
                  print('🔄 AuthEmailPasswordModal: Email field changed to: "$value"');
                  print('🎨 Font rendering debug: Using Digital-7 font for @ symbol support');
                  _validateEmail(value);
                },
                keyboardType: TextInputType.emailAddress,
                textInputAction: TextInputAction.next,
                autocorrect: false,
                enableSuggestions: false,
                // Remove input formatters completely - let email field accept all characters
                style: const TextStyle(
                  color: Colors.white,
                  fontFamily: 'Digital-7', // Changed to Digital-7 for app immersion while maintaining @ symbol support
                  fontSize: 16,
                  // Digital-7 maintains retro aesthetic with @ symbol support
                ),
                decoration: InputDecoration(
                  labelText: 'Email',
                  labelStyle: const TextStyle(
                    color: Colors.cyanAccent,
                    fontFamily: 'Pirulen',
                    fontSize: 14,
                  ),
                  hintText: 'Enter your email address',
                  hintStyle: TextStyle(
                    color: Colors.grey[400],
                    fontFamily: 'Digital-7', // Changed to Digital-7 for consistency with email input
                  ),
                  filled: true,
                  fillColor: Colors.grey[900],
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(color: Colors.grey[700]!),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(color: Colors.grey[700]!),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(color: Colors.cyanAccent),
                  ),
                  errorBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(color: Colors.red),
                  ),
                  focusedErrorBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(color: Colors.red),
                  ),
                ),
              ),
              
              // Email error
              if (_emailError != null) ...[
                const SizedBox(height: 8),
                Align(
                  alignment: Alignment.centerLeft,
                  child: Text(
                    _emailError!,
                    style: const TextStyle(
                      color: Colors.red,
                      fontFamily: 'Bitsumishi',
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
              
              const SizedBox(height: 24),
              
              // Password field
              TextField(
                controller: _passwordController,
                onChanged: _validatePassword,
                obscureText: !_isPasswordVisible,
                style: const TextStyle(
                  color: Colors.white,
                  fontFamily: 'Bitsumishi',
                  fontSize: 16,
                ),
                decoration: InputDecoration(
                  labelText: 'Password',
                  labelStyle: const TextStyle(
                    color: Colors.cyanAccent,
                    fontFamily: 'Pirulen',
                    fontSize: 14,
                  ),
                  hintText: 'Create a secure password',
                  hintStyle: TextStyle(
                    color: Colors.grey[400],
                    fontFamily: 'Bitsumishi',
                  ),
                  filled: true,
                  fillColor: Colors.grey[900],
                  suffixIcon: IconButton(
                    icon: Icon(
                      _isPasswordVisible ? Icons.visibility : Icons.visibility_off,
                      color: Colors.grey[400],
                    ),
                    onPressed: () {
                      setState(() {
                        _isPasswordVisible = !_isPasswordVisible;
                      });
                    },
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(color: Colors.grey[700]!),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(color: Colors.grey[700]!),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(color: Colors.cyanAccent),
                  ),
                  errorBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(color: Colors.red),
                  ),
                  focusedErrorBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(color: Colors.red),
                  ),
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Confirm password field
              TextField(
                controller: _confirmPasswordController,
                onChanged: _validateConfirmPassword,
                obscureText: !_isConfirmPasswordVisible,
                style: const TextStyle(
                  color: Colors.white,
                  fontFamily: 'Bitsumishi',
                  fontSize: 16,
                ),
                decoration: InputDecoration(
                  labelText: 'Confirm Password',
                  labelStyle: const TextStyle(
                    color: Colors.cyanAccent,
                    fontFamily: 'Pirulen',
                    fontSize: 14,
                  ),
                  hintText: 'Confirm your password',
                  hintStyle: TextStyle(
                    color: Colors.grey[400],
                    fontFamily: 'Bitsumishi',
                  ),
                  filled: true,
                  fillColor: Colors.grey[900],
                  suffixIcon: IconButton(
                    icon: Icon(
                      _isConfirmPasswordVisible ? Icons.visibility : Icons.visibility_off,
                      color: Colors.grey[400],
                    ),
                    onPressed: () {
                      setState(() {
                        _isConfirmPasswordVisible = !_isConfirmPasswordVisible;
                      });
                    },
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(color: Colors.grey[700]!),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(color: Colors.grey[700]!),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(color: Colors.cyanAccent),
                  ),
                  errorBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(color: Colors.red),
                  ),
                  focusedErrorBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(color: Colors.red),
                  ),
                ),
              ),
              
              // Password error
              if (_passwordError != null) ...[
                const SizedBox(height: 8),
                Align(
                  alignment: Alignment.centerLeft,
                  child: Text(
                    _passwordError!,
                    style: const TextStyle(
                      color: Colors.red,
                      fontFamily: 'Bitsumishi',
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
              
              // Password requirements checklist
              if (_showPasswordChecklist) ...[
                const SizedBox(height: 16),
                PasswordRequirementsChecklist(password: _passwordController.text),
              ],
              
              const SizedBox(height: 32),
              
              // Continue button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _canContinue ? _attemptContinue : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _canContinue ? Colors.cyanAccent : Colors.grey[700],
                    foregroundColor: Colors.black,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: _isLoading
                      ? const SizedBox(
                          width: 24,
                          height: 24,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.black),
                          ),
                        )
                      : const Text(
                          'Create Account',
                          style: TextStyle(
                            fontFamily: 'Pirulen',
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
