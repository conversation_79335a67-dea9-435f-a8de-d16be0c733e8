// lib/widgets/training_coach_chat_modal.dart

import 'package:flutter/material.dart';
import '../models/user_model.dart';
import '../screens/coach_chat_screen.dart';

/// Small coach chat modal for training sessions
/// Opens a compact version of the coach chat without disrupting training
class TrainingCoachChatModal extends StatelessWidget {
  final User user;
  final String coachName;
  final Color glowColor;
  final VoidCallback onClose;

  const TrainingCoachChatModal({
    super.key,
    required this.user,
    required this.coachName,
    required this.glowColor,
    required this.onClose,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.black.withValues(alpha: 0.95),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.7,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: glowColor.withValues(alpha: 0.3), width: 2),
          boxShadow: [
            BoxShadow(
              color: glowColor.withValues(alpha: 0.2),
              blurRadius: 20,
              spreadRadius: 4,
            ),
          ],
        ),
        child: Column(
          children: [
            _buildHeader(),
            const SizedBox(height: 12),
            Expanded(child: _buildChatContent()),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Container(
          width: 32,
          height: 32,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: glowColor.withValues(alpha: 0.6),
                blurRadius: 8,
                spreadRadius: 2,
              ),
            ],
          ),
          child: Icon(
            Icons.psychology,
            color: glowColor,
            size: 20,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'ASK $coachName',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontFamily: 'Pirulen',
                  fontWeight: FontWeight.bold,
                  shadows: [
                    Shadow(
                      color: glowColor.withValues(alpha: 0.8),
                      offset: const Offset(0, 0),
                      blurRadius: 10,
                    ),
                  ],
                ),
              ),
              Text(
                'Training continues in background',
                style: TextStyle(
                  color: glowColor.withValues(alpha: 0.7),
                  fontSize: 10,
                  fontFamily: 'Bitsumishi',
                  fontStyle: FontStyle.italic,
                ),
              ),
            ],
          ),
        ),
        IconButton(
          onPressed: onClose,
          icon: const Icon(Icons.close, color: Colors.white70, size: 20),
          padding: EdgeInsets.zero,
          constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
        ),
      ],
    );
  }

  Widget _buildChatContent() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.4),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: glowColor.withValues(alpha: 0.2), width: 1),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: CoachChatScreen(
          user: user,
          category: 'Health',
        ),
      ),
    );
  }
}

/// Compact coach chat button for quick access during training
class QuickCoachChatButton extends StatelessWidget {
  final User user;
  final String coachName;
  final Color glowColor;
  final VoidCallback? onPressed;

  const QuickCoachChatButton({
    super.key,
    required this.user,
    required this.coachName,
    required this.glowColor,
    this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: glowColor.withValues(alpha: 0.3),
            blurRadius: 8,
            spreadRadius: 1,
          ),
        ],
      ),
      child: ElevatedButton.icon(
        onPressed: onPressed,
        icon: Icon(
          Icons.psychology,
          size: 16,
          color: glowColor,
        ),
        label: Text(
          'ASK $coachName',
          style: TextStyle(
            fontFamily: 'Pirulen',
            fontSize: 11,
            fontWeight: FontWeight.bold,
            color: glowColor,
          ),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: glowColor.withValues(alpha: 0.15),
          foregroundColor: glowColor,
          side: BorderSide(color: glowColor.withValues(alpha: 0.5), width: 1),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          elevation: 0,
        ),
      ),
    );
  }
}

/// Training-specific coach suggestions
class TrainingCoachSuggestions extends StatelessWidget {
  final Color glowColor;
  final Function(String) onSuggestionTapped;

  const TrainingCoachSuggestions({
    super.key,
    required this.glowColor,
    required this.onSuggestionTapped,
  });

  static const List<String> _trainingSuggestions = [
    "What's the best warm-up for today's workout?",
    "How can I improve my form?",
    "Should I increase the intensity?",
    "What's a good cool-down routine?",
    "How do I prevent injury during training?",
    "What should I focus on today?",
  ];

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Quick Questions:',
            style: TextStyle(
              color: glowColor,
              fontSize: 12,
              fontFamily: 'Pirulen',
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 6,
            runSpacing: 6,
            children: _trainingSuggestions.map((suggestion) {
              return GestureDetector(
                onTap: () => onSuggestionTapped(suggestion),
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: glowColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: glowColor.withValues(alpha: 0.3), width: 1),
                  ),
                  child: Text(
                    suggestion,
                    style: TextStyle(
                      color: glowColor.withValues(alpha: 0.9),
                      fontSize: 10,
                      fontFamily: 'Bitsumishi',
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }
}
