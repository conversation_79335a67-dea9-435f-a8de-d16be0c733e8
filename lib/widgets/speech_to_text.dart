// 📁 lib/widgets/speech_to_text.dart

import 'package:flutter/material.dart';
import 'package:speech_to_text/speech_to_text.dart' as stt;

class SpeechToTextWidget extends StatefulWidget {
  final Function(String transcribedText) onResult;
  final Color glowColor;

  const SpeechToTextWidget({
    super.key,
    required this.onResult,
    this.glowColor = Colors.cyanAccent,
  });

  @override
  State<SpeechToTextWidget> createState() => _SpeechToTextWidgetState();
}

class _SpeechToTextWidgetState extends State<SpeechToTextWidget> {
  late stt.SpeechToText _speech;
  bool _isListening = false;
  String _currentText = "";

  @override
  void initState() {
    super.initState();
    _speech = stt.SpeechToText();
  }

  Future<void> _toggleListening() async {
    if (_isListening) {
      _speech.stop();
      setState(() => _isListening = false);
      widget.onResult(_currentText);
    } else {
      bool available = await _speech.initialize(
        onStatus: (status) {
          if (status == 'done') {
            setState(() => _isListening = false);
            widget.onResult(_currentText);
          }
        },
        onError: (error) => debugPrint("Speech error: $error"),
      );

      if (available) {
        setState(() {
          _isListening = true;
          _currentText = "";
        });

        _speech.listen(
          onResult: (val) {
            setState(() {
              _currentText = val.recognizedWords;
            });
          },
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _toggleListening,
      child: Container(
        width: 56,
        height: 56,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: Colors.black,
          border: Border.all(color: widget.glowColor, width: 2),
          boxShadow: [
            BoxShadow(
              color: widget.glowColor.withValues(alpha: _isListening ? 0.7 : 0.3),
              blurRadius: _isListening ? 16 : 6,
              spreadRadius: 1,
            )
          ],
        ),
        child: Icon(
          _isListening ? Icons.mic : Icons.mic_none,
          color: widget.glowColor,
        ),
      ),
    );
  }
}
