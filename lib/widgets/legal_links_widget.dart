// lib/widgets/legal_links_widget.dart

import 'package:flutter/material.dart';
import '../services/legal_document_service.dart';

/// Widget that displays legal document links in settings or about screens
class LegalLinksWidget extends StatelessWidget {
  final bool showAsButtons;
  final bool showIcons;
  final Color? textColor;
  final double fontSize;
  
  const LegalLinksWidget({
    super.key,
    this.showAsButtons = false,
    this.showIcons = true,
    this.textColor,
    this.fontSize = 14,
  });
  
  @override
  Widget build(BuildContext context) {
    final color = textColor ?? Colors.white;
    
    if (showAsButtons) {
      return Column(
        children: [
          _buildLegalButton(
            context,
            'Privacy Policy',
            Icons.privacy_tip,
            () => LegalDocumentService.showPrivacyPolicy(context),
            color,
          ),
          const SizedBox(height: 8),
          _buildLegalButton(
            context,
            'Terms of Service',
            Icons.description,
            () => LegalDocumentService.showTermsOfService(context),
            color,
          ),
        ],
      );
    }
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Legal',
          style: TextStyle(
            color: color,
            fontSize: fontSize + 2,
            fontWeight: FontWeight.bold,
            fontFamily: 'Pirulen',
          ),
        ),
        const SizedBox(height: 8),
        _buildLegalLink(
          context,
          'Privacy Policy',
          Icons.privacy_tip,
          () => LegalDocumentService.showPrivacyPolicy(context),
          color,
        ),
        const SizedBox(height: 4),
        _buildLegalLink(
          context,
          'Terms of Service',
          Icons.description,
          () => LegalDocumentService.showTermsOfService(context),
          color,
        ),
      ],
    );
  }
  
  Widget _buildLegalButton(
    BuildContext context,
    String title,
    IconData icon,
    VoidCallback onTap,
    Color color,
  ) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: onTap,
        icon: showIcons ? Icon(icon, size: 18) : const SizedBox.shrink(),
        label: Text(title),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.grey[800],
          foregroundColor: color,
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
    );
  }
  
  Widget _buildLegalLink(
    BuildContext context,
    String title,
    IconData icon,
    VoidCallback onTap,
    Color color,
  ) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 4),
        child: Row(
          children: [
            if (showIcons) ...[
              Icon(icon, size: 16, color: color.withValues(alpha: 0.7)),
              const SizedBox(width: 8),
            ],
            Text(
              title,
              style: TextStyle(
                color: color,
                fontSize: fontSize,
                decoration: TextDecoration.underline,
                fontFamily: 'Bitsumishi',
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Compact legal footer for bottom of screens
class LegalFooterWidget extends StatelessWidget {
  final Color? textColor;
  final double fontSize;
  
  const LegalFooterWidget({
    super.key,
    this.textColor,
    this.fontSize = 12,
  });
  
  @override
  Widget build(BuildContext context) {
    final color = textColor ?? Colors.grey;
    
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              InkWell(
                onTap: () => LegalDocumentService.showPrivacyPolicy(context),
                child: Text(
                  'Privacy Policy',
                  style: TextStyle(
                    color: color,
                    fontSize: fontSize,
                    decoration: TextDecoration.underline,
                  ),
                ),
              ),
              Text(
                ' • ',
                style: TextStyle(color: color, fontSize: fontSize),
              ),
              InkWell(
                onTap: () => LegalDocumentService.showTermsOfService(context),
                child: Text(
                  'Terms of Service',
                  style: TextStyle(
                    color: color,
                    fontSize: fontSize,
                    decoration: TextDecoration.underline,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            '© 2025 MXD. All rights reserved.',
            style: TextStyle(
              color: color,
              fontSize: fontSize - 1,
            ),
          ),
        ],
      ),
    );
  }
}

/// Settings section for legal documents
class LegalSettingsSection extends StatelessWidget {
  const LegalSettingsSection({super.key});
  
  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[700]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Legal & Privacy',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
              fontFamily: 'Pirulen',
            ),
          ),
          const SizedBox(height: 16),
          
          // Privacy Policy
          ListTile(
            leading: const Icon(Icons.privacy_tip, color: Colors.blue),
            title: const Text(
              'Privacy Policy',
              style: TextStyle(color: Colors.white, fontFamily: 'Bitsumishi'),
            ),
            subtitle: const Text(
              'How we collect, use, and protect your data',
              style: TextStyle(color: Colors.grey),
            ),
            trailing: const Icon(Icons.arrow_forward_ios, color: Colors.grey, size: 16),
            onTap: () => LegalDocumentService.showPrivacyPolicy(context),
          ),
          
          const Divider(color: Colors.grey),
          
          // Terms of Service
          ListTile(
            leading: const Icon(Icons.description, color: Colors.green),
            title: const Text(
              'Terms of Service',
              style: TextStyle(color: Colors.white, fontFamily: 'Bitsumishi'),
            ),
            subtitle: const Text(
              'Terms and conditions for using MXD',
              style: TextStyle(color: Colors.grey),
            ),
            trailing: const Icon(Icons.arrow_forward_ios, color: Colors.grey, size: 16),
            onTap: () => LegalDocumentService.showTermsOfService(context),
          ),
          
          const Divider(color: Colors.grey),
          
          // Data Usage Info
          ListTile(
            leading: const Icon(Icons.info_outline, color: Colors.orange),
            title: const Text(
              'Data Usage',
              style: TextStyle(color: Colors.white, fontFamily: 'Bitsumishi'),
            ),
            subtitle: const Text(
              'What data we collect and why',
              style: TextStyle(color: Colors.grey),
            ),
            trailing: const Icon(Icons.arrow_forward_ios, color: Colors.grey, size: 16),
            onTap: () => _showDataUsageInfo(context),
          ),
        ],
      ),
    );
  }
  
  void _showDataUsageInfo(BuildContext context) {
    final dataUsage = LegalDocumentService.getDataUsageDeclaration();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.black,
        title: const Text(
          'Data Usage Information',
          style: TextStyle(color: Colors.white, fontFamily: 'Pirulen'),
        ),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'We collect the following types of data:',
                style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 12),
              ...dataUsage['dataTypes'].map<Widget>((type) => Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: Text(
                  '• ${type['type']}: ${type['purposes'].join(', ')}',
                  style: const TextStyle(color: Colors.grey),
                ),
              )).toList(),
              const SizedBox(height: 16),
              const Text(
                'Third-party services:',
                style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 12),
              ...dataUsage['thirdPartyServices'].map<Widget>((service) => Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: Text(
                  '• ${service['name']}: ${service['purpose']}',
                  style: const TextStyle(color: Colors.grey),
                ),
              )).toList(),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close', style: TextStyle(color: Colors.blue)),
          ),
        ],
      ),
    );
  }
}
