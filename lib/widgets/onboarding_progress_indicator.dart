import 'package:flutter/material.dart';
import '../models/onboarding_progress.dart';

class OnboardingProgressIndicator extends StatelessWidget {
  final OnboardingProgress progress;
  final VoidCallback? onStepTap;

  const OnboardingProgressIndicator({
    super.key,
    required this.progress,
    this.onStepTap,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Progress bar
        ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: LinearProgressIndicator(
            value: progress.progressPercentage,
            backgroundColor: Colors.grey[800],
            valueColor: const AlwaysStoppedAnimation<Color>(Colors.cyanAccent),
            minHeight: 8,
          ),
        ),
        const SizedBox(height: 16),
        
        // Steps
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildStep(
              context,
              'Welcome',
              Icons.waving_hand,
              progress.hasCompletedWelcome,
              0,
            ),
            _buildStep(
              context,
              'North Star',
              Icons.star,
              progress.hasCreatedNorthStar,
              1,
            ),
            _buildStep(
              context,
              'Categories',
              Icons.category,
              progress.hasSetCategories,
              2,
            ),
            _buildStep(
              context,
              'Coaches',
              Icons.people,
              progress.hasMetCoaches,
              3,
            ),
            _buildStep(
              context,
              'Habits',
              Icons.check_circle,
              progress.hasSetHabits,
              4,
            ),
            _buildStep(
              context,
              'Tutorial',
              Icons.school,
              progress.hasCompletedTutorial,
              5,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStep(
    BuildContext context,
    String label,
    IconData icon,
    bool isCompleted,
    int stepIndex,
  ) {
    // Different colors for each step when completed
    final List<Color> stepColors = [
      Colors.blue,        // Welcome - Blue
      Colors.yellow,      // North Star - Yellow
      Colors.green,       // Categories - Green
      Colors.purple,      // Coaches - Purple
      Colors.orange,      // Habits - Orange
      Colors.cyan,        // Tutorial - Cyan
    ];

    final color = isCompleted ? stepColors[stepIndex] : Colors.grey[600]!;

    return GestureDetector(
      onTap: onStepTap,
      child: SizedBox(
        width: 44,
        height: 44,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.all(2), // Reduced from 4 to 2
              decoration: BoxDecoration(
                color: isCompleted ? color.withValues(alpha: 0.1) : Colors.transparent,
                borderRadius: BorderRadius.circular(12), // Reduced from 16 to 12
                border: Border.all(
                  color: color,
                  width: 1.5, // Reduced from 2 to 1.5
                ),
                // Add neon glow for completed steps
                boxShadow: isCompleted ? [
                  BoxShadow(
                    color: color.withValues(alpha: 0.6),
                    blurRadius: 15,
                    spreadRadius: 2,
                  ),
                  BoxShadow(
                    color: color.withValues(alpha: 0.4),
                    blurRadius: 20,
                    spreadRadius: 3,
                  ),
                ] : null,
              ),
              child: Icon(
                icon,
                color: color,
                size: 18, // Reduced from 20 to 18
              ),
            ),
            const SizedBox(height: 2), // Reduced from 4 to 2
            Flexible( // Added Flexible to prevent overflow
              child: Text(
                label,
                style: TextStyle(
                  color: color,
                  fontSize: 6,
                  fontFamily: 'Pirulen',
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }
} 