// lib/widgets/training_history_modal.dart

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:io';
import '../models/training_session_model.dart';
import '../services/training_storage_service.dart';
import '../services/comprehensive_logging_service.dart';
import '../services/training_diary_sync_service.dart';
import '../controller/user_controller2.dart';
import 'training_session_edit_modal.dart';

/// Modal for viewing training history with calendar and session management
class TrainingHistoryModal extends StatefulWidget {
  final Color glowColor;
  final VoidCallback onClose;
  final Function(TrainingSession)? onSessionSelected;

  const TrainingHistoryModal({
    super.key,
    required this.glowColor,
    required this.onClose,
    this.onSessionSelected,
  });

  @override
  State<TrainingHistoryModal> createState() => _TrainingHistoryModalState();
}

class _TrainingHistoryModalState extends State<TrainingHistoryModal> {
  final TrainingStorageService _storageService = TrainingStorageService();
  final TrainingDiarySyncService _syncService = TrainingDiarySyncService();

  List<TrainingSession> _allSessions = [];
  List<TrainingSession> _filteredSessions = [];
  DateTime _selectedMonth = DateTime.now();
  String _selectedFilter = 'All';
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadSessions();
  }

  Future<void> _loadSessions() async {
    try {
      setState(() => _isLoading = true);
      
      _allSessions = await _storageService.getAllSessions();
      _applyFilters();
      
      setState(() => _isLoading = false);
      
      await ComprehensiveLoggingService.logInfo('📂 TrainingHistory: Loaded ${_allSessions.length} sessions');
    } catch (e) {
      setState(() => _isLoading = false);
      await ComprehensiveLoggingService.logError('❌ TrainingHistory: Failed to load sessions: $e');
    }
  }

  void _applyFilters() {
    _filteredSessions = _allSessions.where((session) {
      // Month filter
      final sessionMonth = DateTime(session.createdAt.year, session.createdAt.month);
      final selectedMonth = DateTime(_selectedMonth.year, _selectedMonth.month);
      
      if (sessionMonth != selectedMonth) return false;
      
      // Label filter
      if (_selectedFilter != 'All' && session.label != _selectedFilter) return false;
      
      return true;
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.black.withValues(alpha: 0.95),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: widget.glowColor.withValues(alpha: 0.3), width: 2),
          boxShadow: [
            BoxShadow(
              color: widget.glowColor.withValues(alpha: 0.2),
              blurRadius: 20,
              spreadRadius: 4,
            ),
          ],
        ),
        child: Column(
          children: [
            _buildHeader(),
            const SizedBox(height: 16),
            _buildFilters(),
            const SizedBox(height: 16),
            Expanded(child: _buildSessionsList()),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Icon(
          Icons.history,
          color: widget.glowColor,
          size: 24,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            'TRAINING HISTORY',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontFamily: 'Pirulen',
              fontWeight: FontWeight.bold,
              shadows: [
                Shadow(
                  color: widget.glowColor.withValues(alpha: 0.8),
                  offset: const Offset(0, 0),
                  blurRadius: 10,
                ),
              ],
            ),
          ),
        ),
        IconButton(
          onPressed: widget.onClose,
          icon: const Icon(Icons.close, color: Colors.white70),
        ),
      ],
    );
  }

  Widget _buildFilters() {
    final uniqueLabels = _allSessions.map((s) => s.label).toSet().toList()..sort();
    
    return Column(
      children: [
        // Month navigation
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            IconButton(
              onPressed: () {
                setState(() {
                  _selectedMonth = DateTime(_selectedMonth.year, _selectedMonth.month - 1);
                  _applyFilters();
                });
              },
              icon: Icon(Icons.chevron_left, color: widget.glowColor),
            ),
            Text(
              '${_getMonthName(_selectedMonth.month)} ${_selectedMonth.year}',
              style: TextStyle(
                color: widget.glowColor,
                fontSize: 16,
                fontFamily: 'Pirulen',
                fontWeight: FontWeight.bold,
              ),
            ),
            IconButton(
              onPressed: () {
                setState(() {
                  _selectedMonth = DateTime(_selectedMonth.year, _selectedMonth.month + 1);
                  _applyFilters();
                });
              },
              icon: Icon(Icons.chevron_right, color: widget.glowColor),
            ),
          ],
        ),
        const SizedBox(height: 12),
        
        // Label filter
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            children: [
              _buildFilterChip('All'),
              ...uniqueLabels.map((label) => _buildFilterChip(label)),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildFilterChip(String label) {
    final isSelected = _selectedFilter == label;
    
    return Container(
      margin: const EdgeInsets.only(right: 8),
      child: FilterChip(
        label: Text(
          label,
          style: TextStyle(
            color: isSelected ? Colors.black : Colors.white70,
            fontSize: 12,
            fontFamily: 'Bitsumishi',
            fontWeight: FontWeight.bold,
          ),
        ),
        selected: isSelected,
        onSelected: (selected) {
          setState(() {
            _selectedFilter = label;
            _applyFilters();
          });
        },
        backgroundColor: Colors.black.withValues(alpha: 0.4),
        selectedColor: widget.glowColor,
        side: BorderSide(
          color: isSelected ? widget.glowColor : Colors.white30,
          width: 1,
        ),
      ),
    );
  }

  Widget _buildSessionsList() {
    if (_isLoading) {
      return Center(
        child: CircularProgressIndicator(color: widget.glowColor),
      );
    }
    
    if (_filteredSessions.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.fitness_center,
              color: Colors.white30,
              size: 48,
            ),
            const SizedBox(height: 16),
            Text(
              'No training sessions found',
              style: TextStyle(
                color: Colors.white30,
                fontSize: 16,
                fontFamily: 'Bitsumishi',
              ),
            ),
            Text(
              'for ${_getMonthName(_selectedMonth.month)} ${_selectedMonth.year}',
              style: TextStyle(
                color: Colors.white30,
                fontSize: 14,
                fontFamily: 'Bitsumishi',
              ),
            ),
          ],
        ),
      );
    }
    
    return ListView.builder(
      itemCount: _filteredSessions.length,
      itemBuilder: (context, index) {
        final session = _filteredSessions[index];
        return _buildSessionTile(session);
      },
    );
  }

  Widget _buildSessionTile(TrainingSession session) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.4),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withValues(alpha: 0.1), width: 1),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header row with title and date
            Row(
              children: [
                // Title - takes most space
                Expanded(
                  child: Text(
                    session.label,
                    style: TextStyle(
                      color: widget.glowColor,
                      fontSize: 16,
                      fontFamily: 'Pirulen',
                      fontWeight: FontWeight.bold,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                const SizedBox(width: 8),

                // Date
                Text(
                  session.formattedDate,
                  style: const TextStyle(
                    color: Colors.white70,
                    fontSize: 12,
                    fontFamily: 'Digital-7',
                  ),
                ),
              ],
            ),
            const SizedBox(height: 4),

            // Action buttons row
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                IconButton(
                  onPressed: () {
                    if (widget.onSessionSelected != null) {
                      widget.onSessionSelected!(session);
                      widget.onClose();
                    }
                  },
                  icon: Icon(
                    Icons.compare_arrows,
                    color: widget.glowColor.withValues(alpha: 0.7),
                    size: 16,
                  ),
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(
                    minWidth: 24,
                    minHeight: 24,
                  ),
                ),
                const SizedBox(width: 8),
                PopupMenuButton<String>(
                  icon: Icon(Icons.more_vert, color: Colors.white70, size: 16),
                  onSelected: (value) => _handleSessionAction(value, session),
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(
                    minWidth: 24,
                    minHeight: 24,
                  ),
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'edit',
                      child: Text('Edit Session'),
                    ),
                    const PopupMenuItem(
                      value: 'delete',
                      child: Text('Delete Session'),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 8),

            // Session details - wrap to prevent overflow
            Wrap(
              spacing: 16,
              runSpacing: 8,
              children: [
                // Duration
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(Icons.timer, color: Colors.white70, size: 16),
                    const SizedBox(width: 4),
                    Text(
                      session.formattedDuration,
                      style: const TextStyle(
                        color: Colors.white70,
                        fontSize: 14,
                        fontFamily: 'Digital-7',
                      ),
                    ),
                  ],
                ),

                // EXP
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(Icons.star, color: widget.glowColor, size: 16),
                    const SizedBox(width: 4),
                    Text(
                      '${session.expEarned} EXP',
                      style: TextStyle(
                        color: widget.glowColor,
                        fontSize: 14,
                        fontFamily: 'Digital-7',
                      ),
                    ),
                  ],
                ),

                // Bodyweight (if available)
                if (session.bodyweightKg != null)
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.monitor_weight, color: Colors.white70, size: 16),
                      const SizedBox(width: 4),
                      Text(
                        '${session.bodyweightKg!.toStringAsFixed(1)} kg',
                        style: const TextStyle(
                          color: Colors.white70,
                          fontSize: 14,
                          fontFamily: 'Digital-7',
                        ),
                      ),
                    ],
                  ),
              ],
            ),

            // Current Goal (if available)
            if (session.currentGoal.isNotEmpty) ...[
              const SizedBox(height: 8),
              Row(
                children: [
                  Icon(Icons.flag, color: widget.glowColor.withValues(alpha: 0.7), size: 16),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      session.currentGoal,
                      style: TextStyle(
                        color: widget.glowColor.withValues(alpha: 0.8),
                        fontSize: 12,
                        fontFamily: 'Bitsumishi',
                        fontStyle: FontStyle.italic,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ],

            // Notes (if available)
            if (session.notes.isNotEmpty) ...[
              const SizedBox(height: 8),
              SizedBox(
                width: double.infinity,
                child: SelectableText(
                  session.notes,
                  style: const TextStyle(
                    color: Colors.white60,
                    fontSize: 12,
                    fontFamily: 'Bitsumishi',
                  ),
                  maxLines: 3,
                ),
              ),
            ],

            // Image (if available)
            if (session.imagePath != null && session.imagePath!.isNotEmpty) ...[
              const SizedBox(height: 8),
              GestureDetector(
                onTap: () => _showImageModal(session.imagePath!),
                child: Container(
                  height: 120,
                  width: double.infinity,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: widget.glowColor.withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Image.file(
                      File(session.imagePath!),
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          color: Colors.grey.withValues(alpha: 0.2),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.broken_image,
                                color: Colors.grey,
                                size: 32,
                              ),
                              const SizedBox(height: 4),
                              Text(
                                'Image not found',
                                style: TextStyle(
                                  color: Colors.grey,
                                  fontSize: 12,
                                  fontFamily: 'Bitsumishi',
                                ),
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _handleSessionAction(String action, TrainingSession session) {
    switch (action) {
      case 'edit':
        _showEditSessionModal(session);
        break;
      case 'delete':
        _confirmDeleteSession(session);
        break;
    }
  }

  void _showEditSessionModal(TrainingSession session) {
    showDialog(
      context: context,
      builder: (context) => TrainingSessionEditModal(
        session: session,
        glowColor: widget.glowColor,
        onSave: (updatedSession) async {
          // Capture context and user controller before async operations
          final messenger = ScaffoldMessenger.of(context);
          final userController = context.read<UserController2>();

          // Update the session in storage
          final success = await _storageService.updateSession(updatedSession);
          if (success) {
            // Sync diary entry with updated session data
            final syncResult = await _syncService.updateDiaryEntryForEditedSession(
              userController: userController,
              originalSession: session,
              updatedSession: updatedSession,
            );

            // Refresh the sessions list
            await _loadSessions();

            if (mounted) {
              if (syncResult != null) {
                messenger.showSnackBar(
                  const SnackBar(content: Text('Session and diary updated successfully!')),
                );
              } else {
                messenger.showSnackBar(
                  const SnackBar(content: Text('Session updated, but diary sync failed')),
                );
              }
            }
          } else {
            if (mounted) {
              messenger.showSnackBar(
                const SnackBar(content: Text('Failed to update session')),
              );
            }
          }
        },
        onClose: () => Navigator.of(context).pop(),
      ),
    );
  }

  void _confirmDeleteSession(TrainingSession session) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.black.withValues(alpha: 0.9),
        title: const Text('Delete Session', style: TextStyle(color: Colors.white)),
        content: Text(
          'Are you sure you want to delete "${session.label}" from ${session.formattedDate}?',
          style: const TextStyle(color: Colors.white70),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _deleteSession(session);
            },
            child: const Text('Delete', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteSession(TrainingSession session) async {
    try {
      // Capture context and user controller before async operations
      final messenger = ScaffoldMessenger.of(context);
      final userController = context.read<UserController2>();

      // Delete the session from storage
      final success = await _storageService.deleteSession(session.id);
      if (success) {
        // Sync diary entry removal
        final syncResult = await _syncService.removeDiaryEntryForDeletedSession(
          userController: userController,
          deletedSession: session,
        );

        // Refresh the list
        await _loadSessions();

        if (mounted) {
          if (syncResult != null) {
            messenger.showSnackBar(
              SnackBar(
                content: Text('Session "${session.label}" and diary entry deleted'),
                backgroundColor: widget.glowColor,
              ),
            );
          } else {
            messenger.showSnackBar(
              SnackBar(
                content: Text('Session "${session.label}" deleted, diary sync failed'),
                backgroundColor: widget.glowColor,
              ),
            );
          }
        }
      } else {
        if (mounted) {
          messenger.showSnackBar(
            const SnackBar(
              content: Text('Failed to delete session'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to delete session'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  String _getMonthName(int month) {
    const months = [
      '', 'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];
    return months[month];
  }

  /// Show image in full screen modal
  void _showImageModal(String imagePath) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) => Dialog(
        backgroundColor: Colors.black.withValues(alpha: 0.9),
        child: Stack(
          children: [
            Center(
              child: InteractiveViewer(
                child: Image.file(
                  File(imagePath),
                  fit: BoxFit.contain,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      padding: const EdgeInsets.all(32),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.broken_image,
                            color: Colors.grey,
                            size: 64,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Image not found',
                            style: TextStyle(
                              color: Colors.grey,
                              fontSize: 16,
                              fontFamily: 'Bitsumishi',
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'The image file may have been moved or deleted.',
                            style: TextStyle(
                              color: Colors.grey.withValues(alpha: 0.7),
                              fontSize: 12,
                              fontFamily: 'Bitsumishi',
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
            ),
            Positioned(
              top: 16,
              right: 16,
              child: IconButton(
                onPressed: () => Navigator.of(context).pop(),
                icon: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.7),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.close,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
