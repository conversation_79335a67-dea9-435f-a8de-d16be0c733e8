// lib/widgets/thinking_visualization_widget.dart

import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../services/superintelligent_thinking_service.dart';

/// Advanced thinking visualization widget for superintelligent AI coaches
/// 
/// Displays personality-specific animations while coaches process responses
class ThinkingVisualizationWidget extends StatefulWidget {
  final Stream<ThinkingState> thinkingStream;
  final Color primaryColor;
  final String coachCategory;
  
  const ThinkingVisualizationWidget({
    super.key,
    required this.thinkingStream,
    required this.primaryColor,
    required this.coachCategory,
  });
  
  @override
  State<ThinkingVisualizationWidget> createState() => _ThinkingVisualizationWidgetState();
}

class _ThinkingVisualizationWidgetState extends State<ThinkingVisualizationWidget>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _rotationController;
  late AnimationController _scaleController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _rotationAnimation;
  late Animation<double> _scaleAnimation;
  
  ThinkingState? _currentState;
  
  @override
  void initState() {
    super.initState();
    
    // Initialize animation controllers
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _rotationController = AnimationController(
      duration: const Duration(milliseconds: 3000),
      vsync: this,
    );
    
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    // Initialize animations
    _pulseAnimation = Tween<double>(begin: 0.8, end: 1.2).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );
    
    _rotationAnimation = Tween<double>(begin: 0, end: 2 * math.pi).animate(
      CurvedAnimation(parent: _rotationController, curve: Curves.linear),
    );
    
    _scaleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _scaleController, curve: Curves.elasticOut),
    );
    
    // Start animations
    _pulseController.repeat(reverse: true);
    _rotationController.repeat();
    _scaleController.forward();
    
    // Listen to thinking stream
    widget.thinkingStream.listen((state) {
      if (mounted) {
        setState(() {
          _currentState = state;
        });
        
        if (state.isCompleted) {
          _stopAnimations();
        } else {
          _updateAnimationsForState(state);
        }
      }
    });
  }
  
  @override
  void dispose() {
    _pulseController.dispose();
    _rotationController.dispose();
    _scaleController.dispose();
    super.dispose();
  }
  
  void _updateAnimationsForState(ThinkingState state) {
    switch (state.type) {
      case ThinkingType.deepAnalysis:
        _pulseController.duration = const Duration(milliseconds: 1000);
        _rotationController.duration = const Duration(milliseconds: 2000);
        break;
      case ThinkingType.crossReference:
        _pulseController.duration = const Duration(milliseconds: 1200);
        _rotationController.duration = const Duration(milliseconds: 2500);
        break;
      case ThinkingType.synthesizing:
        _pulseController.duration = const Duration(milliseconds: 1800);
        _rotationController.duration = const Duration(milliseconds: 3500);
        break;
      default:
        _pulseController.duration = const Duration(milliseconds: 1500);
        _rotationController.duration = const Duration(milliseconds: 3000);
    }
  }
  
  void _stopAnimations() {
    _pulseController.stop();
    _rotationController.stop();
    _scaleController.animateTo(0.0);
  }
  
  @override
  Widget build(BuildContext context) {
    if (_currentState == null) {
      return const SizedBox.shrink();
    }
    
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[850],
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: widget.primaryColor.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          // Animated thinking indicator
          AnimatedBuilder(
            animation: Listenable.merge([_pulseAnimation, _rotationAnimation, _scaleAnimation]),
            builder: (context, child) {
              return Transform.scale(
                scale: _scaleAnimation.value,
                child: Transform.rotate(
                  angle: _rotationAnimation.value,
                  child: Transform.scale(
                    scale: _pulseAnimation.value,
                    child: _buildThinkingIcon(),
                  ),
                ),
              );
            },
          ),
          const SizedBox(width: 16),
          
          // Thinking message
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _currentState!.coachName,
                  style: TextStyle(
                    color: widget.primaryColor,
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                    fontFamily: 'Bitsumishi',
                  ),
                ),
                const SizedBox(height: 4),
                AnimatedSwitcher(
                  duration: const Duration(milliseconds: 300),
                  child: Text(
                    _currentState!.message,
                    key: ValueKey(_currentState!.message),
                    style: const TextStyle(
                      color: Colors.white70,
                      fontSize: 13,
                      fontFamily: 'Bitsumishi',
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildThinkingIcon() {
    switch (widget.coachCategory.toLowerCase()) {
      case 'health':
        return _buildHealthThinkingIcon();
      case 'wealth':
        return _buildWealthThinkingIcon();
      case 'purpose':
        return _buildPurposeThinkingIcon();
      case 'connection':
        return _buildConnectionThinkingIcon();
      default:
        return _buildDefaultThinkingIcon();
    }
  }
  
  Widget _buildHealthThinkingIcon() {
    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: RadialGradient(
          colors: [
            widget.primaryColor.withValues(alpha: 0.8),
            widget.primaryColor.withValues(alpha: 0.3),
          ],
        ),
      ),
      child: const Icon(
        Icons.flash_on,
        color: Colors.white,
        size: 24,
      ),
    );
  }
  
  Widget _buildWealthThinkingIcon() {
    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: RadialGradient(
          colors: [
            widget.primaryColor.withValues(alpha: 0.8),
            widget.primaryColor.withValues(alpha: 0.3),
          ],
        ),
      ),
      child: const Icon(
        Icons.diamond,
        color: Colors.white,
        size: 24,
      ),
    );
  }
  
  Widget _buildPurposeThinkingIcon() {
    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: RadialGradient(
          colors: [
            widget.primaryColor.withValues(alpha: 0.8),
            widget.primaryColor.withValues(alpha: 0.3),
          ],
        ),
      ),
      child: const Icon(
        Icons.auto_awesome,
        color: Colors.white,
        size: 24,
      ),
    );
  }
  
  Widget _buildConnectionThinkingIcon() {
    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: RadialGradient(
          colors: [
            widget.primaryColor.withValues(alpha: 0.8),
            widget.primaryColor.withValues(alpha: 0.3),
          ],
        ),
      ),
      child: const Icon(
        Icons.favorite,
        color: Colors.white,
        size: 24,
      ),
    );
  }
  
  Widget _buildDefaultThinkingIcon() {
    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: RadialGradient(
          colors: [
            widget.primaryColor.withValues(alpha: 0.8),
            widget.primaryColor.withValues(alpha: 0.3),
          ],
        ),
      ),
      child: const Icon(
        Icons.psychology,
        color: Colors.white,
        size: 24,
      ),
    );
  }
}

/// Simple thinking indicator for basic loading states
class SimpleThinkingIndicator extends StatefulWidget {
  final String coachName;
  final Color primaryColor;
  
  const SimpleThinkingIndicator({
    super.key,
    required this.coachName,
    required this.primaryColor,
  });
  
  @override
  State<SimpleThinkingIndicator> createState() => _SimpleThinkingIndicatorState();
}

class _SimpleThinkingIndicatorState extends State<SimpleThinkingIndicator>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;
  
  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _animation = Tween<double>(begin: 0.5, end: 1.0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );
    _controller.repeat(reverse: true);
  }
  
  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[850],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: widget.primaryColor.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          AnimatedBuilder(
            animation: _animation,
            builder: (context, child) {
              return Opacity(
                opacity: _animation.value,
                child: Icon(
                  Icons.psychology,
                  color: widget.primaryColor,
                  size: 20,
                ),
              );
            },
          ),
          const SizedBox(width: 12),
          Text(
            '*${widget.coachName} is thinking...*',
            style: const TextStyle(
              color: Colors.white70,
              fontSize: 13,
              fontStyle: FontStyle.italic,
              fontFamily: 'Bitsumishi',
            ),
          ),
        ],
      ),
    );
  }
}
