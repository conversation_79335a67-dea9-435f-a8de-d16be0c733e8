// 📁 lib/widgets/common_error_dialogs.dart

import 'package:flutter/material.dart';
import '../theme/colors.dart';

/// Consolidated error dialog system for consistent error handling across the app.
/// 
/// Replaces duplicate error dialog implementations in signup_error_dialogs.dart
/// and signin_error_dialogs.dart with a unified, reusable system.
class CommonErrorDialogs {
  
  /// Show connection error dialog
  static Future<void> showConnectionError(
    BuildContext context, {
    VoidCallback? onRetry,
    VoidCallback? onSecondaryAction,
    String? secondaryActionLabel,
  }) async {
    return _showErrorDialog(
      context,
      title: 'Connection Failed',
      message: 'Unable to connect to MXD servers. Please check your internet connection and try again.',
      icon: Icons.wifi_off_outlined,
      iconColor: MolColors.red,
      primaryAction: _DialogAction(
        label: 'Retry',
        onPressed: () {
          Navigator.of(context).pop();
          onRetry?.call();
        },
        color: MolColors.red,
      ),
      secondaryAction: onSecondaryAction != null ? _DialogAction(
        label: secondaryActionLabel ?? 'Continue Offline',
        onPressed: () {
          Navigator.of(context).pop();
          onSecondaryAction.call();
        },
        color: MolColors.blue,
      ) : null,
    );
  }

  /// Show email already exists error
  static Future<void> showEmailExistsError(
    BuildContext context, {
    required String email,
    VoidCallback? onTryDifferentEmail,
    VoidCallback? onSignIn,
  }) async {
    return _showErrorDialog(
      context,
      title: 'Email Already Registered',
      message: 'The email "$email" is already registered with MXD.',
      icon: Icons.email_outlined,
      iconColor: MolColors.purple,
      primaryAction: _DialogAction(
        label: 'Try Different Email',
        onPressed: () {
          Navigator.of(context).pop();
          onTryDifferentEmail?.call();
        },
        color: MolColors.purple,
      ),
      secondaryAction: onSignIn != null ? _DialogAction(
        label: 'Sign In Instead',
        onPressed: () {
          Navigator.of(context).pop();
          onSignIn.call();
        },
        color: MolColors.blue,
      ) : null,
    );
  }

  /// Show servers offline error
  static Future<void> showServersOfflineError(
    BuildContext context, {
    VoidCallback? onRetry,
    VoidCallback? onTryLater,
  }) async {
    return _showErrorDialog(
      context,
      title: 'Servers Offline',
      message: 'Our servers are temporarily unavailable. Please try again soon.',
      icon: Icons.cloud_off_outlined,
      iconColor: MolColors.orange,
      primaryAction: _DialogAction(
        label: 'Try Again',
        onPressed: () {
          Navigator.of(context).pop();
          onRetry?.call();
        },
        color: MolColors.orange,
      ),
      secondaryAction: onTryLater != null ? _DialogAction(
        label: 'Try Later',
        onPressed: () {
          Navigator.of(context).pop();
          onTryLater.call();
        },
        color: MolColors.gray,
      ) : null,
    );
  }

  /// Show storage error
  static Future<void> showStorageError(
    BuildContext context, {
    VoidCallback? onRetry,
    VoidCallback? onContactSupport,
  }) async {
    return _showErrorDialog(
      context,
      title: 'Storage Error',
      message: 'Unable to save your account data. This may be a device storage issue.',
      icon: Icons.storage_outlined,
      iconColor: MolColors.yellow,
      primaryAction: _DialogAction(
        label: 'Try Again',
        onPressed: () {
          Navigator.of(context).pop();
          onRetry?.call();
        },
        color: MolColors.yellow,
      ),
      secondaryAction: onContactSupport != null ? _DialogAction(
        label: 'Contact Support',
        onPressed: () {
          Navigator.of(context).pop();
          onContactSupport.call();
        },
        color: MolColors.purple,
      ) : null,
    );
  }

  /// Show email verification required error
  static Future<void> showEmailVerificationRequired(
    BuildContext context, {
    required String email,
    VoidCallback? onResendVerification,
    VoidCallback? onChangeEmail,
  }) async {
    return _showErrorDialog(
      context,
      title: 'Please Verify Your Email',
      message: 'Please verify your email to unlock your MXD Out Life coaches',
      icon: Icons.mark_email_unread_outlined,
      iconColor: MolColors.blue,
      primaryAction: _DialogAction(
        label: 'Resend Verification',
        onPressed: () {
          Navigator.of(context).pop();
          onResendVerification?.call();
        },
        color: MolColors.blue,
      ),
      secondaryAction: onChangeEmail != null ? _DialogAction(
        label: 'Change Email',
        onPressed: () {
          Navigator.of(context).pop();
          onChangeEmail.call();
        },
        color: MolColors.purple,
      ) : null,
    );
  }

  /// Show generic error with custom message
  static Future<void> showGenericError(
    BuildContext context, {
    required String title,
    required String message,
    IconData? icon,
    Color? iconColor,
    VoidCallback? onRetry,
    VoidCallback? onSecondaryAction,
    String? secondaryActionLabel,
  }) async {
    return _showErrorDialog(
      context,
      title: title,
      message: message,
      icon: icon ?? Icons.error_outline,
      iconColor: iconColor ?? MolColors.red,
      primaryAction: _DialogAction(
        label: 'OK',
        onPressed: () {
          Navigator.of(context).pop();
          onRetry?.call();
        },
        color: iconColor ?? MolColors.red,
      ),
      secondaryAction: onSecondaryAction != null ? _DialogAction(
        label: secondaryActionLabel ?? 'Cancel',
        onPressed: () {
          Navigator.of(context).pop();
          onSecondaryAction.call();
        },
        color: MolColors.gray,
      ) : null,
    );
  }

  /// Internal method to show error dialog with consistent styling
  static Future<void> _showErrorDialog(
    BuildContext context, {
    required String title,
    required String message,
    required IconData icon,
    required Color iconColor,
    required _DialogAction primaryAction,
    _DialogAction? secondaryAction,
  }) async {
    return showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => _CommonErrorDialog(
        title: title,
        message: message,
        icon: icon,
        iconColor: iconColor,
        primaryAction: primaryAction,
        secondaryAction: secondaryAction,
      ),
    );
  }
}

/// Internal dialog action model
class _DialogAction {
  final String label;
  final VoidCallback onPressed;
  final Color color;

  _DialogAction({
    required this.label,
    required this.onPressed,
    required this.color,
  });
}

/// Internal error dialog widget with consistent MXD styling
class _CommonErrorDialog extends StatelessWidget {
  final String title;
  final String message;
  final IconData icon;
  final Color iconColor;
  final _DialogAction primaryAction;
  final _DialogAction? secondaryAction;

  const _CommonErrorDialog({
    required this.title,
    required this.message,
    required this.icon,
    required this.iconColor,
    required this.primaryAction,
    this.secondaryAction,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.95),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: iconColor.withValues(alpha: 0.3), width: 1),
          boxShadow: [
            BoxShadow(
              color: iconColor.withValues(alpha: 0.2),
              blurRadius: 20,
              spreadRadius: 2,
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Icon
            Container(
              width: 64,
              height: 64,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: iconColor.withValues(alpha: 0.1),
                border: Border.all(color: iconColor.withValues(alpha: 0.3), width: 2),
              ),
              child: Icon(icon, color: iconColor, size: 32),
            ),
            const SizedBox(height: 16),
            
            // Title
            Text(
              title,
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontFamily: 'Pirulen',
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
            
            // Message
            Text(
              message,
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 14,
                fontFamily: 'Bitsumishi',
                height: 1.4,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            
            // Actions
            Row(
              children: [
                if (secondaryAction != null) ...[
                  Expanded(
                    child: _buildActionButton(secondaryAction!),
                  ),
                  const SizedBox(width: 12),
                ],
                Expanded(
                  child: _buildActionButton(primaryAction),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton(_DialogAction action) {
    return ElevatedButton(
      onPressed: action.onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: action.color,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        elevation: 0,
      ),
      child: Text(
        action.label,
        style: const TextStyle(
          fontFamily: 'Bitsumishi',
          fontSize: 14,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }
}
