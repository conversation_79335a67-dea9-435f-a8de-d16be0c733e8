import 'package:flutter/material.dart';
import '../theme/colors.dart';

/// 🚨 Signup Error Dialog System
/// 
/// Provides user-friendly, actionable error dialogs with specific messages
/// for each failure scenario. All dialogs follow MOL visual consistency
/// with neon glow, rainbow theme, and retro-futurism styling.
/// 
/// Features:
/// - Specific error messages for each scenario
/// - Actionable guidance for users
/// - Consistent MOL visual styling
/// - Retry and alternative action buttons
/// - Progress indicators for retry attempts
class SignupErrorDialogs {
  
  /// Show email already exists error
  static Future<void> showEmailExistsError(
    BuildContext context, {
    required String email,
    VoidCallback? onTryDifferentEmail,
    VoidCallback? onSignIn,
  }) async {
    return showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => _SignupErrorDialog(
        title: 'Email Already Registered',
        message: 'The email "$email" is already registered with MXD.',
        icon: Icons.email_outlined,
        iconColor: MolColors.purple,
        primaryAction: _DialogAction(
          label: 'Try Different Email',
          onPressed: () {
            Navigator.of(context).pop();
            onTryDifferentEmail?.call();
          },
          color: MolColors.purple,
        ),
        secondaryAction: _DialogAction(
          label: 'Sign In Instead',
          onPressed: () {
            Navigator.of(context).pop();
            onSignIn?.call();
          },
          color: MolColors.blue,
        ),
      ),
    );
  }

  /// Show signup servers offline error
  static Future<void> showServersOfflineError(
    BuildContext context, {
    VoidCallback? onRetry,
    VoidCallback? onTryLater,
  }) async {
    return showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => _SignupErrorDialog(
        title: 'Signup Servers Offline',
        message: 'Our signup servers are temporarily unavailable. Please try again soon.',
        icon: Icons.cloud_off_outlined,
        iconColor: MolColors.orange,
        primaryAction: _DialogAction(
          label: 'Try Again',
          onPressed: () {
            Navigator.of(context).pop();
            onRetry?.call();
          },
          color: MolColors.orange,
        ),
        secondaryAction: _DialogAction(
          label: 'Try Later',
          onPressed: () {
            Navigator.of(context).pop();
            onTryLater?.call();
          },
          color: MolColors.gray,
        ),
      ),
    );
  }

  /// Show username taken error
  static Future<void> showUsernameTakenError(
    BuildContext context, {
    required String username,
    required List<String> suggestions,
    required Function(String) onSelectUsername,
    VoidCallback? onTryDifferent,
  }) async {
    return showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => _UsernameSuggestionsDialog(
        username: username,
        suggestions: suggestions,
        onSelectUsername: onSelectUsername,
        onTryDifferent: onTryDifferent,
      ),
    );
  }

  /// Show connection failed error
  static Future<void> showConnectionError(
    BuildContext context, {
    VoidCallback? onRetry,
    VoidCallback? onCheckConnection,
  }) async {
    return showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => _SignupErrorDialog(
        title: 'Connection Failed',
        message: 'Unable to connect to MXD servers. Please check your internet connection and try again.',
        icon: Icons.wifi_off_outlined,
        iconColor: MolColors.red,
        primaryAction: _DialogAction(
          label: 'Retry',
          onPressed: () {
            Navigator.of(context).pop();
            onRetry?.call();
          },
          color: MolColors.red,
        ),
        secondaryAction: _DialogAction(
          label: 'Check Connection',
          onPressed: () {
            Navigator.of(context).pop();
            onCheckConnection?.call();
          },
          color: MolColors.blue,
        ),
      ),
    );
  }

  /// Show storage error
  static Future<void> showStorageError(
    BuildContext context, {
    VoidCallback? onRetry,
    VoidCallback? onContactSupport,
  }) async {
    return showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => _SignupErrorDialog(
        title: 'Storage Error',
        message: 'Unable to save your account data. This may be a device storage issue.',
        icon: Icons.storage_outlined,
        iconColor: MolColors.yellow,
        primaryAction: _DialogAction(
          label: 'Try Again',
          onPressed: () {
            Navigator.of(context).pop();
            onRetry?.call();
          },
          color: MolColors.yellow,
        ),
        secondaryAction: _DialogAction(
          label: 'Contact Support',
          onPressed: () {
            Navigator.of(context).pop();
            onContactSupport?.call();
          },
          color: MolColors.purple,
        ),
      ),
    );
  }

  /// Show email verification required error
  static Future<void> showEmailVerificationRequired(
    BuildContext context, {
    required String email,
    VoidCallback? onResendVerification,
    VoidCallback? onChangeEmail,
  }) async {
    return showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => _SignupErrorDialog(
        title: 'Please Verify Your Email',
        message: 'Please verify your email to unlock your MXD Out Life coaches',
        icon: Icons.mark_email_unread_outlined,
        iconColor: MolColors.blue,
        primaryAction: _DialogAction(
          label: 'Resend Verification',
          onPressed: () {
            Navigator.of(context).pop();
            onResendVerification?.call();
          },
          color: MolColors.blue,
        ),
        secondaryAction: _DialogAction(
          label: 'Change Email',
          onPressed: () {
            Navigator.of(context).pop();
            onChangeEmail?.call();
          },
          color: MolColors.purple,
        ),
      ),
    );
  }

  /// Show retry progress dialog
  static Future<void> showRetryProgress(
    BuildContext context, {
    required String operation,
    required int currentAttempt,
    required int maxAttempts,
  }) async {
    return showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => _RetryProgressDialog(
        operation: operation,
        currentAttempt: currentAttempt,
        maxAttempts: maxAttempts,
      ),
    );
  }
}

/// Internal dialog action model
class _DialogAction {
  final String label;
  final VoidCallback onPressed;
  final Color color;

  _DialogAction({
    required this.label,
    required this.onPressed,
    required this.color,
  });
}

/// Base error dialog widget with MOL styling
class _SignupErrorDialog extends StatelessWidget {
  final String title;
  final String message;
  final IconData icon;
  final Color iconColor;
  final _DialogAction primaryAction;
  final _DialogAction? secondaryAction;

  const _SignupErrorDialog({
    required this.title,
    required this.message,
    required this.icon,
    required this.iconColor,
    required this.primaryAction,
    this.secondaryAction,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.9),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: iconColor.withValues(alpha: 0.5),
            width: 2,
          ),
          boxShadow: [
            BoxShadow(
              color: iconColor.withValues(alpha: 0.3),
              blurRadius: 20,
              spreadRadius: 2,
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Error icon with glow
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: iconColor.withValues(alpha: 0.1),
                border: Border.all(
                  color: iconColor.withValues(alpha: 0.5),
                  width: 2,
                ),
                boxShadow: [
                  BoxShadow(
                    color: iconColor.withValues(alpha: 0.3),
                    blurRadius: 10,
                    spreadRadius: 1,
                  ),
                ],
              ),
              child: Icon(
                icon,
                size: 48,
                color: iconColor,
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Title
            Text(
              title,
              style: const TextStyle(
                fontFamily: 'Pirulen',
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
            
            const SizedBox(height: 16),
            
            // Message
            Text(
              message,
              style: const TextStyle(
                fontFamily: 'Bitsumishi',
                fontSize: 16,
                color: Colors.white70,
              ),
              textAlign: TextAlign.center,
            ),
            
            const SizedBox(height: 32),
            
            // Action buttons
            Row(
              children: [
                if (secondaryAction != null) ...[
                  Expanded(
                    child: _buildActionButton(
                      secondaryAction!,
                      isPrimary: false,
                    ),
                  ),
                  const SizedBox(width: 16),
                ],
                Expanded(
                  child: _buildActionButton(
                    primaryAction,
                    isPrimary: true,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton(_DialogAction action, {required bool isPrimary}) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        boxShadow: isPrimary ? [
          BoxShadow(
            color: action.color.withValues(alpha: 0.3),
            blurRadius: 8,
            spreadRadius: 1,
          ),
        ] : null,
      ),
      child: ElevatedButton(
        onPressed: action.onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: isPrimary ? action.color : Colors.grey[800],
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
            side: BorderSide(
              color: action.color.withValues(alpha: 0.5),
              width: 1,
            ),
          ),
        ),
        child: Text(
          action.label,
          style: const TextStyle(
            fontFamily: 'Pirulen',
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }
}

/// Username suggestions dialog
class _UsernameSuggestionsDialog extends StatelessWidget {
  final String username;
  final List<String> suggestions;
  final Function(String) onSelectUsername;
  final VoidCallback? onTryDifferent;

  const _UsernameSuggestionsDialog({
    required this.username,
    required this.suggestions,
    required this.onSelectUsername,
    this.onTryDifferent,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.9),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: MolColors.purple.withValues(alpha: 0.5),
            width: 2,
          ),
          boxShadow: [
            BoxShadow(
              color: MolColors.purple.withValues(alpha: 0.3),
              blurRadius: 20,
              spreadRadius: 2,
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Icon
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: MolColors.purple.withValues(alpha: 0.1),
                border: Border.all(
                  color: MolColors.purple.withValues(alpha: 0.5),
                  width: 2,
                ),
                boxShadow: [
                  BoxShadow(
                    color: MolColors.purple.withValues(alpha: 0.3),
                    blurRadius: 10,
                    spreadRadius: 1,
                  ),
                ],
              ),
              child: Icon(
                Icons.person_outline,
                size: 48,
                color: MolColors.purple,
              ),
            ),

            const SizedBox(height: 24),

            // Title
            const Text(
              'Username Taken',
              style: TextStyle(
                fontFamily: 'Pirulen',
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 16),

            // Message
            Text(
              '"$username" is already taken. Try one of these suggestions:',
              style: const TextStyle(
                fontFamily: 'Bitsumishi',
                fontSize: 16,
                color: Colors.white70,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 24),

            // Suggestions
            ...suggestions.map((suggestion) => Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: MolColors.blue.withValues(alpha: 0.2),
                      blurRadius: 6,
                      spreadRadius: 1,
                    ),
                  ],
                ),
                child: ElevatedButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                    onSelectUsername(suggestion);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: MolColors.blue,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: Text(
                    suggestion,
                    style: const TextStyle(
                      fontFamily: 'Pirulen',
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            )),

            const SizedBox(height: 16),

            // Try different button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  onTryDifferent?.call();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.grey[800],
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                    side: BorderSide(
                      color: MolColors.purple.withValues(alpha: 0.5),
                      width: 1,
                    ),
                  ),
                ),
                child: const Text(
                  'Try Different Username',
                  style: TextStyle(
                    fontFamily: 'Pirulen',
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Retry progress dialog
class _RetryProgressDialog extends StatefulWidget {
  final String operation;
  final int currentAttempt;
  final int maxAttempts;

  const _RetryProgressDialog({
    required this.operation,
    required this.currentAttempt,
    required this.maxAttempts,
  });

  @override
  State<_RetryProgressDialog> createState() => _RetryProgressDialogState();
}

class _RetryProgressDialogState extends State<_RetryProgressDialog>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
    _pulseController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final progress = widget.currentAttempt / widget.maxAttempts;

    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.9),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: MolColors.orange.withValues(alpha: 0.5),
            width: 2,
          ),
          boxShadow: [
            BoxShadow(
              color: MolColors.orange.withValues(alpha: 0.3),
              blurRadius: 20,
              spreadRadius: 2,
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Animated retry icon
            AnimatedBuilder(
              animation: _pulseAnimation,
              builder: (context, child) {
                return Transform.scale(
                  scale: _pulseAnimation.value,
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: MolColors.orange.withValues(alpha: 0.1),
                      border: Border.all(
                        color: MolColors.orange.withValues(alpha: 0.5),
                        width: 2,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: MolColors.orange.withValues(alpha: 0.3),
                          blurRadius: 10,
                          spreadRadius: 1,
                        ),
                      ],
                    ),
                    child: Icon(
                      Icons.refresh,
                      size: 48,
                      color: MolColors.orange,
                    ),
                  ),
                );
              },
            ),

            const SizedBox(height: 24),

            // Title
            Text(
              'Retrying ${widget.operation}...',
              style: const TextStyle(
                fontFamily: 'Pirulen',
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 16),

            // Attempt counter
            Text(
              'Attempt ${widget.currentAttempt} of ${widget.maxAttempts}',
              style: const TextStyle(
                fontFamily: 'Bitsumishi',
                fontSize: 16,
                color: Colors.white70,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 24),

            // Progress bar
            Container(
              height: 8,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(4),
                color: Colors.grey[800],
              ),
              child: FractionallySizedBox(
                alignment: Alignment.centerLeft,
                widthFactor: progress,
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(4),
                    gradient: LinearGradient(
                      colors: [
                        MolColors.orange,
                        MolColors.yellow,
                      ],
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: MolColors.orange.withValues(alpha: 0.3),
                        blurRadius: 4,
                        spreadRadius: 1,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
