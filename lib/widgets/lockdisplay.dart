// 📁 lib/widgets/lockdisplay.dart

import 'package:flutter/material.dart';
import '../models/user_model.dart';

class LockDisplay extends StatelessWidget {
  final User user;
  final int expToNextLevel;
  final String todaysMission;
  final int daysStreak;
  final String upcomingReward;
  final String motivationalQuote;
  final Duration timeSinceLastDiaryEntry;
  final Duration timeSinceLastWorkout;

  const LockDisplay({
    super.key,
    required this.user,
    required this.expToNextLevel,
    required this.todaysMission,
    required this.daysStreak,
    required this.upcomingReward,
    required this.motivationalQuote,
    required this.timeSinceLastDiaryEntry,
    required this.timeSinceLastWorkout,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.black,
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          _buildDigitalTimer('Last Diary Entry', timeSinceLastDiaryEntry),
          const SizedBox(height: 10),
          _buildDigitalTimer('Last Workout', timeSinceLastWorkout),
          const SizedBox(height: 20),
          Text(
            'Level ${user.exp ~/ 100}',
            style: const TextStyle(
              fontFamily: 'Digital-7',
              fontSize: 28,
              color: Colors.greenAccent,
            ),
          ),
          const SizedBox(height: 6),
          Text(
            '$expToNextLevel EXP to Next Level',
            style: const TextStyle(
              fontFamily: 'SF Pro',
              fontSize: 16,
              color: Colors.white70,
            ),
          ),
          const SizedBox(height: 20),
          Text(
            'Today: $todaysMission',
            style: const TextStyle(
              fontFamily: 'SF Pro',
              fontSize: 18,
              color: Colors.white,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 12),
          Text(
            'Streak: $daysStreak Days',
            style: const TextStyle(
              fontFamily: 'SF Pro',
              fontSize: 16,
              color: Colors.blueAccent,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            'Unlock: $upcomingReward',
            style: const TextStyle(
              fontFamily: 'SF Pro',
              fontSize: 16,
              color: Colors.orangeAccent,
            ),
          ),
          const SizedBox(height: 20),
          Text(
            '"$motivationalQuote"',
            style: const TextStyle(
              fontFamily: 'SF Pro',
              fontSize: 16,
              fontStyle: FontStyle.italic,
              color: Colors.white54,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 30),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              padding: const EdgeInsets.symmetric(horizontal: 30, vertical: 14),
            ),
            onPressed: () {
              // Future: Navigate to WorkoutHubScreen or ActiveWorkoutSessionScreen
            },
            child: const Text(
              'Tap to Train',
              style: TextStyle(
                fontFamily: 'SF Pro',
                fontSize: 18,
                color: Colors.white,
              ),
            ),
          )
        ],
      ),
    );
  }

  Widget _buildDigitalTimer(String label, Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);

    return Column(
      children: [
        Text(
          label,
          style: const TextStyle(
            fontFamily: 'SF Pro',
            fontSize: 16,
            color: Colors.white54,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}',
          style: const TextStyle(
            fontFamily: 'Digital-7',
            fontSize: 32,
            color: Colors.cyanAccent,
          ),
        ),
      ],
    );
  }
}

