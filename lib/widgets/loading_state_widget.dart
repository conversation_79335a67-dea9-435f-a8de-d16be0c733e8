import 'package:flutter/material.dart';
import '../design_system/design_system.dart';

/// Comprehensive loading state widget with contextual messages and animations.
/// 
/// Provides consistent loading display across the app with:
/// - Contextual loading messages
/// - Smooth animations and transitions
/// - Progress indicators where applicable
/// - Accessibility support
/// 
/// Example usage:
/// ```dart
/// LoadingStateWidget(
///   type: LoadingStateType.savingData,
///   message: 'Saving your progress...',
/// )
/// ```
class LoadingStateWidget extends StatefulWidget {
  /// The type of loading state to display
  final LoadingStateType type;
  
  /// Custom loading message override
  final String? customMessage;
  
  /// Whether to show progress percentage (0.0 to 1.0)
  final double? progress;
  
  /// Custom color for the loading indicator
  final Color? color;
  
  /// Size of the loading indicator
  final double size;
  
  /// Whether to show as overlay or inline
  final bool isOverlay;

  const LoadingStateWidget({
    super.key,
    required this.type,
    this.customMessage,
    this.progress,
    this.color,
    this.size = 60.0,
    this.isOverlay = false,
  });

  @override
  State<LoadingStateWidget> createState() => _LoadingStateWidgetState();
}

class _LoadingStateWidgetState extends State<LoadingStateWidget>
    with TickerProviderStateMixin {
  late AnimationController _rotationController;
  late AnimationController _pulseController;
  late Animation<double> _rotationAnimation;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
  }

  void _setupAnimations() {
    _rotationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _pulseController = AnimationController(
      duration: DesignTokens.durationLoading,
      vsync: this,
    );

    _rotationAnimation = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(_rotationController);

    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _rotationController.repeat();
    _pulseController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _rotationController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.isOverlay) {
      return _buildOverlay();
    } else {
      return _buildInline();
    }
  }

  Widget _buildOverlay() {
    return Container(
      color: Colors.black.withValues(alpha: 0.7),
      child: Center(
        child: Container(
          padding: const EdgeInsets.all(32),
          decoration: BoxDecoration(
            color: Colors.grey[900],
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: widget.color ?? MolColors.cyan,
              width: 1,
            ),
          ),
          child: _buildLoadingContent(),
        ),
      ),
    );
  }

  Widget _buildInline() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: _buildLoadingContent(),
      ),
    );
  }

  Widget _buildLoadingContent() {
    final loadingInfo = _getLoadingInfo();
    
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Loading Indicator
        _buildLoadingIndicator(loadingInfo),
        const SizedBox(height: 24),
        
        // Loading Message
        Text(
          widget.customMessage ?? loadingInfo.message,
          style: const TextStyle(
            fontFamily: 'Bitsumishi',
            fontSize: 16,
            color: Colors.white,
          ),
          textAlign: TextAlign.center,
        ),
        
        // Progress Bar (if applicable)
        if (widget.progress != null) ...[
          const SizedBox(height: 16),
          _buildProgressBar(),
        ],
      ],
    );
  }

  Widget _buildLoadingIndicator(LoadingInfo info) {
    final color = widget.color ?? info.color;
    
    return AnimatedBuilder(
      animation: Listenable.merge([_rotationController, _pulseController]),
      builder: (context, child) {
        return Transform.scale(
          scale: _pulseAnimation.value,
          child: Transform.rotate(
            angle: _rotationAnimation.value * 2 * 3.14159,
            child: Container(
              width: widget.size,
              height: widget.size,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                gradient: RadialGradient(
                  colors: [
                    color,
                    color.withValues(alpha: 0.3),
                  ],
                ),
                boxShadow: [
                  BoxShadow(
                    color: color.withValues(alpha: 0.5),
                    blurRadius: 20,
                    spreadRadius: 2,
                  ),
                ],
              ),
              child: Icon(
                info.icon,
                size: widget.size * 0.5,
                color: Colors.white,
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildProgressBar() {
    final color = widget.color ?? MolColors.cyan;
    final progress = widget.progress!.clamp(0.0, 1.0);
    
    return Column(
      children: [
        Container(
          width: 200,
          height: 4,
          decoration: BoxDecoration(
            color: Colors.grey[800],
            borderRadius: BorderRadius.circular(2),
          ),
          child: FractionallySizedBox(
            alignment: Alignment.centerLeft,
            widthFactor: progress,
            child: Container(
              decoration: BoxDecoration(
                color: color,
                borderRadius: BorderRadius.circular(2),
                boxShadow: [
                  BoxShadow(
                    color: color.withValues(alpha: 0.5),
                    blurRadius: 4,
                  ),
                ],
              ),
            ),
          ),
        ),
        const SizedBox(height: 8),
        Text(
          '${(progress * 100).round()}%',
          style: TextStyle(
            fontFamily: 'Bitsumishi',
            fontSize: 12,
            color: color,
          ),
        ),
      ],
    );
  }

  LoadingInfo _getLoadingInfo() {
    switch (widget.type) {
      case LoadingStateType.loadingUser:
        return const LoadingInfo(
          message: 'Loading your profile...',
          icon: Icons.person,
          color: MolColors.cyan,
        );
        
      case LoadingStateType.savingData:
        return const LoadingInfo(
          message: 'Saving your progress...',
          icon: Icons.save,
          color: MolColors.green,
        );
        
      case LoadingStateType.loadingHabits:
        return const LoadingInfo(
          message: 'Loading your habits...',
          icon: Icons.track_changes,
          color: MolColors.blue,
        );
        
      case LoadingStateType.processingBounty:
        return const LoadingInfo(
          message: 'Processing bounty...',
          icon: Icons.star,
          color: MolColors.yellow,
        );
        
      case LoadingStateType.syncingData:
        return const LoadingInfo(
          message: 'Syncing your data...',
          icon: Icons.sync,
          color: MolColors.purple,
        );
        
      case LoadingStateType.uploadingPhoto:
        return const LoadingInfo(
          message: 'Uploading photo proof...',
          icon: Icons.cloud_upload,
          color: MolColors.orange,
        );
        
      case LoadingStateType.generatingReport:
        return const LoadingInfo(
          message: 'Generating your report...',
          icon: Icons.analytics,
          color: MolColors.pink,
        );
        
      case LoadingStateType.connectingNetwork:
        return const LoadingInfo(
          message: 'Connecting to server...',
          icon: Icons.wifi,
          color: Colors.orange,
        );
        
      case LoadingStateType.initializing:
        return const LoadingInfo(
          message: 'Initializing app...',
          icon: Icons.rocket_launch,
          color: MolColors.cyan,
        );
        
      case LoadingStateType.generic:
        return const LoadingInfo(
          message: 'Loading...',
          icon: Icons.hourglass_empty,
          color: Colors.grey,
        );
    }
  }
}

/// Types of loading states supported by the widget
enum LoadingStateType {
  loadingUser,
  savingData,
  loadingHabits,
  processingBounty,
  syncingData,
  uploadingPhoto,
  generatingReport,
  connectingNetwork,
  initializing,
  generic,
}

/// Information about a loading state for display purposes
class LoadingInfo {
  final String message;
  final IconData icon;
  final Color color;

  const LoadingInfo({
    required this.message,
    required this.icon,
    required this.color,
  });
}
