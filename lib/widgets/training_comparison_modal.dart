// lib/widgets/training_comparison_modal.dart

import 'package:flutter/material.dart';
import '../models/training_session_model.dart';


/// Modal for comparing two training sessions side by side
class TrainingComparisonModal extends StatelessWidget {
  final TrainingSession currentSession;
  final TrainingSession previousSession;
  final Color glowColor;
  final VoidCallback onClose;

  const TrainingComparisonModal({
    super.key,
    required this.currentSession,
    required this.previousSession,
    required this.glowColor,
    required this.onClose,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.black.withValues(alpha: 0.95),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: glowColor.withValues(alpha: 0.3), width: 2),
          boxShadow: [
            BoxShadow(
              color: glowColor.withValues(alpha: 0.2),
              blurRadius: 20,
              spreadRadius: 4,
            ),
          ],
        ),
        child: Column(
          children: [
            _buildHeader(),
            const SizedBox(height: 20),
            Expanded(child: _buildComparison()),
            const SizedBox(height: 16),
            _buildCloseButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Icon(
          Icons.compare_arrows,
          color: glowColor,
          size: 24,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            'TRAINING COMPARISON',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontFamily: 'Pirulen',
              fontWeight: FontWeight.bold,
              shadows: [
                Shadow(
                  color: glowColor.withValues(alpha: 0.8),
                  offset: const Offset(0, 0),
                  blurRadius: 10,
                ),
              ],
            ),
          ),
        ),
        IconButton(
          onPressed: onClose,
          icon: const Icon(Icons.close, color: Colors.white70),
        ),
      ],
    );
  }

  Widget _buildComparison() {
    return Row(
      children: [
        Expanded(child: _buildSessionColumn(previousSession, 'Previous', Colors.white70)),
        Container(
          width: 2,
          margin: const EdgeInsets.symmetric(horizontal: 16),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                glowColor.withValues(alpha: 0.1),
                glowColor.withValues(alpha: 0.6),
                glowColor.withValues(alpha: 0.1),
              ],
            ),
          ),
        ),
        Expanded(child: _buildSessionColumn(currentSession, 'Current', glowColor)),
      ],
    );
  }

  Widget _buildSessionColumn(TrainingSession session, String title, Color accentColor) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Column header
        Center(
          child: Text(
            title.toUpperCase(),
            style: TextStyle(
              color: accentColor,
              fontSize: 14,
              fontFamily: 'Pirulen',
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        const SizedBox(height: 16),
        
        // Session details
        Expanded(
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildDetailRow('Label', session.label, accentColor),
                const SizedBox(height: 16),

                // Notes section - prioritized first
                Text(
                  'NOTES',
                  style: TextStyle(
                    color: accentColor,
                    fontSize: 12,
                    fontFamily: 'Pirulen',
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.4),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: accentColor.withValues(alpha: 0.3), width: 1),
                  ),
                  child: SelectableText(
                    session.notes.isNotEmpty ? session.notes : 'No notes recorded',
                    style: TextStyle(
                      color: session.notes.isNotEmpty ? Colors.white : Colors.white30,
                      fontSize: 12,
                      fontFamily: 'Bitsumishi',
                      fontStyle: session.notes.isNotEmpty ? FontStyle.normal : FontStyle.italic,
                    ),
                  ),
                ),
                const SizedBox(height: 16),

                // Other session details
                _buildDetailRow('Date', session.formattedDate, accentColor),
                _buildDetailRow('Duration', session.formattedDuration, accentColor),
                _buildDetailRow('EXP Earned', '${session.expEarned}', accentColor),
                if (session.bodyweightKg != null)
                  _buildDetailRow('Bodyweight', '${session.bodyweightKg!.toStringAsFixed(1)} kg', accentColor),
                if (session.currentGoal.isNotEmpty)
                  _buildDetailRow('Goal', session.currentGoal, accentColor),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDetailRow(String label, String value, Color accentColor) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label.toUpperCase(),
            style: TextStyle(
              color: Colors.white70,
              fontSize: 10,
              fontFamily: 'Pirulen',
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              color: accentColor,
              fontSize: 14,
              fontFamily: 'Digital-7',
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCloseButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: onClose,
        style: ElevatedButton.styleFrom(
          backgroundColor: glowColor.withValues(alpha: 0.2),
          foregroundColor: glowColor,
          side: BorderSide(color: glowColor.withValues(alpha: 0.6), width: 1),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          padding: const EdgeInsets.symmetric(vertical: 16),
        ),
        child: const Text(
          'CLOSE COMPARISON',
          style: TextStyle(
            fontFamily: 'Pirulen',
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }
}

/// Widget for selecting a session to compare with
class TrainingComparisonSelector extends StatefulWidget {
  final String currentLabel;
  final List<TrainingSession> availableSessions;
  final Color glowColor;
  final Function(TrainingSession) onSessionSelected;
  final VoidCallback onClose;

  const TrainingComparisonSelector({
    super.key,
    required this.currentLabel,
    required this.availableSessions,
    required this.glowColor,
    required this.onSessionSelected,
    required this.onClose,
  });

  @override
  State<TrainingComparisonSelector> createState() => _TrainingComparisonSelectorState();
}

class _TrainingComparisonSelectorState extends State<TrainingComparisonSelector> {
  String _filterType = 'Same Label';
  List<TrainingSession> _filteredSessions = [];

  @override
  void initState() {
    super.initState();
    _applyFilter();
  }

  void _applyFilter() {
    setState(() {
      switch (_filterType) {
        case 'Same Label':
          _filteredSessions = widget.availableSessions
              .where((session) => session.label == widget.currentLabel)
              .toList();
          break;
        case 'Recent':
          _filteredSessions = widget.availableSessions.take(10).toList();
          break;
        case 'All':
        default:
          _filteredSessions = widget.availableSessions;
          break;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.black.withValues(alpha: 0.95),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.85,
        height: MediaQuery.of(context).size.height * 0.7,
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: widget.glowColor.withValues(alpha: 0.3), width: 2),
          boxShadow: [
            BoxShadow(
              color: widget.glowColor.withValues(alpha: 0.2),
              blurRadius: 20,
              spreadRadius: 4,
            ),
          ],
        ),
        child: Column(
          children: [
            _buildHeader(),
            const SizedBox(height: 16),
            _buildFilterTabs(),
            const SizedBox(height: 16),
            Expanded(child: _buildSessionsList()),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Icon(
          Icons.compare_arrows,
          color: widget.glowColor,
          size: 24,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            'SELECT SESSION TO COMPARE',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontFamily: 'Pirulen',
              fontWeight: FontWeight.bold,
              shadows: [
                Shadow(
                  color: widget.glowColor.withValues(alpha: 0.8),
                  offset: const Offset(0, 0),
                  blurRadius: 10,
                ),
              ],
            ),
          ),
        ),
        IconButton(
          onPressed: widget.onClose,
          icon: const Icon(Icons.close, color: Colors.white70),
        ),
      ],
    );
  }

  Widget _buildFilterTabs() {
    final filters = ['Same Label', 'Recent', 'All'];
    
    return Row(
      children: filters.map((filter) {
        final isSelected = _filterType == filter;
        return Expanded(
          child: GestureDetector(
            onTap: () {
              setState(() {
                _filterType = filter;
                _applyFilter();
              });
            },
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 4),
              padding: const EdgeInsets.symmetric(vertical: 8),
              decoration: BoxDecoration(
                color: isSelected 
                    ? widget.glowColor.withValues(alpha: 0.2)
                    : Colors.black.withValues(alpha: 0.4),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: isSelected 
                      ? widget.glowColor.withValues(alpha: 0.6)
                      : Colors.white.withValues(alpha: 0.2),
                  width: 1,
                ),
              ),
              child: Text(
                filter,
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: isSelected ? widget.glowColor : Colors.white70,
                  fontSize: 12,
                  fontFamily: 'Bitsumishi',
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildSessionsList() {
    if (_filteredSessions.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              color: Colors.white30,
              size: 48,
            ),
            const SizedBox(height: 16),
            Text(
              'No sessions found',
              style: TextStyle(
                color: Colors.white30,
                fontSize: 16,
                fontFamily: 'Bitsumishi',
              ),
            ),
            Text(
              'for the selected filter',
              style: TextStyle(
                color: Colors.white30,
                fontSize: 14,
                fontFamily: 'Bitsumishi',
              ),
            ),
          ],
        ),
      );
    }
    
    return ListView.builder(
      itemCount: _filteredSessions.length,
      itemBuilder: (context, index) {
        final session = _filteredSessions[index];
        return _buildSessionTile(session);
      },
    );
  }

  Widget _buildSessionTile(TrainingSession session) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.4),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.white.withValues(alpha: 0.1), width: 1),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(12),
        title: Text(
          session.label,
          style: TextStyle(
            color: widget.glowColor,
            fontSize: 14,
            fontFamily: 'Pirulen',
            fontWeight: FontWeight.bold,
          ),
        ),
        subtitle: Text(
          '${session.formattedDuration} • ${session.expEarned} EXP • ${session.formattedDate}',
          style: const TextStyle(
            color: Colors.white70,
            fontSize: 12,
            fontFamily: 'Digital-7',
          ),
        ),
        trailing: Icon(
          Icons.arrow_forward_ios,
          color: widget.glowColor.withValues(alpha: 0.7),
          size: 16,
        ),
        onTap: () => widget.onSessionSelected(session),
      ),
    );
  }
}
