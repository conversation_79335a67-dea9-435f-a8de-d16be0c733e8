import 'dart:math';
import 'package:flutter/material.dart';

/// Fireworks animation widget for epic bounty celebrations
/// 
/// Features:
/// - Multiple particle explosions
/// - Lightning bolt effects
/// - Neon glow and color variations
/// - Continuous animation loop
class FireworksAnimation extends StatefulWidget {
  final double width;
  final double height;
  final Duration duration;
  
  const FireworksAnimation({
    super.key,
    this.width = 200,
    this.height = 200,
    this.duration = const Duration(seconds: 3),
  });

  @override
  State<FireworksAnimation> createState() => _FireworksAnimationState();
}

class _FireworksAnimationState extends State<FireworksAnimation>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;
  
  final List<Firework> _fireworks = [];
  final Random _random = Random();
  
  @override
  void initState() {
    super.initState();
    
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );
    
    _animation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOut,
    ));
    
    _generateFireworks();
    _controller.repeat();
  }
  
  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
  
  void _generateFireworks() {
    _fireworks.clear();
    
    // Generate 5-8 fireworks at random positions
    final int count = 5 + _random.nextInt(4);
    
    for (int i = 0; i < count; i++) {
      _fireworks.add(Firework(
        x: _random.nextDouble() * widget.width,
        y: _random.nextDouble() * widget.height,
        color: _getRandomColor(),
        startTime: _random.nextDouble() * 0.5, // Stagger start times
      ));
    }
  }
  
  Color _getRandomColor() {
    final colors = [
      Colors.purple,
      Colors.blue,
      Colors.cyan,
      Colors.amber,
      Colors.pink,
      Colors.green,
      Colors.orange,
      Colors.red,
    ];
    return colors[_random.nextInt(colors.length)];
  }
  
  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return CustomPaint(
          size: Size(widget.width, widget.height),
          painter: FireworksPainter(
            fireworks: _fireworks,
            progress: _animation.value,
          ),
        );
      },
    );
  }
}

/// Individual firework data
class Firework {
  final double x;
  final double y;
  final Color color;
  final double startTime;
  final List<Particle> particles;
  
  Firework({
    required this.x,
    required this.y,
    required this.color,
    required this.startTime,
  }) : particles = _generateParticles(x, y, color);
  
  static List<Particle> _generateParticles(double x, double y, Color color) {
    final List<Particle> particles = [];
    final Random random = Random();
    
    // Generate 12-20 particles per firework
    final int count = 12 + random.nextInt(9);
    
    for (int i = 0; i < count; i++) {
      final double angle = (i / count) * 2 * pi + random.nextDouble() * 0.5;
      final double speed = 30 + random.nextDouble() * 40;
      
      particles.add(Particle(
        startX: x,
        startY: y,
        velocityX: cos(angle) * speed,
        velocityY: sin(angle) * speed,
        color: color,
        life: 0.8 + random.nextDouble() * 0.4,
      ));
    }
    
    return particles;
  }
}

/// Individual particle in a firework explosion
class Particle {
  final double startX;
  final double startY;
  final double velocityX;
  final double velocityY;
  final Color color;
  final double life;
  
  const Particle({
    required this.startX,
    required this.startY,
    required this.velocityX,
    required this.velocityY,
    required this.color,
    required this.life,
  });
  
  double getX(double time) {
    return startX + velocityX * time;
  }
  
  double getY(double time) {
    return startY + velocityY * time + 0.5 * 100 * time * time; // Gravity effect
  }
  
  double getOpacity(double time) {
    final double normalizedTime = time / life;
    if (normalizedTime > 1.0) return 0.0;
    return 1.0 - normalizedTime;
  }
}

/// Custom painter for fireworks animation
class FireworksPainter extends CustomPainter {
  final List<Firework> fireworks;
  final double progress;
  
  FireworksPainter({
    required this.fireworks,
    required this.progress,
  });
  
  @override
  void paint(Canvas canvas, Size size) {
    for (final firework in fireworks) {
      final double fireworkTime = (progress - firework.startTime).clamp(0.0, 1.0);
      
      if (fireworkTime <= 0) continue;
      
      // Draw particles
      for (final particle in firework.particles) {
        final double particleTime = fireworkTime * 2.0; // Speed up particle animation
        final double opacity = particle.getOpacity(particleTime);
        
        if (opacity <= 0) continue;
        
        final double x = particle.getX(particleTime);
        final double y = particle.getY(particleTime);
        
        // Skip particles outside canvas
        if (x < 0 || x > size.width || y < 0 || y > size.height) continue;
        
        final Paint paint = Paint()
          ..color = particle.color.withValues(alpha: opacity)
          ..style = PaintingStyle.fill
          ..maskFilter = MaskFilter.blur(BlurStyle.normal, 2.0);
        
        // Draw particle as small circle
        canvas.drawCircle(
          Offset(x, y),
          2.0 + (1.0 - fireworkTime) * 2.0, // Particles get smaller over time
          paint,
        );
        
        // Draw glow effect
        final Paint glowPaint = Paint()
          ..color = particle.color.withValues(alpha: opacity * 0.3)
          ..style = PaintingStyle.fill
          ..maskFilter = MaskFilter.blur(BlurStyle.normal, 6.0);
        
        canvas.drawCircle(
          Offset(x, y),
          4.0 + (1.0 - fireworkTime) * 4.0,
          glowPaint,
        );
      }
      
      // Draw lightning bolts for epic effect
      if (fireworkTime > 0.1 && fireworkTime < 0.6) {
        _drawLightningBolts(canvas, firework, fireworkTime);
      }
    }
  }
  
  void _drawLightningBolts(Canvas canvas, Firework firework, double time) {
    final Paint lightningPaint = Paint()
      ..color = Colors.white.withValues(alpha: 0.8 * (1.0 - time))
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0
      ..maskFilter = MaskFilter.blur(BlurStyle.normal, 1.0);
    
    final Random random = Random(firework.x.hashCode + firework.y.hashCode);
    
    // Draw 3-5 lightning bolts
    for (int i = 0; i < 4; i++) {
      final double angle = (i / 4) * 2 * pi + random.nextDouble() * 0.5;
      final double length = 30 + random.nextDouble() * 20;
      
      final double endX = firework.x + cos(angle) * length;
      final double endY = firework.y + sin(angle) * length;
      
      // Create jagged lightning path
      final Path lightningPath = Path();
      lightningPath.moveTo(firework.x, firework.y);
      
      final int segments = 3 + random.nextInt(3);
      for (int j = 1; j <= segments; j++) {
        final double t = j / segments;
        final double x = firework.x + (endX - firework.x) * t + (random.nextDouble() - 0.5) * 10;
        final double y = firework.y + (endY - firework.y) * t + (random.nextDouble() - 0.5) * 10;
        lightningPath.lineTo(x, y);
      }
      
      canvas.drawPath(lightningPath, lightningPaint);
    }
  }
  
  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}
