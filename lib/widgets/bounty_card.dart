import 'package:flutter/material.dart';
import '../models/bounty_model.dart';

class BountyCard extends StatelessWidget {
  final BountyModel bounty;
  final Color glowColor;
  final VoidCallback onCashIn;
  final bool showCashIn;

  const BountyCard({
    super.key,
    required this.bounty,
    required this.glowColor,
    required this.onCashIn,
    this.showCashIn = true,
  });

  @override
  Widget build(BuildContext context) {
    // Debug logging for bounty card layout
    print('🎯 BountyCard: Building card for ${bounty.description}');
    print('🎯 Categories: ${bounty.categories.length} items - ${bounty.categories}');
    print('🎯 EXP rewards: ${bounty.expPerCategory.length} items - ${bounty.expPerCategory}');

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.7),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: bounty.isEpic ? Colors.purple : Colors.blue,
          width: 2,
        ),
        boxShadow: [
          BoxShadow(
            color: glowColor.withValues(alpha: 0.5),
            blurRadius: 16,
            spreadRadius: 2,
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header with difficulty and categories
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: bounty.isEpic ? Colors.purple.withValues(alpha: 0.3) : Colors.blue.withValues(alpha: 0.3),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(14),
                topRight: Radius.circular(14),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // Difficulty badge
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4), // Reduced padding
                  decoration: BoxDecoration(
                    color: _getDifficultyColor(bounty.difficulty),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    bounty.difficulty.toUpperCase(),
                    style: const TextStyle(
                      fontFamily: 'Pirulen',
                      fontSize: 10, // Reduced font size
                      color: Colors.white,
                    ),
                  ),
                ),
                // Categories - wrapped to prevent overflow
                Flexible(
                  child: Wrap(
                    alignment: WrapAlignment.end,
                    spacing: 6, // Horizontal spacing
                    runSpacing: 4, // Vertical spacing
                    children: bounty.categories.map((category) {
                      return Container(
                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.black.withValues(alpha: 0.5),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          category,
                          style: const TextStyle(
                            fontFamily: 'Pirulen',
                            fontSize: 10,
                            color: Colors.white,
                          ),
                        ),
                      );
                    }).toList(),
                  ),
                ),
              ],
            ),
          ),
          // Description
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              bounty.description,
              style: const TextStyle(
                fontFamily: 'Pirulen',
                fontSize: 16,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          // EXP rewards - wrapped to prevent overflow
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Wrap(
              alignment: WrapAlignment.center,
              spacing: 8, // Horizontal spacing between items
              runSpacing: 6, // Vertical spacing between rows
              children: bounty.expPerCategory.entries.map((entry) {
                return Container(
                  padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      colors: [
                        Colors.amber,
                        Colors.orange,
                      ],
                    ),
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.amber.withValues(alpha: 0.3),
                        blurRadius: 8,
                        spreadRadius: 1,
                      ),
                    ],
                  ),
                  child: Text(
                    '+${entry.value} ${entry.key}',
                    style: const TextStyle(
                      fontFamily: 'Pirulen',
                      fontSize: 12,
                      color: Colors.black,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                );
              }).toList(),
            ),
          ),
          // Cash In button
          if (showCashIn)
            Padding(
              padding: const EdgeInsets.all(16),
              child: ElevatedButton(
                onPressed: onCashIn,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: const [
                    Icon(Icons.camera_alt),
                    SizedBox(width: 8),
                    Text(
                      'CASH IN',
                      style: TextStyle(
                        fontFamily: 'Pirulen',
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  Color _getDifficultyColor(String difficulty) {
    switch (difficulty.toLowerCase()) {
      case 'easy':
        return Colors.green;
      case 'medium':
        return Colors.orange;
      case 'hard':
        return Colors.red;
      case 'epic':
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }
}