// 📁 lib/widgets/coach_monitoring_dashboard.dart

import 'package:flutter/material.dart';

import '../theme/colors.dart';
import '../services/coach_analytics_service.dart';


import '../services/coach_debug_service.dart';
import '../models/user_model.dart';

/// Enterprise-grade monitoring dashboard for real-time coach system visibility,
/// performance metrics, safety status, and debugging capabilities.
class CoachMonitoringDashboard extends StatefulWidget {
  final User? currentUser;
  final bool isCreatorVersion;

  const CoachMonitoringDashboard({
    super.key,
    this.currentUser,
    this.isCreatorVersion = false,
  });

  @override
  State<CoachMonitoringDashboard> createState() => _CoachMonitoringDashboardState();
}

class _CoachMonitoringDashboardState extends State<CoachMonitoringDashboard>
    with TickerProviderStateMixin {
  late TabController _tabController;
  AnalyticsDashboard? _analytics;

  SystemHealth? _systemHealth;
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: widget.isCreatorVersion ? 5 : 3, vsync: this);
    _loadDashboardData();
    
    // Auto-refresh every 30 seconds
    if (mounted) {
      Future.delayed(const Duration(seconds: 30), _autoRefresh);
    }
  }

  void _autoRefresh() {
    if (mounted) {
      _loadDashboardData();
      Future.delayed(const Duration(seconds: 30), _autoRefresh);
    }
  }

  Future<void> _loadDashboardData() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final analytics = await CoachAnalyticsService.getDashboardData();
      final systemHealth = await CoachAnalyticsService.getSystemHealth();

      if (mounted) {
        setState(() {
          _analytics = analytics;
          _systemHealth = systemHealth;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        title: const Text(
          'Coach System Monitor',
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh, color: Colors.white),
            onPressed: _loadDashboardData,
          ),
          if (widget.isCreatorVersion)
            IconButton(
              icon: const Icon(Icons.bug_report, color: MolColors.cyan),
              onPressed: _runDebugTests,
            ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: MolColors.cyan,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.grey,
          tabs: [
            const Tab(text: 'Overview'),
            const Tab(text: 'Analytics'),
            const Tab(text: 'Safety'),
            if (widget.isCreatorVersion) ...[
              const Tab(text: 'Debug'),
              const Tab(text: 'Admin'),
            ],
          ],
        ),
      ),
      body: _isLoading
          ? _buildLoadingWidget()
          : _error != null
              ? _buildErrorWidget()
              : TabBarView(
                  controller: _tabController,
                  children: [
                    _buildOverviewTab(),
                    _buildAnalyticsTab(),
                    _buildSafetyTab(),
                    if (widget.isCreatorVersion) ...[
                      _buildDebugTab(),
                      _buildAdminTab(),
                    ],
                  ],
                ),
    );
  }

  Widget _buildLoadingWidget() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(color: MolColors.cyan),
          SizedBox(height: 16),
          Text(
            'Loading dashboard data...',
            style: TextStyle(color: Colors.white),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error, color: Colors.red, size: 64),
          const SizedBox(height: 16),
          Text(
            'Error loading dashboard',
            style: const TextStyle(color: Colors.white, fontSize: 18),
          ),
          const SizedBox(height: 8),
          Text(
            _error!,
            style: const TextStyle(color: Colors.grey, fontSize: 14),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _loadDashboardData,
            style: ElevatedButton.styleFrom(backgroundColor: MolColors.cyan),
            child: const Text('Retry', style: TextStyle(color: Colors.black)),
          ),
        ],
      ),
    );
  }

  Widget _buildOverviewTab() {
    if (_systemHealth == null) return const SizedBox();

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSystemStatusCard(),
          const SizedBox(height: 16),
          _buildQuickStatsGrid(),
          const SizedBox(height: 16),
          _buildRealtimeMetrics(),
          const SizedBox(height: 16),
          _buildActiveAlerts(),
        ],
      ),
    );
  }

  Widget _buildSystemStatusCard() {
    final status = _systemHealth!.status;
    final statusColor = status == SystemStatus.healthy
        ? Colors.green
        : status == SystemStatus.warning
            ? Colors.orange
            : Colors.red;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: statusColor, width: 2),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                status == SystemStatus.healthy
                    ? Icons.check_circle
                    : status == SystemStatus.warning
                        ? Icons.warning
                        : Icons.error,
                color: statusColor,
                size: 32,
              ),
              const SizedBox(width: 12),
              Text(
                'System Status: ${status.name.toUpperCase()}',
                style: TextStyle(
                  color: statusColor,
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _buildStatusMetric(
                'Error Rate',
                '${(_systemHealth!.errorRate * 100).toStringAsFixed(1)}%',
                _systemHealth!.errorRate < 0.05 ? Colors.green : Colors.red,
              ),
              _buildStatusMetric(
                'Avg Response',
                '${(_systemHealth!.averageResponseTime / 1000).toStringAsFixed(1)}s',
                _systemHealth!.averageResponseTime < 15000 ? Colors.green : Colors.red,
              ),
              _buildStatusMetric(
                'Active Users',
                '${_systemHealth!.activeUsers}',
                Colors.cyan,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatusMetric(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          value,
          style: TextStyle(
            color: color,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            color: Colors.grey,
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  Widget _buildQuickStatsGrid() {
    if (_analytics == null) return const SizedBox();

    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      childAspectRatio: 1.5,
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      children: [
        _buildStatCard(
          'Total Interactions',
          '${_analytics!.totalInteractions}',
          Icons.chat,
          MolColors.purple,
        ),
        _buildStatCard(
          'Success Rate',
          '${(_analytics!.successRate * 100).toStringAsFixed(1)}%',
          Icons.check_circle,
          Colors.green,
        ),
        _buildStatCard(
          'Total Cost',
          '\$${_analytics!.totalCost.toStringAsFixed(2)}',
          Icons.attach_money,
          MolColors.yellow,
        ),
        _buildStatCard(
          'Avg Response Time',
          '${(_analytics!.averageResponseTime / 1000).toStringAsFixed(1)}s',
          Icons.timer,
          MolColors.cyan,
        ),
      ],
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: color, size: 32),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              color: color,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            title,
            style: const TextStyle(
              color: Colors.grey,
              fontSize: 12,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildRealtimeMetrics() {
    if (_analytics?.realtimeMetrics.isEmpty ?? true) return const SizedBox();

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Real-time Metrics',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          ...(_analytics!.realtimeMetrics.entries.map((entry) {
            if (entry.key == 'last_interaction') return const SizedBox();
            return Padding(
              padding: const EdgeInsets.symmetric(vertical: 4),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    entry.key.replaceAll('_', ' ').toUpperCase(),
                    style: const TextStyle(color: Colors.grey),
                  ),
                  Text(
                    entry.value.toString(),
                    style: const TextStyle(color: Colors.white),
                  ),
                ],
              ),
            );
          }).toList()),
        ],
      ),
    );
  }

  Widget _buildActiveAlerts() {
    if (_analytics?.activeAlerts.isEmpty ?? true) {
      return Container(
        width: double.infinity,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.grey[900],
          borderRadius: BorderRadius.circular(12),
        ),
        child: const Row(
          children: [
            Icon(Icons.check_circle, color: Colors.green),
            SizedBox(width: 12),
            Text(
              'No Active Alerts',
              style: TextStyle(color: Colors.green, fontSize: 16),
            ),
          ],
        ),
      );
    }

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Active Alerts (${_analytics!.activeAlerts.length})',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          ...(_analytics!.activeAlerts.take(5).map((alert) {
            final alertColor = alert.severity == AlertSeverity.critical
                ? Colors.red
                : alert.severity == AlertSeverity.warning
                    ? Colors.orange
                    : Colors.blue;

            return Container(
              margin: const EdgeInsets.only(bottom: 8),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: alertColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: alertColor.withValues(alpha: 0.3)),
              ),
              child: Row(
                children: [
                  Icon(
                    alert.severity == AlertSeverity.critical
                        ? Icons.error
                        : alert.severity == AlertSeverity.warning
                            ? Icons.warning
                            : Icons.info,
                    color: alertColor,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          alert.title,
                          style: TextStyle(
                            color: alertColor,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          alert.message,
                          style: const TextStyle(color: Colors.grey, fontSize: 12),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          }).toList()),
        ],
      ),
    );
  }

  Widget _buildAnalyticsTab() {
    // Implementation for detailed analytics
    return const Center(
      child: Text(
        'Detailed Analytics Coming Soon',
        style: TextStyle(color: Colors.white),
      ),
    );
  }

  Widget _buildSafetyTab() {
    // Implementation for safety metrics
    return const Center(
      child: Text(
        'Safety Metrics Coming Soon',
        style: TextStyle(color: Colors.white),
      ),
    );
  }

  Widget _buildDebugTab() {
    // Implementation for debug tools
    return const Center(
      child: Text(
        'Debug Tools Coming Soon',
        style: TextStyle(color: Colors.white),
      ),
    );
  }

  Widget _buildAdminTab() {
    // Implementation for admin controls
    return const Center(
      child: Text(
        'Admin Controls Coming Soon',
        style: TextStyle(color: Colors.white),
      ),
    );
  }

  Future<void> _runDebugTests() async {
    if (widget.currentUser == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('No user available for testing'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    try {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          backgroundColor: Colors.black,
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(color: MolColors.cyan),
              SizedBox(height: 16),
              Text(
                'Running debug tests...',
                style: TextStyle(color: Colors.white),
              ),
            ],
          ),
        ),
      );

      final summary = await CoachDebugService.runComprehensiveTests(
        testUser: widget.currentUser!,
        includeStressTests: false,
        includeEdgeCases: true,
        includePerformanceTests: true,
      );

      if (mounted) {
        Navigator.of(context).pop(); // Close loading dialog

        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            backgroundColor: Colors.grey[900],
            title: const Text(
              'Debug Test Results',
              style: TextStyle(color: Colors.white),
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Tests: ${summary.passedTests}/${summary.totalTests} passed',
                  style: TextStyle(
                    color: summary.passedTests == summary.totalTests
                        ? Colors.green
                        : Colors.orange,
                  ),
                ),
                Text(
                  'Success Rate: ${(summary.successRate * 100).toStringAsFixed(1)}%',
                  style: const TextStyle(color: Colors.white),
                ),
                Text(
                  'Duration: ${summary.totalDuration}ms',
                  style: const TextStyle(color: Colors.white),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('OK', style: TextStyle(color: MolColors.cyan)),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        Navigator.of(context).pop(); // Close loading dialog
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Debug tests failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }
}
