// lib/widgets/confirm_email_verification_modal.dart

import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../services/klaviyo_service.dart';
import '../services/comprehensive_logging_service.dart';

/// Modal for confirming email verification status
/// Shows after user returns from checking their email
/// Provides red "Not Yet" and green "Verified!" buttons
class ConfirmEmailVerificationModal extends StatefulWidget {
  final String email;
  final String password;
  final String username;
  final String gender;
  final VoidCallback onSuccess;
  final VoidCallback onBack;

  const ConfirmEmailVerificationModal({
    super.key,
    required this.email,
    required this.password,
    required this.username,
    required this.gender,
    required this.onSuccess,
    required this.onBack,
  });

  @override
  State<ConfirmEmailVerificationModal> createState() => _ConfirmEmailVerificationModalState();
}

class _ConfirmEmailVerificationModalState extends State<ConfirmEmailVerificationModal> {
  bool _isChecking = false;

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: const Color(0xFF1A1A1A),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: Colors.cyanAccent.withValues(alpha: 0.3),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.cyanAccent.withValues(alpha: 0.2),
              blurRadius: 20,
              spreadRadius: 2,
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Close button
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const SizedBox(width: 24),
                const Text(
                  '📧 Email Verification Check',
                  style: TextStyle(
                    color: Colors.cyanAccent,
                    fontFamily: 'Pirulen',
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
                GestureDetector(
                  onTap: widget.onBack,
                  child: Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      color: Colors.grey[800],
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Icon(
                      Icons.close,
                      color: Colors.white70,
                      size: 16,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),
            
            // Main content
            const Text(
              'Did you verify your email address?',
              style: TextStyle(
                color: Colors.white,
                fontFamily: 'Bitsumishi',
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
            
            const Text(
              'Check your inbox and click the\nverification link.',
              style: TextStyle(
                color: Colors.white70,
                fontFamily: 'Bitsumishi',
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            
            // Warning about coaches
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.orange.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Colors.orange.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: const Row(
                children: [
                  Icon(
                    Icons.warning_amber_rounded,
                    color: Colors.orange,
                    size: 20,
                  ),
                  SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'Note: Email verification unlocks MXD AI Coaches',
                      style: TextStyle(
                        color: Colors.orange,
                        fontFamily: 'Bitsumishi',
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 32),
            
            // Action buttons
            if (_isChecking) ...[
              const CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Colors.cyanAccent),
              ),
              const SizedBox(height: 16),
              const Text(
                'Checking verification status...',
                style: TextStyle(
                  color: Colors.white70,
                  fontFamily: 'Bitsumishi',
                  fontSize: 14,
                ),
              ),
            ] else ...[
              Row(
                children: [
                  // Red "Not Yet" button
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _handleNotVerified,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red[700],
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: const Text(
                        '🔴 Not Yet',
                        style: TextStyle(
                          fontFamily: 'Pirulen',
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  
                  // Green "Verified!" button
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _handleVerified,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green[700],
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: const Text(
                        '✅ Verified!',
                        style: TextStyle(
                          fontFamily: 'Pirulen',
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// Handle when user says they haven't verified yet
  void _handleNotVerified() async {
    await ComprehensiveLoggingService.logInfo('🔴 User indicated email not verified yet');
    
    // Proceed to onboarding anyway, but mark as unverified
    await _proceedToOnboarding(emailVerified: false);
  }

  /// Handle when user says they have verified
  void _handleVerified() async {
    setState(() {
      _isChecking = true;
    });

    await ComprehensiveLoggingService.logInfo('✅ User indicated email verified - checking status');
    
    try {
      // Check both Firebase and Klaviyo
      final firebaseVerified = await _checkFirebaseVerification();
      final klaviyoExists = await _checkKlaviyoStatus();
      
      await ComprehensiveLoggingService.logInfo(
        '📊 Verification status: Firebase=$firebaseVerified, Klaviyo=$klaviyoExists'
      );
      
      // Proceed to onboarding with verification status
      await _proceedToOnboarding(
        emailVerified: firebaseVerified,
        klaviyoRegistered: klaviyoExists,
      );
      
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Error checking verification status: $e');
      
      // On error, proceed anyway but mark as unverified
      await _proceedToOnboarding(emailVerified: false);
    }
  }

  /// Check Firebase email verification status
  Future<bool> _checkFirebaseVerification() async {
    try {
      await FirebaseAuth.instance.currentUser?.reload();
      final user = FirebaseAuth.instance.currentUser;
      final isVerified = user?.emailVerified ?? false;
      
      await ComprehensiveLoggingService.logInfo('🔥 Firebase verification check: $isVerified');
      return isVerified;
      
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Firebase verification check failed: $e');
      return false;
    }
  }

  /// Check Klaviyo registration status
  Future<bool> _checkKlaviyoStatus() async {
    try {
      final exists = await KlaviyoService.emailExists(widget.email);
      await ComprehensiveLoggingService.logInfo('📧 Klaviyo status check: $exists');
      return exists;
      
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Klaviyo status check failed: $e');
      return false;
    }
  }

  /// Proceed to onboarding with verification status
  Future<void> _proceedToOnboarding({
    required bool emailVerified,
    bool klaviyoRegistered = false,
  }) async {
    await ComprehensiveLoggingService.logInfo(
      '🚀 Proceeding to onboarding: verified=$emailVerified, klaviyo=$klaviyoRegistered'
    );
    
    setState(() {
      _isChecking = false;
    });
    
    // Call success callback to continue onboarding
    widget.onSuccess();
  }
}
