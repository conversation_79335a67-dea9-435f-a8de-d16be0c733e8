// 📁 lib/Widgets/exp_category_tile.dart
import 'package:flutter/material.dart';

class ExpCategoryTile extends StatelessWidget {
  final String category;
  final int exp;
  final int level;
  final Color color;

  const ExpCategoryTile({
    super.key,
    required this.category,
    required this.exp,
    required this.level,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    final progress = (exp % 100) / 100.0;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 14),
              alignment: Alignment.center,
              decoration: BoxDecoration(
                color: color,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                "${category.toUpperCase()} EXP",
                style: const TextStyle(
                  fontFamily: 'Bitsumishi',
                  fontSize: 16,
                  color: Colors.white,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            flex: 3,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "$exp EXP  |  LEVEL $level",
                  style: TextStyle(
                    fontFamily: 'Orbitron',
                    fontSize: 13,
                    color: color,
                  ),
                ),
                const SizedBox(height: 4),
                LinearProgressIndicator(
                  value: progress,
                  backgroundColor: Colors.grey.shade800,
                  valueColor: AlwaysStoppedAnimation<Color>(color),
                  minHeight: 8,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
