// 📁 lib/widgets/sbs_exp.dart

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../controller/user_controller2.dart';
import '../utils/date_formatter.dart';

/// A full‐screen modal showing side‐by‐side EXP breakdown:
/// • Left pane: “Overall EXP” by category
/// • Right pane: “North‐Star EXP” details
class SideBySideExpModal extends StatelessWidget {
  const SideBySideExpModal({super.key});

  @override
  Widget build(BuildContext context) {
    final userController = context.read<UserController2>();
    final user = userController.user;

    if (user == null) {
      return Scaffold(
        backgroundColor: Colors.black,
        appBar: AppBar(
          backgroundColor: Colors.black,
          title: const Text(
            'EXP Breakdown',
            style: TextStyle(
              fontFamily: 'Bitsumishi',
              color: Colors.cyanAccent,
              fontSize: 20,
            ),
          ),
          elevation: 0,
          centerTitle: true,
        ),
        body: const Center(
          child: Text(
            'No user data available.',
            style: TextStyle(
              color: Colors.white,
              fontFamily: 'Pirulen',
              fontSize: 22,
            ),
          ),
        ),
      );
    }

    // Gather overall EXP categories
    final overallCategories = user.categories.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value)); // highest first

    // Gather NS quest data
    final nsQuest = user.northStarQuest;

    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        title: const Text(
          'EXP Breakdown',
          style: TextStyle(
            fontFamily: 'Bitsumishi',
            color: Colors.cyanAccent,
            fontSize: 20,
          ),
        ),
        elevation: 0,
        centerTitle: true,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          children: [
            // ── Left Pane: Overall EXP ─────────────────────────
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.grey.shade900,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.cyanAccent, width: 2),
                ),
                padding: const EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Overall EXP',
                      style: TextStyle(
                        fontFamily: 'Pirulen',
                        fontSize: 18,
                        color: Colors.cyanAccent,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Total: ${user.exp}',
                      style: const TextStyle(
                        fontFamily: 'Digital-7',
                        fontSize: 32,
                        color: Colors.yellow,
                      ),
                    ),
                    const Divider(color: Colors.white24, height: 24),
                    if (overallCategories.isEmpty)
                      const Text(
                        'No EXP categories.',
                        style: TextStyle(
                          color: Colors.white54,
                          fontFamily: 'Bitsumishi',
                          fontSize: 16,
                        ),
                      )
                    else
                      Expanded(
                        child: ListView.builder(
                          itemCount: overallCategories.length,
                          itemBuilder: (context, index) {
                            final entry = overallCategories[index];
                            return Padding(
                              padding: const EdgeInsets.symmetric(vertical: 4),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    entry.key,
                                    style: const TextStyle(
                                      fontFamily: 'Bitsumishi',
                                      fontSize: 16,
                                      color: Colors.white,
                                    ),
                                  ),
                                  Text(
                                    entry.value.toString(),
                                    style: const TextStyle(
                                      fontFamily: 'Digital-7',
                                      fontSize: 16,
                                      color: Colors.yellow,
                                    ),
                                  ),
                                ],
                              ),
                            );
                          },
                        ),
                      ),
                  ],
                ),
              ),
            ),

            const SizedBox(width: 12),

            // ── Right Pane: North-Star EXP ────────────────────
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.grey.shade900,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.cyanAccent, width: 2),
                ),
                padding: const EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'North-Star EXP',
                      style: TextStyle(
                        fontFamily: 'Pirulen',
                        fontSize: 18,
                        color: Colors.cyanAccent,
                      ),
                    ),
                    if (nsQuest != null && nsQuest.logs.isNotEmpty) ...[
                      const SizedBox(height: 8),
                      Text(
                        'Quest: ${nsQuest.title}',
                        style: const TextStyle(
                          fontFamily: 'Bitsumishi',
                          fontSize: 16,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Total: ${nsQuest.totalExp}',
                        style: const TextStyle(
                          fontFamily: 'Digital-7',
                          fontSize: 32,
                          color: Colors.yellow,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Hours: ${nsQuest.hoursLogged.toStringAsFixed(1)}',
                        style: const TextStyle(
                          fontFamily: 'Bitsumishi',
                          fontSize: 14,
                          color: Colors.white70,
                        ),
                      ),
                      const Divider(color: Colors.white24, height: 24),
                      const Text(
                        'Recent Logs:',
                        style: TextStyle(
                          fontFamily: 'Bitsumishi',
                          fontSize: 16,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Expanded(
                        child: ListView.builder(
                          itemCount: nsQuest.logs.length,
                          itemBuilder: (context, idx) {
                            final log = nsQuest.logs[idx];
                            return Padding(
                              padding: const EdgeInsets.symmetric(vertical: 4),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    DateFormatter.formatDateTime(log.loggedAt),
                                    style: const TextStyle(
                                      fontFamily: 'Digital-7',
                                      fontSize: 12,
                                      color: Colors.grey,
                                    ),
                                  ),
                                  const SizedBox(height: 2),
                                  Text(
                                    log.entryText,
                                    style: const TextStyle(
                                      fontFamily: 'Bitsumishi',
                                      fontSize: 14,
                                      color: Colors.white,
                                    ),
                                  ),
                                  const SizedBox(height: 4),
                                ],
                              ),
                            );
                          },
                        ),
                      ),
                    ] else ...[
                      const SizedBox(height: 16),
                      const Text(
                        'No North-Star quest or logs.',
                        style: TextStyle(
                          fontFamily: 'Bitsumishi',
                          fontSize: 16,
                          color: Colors.white54,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
