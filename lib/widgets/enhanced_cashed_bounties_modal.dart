import 'dart:io';
import 'package:flutter/material.dart';
import '../models/bounty_model.dart';
import '../utils/debug_logger.dart';

/// Represents a completed bounty with photo proof
class CashedBounty {
  final BountyModel bounty;
  final String photoPath;
  final DateTime completedAt;

  CashedBounty({
    required this.bounty,
    required this.photoPath,
    required this.completedAt,
  });
}

/// Enhanced cashed bounties modal with search, filter, and pagination
/// 
/// Features:
/// - Search by bounty description
/// - Filter by category, difficulty, and date range
/// - Infinite scroll with pagination (20 per page)
/// - Photo zoom and full bounty details view
/// - Neon retrofuturistic design
class EnhancedCashedBountiesModal extends StatefulWidget {
  final List<CashedBounty> bounties;
  final Color glowColor;
  final VoidCallback onClose;

  const EnhancedCashedBountiesModal({
    super.key,
    required this.bounties,
    required this.glowColor,
    required this.onClose,
  });

  @override
  State<EnhancedCashedBountiesModal> createState() => _EnhancedCashedBountiesModalState();
}

class _EnhancedCashedBountiesModalState extends State<EnhancedCashedBountiesModal> {
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  
  List<CashedBounty> _filteredBounties = [];
  String _searchQuery = '';
  String _selectedCategory = 'All';
  String _selectedDifficulty = 'All';
  DateTimeRange? _selectedDateRange;
  int _currentPage = 0;
  static const int _itemsPerPage = 20;
  bool _isLoadingMore = false;
  
  @override
  void initState() {
    super.initState();
    _filteredBounties = List.from(widget.bounties);
    _sortBountiesByDate();
    _setupScrollListener();
    DebugLogger.log('EnhancedCashedBountiesModal', 'Initialized with ${widget.bounties.length} bounties');
  }
  
  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }
  
  void _setupScrollListener() {
    _scrollController.addListener(() {
      if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent - 200) {
        _loadMoreBounties();
      }
    });
  }
  
  void _sortBountiesByDate() {
    _filteredBounties.sort((a, b) => b.completedAt.compareTo(a.completedAt));
  }
  
  void _loadMoreBounties() {
    if (_isLoadingMore) return;
    
    final int totalPages = (_filteredBounties.length / _itemsPerPage).ceil();
    if (_currentPage >= totalPages - 1) return;
    
    setState(() {
      _isLoadingMore = true;
      _currentPage++;
    });
    
    // Simulate loading delay
    Future.delayed(Duration(milliseconds: 500), () {
      setState(() {
        _isLoadingMore = false;
      });
    });
    
    DebugLogger.log('EnhancedCashedBountiesModal', 'Loaded page $_currentPage');
  }
  
  void _applyFilters() {
    setState(() {
      _filteredBounties = widget.bounties.where((bounty) {
        // Search filter
        if (_searchQuery.isNotEmpty) {
          if (!bounty.bounty.description.toLowerCase().contains(_searchQuery.toLowerCase())) {
            return false;
          }
        }
        
        // Category filter
        if (_selectedCategory != 'All') {
          if (!bounty.bounty.categories.contains(_selectedCategory)) {
            return false;
          }
        }
        
        // Difficulty filter
        if (_selectedDifficulty != 'All') {
          if (bounty.bounty.difficulty != _selectedDifficulty.toLowerCase()) {
            return false;
          }
        }
        
        // Date range filter
        if (_selectedDateRange != null) {
          final completedDate = DateTime(
            bounty.completedAt.year,
            bounty.completedAt.month,
            bounty.completedAt.day,
          );
          final startDate = DateTime(
            _selectedDateRange!.start.year,
            _selectedDateRange!.start.month,
            _selectedDateRange!.start.day,
          );
          final endDate = DateTime(
            _selectedDateRange!.end.year,
            _selectedDateRange!.end.month,
            _selectedDateRange!.end.day,
          );
          
          if (completedDate.isBefore(startDate) || completedDate.isAfter(endDate)) {
            return false;
          }
        }
        
        return true;
      }).toList();
      
      _sortBountiesByDate();
      _currentPage = 0; // Reset pagination
    });
    
    DebugLogger.log('EnhancedCashedBountiesModal', 
      'Applied filters: ${_filteredBounties.length} bounties match criteria');
  }
  
  List<CashedBounty> _getCurrentPageBounties() {
    final int startIndex = 0;
    final int endIndex = ((_currentPage + 1) * _itemsPerPage).clamp(0, _filteredBounties.length);
    return _filteredBounties.sublist(startIndex, endIndex);
  }
  
  @override
  Widget build(BuildContext context) {
    final currentBounties = _getCurrentPageBounties();
    final screenSize = MediaQuery.of(context).size;

    // Optimize for iPhone 1920x1080 vertical resolution
    final modalWidth = screenSize.width * 0.92; // Use more screen width
    final modalHeight = screenSize.height * 0.88; // Use more screen height

    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
      child: Container(
        width: modalWidth,
        height: modalHeight,
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.95),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: widget.glowColor, width: 2),
          boxShadow: [
            BoxShadow(
              color: widget.glowColor.withValues(alpha: 0.5),
              blurRadius: 20,
              spreadRadius: 4,
            ),
          ],
        ),
        child: Column(
          children: [
            _buildHeader(),
            _buildSearchAndFilters(),
            Expanded(
              child: _filteredBounties.isEmpty
                  ? _buildEmptyState()
                  : _buildBountiesList(currentBounties),
            ),
            _buildPaginationInfo(),
          ],
        ),
      ),
    );
  }
  
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 8), // Reduced padding
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Flexible(
            child: Text(
              'CASHED BOUNTIES',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18, // Slightly smaller for mobile
                fontFamily: 'Pirulen',
                fontWeight: FontWeight.bold,
                shadows: [
                  Shadow(
                    color: widget.glowColor.withValues(alpha: 0.8),
                    offset: const Offset(0, 0),
                    blurRadius: 10,
                  ),
                ],
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
          IconButton(
            icon: const Icon(Icons.close, color: Colors.white, size: 22),
            onPressed: widget.onClose,
            style: IconButton.styleFrom(
              backgroundColor: Colors.red.withValues(alpha: 0.2),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
                side: BorderSide(color: Colors.red, width: 1),
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildSearchAndFilters() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16), // Reduced padding
      child: Column(
        children: [
          // Search bar - more compact
          SizedBox(
            height: 44, // Fixed height to prevent overflow
            child: TextField(
              controller: _searchController,
              onChanged: (value) {
                _searchQuery = value;
                _applyFilters();
              },
              style: const TextStyle(color: Colors.white, fontFamily: 'Bitsumishi', fontSize: 14),
              decoration: InputDecoration(
                hintText: 'Search bounties...',
                hintStyle: TextStyle(color: Colors.white54, fontFamily: 'Bitsumishi', fontSize: 14),
                prefixIcon: Icon(Icons.search, color: widget.glowColor, size: 20),
                filled: true,
                fillColor: Colors.black.withValues(alpha: 0.5),
                contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                  borderSide: BorderSide(color: widget.glowColor),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                  borderSide: BorderSide(color: widget.glowColor.withValues(alpha: 0.5)),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                  borderSide: BorderSide(color: widget.glowColor, width: 2),
                ),
              ),
            ),
          ),

          const SizedBox(height: 12),

          // Filter row - more compact and responsive
          SizedBox(
            height: 48, // Increased height to prevent overflow
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: [
                  SizedBox(
                    width: 140, // Further increased width for "Category"
                    child: _buildCategoryFilter(),
                  ),
                  const SizedBox(width: 8),
                  SizedBox(
                    width: 140, // Further increased width for "Difficulty"
                    child: _buildDifficultyFilter(),
                  ),
                  const SizedBox(width: 8),
                  _buildDateRangeFilter(),
                ],
              ),
            ),
          ),

          const SizedBox(height: 12),
        ],
      ),
    );
  }

  Widget _buildCategoryFilter() {
    final categories = ['All', 'Health', 'Wealth', 'Purpose', 'Connection'];

    return DropdownButtonFormField<String>(
      value: _selectedCategory,
      onChanged: (value) {
        if (value != null) {
          _selectedCategory = value;
          _applyFilters();
        }
      },
      style: const TextStyle(
        color: Colors.white,
        fontFamily: 'Bitsumishi',
        fontSize: 11, // Even smaller font size
      ),
      decoration: InputDecoration(
        labelText: 'Category',
        labelStyle: TextStyle(
          color: widget.glowColor,
          fontFamily: 'Bitsumishi',
          fontSize: 10, // Even smaller label font
        ),
        filled: true,
        fillColor: Colors.black.withValues(alpha: 0.5),
        contentPadding: const EdgeInsets.symmetric(horizontal: 6, vertical: 6), // Further reduced padding
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(6),
          borderSide: BorderSide(color: widget.glowColor.withValues(alpha: 0.5)),
        ),
        isDense: true, // Make it more compact
      ),
      dropdownColor: Colors.black,
      items: categories.map((category) {
        return DropdownMenuItem(
          value: category,
          child: Text(
            category,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 11, // Even smaller dropdown item font
            ),
            overflow: TextOverflow.ellipsis, // Handle overflow
          ),
        );
      }).toList(),
    );
  }

  Widget _buildDifficultyFilter() {
    final difficulties = ['All', 'Easy', 'Medium', 'Hard', 'Epic'];

    return DropdownButtonFormField<String>(
      value: _selectedDifficulty,
      onChanged: (value) {
        if (value != null) {
          _selectedDifficulty = value;
          _applyFilters();
        }
      },
      style: const TextStyle(
        color: Colors.white,
        fontFamily: 'Bitsumishi',
        fontSize: 11, // Even smaller font size
      ),
      decoration: InputDecoration(
        labelText: 'Difficulty',
        labelStyle: TextStyle(
          color: widget.glowColor,
          fontFamily: 'Bitsumishi',
          fontSize: 10, // Even smaller label font
        ),
        filled: true,
        fillColor: Colors.black.withValues(alpha: 0.5),
        contentPadding: const EdgeInsets.symmetric(horizontal: 6, vertical: 6), // Further reduced padding
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(6),
          borderSide: BorderSide(color: widget.glowColor.withValues(alpha: 0.5)),
        ),
        isDense: true, // Make it more compact
      ),
      dropdownColor: Colors.black,
      items: difficulties.map((difficulty) {
        return DropdownMenuItem(
          value: difficulty,
          child: Text(
            difficulty,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 11, // Even smaller dropdown item font
            ),
            overflow: TextOverflow.ellipsis, // Handle overflow
          ),
        );
      }).toList(),
    );
  }

  Widget _buildDateRangeFilter() {
    return IconButton(
      onPressed: () async {
        final DateTimeRange? picked = await showDateRangePicker(
          context: context,
          firstDate: DateTime(2020),
          lastDate: DateTime.now(),
          initialDateRange: _selectedDateRange,
          builder: (context, child) {
            return Theme(
              data: Theme.of(context).copyWith(
                colorScheme: ColorScheme.dark(
                  primary: widget.glowColor,
                  onPrimary: Colors.black,
                  surface: Colors.black,
                  onSurface: Colors.white,
                ),
              ),
              child: child!,
            );
          },
        );

        if (picked != null) {
          _selectedDateRange = picked;
          _applyFilters();
        }
      },
      icon: Icon(
        _selectedDateRange != null ? Icons.date_range : Icons.date_range_outlined,
        color: _selectedDateRange != null ? widget.glowColor : Colors.white54,
      ),
      style: IconButton.styleFrom(
        backgroundColor: Colors.black.withValues(alpha: 0.5),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
          side: BorderSide(color: widget.glowColor.withValues(alpha: 0.5)),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 64,
            color: Colors.white54,
          ),
          const SizedBox(height: 16),
          Text(
            'No bounties found',
            style: TextStyle(
              color: Colors.white54,
              fontSize: 18,
              fontFamily: 'Bitsumishi',
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Try adjusting your search or filters',
            style: TextStyle(
              color: Colors.white38,
              fontSize: 14,
              fontFamily: 'Bitsumishi',
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBountiesList(List<CashedBounty> bounties) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: ListView.builder(
        controller: _scrollController,
        itemCount: bounties.length + (_isLoadingMore ? 1 : 0),
        itemBuilder: (context, index) {
          if (index >= bounties.length) {
            return _buildLoadingIndicator();
          }

          final bounty = bounties[index];
          return _buildBountyCard(bounty);
        },
      ),
    );
  }

  Widget _buildBountyCard(CashedBounty bounty) {
    final file = File(bounty.photoPath);
    final photoExists = file.existsSync();

    return Container(
      margin: const EdgeInsets.only(bottom: 15),
      padding: const EdgeInsets.all(15),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.7),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: bounty.bounty.isEpic ? Colors.purple : widget.glowColor,
          width: bounty.bounty.isEpic ? 2 : 1,
        ),
        boxShadow: [
          BoxShadow(
            color: (bounty.bounty.isEpic ? Colors.purple : widget.glowColor).withValues(alpha: 0.3),
            blurRadius: 10,
            spreadRadius: 2,
          ),
        ],
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Photo with zoom capability
          GestureDetector(
            onTap: () => _showPhotoZoom(bounty.photoPath),
            child: Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.white, width: 1),
              ),
              child: photoExists
                  ? ClipRRect(
                      borderRadius: BorderRadius.circular(7),
                      child: Image.file(
                        file,
                        fit: BoxFit.cover,
                      ),
                    )
                  : Icon(
                      Icons.image_not_supported,
                      color: Colors.grey,
                      size: 32,
                    ),
            ),
          ),

          const SizedBox(width: 15),

          // Bounty details
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Description
                Text(
                  bounty.bounty.description,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontFamily: 'Bitsumishi',
                    fontWeight: FontWeight.bold,
                  ),
                ),

                const SizedBox(height: 8),

                // Categories and EXP
                Wrap(
                  spacing: 6,
                  runSpacing: 4,
                  children: bounty.bounty.categories.map((category) {
                    final exp = bounty.bounty.expPerCategory[category] ?? 0;
                    return Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                      decoration: BoxDecoration(
                        color: _getCategoryColor(category).withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(10),
                        border: Border.all(
                          color: _getCategoryColor(category),
                          width: 1,
                        ),
                      ),
                      child: Text(
                        '$category: ${exp}XP',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                          fontFamily: 'Bitsumishi',
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    );
                  }).toList(),
                ),

                const SizedBox(height: 8),

                // Completion date and difficulty
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Completed: ${_formatDate(bounty.completedAt)}',
                      style: TextStyle(
                        color: Colors.white70,
                        fontSize: 10,
                        fontFamily: 'Bitsumishi',
                      ),
                    ),
                    if (bounty.bounty.isEpic)
                      Row(
                        children: [
                          Icon(Icons.flash_on, color: Colors.purple, size: 16),
                          Text(
                            'EPIC',
                            style: TextStyle(
                              color: Colors.purple,
                              fontSize: 10,
                              fontFamily: 'Pirulen',
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingIndicator() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(widget.glowColor),
        ),
      ),
    );
  }

  Widget _buildPaginationInfo() {
    final totalBounties = _filteredBounties.length;
    final displayedBounties = _getCurrentPageBounties().length;

    return Container(
      padding: const EdgeInsets.fromLTRB(16, 8, 16, 16), // Reduced padding
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Flexible(
            child: Text(
              'Showing $displayedBounties of $totalBounties bounties',
              style: TextStyle(
                color: Colors.white70,
                fontSize: 11, // Smaller font
                fontFamily: 'Bitsumishi',
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
          if (_selectedDateRange != null || _searchQuery.isNotEmpty ||
              _selectedCategory != 'All' || _selectedDifficulty != 'All')
            TextButton(
              onPressed: () {
                setState(() {
                  _searchController.clear();
                  _searchQuery = '';
                  _selectedCategory = 'All';
                  _selectedDifficulty = 'All';
                  _selectedDateRange = null;
                  _currentPage = 0;
                });
                _applyFilters();
              },
              style: TextButton.styleFrom(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                minimumSize: Size.zero,
              ),
              child: Text(
                'Clear Filters',
                style: TextStyle(
                  color: widget.glowColor,
                  fontSize: 11, // Smaller font
                  fontFamily: 'Bitsumishi',
                ),
              ),
            ),
        ],
      ),
    );
  }

  Color _getCategoryColor(String category) {
    switch (category) {
      case 'Health': return Colors.green;
      case 'Wealth': return Colors.amber;
      case 'Purpose': return Colors.purple;
      case 'Connection': return Colors.blue;
      default: return widget.glowColor;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')} '
           '${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
  }

  void _showPhotoZoom(String photoPath) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: Container(
          decoration: BoxDecoration(
            color: Colors.black.withValues(alpha: 0.9),
            borderRadius: BorderRadius.circular(15),
            border: Border.all(color: widget.glowColor, width: 2),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                padding: const EdgeInsets.all(10),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Photo Proof',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontFamily: 'Pirulen',
                      ),
                    ),
                    IconButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: Icon(Icons.close, color: Colors.white),
                    ),
                  ],
                ),
              ),
              Container(
                constraints: BoxConstraints(
                  maxWidth: 400,
                  maxHeight: 400,
                ),
                child: File(photoPath).existsSync()
                    ? Image.file(File(photoPath), fit: BoxFit.contain)
                    : Icon(Icons.image_not_supported, size: 100, color: Colors.grey),
              ),
              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }
}
