// lib/widgets/non_gender_dialog.dart

import 'package:flutter/material.dart';


/// Dialog that explains the non-gender option to users
class NonGenderDialog extends StatelessWidget {
  final VoidCallback onConfirm;

  const NonGenderDialog({
    super.key,
    required this.onConfirm,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.black.withValues(alpha: 0.95),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.8,
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Icon
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.cyanAccent.withValues(alpha: 0.1),
                shape: BoxShape.circle,
                border: Border.all(color: Colors.cyanAccent.withValues(alpha: 0.3), width: 2),
              ),
              child: const Icon(
                Icons.people,
                color: Colors.cyanAccent,
                size: 32,
              ),
            ),
            const SizedBox(height: 16),
            
            // Title
            const Text(
              'Non-Gender Option',
              style: TextStyle(
                color: Colors.cyanAccent,
                fontFamily: 'Pirulen',
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            
            // Explanation
            const Text(
              'If you prefer not to choose a gender, we\'ll assign you a diverse mix of AI coaches.\n\nYou\'ll get a random 50/50 split of male and female coaches across all categories, and your experience will be just as personalized and effective.',
              style: TextStyle(
                color: Colors.white70,
                fontFamily: 'Bitsumishi',
                fontSize: 14,
                height: 1.4,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            
            // Buttons
            Row(
              children: [
                Expanded(
                  child: TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                    child: const Text(
                      'Go Back',
                      style: TextStyle(
                        color: Colors.grey,
                        fontFamily: 'Pirulen',
                        fontSize: 14,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: onConfirm,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.cyanAccent,
                      foregroundColor: Colors.black,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text(
                      'Non-Gender',
                      style: TextStyle(
                        fontFamily: 'Pirulen',
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
