// lib/widgets/auth_signin_modal.dart

import 'package:flutter/material.dart';
import '../services/comprehensive_logging_service.dart';
import 'forgot_password_modal.dart';
import 'saved_accounts_info_box.dart';
import '../services/saved_accounts_service.dart';

/// Modal for user sign-in with username and password
class AuthSignInModal extends StatefulWidget {
  final Function(String username, String password) onSignIn;
  final VoidCallback? onBack;

  const AuthSignInModal({
    super.key,
    required this.onSignIn,
    this.onBack,
  });

  @override
  State<AuthSignInModal> createState() => _AuthSignInModalState();
}

class _AuthSignInModalState extends State<AuthSignInModal> {
  final TextEditingController _usernameController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();

  String? _errorMessage;
  bool _isPasswordVisible = false;
  bool _isLoading = false;
  bool _showSavedAccounts = false;

  @override
  void dispose() {
    _usernameController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  bool get _canSignIn {
    return _usernameController.text.isNotEmpty &&
           _passwordController.text.isNotEmpty &&
           !_isLoading;
  }

  void _showForgotPassword() {
    showDialog(
      context: context,
      builder: (context) => const ForgotPasswordModal(),
    );
  }



  Future<void> _attemptSignIn() async {
    if (!_canSignIn) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // 🛡️ PHASE 3: Implement proper authentication logic
      final username = _usernameController.text.trim();
      final password = _passwordController.text;

      await ComprehensiveLoggingService.logInfo('🔐 Sign in attempt for username: $username');

      // Input validation
      if (username.isEmpty || password.isEmpty) {
        await ComprehensiveLoggingService.logError('❌ Sign in validation failed: empty credentials');
        setState(() {
          _errorMessage = 'Username and password are required.';
          _isLoading = false;
        });
        return;
      }

      // Simulate network delay for realistic UX
      await Future.delayed(const Duration(milliseconds: 500));

      await ComprehensiveLoggingService.logInfo('✅ Sign in credentials validated, proceeding...');

      // Save the username for future convenience
      await SavedAccountsService.saveUsername(username);

      // Call the authentication callback
      widget.onSignIn(username, password);

    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Sign in failed. Please check your credentials and try again.';
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.black.withValues(alpha: 0.95),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      child: Container(
            width: MediaQuery.of(context).size.width * 0.85,
            padding: const EdgeInsets.all(32),
            child: Stack(
          children: [
            // Close button (X) - positioned absolutely in top-left with proper spacing
            if (widget.onBack != null)
              Positioned(
                top: 8,
                left: 8,
                child: GestureDetector(
                  onTap: () {
                    print('❌ Sign In close button pressed');
                    widget.onBack?.call();
                  },
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.black.withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: const Icon(
                      Icons.close,
                      color: Colors.white,
                      size: 18,
                    ),
                  ),
                ),
              ),

            // Main content with top padding to avoid close button
            Padding(
              padding: const EdgeInsets.only(top: 40), // Space for close button
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                // Title - moved to top with minimal spacing
                const Text(
                  'Welcome Back',
                  style: TextStyle(
                    color: Colors.cyanAccent,
                    fontFamily: 'Pirulen',
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
            const SizedBox(height: 8),
            const Text(
              'Sign in to your MXD account',
              style: TextStyle(
                color: Colors.white70,
                fontFamily: 'Bitsumishi',
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),

            // Saved accounts info box
            if (_showSavedAccounts)
              SavedAccountsInfoBox(
                initiallyExpanded: true,
                onAccountSelected: (username) {
                  // Auto-fill the username field when a saved account is selected
                  _usernameController.text = username;
                  setState(() {
                    _showSavedAccounts = false; // Hide after selection
                  });
                },
              ),

            // Username field
            TextField(
              controller: _usernameController,
              onChanged: (value) => setState(() {}), // Trigger rebuild to update button state
              onTap: () {
                setState(() {
                  _showSavedAccounts = !_showSavedAccounts;
                });
              },
              style: const TextStyle(
                color: Colors.white,
                fontFamily: 'Bitsumishi',
                fontSize: 16,
              ),
              decoration: InputDecoration(
                labelText: 'Username',
                labelStyle: const TextStyle(
                  color: Colors.cyanAccent,
                  fontFamily: 'Pirulen',
                  fontSize: 14,
                ),
                hintText: 'Enter your username',
                hintStyle: TextStyle(
                  color: Colors.grey[400],
                  fontFamily: 'Bitsumishi',
                ),
                filled: true,
                fillColor: Colors.grey[900],
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Colors.grey[700]!),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Colors.grey[700]!),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(color: Colors.cyanAccent),
                ),
                errorBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(color: Colors.red),
                ),
                focusedErrorBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(color: Colors.red),
                ),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Password field
            TextField(
              controller: _passwordController,
              obscureText: !_isPasswordVisible,
              onChanged: (value) => setState(() {}), // Trigger rebuild to update button state
              onTap: () {
                setState(() {
                  _showSavedAccounts = false; // Hide saved accounts when password field is tapped
                });
              },
              style: const TextStyle(
                color: Colors.white,
                fontFamily: 'Bitsumishi',
                fontSize: 16,
              ),
              decoration: InputDecoration(
                labelText: 'Password',
                labelStyle: const TextStyle(
                  color: Colors.cyanAccent,
                  fontFamily: 'Pirulen',
                  fontSize: 14,
                ),
                hintText: 'Enter your password',
                hintStyle: TextStyle(
                  color: Colors.grey[400],
                  fontFamily: 'Bitsumishi',
                ),
                filled: true,
                fillColor: Colors.grey[900],
                suffixIcon: IconButton(
                  icon: Icon(
                    _isPasswordVisible ? Icons.visibility : Icons.visibility_off,
                    color: Colors.grey[400],
                  ),
                  onPressed: () {
                    setState(() {
                      _isPasswordVisible = !_isPasswordVisible;
                    });
                  },
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Colors.grey[700]!),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Colors.grey[700]!),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(color: Colors.cyanAccent),
                ),
                errorBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(color: Colors.red),
                ),
                focusedErrorBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(color: Colors.red),
                ),
              ),
            ),
            
            // Error message
            if (_errorMessage != null) ...[
              const SizedBox(height: 16),
              Text(
                _errorMessage!,
                style: const TextStyle(
                  color: Colors.red,
                  fontFamily: 'Bitsumishi',
                  fontSize: 12,
                ),
                textAlign: TextAlign.center,
              ),
            ],
            
            const SizedBox(height: 16),
            
            // Forgot password link
            Align(
              alignment: Alignment.centerRight,
              child: TextButton(
                onPressed: _showForgotPassword,
                child: const Text(
                  'Forgot Password?',
                  style: TextStyle(
                    color: Colors.cyanAccent,
                    fontFamily: 'Bitsumishi',
                    fontSize: 14,
                    decoration: TextDecoration.underline,
                  ),
                ),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Sign in button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _canSignIn ? _attemptSignIn : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: _canSignIn ? Colors.cyanAccent : Colors.grey[700],
                  foregroundColor: Colors.black,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: _isLoading
                    ? const SizedBox(
                        width: 24,
                        height: 24,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.black),
                        ),
                      )
                    : const Text(
                        'Sign In',
                        style: TextStyle(
                          fontFamily: 'Pirulen',
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
              ),
            ),
              ],
            ),
            ),
          ],
        ),
      ),
    );
  }
}
