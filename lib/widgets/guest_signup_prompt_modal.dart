// 📁 lib/widgets/guest_signup_prompt_modal.dart

import 'package:flutter/material.dart';

/// Modal that prompts guest users to create an account
/// 
/// Shows personalized messages based on guest engagement and provides
/// options to sign up or continue browsing as a guest.
class GuestSignupPromptModal extends StatelessWidget {
  final String message;
  final VoidCallback onSignUp;
  final VoidCallback onContinueAsGuest;

  const GuestSignupPromptModal({
    super.key,
    required this.message,
    required this.onSignUp,
    required this.onContinueAsGuest,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: Colors.grey[900],
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: Colors.cyan.withValues(alpha: 0.5),
            width: 2,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.cyan.withValues(alpha: 0.3),
              blurRadius: 20,
              spreadRadius: 2,
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Icon
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: Colors.cyan.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(30),
                border: Border.all(color: Colors.cyan, width: 2),
              ),
              child: const Icon(
                Icons.rocket_launch,
                color: Colors.cyan,
                size: 30,
              ),
            ),
            
            const SizedBox(height: 20),
            
            // Title
            const Text(
              'Ready to Level Up?',
              style: TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontFamily: 'Pirulen',
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            
            const SizedBox(height: 16),
            
            // Message
            Text(
              message,
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 14,
                fontFamily: 'Bitsumishi',
                height: 1.4,
              ),
              textAlign: TextAlign.center,
            ),
            
            const SizedBox(height: 24),
            
            // Benefits list
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Colors.cyan.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'With an account you get:',
                    style: TextStyle(
                      color: Colors.cyan,
                      fontSize: 12,
                      fontFamily: 'Pirulen',
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 12),
                  _buildBenefit('🤖', 'Full AI Coach conversations'),
                  _buildBenefit('📊', 'Progress tracking & analytics'),
                  _buildBenefit('🎯', 'Habit tracking & streaks'),
                  _buildBenefit('💎', 'Bounty Hunter rewards'),
                  _buildBenefit('☁️', 'Cloud sync across devices'),
                ],
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Buttons
            Row(
              children: [
                Expanded(
                  child: TextButton(
                    onPressed: onContinueAsGuest,
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                        side: BorderSide(
                          color: Colors.grey.withValues(alpha: 0.5),
                          width: 1,
                        ),
                      ),
                    ),
                    child: const Text(
                      'Continue Browsing',
                      style: TextStyle(
                        color: Colors.grey,
                        fontFamily: 'Bitsumishi',
                        fontSize: 12,
                      ),
                    ),
                  ),
                ),
                
                const SizedBox(width: 12),
                
                Expanded(
                  flex: 2,
                  child: ElevatedButton(
                    onPressed: onSignUp,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.cyan,
                      foregroundColor: Colors.black,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      elevation: 8,
                    ),
                    child: const Text(
                      'Create Account',
                      style: TextStyle(
                        fontFamily: 'Pirulen',
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            // Small disclaimer
            const Text(
              'Free forever • No credit card required',
              style: TextStyle(
                color: Colors.grey,
                fontSize: 10,
                fontFamily: 'Bitsumishi',
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBenefit(String icon, String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Text(
            icon,
            style: const TextStyle(fontSize: 14),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              text,
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 12,
                fontFamily: 'Bitsumishi',
              ),
            ),
          ),
        ],
      ),
    );
  }
}
