// lib/widgets/training_program_settings_modal.dart

import 'package:flutter/material.dart';
import '../models/training_timer_model.dart';
import '../services/comprehensive_logging_service.dart';
import '../training_tracker/widgets/training_calendar_widget.dart';

/// Modal for customizing training program cycle names
class TrainingProgramSettingsModal extends StatefulWidget {
  final TrainingProgram currentProgram;
  final Color glowColor;
  final Function(TrainingProgram) onProgramChanged;
  final VoidCallback onClose;

  const TrainingProgramSettingsModal({
    super.key,
    required this.currentProgram,
    required this.glowColor,
    required this.onProgramChanged,
    required this.onClose,
  });

  @override
  State<TrainingProgramSettingsModal> createState() => _TrainingProgramSettingsModalState();
}

class _TrainingProgramSettingsModalState extends State<TrainingProgramSettingsModal> {
  late String _selectedType;
  late List<TextEditingController> _customControllers;
  late TextEditingController _programNameController;
  late Map<String, String> _notesTemplates;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _selectedType = widget.currentProgram.programType;
    _programNameController = TextEditingController(text: widget.currentProgram.programType);
    _notesTemplates = Map<String, String>.from(widget.currentProgram.notesTemplates);

    // Initialize custom controllers with current labels
    _customControllers = widget.currentProgram.workoutLabels
        .map((label) => TextEditingController(text: label))
        .toList();

    // Ensure we have at least 2 controllers for custom programs
    while (_customControllers.length < 2) {
      _customControllers.add(TextEditingController());
    }
  }

  @override
  void dispose() {
    for (final controller in _customControllers) {
      controller.dispose();
    }
    _programNameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.black.withValues(alpha: 0.95),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: widget.glowColor.withValues(alpha: 0.3), width: 2),
          boxShadow: [
            BoxShadow(
              color: widget.glowColor.withValues(alpha: 0.2),
              blurRadius: 20,
              spreadRadius: 4,
            ),
          ],
        ),
        child: Column(
          children: [
            _buildHeader(),
            const SizedBox(height: 20),
            Expanded(child: _buildContent()),
            const SizedBox(height: 20),
            _buildActions(),
            _buildAdvancedSchedulingButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Icon(Icons.settings, color: widget.glowColor, size: 20),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            'Training Settings',
            style: TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontFamily: 'Pirulen',
              fontWeight: FontWeight.bold,
            ),
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
          ),
        ),
        const SizedBox(width: 8),
        IconButton(
          onPressed: widget.onClose,
          icon: const Icon(Icons.close, color: Colors.white70, size: 20),
          padding: const EdgeInsets.all(4),
          constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
        ),
      ],
    );
  }

  Widget _buildContent() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildProgramTypeSelector(),
          const SizedBox(height: 20),
          if (_selectedType == 'Custom') ...[
            _buildCustomProgramName(),
            const SizedBox(height: 20),
            _buildCustomLabelsEditor(),
          ] else ...[
            _buildPresetPreview(),
          ],
        ],
      ),
    );
  }

  Widget _buildProgramTypeSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Program Type',
          style: TextStyle(
            color: widget.glowColor,
            fontSize: 16,
            fontFamily: 'Pirulen',
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            _buildTypeChip('Letters', ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J']),
            _buildTypeChip('Days', ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']),
            _buildTypeChip('Numbers', ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10']),
            _buildTypeChip('Custom', ['Custom']),
          ],
        ),
      ],
    );
  }

  Widget _buildTypeChip(String type, List<String> preview) {
    final isSelected = _selectedType == type;
    return GestureDetector(
      onTap: () => setState(() => _selectedType = type),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? widget.glowColor.withValues(alpha: 0.2) : Colors.black.withValues(alpha: 0.3),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected ? widget.glowColor : Colors.white.withValues(alpha: 0.3),
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Column(
          children: [
            Text(
              type,
              style: TextStyle(
                color: isSelected ? widget.glowColor : Colors.white70,
                fontSize: 14,
                fontFamily: 'Pirulen',
                fontWeight: FontWeight.bold,
              ),
            ),
            if (type != 'Custom') ...[
              const SizedBox(height: 4),
              Text(
                preview.take(3).join(' • '),
                style: TextStyle(
                  color: Colors.white60,
                  fontSize: 10,
                  fontFamily: 'Digital-7',
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildCustomProgramName() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Program Name',
          style: TextStyle(
            color: widget.glowColor,
            fontSize: 14,
            fontFamily: 'Pirulen',
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: _programNameController,
          style: const TextStyle(color: Colors.white, fontFamily: 'Bitsumishi'),
          decoration: InputDecoration(
            hintText: 'Enter program name (e.g., "My Workout")',
            hintStyle: const TextStyle(color: Colors.white60),
            filled: true,
            fillColor: Colors.black.withValues(alpha: 0.3),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.white.withValues(alpha: 0.3)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: widget.glowColor, width: 2),
            ),
          ),
          maxLength: 30,
        ),
      ],
    );
  }

  Widget _buildCustomLabelsEditor() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Workout Labels',
              style: TextStyle(
                color: widget.glowColor,
                fontSize: 14,
                fontFamily: 'Pirulen',
                fontWeight: FontWeight.bold,
              ),
            ),
            const Spacer(),
            IconButton(
              onPressed: _addCustomLabel,
              icon: Icon(Icons.add, color: widget.glowColor, size: 20),
            ),
          ],
        ),
        const SizedBox(height: 8),
        ..._customControllers.asMap().entries.map((entry) {
          final index = entry.key;
          final controller = entry.value;
          return _buildCustomLabelField(index, controller);
        }),
      ],
    );
  }

  Widget _buildCustomLabelField(int index, TextEditingController controller) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: controller,
                  style: const TextStyle(color: Colors.white, fontFamily: 'Bitsumishi'),
                  decoration: InputDecoration(
                    hintText: 'Workout ${index + 1} (e.g., "Push", "Pull", "Legs")',
                    hintStyle: const TextStyle(color: Colors.white60),
                    filled: true,
                    fillColor: Colors.black.withValues(alpha: 0.3),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.white.withValues(alpha: 0.3)),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: widget.glowColor, width: 2),
                    ),
                  ),
                  maxLength: 30,
                ),
              ),
              if (_customControllers.length > 2) ...[
                const SizedBox(width: 8),
                IconButton(
                  onPressed: () => _removeCustomLabel(index),
                  icon: const Icon(Icons.remove_circle, color: Colors.red, size: 20),
                ),
              ],
            ],
          ),
          const SizedBox(height: 8),
          ElevatedButton(
            onPressed: () => _showNotesTemplateModal(controller.text.trim().isNotEmpty ? controller.text.trim() : 'Workout ${index + 1}'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.black.withValues(alpha: 0.4),
              foregroundColor: widget.glowColor.withValues(alpha: 0.8),
              side: BorderSide(color: widget.glowColor.withValues(alpha: 0.4), width: 1),
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
            ),
            child: Text(
              'Save Notes Template',
              style: TextStyle(
                fontFamily: 'Bitsumishi',
                fontSize: 12,
                color: widget.glowColor.withValues(alpha: 0.9),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPresetPreview() {
    final program = _getPreviewProgram();
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Preview',
          style: TextStyle(
            color: widget.glowColor,
            fontSize: 14,
            fontFamily: 'Pirulen',
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.black.withValues(alpha: 0.3),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.white.withValues(alpha: 0.3)),
          ),
          child: Wrap(
            spacing: 8,
            children: program.workoutLabels.map((label) => Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: widget.glowColor.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(color: widget.glowColor.withValues(alpha: 0.5)),
              ),
              child: Text(
                label,
                style: TextStyle(
                  color: widget.glowColor,
                  fontSize: 12,
                  fontFamily: 'Pirulen',
                  fontWeight: FontWeight.bold,
                ),
              ),
            )).toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildActions() {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton(
            onPressed: widget.onClose,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.grey.withValues(alpha: 0.2),
              foregroundColor: Colors.white70,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
            child: const Text(
              'Cancel',
              style: TextStyle(fontFamily: 'Pirulen', fontSize: 14),
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: ElevatedButton(
            onPressed: _isLoading ? null : _saveProgram,
            style: ElevatedButton.styleFrom(
              backgroundColor: widget.glowColor.withValues(alpha: 0.2),
              foregroundColor: widget.glowColor,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
            child: _isLoading
                ? SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(
                      color: widget.glowColor,
                      strokeWidth: 2,
                    ),
                  )
                : const Text(
                    'SAVE\nPROGRAM',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontFamily: 'Pirulen',
                      fontSize: 12,
                      height: 1.1,
                    ),
                  ),
          ),
        ),
      ],
    );
  }

  Widget _buildAdvancedSchedulingButton() {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.only(top: 16),
      child: ElevatedButton(
        onPressed: _openAdvancedScheduling,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.black.withValues(alpha: 0.4),
          foregroundColor: widget.glowColor.withValues(alpha: 0.9),
          side: BorderSide(color: widget.glowColor.withValues(alpha: 0.6), width: 1),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          padding: const EdgeInsets.symmetric(vertical: 16),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.calendar_month,
              color: widget.glowColor.withValues(alpha: 0.9),
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              'Advanced Scheduling',
              style: TextStyle(
                fontFamily: 'Pirulen',
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: widget.glowColor.withValues(alpha: 0.9),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _openAdvancedScheduling() async {
    await ComprehensiveLoggingService.logInfo('📅 Opening Advanced Scheduling from Training Settings');

    if (!mounted) return;

    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (dialogContext) => TrainingCalendarWidget(
        glowColor: widget.glowColor,
        onClose: () {
          Navigator.of(dialogContext).pop();
          ComprehensiveLoggingService.logInfo('📅 Advanced Scheduling closed');
        },
        onSessionPlanned: (session) async {
          await ComprehensiveLoggingService.logInfo('📅 Session planned from Advanced Scheduling: ${session.workoutType}');
        },
        onRestDayPlanned: (restDay) async {
          await ComprehensiveLoggingService.logInfo('📅 Rest day planned from Advanced Scheduling: ${restDay.type}');
        },
        programToLoad: _getPreviewProgram(), // Pass program for manual loading
      ),
    );
  }

  void _addCustomLabel() {
    if (_customControllers.length < 21) {
      setState(() {
        _customControllers.add(TextEditingController());
      });
    }
  }

  void _removeCustomLabel(int index) {
    if (_customControllers.length > 2) {
      setState(() {
        _customControllers[index].dispose();
        _customControllers.removeAt(index);
      });
    }
  }

  void _showNotesTemplateModal(String labelName) {
    final currentNotes = _notesTemplates[labelName] ?? '';
    final notesController = TextEditingController(text: currentNotes);

    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: Colors.black.withValues(alpha: 0.95),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        child: Container(
          width: MediaQuery.of(context).size.width * 0.85,
          height: MediaQuery.of(context).size.height * 0.6,
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            border: Border.all(color: widget.glowColor.withValues(alpha: 0.3), width: 2),
            boxShadow: [
              BoxShadow(
                color: widget.glowColor.withValues(alpha: 0.2),
                blurRadius: 20,
                spreadRadius: 4,
              ),
            ],
          ),
          child: Column(
            children: [
              // Header
              Text(
                '$labelName Notes',
                style: TextStyle(
                  color: widget.glowColor,
                  fontSize: 18,
                  fontFamily: 'Pirulen',
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 20),

              // Notes input
              Expanded(
                child: TextField(
                  controller: notesController,
                  style: const TextStyle(color: Colors.white, fontFamily: 'Bitsumishi'),
                  decoration: InputDecoration(
                    hintText: 'Enter notes template for $labelName workouts...',
                    hintStyle: const TextStyle(color: Colors.white60),
                    filled: true,
                    fillColor: Colors.black.withValues(alpha: 0.3),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.white.withValues(alpha: 0.3)),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: widget.glowColor, width: 2),
                    ),
                  ),
                  maxLines: null,
                  expands: true,
                  textAlignVertical: TextAlignVertical.top,
                  maxLength: 1500,
                ),
              ),

              const SizedBox(height: 20),

              // Save button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () {
                    setState(() {
                      final notes = notesController.text.trim();
                      if (notes.isEmpty) {
                        _notesTemplates.remove(labelName);
                      } else {
                        _notesTemplates[labelName] = notes;
                      }
                    });
                    Navigator.of(context).pop();
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: widget.glowColor.withValues(alpha: 0.2),
                    foregroundColor: widget.glowColor,
                    side: BorderSide(color: widget.glowColor.withValues(alpha: 0.6), width: 1),
                    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                  child: const Text(
                    'Save',
                    style: TextStyle(
                      fontFamily: 'Pirulen',
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  TrainingProgram _getPreviewProgram() {
    switch (_selectedType) {
      case 'Letters':
        return TrainingProgram.letters();
      case 'Days':
        return TrainingProgram.days();
      case 'Numbers':
        return TrainingProgram.numbers();
      case 'Custom':
        final labels = _customControllers
            .map((c) => c.text.trim())
            .where((text) => text.isNotEmpty)
            .toList();
        return TrainingProgram.custom(
          customLabels: labels.isNotEmpty ? labels : ['Custom 1'],
          customName: _programNameController.text.trim(),
          notesTemplates: _notesTemplates,
        );
      default:
        return TrainingProgram.letters();
    }
  }

  Future<void> _saveProgram() async {
    setState(() => _isLoading = true);
    
    try {
      final newProgram = _getPreviewProgram();
      await ComprehensiveLoggingService.logInfo('⚙️ TrainingProgram: Saving new program: ${newProgram.programType}');
      
      widget.onProgramChanged(newProgram);
      widget.onClose();
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ TrainingProgram: Failed to save program: $e');
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to save training program'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }
}
