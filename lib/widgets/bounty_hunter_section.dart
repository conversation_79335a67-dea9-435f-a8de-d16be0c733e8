import 'package:flutter/material.dart';
import '../models/user_model.dart';
import '../models/bounty_model.dart';
import '../services/reward_engine.dart';
import '../widgets/spinner_wheel.dart';
import '../widgets/bounty_card.dart';
import '../widgets/enhanced_cashed_bounties_modal.dart';
import '../widgets/fireworks_animation.dart';
import '../services/camera_service.dart';
import '../services/advanced_bounty_generator.dart';
import '../services/bounty_notification_service.dart';
import '../services/bounty_analytics_service.dart';
import '../services/notification_manager.dart';
import '../utils/debug_logger.dart';

/// Bounty Hunter section widget for the home screen
/// 
/// Features:
/// - Daily EXP tracking display
/// - Spinner wheel integration
/// - Active bonus categories display with neon glow
/// - Progress tracking toward next spinner unlock
/// - Neon rainbow + black + retrofuturistic visual theme
class BountyHunterSection extends StatefulWidget {
  final User user;
  final Function(User) onUserUpdate;
  
  const BountyHunterSection({
    super.key,
    required this.user,
    required this.onUserUpdate,
  });

  @override
  State<BountyHunterSection> createState() => _BountyHunterSectionState();
}

class _BountyHunterSectionState extends State<BountyHunterSection> {
  late User _currentUser;
  BountyModel? _dailyBounty;
  List<CashedBounty> _cashedBounties = [];
  final NotificationManager _notificationManager = NotificationManager();
  bool _notificationSystemReady = false;

  @override
  void initState() {
    super.initState();
    _currentUser = widget.user;

    // Defer ALL state updates to avoid setState during build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _checkDailyReset();
        _selectDailyBounty();
        _loadCashedBounties();
        _initializeNotificationSystem();
      }
    });
  }

  /// Initialize the notification system
  Future<void> _initializeNotificationSystem() async {
    try {
      DebugLogger.log('BountyHunterSection', '🔔 Initializing notification system...');

      final success = await _notificationManager.initialize();
      setState(() {
        _notificationSystemReady = success;
      });

      if (success) {
        DebugLogger.log('BountyHunterSection', '✅ Notification system ready');

        // Schedule optimal notification for user if they don't have a recent one
        if (BountyNotificationService.shouldSendNotification(_currentUser)) {
          await _notificationManager.scheduleOptimalBountyNotification(
            user: _currentUser,
            bounty: _dailyBounty,
          );
        }
      } else {
        DebugLogger.warn('BountyHunterSection', '❌ Notification system failed to initialize');
      }
    } catch (e, stackTrace) {
      DebugLogger.error('BountyHunterSection', 'Error initializing notification system', e, stackTrace);
      setState(() {
        _notificationSystemReady = false;
      });
    }
  }

  /// Send congratulations notification for completed bounty
  Future<void> _sendCongratulationsNotification(BountyModel completedBounty) async {
    try {
      if (!_notificationSystemReady) return;

      final congratsMessage = 'Congratulations ${_currentUser.username}! You completed: ${completedBounty.description}. Ready for your next challenge?';

      // Schedule next bounty notification
      await _notificationManager.scheduleOptimalBountyNotification(
        user: _currentUser,
        bounty: _dailyBounty,
        customMessage: congratsMessage,
      );

      DebugLogger.log('BountyHunterSection', 'Congratulations notification scheduled');

    } catch (e, stackTrace) {
      DebugLogger.error('BountyHunterSection', 'Error sending congratulations notification', e, stackTrace);
    }
  }
  
  @override
  void didUpdateWidget(BountyHunterSection oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.user != widget.user) {
      _currentUser = widget.user;
      // Defer daily reset check to avoid setState during build
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          _checkDailyReset();
        }
      });
    }
  }
  
  void _checkDailyReset() {
    if (RewardEngine.instance.shouldResetDailyExp(_currentUser)) {
      DebugLogger.log('BountyHunterSection', 'Performing daily reset');
      final resetUser = RewardEngine.instance.resetDailyExp(_currentUser);

      // Always defer user updates to avoid setState during build
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          setState(() {
            _currentUser = resetUser;
          });
          widget.onUserUpdate(resetUser);
        }
      });
    }
  }
  
  void _updateUser(User updatedUser) {
    setState(() {
      _currentUser = updatedUser;
    });
    widget.onUserUpdate(updatedUser);
  }

  /// Select a personalized daily bounty using advanced AI-like generation
  void _selectDailyBounty() {
    // Get completed bounty IDs
    final completedBountyIds = _cashedBounties.map((cashed) => cashed.bounty.id).toList();

    // Use advanced bounty generator for personalized selection
    final selectedBounty = AdvancedBountyGenerator.generateDailyBounty(_currentUser, completedBountyIds);

    setState(() {
      _dailyBounty = selectedBounty;
    });

    if (selectedBounty != null) {
      DebugLogger.log('BountyHunterSection',
        'AI-selected personalized bounty: ${selectedBounty.description} (${selectedBounty.difficulty})');

      // Check if user should receive notification about new bounty
      if (BountyNotificationService.shouldSendNotification(_currentUser)) {
        final message = BountyNotificationService.generateNotificationMessage(_currentUser, selectedBounty);
        DebugLogger.log('BountyHunterSection', 'Notification message: $message');
        // TODO: Send actual notification
      }
    } else {
      DebugLogger.warn('BountyHunterSection', 'No suitable bounty found for user');
    }
  }

  /// Load cashed bounties from storage (placeholder for now)
  void _loadCashedBounties() {
    // TODO: Load from actual storage
    setState(() {
      _cashedBounties = [];
    });
    DebugLogger.log('BountyHunterSection', 'Loaded ${_cashedBounties.length} cashed bounties');
  }

  /// Complete a bounty with photo proof
  void _completeBounty(String photoPath) async {
    if (_dailyBounty == null) return;

    DebugLogger.log('BountyHunterSection', 'Completing bounty: ${_dailyBounty!.description}');

    // Create cashed bounty
    final cashedBounty = CashedBounty(
      bounty: _dailyBounty!,
      photoPath: photoPath,
      completedAt: DateTime.now(),
    );

    // Add to cashed bounties
    setState(() {
      _cashedBounties.insert(0, cashedBounty); // Most recent at top
    });

    // Award EXP for each category
    var updatedUser = _currentUser;
    for (final entry in _dailyBounty!.expPerCategory.entries) {
      updatedUser = updatedUser.copyWithAddedExp(
        entry.key,
        entry.value,
        'Bounty: ${_dailyBounty!.description}'
      );
    }

    // Check if bounty completion triggers spinner unlock
    final totalBountyExp = _dailyBounty!.expPerCategory.values.fold(0, (sum, exp) => sum + exp);
    if (updatedUser.dailyExpTotal >= 40 && updatedUser.availableSpinnerPlays > _currentUser.availableSpinnerPlays) {
      DebugLogger.log('BountyHunterSection', 'Bounty completion (${totalBountyExp}XP) triggered spinner unlock!');
      // TODO: Show spinner unlock notification
    }

    // Play success sound and show celebration
    if (_dailyBounty!.isEpic) {
      _showEpicCelebration();
    } else {
      _showBountyCelebration();
    }

    // Update user and clear daily bounty
    _updateUser(updatedUser);
    setState(() {
      _dailyBounty = null;
    });

    // Select new bounty for tomorrow or immediately
    _selectDailyBounty();

    // Send congratulations notification if system is ready
    if (_notificationSystemReady && _dailyBounty != null) {
      _sendCongratulationsNotification(_dailyBounty!);
    }
  }

  /// Show epic bounty celebration with fireworks and lightning
  void _showEpicCelebration() {
    DebugLogger.log('BountyHunterSection', 'Showing epic bounty celebration');
    // TODO: Implement fireworks + lightning animation
    // TODO: Play epic_success.mp3

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => _EpicCelebrationModal(
        bounty: _dailyBounty!,
        onClose: () => Navigator.of(context).pop(),
      ),
    );
  }

  /// Show regular bounty celebration
  void _showBountyCelebration() {
    DebugLogger.log('BountyHunterSection', 'Showing bounty celebration');

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => _BountyCelebrationModal(
        bounty: _dailyBounty!,
        onClose: () => Navigator.of(context).pop(),
      ),
    );
  }

  /// Show cashed bounties modal with analytics
  void _showCashedBounties() {
    // Calculate analytics before showing modal
    final stats = BountyAnalyticsService.calculateBountyStats(_currentUser, _cashedBounties);
    final insights = BountyAnalyticsService.generateInsights(stats);

    DebugLogger.log('BountyHunterSection',
      'Bounty Analytics: ${stats.totalBountiesCompleted} completed, ${stats.currentStreak} day streak');

    for (final insight in insights) {
      DebugLogger.log('BountyHunterSection', 'Insight: $insight');
    }

    showDialog(
      context: context,
      builder: (context) => EnhancedCashedBountiesModal(
        bounties: _cashedBounties,
        glowColor: Colors.cyan,
        onClose: () => Navigator.of(context).pop(),
      ),
    );
  }
  
  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.fromLTRB(16, 32, 16, 16), // 15% more top margin for separation
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.8),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(
          color: Colors.cyan.withValues(alpha: 0.6),
          width: 2,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.cyan.withValues(alpha: 0.3),
            blurRadius: 15,
            spreadRadius: 2,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center, // Center align all content
        children: [
          _buildHeader(),
          const SizedBox(height: 15),
          _buildDailyExpDisplay(),
          const SizedBox(height: 15),
          _buildActiveBonusCategories(),
          const SizedBox(height: 15),
          _buildDailyBountySection(),
          const SizedBox(height: 15),
          _buildSpinnerSection(),
          const SizedBox(height: 15),
          _buildCashedBountiesButton(),
        ],
      ),
    );
  }
  
  Widget _buildHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center, // Center align the header
      children: [
        Container(
          width: 37, // 33% larger (28 * 1.33)
          height: 37,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            boxShadow: [
              BoxShadow(
                color: Colors.amber.withValues(alpha: 0.6),
                blurRadius: 8,
                spreadRadius: 2,
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Image.asset(
              'assets/images/bounty_icon.png',
              width: 37,
              height: 37,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                print('🚨 BountyHunterIcon: Failed to load bounty_icon.png - $error');
                // Fallback to a simple icon if image fails to load
                return Container(
                  width: 37,
                  height: 37,
                  decoration: BoxDecoration(
                    color: Colors.amber,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.monetization_on,
                    color: Colors.black,
                    size: 24,
                  ),
                );
              },
            ),
          ),
        ),
        const SizedBox(width: 15), // Slightly more spacing
        Flexible( // Allow text to shrink if needed
          child: Text(
            'BOUNTY\nHUNTER',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Colors.white,
              fontSize: 24, // Slightly smaller to fit two lines
              fontFamily: 'Pirulen',
              fontWeight: FontWeight.bold,
              height: 1.1, // Tighter line spacing
              shadows: [
                Shadow(
                  color: Colors.cyan.withValues(alpha: 0.8),
                  offset: const Offset(0, 0),
                  blurRadius: 10,
                ),
              ],
            ),
            overflow: TextOverflow.ellipsis, // Handle overflow gracefully
          ),
        ),
      ],
    );
  }
  
  Widget _buildDailyExpDisplay() {
    return Container(
      padding: const EdgeInsets.all(15),
      decoration: BoxDecoration(
        color: Colors.purple.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          color: Colors.purple.withValues(alpha: 0.6),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Daily EXP',
                style: TextStyle(
                  color: Colors.white70,
                  fontSize: 12,
                  fontFamily: 'Bitsumishi',
                ),
              ),
              Text(
                '${_currentUser.dailyExpTotal}',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 24,
                  fontFamily: 'Digital-7',
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                'Spinner Plays',
                style: TextStyle(
                  color: Colors.white70,
                  fontSize: 12,
                  fontFamily: 'Bitsumishi',
                ),
              ),
              Text(
                '${_currentUser.availableSpinnerPlays}',
                style: TextStyle(
                  color: Colors.amber,
                  fontSize: 24,
                  fontFamily: 'Digital-7',
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
  
  Widget _buildActiveBonusCategories() {
    if (_currentUser.activeBonusCategories.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(15),
        decoration: BoxDecoration(
          color: Colors.grey.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(10),
          border: Border.all(
            color: Colors.grey.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Row(
          children: [
            Icon(
              Icons.info_outline,
              color: Colors.grey,
              size: 20,
            ),
            const SizedBox(width: 10),
            Text(
              'No active bonus categories',
              style: TextStyle(
                color: Colors.grey,
                fontSize: 14,
                fontFamily: 'Bitsumishi',
              ),
            ),
          ],
        ),
      );
    }
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Active Bonus Categories (25% chance)',
          style: TextStyle(
            color: Colors.white,
            fontSize: 14,
            fontFamily: 'Bitsumishi',
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _currentUser.activeBonusCategories.map((category) {
            return _buildBonusCategoryChip(category);
          }).toList(),
        ),
      ],
    );
  }
  
  Widget _buildBonusCategoryChip(String category) {
    final colors = {
      'Health': Colors.green,
      'Wealth': Colors.amber,
      'Purpose': Colors.purple,
      'Connection': Colors.blue,
    };
    
    final color = colors[category] ?? Colors.cyan;
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(
          color: color.withValues(alpha: 0.8),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: color.withValues(alpha: 0.4),
            blurRadius: 8,
            spreadRadius: 1,
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.flash_on,
            color: color,
            size: 16,
          ),
          const SizedBox(width: 4),
          Text(
            category,
            style: TextStyle(
              color: Colors.white,
              fontSize: 12,
              fontFamily: 'Bitsumishi',
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildSpinnerSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center, // Center align the section
      children: [
        // Bounty Spinner Title
        Text(
          'Bounty Spinner',
          style: TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontFamily: 'Pirulen',
            fontWeight: FontWeight.bold,
            shadows: [
              Shadow(
                color: Colors.amber.withValues(alpha: 0.8),
                offset: const Offset(0, 0),
                blurRadius: 10,
              ),
            ],
          ),
        ),
        const SizedBox(height: 10),
        Container(
          padding: const EdgeInsets.all(15),
          decoration: BoxDecoration(
            color: Colors.black.withValues(alpha: 0.5),
            borderRadius: BorderRadius.circular(10),
            border: Border.all(
              color: Colors.amber.withValues(alpha: 0.6),
              width: 1,
            ),
          ),
          child: SpinnerWheel(
            user: _currentUser,
            onUserUpdate: _updateUser,
            onSpinComplete: () {
              DebugLogger.log('BountyHunterSection', 'Spinner play completed');
            },
          ),
        ),
      ],
    );
  }

  Widget _buildDailyBountySection() {
    if (_dailyBounty == null) {
      return Container(
        padding: const EdgeInsets.all(15),
        decoration: BoxDecoration(
          color: Colors.grey.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(10),
          border: Border.all(
            color: Colors.grey.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Row(
          children: [
            Icon(
              Icons.info_outline,
              color: Colors.grey,
              size: 20,
            ),
            const SizedBox(width: 10),
            Text(
              'No daily bounty available',
              style: TextStyle(
                color: Colors.grey,
                fontSize: 14,
                fontFamily: 'Bitsumishi',
              ),
            ),
          ],
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.center, // Center align the section
      children: [
        Text(
          'Daily Bounty',
          style: TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontFamily: 'Pirulen',
            fontWeight: FontWeight.bold,
            shadows: [
              Shadow(
                color: Colors.amber.withValues(alpha: 0.8),
                offset: const Offset(0, 0),
                blurRadius: 10,
              ),
            ],
          ),
        ),
        const SizedBox(height: 10),
        BountyCard(
          bounty: _dailyBounty!,
          glowColor: Colors.amber,
          onCashIn: () => _showPhotoCapture(),
          showCashIn: true,
        ),
      ],
    );
  }

  Widget _buildCashedBountiesButton() {
    return GestureDetector(
      onTap: _showCashedBounties,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(25),
          gradient: LinearGradient(
            colors: [
              Colors.purple.withValues(alpha: 0.8),
              Colors.blue.withValues(alpha: 0.6),
            ],
          ),
          border: Border.all(
            color: Colors.purple,
            width: 2,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.purple.withValues(alpha: 0.4),
              blurRadius: 15,
              spreadRadius: 3,
            ),
          ],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.history,
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              'CASHED BOUNTIES (${_cashedBounties.length})',
              style: TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontFamily: 'Pirulen',
                fontWeight: FontWeight.bold,
                shadows: [
                  Shadow(
                    color: Colors.black.withValues(alpha: 0.8),
                    offset: const Offset(1, 1),
                    blurRadius: 2,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Show photo capture interface for bounty completion
  void _showPhotoCapture() {
    showDialog(
      context: context,
      builder: (context) => _PhotoCaptureModal(
        bounty: _dailyBounty!,
        onPhotoTaken: (photoPath) {
          Navigator.of(context).pop();
          _completeBounty(photoPath);
        },
        onCancel: () => Navigator.of(context).pop(),
      ),
    );
  }
}

/// Photo capture modal for bounty completion
class _PhotoCaptureModal extends StatelessWidget {
  final BountyModel bounty;
  final Function(String) onPhotoTaken;
  final VoidCallback onCancel;

  const _PhotoCaptureModal({
    required this.bounty,
    required this.onPhotoTaken,
    required this.onCancel,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9, // Responsive width
        constraints: BoxConstraints(
          maxWidth: 350, // Maximum width to prevent overflow
        ),
        padding: const EdgeInsets.all(16), // Reduced padding
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.9),
          borderRadius: BorderRadius.circular(15),
          border: Border.all(
            color: Colors.amber,
            width: 2,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.amber.withValues(alpha: 0.5),
              blurRadius: 20,
              spreadRadius: 5,
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.camera_alt,
              size: 60,
              color: Colors.amber,
            ),
            const SizedBox(height: 15),
            Text(
              'PHOTO PROOF REQUIRED',
              textAlign: TextAlign.center,
              style: TextStyle(
                color: Colors.amber,
                fontSize: 16, // Reduced from 18
                fontFamily: 'Pirulen',
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8), // Reduced spacing
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: Text(
                bounty.description,
                textAlign: TextAlign.center,
                maxLines: 3, // Limit lines to prevent overflow
                overflow: TextOverflow.ellipsis,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 12, // Reduced from 14
                  fontFamily: 'Bitsumishi',
                ),
              ),
            ),
            const SizedBox(height: 20),
            // First row: Camera and Gallery buttons
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () async {
                      final photoPath = await CameraService.capturePhotoProof(
                        bountyId: bounty.id,
                      );
                      if (photoPath != null) {
                        onPhotoTaken(photoPath);
                      }
                    },
                    icon: Icon(Icons.camera, size: 18),
                    label: Text(
                      'CAMERA',
                      style: TextStyle(
                        fontSize: 12,
                        fontFamily: 'Pirulen',
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.amber,
                      foregroundColor: Colors.black,
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () async {
                      final photoPath = await CameraService.selectPhotoFromGallery(
                        bountyId: bounty.id,
                      );
                      if (photoPath != null) {
                        onPhotoTaken(photoPath);
                      }
                    },
                    icon: Icon(Icons.photo_library, size: 18),
                    label: Text(
                      'GALLERY',
                      style: TextStyle(
                        fontSize: 12,
                        fontFamily: 'Pirulen',
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            // Second row: Cancel button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: onCancel,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: Text(
                  'CANCEL',
                  style: TextStyle(
                    fontSize: 12,
                    fontFamily: 'Pirulen',
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Epic bounty celebration modal with fireworks and lightning
class _EpicCelebrationModal extends StatelessWidget {
  final BountyModel bounty;
  final VoidCallback onClose;

  const _EpicCelebrationModal({
    required this.bounty,
    required this.onClose,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.9),
          borderRadius: BorderRadius.circular(15),
          border: Border.all(
            color: Colors.purple,
            width: 3,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.purple.withValues(alpha: 0.8),
              blurRadius: 30,
              spreadRadius: 10,
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Fireworks animation
            SizedBox(
              width: 150,
              height: 150,
              child: Stack(
                children: [
                  FireworksAnimation(
                    width: 150,
                    height: 150,
                    duration: Duration(seconds: 4),
                  ),
                  Center(
                    child: Icon(
                      Icons.celebration,
                      size: 60,
                      color: Colors.purple,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 15),
            Text(
              'EPIC BOUNTY COMPLETE!',
              style: TextStyle(
                color: Colors.purple,
                fontSize: 24,
                fontFamily: 'Pirulen',
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 10),
            Text(
              bounty.description,
              textAlign: TextAlign.center,
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontFamily: 'Bitsumishi',
              ),
            ),
            const SizedBox(height: 15),
            Text(
              'EXP Earned: ${bounty.expPerCategory.values.fold(0, (sum, exp) => sum + exp)}',
              style: TextStyle(
                color: Colors.amber,
                fontSize: 18,
                fontFamily: 'Digital-7',
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: onClose,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.purple,
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(horizontal: 30, vertical: 15),
              ),
              child: Text('AMAZING!'),
            ),
          ],
        ),
      ),
    );
  }
}

/// Regular bounty celebration modal
class _BountyCelebrationModal extends StatelessWidget {
  final BountyModel bounty;
  final VoidCallback onClose;

  const _BountyCelebrationModal({
    required this.bounty,
    required this.onClose,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.9),
          borderRadius: BorderRadius.circular(15),
          border: Border.all(
            color: Colors.green,
            width: 2,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.green.withValues(alpha: 0.5),
              blurRadius: 20,
              spreadRadius: 5,
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.check_circle,
              size: 60,
              color: Colors.green,
            ),
            const SizedBox(height: 15),
            Text(
              'BOUNTY COMPLETE!',
              style: TextStyle(
                color: Colors.green,
                fontSize: 20,
                fontFamily: 'Pirulen',
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 10),
            Text(
              bounty.description,
              textAlign: TextAlign.center,
              style: TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontFamily: 'Bitsumishi',
              ),
            ),
            const SizedBox(height: 15),
            Text(
              'EXP Earned: ${bounty.expPerCategory.values.fold(0, (sum, exp) => sum + exp)}',
              style: TextStyle(
                color: Colors.amber,
                fontSize: 16,
                fontFamily: 'Digital-7',
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: onClose,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
              ),
              child: Text('CONTINUE'),
            ),
          ],
        ),
      ),
    );
  }
}
