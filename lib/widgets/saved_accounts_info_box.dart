// lib/widgets/saved_accounts_info_box.dart

import 'package:flutter/material.dart';
import '../theme/colors.dart';
import '../services/saved_accounts_service.dart';
import '../services/comprehensive_logging_service.dart';

/// Info box widget that displays saved accounts on the device.
/// 
/// This widget shows a dropdown-style info box similar to the password
/// requirements checklist, with green indicators for saved accounts.
/// It maintains visual consistency with the MOL design system.
/// 
/// Example usage:
/// ```dart
/// SavedAccountsInfoBox(
///   onAccountSelected: (username) {
///     // Handle account selection
///   },
/// )
/// ```
class SavedAccountsInfoBox extends StatefulWidget {
  /// Callback when a saved account is tapped (optional)
  final Function(String username)? onAccountSelected;
  
  /// Whether to show the info box in expanded state initially
  final bool initiallyExpanded;

  const SavedAccountsInfoBox({
    super.key,
    this.onAccountSelected,
    this.initiallyExpanded = false,
  });

  @override
  State<SavedAccountsInfoBox> createState() => _SavedAccountsInfoBoxState();
}

class _SavedAccountsInfoBoxState extends State<SavedAccountsInfoBox> 
    with SingleTickerProviderStateMixin {
  
  List<String> _savedAccounts = [];
  bool _isLoading = true;
  bool _isExpanded = false;
  late AnimationController _animationController;
  late Animation<double> _expandAnimation;

  @override
  void initState() {
    super.initState();
    _isExpanded = widget.initiallyExpanded;
    
    // Initialize animation controller
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _expandAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
    
    if (_isExpanded) {
      _animationController.value = 1.0;
    }
    
    _loadSavedAccounts();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadSavedAccounts() async {
    try {
      final accounts = await SavedAccountsService.getSavedUsernames();
      if (mounted) {
        setState(() {
          _savedAccounts = accounts;
          _isLoading = false;
        });
      }
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Error loading saved accounts for info box: $e');
      if (mounted) {
        setState(() {
          _savedAccounts = [];
          _isLoading = false;
        });
      }
    }
  }

  void _toggleExpanded() {
    setState(() {
      _isExpanded = !_isExpanded;
    });
    
    if (_isExpanded) {
      _animationController.forward();
    } else {
      _animationController.reverse();
    }
  }

  void _handleAccountTap(String username) {
    widget.onAccountSelected?.call(username);
  }

  @override
  Widget build(BuildContext context) {
    // Don't show if loading
    if (_isLoading) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: MolColors.green.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with expand/collapse functionality
          InkWell(
            onTap: _toggleExpanded,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Icon(
                    Icons.account_circle,
                    color: MolColors.green,
                    size: 20,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      _savedAccounts.isEmpty
                        ? 'Saved Accounts'
                        : 'Saved Accounts (${_savedAccounts.length})',
                      style: const TextStyle(
                        color: MolColors.green,
                        fontFamily: 'Pirulen',
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  AnimatedRotation(
                    turns: _isExpanded ? 0.5 : 0.0,
                    duration: const Duration(milliseconds: 300),
                    child: Icon(
                      Icons.keyboard_arrow_down,
                      color: MolColors.green,
                      size: 20,
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          // Expandable content
          SizeTransition(
            sizeFactor: _expandAnimation,
            child: Container(
              decoration: BoxDecoration(
                border: Border(
                  top: BorderSide(
                    color: Colors.grey[700]!,
                    width: 1,
                  ),
                ),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Info text
                    Text(
                      _savedAccounts.isEmpty
                        ? 'No accounts saved on this device yet.'
                        : 'Accounts saved on this device:',
                      style: TextStyle(
                        color: Colors.grey[400],
                        fontFamily: 'Bitsumishi',
                        fontSize: 12,
                      ),
                    ),
                    const SizedBox(height: 12),

                    // Saved accounts list or empty message
                    if (_savedAccounts.isEmpty)
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
                        child: Row(
                          children: [
                            Icon(
                              Icons.info_outline,
                              color: Colors.grey[500],
                              size: 16,
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                'Sign in successfully to save your account for quick access.',
                                style: TextStyle(
                                  color: Colors.grey[500],
                                  fontFamily: 'Bitsumishi',
                                  fontSize: 11,
                                ),
                              ),
                            ),
                          ],
                        ),
                      )
                    else
                      ...(_savedAccounts.map((username) => _buildAccountItem(username))),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAccountItem(String username) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: InkWell(
        onTap: () => _handleAccountTap(username),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
          child: Row(
            children: [
              Icon(
                Icons.check_circle,
                color: MolColors.green,
                size: 16,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  username,
                  style: const TextStyle(
                    color: MolColors.green,
                    fontFamily: 'Bitsumishi',
                    fontSize: 12,
                  ),
                ),
              ),
              if (widget.onAccountSelected != null)
                Icon(
                  Icons.arrow_forward_ios,
                  color: Colors.grey[600],
                  size: 12,
                ),
            ],
          ),
        ),
      ),
    );
  }
}
