import 'package:flutter/material.dart';
import 'reward_photo_proof_modal.dart';
import 'bounty_spinner.dart';
import '../models/bounty_model.dart';
import '../models/user_model.dart';
import '../services/reward_engine.dart';
import 'bounty_card.dart';

class RewardsDashboard extends StatefulWidget {
  final Color glowColor;
  final List<String> categories;
  final List<String> customCategories;
  final BountyModel? todayBounty;
  final int bonusExpToday;
  final User user;
  final void Function(User updatedUser, SpinResult result) onSpin;
  final VoidCallback onCashedBounties;

  const RewardsDashboard({
    super.key,
    required this.glowColor,
    required this.categories,
    required this.customCategories,
    required this.todayBounty,
    required this.bonusExpToday,
    required this.user,
    required this.onSpin,
    required this.onCashedBounties,
  });

  @override
  State<RewardsDashboard> createState() => _RewardsDashboardState();
}

class _RewardsDashboardState extends State<RewardsDashboard> {
  bool _showPhotoModal = false;
  String? _confirmationMsg;
  bool _showSpinner = false;

  @override
  void initState() {
    super.initState();
  }

  void _openPhotoProofModal() {
    setState(() => _showPhotoModal = true);
  }

  void _closePhotoProofModal() {
    setState(() => _showPhotoModal = false);
  }

  Future<void> _onPhotoSelected(String path) async {
    setState(() => _showPhotoModal = false);
    if (widget.todayBounty != null) {
      await RewardEngine.instance.checkBountyCompletion(
        widget.todayBounty!.categories.first,
        photoProofPath: path,
      );
    }
    setState(() {
      _confirmationMsg = 'Bounty completed!';
    });
    Future.delayed(const Duration(seconds: 2), () {
      setState(() => _confirmationMsg = null);
    });
  }

  void _openSpinner() {
    setState(() => _showSpinner = true);
  }

  void _closeSpinner() {
    setState(() => _showSpinner = false);
  }

  void _onSpinnerComplete(String category, SpinResult result, User updatedUser) {
    widget.onSpin(updatedUser, result);
    _closeSpinner();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Today's Bounty
        if (widget.todayBounty != null)
          BountyCard(
            bounty: widget.todayBounty!,
            glowColor: widget.glowColor,
            onCashIn: _openPhotoProofModal,
          ),

        const SizedBox(height: 24),

        // Bonus EXP Today
        if (widget.bonusExpToday > 0)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.7),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.amber),
              boxShadow: [
                BoxShadow(
                  color: Colors.amber.withValues(alpha: 0.3),
                  blurRadius: 8,
                  spreadRadius: 1,
                ),
              ],
            ),
            child: Text(
              'BONUS EXP TODAY: +${widget.bonusExpToday}',
              style: const TextStyle(
                fontFamily: 'Pirulen',
                fontSize: 16,
                color: Colors.amber,
              ),
            ),
          ),

        const SizedBox(height: 24),

        // Action Buttons
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Spin Button
            ElevatedButton.icon(
              onPressed: _openSpinner,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.purple,
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              icon: const Icon(Icons.casino),
              label: const Text(
                'SPIN',
                style: TextStyle(
                  fontFamily: 'Pirulen',
                  fontSize: 16,
                ),
              ),
            ),

            const SizedBox(width: 16),

            // History Button
            ElevatedButton.icon(
              onPressed: widget.onCashedBounties,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              icon: const Icon(Icons.history),
              label: const Text(
                'HISTORY',
                style: TextStyle(
                  fontFamily: 'Pirulen',
                  fontSize: 16,
                ),
              ),
            ),
          ],
        ),

        // Modals
        if (_showPhotoModal)
          RewardPhotoProofModal(
            onClose: _closePhotoProofModal,
            onPhotoSelected: _onPhotoSelected,
          ),

        if (_showSpinner)
          BountySpinner(
            categories: widget.categories,
            customCategories: widget.customCategories,
            glowColor: widget.glowColor,
            user: widget.user,
            onSpinComplete: _onSpinnerComplete,
            onClose: _closeSpinner,
          ),

        if (_confirmationMsg != null)
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.green.withValues(alpha: 0.9),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              _confirmationMsg!,
              style: const TextStyle(
                fontFamily: 'Pirulen',
                fontSize: 18,
                color: Colors.white,
              ),
            ),
          ),
      ],
    );
  }
}