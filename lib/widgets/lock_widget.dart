import 'package:home_widget/home_widget.dart';

Future<void> updateLockScreenWidget({
  required int level,
  required int expToNextLevel,
  required String todaysMission,
  required int streakDays,
  required String nextReward,
  required String quote,
}) async {
  await HomeWidget.saveWidgetData<String>('level', 'Level $level');
  await HomeWidget.saveWidgetData<String>('exp', '$expToNextLevel EXP left');
  await HomeWidget.saveWidgetData<String>('mission', todaysMission);
  await HomeWidget.saveWidgetData<String>('streak', 'Streak: $streakDays days');
  await HomeWidget.saveWidgetData<String>('reward', nextReward);
  await HomeWidget.saveWidgetData<String>('quote', quote);

  await HomeWidget.updateWidget(
    iOSName: 'LockWidgetExtension',
    androidName: 'LockWidgetProvider',
  );
}
