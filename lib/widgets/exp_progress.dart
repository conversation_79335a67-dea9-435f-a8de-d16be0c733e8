// 📁 lib/widgets/exp_progress.dart

import 'package:flutter/material.dart';

class ExpProgress extends StatelessWidget {
  final double percent;
  final Color color;

  const ExpProgress({
    super.key,
    required this.percent,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(6),
        child: LinearProgressIndicator(
          value: percent.clamp(0.0, 1.0),
          minHeight: 10,
          backgroundColor: Colors.grey[800],
          color: color,
        ),
      ),
    );
  }
}
