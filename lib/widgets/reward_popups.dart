import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../controller/reward_controller.dart';
import '../services/reward_engine.dart';
//import '../models/reward_model.dart';
//import '../theme/colors.dart';

class RewardPopups extends StatefulWidget {
  final Color glowColor;
  const RewardPopups({super.key, required this.glowColor});

  @override
  State<RewardPopups> createState() => _RewardPopupsState();
}

class _RewardPopupsState extends State<RewardPopups> with TickerProviderStateMixin {
  OverlayEntry? _popupEntry;
  AnimationController? _controller;
  String? _popupText;
  IconData? _popupIcon;
  bool _showBonusMiss = false;

  @override
  void initState() {
    super.initState();
    final rewardController = Provider.of<RewardController>(context, listen: false);
    rewardController.bonusStream.listen((event) {
      _showPopup('+${event.xp} XP in ${event.category}!', Icons.flash_on);
    });
    rewardController.questStream.listen((event) {
      _showPopup('Quest Complete! +${event.xp} XP', Icons.emoji_events);
    });
    // Listen for bonus miss events
    RewardEngine.instance.bonusMissStream.listen((_) {
      showBonusMiss();
    });
  }

  void _showPopup(String text, IconData icon) {
    _controller?.dispose();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
      reverseDuration: const Duration(milliseconds: 400),
    );
    _popupText = text;
    _popupIcon = icon;
    _popupEntry?.remove();
    _popupEntry = OverlayEntry(
      builder: (context) => Positioned(
        top: 100,
        left: 0,
        right: 0,
        child: AnimatedBuilder(
          animation: _controller!,
          builder: (context, child) {
            final opacity = _controller!.value;
            final offset = (1 - opacity) * -40;
            return Opacity(
              opacity: opacity,
              child: Transform.translate(
                offset: Offset(0, offset),
                child: Center(
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 18),
                    decoration: BoxDecoration(
                      color: Colors.black.withValues(alpha: 0.92),
                      borderRadius: BorderRadius.circular(18),
                      boxShadow: [
                        BoxShadow(color: widget.glowColor, blurRadius: 24, spreadRadius: 4),
                      ],
                      border: Border.all(color: widget.glowColor, width: 2),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(_popupIcon, color: widget.glowColor, size: 32, shadows: [
                          Shadow(color: widget.glowColor, blurRadius: 12),
                        ]),
                        const SizedBox(width: 16),
                        Text(_popupText ?? '',
                          style: TextStyle(
                            fontFamily: 'Pirulen',
                            fontSize: 18,
                            color: Colors.white,
                            shadows: [Shadow(color: widget.glowColor, blurRadius: 8)],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
    Overlay.of(context).insert(_popupEntry!);
    _controller!.forward();
    Future.delayed(const Duration(seconds: 2), () async {
      await _controller?.reverse();
      _popupEntry?.remove();
      _popupEntry = null;
    });
  }

  void showBonusMiss() {
    setState(() {
      _showBonusMiss = true;
    });
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) setState(() => _showBonusMiss = false);
    });
  }

  @override
  void dispose() {
    _controller?.dispose();
    _popupEntry?.remove();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        const SizedBox.shrink(),
        if (_showBonusMiss)
          Center(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 20),
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.95),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(color: widget.glowColor, width: 2),
                boxShadow: [BoxShadow(color: widget.glowColor, blurRadius: 16)],
              ),
              child: Stack(
                children: [
                  Text('Better Luck Next Time',
                      style: TextStyle(
                        fontFamily: 'Pirulen',
                        fontSize: 22,
                        fontWeight: FontWeight.bold,
                        foreground: Paint()
                          ..style = PaintingStyle.stroke
                          ..strokeWidth = 4
                          ..color = Colors.black,
                      )),
                  Text('Better Luck Next Time',
                      style: TextStyle(
                        fontFamily: 'Pirulen',
                        fontSize: 22,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      )),
                ],
              ),
            ),
          ),
      ],
    );
  }
} 