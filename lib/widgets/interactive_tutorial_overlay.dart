import 'package:flutter/material.dart';
import '../models/user_model.dart';

class TutorialStep {
  final String id;
  final String title;
  final String message;
  final String action;
  final IconData icon;
  final Color accentColor;

  const TutorialStep({
    required this.id,
    required this.title,
    required this.message,
    required this.action,
    required this.icon,
    this.accentColor = Colors.cyanAccent,
  });
}

class InteractiveTutorialOverlay extends StatefulWidget {
  final VoidCallback onComplete;
  final User user;

  const InteractiveTutorialOverlay({
    super.key,
    required this.onComplete,
    required this.user,
  });

  @override
  State<InteractiveTutorialOverlay> createState() => _InteractiveTutorialOverlayState();
}

class _InteractiveTutorialOverlayState extends State<InteractiveTutorialOverlay>
    with TickerProviderStateMixin {
  int _currentStep = 0;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  List<TutorialStep> get _tutorialSteps {
    final glowColor = widget.user.northStarQuest?.glowColor ?? Colors.cyan;
    return [
      const TutorialStep(
        id: 'welcome',
        title: 'Welcome to Your MXD Life!',
        message: 'You\'ve completed your setup. Now let\'s explore your new life-changing dashboard.',
        action: 'Let\'s begin your journey',
        icon: Icons.rocket_launch,
        accentColor: Colors.cyanAccent,
      ),
      TutorialStep(
        id: 'north_star',
        title: 'Your North Star Quest',
        message: 'This is your ultimate goal - the mission that drives everything you do. Tap to view details and track your progress.',
        action: 'This guides your entire journey',
        icon: Icons.star,
        accentColor: glowColor,
      ),
      const TutorialStep(
        id: 'exp_bar',
        title: 'Experience & Leveling',
        message: 'Complete habits and activities to earn XP. Level up to unlock new features and track your growth.',
        action: 'Your progress is visible here',
        icon: Icons.trending_up,
        accentColor: Color(0xFF00FF00), // Bright vibrant neon green
      ),
      const TutorialStep(
        id: 'daily_habits',
        title: 'Your Daily Habits',
        message: 'These are your 10 non-negotiable daily habits. Complete them daily to build consistency and earn rewards.',
        action: 'Swipe to mark complete',
        icon: Icons.check_circle,
        accentColor: Colors.blueAccent,
      ),
      const TutorialStep(
        id: 'streak_tracker',
        title: 'Streak Tracking',
        message: 'Build momentum with daily streaks. The longer your streak, the more powerful your habits become.',
        action: 'Consistency is key',
        icon: Icons.local_fire_department,
        accentColor: Colors.orangeAccent,
      ),
      const TutorialStep(
        id: 'coach_chat',
        title: 'Life Coaches',
        message: 'Your MXD Out Life personal coaches are here to guide you. \n Chat with them for motivation, advice, and accountability.',
        action: 'Get personalized guidance',
        icon: Icons.psychology,
        accentColor: Colors.purpleAccent,
      ),
      const TutorialStep(
        id: 'super_feed',
        title: 'Super Feed',
        message: 'Reflect on your progress, celebrate wins, and track your journey. This is your personal growth journal.',
        action: 'Document your transformation',
        icon: Icons.article,
        accentColor: Colors.tealAccent,
      ),
      const TutorialStep(
        id: 'training_tracker',
        title: 'Training Tracker',
        message: 'Track your workouts with precision! Use the built-in timer, record notes, track bodyweight, and earn Health EXP. Your personal training coach will guide you.',
        action: 'Level up your fitness',
        icon: Icons.fitness_center,
        accentColor: Colors.redAccent,
      ),
      const TutorialStep(
        id: 'training_programs',
        title: 'Custom Training Programs',
        message: 'Customize your workout cycle! Change from A, B, C to any names you want - Push/Pull/Legs, Monday/Tuesday, or your own system. Make it yours!',
        action: 'Personalize your training',
        icon: Icons.settings,
        accentColor: Colors.deepOrangeAccent,
      ),
      const TutorialStep(
        id: 'bounty_hunter',
        title: 'Bounty Hunter',
        message: 'Complete daily bounties to earn bonus EXP! Each day brings new challenges across all life categories. Prove your progress with photo evidence.',
        action: 'Hunt for daily rewards',
        icon: Icons.military_tech,
        accentColor: Colors.amber,
      ),
      const TutorialStep(
        id: 'bounty_spinner',
        title: 'Bounty Spinner',
        message: 'Unlock the spinner at 40 EXP, then every +20 EXP! Spin for bonus EXP multipliers (5-20 EXP) that stack across categories. Epic rewards await!',
        action: 'Spin for epic bonuses',
        icon: Icons.casino,
        accentColor: Colors.deepPurple,
      ),
      const TutorialStep(
        id: 'completion',
        title: 'You\'re Ready!',
        message: 'You now have everything you need to transform your life. Remember, consistency beats perfection every time.',
        action: 'Start your transformation',
        icon: Icons.celebration,
        accentColor: Colors.cyanAccent,
      ),
    ];
  }

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _nextStep() {
    if (_currentStep < _tutorialSteps.length - 1) {
      setState(() {
        _currentStep++;
      });
      _animationController.reset();
      _animationController.forward();
    } else {
      widget.onComplete();
    }
  }

  void _previousStep() {
    if (_currentStep > 0) {
      setState(() {
        _currentStep--;
      });
      _animationController.reset();
      _animationController.forward();
    }
  }

  void _skipTutorial() {
    widget.onComplete();
  }

  @override
  Widget build(BuildContext context) {
    final currentStep = _tutorialSteps[_currentStep];
    final isLastStep = _currentStep == _tutorialSteps.length - 1;

    return Scaffold(
      backgroundColor: Colors.black.withValues(alpha: 0.9),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            children: [
              // Progress indicator
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Tutorial ${_currentStep + 1}/${_tutorialSteps.length}',
                    style: TextStyle(
                      color: Colors.grey[400],
                      fontFamily: 'Bitsumishi',
                      fontSize: 14,
                    ),
                  ),
                  TextButton(
                    onPressed: _skipTutorial,
                    child: Text(
                      'Skip',
                      style: TextStyle(
                        color: Colors.grey[500],
                        fontFamily: 'Pirulen',
                        fontSize: 14,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              
              // Progress bar
              LinearProgressIndicator(
                value: (_currentStep + 1) / _tutorialSteps.length,
                backgroundColor: Colors.grey[800],
                valueColor: AlwaysStoppedAnimation<Color>(currentStep.accentColor),
              ),
              
              const Spacer(),
              
              // Tutorial content
              FadeTransition(
                opacity: _fadeAnimation,
                child: SlideTransition(
                  position: _slideAnimation,
                  child: Container(
                    padding: const EdgeInsets.all(32),
                    decoration: BoxDecoration(
                      color: Colors.grey[900],
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: currentStep.accentColor.withValues(alpha: 0.3),
                        width: 2,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: currentStep.accentColor.withValues(alpha: 0.2),
                          blurRadius: 20,
                          spreadRadius: 2,
                        ),
                      ],
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // Icon
                        Container(
                          padding: const EdgeInsets.all(20),
                          decoration: BoxDecoration(
                            color: currentStep.accentColor.withValues(alpha: 0.1),
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: currentStep.accentColor.withValues(alpha: 0.3),
                              width: 2,
                            ),
                          ),
                          child: Icon(
                            currentStep.icon,
                            color: currentStep.accentColor,
                            size: 48,
                          ),
                        ),
                        const SizedBox(height: 24),
                        
                        // Title
                        Text(
                          currentStep.title,
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            color: currentStep.accentColor,
                            fontFamily: 'Pirulen',
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),
                        
                        // Message
                        Text(
                          currentStep.message,
                          textAlign: TextAlign.center,
                          style: const TextStyle(
                            color: Colors.white70,
                            fontFamily: 'Bitsumishi',
                            fontSize: 16,
                            height: 1.5,
                          ),
                        ),
                        const SizedBox(height: 20),
                        
                        // Action hint
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 8,
                          ),
                          decoration: BoxDecoration(
                            color: currentStep.accentColor.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(20),
                            border: Border.all(
                              color: currentStep.accentColor.withValues(alpha: 0.3),
                            ),
                          ),
                          child: Text(
                            currentStep.action,
                            style: TextStyle(
                              color: currentStep.accentColor,
                              fontFamily: 'Pirulen',
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              
              const Spacer(),
              
              // Navigation buttons
              Row(
                children: [
                  if (_currentStep > 0)
                    Expanded(
                      child: ElevatedButton(
                        onPressed: _previousStep,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.grey[800],
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(11),
                          ),
                        ),
                        child: const Text(
                          'Previous',
                          style: TextStyle(
                            fontFamily: 'Pirulen',
                            fontSize: 16,
                          ),
                        ),
                      ),
                    ),
                  if (_currentStep > 0) const SizedBox(width: 14),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _nextStep,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: currentStep.accentColor,
                        foregroundColor: Colors.black,
                        padding: const EdgeInsets.symmetric(vertical: 14),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                      ),
                      child: Text(
                        isLastStep ? 'Get Started!' : 'Next',
                        style: const TextStyle(
                          fontFamily: 'Pirulen',
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
} 