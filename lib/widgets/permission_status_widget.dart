// lib/widgets/permission_status_widget.dart

import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import '../services/permission_manager_service.dart';

/// Widget to display and manage app permissions
class PermissionStatusWidget extends StatefulWidget {
  final bool showInSettings;
  
  const PermissionStatusWidget({
    super.key,
    this.showInSettings = false,
  });
  
  @override
  State<PermissionStatusWidget> createState() => _PermissionStatusWidgetState();
}

class _PermissionStatusWidgetState extends State<PermissionStatusWidget> {
  Map<String, PermissionStatus> _permissionStatuses = {};
  bool _isLoading = true;
  
  @override
  void initState() {
    super.initState();
    _loadPermissionStatuses();
  }
  
  Future<void> _loadPermissionStatuses() async {
    setState(() => _isLoading = true);
    
    try {
      final statuses = await Future.wait([
        Permission.camera.status,
        Permission.photos.status,
        Permission.microphone.status,
        Permission.notification.status,
      ]);
      
      setState(() {
        _permissionStatuses = {
          'Camera': statuses[0],
          'Photos': statuses[1],
          'Microphone': statuses[2],
          'Notifications': statuses[3],
        };
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
    }
  }
  
  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(color: Colors.purple),
      );
    }
    
    if (widget.showInSettings) {
      return _buildSettingsView();
    }
    
    return _buildCompactView();
  }
  
  Widget _buildSettingsView() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[700]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'App Permissions',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
              fontFamily: 'Pirulen',
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Manage what MXD can access on your device',
            style: TextStyle(
              color: Colors.grey,
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 16),
          
          ..._permissionStatuses.entries.map((entry) =>
            _buildPermissionTile(entry.key, entry.value)
          ),
          
          const SizedBox(height: 16),
          _buildPrivacyNote(),
        ],
      ),
    );
  }
  
  Widget _buildCompactView() {
    final deniedPermissions = _permissionStatuses.entries
        .where((entry) => !entry.value.isGranted)
        .toList();
    
    if (deniedPermissions.isEmpty) {
      return const SizedBox.shrink();
    }
    
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.orange.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.info_outline, color: Colors.orange, size: 20),
              const SizedBox(width: 8),
              const Text(
                'Optional Permissions',
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'Enable ${deniedPermissions.length} permission${deniedPermissions.length == 1 ? '' : 's'} to unlock additional features',
            style: const TextStyle(color: Colors.white, fontSize: 12),
          ),
          const SizedBox(height: 8),
          TextButton(
            onPressed: () => _showPermissionDialog(),
            child: const Text(
              'Review Permissions',
              style: TextStyle(color: Colors.orange),
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildPermissionTile(String name, PermissionStatus status) {
    final info = _getPermissionInfo(name);
    final isGranted = status.isGranted;
    
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[800],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isGranted ? Colors.green : Colors.grey[600]!,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                info.icon,
                color: isGranted ? Colors.green : Colors.grey,
                size: 24,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      name,
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    Text(
                      info.description,
                      style: const TextStyle(
                        color: Colors.grey,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
              _buildStatusChip(status),
            ],
          ),
          if (!isGranted) ...[
            const SizedBox(height: 8),
            Text(
              info.benefit,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 13,
                fontStyle: FontStyle.italic,
              ),
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: () => _requestPermission(name),
              style: ElevatedButton.styleFrom(
                backgroundColor: info.color,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(6),
                ),
              ),
              child: Text('Enable $name'),
            ),
          ],
        ],
      ),
    );
  }
  
  Widget _buildStatusChip(PermissionStatus status) {
    Color color;
    String text;
    
    switch (status) {
      case PermissionStatus.granted:
        color = Colors.green;
        text = 'Enabled';
        break;
      case PermissionStatus.denied:
        color = Colors.orange;
        text = 'Disabled';
        break;
      case PermissionStatus.permanentlyDenied:
        color = Colors.red;
        text = 'Blocked';
        break;
      default:
        color = Colors.grey;
        text = 'Unknown';
    }
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color, width: 1),
      ),
      child: Text(
        text,
        style: TextStyle(
          color: color,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }
  
  Widget _buildPrivacyNote() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
      ),
      child: const Row(
        children: [
          Icon(Icons.security, color: Colors.blue, size: 20),
          SizedBox(width: 8),
          Expanded(
            child: Text(
              'Your privacy is protected. We never access your data without permission and never share it with third parties.',
              style: TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontStyle: FontStyle.italic,
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  PermissionInfo _getPermissionInfo(String name) {
    switch (name) {
      case 'Camera':
        return PermissionInfo(
          icon: Icons.camera_alt,
          color: Colors.blue,
          description: 'Take photos for quest progress',
          benefit: 'Document your achievements and milestones with photos',
        );
      case 'Photos':
        return PermissionInfo(
          icon: Icons.photo_library,
          color: Colors.green,
          description: 'Select images from your library',
          benefit: 'Choose existing photos to personalize your journey',
        );
      case 'Microphone':
        return PermissionInfo(
          icon: Icons.mic,
          color: Colors.orange,
          description: 'Voice input for hands-free logging',
          benefit: 'Use voice commands to quickly log progress and interact with AI coaches',
        );
      case 'Notifications':
        return PermissionInfo(
          icon: Icons.notifications,
          color: Colors.purple,
          description: 'Helpful reminders and motivation',
          benefit: 'Stay on track with gentle reminders and motivational messages',
        );
      default:
        return PermissionInfo(
          icon: Icons.help,
          color: Colors.grey,
          description: 'Unknown permission',
          benefit: 'Additional app functionality',
        );
    }
  }
  
  Future<void> _requestPermission(String name) async {
    switch (name) {
      case 'Camera':
        await PermissionManagerService.requestCameraPermission(context);
        break;
      case 'Photos':
        await PermissionManagerService.requestPhotoLibraryPermission(context);
        break;
      case 'Microphone':
        await PermissionManagerService.requestMicrophonePermission(context);
        break;
      case 'Notifications':
        await PermissionManagerService.requestNotificationPermission(context);
        break;
    }
    
    // Refresh status after request
    await _loadPermissionStatuses();
  }
  
  void _showPermissionDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.black,
        title: const Text(
          'App Permissions',
          style: TextStyle(color: Colors.white, fontFamily: 'Pirulen'),
        ),
        content: const PermissionStatusWidget(showInSettings: true),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }
}

class PermissionInfo {
  final IconData icon;
  final Color color;
  final String description;
  final String benefit;
  
  const PermissionInfo({
    required this.icon,
    required this.color,
    required this.description,
    required this.benefit,
  });
}
