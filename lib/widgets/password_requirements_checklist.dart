// lib/widgets/password_requirements_checklist.dart

import 'package:flutter/material.dart';
import '../theme/colors.dart';

/// Compact checklist widget that shows password requirements with ✓/✗ icons
class PasswordRequirementsChecklist extends StatelessWidget {
  final String password;

  const PasswordRequirementsChecklist({
    super.key,
    required this.password,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[700]!, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Password Requirements:',
            style: TextStyle(
              color: Colors.white,
              fontFamily: 'Pirulen',
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          _buildRequirement(
            'At least 8 characters',
            _hasMinLength(password),
          ),
          const SizedBox(height: 4),
          _buildRequirement(
            'Contains uppercase letter',
            _hasUppercase(password),
          ),
          const SizedBox(height: 4),
          _buildRequirement(
            'Contains lowercase letter',
            _hasLowercase(password),
          ),
          const SizedBox(height: 4),
          _buildRequirement(
            'Contains number',
            _hasNumber(password),
          ),
          const SizedBox(height: 4),
          _buildRequirement(
            'Contains special character',
            _hasSpecialChar(password),
          ),
        ],
      ),
    );
  }

  Widget _buildRequirement(String text, bool isValid) {
    return Row(
      children: [
        Icon(
          isValid ? Icons.check_circle : Icons.cancel,
          color: isValid ? MolColors.green : Colors.red,
          size: 16,
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            text,
            style: TextStyle(
              color: isValid ? MolColors.green : Colors.grey[400],
              fontFamily: 'Bitsumishi',
              fontSize: 12,
            ),
          ),
        ),
      ],
    );
  }

  // Password validation helper methods
  static bool _hasMinLength(String password) => password.length >= 8;
  
  static bool _hasUppercase(String password) => password.contains(RegExp(r'[A-Z]'));
  
  static bool _hasLowercase(String password) => password.contains(RegExp(r'[a-z]'));
  
  static bool _hasNumber(String password) => password.contains(RegExp(r'[0-9]'));
  
  static bool _hasSpecialChar(String password) => password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'));

  /// Check if password meets all requirements
  static bool isPasswordValid(String password) {
    return _hasMinLength(password) &&
           _hasUppercase(password) &&
           _hasLowercase(password) &&
           _hasNumber(password) &&
           _hasSpecialChar(password);
  }

  /// Get list of validation errors for a password
  static List<String> getPasswordErrors(String password) {
    final errors = <String>[];
    
    if (!_hasMinLength(password)) {
      errors.add('Password must be at least 8 characters long');
    }
    if (!_hasUppercase(password)) {
      errors.add('Password must contain at least one uppercase letter');
    }
    if (!_hasLowercase(password)) {
      errors.add('Password must contain at least one lowercase letter');
    }
    if (!_hasNumber(password)) {
      errors.add('Password must contain at least one number');
    }
    if (!_hasSpecialChar(password)) {
      errors.add('Password must contain at least one special character');
    }
    
    return errors;
  }
}
