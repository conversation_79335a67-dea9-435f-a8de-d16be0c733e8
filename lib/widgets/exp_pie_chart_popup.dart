// 📁 lib/widgets/exp_pie_chart_popup.dart

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:fl_chart/fl_chart.dart';
import '../models/user_model.dart';
import '../controller/user_controller2.dart';
import '../utils/category_utils.dart';
import '../utils/rank_utils.dart';
import '../theme/colors.dart';
import 'error_state_widget.dart';

/// A custom dialog that shows a pie‐chart (left 2/3) and a right‐aligned
/// text breakdown (descending EXP). The dialog is 45% of screen width
/// and enforces a 9:16 (w:h) aspect ratio.  
/// Falls back to a "No EXP to display" message if user has zero across all categories.
class ExpPieChartPopup extends StatelessWidget {
  const ExpPieChartPopup({super.key});

  @override
  Widget build(BuildContext context) {
    final userController = Provider.of<UserController2>(context, listen: false);
    final user = userController.user;
    
    if (user == null) {
      return Dialog(
        backgroundColor: Colors.black,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: SizedBox(
          width: 300,
          height: 200,
          child: ErrorStateWidget(
            error: Exception('No user data available'),
            customMessage: 'Unable to load user data for the chart.',
            isFullScreen: false,
            onDismiss: () => Navigator.of(context).pop(),
          ),
        ),
      );
    }

    // Compute total EXP across all "displayable" categories
    // Create a map from custom categories list for the function
    // Only include categories that are actually in the customCategories list
    final customCategoriesMap = <String, int>{};
    for (final cat in user.customCategories) {
      if (user.categories.containsKey(cat)) {
        customCategoriesMap[cat] = user.categories[cat]!;
      }
    }

    final displayCats = getDisplayCategories(user.categories, customCategoriesMap);
    int totalExp = 0;
    for (final cat in displayCats) {
      totalExp += user.categories[cat] ?? 0;
    }

    // If user has no positive EXP, show a fallback "empty" dialog
    if (totalExp <= 0) {
      return Dialog(
        backgroundColor: Colors.black,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: SizedBox(
          width: 300,
          height: 900, // Increased height to accommodate content
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Icon
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    color: Colors.grey.withValues(alpha: 0.2),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.pie_chart_outline,
                    size: 40,
                    color: Colors.white54,
                  ),
                ),
                const SizedBox(height: 24),

                // Title
                const Text(
                  'NO\nEXPERIENCE\nYET',
                  style: TextStyle(
                    fontFamily: 'Pirulen',
                    fontSize: 20,
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    height: 1.2,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),

                // Message
                const Text(
                  'START COMPLETING HABITS\nSUPERCHARGE YOUR GAME\nAND BOUNTIES TO SEE\nYOUR PROGRESS HERE!',
                  style: TextStyle(
                    fontFamily: 'Bitsumishi',
                    fontSize: 12,
                    color: Colors.white70,
                    height: 1.3,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),

                // Close button
                GestureDetector(
                  onTap: () => Navigator.of(context).pop(),
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                    child: const Text(
                      "CLOSE",
                      style: TextStyle(
                        fontFamily: 'Pirulen',
                        fontSize: 10,
                        color: Colors.pinkAccent,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    }

    // Build a sorted list of categories (descending by EXP), then filter to only positive entries
    final allEntries = sortCategoriesDescending(user.categories, customCategoriesMap);
    final filteredEntries = allEntries
        .where((e) => displayCats.contains(e.key) && e.value > 0)
        .toList();

    // Determine popup dimensions: More conservative sizing to prevent overflow
    final screenW = MediaQuery.of(context).size.width;
    final screenH = MediaQuery.of(context).size.height;

    // Calculate base dimensions - increased by 20% for better spacing
    final basePopupW = screenW * 0.90; // Larger for better readability
    final basePopupH = screenH * 0.99; // Increased to 99% (0.90 -> 0.99) for optimal spacing accommodation

    // Add failsafes for vertical phone screens
    final maxPopupW = screenW * 0.97; // Allow larger width
    final maxPopupH = screenH * 1.0; // Increased maximum height to accommodate larger modal
    final minPopupW = 320.0; // Minimum width for readability
    final minPopupH = 540.0; // Increased minimum height by 20% (450 -> 540)

    // Apply constraints with failsafes
    final popupW = basePopupW.clamp(minPopupW, maxPopupW);
    final popupH = basePopupH.clamp(minPopupH, maxPopupH);

    // Compute insetPadding with additional safety margin
    final rawHorizontal = (screenW - popupW) / 2;
    final rawVertical = (screenH - popupH) / 3;
    // Add 8px safety margin to prevent edge overflow
    final insetHorizontal = (rawHorizontal < 8.0 ? 8.0 : rawHorizontal);
    final insetVertical = (rawVertical < 8.0 ? 8.0 : rawVertical);

    return Dialog(
      backgroundColor: Colors.black,
      insetPadding: EdgeInsets.only(
        left: insetHorizontal.clamp(8.0, double.infinity),
        right: insetHorizontal.clamp(8.0, double.infinity),
        top: (insetVertical + 5).clamp(8.0, double.infinity), // Move modal down by 10 pixels, ensure non-negative
        bottom: (insetVertical - 15).clamp(8.0, double.infinity), // Reduce bottom padding to prevent overflow
      ),
      child: SizedBox(
        width: popupW,
        height: popupH,
        child: Column(
          children: [
            // ─── (A) HEADER: LEVEL & TOTAL EXP (FIXED HEIGHT) ───
            Container(
              height: 120, // Increased from 100 to 110 to prevent overflow
              padding: const EdgeInsets.only(top: 2, bottom: 16), // Moved up by 22px (24 - 22 = 2)
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Player Level - Line 1, Centered (using totalExp from categories for consistency)
                  Text(
                    "LEVEL ${LevelUtils.getRankLevel(totalExp)}",
                    style: TextStyle(
                      fontFamily: 'Pirulen',
                      fontSize: 27,
                      color: Colors.cyanAccent,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 1.5), // 50% closer (3 * 0.5 = 1.5)
                  // Total EXP - Line 2, Centered (matches the sum of categories shown in pie chart)
                  Text(
                    "$totalExp EXP",
                    style: TextStyle(
                      fontFamily: 'Pirulen',
                      fontSize: 19,
                      color: Colors.white70,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),

            // ─── (B) PIE CHART SECTION (DOMINANT) ───
            Expanded(
              flex: 5, // Dominant space for pie chart
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 16.0),
                child: _buildPieChart(filteredEntries, totalExp, user),
              ),
            ),

            // ─── (C) COMPACT SEPARATION (FIXED HEIGHT) ───
            const SizedBox(height: 16), // Reduced from 40 to 16 to prevent overflow

            // ─── (D) CATEGORY BREAKDOWN SECTION ───
            Expanded(
              flex: 4, // Balanced space for categories
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 20.0),
                child: _buildBreakdownList(filteredEntries, user),
              ),
            ),

            // ─── (E) CLOSE BUTTON SECTION (FIXED HEIGHT) ───
            Container(
              height: 50, // Reduced from 60 to 50 to prevent overflow
              padding: const EdgeInsets.only(top: 12, bottom: 12), // Reduced padding
              child: Center(
                child: GestureDetector(
                  onTap: () => Navigator.of(context).pop(),
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 6, horizontal: 16),
                    child: Text(
                      "CLOSE",
                      style: TextStyle(
                        fontFamily: 'Pirulen',
                        fontSize: 10,
                        color: Colors.pinkAccent,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }



  /// Builds an anti‐clockwise PieChart starting from the largest slice,
  /// then overlays the "mana aura" PNG in the center hole via a Stack.
  Widget _buildPieChart(
    List<MapEntry<String, int>> entries,
    int totalExp,
    User user,
  ) {
    final List<PieChartSectionData> sections = [];

    for (var entry in entries) {
      final cat = entry.key;
      final expVal = entry.value;
      final percent = expVal / totalExp;

      // Determine color: if this is a custom category, override to red or orange; else use getCategoryColor
      Color color;
      if (user.customCategories.contains(cat)) {
        final idx = user.customCategories.indexOf(cat);
        if (idx == 0) {
          color = Colors.redAccent;
        } else if (idx == 1) {
          color = Colors.orangeAccent;
        } else {
          color = getCategoryColor(cat);
        }
      } else {
        color = getCategoryColor(cat);
      }

      sections.add(
        PieChartSectionData(
          color: color.withValues(alpha: 0.9),
          value: expVal.toDouble(),
          title: "${(percent * 100).round()}%",
          radius: 110, // Increased from 100 to 110 to take advantage of larger modal
          titleStyle: TextStyle(
            fontFamily: 'Pirulen',
            fontSize: 17, // Increased from 16 to 17 for better readability
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
      );
    }

    // Optimal hole radius for balanced proportions with larger pie chart
    const double holeRadius = 65; // Increased from 60 to 65 to match larger pie chart
    const double holeDiameter = holeRadius * 2; // 130
    const double auraSize = holeDiameter - 8;  // 122

    return Stack(
      alignment: Alignment.center,
      children: [
        // 1) The PieChart itself (with a big center hole).
        PieChart(
          PieChartData(
            sections: sections,
            centerSpaceRadius: holeRadius,
            sectionsSpace: 2,
            startDegreeOffset: 270,
            pieTouchData: PieTouchData(enabled: false),
          ),
        ),

        // 2) On top of that hole, place the gender-based aura image
        SizedBox(
          width: auraSize,
          height: auraSize,
          child: Image.asset(
            _getAuraImageForGender(user.gender),
            fit: BoxFit.contain,
          ),
        ),
      ],
    );
  }

  String _getAuraImageForGender(String gender) {
    switch (gender.toLowerCase()) {
      case 'female':
        return 'assets/images/f3_aura.png';
      case 'non-binary':
      case 'nonbinary':
      case 'non binary':
        return 'assets/images/mana_aura.png';
      case 'male':
      default:
        return 'assets/images/m6_aura.png';
    }
  }

  /// Builds a center-aligned, descending EXP breakdown list with better spacing.
  Widget _buildBreakdownList(
    List<MapEntry<String, int>> entries,
    User user,
  ) {
    // Sort entries by EXP value in descending order (highest to lowest)
    final sortedEntries = List<MapEntry<String, int>>.from(entries);
    sortedEntries.sort((a, b) => b.value.compareTo(a.value));

    return Column(
      children: [
        // Header for the breakdown section - moved below pie chart with clear separation
        Padding(
          padding: const EdgeInsets.only(bottom: 16.0),
          child: Text(
            "CATEGORY BREAKDOWN",
            style: TextStyle(
              fontFamily: 'Pirulen',
              fontSize: 15, // Reduced to 15px for more subtle appearance
              color: Colors.white60,
            ),
            textAlign: TextAlign.center,
          ),
        ),

        // Non-scrollable column to show all categories at once
        Expanded(
          child: Column(
            children: sortedEntries.map((entry) {
              final cat = entry.key;
              final expVal = entry.value;

              // Determine color: if custom, red/orange; else getCategoryColor
              Color color;
              if (user.customCategories.contains(cat)) {
                final idx = user.customCategories.indexOf(cat);
                if (idx == 0) {
                  color = Colors.redAccent;
                } else if (idx == 1) {
                  color = Colors.orangeAccent;
                } else {
                  color = getCategoryColor(cat);
                }
              } else {
                color = getCategoryColor(cat);
              }

              return Container(
                margin: const EdgeInsets.symmetric(vertical: 4.0, horizontal: 10.0),
                padding: const EdgeInsets.symmetric(vertical: 6.0, horizontal: 12.0),
                decoration: BoxDecoration(
                  color: Colors.black26,
                  borderRadius: BorderRadius.circular(6.0),
                  border: Border.all(color: color.withValues(alpha: 0.3), width: 1),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // Category name
                    Expanded(
                      flex: 2,
                      child: Text(
                        cat.toUpperCase(),
                        style: TextStyle(
                          fontFamily: 'Pirulen',
                          fontSize: 12, // Increased back to 12 for better readability with larger modal
                          color: Colors.white,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),

                    // EXP value
                    Text(
                      expVal.toString(),
                      style: TextStyle(
                        fontFamily: 'Pirulen',
                        fontSize: 14, // Increased back to 14 for better readability with larger modal
                        color: color,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
          ),
        ),
      ],
    );
  }
}




