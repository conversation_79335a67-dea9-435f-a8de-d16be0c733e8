// 📁 lib/widgets/maxedout_appbar.dart

import 'package:flutter/material.dart';

class MaxedOutAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final List<Widget>? actions;
  final bool centerTitle;
  final bool useCustomFont;

  const MaxedOutAppBar({
    super.key,
    required this.title,
    this.actions,
    this.centerTitle = true,
    this.useCustomFont = true,
  });

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);

  @override
  Widget build(BuildContext context) {
    return AppBar(
      backgroundColor: Colors.black,
      elevation: 4,
      centerTitle: centerTitle,
      actions: actions,
      title: Text(
        title,
        style: TextStyle(
          fontFamily: useCustomFont ? 'Bitsumishi' : null,
          fontWeight: FontWeight.bold,
          color: Colors.white,
          fontSize: 20,
          letterSpacing: 1.2,
        ),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }
}
