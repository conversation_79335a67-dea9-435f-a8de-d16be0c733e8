import 'package:flutter/material.dart';
import '../theme/colors.dart';

/// 🔐 Sign-In Error Dialog System
/// 
/// Provides user-friendly, actionable error dialogs specifically for sign-in
/// failures. All dialogs follow MOL visual consistency with neon glow,
/// rainbow theme, and retro-futurism styling.
/// 
/// Features:
/// - Specific error messages for each sign-in scenario
/// - Actionable guidance for users
/// - Consistent MOL visual styling
/// - Recovery options and alternative actions
/// - Account lockout information with countdown
class SignInErrorDialogs {
  
  /// Show invalid credentials error
  static Future<void> showInvalidCredentialsError(
    BuildContext context, {
    VoidCallback? onTryAgain,
    VoidCallback? onForgotPassword,
    VoidCallback? onCreateAccount,
  }) async {
    return showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => _SignInErrorDialog(
        title: 'Invalid Credentials',
        message: 'The username or password you entered is incorrect. Please check your credentials and try again.',
        icon: Icons.lock_outline,
        iconColor: MolColors.red,
        primaryAction: _DialogAction(
          label: 'Try Again',
          onPressed: () {
            Navigator.of(context).pop();
            onTryAgain?.call();
          },
          color: MolColors.red,
        ),
        secondaryAction: _DialogAction(
          label: 'Forgot Password?',
          onPressed: () {
            Navigator.of(context).pop();
            onForgotPassword?.call();
          },
          color: MolColors.blue,
        ),
        tertiaryAction: _DialogAction(
          label: 'Create Account',
          onPressed: () {
            Navigator.of(context).pop();
            onCreateAccount?.call();
          },
          color: MolColors.purple,
        ),
      ),
    );
  }

  /// Show account locked error with countdown
  static Future<void> showAccountLockedError(
    BuildContext context, {
    required Duration remainingTime,
    VoidCallback? onContactSupport,
    VoidCallback? onTryLater,
  }) async {
    return showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => _AccountLockedDialog(
        remainingTime: remainingTime,
        onContactSupport: onContactSupport,
        onTryLater: onTryLater,
      ),
    );
  }

  /// Show user not found error
  static Future<void> showUserNotFoundError(
    BuildContext context, {
    required String username,
    VoidCallback? onTryDifferentUsername,
    VoidCallback? onCreateAccount,
  }) async {
    return showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => _SignInErrorDialog(
        title: 'Account Not Found',
        message: 'No account found with username "$username". Please check the username or create a new account.',
        icon: Icons.person_search_outlined,
        iconColor: MolColors.orange,
        primaryAction: _DialogAction(
          label: 'Try Different Username',
          onPressed: () {
            Navigator.of(context).pop();
            onTryDifferentUsername?.call();
          },
          color: MolColors.orange,
        ),
        secondaryAction: _DialogAction(
          label: 'Create Account',
          onPressed: () {
            Navigator.of(context).pop();
            onCreateAccount?.call();
          },
          color: MolColors.purple,
        ),
      ),
    );
  }

  /// Show connection error
  static Future<void> showConnectionError(
    BuildContext context, {
    VoidCallback? onRetry,
    VoidCallback? onOfflineMode,
  }) async {
    return showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => _SignInErrorDialog(
        title: 'Connection Failed',
        message: 'Unable to connect to MXD servers. Please check your internet connection and try again.',
        icon: Icons.wifi_off_outlined,
        iconColor: MolColors.red,
        primaryAction: _DialogAction(
          label: 'Retry',
          onPressed: () {
            Navigator.of(context).pop();
            onRetry?.call();
          },
          color: MolColors.red,
        ),
        secondaryAction: _DialogAction(
          label: 'Continue Offline',
          onPressed: () {
            Navigator.of(context).pop();
            onOfflineMode?.call();
          },
          color: MolColors.blue,
        ),
      ),
    );
  }

  /// Show session expired error
  static Future<void> showSessionExpiredError(
    BuildContext context, {
    VoidCallback? onSignInAgain,
    VoidCallback? onStaySignedOut,
  }) async {
    return showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => _SignInErrorDialog(
        title: 'Session Expired',
        message: 'Your session has expired for security reasons. Please sign in again to continue.',
        icon: Icons.access_time_outlined,
        iconColor: MolColors.yellow,
        primaryAction: _DialogAction(
          label: 'Sign In Again',
          onPressed: () {
            Navigator.of(context).pop();
            onSignInAgain?.call();
          },
          color: MolColors.yellow,
        ),
        secondaryAction: _DialogAction(
          label: 'Stay Signed Out',
          onPressed: () {
            Navigator.of(context).pop();
            onStaySignedOut?.call();
          },
          color: MolColors.gray,
        ),
      ),
    );
  }

  /// Show email verification required error
  static Future<void> showEmailVerificationRequired(
    BuildContext context, {
    required String email,
    VoidCallback? onResendVerification,
    VoidCallback? onContinueAnyway,
  }) async {
    return showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => _SignInErrorDialog(
        title: 'Email Verification Required',
        message: 'Please verify your email address to access all MXD features. Check your inbox for a verification link.',
        icon: Icons.mark_email_unread_outlined,
        iconColor: MolColors.blue,
        primaryAction: _DialogAction(
          label: 'Resend Verification',
          onPressed: () {
            Navigator.of(context).pop();
            onResendVerification?.call();
          },
          color: MolColors.blue,
        ),
        secondaryAction: _DialogAction(
          label: 'Continue Anyway',
          onPressed: () {
            Navigator.of(context).pop();
            onContinueAnyway?.call();
          },
          color: MolColors.purple,
        ),
      ),
    );
  }

  /// Show biometric authentication failed error
  static Future<void> showBiometricFailedError(
    BuildContext context, {
    VoidCallback? onTryPassword,
    VoidCallback? onTryBiometricAgain,
  }) async {
    return showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => _SignInErrorDialog(
        title: 'Biometric Authentication Failed',
        message: 'Biometric authentication was not successful. You can try again or use your password instead.',
        icon: Icons.fingerprint_outlined,
        iconColor: MolColors.orange,
        primaryAction: _DialogAction(
          label: 'Use Password',
          onPressed: () {
            Navigator.of(context).pop();
            onTryPassword?.call();
          },
          color: MolColors.orange,
        ),
        secondaryAction: _DialogAction(
          label: 'Try Biometric Again',
          onPressed: () {
            Navigator.of(context).pop();
            onTryBiometricAgain?.call();
          },
          color: MolColors.blue,
        ),
      ),
    );
  }
}

/// Internal dialog action model
class _DialogAction {
  final String label;
  final VoidCallback onPressed;
  final Color color;

  _DialogAction({
    required this.label,
    required this.onPressed,
    required this.color,
  });
}

/// Base sign-in error dialog widget with MOL styling
class _SignInErrorDialog extends StatelessWidget {
  final String title;
  final String message;
  final IconData icon;
  final Color iconColor;
  final _DialogAction primaryAction;
  final _DialogAction? secondaryAction;
  final _DialogAction? tertiaryAction;

  const _SignInErrorDialog({
    required this.title,
    required this.message,
    required this.icon,
    required this.iconColor,
    required this.primaryAction,
    this.secondaryAction,
    this.tertiaryAction,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.9),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: iconColor.withValues(alpha: 0.5),
            width: 2,
          ),
          boxShadow: [
            BoxShadow(
              color: iconColor.withValues(alpha: 0.3),
              blurRadius: 20,
              spreadRadius: 2,
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Error icon with glow
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: iconColor.withValues(alpha: 0.1),
                border: Border.all(
                  color: iconColor.withValues(alpha: 0.5),
                  width: 2,
                ),
                boxShadow: [
                  BoxShadow(
                    color: iconColor.withValues(alpha: 0.3),
                    blurRadius: 10,
                    spreadRadius: 1,
                  ),
                ],
              ),
              child: Icon(
                icon,
                size: 48,
                color: iconColor,
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Title
            Text(
              title,
              style: const TextStyle(
                fontFamily: 'Pirulen',
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
            
            const SizedBox(height: 16),
            
            // Message
            Text(
              message,
              style: const TextStyle(
                fontFamily: 'Bitsumishi',
                fontSize: 16,
                color: Colors.white70,
              ),
              textAlign: TextAlign.center,
            ),
            
            const SizedBox(height: 32),
            
            // Action buttons
            Column(
              children: [
                // Primary action
                Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: primaryAction.color.withValues(alpha: 0.3),
                        blurRadius: 8,
                        spreadRadius: 1,
                      ),
                    ],
                  ),
                  child: ElevatedButton(
                    onPressed: primaryAction.onPressed,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: primaryAction.color,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: Text(
                      primaryAction.label,
                      style: const TextStyle(
                        fontFamily: 'Pirulen',
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
                
                // Secondary action
                if (secondaryAction != null) ...[
                  const SizedBox(height: 12),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: secondaryAction!.onPressed,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.grey[800],
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                          side: BorderSide(
                            color: secondaryAction!.color.withValues(alpha: 0.5),
                            width: 1,
                          ),
                        ),
                      ),
                      child: Text(
                        secondaryAction!.label,
                        style: const TextStyle(
                          fontFamily: 'Pirulen',
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ],
                
                // Tertiary action
                if (tertiaryAction != null) ...[
                  const SizedBox(height: 8),
                  TextButton(
                    onPressed: tertiaryAction!.onPressed,
                    child: Text(
                      tertiaryAction!.label,
                      style: TextStyle(
                        fontFamily: 'Bitsumishi',
                        fontSize: 14,
                        color: tertiaryAction!.color,
                        decoration: TextDecoration.underline,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }
}

/// Account locked dialog with countdown timer
class _AccountLockedDialog extends StatefulWidget {
  final Duration remainingTime;
  final VoidCallback? onContactSupport;
  final VoidCallback? onTryLater;

  const _AccountLockedDialog({
    required this.remainingTime,
    this.onContactSupport,
    this.onTryLater,
  });

  @override
  State<_AccountLockedDialog> createState() => _AccountLockedDialogState();
}

class _AccountLockedDialogState extends State<_AccountLockedDialog>
    with TickerProviderStateMixin {
  late Duration _remainingTime;
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _remainingTime = widget.remainingTime;

    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
    _pulseController.repeat(reverse: true);

    // Start countdown timer
    _startCountdown();
  }

  @override
  void dispose() {
    _pulseController.dispose();
    super.dispose();
  }

  void _startCountdown() {
    Future.delayed(const Duration(seconds: 1), () {
      if (mounted) {
        setState(() {
          _remainingTime = Duration(seconds: _remainingTime.inSeconds - 1);
        });

        if (_remainingTime.inSeconds > 0) {
          _startCountdown();
        } else {
          // Time's up - close dialog
          Navigator.of(context).pop();
        }
      }
    });
  }

  String _formatTime(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.9),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: MolColors.red.withValues(alpha: 0.5),
            width: 2,
          ),
          boxShadow: [
            BoxShadow(
              color: MolColors.red.withValues(alpha: 0.3),
              blurRadius: 20,
              spreadRadius: 2,
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Animated lock icon
            AnimatedBuilder(
              animation: _pulseAnimation,
              builder: (context, child) {
                return Transform.scale(
                  scale: _pulseAnimation.value,
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: MolColors.red.withValues(alpha: 0.1),
                      border: Border.all(
                        color: MolColors.red.withValues(alpha: 0.5),
                        width: 2,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: MolColors.red.withValues(alpha: 0.3),
                          blurRadius: 10,
                          spreadRadius: 1,
                        ),
                      ],
                    ),
                    child: Icon(
                      Icons.lock_outlined,
                      size: 48,
                      color: MolColors.red,
                    ),
                  ),
                );
              },
            ),

            const SizedBox(height: 24),

            // Title
            const Text(
              'Account Temporarily Locked',
              style: TextStyle(
                fontFamily: 'Pirulen',
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 16),

            // Message
            const Text(
              'Too many failed sign-in attempts. Your account has been temporarily locked for security.',
              style: TextStyle(
                fontFamily: 'Bitsumishi',
                fontSize: 16,
                color: Colors.white70,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 24),

            // Countdown timer
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[900],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: MolColors.red.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: Column(
                children: [
                  const Text(
                    'Try again in:',
                    style: TextStyle(
                      fontFamily: 'Bitsumishi',
                      fontSize: 14,
                      color: Colors.white70,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _formatTime(_remainingTime),
                    style: TextStyle(
                      fontFamily: 'Digital-7',
                      fontSize: 32,
                      fontWeight: FontWeight.bold,
                      color: MolColors.red,
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 32),

            // Action buttons
            Column(
              children: [
                // Contact support button
                if (widget.onContactSupport != null)
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: widget.onContactSupport,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: MolColors.blue,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: const Text(
                        'Contact Support',
                        style: TextStyle(
                          fontFamily: 'Pirulen',
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),

                const SizedBox(height: 12),

                // Try later button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                      widget.onTryLater?.call();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.grey[800],
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                        side: BorderSide(
                          color: Colors.grey.withValues(alpha: 0.5),
                          width: 1,
                        ),
                      ),
                    ),
                    child: const Text(
                      'Try Later',
                      style: TextStyle(
                        fontFamily: 'Pirulen',
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
