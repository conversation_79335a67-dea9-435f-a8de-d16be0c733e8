// 📁 lib/Widgets/top_summary.dart

import 'package:flutter/material.dart';
import '../models/user_model.dart';

class TopSummary extends StatelessWidget {
  final User user;

  const TopSummary({super.key, required this.user});

  @override
  Widget build(BuildContext context) {
    final totalExp = user.exp;
    final avgPerDay = user.streakDays > 0 ? (totalExp / user.streakDays).round() : 0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "TOTAL EXP: $totalExp",
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontFamily: 'Bitsumishi',
                color: Colors.white,
              ),
        ),
        const SizedBox(height: 8),
        Text(
          "STREAK: ${user.streakDays} days",
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                fontFamily: 'Bitsumishi',
                color: Colors.white70,
              ),
        ),
        Text(
          "AVG / DAY: $avgPerDay",
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontFamily: 'Orbitron',
                color: Colors.white38,
              ),
        ),
      ],
    );
  }
}
