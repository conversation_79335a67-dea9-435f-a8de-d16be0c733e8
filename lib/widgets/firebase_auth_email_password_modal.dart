// lib/widgets/firebase_auth_email_password_modal.dart

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../controller/firebase_auth_controller.dart';
import 'password_requirements_checklist.dart';

/// Firebase-powered email/password modal for signup
class FirebaseAuthEmailPasswordModal extends StatefulWidget {
  final Function(String email, String password) onContinue;
  final VoidCallback? onContinueWithoutEmail;

  const FirebaseAuthEmailPasswordModal({
    super.key,
    required this.onContinue,
    this.onContinueWithoutEmail,
  });

  @override
  State<FirebaseAuthEmailPasswordModal> createState() => _FirebaseAuthEmailPasswordModalState();
}

class _FirebaseAuthEmailPasswordModalState extends State<FirebaseAuthEmailPasswordModal> {
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();

  bool _isLoading = false;
  String? _emailError;
  String? _passwordError;
  bool _showPasswordRequirements = false;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  void _validateEmail(String email) async {
    // First do basic format validation (immediate)
    if (email.isEmpty) {
      setState(() {
        _emailError = null;
      });
      return;
    }

    // Basic email format validation
    final emailRegex = RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$');
    if (!emailRegex.hasMatch(email)) {
      setState(() {
        _emailError = 'Please enter a valid email address';
      });
      return;
    }

    // Email format is valid, clear any errors
    setState(() {
      _emailError = null;
    });

    // Skip availability check for now - we'll check during signup
    // This prevents the "Unable to check email availability" error
  }

  void _validatePassword(String password) {
    setState(() {
      // Always show password requirements when there's text
      _showPasswordRequirements = password.isNotEmpty;

      if (password.isEmpty) {
        _passwordError = null;
      } else if (!PasswordRequirementsChecklist.isPasswordValid(password)) {
        _passwordError = null; // Let the checklist handle validation display
      } else {
        _passwordError = null;
      }
    });
  }

  bool get _canContinue {
    return _emailController.text.isNotEmpty &&
           _passwordController.text.isNotEmpty &&
           _emailError == null &&
           _passwordError == null &&
           PasswordRequirementsChecklist.isPasswordValid(_passwordController.text) &&
           !_isLoading;
  }

  Future<void> _handleContinue() async {
    if (!_canContinue) return;

    setState(() {
      _isLoading = true;
    });

    try {
      print('🔥 FirebaseAuthEmailPasswordModal: Starting Firebase signup process');
      
      // Validate inputs one more time
      _validateEmail(_emailController.text);
      _validatePassword(_passwordController.text);
      
      if (_emailError != null || _passwordError != null) {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
        return;
      }

      print('✅ FirebaseAuthEmailPasswordModal: Validation passed, calling onContinue');
      widget.onContinue(_emailController.text, _passwordController.text);

    } catch (e) {
      print('❌ FirebaseAuthEmailPasswordModal: Error during signup: $e');
      // 🛡️ PHASE 3: Add context checking for setState
      if (mounted) {
        setState(() {
          _emailError = 'An error occurred. Please try again.';
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<FirebaseAuthController>(
      builder: (context, authController, child) {
        // Show any Firebase auth errors
        if (authController.error != null) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            setState(() {
              _emailError = authController.error;
              _isLoading = false;
            });
          });
        }

        return Dialog(
          backgroundColor: Colors.black.withValues(alpha: 0.95),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
          child: Container(
            width: MediaQuery.of(context).size.width * 0.85,
            constraints: BoxConstraints(
              maxHeight: MediaQuery.of(context).size.height * 0.8,
            ),
            padding: const EdgeInsets.all(32),
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Title
                  const Text(
                    'Complete Your Account',
                    style: TextStyle(
                      color: Colors.cyanAccent,
                      fontFamily: 'Pirulen',
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'Enter your email and create a secure password',
                    style: TextStyle(
                      color: Colors.white70,
                      fontFamily: 'Bitsumishi',
                      fontSize: 16,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  
                  // Email verification warning
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.orange.withValues(alpha: 0.1),
                      border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.warning_amber_rounded,
                          color: Colors.orange,
                          size: 20,
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            'Important: You MUST verify your email on the same device you plan to use for this app!',
                            style: TextStyle(
                              color: Colors.orange,
                              fontFamily: 'Bitsumishi',
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 24),
                  
                  // Email field
                  TextField(
                    controller: _emailController,
                    onChanged: _validateEmail,
                    keyboardType: TextInputType.emailAddress,
                    textInputAction: TextInputAction.next,
                    autocorrect: false,
                    enableSuggestions: false,
                    style: const TextStyle(
                      color: Colors.white,
                      fontFamily: 'Digital-7',
                      fontSize: 16,
                    ),
                    decoration: InputDecoration(
                      labelText: 'Email',
                      labelStyle: const TextStyle(
                        color: Colors.white70,
                        fontFamily: 'Bitsumishi',
                      ),
                      errorText: _emailError,
                      errorStyle: const TextStyle(
                        color: Colors.redAccent,
                        fontFamily: 'Bitsumishi',
                        fontSize: 12,
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: const BorderSide(color: Colors.white30),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: const BorderSide(color: Colors.cyanAccent),
                      ),
                      errorBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: const BorderSide(color: Colors.redAccent),
                      ),
                      focusedErrorBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: const BorderSide(color: Colors.redAccent),
                      ),
                    ),
                  ),
                  const SizedBox(height: 20),
                  
                  // Password field
                  TextField(
                    controller: _passwordController,
                    onChanged: _validatePassword,
                    obscureText: true,
                    textInputAction: TextInputAction.done,
                    style: const TextStyle(
                      color: Colors.white,
                      fontFamily: 'Digital-7',
                      fontSize: 16,
                    ),
                    decoration: InputDecoration(
                      labelText: 'Password',
                      labelStyle: const TextStyle(
                        color: Colors.white70,
                        fontFamily: 'Bitsumishi',
                      ),
                      errorText: _passwordError,
                      errorStyle: const TextStyle(
                        color: Colors.redAccent,
                        fontFamily: 'Bitsumishi',
                        fontSize: 12,
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: const BorderSide(color: Colors.white30),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: const BorderSide(color: Colors.cyanAccent),
                      ),
                      errorBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: const BorderSide(color: Colors.redAccent),
                      ),
                      focusedErrorBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: const BorderSide(color: Colors.redAccent),
                      ),
                    ),
                  ),
                  
                  // Password requirements
                  if (_showPasswordRequirements) ...[
                    const SizedBox(height: 16),
                    PasswordRequirementsChecklist(password: _passwordController.text),
                  ],
                  
                  const SizedBox(height: 32),
                  
                  // Continue button
                  SizedBox(
                    width: double.infinity,
                    height: 50,
                    child: ElevatedButton(
                      onPressed: _canContinue ? _handleContinue : null,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: _canContinue ? Colors.cyanAccent : Colors.grey[700],
                        foregroundColor: Colors.black,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: _isLoading
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(Colors.black),
                              ),
                            )
                          : const Text(
                              'Continue',
                              style: TextStyle(
                                fontFamily: 'Pirulen',
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                    ),
                  ),

                  // Continue without email option (if callback provided)
                  if (widget.onContinueWithoutEmail != null) ...[
                    const SizedBox(height: 16),
                    SizedBox(
                      width: double.infinity,
                      height: 50,
                      child: TextButton(
                        onPressed: _isLoading ? null : widget.onContinueWithoutEmail,
                        style: TextButton.styleFrom(
                          foregroundColor: Colors.white70,
                          side: const BorderSide(color: Colors.white30),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: const Text(
                          'Continue Without Email',
                          style: TextStyle(
                            fontFamily: 'Bitsumishi',
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
