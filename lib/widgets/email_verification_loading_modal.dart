import 'package:flutter/material.dart';
import '../services/firebase_auth_service.dart';
import '../services/comprehensive_logging_service.dart';

class EmailVerificationLoadingModal extends StatefulWidget {
  final String email;
  final String password;
  final VoidCallback onSuccess;
  final Function(String) onError;

  const EmailVerificationLoadingModal({
    super.key,
    required this.email,
    required this.password,
    required this.onSuccess,
    required this.onError,
  });

  @override
  State<EmailVerificationLoadingModal> createState() => _EmailVerificationLoadingModalState();
}

class _EmailVerificationLoadingModalState extends State<EmailVerificationLoadingModal>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  String _statusMessage = 'Creating your account...';
  bool _isComplete = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(_animationController);
    _animationController.forward();
    _createAccount();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _createAccount() async {
    try {
      // Check for test email bypass
      if (widget.email == '<EMAIL>') {
        await _handleTestEmailBypass();
        return;
      }

      // Step 1: Create Firebase account
      setState(() {
        _statusMessage = 'Creating your account...';
      });
      await Future.delayed(const Duration(milliseconds: 800));

      final result = await FirebaseAuthService.createAccount(
        email: widget.email,
        password: widget.password,
        username: 'TempUser', // Will be updated later
        gender: 'Unknown', // Will be updated later
      );

      if (!result.success) {
        widget.onError(result.message);
        return;
      }

      // Step 2: Send email verification
      setState(() {
        _statusMessage = 'Sending verification email...';
      });
      await Future.delayed(const Duration(milliseconds: 800));

      final emailSent = await FirebaseAuthService.sendEmailVerification();
      if (!emailSent) {
        widget.onError('Failed to send verification email');
        return;
      }

      // Step 3: Success
      setState(() {
        _statusMessage = 'Verification email sent! ✅';
        _isComplete = true;
      });
      await Future.delayed(const Duration(milliseconds: 1000));

      widget.onSuccess();

    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Email verification loading modal error: $e');
      widget.onError('Account creation failed: $e');
    }
  }

  /// Handle test email bypass (<EMAIL>)
  Future<void> _handleTestEmailBypass() async {
    await ComprehensiveLoggingService.logInfo('🧪 Test email detected in loading modal, bypassing Firebase');

    // Step 1: Simulate account creation
    setState(() {
      _statusMessage = 'Creating test account...';
    });
    await Future.delayed(const Duration(milliseconds: 500));

    // Step 2: Simulate email verification
    setState(() {
      _statusMessage = 'Bypassing email verification...';
    });
    await Future.delayed(const Duration(milliseconds: 500));

    // Step 3: Success
    setState(() {
      _statusMessage = 'Test account ready! ✅';
      _isComplete = true;
    });
    await Future.delayed(const Duration(milliseconds: 500));

    // Call success immediately to prevent widget disposal issues
    await ComprehensiveLoggingService.logInfo('🧪 Loading modal calling onSuccess for test email');
    widget.onSuccess();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: const Color(0xFF1A1A1A),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: Colors.purple.withValues(alpha: 0.3),
              width: 1,
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Loading indicator
              if (!_isComplete) ...[
                SizedBox(
                  width: 60,
                  height: 60,
                  child: CircularProgressIndicator(
                    strokeWidth: 3,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      Colors.purple.withValues(alpha: 0.8),
                    ),
                  ),
                ),
              ] else ...[
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: Colors.green.withValues(alpha: 0.2),
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: Colors.green,
                      width: 2,
                    ),
                  ),
                  child: const Icon(
                    Icons.check,
                    color: Colors.green,
                    size: 30,
                  ),
                ),
              ],

              const SizedBox(height: 24),

              // Status message
              Text(
                _statusMessage,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: 16),

              // Email info
              Text(
                widget.email,
                style: TextStyle(
                  color: Colors.purple.withValues(alpha: 0.8),
                  fontSize: 14,
                ),
                textAlign: TextAlign.center,
              ),

              if (_isComplete) ...[
                const SizedBox(height: 16),
                Text(
                  'Check your inbox for the verification link',
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.7),
                    fontSize: 12,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}
