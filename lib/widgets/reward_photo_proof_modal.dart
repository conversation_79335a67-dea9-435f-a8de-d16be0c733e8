import 'dart:io';
import 'package:flutter/material.dart';
import 'image_picker.dart';

class RewardPhotoProofModal extends StatefulWidget {
  final void Function(String path) onPhotoSelected;
  final VoidCallback onClose;
  final String? initialPath;

  const RewardPhotoProofModal({
    super.key,
    required this.onPhotoSelected,
    required this.onClose,
    this.initialPath,
  });

  @override
  State<RewardPhotoProofModal> createState() => _RewardPhotoProofModalState();
}

class _RewardPhotoProofModalState extends State<RewardPhotoProofModal> {
  String? _selectedPath;

  @override
  void initState() {
    super.initState();
    _selectedPath = widget.initialPath;
  }

  void _handleImageSelected(String path) {
    setState(() => _selectedPath = path);
    widget.onPhotoSelected(path);
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.black,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('Photo Proof', style: TextStyle(fontFamily: 'Pirulen', fontSize: 20, color: Colors.white)),
                SizedBox(
                  width: 44,
                  height: 44,
                  child: IconButton(
                    icon: const Icon(Icons.close, color: Colors.white),
                    onPressed: widget.onClose,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 10),
            VisionImagePicker(
              onImageSelected: _handleImageSelected,
              initialPath: _selectedPath,
            ),
            if (_selectedPath != null) ...[
              const SizedBox(height: 16),
              Text('Selected Photo:', style: TextStyle(fontFamily: 'Pirulen', fontSize: 14, color: Colors.white)),
              const SizedBox(height: 8),
              ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: Image.file(
                  File(_selectedPath!),
                  height: 120,
                  width: double.infinity,
                  fit: BoxFit.cover,
                ),
              ),
              const SizedBox(height: 20),
              ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.amber,
                  foregroundColor: Colors.black,
                  elevation: 8,
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                  side: const BorderSide(color: Colors.yellow, width: 2),
                  shadowColor: Colors.yellow,
                  padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                ),
                onPressed: _selectedPath != null
                    ? () {
                        widget.onPhotoSelected(_selectedPath!);
                        widget.onClose();
                      }
                    : null,
                child: Text('LOCK IN PHOTO PROOF',
                    style: TextStyle(fontFamily: 'Pirulen', fontSize: 18, fontWeight: FontWeight.bold)),
              ),
            ],
          ],
        ),
      ),
    );
  }
} 