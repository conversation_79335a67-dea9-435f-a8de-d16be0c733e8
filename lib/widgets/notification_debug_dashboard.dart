import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../services/real_notification_service.dart';
import '../utils/debug_logger.dart';

/// Debug dashboard for monitoring notification system health
/// 
/// Features:
/// - Real-time system status monitoring
/// - Permission status tracking
/// - Notification history and metrics
/// - Failure point detection
/// - Manual testing capabilities
/// - Health check results display
class NotificationDebugDashboard extends StatefulWidget {
  const NotificationDebugDashboard({super.key});

  @override
  State<NotificationDebugDashboard> createState() => _NotificationDebugDashboardState();
}

class _NotificationDebugDashboardState extends State<NotificationDebugDashboard> {
  final RealNotificationService _notificationService = RealNotificationService();
  Map<String, dynamic> _debugMetrics = {};
  Map<String, dynamic> _healthStatus = {};
  List<PendingNotificationRequest> _pendingNotifications = [];
  bool _isLoading = true;
  String _permissionStatus = 'Unknown';
  int _failureCount = 0;
  String _lastNotificationTime = 'Never';

  @override
  void initState() {
    super.initState();
    _loadDashboardData();
  }

  /// Load all dashboard data
  Future<void> _loadDashboardData() async {
    setState(() => _isLoading = true);

    try {
      // Load debug metrics
      _debugMetrics = _notificationService.getDebugMetrics();

      // Load health status
      await _loadHealthStatus();

      // Load permission status
      await _loadPermissionStatus();

      // Load failure metrics
      await _loadFailureMetrics();

      // Load pending notifications
      _pendingNotifications = await _notificationService.getPendingNotifications();

      DebugLogger.log('NotificationDebugDashboard', 
        'Dashboard data loaded: ${_debugMetrics.length} metrics, ${_pendingNotifications.length} pending');

    } catch (e, stackTrace) {
      DebugLogger.error('NotificationDebugDashboard', 'Error loading dashboard data', e, stackTrace);
    } finally {
      setState(() => _isLoading = false);
    }
  }

  /// Load health check status
  Future<void> _loadHealthStatus() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final healthJson = prefs.getString('notification_health_check');
      
      if (healthJson != null) {
        _healthStatus = jsonDecode(healthJson);
      }
    } catch (e) {
      DebugLogger.error('NotificationDebugDashboard', 'Error loading health status', e, StackTrace.current);
    }
  }

  /// Load permission status
  Future<void> _loadPermissionStatus() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _permissionStatus = prefs.getString('notification_permission_status') ?? 'Unknown';
    } catch (e) {
      DebugLogger.error('NotificationDebugDashboard', 'Error loading permission status', e, StackTrace.current);
    }
  }

  /// Load failure metrics
  Future<void> _loadFailureMetrics() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _failureCount = prefs.getInt('notification_failure_count') ?? 0;
      _lastNotificationTime = prefs.getString('last_notification_sent') ?? 'Never';
    } catch (e) {
      DebugLogger.error('NotificationDebugDashboard', 'Error loading failure metrics', e, StackTrace.current);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        title: Text(
          'NOTIFICATION DEBUG',
          style: TextStyle(
            color: Colors.cyan,
            fontFamily: 'Pirulen',
            fontSize: 18,
          ),
        ),
        actions: [
          IconButton(
            onPressed: _loadDashboardData,
            icon: Icon(Icons.refresh, color: Colors.cyan),
          ),
        ],
      ),
      body: _isLoading
          ? Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Colors.cyan),
              ),
            )
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildSystemStatusCard(),
                  const SizedBox(height: 16),
                  _buildPermissionStatusCard(),
                  const SizedBox(height: 16),
                  _buildMetricsCard(),
                  const SizedBox(height: 16),
                  _buildPendingNotificationsCard(),
                  const SizedBox(height: 16),
                  _buildTestingCard(),
                  const SizedBox(height: 16),
                  _buildHealthCheckCard(),
                ],
              ),
            ),
    );
  }

  Widget _buildSystemStatusCard() {
    final isHealthy = _failureCount < 5 && _permissionStatus == 'granted';
    
    return _buildCard(
      title: 'SYSTEM STATUS',
      color: isHealthy ? Colors.green : Colors.red,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildStatusRow('Overall Health', isHealthy ? 'HEALTHY' : 'ISSUES DETECTED', isHealthy),
          _buildStatusRow('Service Initialized', _debugMetrics['initialization_success'] == true ? 'YES' : 'NO', 
            _debugMetrics['initialization_success'] == true),
          _buildStatusRow('Permission Status', _permissionStatus.toUpperCase(), _permissionStatus == 'granted'),
          _buildStatusRow('Failure Count', _failureCount.toString(), _failureCount < 5),
          _buildStatusRow('Last Notification', _formatTimestamp(_lastNotificationTime), true),
        ],
      ),
    );
  }

  Widget _buildPermissionStatusCard() {
    return _buildCard(
      title: 'PERMISSIONS',
      color: Colors.blue,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildStatusRow('Notification Permission', _permissionStatus.toUpperCase(), _permissionStatus == 'granted'),
          const SizedBox(height: 12),
          ElevatedButton(
            onPressed: () async {
              final granted = await _notificationService.requestPermissions();
              await _loadPermissionStatus();
              if (mounted) {
                setState(() {});

                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(granted ? 'Permissions granted!' : 'Permissions denied'),
                    backgroundColor: granted ? Colors.green : Colors.red,
                  ),
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
            ),
            child: Text('REQUEST PERMISSIONS'),
          ),
        ],
      ),
    );
  }

  Widget _buildMetricsCard() {
    return _buildCard(
      title: 'DEBUG METRICS',
      color: Colors.purple,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (_debugMetrics.isEmpty)
            Text('No metrics available', style: TextStyle(color: Colors.white70))
          else
            ..._debugMetrics.entries.map((entry) {
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 2),
                child: Row(
                  children: [
                    Expanded(
                      flex: 2,
                      child: Text(
                        entry.key,
                        style: TextStyle(color: Colors.white70, fontSize: 12),
                      ),
                    ),
                    Expanded(
                      flex: 3,
                      child: Text(
                        entry.value.toString(),
                        style: TextStyle(color: Colors.white, fontSize: 12),
                      ),
                    ),
                  ],
                ),
              );
            }),
        ],
      ),
    );
  }

  Widget _buildPendingNotificationsCard() {
    return _buildCard(
      title: 'PENDING NOTIFICATIONS',
      color: Colors.orange,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Count: ${_pendingNotifications.length}',
            style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          if (_pendingNotifications.isEmpty)
            Text('No pending notifications', style: TextStyle(color: Colors.white70))
          else
            ..._pendingNotifications.map((notification) {
              return Container(
                margin: const EdgeInsets.symmetric(vertical: 4),
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.3),
                  borderRadius: BorderRadius.circular(4),
                  border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('ID: ${notification.id}', style: TextStyle(color: Colors.white, fontSize: 12)),
                    Text('Title: ${notification.title ?? 'No title'}', style: TextStyle(color: Colors.white70, fontSize: 12)),
                    Text('Body: ${notification.body ?? 'No body'}', style: TextStyle(color: Colors.white70, fontSize: 12)),
                  ],
                ),
              );
            }),
        ],
      ),
    );
  }

  Widget _buildTestingCard() {
    return _buildCard(
      title: 'MANUAL TESTING',
      color: Colors.amber,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ElevatedButton(
            onPressed: () async {
              final success = await _notificationService.sendBountyNotification(
                user: _createTestUser(),
                bounty: _createTestBounty(),
                customMessage: 'Test notification from debug dashboard',
              );

              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(success ? 'Test notification sent!' : 'Failed to send notification'),
                    backgroundColor: success ? Colors.green : Colors.red,
                  ),
                );
              }
              
              await _loadDashboardData();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.amber,
              foregroundColor: Colors.black,
            ),
            child: Text('SEND TEST NOTIFICATION'),
          ),
          const SizedBox(height: 8),
          ElevatedButton(
            onPressed: () async {
              final scheduledTime = DateTime.now().add(Duration(seconds: 10));
              final success = await _notificationService.scheduleBountyNotification(
                user: _createTestUser(),
                bounty: _createTestBounty(),
                scheduledTime: scheduledTime,
                customMessage: 'Scheduled test notification',
              );

              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(success ? 'Test notification scheduled for 10 seconds!' : 'Failed to schedule notification'),
                    backgroundColor: success ? Colors.green : Colors.red,
                  ),
                );
              }
              
              await _loadDashboardData();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.amber,
              foregroundColor: Colors.black,
            ),
            child: Text('SCHEDULE TEST (10s)'),
          ),
        ],
      ),
    );
  }

  Widget _buildHealthCheckCard() {
    return _buildCard(
      title: 'HEALTH CHECK',
      color: Colors.teal,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (_healthStatus.isEmpty)
            Text('No health check data', style: TextStyle(color: Colors.white70))
          else
            ..._healthStatus.entries.map((entry) {
              return _buildStatusRow(entry.key, entry.value.toString(), true);
            }),
        ],
      ),
    );
  }

  Widget _buildCard({
    required String title,
    required Color color,
    required Widget child,
  }) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.8),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color, width: 2),
        boxShadow: [
          BoxShadow(
            color: color.withValues(alpha: 0.3),
            blurRadius: 10,
            spreadRadius: 2,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              color: color,
              fontSize: 16,
              fontFamily: 'Pirulen',
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          child,
        ],
      ),
    );
  }

  Widget _buildStatusRow(String label, String value, bool isGood) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: TextStyle(color: Colors.white70, fontSize: 14),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              value,
              style: TextStyle(
                color: isGood ? Colors.green : Colors.red,
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Icon(
            isGood ? Icons.check_circle : Icons.error,
            color: isGood ? Colors.green : Colors.red,
            size: 16,
          ),
        ],
      ),
    );
  }

  String _formatTimestamp(String timestamp) {
    if (timestamp == 'Never') return timestamp;
    
    try {
      final dateTime = DateTime.parse(timestamp);
      final now = DateTime.now();
      final difference = now.difference(dateTime);
      
      if (difference.inMinutes < 60) {
        return '${difference.inMinutes}m ago';
      } else if (difference.inHours < 24) {
        return '${difference.inHours}h ago';
      } else {
        return '${difference.inDays}d ago';
      }
    } catch (e) {
      return 'Invalid';
    }
  }

  // Helper methods for testing
  dynamic _createTestUser() {
    return {
      'id': 'test-user',
      'username': 'TestUser',
      'gender': 'male',
    };
  }

  dynamic _createTestBounty() {
    return {
      'id': 'test-bounty',
      'description': 'Debug test bounty',
      'isEpic': false,
    };
  }
}
