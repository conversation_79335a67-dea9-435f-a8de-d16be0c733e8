import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../design_system/design_system.dart';
import '../utils/audio_popup_utils.dart';

/// Comprehensive error state widget with recovery actions.
/// 
/// Provides consistent error display across the app with:
/// - User-friendly error messages
/// - Clear recovery actions
/// - Visual feedback and animations
/// - Accessibility support
/// - Audio feedback for errors
/// 
/// Example usage:
/// ```dart
/// ErrorStateWidget(
///   error: NetworkError('Connection failed'),
///   onRetry: () => _loadData(),
///   onDismiss: () => Navigator.pop(context),
/// )
/// ```
class ErrorStateWidget extends StatefulWidget {
  /// The error to display
  final Object error;
  
  /// Optional custom error message override
  final String? customMessage;
  
  /// Callback for retry action
  final VoidCallback? onRetry;
  
  /// Callback for dismiss action
  final VoidCallback? onDismiss;
  
  /// Callback for secondary action (e.g., "Contact Support")
  final VoidCallback? onSecondaryAction;
  
  /// Label for secondary action button
  final String? secondaryActionLabel;
  
  /// Whether to show as a full-screen error or inline
  final bool isFullScreen;
  
  /// Whether to play error sound
  final bool playSound;
  
  /// Custom icon for the error
  final IconData? customIcon;
  
  /// Custom color scheme
  final Color? accentColor;

  const ErrorStateWidget({
    super.key,
    required this.error,
    this.customMessage,
    this.onRetry,
    this.onDismiss,
    this.onSecondaryAction,
    this.secondaryActionLabel,
    this.isFullScreen = false,
    this.playSound = true,
    this.customIcon,
    this.accentColor,
  });

  @override
  State<ErrorStateWidget> createState() => _ErrorStateWidgetState();
}

class _ErrorStateWidgetState extends State<ErrorStateWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _playErrorFeedback();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: DesignTokens.durationSlow,
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    ));

    _animationController.forward();
  }

  void _playErrorFeedback() {
    if (widget.playSound) {
      AudioPopupUtils.playSound('HSError2.mp3').catchError((_) {
        // Ignore audio errors
      });
    }
    HapticFeedback.mediumImpact();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.isFullScreen) {
      return _buildFullScreenError();
    } else {
      return _buildInlineError();
    }
  }

  Widget _buildFullScreenError() {
    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: _buildErrorContent(),
      ),
    );
  }

  Widget _buildInlineError() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(DesignTokens.radiusXl),
        border: Border.all(
          color: widget.accentColor ?? Colors.redAccent,
          width: DesignTokens.borderMedium,
        ),
      ),
      child: _buildErrorContent(),
    );
  }

  Widget _buildErrorContent() {
    final errorInfo = _getErrorInfo();
    
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Error Icon
                  _buildErrorIcon(errorInfo),
                  const SizedBox(height: 24),
                  
                  // Error Title
                  Text(
                    errorInfo.title,
                    style: TextStyle(
                      fontFamily: 'Pirulen',
                      fontSize: widget.isFullScreen ? 24 : 20,
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  
                  // Error Message
                  Text(
                    widget.customMessage ?? errorInfo.message,
                    style: TextStyle(
                      fontFamily: 'Bitsumishi',
                      fontSize: widget.isFullScreen ? 16 : 14,
                      color: Colors.white70,
                      height: 1.5,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 32),
                  
                  // Action Buttons
                  _buildActionButtons(errorInfo),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildErrorIcon(ErrorInfo errorInfo) {
    final color = widget.accentColor ?? errorInfo.color;
    final icon = widget.customIcon ?? errorInfo.icon;
    
    return Container(
      width: 80,
      height: 80,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: color.withValues(alpha: 0.1),
        border: Border.all(color: color, width: 2),
      ),
      child: Icon(
        icon,
        size: 40,
        color: color,
      ),
    );
  }

  Widget _buildActionButtons(ErrorInfo errorInfo) {
    final buttons = <Widget>[];
    
    // Primary action (usually retry)
    if (widget.onRetry != null) {
      buttons.add(
        _buildActionButton(
          label: errorInfo.primaryActionLabel,
          onPressed: widget.onRetry!,
          isPrimary: true,
        ),
      );
    }
    
    // Secondary action
    if (widget.onSecondaryAction != null) {
      buttons.add(
        _buildActionButton(
          label: widget.secondaryActionLabel ?? 'Contact Support',
          onPressed: widget.onSecondaryAction!,
          isPrimary: false,
        ),
      );
    }
    
    // Dismiss action
    if (widget.onDismiss != null) {
      buttons.add(
        _buildActionButton(
          label: 'Dismiss',
          onPressed: widget.onDismiss!,
          isPrimary: false,
        ),
      );
    }
    
    if (buttons.isEmpty) return const SizedBox.shrink();
    
    return Column(
      children: [
        for (int i = 0; i < buttons.length; i++) ...[
          buttons[i],
          if (i < buttons.length - 1) const SizedBox(height: 12),
        ],
      ],
    );
  }

  Widget _buildActionButton({
    required String label,
    required VoidCallback onPressed,
    required bool isPrimary,
  }) {
    final color = widget.accentColor ?? (isPrimary ? MolColors.cyan : Colors.grey);
    
    return SizedBox(
      width: widget.isFullScreen ? 200 : 160,
      height: 48,
      child: ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: isPrimary ? color : Colors.transparent,
          foregroundColor: isPrimary ? Colors.black : color,
          side: BorderSide(color: color, width: 2),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(24),
          ),
          elevation: isPrimary ? 4 : 0,
        ),
        child: Text(
          label,
          style: TextStyle(
            fontFamily: 'Bitsumishi',
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  ErrorInfo _getErrorInfo() {
    final error = widget.error;
    
    // Network errors
    if (error.toString().toLowerCase().contains('network') ||
        error.toString().toLowerCase().contains('connection') ||
        error.toString().toLowerCase().contains('internet')) {
      return ErrorInfo(
        title: 'Connection Problem',
        message: 'Unable to connect to the internet. Please check your connection and try again.',
        icon: Icons.wifi_off,
        color: Colors.orange,
        primaryActionLabel: 'Retry',
      );
    }
    
    // Data loading errors
    if (error.toString().toLowerCase().contains('load') ||
        error.toString().toLowerCase().contains('fetch')) {
      return ErrorInfo(
        title: 'Loading Failed',
        message: 'Failed to load data. This might be a temporary issue.',
        icon: Icons.refresh,
        color: Colors.blue,
        primaryActionLabel: 'Try Again',
      );
    }
    
    // Validation errors
    if (error.toString().toLowerCase().contains('validation') ||
        error.toString().toLowerCase().contains('invalid')) {
      return ErrorInfo(
        title: 'Invalid Data',
        message: 'The information provided is not valid. Please check and try again.',
        icon: Icons.warning,
        color: Colors.amber,
        primaryActionLabel: 'Fix',
      );
    }
    
    // Permission errors
    if (error.toString().toLowerCase().contains('permission') ||
        error.toString().toLowerCase().contains('access')) {
      return ErrorInfo(
        title: 'Access Denied',
        message: 'Permission required to perform this action. Please grant access and try again.',
        icon: Icons.lock,
        color: Colors.red,
        primaryActionLabel: 'Grant Access',
      );
    }
    
    // Default error
    return ErrorInfo(
      title: 'Something Went Wrong',
      message: 'An unexpected error occurred. Please try again or contact support if the problem persists.',
      icon: Icons.error_outline,
      color: Colors.redAccent,
      primaryActionLabel: 'Retry',
    );
  }
}

/// Information about an error for display purposes
class ErrorInfo {
  final String title;
  final String message;
  final IconData icon;
  final Color color;
  final String primaryActionLabel;

  const ErrorInfo({
    required this.title,
    required this.message,
    required this.icon,
    required this.color,
    required this.primaryActionLabel,
  });
}
