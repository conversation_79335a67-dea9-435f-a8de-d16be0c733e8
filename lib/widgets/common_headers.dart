// 📁 lib/widgets/common_headers.dart

import 'package:flutter/material.dart';

/// Consolidated header widgets to reduce duplicate header implementations across the app.
/// 
/// Provides consistent header styling and behavior for modals, screens, and widgets
/// while maintaining the MXD retro-futuristic design language.
class CommonHeaders {
  
  /// Standard modal header with icon, title, subtitle, and close button
  static Widget modalHeader({
    required String title,
    required Color glowColor,
    IconData? icon,
    String? subtitle,
    VoidCallback? onClose,
    double iconSize = 20,
    double titleFontSize = 16,
    double subtitleFontSize = 10,
    bool showCloseButton = true,
  }) {
    return Row(
      children: [
        if (icon != null) ...[
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: glowColor.withValues(alpha: 0.6),
                  blurRadius: 8,
                  spreadRadius: 2,
                ),
              ],
            ),
            child: Icon(
              icon,
              color: glowColor,
              size: iconSize,
            ),
          ),
          const SizedBox(width: 12),
        ],
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: titleFontSize,
                  fontFamily: 'Pirulen',
                  fontWeight: FontWeight.bold,
                  shadows: [
                    Shadow(
                      color: glowColor.withValues(alpha: 0.8),
                      offset: const Offset(0, 0),
                      blurRadius: 10,
                    ),
                  ],
                ),
              ),
              if (subtitle != null)
                Text(
                  subtitle,
                  style: TextStyle(
                    color: glowColor.withValues(alpha: 0.7),
                    fontSize: subtitleFontSize,
                    fontFamily: 'Bitsumishi',
                    fontStyle: FontStyle.italic,
                  ),
                ),
            ],
          ),
        ),
        if (showCloseButton && onClose != null)
          IconButton(
            onPressed: onClose,
            icon: const Icon(Icons.close, color: Colors.white70, size: 20),
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
          ),
      ],
    );
  }

  /// Coach header with coach-specific styling
  static Widget coachHeader({
    required String coachName,
    required Color glowColor,
    String? subtitle,
    VoidCallback? onClose,
    bool isCompact = false,
  }) {
    return modalHeader(
      title: 'ASK $coachName',
      subtitle: subtitle ?? 'Your AI coach is ready to help',
      icon: Icons.psychology,
      glowColor: glowColor,
      onClose: onClose,
      iconSize: isCompact ? 16 : 20,
      titleFontSize: isCompact ? 14 : 16,
      subtitleFontSize: isCompact ? 8 : 10,
    );
  }

  /// Training header with training-specific styling
  static Widget trainingHeader({
    required String title,
    required Color glowColor,
    String? subtitle,
    VoidCallback? onClose,
    bool showTimer = false,
    String? timerText,
  }) {
    return modalHeader(
      title: title,
      subtitle: subtitle ?? (showTimer && timerText != null ? timerText : 'Training in progress'),
      icon: Icons.fitness_center,
      glowColor: glowColor,
      onClose: onClose,
    );
  }

  /// Category header with category-specific styling
  static Widget categoryHeader({
    required String categoryName,
    required Color glowColor,
    String? subtitle,
    VoidCallback? onClose,
    IconData? categoryIcon,
  }) {
    return modalHeader(
      title: categoryName.toUpperCase(),
      subtitle: subtitle,
      icon: categoryIcon ?? Icons.category,
      glowColor: glowColor,
      onClose: onClose,
    );
  }

  /// Settings header with settings-specific styling
  static Widget settingsHeader({
    required String title,
    required Color glowColor,
    String? subtitle,
    VoidCallback? onClose,
  }) {
    return modalHeader(
      title: title,
      subtitle: subtitle ?? 'Customize your MXD experience',
      icon: Icons.settings,
      glowColor: glowColor,
      onClose: onClose,
    );
  }

  /// Simple header without icon (for minimal designs)
  static Widget simpleHeader({
    required String title,
    required Color glowColor,
    String? subtitle,
    VoidCallback? onClose,
    double titleFontSize = 18,
    double subtitleFontSize = 12,
  }) {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: titleFontSize,
                  fontFamily: 'Pirulen',
                  fontWeight: FontWeight.bold,
                  shadows: [
                    Shadow(
                      color: glowColor.withValues(alpha: 0.8),
                      offset: const Offset(0, 0),
                      blurRadius: 10,
                    ),
                  ],
                ),
              ),
              if (subtitle != null) ...[
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: TextStyle(
                    color: glowColor.withValues(alpha: 0.7),
                    fontSize: subtitleFontSize,
                    fontFamily: 'Bitsumishi',
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ],
            ],
          ),
        ),
        if (onClose != null)
          IconButton(
            onPressed: onClose,
            icon: const Icon(Icons.close, color: Colors.white70, size: 20),
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
          ),
      ],
    );
  }

  /// Header with custom widget (for special cases)
  static Widget customHeader({
    required Widget child,
    VoidCallback? onClose,
    EdgeInsets padding = const EdgeInsets.all(16),
  }) {
    return Padding(
      padding: padding,
      child: Row(
        children: [
          Expanded(child: child),
          if (onClose != null)
            IconButton(
              onPressed: onClose,
              icon: const Icon(Icons.close, color: Colors.white70, size: 20),
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
            ),
        ],
      ),
    );
  }

  /// Header with action buttons (for complex interactions)
  static Widget actionHeader({
    required String title,
    required Color glowColor,
    String? subtitle,
    List<Widget>? actions,
    IconData? icon,
  }) {
    return Row(
      children: [
        if (icon != null) ...[
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: glowColor.withValues(alpha: 0.6),
                  blurRadius: 8,
                  spreadRadius: 2,
                ),
              ],
            ),
            child: Icon(
              icon,
              color: glowColor,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
        ],
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontFamily: 'Pirulen',
                  fontWeight: FontWeight.bold,
                  shadows: [
                    Shadow(
                      color: glowColor.withValues(alpha: 0.8),
                      offset: const Offset(0, 0),
                      blurRadius: 10,
                    ),
                  ],
                ),
              ),
              if (subtitle != null)
                Text(
                  subtitle,
                  style: TextStyle(
                    color: glowColor.withValues(alpha: 0.7),
                    fontSize: 10,
                    fontFamily: 'Bitsumishi',
                    fontStyle: FontStyle.italic,
                  ),
                ),
            ],
          ),
        ),
        if (actions != null) ...actions,
      ],
    );
  }
}
