// 📁 lib/widgets/image_picker.dart

import 'dart:io';
import 'package:flutter/material.dart';
import '../services/robust_image_service.dart';


class VisionImagePicker extends StatefulWidget {
  final Function(String path) onImageSelected;
  final String? initialPath;

  const VisionImagePicker({
    super.key,
    required this.onImageSelected,
    this.initialPath,
  });

  @override
  State<VisionImagePicker> createState() => _VisionImagePickerState();
}

class _VisionImagePickerState extends State<VisionImagePicker> {
  File? _selectedImage;

  @override
  void initState() {
    super.initState();
    if (widget.initialPath != null) {
      _selectedImage = File(widget.initialPath!);
    }
  }

  Future<void> _pickImage() async {
    try {
      // Use robust image service to completely avoid iOS photo picker issues
      final String? imagePath = await RobustImageService.selectImageSafely(
        context: 'vision_image_picker',
        allowCamera: false,
      );

      if (imagePath != null) {
        // Validate the selected image
        final bool isValid = await RobustImageService.validateImageFile(imagePath);

        if (isValid && mounted) {
          setState(() => _selectedImage = File(imagePath));
          widget.onImageSelected(imagePath);
        } else if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Selected image file is not valid'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      // Handle any errors gracefully
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error selecting image: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (_selectedImage != null)
          ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: Image.file(
              _selectedImage!,
              height: 120,
              width: double.infinity,
              fit: BoxFit.cover,
            ),
          ),
        const SizedBox(height: 10),
        ElevatedButton.icon(
          onPressed: _pickImage,
          icon: const Icon(Icons.image),
          label: Text(
            _selectedImage == null ? "Add Photo Proof" : "Change Photo Proof",
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.cyanAccent,
            foregroundColor: Colors.black,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        ),
      ],
    );
  }
}
