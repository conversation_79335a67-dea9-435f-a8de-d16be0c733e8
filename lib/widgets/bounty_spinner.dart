import 'package:flutter/material.dart';
import 'dart:math';
import '../services/reward_engine.dart';
import '../models/user_model.dart';

class BountySpinner extends StatefulWidget {
  final List<String> categories;
  final List<String> customCategories;
  final Color glowColor;
  final User user;
  final void Function(String category, SpinResult result, User updatedUser) onSpinComplete;
  final VoidCallback onClose;

  const BountySpinner({
    super.key,
    required this.categories,
    required this.customCategories,
    required this.glowColor,
    required this.user,
    required this.onSpinComplete,
    required this.onClose,
  });

  @override
  State<BountySpinner> createState() => _BountySpinnerState();
}

class _BountySpinnerState extends State<BountySpinner> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;
  int? _selectedIndex;
  bool _spinning = false;

  @override
  void initState() {
    super.initState();
    // Start with a default duration - will be randomized per spin
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 3),
    );
    _animation = Tween<double>(begin: 0, end: 1).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOutCubic,
    ));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _spin() async {
    if (_spinning) return;
    setState(() => _spinning = true);

    final random = Random();
    final sectors = widget.categories.length;

    // Much more randomization
    final selected = random.nextInt(sectors);

    // Variable spin duration: 2-6 seconds for more excitement
    final spinDurationMs = 2000 + random.nextInt(4000); // 2-6 seconds

    // More spins: 8-15 full rotations instead of 5-7
    final baseSpins = 8 + random.nextInt(8); // 8-15 full spins

    // Add extra random offset within the selected sector for more realistic landing
    final anglePerSector = 2 * pi / sectors;
    final randomOffsetWithinSector = (random.nextDouble() - 0.5) * anglePerSector * 0.8; // ±40% of sector width

    // Calculate target angle with more randomization
    final targetAngle = (baseSpins * 2 * pi) +
                       (selected * anglePerSector) +
                       (anglePerSector / 2) +
                       randomOffsetWithinSector;

    // Randomize the easing curve for different spin feels
    final curves = [
      Curves.easeInOutCubic,
      Curves.easeInOutQuart,
      Curves.easeInOutExpo,
      Curves.easeInOutCirc,
      Curves.easeInOutBack,
    ];
    final selectedCurve = curves[random.nextInt(curves.length)];

    // Update controller duration and animation
    _controller.duration = Duration(milliseconds: spinDurationMs);
    _controller.reset();

    _animation = Tween<double>(begin: 0, end: targetAngle).animate(CurvedAnimation(
      parent: _controller,
      curve: selectedCurve,
    ));

    // Start spinning
    await _controller.forward();

    // Show result
    setState(() => _selectedIndex = selected);

    // Variable delay before completing (300-1200ms)
    final resultDelay = 300 + random.nextInt(900);
    await Future.delayed(Duration(milliseconds: resultDelay));

    // Get spin result from RewardEngine with the landed category
    final selectedCategory = widget.categories[selected];
    final result = RewardEngine.instance.spinWheelWithCategory(widget.user, selectedCategory);
    final updatedUser = RewardEngine.instance.useSpinnerPlay(widget.user, result);

    // Complete with the selected category, result, and updated user
    widget.onSpinComplete(selectedCategory, result, updatedUser);
  }

  @override
  Widget build(BuildContext context) {
    final size = 260.0;

    return Stack(
      children: [
        Center(
          child: Container(
            width: size + 32,
            height: size + 32,
            decoration: BoxDecoration(
              boxShadow: [
                BoxShadow(color: widget.glowColor, blurRadius: 48, spreadRadius: 8),
              ],
            ),
            child: AnimatedBuilder(
              animation: _animation,
              builder: (context, child) {
                return Transform.rotate(
                  angle: _animation.value,
                  child: CustomPaint(
                    size: Size(size, size),
                    painter: _SpinnerPainter(
                      categories: widget.categories,
                      customCategories: widget.customCategories,
                      glowColor: widget.glowColor,
                      selectedIndex: _selectedIndex,
                    ),
                  ),
                );
              },
            ),
          ),
        ),
        Positioned(
          left: 0,
          right: 0,
          top: MediaQuery.of(context).size.height / 2 - 30,
          child: Center(
            child: ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.black,
                foregroundColor: Colors.yellow,
                elevation: 8,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(32)),
                side: BorderSide(color: widget.glowColor, width: 2),
                shadowColor: widget.glowColor,
              ),
              onPressed: _spinning ? null : _spin,
              child: Text('SPIN', style: TextStyle(fontFamily: 'Pirulen', fontSize: 20, color: Colors.white)),
            ),
          ),
        ),
        Positioned(
          right: 24,
          top: 48,
          child: SizedBox(
            width: 44,
            height: 44,
            child: IconButton(
              icon: Icon(Icons.close, color: Colors.white, size: 32, shadows: [
                BoxShadow(color: widget.glowColor, blurRadius: 16, spreadRadius: 2),
              ]),
              onPressed: widget.onClose,
            ),
          ),
        ),
      ],
    );
  }
}

class _SpinnerPainter extends CustomPainter {
  final List<String> categories;
  final List<String> customCategories;
  final Color glowColor;
  final int? selectedIndex;

  _SpinnerPainter({
    required this.categories,
    required this.customCategories,
    required this.glowColor,
    required this.selectedIndex,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2;
    final anglePerSector = 2 * pi / categories.length;
    final paint = Paint()
      ..style = PaintingStyle.fill;

    for (int i = 0; i < categories.length; i++) {
      final color = getCategoryColor(categories[i], customCategories: customCategories);
      paint.color = color.withValues(alpha: selectedIndex == i ? 0.95 : 0.7);
      canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius),
        i * anglePerSector,
        anglePerSector,
        true,
        paint,
      );
    }
    // Draw center circle
    paint.color = Colors.black;
    canvas.drawCircle(center, radius * 0.35, paint);
    // Draw category labels radially (from outer edge toward center)
    for (int i = 0; i < categories.length; i++) {
      final angle = (i + 0.5) * anglePerSector - pi / 2;

      // Get the actual category name (use custom category names if applicable)
      String categoryName = categories[i];
      if (categoryName == 'Custom Category 1' && customCategories.isNotEmpty) {
        categoryName = customCategories[0];
      } else if (categoryName == 'Custom Category 2' && customCategories.length > 1) {
        categoryName = customCategories[1];
      }

      // Save canvas state
      canvas.save();

      // Translate to center and rotate for radial text
      canvas.translate(center.dx, center.dy);
      canvas.rotate(angle + pi / 2); // Rotate so text goes from edge to center

      final textPainter = TextPainter(
        text: TextSpan(
          text: categoryName.toUpperCase(),
          style: TextStyle(
            fontFamily: 'Pirulen',
            fontSize: 12,
            color: Colors.white,
            fontWeight: FontWeight.bold,
            shadows: [Shadow(color: glowColor, blurRadius: 8)],
          ),
        ),
        textAlign: TextAlign.center,
        textDirection: TextDirection.ltr,
      )..layout();

      // Position text to start from outer edge and travel toward center
      final textStartRadius = radius * 0.85; // Start near outer edge
      textPainter.paint(
        canvas,
        Offset(-textPainter.width / 2, -textStartRadius),
      );

      // Restore canvas state
      canvas.restore();
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// Helper function to get category colors
Color getCategoryColor(String category, {List<String>? customCategories}) {
  switch (category.toLowerCase()) {
    case 'health':
      return Colors.green;
    case 'wealth':
      return Colors.amber;
    case 'purpose':
      return Colors.purple;
    case 'connection':
      return Colors.blue;
    case 'custom category 1':
      return Colors.red; // Custom Category 1 uses red
    case 'custom category 2':
      return Colors.orange; // Custom Category 2 uses orange
    default:
      // Check if it's a custom category by name
      if (customCategories != null && customCategories.isNotEmpty) {
        if (category == customCategories[0]) {
          return Colors.red; // First custom category is red
        } else if (customCategories.length > 1 && category == customCategories[1]) {
          return Colors.orange; // Second custom category is orange
        }
      }
      return Colors.grey;
  }
}