// lib/widgets/spiritual_profile_modal.dart

import 'package:flutter/material.dart';
import '../services/user_spiritual_profile_service.dart';
import '../theme/colors.dart';
import '../home/<USER>';

/// CRITICAL: Spiritual profile setup modal with utmost care and respect
/// 
/// This modal appears when Connection coach first contacts user.
/// It ensures we NEVER provide inappropriate religious guidance.
class SpiritualProfileModal extends StatefulWidget {
  final String userId;
  final VoidCallback onComplete;
  
  const SpiritualProfileModal({
    super.key,
    required this.userId,
    required this.onComplete,
  });
  
  @override
  State<SpiritualProfileModal> createState() => _SpiritualProfileModalState();
}

class _SpiritualProfileModalState extends State<SpiritualProfileModal> {
  SpiritualDenomination? _selectedDenomination;
  ComfortLevel _comfortLevel = ComfortLevel.neutral;
  String _customDenomination = '';
  bool _isLoading = false;
  
  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        constraints: const BoxConstraints(maxWidth: 400, maxHeight: 600),
        decoration: BoxDecoration(
          color: Colors.grey[900],
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: MolColors.yellow, width: 2),
          boxShadow: [
            BoxShadow(
              color: MolColors.yellow.withValues(alpha: 0.3),
              blurRadius: 20,
              spreadRadius: 5,
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header
              _buildHeader(),
              const SizedBox(height: 24),
              
              // Content
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      _buildIntroText(),
                      const SizedBox(height: 24),
                      _buildDenominationSelection(),
                      if (_selectedDenomination != null && _isChristianDenomination()) ...[
                        const SizedBox(height: 24),
                        _buildComfortLevelSelection(),
                      ],
                      if (_selectedDenomination == SpiritualDenomination.other) ...[
                        const SizedBox(height: 16),
                        _buildCustomDenominationInput(),
                      ],
                    ],
                  ),
                ),
              ),
              
              // Buttons
              const SizedBox(height: 24),
              _buildButtons(),
            ],
          ),
        ),
      ),
    );
  }
  
  Widget _buildHeader() {
    return Row(
      children: [
        Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            border: Border.all(color: MolColors.yellow, width: 2),
          ),
          child: Icon(
            Icons.favorite,
            color: MolColors.yellow,
            size: 24,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              StrokeText(
                text: 'Spiritual Journey',
                textStyle: const TextStyle(
                  fontSize: 20,
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
                strokeWidth: 2,
                strokeColor: Colors.black,
              ),
              Text(
                'Help me understand your faith',
                style: TextStyle(
                  color: Colors.grey[400],
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
  
  Widget _buildIntroText() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[850],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: MolColors.yellow.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Text(
            'To provide you with the most meaningful guidance, I\'d love to understand your spiritual background.',
            style: TextStyle(
              color: Colors.grey[300],
              fontSize: 16,
              height: 1.4,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 12),
          Text(
            'This helps me share wisdom that resonates with your beliefs and values. Your privacy is completely respected.',
            style: TextStyle(
              color: Colors.grey[400],
              fontSize: 14,
              height: 1.4,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
  
  Widget _buildDenominationSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        StrokeText(
          text: 'What faith tradition do you follow?',
          textStyle: const TextStyle(
            fontSize: 16,
            color: Colors.white,
            fontWeight: FontWeight.w600,
          ),
          strokeWidth: 1,
          strokeColor: Colors.black,
        ),
        const SizedBox(height: 16),
        ...SpiritualDenomination.values.map((denomination) {
          return _buildDenominationOption(denomination);
        }),
      ],
    );
  }
  
  Widget _buildDenominationOption(SpiritualDenomination denomination) {
    final isSelected = _selectedDenomination == denomination;
    final displayName = _getDenominationDisplayName(denomination);
    
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: GestureDetector(
        onTap: () {
          setState(() {
            _selectedDenomination = denomination;
            if (!_isChristianDenomination()) {
              _comfortLevel = ComfortLevel.comfortable; // Default for non-Christian
            }
          });
        },
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: isSelected ? MolColors.yellow.withValues(alpha: 0.2) : Colors.grey[800],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: isSelected ? MolColors.yellow : Colors.grey[600]!,
              width: isSelected ? 2 : 1,
            ),
          ),
          child: Row(
            children: [
              Icon(
                isSelected ? Icons.radio_button_checked : Icons.radio_button_unchecked,
                color: isSelected ? MolColors.yellow : Colors.grey[400],
                size: 20,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  displayName,
                  style: TextStyle(
                    color: isSelected ? Colors.white : Colors.grey[300],
                    fontSize: 14,
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  Widget _buildComfortLevelSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        StrokeText(
          text: 'How comfortable are you with faith-based guidance?',
          textStyle: const TextStyle(
            fontSize: 16,
            color: Colors.white,
            fontWeight: FontWeight.w600,
          ),
          strokeWidth: 1,
          strokeColor: Colors.black,
        ),
        const SizedBox(height: 16),
        ...ComfortLevel.values.where((level) => level != ComfortLevel.notSet).map((level) {
          return _buildComfortLevelOption(level);
        }),
      ],
    );
  }
  
  Widget _buildComfortLevelOption(ComfortLevel level) {
    final isSelected = _comfortLevel == level;
    final displayName = _getComfortLevelDisplayName(level);
    final description = _getComfortLevelDescription(level);
    
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: GestureDetector(
        onTap: () {
          setState(() {
            _comfortLevel = level;
          });
        },
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: isSelected ? MolColors.yellow.withValues(alpha: 0.2) : Colors.grey[800],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: isSelected ? MolColors.yellow : Colors.grey[600]!,
              width: isSelected ? 2 : 1,
            ),
          ),
          child: Row(
            children: [
              Icon(
                isSelected ? Icons.radio_button_checked : Icons.radio_button_unchecked,
                color: isSelected ? MolColors.yellow : Colors.grey[400],
                size: 20,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      displayName,
                      style: TextStyle(
                        color: isSelected ? Colors.white : Colors.grey[300],
                        fontSize: 14,
                        fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                      ),
                    ),
                    Text(
                      description,
                      style: TextStyle(
                        color: Colors.grey[400],
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  Widget _buildCustomDenominationInput() {
    return TextField(
      onChanged: (value) {
        setState(() {
          _customDenomination = value;
        });
      },
      style: const TextStyle(color: Colors.white),
      decoration: InputDecoration(
        labelText: 'Please specify your faith tradition',
        labelStyle: TextStyle(color: Colors.grey[400]),
        enabledBorder: OutlineInputBorder(
          borderSide: BorderSide(color: Colors.grey[600]!),
          borderRadius: BorderRadius.circular(8),
        ),
        focusedBorder: OutlineInputBorder(
          borderSide: BorderSide(color: MolColors.yellow),
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }
  
  Widget _buildButtons() {
    final canSave = _selectedDenomination != null && 
                   (_selectedDenomination != SpiritualDenomination.other || _customDenomination.isNotEmpty);
    
    return Row(
      children: [
        Expanded(
          child: TextButton(
            onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
            child: Text(
              'Skip for now',
              style: TextStyle(color: Colors.grey[400]),
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: ElevatedButton(
            onPressed: _isLoading || !canSave ? null : _saveProfile,
            style: ElevatedButton.styleFrom(
              backgroundColor: MolColors.yellow,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: _isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Text('Save'),
          ),
        ),
      ],
    );
  }
  
  Future<void> _saveProfile() async {
    if (_selectedDenomination == null) return;
    
    setState(() {
      _isLoading = true;
    });
    
    try {
      final profile = SpiritualProfile(
        userId: widget.userId,
        denomination: _selectedDenomination!,
        comfortLevel: _comfortLevel,
        isProfileSet: true,
        lastUpdated: DateTime.now(),
        customDenomination: _selectedDenomination == SpiritualDenomination.other 
            ? _customDenomination 
            : null,
      );
      
      final success = await UserSpiritualProfileService.saveSpiritualProfile(profile);
      
      if (success) {
        widget.onComplete();
        if (mounted) Navigator.of(context).pop();
      } else {
        _showErrorSnackBar('Failed to save spiritual profile. Please try again.');
      }
    } catch (e) {
      _showErrorSnackBar('An error occurred. Please try again.');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
  
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red[700],
      ),
    );
  }
  
  bool _isChristianDenomination() {
    return _selectedDenomination == SpiritualDenomination.christian ||
           _selectedDenomination == SpiritualDenomination.catholic ||
           _selectedDenomination == SpiritualDenomination.orthodox;
  }
  
  String _getDenominationDisplayName(SpiritualDenomination denomination) {
    switch (denomination) {
      case SpiritualDenomination.christian:
        return 'Christian';
      case SpiritualDenomination.catholic:
        return 'Catholic';
      case SpiritualDenomination.orthodox:
        return 'Orthodox';
      case SpiritualDenomination.jewish:
        return 'Jewish';
      case SpiritualDenomination.muslim:
        return 'Muslim';
      case SpiritualDenomination.buddhist:
        return 'Buddhist';
      case SpiritualDenomination.hindu:
        return 'Hindu';
      case SpiritualDenomination.secular:
        return 'Secular / Non-religious';
      case SpiritualDenomination.preferNotToSay:
        return 'Prefer not to say';
      case SpiritualDenomination.other:
        return 'Other';
    }
  }
  
  String _getComfortLevelDisplayName(ComfortLevel level) {
    switch (level) {
      case ComfortLevel.uncomfortable:
        return 'Prefer minimal faith references';
      case ComfortLevel.neutral:
        return 'Occasional faith-based insights are fine';
      case ComfortLevel.comfortable:
        return 'Welcome faith-based guidance';
      case ComfortLevel.veryComfortable:
        return 'Love deep spiritual wisdom and prayer';
      case ComfortLevel.notSet:
        return '';
    }
  }
  
  String _getComfortLevelDescription(ComfortLevel level) {
    switch (level) {
      case ComfortLevel.uncomfortable:
        return 'Keep spiritual content very light and universal';
      case ComfortLevel.neutral:
        return 'Some spiritual wisdom is welcome when relevant';
      case ComfortLevel.comfortable:
        return 'Bible verses and Christian wisdom are appreciated';
      case ComfortLevel.veryComfortable:
        return 'Prayer suggestions and deep spiritual guidance welcome';
      case ComfortLevel.notSet:
        return '';
    }
  }
}
