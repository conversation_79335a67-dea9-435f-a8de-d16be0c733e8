// lib/widgets/training_session_edit_modal.dart

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/training_session_model.dart';

/// Modal for editing training session details
class TrainingSessionEditModal extends StatefulWidget {
  final TrainingSession session;
  final Color glowColor;
  final Function(TrainingSession) onSave;
  final VoidCallback onClose;

  const TrainingSessionEditModal({
    super.key,
    required this.session,
    required this.glowColor,
    required this.onSave,
    required this.onClose,
  });

  @override
  State<TrainingSessionEditModal> createState() => _TrainingSessionEditModalState();
}

class _TrainingSessionEditModalState extends State<TrainingSessionEditModal> {
  late TextEditingController _labelController;
  late TextEditingController _notesController;
  late TextEditingController _goalController;
  late TextEditingController _bodyweightController;
  late TextEditingController _hoursController;
  late TextEditingController _minutesController;
  late TextEditingController _secondsController;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
  }

  void _initializeControllers() {
    _labelController = TextEditingController(text: widget.session.label);
    _notesController = TextEditingController(text: widget.session.notes);
    _goalController = TextEditingController(text: widget.session.currentGoal);
    _bodyweightController = TextEditingController(
      text: widget.session.bodyweightKg?.toStringAsFixed(1) ?? '',
    );

    // Parse duration into hours, minutes, seconds
    final duration = Duration(seconds: widget.session.durationSeconds);
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);

    _hoursController = TextEditingController(text: hours.toString());
    _minutesController = TextEditingController(text: minutes.toString());
    _secondsController = TextEditingController(text: seconds.toString());
  }

  @override
  void dispose() {
    _labelController.dispose();
    _notesController.dispose();
    _goalController.dispose();
    _bodyweightController.dispose();
    _hoursController.dispose();
    _minutesController.dispose();
    _secondsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.black.withValues(alpha: 0.95),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: widget.glowColor.withValues(alpha: 0.3), width: 2),
          boxShadow: [
            BoxShadow(
              color: widget.glowColor.withValues(alpha: 0.2),
              blurRadius: 20,
              spreadRadius: 4,
            ),
          ],
        ),
        child: Column(
          children: [
            _buildHeader(),
            const SizedBox(height: 20),
            Expanded(child: _buildEditForm()),
            const SizedBox(height: 16),
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Icon(
          Icons.edit,
          color: widget.glowColor,
          size: 24,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            'EDIT SESSION',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontFamily: 'Pirulen',
              fontWeight: FontWeight.bold,
              shadows: [
                Shadow(
                  color: widget.glowColor.withValues(alpha: 0.8),
                  offset: const Offset(0, 0),
                  blurRadius: 10,
                ),
              ],
            ),
          ),
        ),
        IconButton(
          onPressed: widget.onClose,
          icon: const Icon(Icons.close, color: Colors.white70),
        ),
      ],
    );
  }

  Widget _buildEditForm() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildTextField(
            'Label',
            _labelController,
            'Session label (e.g., A, Mon, 1)',
            maxLength: 50,
          ),
          const SizedBox(height: 16),
          _buildDurationFields(),
          const SizedBox(height: 16),
          _buildTextField(
            'Goal',
            _goalController,
            'Training goal (optional)',
            maxLength: 100,
          ),
          const SizedBox(height: 16),
          _buildTextField(
            'Bodyweight (kg)',
            _bodyweightController,
            'Enter bodyweight in kg (optional)',
            keyboardType: TextInputType.numberWithOptions(decimal: true),
            maxLength: 10,
          ),
          const SizedBox(height: 16),
          _buildNotesField(),
        ],
      ),
    );
  }

  Widget _buildTextField(
    String label,
    TextEditingController controller,
    String hint, {
    TextInputType? keyboardType,
    int? maxLength,
    int maxLines = 1,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label.toUpperCase(),
          style: TextStyle(
            color: widget.glowColor,
            fontSize: 12,
            fontFamily: 'Pirulen',
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: controller,
          keyboardType: keyboardType,
          maxLength: maxLength,
          maxLines: maxLines,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 14,
            fontFamily: 'Bitsumishi',
          ),
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: const TextStyle(
              color: Colors.white30,
              fontSize: 14,
              fontFamily: 'Bitsumishi',
            ),
            filled: true,
            fillColor: Colors.black.withValues(alpha: 0.4),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: widget.glowColor.withValues(alpha: 0.3)),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: widget.glowColor.withValues(alpha: 0.3)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: widget.glowColor, width: 2),
            ),
            counterStyle: const TextStyle(color: Colors.white30),
          ),
        ),
      ],
    );
  }

  Widget _buildDurationFields() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'DURATION',
          style: TextStyle(
            color: widget.glowColor,
            fontSize: 12,
            fontFamily: 'Pirulen',
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(child: _buildDurationField('Hours', _hoursController)),
            const SizedBox(width: 8),
            Expanded(child: _buildDurationField('Minutes', _minutesController)),
            const SizedBox(width: 8),
            Expanded(child: _buildDurationField('Seconds', _secondsController)),
          ],
        ),
      ],
    );
  }

  Widget _buildDurationField(String label, TextEditingController controller) {
    return Column(
      children: [
        Text(
          label,
          style: const TextStyle(
            color: Colors.white70,
            fontSize: 10,
            fontFamily: 'Digital-7',
          ),
        ),
        const SizedBox(height: 4),
        TextField(
          controller: controller,
          keyboardType: TextInputType.number,
          inputFormatters: [FilteringTextInputFormatter.digitsOnly],
          textAlign: TextAlign.center,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontFamily: 'Digital-7',
            fontWeight: FontWeight.bold,
          ),
          decoration: InputDecoration(
            filled: true,
            fillColor: Colors.black.withValues(alpha: 0.4),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: widget.glowColor.withValues(alpha: 0.3)),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: widget.glowColor.withValues(alpha: 0.3)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: widget.glowColor, width: 2),
            ),
            contentPadding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
          ),
        ),
      ],
    );
  }

  Widget _buildNotesField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'NOTES',
          style: TextStyle(
            color: widget.glowColor,
            fontSize: 12,
            fontFamily: 'Pirulen',
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: _notesController,
          maxLines: 6,
          maxLength: 1500,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 14,
            fontFamily: 'Bitsumishi',
          ),
          decoration: InputDecoration(
            hintText: 'Enter workout details, exercises, sets, reps, etc.',
            hintStyle: const TextStyle(
              color: Colors.white30,
              fontSize: 14,
              fontFamily: 'Bitsumishi',
            ),
            filled: true,
            fillColor: Colors.black.withValues(alpha: 0.4),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: widget.glowColor.withValues(alpha: 0.3)),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: widget.glowColor.withValues(alpha: 0.3)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: widget.glowColor, width: 2),
            ),
            counterStyle: const TextStyle(color: Colors.white30),
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton(
            onPressed: widget.onClose,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.black.withValues(alpha: 0.4),
              foregroundColor: Colors.white70,
              side: BorderSide(color: Colors.white.withValues(alpha: 0.3), width: 1),
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
            child: const Text(
              'CANCEL',
              style: TextStyle(
                fontFamily: 'Pirulen',
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: ElevatedButton(
            onPressed: _saveSession,
            style: ElevatedButton.styleFrom(
              backgroundColor: widget.glowColor.withValues(alpha: 0.2),
              foregroundColor: widget.glowColor,
              side: BorderSide(color: widget.glowColor.withValues(alpha: 0.6), width: 1),
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
            child: const Text(
              'SAVE CHANGES',
              style: TextStyle(
                fontFamily: 'Pirulen',
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ],
    );
  }

  void _saveSession() {
    // Validate inputs
    if (_labelController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Label cannot be empty')),
      );
      return;
    }

    // Parse duration
    final hours = int.tryParse(_hoursController.text) ?? 0;
    final minutes = int.tryParse(_minutesController.text) ?? 0;
    final seconds = int.tryParse(_secondsController.text) ?? 0;
    final totalSeconds = (hours * 3600) + (minutes * 60) + seconds;

    if (totalSeconds <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Duration must be greater than 0')),
      );
      return;
    }

    // Parse bodyweight
    double? bodyweight;
    if (_bodyweightController.text.trim().isNotEmpty) {
      bodyweight = double.tryParse(_bodyweightController.text.trim());
      if (bodyweight == null || bodyweight <= 0) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Invalid bodyweight value')),
        );
        return;
      }
    }

    // Calculate new EXP based on updated duration
    final newExp = TrainingSession.calculateExp(totalSeconds);

    // Create updated session
    final updatedSession = widget.session.copyWith(
      label: _labelController.text.trim(),
      notes: _notesController.text.trim(),
      currentGoal: _goalController.text.trim(),
      durationSeconds: totalSeconds,
      bodyweightKg: bodyweight,
      expEarned: newExp,
    );

    widget.onSave(updatedSession);
    widget.onClose();
  }
}
