// 📁 lib/widgets/ns_quest_button.dart

import 'package:flutter/material.dart';

class NorthStarQuestButton extends StatelessWidget {
  final VoidCallback onPressed;

  const NorthStarQuestButton({super.key, required this.onPressed});

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return Container(
          width: constraints.maxWidth, // Ensure it expands safely within layout bounds
          decoration: BoxDecoration(
            boxShadow: [
              BoxShadow(
                color: Colors.redAccent.withValues(alpha: 0.7),
                blurRadius: 25,
                spreadRadius: 3,
                offset: const Offset(0, 0),
              ),
              BoxShadow(
                color: Colors.redAccent.withValues(alpha: 0.4),
                blurRadius: 40,
                spreadRadius: 20,
                offset: const Offset(0, 0),
              ),
            ],
          ),
          child: ElevatedButton.icon(
            icon: const Icon(Icons.auto_awesome, color: Colors.black),
            label: const Text(
              "North Star Quest",
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.black,
                letterSpacing: 1.0,
              ),
            ),
            onPressed: onPressed,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.redAccent,
              foregroundColor: Colors.black,
              padding: const EdgeInsets.symmetric(vertical: 14, horizontal: 22),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              elevation: 0,
            ),
          ),
        );
      },
    );
  }
}
