// 📁 lib/widgets/ns_progress_meter.dart

import 'package:flutter/material.dart';
import 'dart:math';

class NsProgressMeter extends StatelessWidget {
  final double hoursLogged;
  final double targetHours;

  const NsProgressMeter({
    super.key,
    required this.hoursLogged,
    this.targetHours = 100.0,
  });

  @override
  Widget build(BuildContext context) {
    final percent = (hoursLogged / targetHours).clamp(0.0, 1.0);

    return Center(
      child: LayoutBuilder(
        builder: (context, constraints) {
          final size = min(constraints.maxWidth, 140.0);

          return SizedBox(
            width: size,
            height: size,
            child: Stack(
              alignment: Alignment.center,
              children: [
                // Base Ring
                CustomPaint(
                  size: Size(size, size),
                  painter: _GlowRingPainter(
                    percent: percent,
                    glowColor: Colors.cyanAccent,
                  ),
                ),

                // Center Data
                Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      "${(percent * 100).toStringAsFixed(0)}%",
                      style: const TextStyle(
                        fontSize: 24,
                        color: Colors.cyanAccent,
                        fontWeight: FontWeight.bold,
                        fontFamily: 'Orbitron',
                        shadows: [
                          Shadow(
                            blurRadius: 10,
                            color: Colors.cyanAccent,
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      "${hoursLogged.toStringAsFixed(1)} / ${targetHours.toStringAsFixed(0)} hrs",
                      style: const TextStyle(
                        fontSize: 12,
                        color: Colors.white54,
                        fontFamily: 'RobotoMono',
                      ),
                    ),
                  ],
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}

class _GlowRingPainter extends CustomPainter {
  final double percent;
  final Color glowColor;

  _GlowRingPainter({required this.percent, required this.glowColor});

  @override
  void paint(Canvas canvas, Size size) {
    final strokeWidth = 10.0;
    final center = Offset(size.width / 2, size.height / 2);
    final radius = (size.width - strokeWidth) / 2;

    final trackPaint = Paint()
      ..color = Colors.white10
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth;

    canvas.drawCircle(center, radius, trackPaint);

    final glowPaint = Paint()
      ..shader = SweepGradient(
        colors: [
          glowColor.withValues(alpha: 0.1),
          glowColor,
          glowColor.withValues(alpha: 0.1),
        ],
        stops: [0.0, 0.5, 1.0],
        startAngle: 0.0,
        endAngle: 2 * pi,
      ).createShader(Rect.fromCircle(center: center, radius: radius))
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round
      ..strokeWidth = strokeWidth;

    final sweepAngle = 2 * pi * percent;
    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      -pi / 2,
      sweepAngle,
      false,
      glowPaint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
