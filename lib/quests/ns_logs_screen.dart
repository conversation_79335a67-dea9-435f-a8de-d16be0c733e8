import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../controller/user_controller2.dart';
//import 'package:maxed_out_life/utils/date_formatter.dart';
import 'ns_tracker.dart';  // adjust relative path if needed

class NsLogsScreen extends StatelessWidget {
  const NsLogsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final user = Provider.of<UserController2>(context).user;
    if (user == null || user.northStarQuest == null) {
      return const Scaffold(
        backgroundColor: Colors.black,
        body: Center(
          child: Text(
            'No North-Star quest found.',
            style: TextStyle(
              color: Colors.white54,
              fontFamily: 'Bitsumishi',
            ),
          ),
        ),
      );
    }

    final quest = user.northStarQuest!;
    final glow = quest.glowColor;

    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        title: Text(
          'North-Star Logs',
          style: TextStyle(
            color: glow,
            fontFamily: '<PERSON>su<PERSON>hi',
          ),
        ),
        backgroundColor: Colors.grey[900],
      ),
      body: const NsTracker(fullScreen: true),
    );
  }
}
