// 📁 lib/quests/ns_dashboard.dart

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:audioplayers/audioplayers.dart';

import '../models/user_model.dart';
import '../controller/user_controller2.dart';
import '../quests/north_star_model.dart';
import '../utils/rank_utils.dart';
import '../quests/ns_progress_meter.dart';
import '../quests/ns_exp_tracker.dart';
import '../quests/ns_glow_card_v2.dart';
import '../quests/ns_timer.dart';
import '../onboarding/north_star_setup_04.dart';
import '../home/<USER>'; // For StrokeText
import '../quests/ns_tracker.dart';
import '../services/super_entry_service.dart';

class NsDashboard extends StatefulWidget {
  final NorthStarQuest quest;
  final Function(User) onQuestUpdate;

  const NsDashboard({
    super.key,
    required this.quest,
    required this.onQuestUpdate,
  });

  @override
  State<NsDashboard> createState() => _NsDashboardState();
}

class _NsDashboardState extends State<NsDashboard> with TickerProviderStateMixin {
  late UserController2 _userController;
  AnimationController? _glowController;
  Animation<double>? _glowAnimation;

  @override
  void initState() {
    super.initState();
    _userController = Provider.of<UserController2>(context, listen: false);

    // Initialize glow animation
    _glowController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat(reverse: true);

    _glowAnimation = Tween<double>(begin: 0.3, end: 1.0).animate(
      CurvedAnimation(parent: _glowController!, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _glowController?.dispose();
    super.dispose();
  }

  Future<void> _showNsJournalModal() async {
    final quest = _userController.user?.northStarQuest;
    if (quest == null) return;
    final themeColor = quest.glowColor;

    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        backgroundColor: Colors.black.withValues(alpha: 0.9),
        child: SingleChildScrollView(
          child: Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              border: Border.all(color: themeColor, width: 2),
              boxShadow: [
                BoxShadow(
                  color: themeColor.withValues(alpha: 0.6),
                  blurRadius: 12,
                  spreadRadius: 4,
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Title with white outline for better readability
                StrokeText(
                  text: 'LOG NORTH STAR\nPROGRESS',
                  textAlign: TextAlign.center,
                  textStyle: TextStyle(
                    fontFamily: 'Pirulen',
                    fontSize: 20,
                    color: themeColor,
                    height: 1.2,
                  ),
                  strokeWidth: 1.5,
                  strokeColor: Colors.white,
                ),
                const SizedBox(height: 20),

                // Journal form
                _NsJournalModalContent(
                  onCompleted: () async {
                    Navigator.of(context).pop();
                    setState(() {}); // Refresh the dashboard

                    // Trigger home screen refresh through user controller
                    final userController = context.read<UserController2>();
                    await userController.refreshFromDisk();
                    final updatedUser = userController.user;
                    if (updatedUser != null) {
                      widget.onQuestUpdate(updatedUser);
                    }
                  },
                  themeColor: themeColor,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }



  Future<void> _showNsLogModal() async {
    final quest = _userController.user?.northStarQuest;
    if (quest == null) return;
    final themeColor = quest.glowColor;

    try {
      await AudioPlayer().play(AssetSource('audio/lightning.mp3'));
    } catch (_) {
      // Ignore audio errors
    }

    if (!mounted) return;

    await showDialog<void>(
      context: context,
      barrierDismissible: false, // Prevent accidental dismissal
      builder: (context) => PopScope(
        canPop: false, // Prevent back button
        child: Dialog(
          backgroundColor: Colors.transparent,
          child: Container(
            decoration: BoxDecoration(
              color: Colors.grey[900],
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: themeColor.withValues(alpha: 0.6),
                width: 2.0,
              ),
              boxShadow: [
                BoxShadow(
                  color: themeColor.withValues(alpha: 0.4),
                  blurRadius: 12,
                  spreadRadius: 3,
                ),
                BoxShadow(
                  color: themeColor.withValues(alpha: 0.2),
                  blurRadius: 24,
                  spreadRadius: 6,
                ),
              ],
            ),
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const SizedBox(height: 6),

                  // Success message
                  Text(
                    "You Are Working Towards Your Dream.\nDo Not Stop.",
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontFamily: 'Pirulen',
                      fontSize: 16,
                      color: themeColor,
                      height: 1.1,
                    ),
                  ),
                  const Divider(color: Colors.white24),
                  const SizedBox(height: 4),

                  // Log tracker
                  const SizedBox(
                    height: 300,
                    child: NsTracker(),
                  ),
                  const SizedBox(height: 6),

                  // Close button
                  TextButton(
                    onPressed: () {
                      Navigator.of(context).pop(); // Close modal
                    },
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                    ),
                    child: const Text(
                      'CLOSE',
                      style: TextStyle(
                        color: Colors.white,
                        fontFamily: 'Bitsumishi',
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final user = _userController.user;
    final quest = _userController.user?.northStarQuest;
    final customCategories = user?.customCategories ?? [];

    // If no quest exists, show setup screen
    if (quest == null) {
      if (user == null) return const SizedBox.shrink();
      return NorthStarSetup(
        user: user,
        onContinue: (updatedUser) {
          _userController.updateUser(updatedUser);
          _userController.save();
          setState(() {});
          widget.onQuestUpdate(updatedUser);
        },
      );
    }

    // If quest exists but no custom categories, show a message to complete onboarding
    if (customCategories.isEmpty) {
      return Container(
        width: double.infinity,
        constraints: const BoxConstraints(maxHeight: 700),
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.grey[900],
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.category,
              color: Colors.cyanAccent,
              size: 64,
            ),
            const SizedBox(height: 16),
            const Text(
              'Complete Onboarding',
              style: TextStyle(
                color: Colors.white,
                fontFamily: 'Pirulen',
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'Please complete the onboarding process to set up your categories',
              style: TextStyle(
                color: Colors.white70,
                fontFamily: 'Bitsumishi',
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    final themeColor = quest.glowColor;
    final logs = quest.logs.reversed.toList();
    final exp = quest.totalExp;
    final rankName = RankUtils.getRankName(exp);
    final expToNext = RankUtils.getExpToNextRank(exp);

    // If animation is not ready, show static version
    if (_glowController == null || _glowAnimation == null) {
      return Container(
        width: double.infinity,
        constraints: const BoxConstraints(maxHeight: 800),
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.grey[900],
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: themeColor.withValues(alpha: 0.8),
            width: 2,
          ),
          boxShadow: [
            BoxShadow(
              color: themeColor.withValues(alpha: 0.3),
              blurRadius: 20,
              spreadRadius: 3,
            ),
          ],
        ),
        child: SizedBox(
        height: 750, // Increased height to accommodate timer and logs buttons
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              NsGlowCardV2(
                title: quest.title,
                summary: quest.summary,
                color: themeColor,
              ),
              const SizedBox(height: 24),
              Text(
                'Rank: $rankName',
                style: const TextStyle(
                  color: Colors.yellow,
                  fontSize: 16,
                  fontFamily: 'Pirulen',
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                '$exp / ${exp + expToNext} EXP to next rank',
                style: TextStyle(
                  color: themeColor,
                  fontSize: 12,
                  fontFamily: 'Digital-7',
                ),
              ),
              const SizedBox(height: 16),
              NsExpTracker(quest: quest, onQuestUpdate: widget.onQuestUpdate),
              const SizedBox(height: 24),
              NsProgressMeter(hoursLogged: quest.hoursLogged, targetHours: 100.0),
              const SizedBox(height: 30),
              ElevatedButton.icon(
                onPressed: _showNsJournalModal,
                icon: const Icon(Icons.add),
                label: Stack(
                  alignment: Alignment.center,
                  children: [
                    // Black border
                    Text(
                      "Log Progress",
                      style: TextStyle(
                        fontFamily: 'Bitsumishi',
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        foreground: Paint()
                          ..style = PaintingStyle.stroke
                          ..strokeWidth = 1.0
                          ..color = Colors.black,
                      ),
                    ),
                    // White text
                    const Text(
                      "Log Progress",
                      style: TextStyle(
                        fontFamily: 'Bitsumishi',
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: themeColor,
                  foregroundColor: Colors.black,
                  padding:
                      const EdgeInsets.symmetric(vertical: 12, horizontal: 24),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 8,
                  shadowColor: themeColor.withValues(alpha: 0.5),
                ),
              ),
              const SizedBox(height: 12),
              ElevatedButton.icon(
                onPressed: () => showNsTimerPopup(context, themeColor),
                icon: const Icon(Icons.timer),
                label: Stack(
                  alignment: Alignment.center,
                  children: [
                    // Black border
                    Text(
                      "Start 30-Minute Timer",
                      style: TextStyle(
                        fontFamily: 'Bitsumishi',
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        foreground: Paint()
                          ..style = PaintingStyle.stroke
                          ..strokeWidth = 1.0
                          ..color = Colors.black,
                      ),
                    ),
                    // White text
                    const Text(
                      "Start 30-Minute Timer",
                      style: TextStyle(
                        fontFamily: 'Bitsumishi',
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: themeColor,
                  foregroundColor: Colors.black,
                  padding:
                      const EdgeInsets.symmetric(vertical: 12, horizontal: 24),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
              const SizedBox(height: 30),
              if (logs.isNotEmpty) ...[
                ElevatedButton(
                  onPressed: _showNsLogModal,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: themeColor,
                    foregroundColor: Colors.black,
                    minimumSize: const Size(double.infinity, 48),
                    padding:
                        const EdgeInsets.symmetric(vertical: 16, horizontal: 32),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                    ),
                  ),
                  child: Stack(
                    alignment: Alignment.center,
                    children: [
                      // Black border
                      Text(
                        'VIEW NORTH STAR ENTRY LOG',
                        style: TextStyle(
                          fontFamily: 'Pirulen',
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          foreground: Paint()
                            ..style = PaintingStyle.stroke
                            ..strokeWidth = 1.0
                            ..color = Colors.black,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      // White text
                      const Text(
                        'VIEW NORTH STAR ENTRY LOG',
                        style: TextStyle(
                          fontFamily: 'Pirulen',
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 24),
              ],
            ],
          ),
        ),
      ),
    );
    }

    // Animated version with glow effect
    return AnimatedBuilder(
      animation: _glowController!,
      builder: (context, child) {
        final animValue = _glowAnimation!.value;
        return Container(
          width: double.infinity,
          constraints: const BoxConstraints(maxHeight: 800),
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.grey[900],
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: themeColor.withValues(alpha: 0.6 + 0.4 * animValue),
              width: 2 + 1 * animValue,
            ),
            boxShadow: [
              // Main outer glow
              BoxShadow(
                color: themeColor.withValues(alpha: 0.3 * animValue),
                blurRadius: 20 + 10 * animValue,
                spreadRadius: 3 + 2 * animValue,
              ),
              // Inner glow
              BoxShadow(
                color: themeColor.withValues(alpha: 0.2 * animValue),
                blurRadius: 10 + 5 * animValue,
                spreadRadius: 1 + 1 * animValue,
              ),
              // Subtle background glow
              BoxShadow(
                color: themeColor.withValues(alpha: 0.1 * animValue),
                blurRadius: 30 + 15 * animValue,
                spreadRadius: 5 + 3 * animValue,
              ),
            ],
          ),
          child: SizedBox(
            height: 750, // Increased height to accommodate timer and logs buttons
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  NsGlowCardV2(
                    title: quest.title,
                    summary: quest.summary,
                    color: themeColor,
                  ),
                  const SizedBox(height: 24),
                  Text(
                    'Rank: $rankName',
                    style: const TextStyle(
                      color: Colors.yellow,
                      fontSize: 16,
                      fontFamily: 'Pirulen',
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '$exp / ${exp + expToNext} EXP to next rank',
                    style: TextStyle(
                      color: themeColor,
                      fontSize: 12,
                      fontFamily: 'Digital-7',
                    ),
                  ),
                  const SizedBox(height: 16),
                  NsExpTracker(quest: quest, onQuestUpdate: widget.onQuestUpdate),
                  const SizedBox(height: 24),
                  NsProgressMeter(hoursLogged: quest.hoursLogged, targetHours: 100.0),
                  const SizedBox(height: 30),
                  ElevatedButton.icon(
                    onPressed: _showNsJournalModal,
                    icon: const Icon(Icons.add),
                    label: Stack(
                      alignment: Alignment.center,
                      children: [
                        // Black border
                        Text(
                          "Log Progress",
                          style: TextStyle(
                            fontFamily: 'Bitsumishi',
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            foreground: Paint()
                              ..style = PaintingStyle.stroke
                              ..strokeWidth = 1.0
                              ..color = Colors.black,
                          ),
                        ),
                        // White text
                        const Text(
                          "Log Progress",
                          style: TextStyle(
                            fontFamily: 'Bitsumishi',
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: themeColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                  const SizedBox(height: 12),
                  ElevatedButton.icon(
                    onPressed: () => showNsTimerPopup(context, themeColor),
                    icon: const Icon(Icons.timer),
                    label: Stack(
                      alignment: Alignment.center,
                      children: [
                        // Black border
                        Text(
                          "Start 30-Minute Timer",
                          style: TextStyle(
                            fontFamily: 'Bitsumishi',
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            foreground: Paint()
                              ..style = PaintingStyle.stroke
                              ..strokeWidth = 1.0
                              ..color = Colors.black,
                          ),
                        ),
                        // White text
                        const Text(
                          "Start 30-Minute Timer",
                          style: TextStyle(
                            fontFamily: 'Bitsumishi',
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: themeColor,
                      foregroundColor: Colors.black,
                      padding:
                          const EdgeInsets.symmetric(vertical: 12, horizontal: 24),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  ),
                  const SizedBox(height: 30),
                  if (logs.isNotEmpty) ...[
                    ElevatedButton(
                      onPressed: _showNsLogModal,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: themeColor,
                        foregroundColor: Colors.black,
                        minimumSize: const Size(double.infinity, 48),
                        padding:
                            const EdgeInsets.symmetric(vertical: 16, horizontal: 32),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16),
                        ),
                      ),
                      child: Stack(
                        alignment: Alignment.center,
                        children: [
                          // Black border
                          Text(
                            'VIEW NORTH STAR ENTRY LOG',
                            style: TextStyle(
                              fontFamily: 'Pirulen',
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              foreground: Paint()
                                ..style = PaintingStyle.stroke
                                ..strokeWidth = 1.0
                                ..color = Colors.black,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          // White text
                          const Text(
                            'VIEW NORTH STAR ENTRY LOG',
                            style: TextStyle(
                              fontFamily: 'Pirulen',
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 24),
                  ],
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

/// Modal-specific content for North Star journal entry
class _NsJournalModalContent extends StatefulWidget {
  final VoidCallback? onCompleted;
  final Color themeColor;

  const _NsJournalModalContent({
    this.onCompleted,
    required this.themeColor,
  });

  @override
  State<_NsJournalModalContent> createState() => _NsJournalModalContentState();
}

class _NsJournalModalContentState extends State<_NsJournalModalContent> {
  final _formKey = GlobalKey<FormState>();
  final _entryController = TextEditingController();
  final _hoursController = TextEditingController(text: '0.5');
  String _selectedCategory = 'Health';
  bool _isLoading = false;
  late UserController2 _userController;

  @override
  void initState() {
    super.initState();
    _userController = Provider.of<UserController2>(context, listen: false);
  }

  @override
  void dispose() {
    _entryController.dispose();
    _hoursController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final user = _userController.user;
    if (user == null || user.northStarQuest == null) {
      return const Text(
        'No North-Star quest found.',
        style: TextStyle(
          color: Colors.white54,
          fontFamily: 'Bitsumishi',
        ),
      );
    }

    // Build category list with user's custom categories
    final custom1 = user.customCategories.isNotEmpty
        ? user.customCategories[0]
        : 'Custom 1';
    final custom2 = user.customCategories.length > 1
        ? user.customCategories[1]
        : 'Custom 2';

    final categories = [
      'Health',
      'Wealth',
      'Purpose',
      'Connection',
      custom1,
      custom2,
    ];

    return Form(
      key: _formKey,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Category Dropdown
          DropdownButtonFormField<String>(
            value: _selectedCategory,
            items: categories.map((cat) => DropdownMenuItem(
              value: cat,
              child: Text(
                cat,
                style: const TextStyle(
                  fontFamily: 'Digital-7',
                ),
              ),
            )).toList(),
            onChanged: (val) {
              setState(() {
                _selectedCategory = val!;
              });
            },
            decoration: InputDecoration(
              label: Stack(
                children: [
                  // White outline for Category label
                  Text(
                    'Category',
                    style: TextStyle(
                      fontFamily: 'Bitsumishi',
                      fontSize: 19, // Increased by 20% from 16 to 19
                      foreground: Paint()
                        ..style = PaintingStyle.stroke
                        ..strokeWidth = 1.0
                        ..color = Colors.white,
                    ),
                  ),
                  // Main Category label
                  Text(
                    'Category',
                    style: TextStyle(
                      color: widget.themeColor,
                      fontFamily: 'Bitsumishi',
                      fontSize: 19, // Increased by 20% from 16 to 19
                    ),
                  ),
                ],
              ),
              enabledBorder: OutlineInputBorder(
                borderSide: BorderSide(color: Colors.grey[800]!),
                borderRadius: BorderRadius.circular(12),
              ),
              focusedBorder: OutlineInputBorder(
                borderSide: BorderSide(color: widget.themeColor),
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            dropdownColor: Colors.grey[900],
            style: const TextStyle(
              color: Colors.white,
              fontFamily: 'Bitsumishi',
            ),
          ),
          const SizedBox(height: 16),

          // Entry Text
          TextField(
            controller: _entryController,
            maxLines: 3,
            style: const TextStyle(
              color: Colors.white,
              fontFamily: 'Bitsumishi',
            ),
            decoration: InputDecoration(
              label: Stack(
                children: [
                  // White outline for Entry label
                  Text(
                    'Entry',
                    style: TextStyle(
                      fontFamily: 'Bitsumishi',
                      fontSize: 19, // Increased by 20% from 16 to 19
                      foreground: Paint()
                        ..style = PaintingStyle.stroke
                        ..strokeWidth = 1.0
                        ..color = Colors.white,
                    ),
                  ),
                  // Main Entry label
                  Text(
                    'Entry',
                    style: TextStyle(
                      color: widget.themeColor,
                      fontFamily: 'Bitsumishi',
                      fontSize: 19, // Increased by 20% from 16 to 19
                    ),
                  ),
                ],
              ),
              enabledBorder: OutlineInputBorder(
                borderSide: BorderSide(color: Colors.grey[800]!),
                borderRadius: BorderRadius.circular(12),
              ),
              focusedBorder: OutlineInputBorder(
                borderSide: BorderSide(color: widget.themeColor),
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Hours Slider
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Hours: ${_hoursController.text}',
                style: const TextStyle(
                  fontFamily: 'Digital-7',
                  color: Colors.white,
                  fontSize: 18,
                ),
              ),
            ],
          ),
          Slider(
            value: double.parse(_hoursController.text),
            min: 0.5,
            max: 4.0,
            divisions: 7,
            label: '${double.parse(_hoursController.text).toStringAsFixed(1)}h',
            activeColor: widget.themeColor,
            inactiveColor: Colors.white30,
            onChanged: (val) {
              setState(() {
                _hoursController.text = val.toString();
              });
            },
          ),
          const SizedBox(height: 16),

          // Buttons Row
          Row(
            children: [
              // Cancel Button
              Expanded(
                child: OutlinedButton(
                  style: OutlinedButton.styleFrom(
                    side: const BorderSide(color: Colors.white), // White border instead of red
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
                  child: Stack(
                    alignment: Alignment.center,
                    children: [
                      // White outline
                      Text(
                        'Cancel',
                        style: TextStyle(
                          fontFamily: 'Bitsumishi',
                          fontSize: 18,
                          foreground: Paint()
                            ..style = PaintingStyle.stroke
                            ..strokeWidth = 1.0
                            ..color = Colors.white,
                        ),
                      ),
                      // Main text
                      Text(
                        'Cancel',
                        style: TextStyle(
                          color: widget.themeColor,
                          fontFamily: 'Bitsumishi',
                          fontSize: 18,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(width: 16),
              // Submit Button - Fixed to prevent text wrapping
              Expanded(
                child: ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: widget.themeColor,
                    padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 8), // Added horizontal padding for spacing
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  onPressed: (_entryController.text.trim().isEmpty || _isLoading)
                      ? null
                      : _handleSubmit,
                  child: _isLoading
                      ? const SizedBox(
                          width: 24,
                          height: 24,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.black),
                          ),
                        )
                      : FittedBox(
                          fit: BoxFit.scaleDown,
                          child: StrokeText(
                            text: 'Submit Entry',
                            textStyle: const TextStyle(
                              color: Colors.white,
                              fontFamily: 'Bitsumishi',
                              fontSize: 16, // Reduced from 18 to ensure single line
                            ),
                            strokeWidth: 1.0,
                            strokeColor: Colors.black,
                          ),
                        ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Future<void> _handleSubmit() async {
    setState(() => _isLoading = true);
    final userCtrl = _userController;
    final user = userCtrl.user;
    if (user == null) return;

    final String category = _selectedCategory;
    final String rawNote = _entryController.text.trim();
    final double hours = double.parse(_hoursController.text);

    try {
      // Create new log
      await SuperEntryService().logNorthStar(
        userController: userCtrl,
        hours: hours,
        category: category,
        note: rawNote,
      );

      // Play lightning sound
      try {
        await AudioPlayer().play(AssetSource('audio/lightning.mp3'));
      } catch (_) {
        // Ignore audio errors
      }

      if (mounted) {
        // Show success modal
        await _showSuccessModal();

        // Close the current modal and refresh
        widget.onCompleted?.call();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Error: $e',
              style: const TextStyle(
                fontFamily: 'Bitsumishi',
              ),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  /// Show success modal with lightning effect
  Future<void> _showSuccessModal() async {
    await showDialog<void>(
      context: context,
      barrierDismissible: true,
      builder: (context) => Dialog(
        backgroundColor: Colors.grey[900],
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
          side: BorderSide(
            color: widget.themeColor.withValues(alpha: 0.4),
            width: 1.5,
          ),
        ),
        child: Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: widget.themeColor.withValues(alpha: 0.3),
                blurRadius: 8,
                spreadRadius: 2,
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Success - North Star Progress Logged',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontFamily: 'Pirulen',
                  fontSize: 18,
                  color: widget.themeColor,
                ),
              ),
              const SizedBox(height: 20),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(),
                style: ElevatedButton.styleFrom(
                  backgroundColor: widget.themeColor,
                  padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 24),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: const Text(
                  'OK',
                  style: TextStyle(
                    color: Colors.black,
                    fontFamily: 'Bitsumishi',
                    fontSize: 16,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
