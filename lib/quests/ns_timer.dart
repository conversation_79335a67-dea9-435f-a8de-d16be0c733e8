// 📁 lib/quests/ns_timer.dart

import 'dart:async';
import 'package:flutter/material.dart';
import 'dart:math';
import 'package:audioplayers/audioplayers.dart';

/// Call this method to display the NS Timer Popup
Future<Object?> showNsTimerPopup(BuildContext context, Color glowColor) async {
  return showGeneralDialog(
    context: context,
    barrierDismissible: false,
    barrierLabel: "Timer",
    pageBuilder: (_, __, ___) => const SizedBox.shrink(),
    transitionBuilder: (context, animation, secondaryAnimation, child) {
      return FadeTransition(
        opacity: animation,
        child: NSCountdownTimer(glowColor: glowColor),
      );
    },
  );
}

/// Widget for a 30‐Minute Countdown Timer (real time)
class NSCountdownTimer extends StatefulWidget {
  final Color glowColor;
  const NSCountdownTimer({super.key, required this.glowColor});

  @override
  State<NSCountdownTimer> createState() => _NSCountdownTimerState();
}

class _NSCountdownTimerState extends State<NSCountdownTimer> {
  static const int totalSeconds = 30 * 60; // 30 minutes in seconds
  late int _remainingSeconds;
  Timer? _timer;

  @override
  void initState() {
    super.initState();
    _remainingSeconds = totalSeconds;
    _startTimer();
  }

  void _startTimer() {
    // Fire once immediately to show "30:00" before the first tick
    _tick();
    // Then schedule one‐second intervals
    _timer = Timer.periodic(const Duration(seconds: 1), (_) => _tick());
  }

  void _tick() {
    if (!mounted) return;

    setState(() {
      _remainingSeconds = max(0, _remainingSeconds - 1);
    });

    if (_remainingSeconds == 0) {
      _timer?.cancel();
      _playFinishSound();
      Navigator.of(context).pop(); // Dismiss the dialog when time is up
    }
  }

  Future<void> _playFinishSound() async {
    final player = AudioPlayer();
    // Play the lightning.mp3 from assets/sounds/lightning.mp3
    await player.play(AssetSource('assets/sounds/lightning.mp3'));
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  String get timeLeft {
    final int minutes = _remainingSeconds ~/ 60;
    final int seconds = _remainingSeconds % 60;
    final String minStr = minutes.toString().padLeft(2, '0');
    final String secStr = seconds.toString().padLeft(2, '0');
    return '$minStr:$secStr';
  }

  @override
  Widget build(BuildContext context) {
    final glow = widget.glowColor;
    // Calculate progress for the ring painter (1.0 → full circle, 0.0 → empty)
    final double progress =
        _remainingSeconds / totalSeconds; // ranges from 1.0 down to 0.0

    return Scaffold(
      backgroundColor: Colors.black.withValues(alpha: 0.92),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              height: 250,
              width: 250,
              child: CustomPaint(
                painter: _RingPainter(progress: progress, glowColor: glow),
                child: Center(
                  child: Text(
                    timeLeft,
                    style: TextStyle(
                      fontFamily: 'Digital-7',
                      fontSize: 42,
                      color: glow,
                      shadows: [
                        Shadow(
                          color: glow.withValues(alpha: 0.8),
                          blurRadius: 12,
                          offset: const Offset(0, 0),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
            const SizedBox(height: 30),
            ElevatedButton(
              onPressed: () {
                _timer?.cancel();
                Navigator.of(context).pop();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.black,
                padding:
                    const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(24),
                ),
                elevation: 0,
                shadowColor: glow,
              ),
              child: Text(
                'Cancel',
                style: TextStyle(
                  color: glow,
                  fontSize: 20,
                  fontFamily: 'Bitsumishi',
                  shadows: [
                    Shadow(
                      blurRadius: 12,
                      color: glow,
                      offset: const Offset(0, 0),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _RingPainter extends CustomPainter {
  final double progress;
  final Color glowColor;

  _RingPainter({required this.progress, required this.glowColor});

  @override
  void paint(Canvas canvas, Size size) {
    final double strokeWidth = 12.0;
    final Paint backgroundPaint = Paint()
      ..color = Colors.grey[900]!
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke;
    final Paint progressPaint = Paint()
      ..color = glowColor
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 8);

    final Offset center = Offset(size.width / 2, size.height / 2);
    final double radius = (size.width - strokeWidth) / 2;

    // Draw the full background circle first
    canvas.drawCircle(center, radius, backgroundPaint);

    // Draw the progress arc (clockwise, starting from top)
    final double sweepAngle = 2 * pi * progress;
    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      -pi / 2,
      sweepAngle,
      false,
      progressPaint,
    );
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => true;
}
