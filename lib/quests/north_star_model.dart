import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';
import '../utils/rank_utils.dart';

class NorthStarQuest {
  final String id;
  final String title;
  final String summary;
  final Color glowColor;
  final List<String> coreValues;
  final DateTime createdAt;
  final List<NorthStarLog> logs;
  final int totalExp;
  final double hoursLogged;
  final List<String> milestones;
  final String? visionImagePath;
  final String icon;
  final String category;

  const NorthStarQuest({
    required this.id,
    required this.title,
    required this.summary,
    required this.glowColor,
    required this.coreValues,
    required this.createdAt,
    required this.logs,
    required this.totalExp,
    required this.hoursLogged,
    required this.milestones,
    required this.visionImagePath,
    required this.icon,
    required this.category,
  });

  factory NorthStarQuest.empty() {
    return NorthStarQuest(
      id: const Uuid().v4(),
      title: 'No Active Quest',
      summary: 'Create a new quest to begin your journey.',
      glowColor: Colors.grey,
      icon: '🌟',
      coreValues: [],
      category: 'Purpose',
      createdAt: DateTime.now(),
      logs: [],
      totalExp: 0,
      hoursLogged: 0,
      milestones: [],
      visionImagePath: null,
    );
  }

  factory NorthStarQuest.create({
    required String title,
    required String summary,
    required Color glowColor,
    required String icon,
    required List<String> coreValues,
    required String category,
    String? visionImagePath,
  }) {
    return NorthStarQuest(
      id: const Uuid().v4(),
      title: title,
      summary: summary,
      glowColor: glowColor,
      icon: icon,
      coreValues: coreValues,
      category: category,
      createdAt: DateTime.now(),
      logs: [],
      totalExp: 0,
      hoursLogged: 0,
      milestones: [],
      visionImagePath: visionImagePath,
    );
  }

  NorthStarQuest copyWith({
    String? id,
    String? title,
    String? summary,
    Color? glowColor,
    List<String>? coreValues,
    DateTime? createdAt,
    List<NorthStarLog>? logs,
    int? totalExp,
    double? hoursLogged,
    List<String>? milestones,
    String? visionImagePath,
    String? icon,
    String? category,
  }) {
    return NorthStarQuest(
      id: id ?? this.id,
      title: title ?? this.title,
      summary: summary ?? this.summary,
      glowColor: glowColor ?? this.glowColor,
      coreValues: coreValues ?? this.coreValues,
      createdAt: createdAt ?? this.createdAt,
      logs: logs ?? this.logs,
      totalExp: totalExp ?? this.totalExp,
      hoursLogged: hoursLogged ?? this.hoursLogged,
      milestones: milestones ?? this.milestones,
      visionImagePath: visionImagePath ?? this.visionImagePath,
      icon: icon ?? this.icon,
      category: category ?? this.category,
    );
  }

  String get currentRank => RankUtils.getRankName(totalExp);

  Map<String, dynamic> toJson() => {
    'id': id,
    'title': title,
    'summary': summary,
    // ignore: deprecated_member_use
    'glowColor': glowColor.value, // Using .value for backward compatibility with existing data
    'coreValues': coreValues,
    'createdAt': createdAt.toIso8601String(),
    'logs': logs.map((l) => l.toJson()).toList(),
    'totalExp': totalExp,
    'hoursLogged': hoursLogged,
    'milestones': milestones,
    'visionImagePath': visionImagePath,
    'icon': icon,
    'category': category,
  };

  factory NorthStarQuest.fromJson(Map<String, dynamic> json) {
    return NorthStarQuest(
      id: json['id'] as String,
      title: json['title'] as String,
      summary: json['summary'] as String,
      glowColor: Color(json['glowColor'] as int),
      coreValues: List<String>.from(json['coreValues'] as List),
      createdAt: DateTime.parse(json['createdAt'] as String),
      logs: (json['logs'] as List)
          .map((l) => NorthStarLog.fromJson(l as Map<String, dynamic>))
          .toList(),
      totalExp: json['totalExp'] as int,
      hoursLogged: json['hoursLogged'] as double,
      milestones: List<String>.from(json['milestones'] as List),
      visionImagePath: json['visionImagePath'] as String?,
      icon: json['icon'] as String,
      category: json['category'] as String,
    );
  }
}

class NorthStarLog {
  final String id;
  final String entryText;
  final double hours;
  final String title;
  final DateTime loggedAt;

  const NorthStarLog({
    required this.id,
    required this.entryText,
    required this.hours,
    required this.title,
    required this.loggedAt,
  });

  DateTime get timestamp => loggedAt;

  String get category {
    final match = RegExp(r'\[N\.S\. - (.*?)\]').firstMatch(entryText);
    return match != null ? match.group(1)! : 'Unknown';
  }

  NorthStarLog copyWith({
    String? id,
    String? entryText,
    double? hours,
    String? title,
    DateTime? loggedAt,
  }) {
    return NorthStarLog(
      id: id ?? this.id,
      entryText: entryText ?? this.entryText,
      hours: hours ?? this.hours,
      title: title ?? this.title,
      loggedAt: loggedAt ?? this.loggedAt,
    );
  }

  Map<String, dynamic> toJson() => {
    'id': id,
    'entryText': entryText,
    'hours': hours,
    'title': title,
    'loggedAt': loggedAt.toIso8601String(),
  };

  factory NorthStarLog.fromJson(Map<String, dynamic> json) {
    return NorthStarLog(
      id: json['id'] as String,
      entryText: json['entryText'] as String,
      hours: json['hours'] as double,
      title: json['title'] as String,
      loggedAt: DateTime.parse(json['loggedAt'] as String),
    );
  }
}
