import 'package:flutter/material.dart';

class NsGlowCardV2 extends StatefulWidget {
  final String title;
  final String summary;
  final Color color;
  final VoidCallback? onTap;

  const NsGlowCardV2({
    super.key,
    required this.title,
    required this.summary,
    required this.color,
    this.onTap,
  });

  @override
  State<NsGlowCardV2> createState() => _NsGlowCardV2State();
}

class _NsGlowCardV2State extends State<NsGlowCardV2> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _glowAnimation;
  bool _isHovered = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat(reverse: true);
    
    _glowAnimation = Tween<double>(begin: 0.5, end: 1.0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => _isHovered = true),
      onExit: (_) => setState(() => _isHovered = false),
      child: AnimatedBuilder(
        animation: _glowAnimation,
        builder: (context, child) {
          return GestureDetector(
            onTap: widget.onTap,
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              padding: const EdgeInsets.all(16),
              width: MediaQuery.of(context).size.width * 0.72,
              decoration: BoxDecoration(
                color: Colors.black,
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: widget.color.withValues(alpha: 0.8 + 0.2 * _glowAnimation.value),
                  width: 3 + 2 * _glowAnimation.value,
                ),
                boxShadow: [
                  // Main glow
                  BoxShadow(
                    color: widget.color.withValues(alpha: 0.2 * _glowAnimation.value),
                    blurRadius: 15,
                    spreadRadius: 2,
                  ),
                  // Inner glow
                  BoxShadow(
                    color: widget.color.withValues(alpha: 0.1 * _glowAnimation.value),
                    blurRadius: 8,
                    spreadRadius: -2,
                  ),
                  // Hover effect
                  if (_isHovered)
                    BoxShadow(
                      color: widget.color.withValues(alpha: 0.3),
                      blurRadius: 20,
                      spreadRadius: 5,
                    ),
                ],
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // Title with enhanced glow effect
                  Text(
                    widget.title,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: widget.color,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      shadows: [
                        Shadow(
                          color: widget.color.withValues(alpha: 0.5 * _glowAnimation.value),
                          blurRadius: 10,
                          offset: const Offset(0, 0),
                        ),
                        Shadow(
                          color: widget.color.withValues(alpha: 0.3 * _glowAnimation.value),
                          blurRadius: 20,
                          offset: const Offset(0, 0),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 8),
                  // Summary with subtle glow
                  Text(
                    widget.summary,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: widget.color.withValues(alpha: 0.9),
                      fontSize: 16,
                      shadows: [
                        Shadow(
                          color: widget.color.withValues(alpha: 0.2 * _glowAnimation.value),
                          blurRadius: 5,
                          offset: const Offset(0, 0),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
} 