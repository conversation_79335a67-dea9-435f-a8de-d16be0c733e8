import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../quests/north_star_model.dart';
import '../controller/user_controller2.dart';
import '../utils/date_formatter.dart';
import '../diary/ns_journal_xl.dart'; // ← Import the new NS Journal XL

class NsTracker extends StatefulWidget {
  final bool fullScreen;
  const NsTracker({super.key, this.fullScreen = false});

  @override
  State<NsTracker> createState() => _NsTrackerState();
}

class _NsTrackerState extends State<NsTracker> {
  final ScrollController _scrollController = ScrollController();

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _deleteLog(NorthStarLog toRemove) async {
    final uc = Provider.of<UserController2>(context, listen: false);
    final user = uc.user;
    if (user == null || user.northStarQuest == null) return;
    final quest = user.northStarQuest!;
    final updatedLogs = quest.logs.where((l) => l.id != toRemove.id).toList();

    final newTotalExp = updatedLogs.fold<int>(0, (sum, l) => sum + (l.hours * 10).round());
    final newHours = updatedLogs.fold<double>(0.0, (sum, l) => sum + l.hours);

    final updatedQuest = quest.copyWith(
      logs: updatedLogs,
      totalExp: newTotalExp,
      hoursLogged: newHours,
    );

    final updatedUser = user.copyWith(northStarQuest: updatedQuest);
    uc.updateUser(updatedUser);
    await uc.save();
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    final user = Provider.of<UserController2>(context).user;
    if (user == null || user.northStarQuest == null) {
      return const Padding(
        padding: EdgeInsets.all(16),
        child: Text(
          'No North-Star quest found.',
          style: TextStyle(
            color: Colors.white54,
            fontFamily: 'Bitsumishi',
          ),
        ),
      );
    }
    final quest = user.northStarQuest!;
    final glow = quest.glowColor;

    final seen = <String>{};
    final logs = quest.logs.where((l) => seen.add(l.id)).toList()
      ..sort((a, b) => b.loggedAt.compareTo(a.loggedAt));

    if (logs.isEmpty) {
      return const Padding(
        padding: EdgeInsets.all(16),
        child: Text(
          'No North-Star logs yet.',
          style: TextStyle(
            color: Colors.white54,
            fontFamily: 'Bitsumishi',
          ),
        ),
      );
    }

    final listView = Scrollbar(
      controller: _scrollController,
      thumbVisibility: true,
      child: ListView.builder(
        controller: _scrollController,
        padding: const EdgeInsets.all(12),
        itemCount: logs.length,
        itemBuilder: (ctx, i) {
          final log = logs[i];
          final earned = (log.hours * 10).round();
          final category = log.category;
          final note = log.entryText.replaceFirst(RegExp(r'^\[N\.S\. - .*?\]\s*'), '');
          final ts = DateFormatter.formatDateTime(log.loggedAt);

          return Padding(
            padding: const EdgeInsets.symmetric(vertical: 4),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    '• [$ts] $category - $note (+$earned EXP)',
                    style: TextStyle(
                      color: glow,
                      fontFamily: 'Bitsumishi',
                      fontSize: 14,
                    ),
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.edit, size: 20),
                  color: glow,
                  onPressed: () {
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (_) => NsJournalXL(
                          existingLog: log,
                          onCompleted: () {
                            setState(() {}); // Refresh after editing
                          },
                        ),
                      ),
                    );
                  },
                ),
                IconButton(
                  icon: const Icon(Icons.delete, size: 20),
                  color: glow.withValues(alpha: 0.7),
                  onPressed: () => _deleteLog(log),
                ),
              ],
            ),
          );
        },
      ),
    );

    if (widget.fullScreen) {
      // ListView is only used full screen when explicitly requested
      return listView;
    }

    // Container with fixed height ensures no infinite constraints
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(12),
      ),
      height: 250,
      child: listView,
    );
  }
}
