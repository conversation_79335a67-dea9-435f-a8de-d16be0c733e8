// 📁 lib/quests/ns_exp_tracker.dart

//import 'dart:math';
import 'package:flutter/material.dart';
import '../models/user_model.dart';
import '../quests/north_star_model.dart';
//import '../theme/colors.dart';
import '../utils/rank_utils.dart';

class NsExpTracker extends StatelessWidget {
  final NorthStarQuest quest;
  final Function(User) onQuestUpdate;

  const NsExpTracker({
    super.key,
    required this.quest,
    required this.onQuestUpdate,
  });

  @override
  Widget build(BuildContext context) {
    // quest is non-nullable, so this check is unnecessary
    // if (quest == null) return const SizedBox.shrink();

    // final String rankTitle = RankUtils.getRankName(exp);
    // final int nextExp = RankUtils.getExpToNextRank(exp);
    final double progress = RankUtils.getProgressPercent(quest.totalExp);

    // pull in the current North Star quest's glow color
    final Color glowColor = quest.glowColor;

    // Calculate container width and ensure non-negative
    final double rawWidth = MediaQuery.of(context).size.width - 40;
    final double fullWidth = rawWidth < 0 ? 0.0 : rawWidth;

    // Compute progress width, safely clamped between 0 and fullWidth
    final double progressWidth = (fullWidth * progress).clamp(0.0, fullWidth);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 6),

        // 🌈 EXP PROGRESS BAR
        ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: Container(
            width: double.infinity,
            height: 24,
            decoration: BoxDecoration(
              color: Colors.white10,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Stack(
              children: [
                AnimatedContainer(
                  duration: const Duration(milliseconds: 500),
                  width: progressWidth,
                  height: 20,
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      colors: [
                        Color(0xFF00FFFF), // Cyan
                        Color(0xFF9C27B0), // Purple
                        Color(0xFFFFD700), // Gold
                      ],
                    ),
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: glowColor.withValues(alpha: 0.5),
                        blurRadius: 8,
                        spreadRadius: 2,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),

        const SizedBox(height: 6),
      ],
    );
  }
}
