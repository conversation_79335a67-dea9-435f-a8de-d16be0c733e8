import '../models/diary_entry_model.dart';
import '../models/user_model.dart';

mixin UserControllerDiary {
  User? get user;

  /// Returns the diary entries for the current user.
  List<DiaryEntry> get diaryEntries => user?.diaryEntries ?? [];

  /// Add a new diary entry.
  Future<void> addDiaryEntry(DiaryEntry entry) async {
    if (user == null) return;
    user!.diaryEntries.add(entry);
  }

  /// Remove a diary entry by id.
  void removeDiaryEntry(String entryId) {
    if (user == null) return;
    user!.diaryEntries.removeWhere((e) => e.id == entryId);
  }

  /// Update a diary entry by id.
  void updateDiaryEntry(DiaryEntry updatedEntry) {
    if (user == null) return;
    final idx = user!.diaryEntries.indexWhere((e) => e.id == updatedEntry.id);
    if (idx != -1) {
      user!.diaryEntries[idx] = updatedEntry;
    }
  }

  /// Edit a diary entry.
  Future<void> editDiaryEntry(DiaryEntry entry) async {
    if (user == null) return;
    final idx = user!.diaryEntries.indexWhere((e) => e.id == entry.id);
    if (idx != -1) {
      user!.diaryEntries[idx] = entry;
    }
  }

  /// Delete a diary entry.
  Future<void> deleteDiaryEntry(String entryId) async {
    if (user == null) return;
    user!.diaryEntries.removeWhere((e) => e.id == entryId);
  }
}