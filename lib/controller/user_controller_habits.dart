import '../models/user_model.dart';
import '../models/habit_model.dart';

mixin UserControllerHabits {
  User? get user;

  /// Returns the habits for the current user.
  List<Habit> get habits => user?.dailyHabits ?? [];

  /// Add a new habit.
  void addHabit(Habit habit) {
    if (user == null) return;
    user!.dailyHabits.add(habit);
  }

  /// Remove a habit by id.
  void removeHabit(String habitId) {
    if (user == null) return;
    user!.dailyHabits.removeWhere((h) => h.id == habitId);
  }

  /// Update a habit by id.
  void updateHabit(Habit updatedHabit) {
    if (user == null) return;
    final idx = user!.dailyHabits.indexWhere((h) => h.id == updatedHabit.id);
    if (idx != -1) {
      user!.dailyHabits[idx] = updatedHabit;
    }
  }

  /// Complete a habit by id.
  Future<void> completeHabit(String habitId) async {
    if (user == null) return;
    final idx = user!.dailyHabits.indexWhere((h) => h.id == habitId);
    if (idx != -1) {
      final habit = user!.dailyHabits[idx];
      final updatedHabit = habit.copyWith(
        lastCompleted: DateTime.now(),
        lastModified: DateTime.now(),
      );
      user!.dailyHabits[idx] = updatedHabit;
    }
  }

  /// Reset daily habits.
  Future<void> resetDailyHabits() async {
    if (user == null) return;
    for (int i = 0; i < user!.dailyHabits.length; i++) {
      final habit = user!.dailyHabits[i];
      final updatedHabit = habit.copyWith(
        lastCompleted: null,
        lastModified: DateTime.now(),
      );
      user!.dailyHabits[i] = updatedHabit;
    }
  }

  /// Update the entire habits list.
  Future<void> updateHabits(List<Habit> updatedList) async {
    if (user == null) return;
    // This method should be called from UserControllerBase which has access to updateUser
    // For now, we'll throw an error to indicate this needs to be handled at the base level
    throw UnimplementedError('updateHabits should be called from UserControllerBase.updateUserHabits()');
  }
}
