import 'package:flutter/foundation.dart';
import '../models/user_model.dart';
import '../quests/north_star_model.dart';
import '../bulletproof/error_handler.dart';
import '../services/user_service.dart';

mixin UserControllerNorthStar on ChangeNotifier {
  User? get user;
  set user(User? u);
  
  late final ErrorHandler _errorHandler;
  late final UserService _userService;
  
  // Quest tracking
  final List<NorthStarQuest> _questHistory = [];
  DateTime? _lastQuestUpdate;
  bool _isUpdatingQuest = false;

  NorthStarQuest? get northStarQuest => user?.northStarQuest;
  List<NorthStarQuest> get questHistory => List.unmodifiable(_questHistory);
  DateTime? get lastQuestUpdate => _lastQuestUpdate;
  bool get isUpdatingQuest => _isUpdatingQuest;

  void initialize() {
    _errorHandler = ErrorHandler();
    _userService = UserService(_errorHandler);
  }

  /// Set the North Star quest and notify listeners.
  Future<void> setNorthStarQuest(NorthStarQuest? quest) async {
    if (user == null) {
      await _errorHandler.handleError(
        Exception('Cannot set quest: No user found'),
        StackTrace.current,
        context: 'setNorthStarQuest',
      );
      return;
    }

    if (_isUpdatingQuest) {
      await _errorHandler.handleError(
        Exception('Quest update already in progress'),
        StackTrace.current,
        context: 'setNorthStarQuest',
      );
      return;
    }

    try {
      _isUpdatingQuest = true;
      
      // Validate quest if not null
      if (quest != null && !_validateQuest(quest)) {
        throw Exception('Invalid quest data');
      }

      // Update user with new quest
      final updatedUser = user!.copyWith(
        northStarQuest: quest,
        lastModified: DateTime.now(),
      );

      // Save to history if not null
      if (quest != null) {
        _questHistory.add(quest);
        _lastQuestUpdate = DateTime.now();
      }

      // Update user
      user = updatedUser;
      
      // Persist changes
      await _userService.saveUser(updatedUser);
      
      notifyListeners();
    } catch (e, stackTrace) {
      await _errorHandler.handleError(
        e,
        stackTrace,
        context: 'setNorthStarQuest',
      );
    } finally {
      _isUpdatingQuest = false;
    }
  }

  /// Clear the North Star quest and notify listeners.
  Future<void> clearNorthStarQuest() async {
    if (user == null) {
      await _errorHandler.handleError(
        Exception('Cannot clear quest: No user found'),
        StackTrace.current,
        context: 'clearNorthStarQuest',
      );
      return;
    }

    if (_isUpdatingQuest) {
      await _errorHandler.handleError(
        Exception('Quest update already in progress'),
        StackTrace.current,
        context: 'clearNorthStarQuest',
      );
      return;
    }

    try {
      _isUpdatingQuest = true;
      
      final updatedUser = user!.copyWith(
        northStarQuest: null,
        lastModified: DateTime.now(),
      );

      // Update user
      user = updatedUser;
      
      // Persist changes
      await _userService.saveUser(updatedUser);
      
      notifyListeners();
    } catch (e, stackTrace) {
      await _errorHandler.handleError(
        e,
        stackTrace,
        context: 'clearNorthStarQuest',
      );
    } finally {
      _isUpdatingQuest = false;
    }
  }

  /// Update quest progress
  Future<void> updateQuestProgress(double hours) async {
    if (user == null || northStarQuest == null) {
      await _errorHandler.handleError(
        Exception('Cannot update progress: No active quest'),
        StackTrace.current,
        context: 'updateQuestProgress',
      );
      return;
    }

    if (_isUpdatingQuest) {
      await _errorHandler.handleError(
        Exception('Quest update already in progress'),
        StackTrace.current,
        context: 'updateQuestProgress',
      );
      return;
    }

    try {
      _isUpdatingQuest = true;
      
      final updatedQuest = northStarQuest!.copyWith(
        hoursLogged: northStarQuest!.hoursLogged + hours,
        totalExp: northStarQuest!.totalExp + (hours * 100).round(),
      );

      await setNorthStarQuest(updatedQuest);
    } catch (e, stackTrace) {
      await _errorHandler.handleError(
        e,
        stackTrace,
        context: 'updateQuestProgress',
      );
    } finally {
      _isUpdatingQuest = false;
    }
  }

  /// Add a milestone to the current quest
  Future<void> addMilestone(String milestone) async {
    if (user == null || northStarQuest == null) {
      await _errorHandler.handleError(
        Exception('Cannot add milestone: No active quest'),
        StackTrace.current,
        context: 'addMilestone',
      );
      return;
    }

    if (_isUpdatingQuest) {
      await _errorHandler.handleError(
        Exception('Quest update already in progress'),
        StackTrace.current,
        context: 'addMilestone',
      );
      return;
    }

    try {
      _isUpdatingQuest = true;
      
      final updatedMilestones = [...northStarQuest!.milestones, milestone];
      final updatedQuest = northStarQuest!.copyWith(
        milestones: updatedMilestones,
      );

      await setNorthStarQuest(updatedQuest);
    } catch (e, stackTrace) {
      await _errorHandler.handleError(
        e,
        stackTrace,
        context: 'addMilestone',
      );
    } finally {
      _isUpdatingQuest = false;
    }
  }

  /// Get quest analytics
  Map<String, dynamic> getQuestAnalytics() {
    final completedQuests = _questHistory.where((q) => q.hoursLogged >= 100).length;
    final totalQuests = _questHistory.length;
    final averageCompletionTime = _calculateAverageCompletionTime();
    final successRate = totalQuests > 0 ? completedQuests / totalQuests : 0.0;

    return {
      'totalQuests': totalQuests,
      'completedQuests': completedQuests,
      'successRate': successRate,
      'averageCompletionTime': averageCompletionTime,
      'lastQuestUpdate': _lastQuestUpdate,
      'totalHoursLogged': _questHistory.fold<double>(0, (sum, q) => sum + q.hoursLogged),
      'totalExpEarned': _questHistory.fold<int>(0, (sum, q) => sum + q.totalExp),
    };
  }

  /// Validate quest data
  bool _validateQuest(NorthStarQuest quest) {
    if (quest.title.isEmpty) return false;
    if (quest.summary.isEmpty) return false;
    // Note: coreValues can be empty during initial quest creation
    if (quest.category.isEmpty) return false;
    if (quest.icon.isEmpty) return false;
    return true;
  }

  /// Calculate average completion time for completed quests
  Duration? _calculateAverageCompletionTime() {
    final completedQuests = _questHistory.where((q) => 
      q.hoursLogged >= 100
    ).toList();

    if (completedQuests.isEmpty) return null;

    final totalDuration = completedQuests.fold<Duration>(
      Duration.zero,
      (total, quest) => total + DateTime.now().difference(quest.createdAt),
    );

    return Duration(
      milliseconds: totalDuration.inMilliseconds ~/ completedQuests.length,
    );
  }
}