import 'dart:async';
import 'package:flutter/foundation.dart';
import '../models/user_model.dart';
import '../models/diary_entry_model.dart';
import '../models/habit_model.dart';
import '../quests/north_star_model.dart';
import '../notify/widget_controller.dart';
import 'package:flutter/material.dart';
//import 'package:shared_preferences/shared_preferences.dart';
import '../bulletproof/error_handler.dart';
import '../services/user_service.dart';
import './user_controller2.dart';

/// Base controller for user state management and persistence.
///
/// [UserControllerBase] provides the foundational functionality for user
/// management in the Maxed Out Life application. It handles:
///
/// Core responsibilities:
/// - Current user state management
/// - User loading and persistence
/// - Error handling and recovery
/// - Loading state management
/// - Widget controller integration
///
/// This class serves as the base for [UserController2] and provides
/// the essential infrastructure that specialized mixins build upon.
///
/// Features:
/// - Automatic error handling through [ErrorHandler]
/// - Persistent storage through [UserService]
/// - Real-time UI updates via [ChangeNotifier]
/// - Widget state synchronization
/// - Loading state tracking for UI feedback
///
/// The controller maintains a single current user and ensures all
/// changes are properly persisted and communicated to the UI layer.
class UserControllerBase extends ChangeNotifier {
  late final ErrorHandler _errorHandler;
  late final UserService _userService;
  User? _user;
  bool _isLoading = false;
  String? _error;
  //late Future<SharedPreferences> _prefs;
  late final WidgetController _widgetController;

  // Race condition prevention - ensures only one async operation at a time
  bool _operationInProgress = false;

  UserControllerBase() {
    _errorHandler = ErrorHandler();
    _userService = UserService(_errorHandler);
    _init();
  }

  Future<void> _init() async {
    //_prefs = SharedPreferences.getInstance();
    _widgetController = WidgetController(this as UserController2?, _errorHandler);
    try {
      _isLoading = true;
      notifyListeners();

      final lastUser = await _userService.getLastUser();
      if (lastUser != null) {
        _user = await _userService.loadUserByUsername(lastUser);
      }

      _isLoading = false;
      notifyListeners();
    } catch (e, stackTrace) {
      _isLoading = false;
      _error = e.toString();
      await _errorHandler.handleError(e, stackTrace, context: 'User initialization');
      notifyListeners();
    }
  }

  /// Safe getter for the current user.
  User? get user => _user;
  set user(User? value) {
    _user = value;
    notifyListeners();
  }

  /// Whether a user is loaded.
  bool get hasUser => _user != null;

  WidgetController get widgetController => _widgetController;

  bool get isLoading => _isLoading;
  String? get error => _error;

  @protected
  ErrorHandler get errorHandler => _errorHandler;

  /// Load the active user from storage and clean out duplicates.
  Future<void> refreshFromDisk() async {
    // Prevent race conditions
    if (_operationInProgress) {
      debugPrint('⚠️ Operation already in progress, skipping refreshFromDisk');
      return;
    }

    try {
      _operationInProgress = true;
      _isLoading = true;
      _error = null;
      notifyListeners();

      final lastUsername = await _userService.getLastUser();
      if (lastUsername != null) {
        final loaded = await _userService.loadUserByUsername(lastUsername);
        if (loaded != null) {
          // Ensure all built-in categories are present
          const builtInCategories = ['Health', 'Wealth', 'Purpose', 'Connection'];
          final updatedCategories = Map<String, int>.from(loaded.categories);
          for (final cat in builtInCategories) {
            updatedCategories.putIfAbsent(cat, () => 0);
          }
          final userWithAllCats = loaded.copyWith(categories: updatedCategories);
          _user = _removeDuplicates(userWithAllCats);
        }
      }

      _isLoading = false;
      notifyListeners();
    } catch (e, stackTrace) {
      _isLoading = false;
      _error = e.toString();
      await _errorHandler.handleError(e, stackTrace, context: 'refreshFromDisk');
      notifyListeners();
    } finally {
      _operationInProgress = false;
    }
  }

  /// Replace the in-memory user (runs dedupe + category cleanup) and notify listeners.
  Future<void> updateUser(User user) async {
    // Prevent race conditions
    if (_operationInProgress) {
      debugPrint('⚠️ Operation already in progress, skipping updateUser');
      return;
    }

    try {
      _operationInProgress = true;
      _isLoading = true;
      _error = null;
      notifyListeners();

      await _userService.updateUser(user);
      _user = _removeDuplicates(user);
      _isLoading = false;
      notifyListeners();

      // Sync OS widgets with saved user state (don't let this block user save)
      try {
        await _widgetController.syncWithUserState();
        final prefs = await _widgetController.loadPreferences();
        await _widgetController.updateWidgets(prefs);
      } catch (e, stackTrace) {
        // Log widget sync error but don't fail the user save operation
        await _errorHandler.handleError(e, stackTrace, context: 'updateUser.widgetSync');
      }
    } catch (e, stackTrace) {
      _isLoading = false;
      _error = e.toString();
      await _errorHandler.handleError(e, stackTrace, context: 'updateUser');
      notifyListeners();
    } finally {
      _operationInProgress = false;
    }
  }

  /// Persist the current user back to storage.
  Future<void> save() async {
    if (_user != null) {
      try {
        await _userService.updateUser(_user!);
        notifyListeners();
        // Sync OS widgets with saved user state (don't let this block user save)
        try {
          await _widgetController.syncWithUserState();
          final prefs = await _widgetController.loadPreferences();
          await _widgetController.updateWidgets(prefs);
        } catch (e, stackTrace) {
          // Log widget sync error but don't fail the user save operation
          await _errorHandler.handleError(e, stackTrace, context: 'save.widgetSync');
        }
      } catch (e, stackTrace) {
        await _errorHandler.handleError(e, stackTrace, context: 'save');
        rethrow;
      }
    }
  }

  /// Remove duplicate diary entries or North-Star logs, then prune invalid/zero/negative categories.
  User _removeDuplicates(User user) {
    // 1️⃣ Dedupe diary entries
    final seenDiary = <String>{};
    final cleanDiary = <DiaryEntry>[];
    for (var entry in user.diaryEntries) {
      if (seenDiary.add(entry.id)) {
        cleanDiary.add(entry);
      }
    }

    // 2️⃣ Dedupe North Star logs, if present
    NorthStarQuest? quest = user.northStarQuest;
    if (quest != null) {
      final seenLogs = <String>{};
      final cleanLogs = <NorthStarLog>[];
      for (var l in quest.logs) {
        if (seenLogs.add(l.id)) {
          cleanLogs.add(l);
        }
      }
      quest = quest.copyWith(logs: cleanLogs);
    }

    // 3️⃣ Prune invalid categories while preserving custom categories
    final validKeys = <String>{};
    // Add all custom categories (even with 0 EXP)
    validKeys.addAll(user.categories.keys);
    // Remove any categories with negative or zero EXP
    final cleanCategories = Map<String, int>.fromEntries(
      user.categories.entries.where((e) => e.value > 0 || validKeys.contains(e.key))
    );

    return user.copyWith(
      diaryEntries: cleanDiary,
      northStarQuest: quest,
      categories: cleanCategories,
    );
  }

  /// Delete the current user and all associated data.
  Future<void> deleteCurrentUser() async {
    if (_user == null) {
      throw Exception('No user loaded');
    }

    try {
      await _userService.deleteUser(_user!.username);
      _user = null;
      notifyListeners();
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'deleteCurrentUser');
      rethrow;
    }
  }

  /// Clear all user data.
  Future<void> clearAllUserData() async {
    try {
      await _userService.clearUserData();
      _user = null;
      notifyListeners();
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'clearAllUserData');
      rethrow;
    }
  }

  /// Update user information.
  Future<void> updateUserInfo({
    String? username,
    String? gender,
    Map<String, int>? categories,
  }) async {
    if (_user == null) return;
    
    final updatedUser = _user!.copyWith(
      username: username ?? _user!.username,
      gender: gender ?? _user!.gender,
      categories: categories ?? _user!.categories,
    );
    
    await updateUser(updatedUser);
  }

  /// Update user habits.
  Future<void> updateUserHabits(List<Habit> habits) async {
    if (_user == null) return;
    
    final updatedUser = _user!.copyWith(
      dailyHabits: habits,
    );
    
    await updateUser(updatedUser);
  }
}