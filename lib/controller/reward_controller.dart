import 'package:flutter/foundation.dart';
import '../models/reward_model.dart';
import '../services/reward_engine.dart';

/// Controller for the Bounty Hunter reward system.
class RewardController extends ChangeNotifier {
  final RewardEngine _engine = RewardEngine.instance;

  ValueListenable<RewardModel> get model => _engine.stateNotifier;
  Stream<QuestEvent> get questStream => _engine.questStream;
  Stream<BonusEvent> get bonusStream => _engine.bonusStream;

  /// Log a work block in a category.
  Future<void> logWork(String category) async {
    await _engine.recordWorkBlock(category);
    notifyListeners();
  }

  /// Spin the bounty gate and set a random eligible category as the bonus category.
  Future<void> spinBountyGate(List<String> eligibleCategories) async {
    if (eligibleCategories.isEmpty) return;
    eligibleCategories.shuffle();
    await _engine.setBonusCategory(eligibleCategories.first);
    notifyListeners();
  }

  /// Initialize the reward system (reset daily, activate quests, notifications).
  Future<void> initialize() async {
    await _engine.initialize();
    await _engine.initializeNotifications();
    notifyListeners();
  }

  @override
  void dispose() {
    _engine.dispose();
    super.dispose();
  }
} 