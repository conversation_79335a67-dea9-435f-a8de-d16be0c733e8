// lib/controller/firebase_auth_controller.dart

import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;
import '../services/firebase_auth_service.dart';
import '../services/comprehensive_logging_service.dart';
import '../models/user_model.dart';
import '../services/user_service.dart';
import '../bulletproof/error_handler.dart';
import '../services/saved_accounts_service.dart';

/// 🔥 Firebase Authentication Controller
/// 
/// Manages user authentication state using Firebase Auth
/// while maintaining compatibility with existing User model.
/// 
/// Features:
/// - Firebase Auth integration
/// - Local user model sync
/// - Email verification handling
/// - Error state management
class FirebaseAuthController extends ChangeNotifier {
  firebase_auth.User? _firebaseUser;
  User? _localUser;
  bool _isLoading = false;
  String? _error;
  bool _isInitialized = false;

  final UserService _userService;

  FirebaseAuthController({
    UserService? userService,
    ErrorHandler? errorHandler,
  }) : _userService = userService ?? UserService(ErrorHandler());

  // Getters
  firebase_auth.User? get firebaseUser => _firebaseUser;
  User? get localUser => _localUser;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isSignedIn => _firebaseUser != null;
  bool get isEmailVerified => _firebaseUser?.emailVerified ?? false;
  bool get isInitialized => _isInitialized;

  /// Initialize the controller
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await ComprehensiveLoggingService.logInfo('🔥 Initializing Firebase Auth Controller');
      
      // Initialize Firebase Auth Service
      await FirebaseAuthService.initialize();
      
      // Listen to auth state changes
      firebase_auth.FirebaseAuth.instance.authStateChanges().listen(_onAuthStateChanged);
      
      // Check current user
      _firebaseUser = FirebaseAuthService.currentUser;
      if (_firebaseUser != null) {
        await _syncLocalUser();
      }
      
      _isInitialized = true;
      await ComprehensiveLoggingService.logInfo('✅ Firebase Auth Controller initialized');
      notifyListeners();
      
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Failed to initialize Firebase Auth Controller: $e');
      _setError('Failed to initialize authentication');
    }
  }

  /// Handle Firebase auth state changes
  void _onAuthStateChanged(firebase_auth.User? user) async {
    try {
      await ComprehensiveLoggingService.logInfo('🔥 Auth state changed: ${user?.email ?? 'signed out'}');
      
      _firebaseUser = user;
      
      if (user != null) {
        await _syncLocalUser();
      } else {
        _localUser = null;
      }
      
      notifyListeners();
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Error handling auth state change: $e');
    }
  }

  /// Sync Firebase user with local User model
  Future<void> _syncLocalUser() async {
    if (_firebaseUser == null) return;

    try {
      // Try to load existing local user
      final existingUser = await _userService.loadUserByUsername(_firebaseUser!.displayName ?? '');
      
      if (existingUser != null) {
        // Update existing user with Firebase data
        _localUser = existingUser.copyWith(
          email: _firebaseUser!.email,
          isEmailVerified: _firebaseUser!.emailVerified,
        );
      } else {
        // Create new local user from Firebase user
        _localUser = User.blank(
          id: _firebaseUser!.displayName ?? _firebaseUser!.email!.split('@')[0],
          username: _firebaseUser!.displayName ?? _firebaseUser!.email!.split('@')[0],
        ).copyWith(
          email: _firebaseUser!.email,
          isEmailVerified: _firebaseUser!.emailVerified,
          klaviyoSubscribed: true, // Assume true since we add to Klaviyo on signup
        );
      }
      
      // Save updated user
      await _userService.saveUser(_localUser!);
      await _userService.saveLastUser(_localUser!.username);
      
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Failed to sync local user: $e');
    }
  }

  /// Create account with email and password
  Future<bool> createAccount({
    required String email,
    required String password,
    required String username,
    required String gender,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      await ComprehensiveLoggingService.logInfo('🔥 Creating account for: $email');

      final result = await FirebaseAuthService.createAccount(
        email: email,
        password: password,
        username: username,
        gender: gender,
      );

      if (result.success) {
        await ComprehensiveLoggingService.logInfo('✅ Account created successfully');

        // For test email, create a local user since Firebase is bypassed
        if (email == '<EMAIL>') {
          await _createTestUser(username, email, gender);
        }

        return true;
      } else {
        _setError(result.message);
        return false;
      }

    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Failed to create account: $e');
      _setError('Failed to create account. Please try again.');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Create account without email requirement
  Future<bool> createAccountWithoutEmail({
    required String username,
    required String gender,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      await ComprehensiveLoggingService.logInfo('🔥 Creating email-less account for: $username');

      // Create local user without Firebase authentication
      await _createEmailLessUser(username, gender);

      await ComprehensiveLoggingService.logInfo('✅ Email-less account created successfully');
      return true;

    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Email-less account creation failed: $e');
      _setError('Account creation failed: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Create test <NAME_EMAIL> (bypasses Firebase)
  Future<void> _createTestUser(String username, String email, String gender) async {
    try {
      await ComprehensiveLoggingService.logInfo('🧪 Creating test user locally: $username with gender: $gender');

      // Generate coach assignments for non-gender users or ensure proper gender handling
      Map<String, String>? assignedCoaches;
      if (gender.toLowerCase() == 'non-gender') {
        final random = Random();
        assignedCoaches = {
          'Health': random.nextBool() ? 'Male' : 'Female',
          'Wealth': random.nextBool() ? 'Male' : 'Female',
          'Purpose': random.nextBool() ? 'Male' : 'Female',
          'Connection': random.nextBool() ? 'Male' : 'Female',
          'Custom Category 1': random.nextBool() ? 'Male' : 'Female',
          'Custom Category 2': random.nextBool() ? 'Male' : 'Female',
        };
        await ComprehensiveLoggingService.logInfo('🧪 Generated coach assignments for non-gender user');
      }

      // Create a local user object for testing
      final testUser = User.blank(
        id: username,
        username: username,
      ).copyWith(
        email: email,
        gender: gender, // Use the gender passed from signup flow
        isEmailVerified: true, // Test email is always "verified"
        klaviyoSubscribed: true,
        createdAt: DateTime.now(),
        lastLoginAt: DateTime.now(),
        assignedCoaches: assignedCoaches,
      );

      // Save the test user to storage so it can be found during sign-in
      await _userService.saveUser(testUser);
      await _userService.saveLastUser(username);

      // Set as current user
      _localUser = testUser;
      notifyListeners();

      // Save username for future convenience (dropdown feature)
      await SavedAccountsService.saveUsername(username);
      await ComprehensiveLoggingService.logInfo('💾 Test user username saved for dropdown: $username');

      await ComprehensiveLoggingService.logInfo('✅ Test user created and saved successfully');
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Failed to create test user: $e');
    }
  }

  /// Create email-less user without Firebase authentication
  Future<void> _createEmailLessUser(String username, String gender) async {
    try {
      // Generate assigned coaches for email-less user
      final assignedCoaches = <String, String>{};

      // Create a local user object for email-less signup
      final emailLessUser = User.blank(
        id: username,
        username: username,
      ).copyWith(
        gender: gender,
        email: null, // No email required
        passwordHash: null, // No password required
        isEmailVerified: false, // Always false for email-less users
        klaviyoSubscribed: false, // Skip Klaviyo
        createdAt: DateTime.now(),
        lastLoginAt: DateTime.now(),
        assignedCoaches: assignedCoaches,
      );

      // Save the email-less user to storage
      await _userService.saveUser(emailLessUser);
      await _userService.saveLastUser(username);

      // Set as current user
      _localUser = emailLessUser;
      notifyListeners();

      // Save username for future convenience (dropdown feature)
      await SavedAccountsService.saveUsername(username);
      await ComprehensiveLoggingService.logInfo('💾 Email-less username saved for dropdown: $username');

      await ComprehensiveLoggingService.logInfo('✅ Email-less user created and saved successfully');
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Failed to create email-less user: $e');
    }
  }

  /// Load test <NAME_EMAIL> (bypasses Firebase)
  Future<void> _loadTestUser() async {
    try {
      await ComprehensiveLoggingService.logInfo('🧪 Loading test user for sign-in');

      // Try to load existing test user from storage first
      final userService = UserService(ErrorHandler());
      User? existingTestUser;

      // Check for any existing <NAME_EMAIL> email
      final allUsernames = await userService.getAllUsernames();
      for (final username in allUsernames) {
        final user = await userService.loadUserByUsername(username);
        if (user?.email == '<EMAIL>') {
          existingTestUser = user;
          break;
        }
      }

      if (existingTestUser != null) {
        // Use existing test user and update last login
        _localUser = existingTestUser.copyWith(
          lastLoginAt: DateTime.now(),
        );
        await ComprehensiveLoggingService.logInfo('✅ Existing test user loaded: ${_localUser!.username}');
      } else {
        // This should not happen if _createTestUser was called properly
        // But provide a fallback with Female gender to ensure proper coach assignment
        await ComprehensiveLoggingService.logInfo('⚠️ No test user found, creating fallback with Female gender');

        final testUser = User.blank(
          id: 'xyz',
          username: 'xyz',  // Use 'xyz' as the username for consistency
        ).copyWith(
          email: '<EMAIL>',
          gender: 'Female', // Default to Female for proper coach assignment
          isEmailVerified: true,
          klaviyoSubscribed: true,
          lastLoginAt: DateTime.now(),
        );

        // Save the test user to storage so it can be found next time
        await _userService.saveUser(testUser);
        await _userService.saveLastUser('xyz');

        // Set as current user
        _localUser = testUser;
        await ComprehensiveLoggingService.logInfo('✅ Fallback test user created with username: xyz');
      }

      // Save the test user's username for the saved accounts dropdown
      if (_localUser?.username != null && _localUser!.username.isNotEmpty) {
        await SavedAccountsService.saveUsername(_localUser!.username);
        await ComprehensiveLoggingService.logInfo('💾 Test user username saved for dropdown: ${_localUser!.username}');
      }

      notifyListeners();
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Failed to load test user: $e');
    }
  }

  /// Sign in with email and password
  Future<bool> signIn({
    required String email,
    required String password,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      await ComprehensiveLoggingService.logInfo('🔥 Signing in: $email');

      final result = await FirebaseAuthService.signIn(
        email: email,
        password: password,
      );

      if (result.success) {
        await ComprehensiveLoggingService.logInfo('✅ Signed in successfully');

        // For test email, create/load a local user since Firebase is bypassed
        if (email == '<EMAIL>') {
          await _loadTestUser();
        }

        return true;
      } else {
        _setError(result.message);
        return false;
      }

    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Failed to sign in: $e');
      _setError('Failed to sign in. Please try again.');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Send email verification
  Future<bool> sendEmailVerification() async {
    try {
      final success = await FirebaseAuthService.sendEmailVerification();
      if (!success) {
        _setError('Failed to send verification email');
      }
      return success;
    } catch (e) {
      _setError('Failed to send verification email');
      return false;
    }
  }

  /// Reload user to check verification status
  Future<void> reloadUser() async {
    try {
      await FirebaseAuthService.reloadUser();
      await _syncLocalUser();
      notifyListeners();
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Failed to reload user: $e');
    }
  }

  /// Sign out
  Future<bool> signOut() async {
    _setLoading(true);
    
    try {
      final success = await FirebaseAuthService.signOut();
      if (success) {
        _localUser = null;
        _firebaseUser = null;
        notifyListeners();
      }
      return success;
    } catch (e) {
      _setError('Failed to sign out');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Send password reset email
  Future<bool> sendPasswordResetEmail(String email) async {
    try {
      return await FirebaseAuthService.sendPasswordResetEmail(email);
    } catch (e) {
      _setError('Failed to send password reset email');
      return false;
    }
  }

  /// Set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// Set error message
  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  /// Clear error message
  void _clearError() {
    _error = null;
    notifyListeners();
  }
}
