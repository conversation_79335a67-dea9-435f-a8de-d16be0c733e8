// Removed unused import
import './user_controller_base.dart';
import './user_controller_habits.dart';
import './user_controller_diary.dart';
import './user_controller_northstar.dart';
import './user_controller_widgets.dart';
import './user_controller_auth.dart';
import '../models/user_model.dart';

/// Primary user controller that combines all user-related functionality.
///
/// [UserController2] is the main controller for user state management in the
/// Maxed Out Life application. It extends [UserControllerBase] and mixes in
/// specialized controllers for different aspects of user data.
///
/// This controller provides a unified interface for:
/// - User data persistence and retrieval
/// - Daily habit management and completion tracking
/// - Diary entry creation and management
/// - North Star quest logging and progress tracking
/// - Widget state synchronization
///
/// The controller uses the Provider pattern for state management and
/// automatically handles data persistence through the underlying services.
///
/// Mixins included:
/// - [UserControllerHabits]: Habit management functionality
/// - [UserControllerDiary]: Diary entry management
/// - [UserControllerNorthStar]: North Star quest functionality
/// - [UserControllerWidgets]: Widget state management
///
/// Example usage:
/// ```dart
/// final controller = UserController2();
/// await controller.loadUser('username');
/// await controller.completeHabit(habit);
/// await controller.addDiaryEntry('Health', 'Workout completed', 50);
/// ```
class UserController2 extends UserControllerBase
    with
        UserControllerHabits,
        UserControllerDiary,
        UserControllerNorthStar,
        UserControllerWidgets,
        UserControllerAuth {
  
  UserController2() : super() {
    initialize();
  }

  @override
  Future<void> updateUser(User user) async {
    print('🔧 UserController2: updateUser called');
    print('   User ID: ${user.id}');
    print('   Username: ${user.username}');
    print('   Total EXP: ${user.exp}');
    print('   Categories: ${user.categories}');

    await super.updateUser(user);

    print('🔧 UserController2: updateUser completed');
    print('   Controller user EXP after update: ${this.user?.exp}');

    // Note: loadAuthData() is called explicitly where needed
    // Don't automatically load auth data here as it can cause issues during account creation
  }



  @override
  void dispose() {
    // Clean up any resources from mixins
    super.dispose(); // ChangeNotifier requires calling super.dispose()
  }
} 