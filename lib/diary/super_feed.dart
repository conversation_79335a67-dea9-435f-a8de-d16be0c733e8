// lib/widgets/super_feed.dart

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../utils/dperfecter_util.dart';
import '../utils/date_formatter.dart';
import '../theme/colors.dart';
import '../controller/user_controller2.dart';
import '../models/user_model.dart';

import '../widgets/empty_state_widget.dart';
import '../widgets/error_state_widget.dart';

class SuperFeed extends StatefulWidget {
  const SuperFeed({
    super.key,
    required this.user,
    required this.scrollController,
    required this.fallbackColors,
    required this.defaultCategoryColors,
  });

  final User                user;
  final ScrollController    scrollController;
  final List<Color>         fallbackColors;
  final Map<String, Color>  defaultCategoryColors;

  @override
  // ignore: library_private_types_in_public_api
  _SuperFeedState createState() => _SuperFeedState();
}

class _SuperFeedState extends State<SuperFeed> {
  // Removed RouteAware functionality to prevent setState during build issues

  @override
  void didUpdateWidget(covariant SuperFeed oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Removed scroll animation to prevent conflicts with CustomScrollView
  }

  @override
  Widget build(BuildContext context) {
    final user = context.watch<UserController2>().user;
    if (user == null) {
      return ErrorStateWidget(
        error: Exception('No user data available'),
        customMessage: 'Unable to load user data. Please try refreshing the app.',
        isFullScreen: false,
        onRetry: () {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted) {
              context.read<UserController2>().refreshFromDisk();
            }
          });
        },
      );
    }

    final List<FeedItem> feedItems = DiaryPerfecterUtil.generateFeed(user);

    // Debug logging for Super Feed modal
    print('📱 SuperFeed: Modal opened with ${feedItems.length} items');
    print('📱 SuperFeed: Scrolling enabled (AlwaysScrollableScrollPhysics)');

    if (feedItems.isEmpty) {
      return const EmptyStateWidget(
        type: EmptyStateType.diary,
        showAction: false,
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          "DIARY ENTRIES:",
          style: TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.bold,
            fontFamily: 'Bitsumishi',
          ),
        ),
        const SizedBox(height: 12),
        Container(
          height: 220,
          decoration: BoxDecoration(
            color: Colors.black12,
            border: Border.all(color: Colors.white30),
            borderRadius: BorderRadius.circular(8),
          ),
          child: ListView.builder(
            physics: const AlwaysScrollableScrollPhysics(), // Enable scrolling
            shrinkWrap: true,
            itemCount: feedItems.length,
            itemBuilder: (context, i) {
                final FeedItem item = feedItems[i];
                final String rawTs = DateFormatter.formatDateTime(item.timestamp);
                final String category = item.category;

                // 1) Strip any leading "[N.S. - ...]" or "[N.S.]" from item.note:
                String bareNote = item.note;
                // If it begins "[N.S. - <Category>]" strip that first:
                final bracketedPattern = RegExp(r'^\[N\.S\. - .*?\]\s*');
                if (bracketedPattern.hasMatch(bareNote)) {
                  bareNote = bareNote.replaceFirst(bracketedPattern, '').trim();
                }
                // Now if it still begins "[N.S.]", strip that:
                if (bareNote.startsWith('[N.S.]')) {
                  bareNote = bareNote.substring(6).trim();
                }

                // 2) Build the display string:
                //    • [TS] - Category - [N.S.] - "bareNote"
                final bool isNs = item.isNorthStar;
                final StringBuffer sb = StringBuffer();
                sb.write('[$rawTs] - ');
                sb.write(category);
                if (isNs) {
                  sb.write(' - [N.S.]');
                }
                sb.write(' - "');
                sb.write(bareNote);
                sb.write('"');

                final Color textColor = getCategoryColor(
                  category,
                  customCategories: user.customCategories,
                );

                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 3),
                  child: Container(
                    decoration: isNs
                        ? BoxDecoration(
                            boxShadow: [
                              BoxShadow(
                                color: textColor.withValues(alpha: 0.25),
                                blurRadius: 4,
                                spreadRadius: 1,
                              )
                            ],
                          )
                        : null,
                    child: Text(
                      '• ${sb.toString()}',
                      style: TextStyle(
                        fontFamily: 'Bitsumishi',
                        color: textColor,
                        fontSize: 15,
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
      ],
    );
  }
}
