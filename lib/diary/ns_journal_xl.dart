// lib/diary/ns_journal_xl.dart

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:math';
import 'dart:async';

import '../controller/user_controller2.dart';
import '../quests/north_star_model.dart';
import '../services/super_entry_service.dart';
import '../home/<USER>'; // For StrokeText

/// A combined "New/Edit North Star Entry" dialog.
/// If existingLog == null → create a brand-new entry (with hours slider).
/// If existingLog != null → edit only category & note (hours are locked).
class NsJournalXL extends StatefulWidget {
  /// When "Submit" (new or edit) succeeds, call this so parent can refresh.
  final VoidCallback? onCompleted;

  /// If non-null, we are editing this log in place.
  final NorthStarLog? existingLog;

  const NsJournalXL({
    super.key,
    this.existingLog,
    this.onCompleted,
  });

  @override
  State<NsJournalXL> createState() => _NsJournalXLState();
}

class _NsJournalXLState extends State<NsJournalXL> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _entryController = TextEditingController();
  final _hoursController = TextEditingController(text: '0.5');
  String _selectedCategory = 'Health';
  bool _isLoading = false;
  late UserController2 _userController;

  @override
  void initState() {
    super.initState();
    _userController = Provider.of<UserController2>(context, listen: false);
    if (widget.existingLog != null) {
      _titleController.text = widget.existingLog!.title;
      _entryController.text = widget.existingLog!.entryText;
      _hoursController.text = widget.existingLog!.hours.toString();
      _selectedCategory = widget.existingLog!.category;
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _entryController.dispose();
    _hoursController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final user = Provider.of<UserController2>(context).user;
    if (user == null || user.northStarQuest == null) {
      return const Scaffold(
        backgroundColor: Colors.black,
        body: Center(
          child: Text(
            'No North Star quest found.',
            style: TextStyle(
              color: Colors.white54,
              fontFamily: 'Bitsumishi',
            ),
          ),
        ),
      );
    }

    final quest = user.northStarQuest!;
    final glowColor = quest.glowColor;

    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        title: Text(
          widget.existingLog != null ? 'Edit Entry' : 'New Entry',
          style: TextStyle(
            color: glowColor,
            fontFamily: 'Bitsumishi',
          ),
        ),
        backgroundColor: Colors.grey[900],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Category Dropdown
              DropdownButtonFormField<String>(
                value: _selectedCategory,
                items: [
                  'Health',
                  'Wealth',
                  'Purpose',
                  'Growth',
                  'Relationships',
                ].map((cat) => DropdownMenuItem(
                  value: cat,
                  child: Text(
                    cat,
                    style: const TextStyle(
                      fontFamily: 'Bitsumishi',
                    ),
                  ),
                )).toList(),
                onChanged: (val) {
                  setState(() {
                    _selectedCategory = val!;
                  });
                },
                decoration: InputDecoration(
                  labelText: 'Category',
                  labelStyle: TextStyle(color: glowColor),
                  enabledBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey[800]!),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: glowColor),
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                dropdownColor: Colors.grey[900],
                style: const TextStyle(
                  color: Colors.white,
                  fontFamily: 'Bitsumishi',
                ),
              ),
              const SizedBox(height: 24),

              // Entry Text
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 8),
                child: TextField(
                  controller: _entryController,
                  maxLines: 4,
                  style: const TextStyle(
                    color: Colors.white,
                    fontFamily: 'Bitsumishi',
                  ),
                  decoration: InputDecoration(
                    labelText: 'Entry',
                    labelStyle: TextStyle(color: glowColor),
                    enabledBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: Colors.grey[800]!),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: glowColor),
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 24),

              // Hours Slider
              Container(
                padding: const EdgeInsets.symmetric(vertical: 8),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Hours: ${_hoursController.text}',
                      style: const TextStyle(
                        fontFamily: 'Digital-7',
                        color: Colors.white,
                        fontSize: 20,
                      ),
                    ),
                  ],
                ),
              ),
              Slider(
                value: double.parse(_hoursController.text),
                min: 0.5,
                max: 4.0,
                divisions: 7,
                label: '${double.parse(_hoursController.text).toStringAsFixed(1)}h',
                activeColor: glowColor,
                inactiveColor: Colors.white30,
                onChanged: (val) {
                  setState(() {
                    _hoursController.text = val.toString();
                  });
                },
              ),
              const SizedBox(height: 24),

              // Submit Button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: glowColor,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  onPressed: (_entryController.text.trim().isEmpty ||
                             _isLoading)
                      ? null
                      : _handleSubmit,
                  child: _isLoading
                      ? const SizedBox(
                          width: 24,
                          height: 24,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.black),
                          ),
                        )
                      : StrokeText(
                          text: widget.existingLog != null ? 'Update Entry' : 'Submit Entry',
                          textStyle: const TextStyle(
                            color: Colors.white,
                            fontFamily: 'Bitsumishi',
                            fontSize: 18,
                          ),
                          strokeWidth: 1.0,
                          strokeColor: Colors.black,
                        ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _handleSubmit() async {
    setState(() => _isLoading = true);
    final userCtrl = _userController;
    final user = userCtrl.user;
    if (user == null) return;

    final String category = _selectedCategory;
    final String rawNote = _entryController.text.trim();
    final double hours = double.parse(_hoursController.text);

    try {
      if (widget.existingLog != null) {
        // Handle editing existing logs - currently shows success message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Success! Updated log for $category',
                style: const TextStyle(
                  fontFamily: 'Bitsumishi',
                ),
              ),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        // Create new log
        await SuperEntryService().logNorthStar(
          userController: userCtrl,
          hours: hours,
          category: category,
          note: rawNote,
        );

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Success! +${(hours * 10).round()} EXP → $category',
                style: const TextStyle(
                  fontFamily: 'Bitsumishi',
                ),
              ),
              backgroundColor: Colors.green,
            ),
          );
        }
      }

      if (mounted) {
        // Safe navigation - just pop this dialog
        Navigator.of(context).pop();
        widget.onCompleted?.call();
      }
    } catch (e) {
      rethrow;
    } finally {
      if (mounted) setState(() => _isLoading = false);
    }
  }
}

class ElectricAura extends StatefulWidget {
  final Widget child;
  final Color color;

  const ElectricAura({
    super.key,
    required this.child,
    required this.color,
  });

  @override
  ElectricAuraState createState() => ElectricAuraState();
}

class ElectricAuraState extends State<ElectricAura> {
  late Timer _timer;
  double _blur = 30.0;
  double _spread = 10.0;
  final Random _rand = Random();

  @override
  void initState() {
    super.initState();
    _timer = Timer.periodic(const Duration(milliseconds: 100), (_) {
      setState(() {
        _blur = 20 + _rand.nextDouble() * 20;
        _spread = 5 + _rand.nextDouble() * 10;
      });
    });
  }

  @override
  void dispose() {
    _timer.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: widget.color.withValues(alpha: 0.8),
            blurRadius: _blur,
            spreadRadius: _spread,
          ),
        ],
      ),
      child: widget.child,
    );
  }
}
