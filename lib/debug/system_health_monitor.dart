import 'dart:async';
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/debug_logger.dart';
import 'enterprise_debug_system.dart';

/// Advanced system health monitor for proactive issue detection
/// 
/// Features:
/// - Continuous health monitoring
/// - Predictive failure detection
/// - Automated issue reporting
/// - Performance degradation alerts
/// - Resource usage monitoring
/// - System stability tracking
/// - Recovery recommendations
/// - Health trend analysis
class SystemHealthMonitor {
  static final SystemHealthMonitor _instance = SystemHealthMonitor._internal();
  factory SystemHealthMonitor() => _instance;
  SystemHealthMonitor._internal();

  // Monitoring state
  Timer? _healthCheckTimer;
  bool _isMonitoring = false;
  
  // Health metrics
  final Map<String, HealthMetric> _healthMetrics = {};
  final List<HealthAlert> _activeAlerts = [];
  final List<HealthTrend> _healthTrends = [];
  
  // Thresholds and configuration
  static const Map<String, double> _healthThresholds = {
    'error_rate': 0.05, // 5% error rate
    'response_time': 3000, // 3 seconds
    'memory_growth': 0.2, // 20% memory growth
    'network_failure': 0.1, // 10% network failure rate
    'crash_rate': 0.01, // 1% crash rate
  };
  
  // System components to monitor
  final List<String> _monitoredComponents = [
    'ui_rendering',
    'data_loading',
    'network_requests',
    'database_operations',
    'image_processing',
    'notification_system',
    'authentication',
    'storage_operations',
  ];

  /// Initialize the health monitoring system
  Future<bool> initialize() async {
    try {
      DebugLogger.log('SystemHealthMonitor', '🏥 Initializing system health monitor...');
      
      // Initialize health metrics for all components
      for (final component in _monitoredComponents) {
        _healthMetrics[component] = HealthMetric(
          componentName: component,
          status: HealthStatus.unknown,
          score: 0.0,
          lastCheck: DateTime.now(),
        );
      }
      
      // Start continuous monitoring
      _startHealthMonitoring();
      
      // Load historical health data
      await _loadHealthHistory();
      
      DebugLogger.log('SystemHealthMonitor', '✅ System health monitor initialized');
      return true;
      
    } catch (e, stackTrace) {
      DebugLogger.error('SystemHealthMonitor', 'Failed to initialize health monitor', e, stackTrace);
      return false;
    }
  }

  /// Start continuous health monitoring
  void _startHealthMonitoring() {
    if (_isMonitoring) return;
    
    _isMonitoring = true;
    _healthCheckTimer?.cancel();
    
    // Perform health checks every 2 minutes
    _healthCheckTimer = Timer.periodic(const Duration(minutes: 2), (timer) {
      _performComprehensiveHealthCheck();
    });
    
    DebugLogger.log('SystemHealthMonitor', '🔄 Continuous health monitoring started');
  }

  /// Perform comprehensive health check across all system components
  Future<void> _performComprehensiveHealthCheck() async {
    try {
      DebugLogger.log('SystemHealthMonitor', '🔍 Performing comprehensive health check...');
      
      final debugSystem = EnterpriseDebugSystem();
      final debugReport = debugSystem.getDebugReport();
      
      // Check each monitored component
      for (final component in _monitoredComponents) {
        await _checkComponentHealth(component, debugReport);
      }
      
      // Analyze overall system health
      _analyzeSystemHealth();
      
      // Check for health trends
      _analyzeHealthTrends();
      
      // Generate alerts if needed
      _generateHealthAlerts();
      
      // Save health data
      await _saveHealthData();
      
      DebugLogger.log('SystemHealthMonitor', '✅ Health check completed');
      
    } catch (e, stackTrace) {
      DebugLogger.error('SystemHealthMonitor', 'Error during health check', e, stackTrace);
    }
  }

  /// Check health of a specific system component
  Future<void> _checkComponentHealth(String component, Map<String, dynamic> debugReport) async {
    try {
      final metric = _healthMetrics[component]!;
      double healthScore = 1.0; // Start with perfect health
      final issues = <String>[];
      
      // Check performance metrics
      final performanceMetrics = debugReport['performance_metrics'] as Map<String, dynamic>? ?? {};
      if (performanceMetrics.containsKey(component)) {
        final componentMetrics = performanceMetrics[component] as Map<String, dynamic>;
        final averageTime = componentMetrics['average'] as double? ?? 0.0;
        
        if (averageTime > _healthThresholds['response_time']!) {
          healthScore -= 0.3;
          issues.add('Slow response time: ${averageTime.toStringAsFixed(1)}ms');
        }
      }
      
      // Check error rates
      final errorCounts = debugReport['error_counts'] as Map<String, dynamic>? ?? {};
      final componentErrors = errorCounts.entries
          .where((e) => e.key.contains(component))
          .fold(0, (sum, e) => sum + (e.value as int));
      
      if (componentErrors > 5) {
        healthScore -= 0.4;
        issues.add('High error count: $componentErrors errors');
      }
      
      // Check network-specific metrics
      if (component == 'network_requests') {
        final networkStats = debugReport['network_stats'] as Map<String, dynamic>? ?? {};
        final failureRate = networkStats['failure_rate'] as double? ?? 0.0;
        
        if (failureRate > _healthThresholds['network_failure']!) {
          healthScore -= 0.5;
          issues.add('High network failure rate: ${(failureRate * 100).toStringAsFixed(1)}%');
        }
      }
      
      // Determine health status
      HealthStatus status;
      if (healthScore >= 0.9) {
        status = HealthStatus.excellent;
      } else if (healthScore >= 0.7) {
        status = HealthStatus.good;
      } else if (healthScore >= 0.5) {
        status = HealthStatus.warning;
      } else {
        status = HealthStatus.critical;
      }
      
      // Update health metric
      metric.status = status;
      metric.score = healthScore;
      metric.lastCheck = DateTime.now();
      metric.issues = issues;
      
      DebugLogger.log('SystemHealthMonitor', 
        '📊 $component health: ${status.name} (${(healthScore * 100).toStringAsFixed(1)}%)');
      
    } catch (e, stackTrace) {
      DebugLogger.error('SystemHealthMonitor', 'Error checking $component health', e, stackTrace);
    }
  }

  /// Analyze overall system health
  void _analyzeSystemHealth() {
    final healthScores = _healthMetrics.values.map((m) => m.score).toList();
    final averageHealth = healthScores.isNotEmpty 
        ? healthScores.reduce((a, b) => a + b) / healthScores.length 
        : 0.0;
    
    final criticalComponents = _healthMetrics.values
        .where((m) => m.status == HealthStatus.critical)
        .length;
    
    final warningComponents = _healthMetrics.values
        .where((m) => m.status == HealthStatus.warning)
        .length;
    
    DebugLogger.log('SystemHealthMonitor', 
      '🏥 System health: ${(averageHealth * 100).toStringAsFixed(1)}% '
      '(Critical: $criticalComponents, Warning: $warningComponents)');
  }

  /// Analyze health trends over time
  void _analyzeHealthTrends() {
    final now = DateTime.now();
    
    for (final component in _monitoredComponents) {
      final metric = _healthMetrics[component]!;
      
      // Add current health score to trends
      _healthTrends.add(HealthTrend(
        componentName: component,
        healthScore: metric.score,
        timestamp: now,
      ));
    }
    
    // Keep only last 100 trend points per component
    final cutoffTime = now.subtract(const Duration(hours: 24));
    _healthTrends.removeWhere((trend) => trend.timestamp.isBefore(cutoffTime));
    
    // Analyze trends for each component
    for (final component in _monitoredComponents) {
      final componentTrends = _healthTrends
          .where((t) => t.componentName == component)
          .toList()
        ..sort((a, b) => a.timestamp.compareTo(b.timestamp));
      
      if (componentTrends.length >= 5) {
        final recent = componentTrends.skip(componentTrends.length - 3).map((t) => t.healthScore).toList();
        final older = componentTrends.skip(componentTrends.length - 6).take(3).map((t) => t.healthScore).toList();
        
        final recentAvg = recent.reduce((a, b) => a + b) / recent.length;
        final olderAvg = older.reduce((a, b) => a + b) / older.length;
        
        if (recentAvg < olderAvg - 0.2) { // 20% decline
          _addHealthAlert(HealthAlert(
            type: HealthAlertType.degradation,
            component: component,
            message: 'Health degradation detected: ${((olderAvg - recentAvg) * 100).toStringAsFixed(1)}% decline',
            severity: AlertSeverity.warning,
            timestamp: now,
          ));
        }
      }
    }
  }

  /// Generate health alerts based on current status
  void _generateHealthAlerts() {
    final now = DateTime.now();
    
    for (final metric in _healthMetrics.values) {
      if (metric.status == HealthStatus.critical) {
        _addHealthAlert(HealthAlert(
          type: HealthAlertType.critical,
          component: metric.componentName,
          message: 'Critical health status: ${metric.issues.join(", ")}',
          severity: AlertSeverity.critical,
          timestamp: now,
        ));
      } else if (metric.status == HealthStatus.warning && metric.score < 0.6) {
        _addHealthAlert(HealthAlert(
          type: HealthAlertType.warning,
          component: metric.componentName,
          message: 'Warning: ${metric.issues.join(", ")}',
          severity: AlertSeverity.warning,
          timestamp: now,
        ));
      }
    }
  }

  /// Add a health alert
  void _addHealthAlert(HealthAlert alert) {
    // Check if similar alert already exists
    final existingAlert = _activeAlerts.firstWhere(
      (a) => a.component == alert.component && a.type == alert.type,
      orElse: () => HealthAlert(
        type: HealthAlertType.info,
        component: '',
        message: '',
        severity: AlertSeverity.info,
        timestamp: DateTime.now(),
      ),
    );
    
    if (existingAlert.component.isEmpty) {
      _activeAlerts.add(alert);
      DebugLogger.log('SystemHealthMonitor', 
        '🚨 Health Alert: ${alert.component} - ${alert.message}');
    }
    
    // Keep only last 50 alerts
    if (_activeAlerts.length > 50) {
      _activeAlerts.removeAt(0);
    }
  }

  /// Get current system health status
  SystemHealthStatus getSystemHealthStatus() {
    final healthScores = _healthMetrics.values.map((m) => m.score).toList();
    final averageHealth = healthScores.isNotEmpty 
        ? healthScores.reduce((a, b) => a + b) / healthScores.length 
        : 0.0;
    
    final criticalComponents = _healthMetrics.values
        .where((m) => m.status == HealthStatus.critical)
        .map((m) => m.componentName)
        .toList();
    
    final warningComponents = _healthMetrics.values
        .where((m) => m.status == HealthStatus.warning)
        .map((m) => m.componentName)
        .toList();
    
    return SystemHealthStatus(
      overallHealth: averageHealth,
      componentMetrics: Map.from(_healthMetrics),
      activeAlerts: List.from(_activeAlerts),
      criticalComponents: criticalComponents,
      warningComponents: warningComponents,
      lastCheck: DateTime.now(),
    );
  }

  /// Get health recommendations
  List<String> getHealthRecommendations() {
    final recommendations = <String>[];
    
    for (final metric in _healthMetrics.values) {
      if (metric.status == HealthStatus.critical) {
        recommendations.add('🔴 ${metric.componentName}: Immediate attention required - ${metric.issues.join(", ")}');
      } else if (metric.status == HealthStatus.warning) {
        recommendations.add('🟡 ${metric.componentName}: Monitor closely - ${metric.issues.join(", ")}');
      }
    }
    
    if (recommendations.isEmpty) {
      recommendations.add('✅ All systems operating normally');
    }
    
    return recommendations;
  }

  /// Load historical health data
  Future<void> _loadHealthHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final healthData = prefs.getString('system_health_history');
      
      if (healthData != null) {
        final data = jsonDecode(healthData) as Map<String, dynamic>;
        // Load health trends and other historical data
        DebugLogger.log('SystemHealthMonitor', '📚 Loaded health history: ${data.keys.length} sections');
      }
    } catch (e, stackTrace) {
      DebugLogger.error('SystemHealthMonitor', 'Error loading health history', e, stackTrace);
    }
  }

  /// Save health data
  Future<void> _saveHealthData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final healthData = {
        'metrics': _healthMetrics.map((k, v) => MapEntry(k, v.toJson())),
        'alerts': _activeAlerts.map((a) => a.toJson()).toList(),
        'trends': _healthTrends.map((t) => t.toJson()).toList(),
        'last_save': DateTime.now().toIso8601String(),
      };
      
      await prefs.setString('system_health_history', jsonEncode(healthData));
    } catch (e, stackTrace) {
      DebugLogger.error('SystemHealthMonitor', 'Error saving health data', e, stackTrace);
    }
  }

  /// Dispose of the health monitor
  void dispose() {
    _healthCheckTimer?.cancel();
    _isMonitoring = false;
    DebugLogger.log('SystemHealthMonitor', '🛑 System health monitor disposed');
  }
}

/// Health metric for a system component
class HealthMetric {
  String componentName;
  HealthStatus status;
  double score;
  DateTime lastCheck;
  List<String> issues;
  
  HealthMetric({
    required this.componentName,
    required this.status,
    required this.score,
    required this.lastCheck,
    this.issues = const [],
  });
  
  Map<String, dynamic> toJson() => {
    'componentName': componentName,
    'status': status.name,
    'score': score,
    'lastCheck': lastCheck.toIso8601String(),
    'issues': issues,
  };
}

/// Health trend data point
class HealthTrend {
  final String componentName;
  final double healthScore;
  final DateTime timestamp;
  
  HealthTrend({
    required this.componentName,
    required this.healthScore,
    required this.timestamp,
  });
  
  Map<String, dynamic> toJson() => {
    'componentName': componentName,
    'healthScore': healthScore,
    'timestamp': timestamp.toIso8601String(),
  };
}

/// Health alert
class HealthAlert {
  final HealthAlertType type;
  final String component;
  final String message;
  final AlertSeverity severity;
  final DateTime timestamp;
  
  HealthAlert({
    required this.type,
    required this.component,
    required this.message,
    required this.severity,
    required this.timestamp,
  });
  
  Map<String, dynamic> toJson() => {
    'type': type.name,
    'component': component,
    'message': message,
    'severity': severity.name,
    'timestamp': timestamp.toIso8601String(),
  };
}

/// System health status
class SystemHealthStatus {
  final double overallHealth;
  final Map<String, HealthMetric> componentMetrics;
  final List<HealthAlert> activeAlerts;
  final List<String> criticalComponents;
  final List<String> warningComponents;
  final DateTime lastCheck;
  
  SystemHealthStatus({
    required this.overallHealth,
    required this.componentMetrics,
    required this.activeAlerts,
    required this.criticalComponents,
    required this.warningComponents,
    required this.lastCheck,
  });
}

/// Health status levels
enum HealthStatus { excellent, good, warning, critical, unknown }

/// Health alert types
enum HealthAlertType { critical, warning, degradation, recovery, info }

/// Alert severity levels
enum AlertSeverity { info, warning, critical }
