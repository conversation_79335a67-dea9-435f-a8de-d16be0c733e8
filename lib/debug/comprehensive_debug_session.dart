// 📁 lib/debug/comprehensive_debug_session.dart

import 'package:flutter/foundation.dart';
import '../models/user_model.dart';
import '../services/coach_orchestration_service.dart';
import '../services/coach_context_service.dart';
import '../services/model_routing_service.dart';
import '../services/coach_safety_service.dart';
import '../services/coach_analytics_service.dart';


/// Comprehensive debugging session to validate the entire coach system
/// before deployment to thousands of users.
class ComprehensiveDebugSession {
  static bool _isRunning = false;
  static final List<String> _debugLog = [];

  /// Run complete system validation
  static Future<DebugSessionResult> runCompleteValidation({
    required User testUser,
    bool verboseLogging = true,
  }) async {
    if (_isRunning) {
      throw Exception('Debug session already running');
    }

    _isRunning = true;
    _debugLog.clear();
    
    try {
      _log('🚀 STARTING COMPREHENSIVE DEBUG SESSION');
      _log('👤 Test User: ${testUser.username} (${testUser.gender})');
      _log('📊 Categories: ${testUser.categories.keys.join(', ')}');
      _log('📝 Diary Entries: ${testUser.diaryEntries.length}');
      _log('🎯 North Star: ${testUser.northStarQuest?.title ?? 'None'}');
      _log('💪 Habits: ${testUser.dailyHabits.length}');
      _log('');

      final results = <String, dynamic>{};
      final stopwatch = Stopwatch()..start();

      // 1. Test Core Coach Functionality
      _log('🧪 PHASE 1: CORE COACH FUNCTIONALITY');
      results['core_functionality'] = await _testCoreFunctionality(testUser);
      
      // 2. Test Context System
      _log('🧪 PHASE 2: CONTEXT SYSTEM VALIDATION');
      results['context_system'] = await _testContextSystem(testUser);
      
      // 3. Test Model Routing & Limits
      _log('🧪 PHASE 3: MODEL ROUTING & LIMITS');
      results['model_routing'] = await _testModelRouting(testUser);
      
      // 4. Test Safety Systems
      _log('🧪 PHASE 4: SAFETY SYSTEMS');
      results['safety_systems'] = await _testSafetySystems(testUser);
      
      // 5. Test Analytics Integration
      _log('🧪 PHASE 5: ANALYTICS INTEGRATION');
      results['analytics'] = await _testAnalytics(testUser);
      
      // 6. Test Edge Cases
      _log('🧪 PHASE 6: EDGE CASES & ERROR HANDLING');
      results['edge_cases'] = await _testEdgeCases(testUser);
      
      // 7. Performance Validation
      _log('🧪 PHASE 7: PERFORMANCE VALIDATION');
      results['performance'] = await _testPerformance(testUser);

      stopwatch.stop();
      
      final summary = _generateSummary(results, stopwatch.elapsedMilliseconds);
      _log('');
      _log('✅ DEBUG SESSION COMPLETE');
      _log('⏱️ Total Time: ${stopwatch.elapsedMilliseconds}ms');
      _log('📊 Overall Success: ${summary.overallSuccess ? 'PASS' : 'FAIL'}');
      
      return summary;

    } catch (e, stackTrace) {
      _log('❌ CRITICAL ERROR: $e');
      if (kDebugMode) print('Stack trace: $stackTrace');
      rethrow;
    } finally {
      _isRunning = false;
    }
  }

  /// Test all coach categories with real conversations
  static Future<Map<String, dynamic>> _testCoreFunctionality(User user) async {
    final results = <String, dynamic>{};
    final testMessages = [
      'Hello, I need some advice about improving my life.',
      'I\'m feeling stuck and need motivation.',
      'What should I focus on this week?',
      'How can I build better habits?',
    ];

    // Test main categories
    final categories = ['Health', 'Wealth', 'Purpose', 'Connection'];
    
    for (final category in categories) {
      _log('  Testing $category coach...');
      
      try {
        final response = await CoachOrchestrationService.generateSuperintelligentResponse(
          category: category,
          userPrompt: testMessages[0],
          user: user,
        );
        
        final success = response.isNotEmpty && 
                       !response.toLowerCase().contains('error') &&
                       response.length > 50; // Meaningful response
        
        results[category.toLowerCase()] = {
          'success': success,
          'response_length': response.length,
          'response_preview': response.length > 100 
              ? '${response.substring(0, 100)}...' 
              : response,
        };
        
        _log('    ${success ? '✅' : '❌'} $category: ${response.length} chars');
        
      } catch (e) {
        results[category.toLowerCase()] = {
          'success': false,
          'error': e.toString(),
        };
        _log('    ❌ $category: ERROR - $e');
      }
    }

    // Test custom categories if available
    for (final customCategory in user.customCategories) {
      _log('  Testing custom category: $customCategory...');
      
      try {
        final response = await CoachOrchestrationService.generateSuperintelligentResponse(
          category: customCategory,
          userPrompt: 'Help me improve my $customCategory skills.',
          user: user,
        );
        
        final success = response.isNotEmpty && !response.toLowerCase().contains('error');
        
        results['custom_$customCategory'] = {
          'success': success,
          'response_length': response.length,
        };
        
        _log('    ${success ? '✅' : '❌'} $customCategory: ${response.length} chars');
        
      } catch (e) {
        results['custom_$customCategory'] = {
          'success': false,
          'error': e.toString(),
        };
        _log('    ❌ $customCategory: ERROR - $e');
      }
    }

    final successCount = results.values.where((r) => r['success'] == true).length;
    final totalTests = results.length;
    
    _log('  📊 Core Functionality: $successCount/$totalTests passed');
    
    return {
      'tests': results,
      'success_rate': totalTests > 0 ? successCount / totalTests : 0.0,
      'total_tests': totalTests,
      'passed_tests': successCount,
    };
  }

  /// Test context system accuracy and performance
  static Future<Map<String, dynamic>> _testContextSystem(User user) async {
    _log('  Testing context generation...');
    
    try {
      final stopwatch = Stopwatch()..start();
      final context = await CoachContextService.getUserContext(user);
      stopwatch.stop();
      
      // Validate context structure
      final requiredKeys = [
        'user_profile', 'progress_data', 'diary_feed', 
        'north_star_quest', 'habits_data', 'recent_activity'
      ];
      
      final missingKeys = requiredKeys.where((key) => !context.containsKey(key)).toList();
      final contextSize = context.toString().length;
      
      final success = missingKeys.isEmpty && contextSize > 500 && contextSize < 100000;
      
      _log('    ${success ? '✅' : '❌'} Context: $contextSize chars, ${stopwatch.elapsedMilliseconds}ms');
      if (missingKeys.isNotEmpty) {
        _log('    ⚠️ Missing keys: ${missingKeys.join(', ')}');
      }
      
      return {
        'success': success,
        'context_size': contextSize,
        'generation_time_ms': stopwatch.elapsedMilliseconds,
        'missing_keys': missingKeys,
        'has_user_data': context['user_profile']?['basic_info']?['username'] == user.username,
      };
      
    } catch (e) {
      _log('    ❌ Context: ERROR - $e');
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  /// Test model routing and usage limits
  static Future<Map<String, dynamic>> _testModelRouting(User user) async {
    _log('  Testing model routing...');
    
    try {
      // Test normal routing
      final routing = await ModelRoutingService.routeMessage(
        userId: user.id,
        category: 'Health',
        message: 'Test message for routing',
        user: user,
      );
      
      // Get usage stats
      final stats = await ModelRoutingService.getUserStats(user.id);
      
      _log('    ${routing.success ? '✅' : '❌'} Routing: ${routing.selectedModel ?? 'None'}');
      _log('    📊 Usage: ${stats.dailyMessageCount} messages, \$${stats.totalCost.toStringAsFixed(4)} cost');
      
      return {
        'routing_success': routing.success,
        'selected_model': routing.selectedModel,
        'limit_exceeded': routing.limitExceeded,
        'daily_messages': stats.dailyMessageCount,
        'total_cost': stats.totalCost,
        'message': routing.message,
      };
      
    } catch (e) {
      _log('    ❌ Routing: ERROR - $e');
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  /// Test safety systems
  static Future<Map<String, dynamic>> _testSafetySystems(User user) async {
    _log('  Testing safety systems...');
    
    try {
      // Test safety check
      final safetyCheck = await CoachSafetyService.checkRequestSafety(
        userId: user.id,
        category: 'Health',
        operation: 'test_operation',
      );
      
      // Get safety metrics
      final metrics = CoachSafetyService.getSafetyMetrics();
      
      _log('    ${safetyCheck.allowed ? '✅' : '❌'} Safety: ${safetyCheck.reason}');
      _log('    🛡️ Circuit: ${metrics.circuitState.name}, Success Rate: ${(metrics.successRate * 100).toStringAsFixed(1)}%');
      
      return {
        'safety_check_passed': safetyCheck.allowed,
        'circuit_state': metrics.circuitState.name,
        'success_rate': metrics.successRate,
        'total_requests': metrics.totalRequests,
        'blocked_requests': metrics.blockedRequests,
      };
      
    } catch (e) {
      _log('    ❌ Safety: ERROR - $e');
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  /// Test analytics integration
  static Future<Map<String, dynamic>> _testAnalytics(User user) async {
    _log('  Testing analytics...');
    
    try {
      // Get dashboard data
      final dashboard = await CoachAnalyticsService.getDashboardData();
      
      _log('    ✅ Analytics: ${dashboard.totalInteractions} interactions tracked');
      
      return {
        'success': true,
        'total_interactions': dashboard.totalInteractions,
        'success_rate': dashboard.successRate,
        'average_response_time': dashboard.averageResponseTime,
        'total_cost': dashboard.totalCost,
      };
      
    } catch (e) {
      _log('    ❌ Analytics: ERROR - $e');
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  /// Test edge cases and error handling
  static Future<Map<String, dynamic>> _testEdgeCases(User user) async {
    _log('  Testing edge cases...');
    
    final edgeCases = [
      {'name': 'Empty Message', 'message': ''},
      {'name': 'Very Long Message', 'message': 'A' * 5000},
      {'name': 'Special Characters', 'message': '🎯💪🔥 @#\$%^&*()'},
      {'name': 'Invalid Category', 'message': 'Test message'},
    ];
    
    final results = <String, dynamic>{};
    
    for (final testCase in edgeCases) {
      try {
        final response = await CoachOrchestrationService.generateSuperintelligentResponse(
          category: testCase['name'] == 'Invalid Category' ? 'InvalidCategory' : 'Health',
          userPrompt: testCase['message']!,
          user: user,
        );
        
        final success = response.isNotEmpty;
        results[testCase['name']!] = {
          'success': success,
          'response_length': response.length,
        };
        
        _log('    ${success ? '✅' : '❌'} ${testCase['name']}: ${response.length} chars');
        
      } catch (e) {
        results[testCase['name']!] = {
          'success': false,
          'error': e.toString(),
        };
        _log('    ❌ ${testCase['name']}: ERROR - $e');
      }
    }
    
    final successCount = results.values.where((r) => r['success'] == true).length;
    
    return {
      'tests': results,
      'success_rate': successCount / edgeCases.length,
      'total_tests': edgeCases.length,
      'passed_tests': successCount,
    };
  }

  /// Test performance under various conditions
  static Future<Map<String, dynamic>> _testPerformance(User user) async {
    _log('  Testing performance...');
    
    final responseTimes = <int>[];
    const testCount = 3; // Reduced for faster testing
    
    for (int i = 0; i < testCount; i++) {
      try {
        final stopwatch = Stopwatch()..start();
        
        await CoachOrchestrationService.generateSuperintelligentResponse(
          category: 'Health',
          userPrompt: 'Performance test message $i',
          user: user,
        );
        
        stopwatch.stop();
        responseTimes.add(stopwatch.elapsedMilliseconds);
        
        _log('    ⏱️ Test ${i + 1}: ${stopwatch.elapsedMilliseconds}ms');
        
      } catch (e) {
        _log('    ❌ Test ${i + 1}: ERROR - $e');
      }
    }
    
    if (responseTimes.isNotEmpty) {
      final avgTime = responseTimes.reduce((a, b) => a + b) / responseTimes.length;
      final maxTime = responseTimes.reduce((a, b) => a > b ? a : b);
      final minTime = responseTimes.reduce((a, b) => a < b ? a : b);
      
      final success = avgTime < 30000 && maxTime < 45000; // 30s avg, 45s max
      
      _log('    ${success ? '✅' : '❌'} Performance: ${avgTime.toStringAsFixed(0)}ms avg');
      
      return {
        'success': success,
        'average_time_ms': avgTime,
        'max_time_ms': maxTime,
        'min_time_ms': minTime,
        'response_times': responseTimes,
      };
    }
    
    return {
      'success': false,
      'error': 'No successful performance tests',
    };
  }

  /// Generate comprehensive summary
  static DebugSessionResult _generateSummary(Map<String, dynamic> results, int totalTimeMs) {
    final phases = results.keys.toList();
    final successfulPhases = phases.where((phase) {
      final result = results[phase];
      return result is Map && (result['success'] == true || (result['success_rate'] ?? 0.0) > 0.8);
    }).length;
    
    final overallSuccess = successfulPhases >= (phases.length * 0.8); // 80% success threshold
    
    return DebugSessionResult(
      overallSuccess: overallSuccess,
      totalPhases: phases.length,
      successfulPhases: successfulPhases,
      totalTimeMs: totalTimeMs,
      results: results,
      debugLog: List.from(_debugLog),
      timestamp: DateTime.now(),
    );
  }

  static void _log(String message) {
    _debugLog.add('${DateTime.now().toIso8601String()}: $message');
    if (kDebugMode) print(message);
  }

  /// Get debug log
  static List<String> getDebugLog() => List.from(_debugLog);
}

/// Result of a comprehensive debug session
class DebugSessionResult {
  final bool overallSuccess;
  final int totalPhases;
  final int successfulPhases;
  final int totalTimeMs;
  final Map<String, dynamic> results;
  final List<String> debugLog;
  final DateTime timestamp;

  DebugSessionResult({
    required this.overallSuccess,
    required this.totalPhases,
    required this.successfulPhases,
    required this.totalTimeMs,
    required this.results,
    required this.debugLog,
    required this.timestamp,
  });

  double get successRate => totalPhases > 0 ? successfulPhases / totalPhases : 0.0;
}
