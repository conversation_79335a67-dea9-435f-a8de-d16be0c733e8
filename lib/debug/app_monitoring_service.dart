import 'dart:async';
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/debug_logger.dart';
import 'enterprise_debug_system.dart';
import 'system_health_monitor.dart';
import 'bulletproof_error_handler.dart';

/// Comprehensive app monitoring service that orchestrates all debug systems
/// 
/// Features:
/// - Centralized monitoring coordination
/// - Automated health checks and alerts
/// - Performance trend analysis
/// - Predictive failure detection
/// - Automated recovery coordination
/// - Real-time dashboard updates
/// - Alert escalation management
/// - System optimization recommendations
/// - Comprehensive reporting
/// - Remote monitoring capabilities
class AppMonitoringService {
  static final AppMonitoringService _instance = AppMonitoringService._internal();
  factory AppMonitoringService() => _instance;
  AppMonitoringService._internal();

  // Monitoring systems
  final EnterpriseDebugSystem _debugSystem = EnterpriseDebugSystem();
  final SystemHealthMonitor _healthMonitor = SystemHealthMonitor();
  final BulletproofErrorHandler _errorHandler = BulletproofErrorHandler();
  
  // Monitoring state
  bool _isInitialized = false;
  Timer? _monitoringTimer;
  Timer? _alertTimer;
  
  // Alert management
  final List<MonitoringAlert> _activeAlerts = [];
  final Map<String, DateTime> _lastAlertTimes = {};
  
  // Performance tracking
  final Map<String, List<double>> _performanceTrends = {};
  final Map<String, double> _baselineMetrics = {};
  
  // Configuration
  static const Duration monitoringInterval = Duration(minutes: 1);
  static const Duration alertCooldown = Duration(minutes: 5);
  static const int maxActiveAlerts = 50;

  /// Initialize the comprehensive monitoring service
  Future<bool> initialize() async {
    if (_isInitialized) return true;
    
    try {
      DebugLogger.log('AppMonitoringService', '🚀 Initializing comprehensive app monitoring...');
      
      // Initialize all monitoring systems
      final debugInit = await _debugSystem.initialize();
      final healthInit = await _healthMonitor.initialize();
      final errorInit = await _errorHandler.initialize();
      
      if (!debugInit || !healthInit || !errorInit) {
        DebugLogger.log('AppMonitoringService', '❌ Failed to initialize some monitoring systems');
        return false;
      }
      
      // Start coordinated monitoring
      _startCoordinatedMonitoring();
      
      // Setup alert management
      _setupAlertManagement();
      
      // Initialize performance baselines
      await _initializePerformanceBaselines();
      
      // Load historical monitoring data
      await _loadMonitoringHistory();
      
      _isInitialized = true;
      DebugLogger.log('AppMonitoringService', '✅ Comprehensive app monitoring initialized');
      
      // Send initialization success alert
      _addAlert(MonitoringAlert(
        type: AlertType.info,
        severity: AlertSeverity.low,
        title: 'Monitoring System Online',
        message: 'All monitoring systems initialized successfully',
        source: 'AppMonitoringService',
        timestamp: DateTime.now(),
      ));
      
      return true;
      
    } catch (e, stackTrace) {
      DebugLogger.error('AppMonitoringService', 'Failed to initialize monitoring service', e, stackTrace);
      return false;
    }
  }

  /// Start coordinated monitoring across all systems
  void _startCoordinatedMonitoring() {
    _monitoringTimer?.cancel();
    _monitoringTimer = Timer.periodic(monitoringInterval, (timer) {
      _performCoordinatedHealthCheck();
    });
    
    DebugLogger.log('AppMonitoringService', '🔄 Coordinated monitoring started');
  }

  /// Setup comprehensive alert management
  void _setupAlertManagement() {
    _alertTimer?.cancel();
    _alertTimer = Timer.periodic(const Duration(minutes: 2), (timer) {
      _processAlerts();
      _escalateAlertsIfNeeded();
      _cleanupOldAlerts();
    });
    
    DebugLogger.log('AppMonitoringService', '🚨 Alert management configured');
  }

  /// Initialize performance baselines for comparison
  Future<void> _initializePerformanceBaselines() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final baselinesData = prefs.getString('performance_baselines');
      
      if (baselinesData != null) {
        final baselines = jsonDecode(baselinesData) as Map<String, dynamic>;
        _baselineMetrics.addAll(baselines.cast<String, double>());
        DebugLogger.log('AppMonitoringService', '📊 Loaded performance baselines: ${_baselineMetrics.length}');
      } else {
        // Set initial baselines
        _baselineMetrics['response_time'] = 1000.0; // 1 second
        _baselineMetrics['memory_usage'] = 100000.0; // 100MB
        _baselineMetrics['error_rate'] = 0.01; // 1%
        _baselineMetrics['network_failure_rate'] = 0.05; // 5%
        
        await _savePerformanceBaselines();
        DebugLogger.log('AppMonitoringService', '📊 Initialized default performance baselines');
      }
    } catch (e, stackTrace) {
      DebugLogger.error('AppMonitoringService', 'Error initializing baselines', e, stackTrace);
    }
  }

  /// Perform coordinated health check across all systems
  Future<void> _performCoordinatedHealthCheck() async {
    try {
      DebugLogger.log('AppMonitoringService', '🔍 Performing coordinated health check...');
      
      // Gather data from all monitoring systems
      final debugReport = _debugSystem.getDebugReport();
      final healthStatus = _healthMonitor.getSystemHealthStatus();
      final errorReport = _errorHandler.getErrorReport();
      
      // Analyze overall system health
      final overallHealth = _calculateOverallHealth(debugReport, healthStatus, errorReport);
      
      // Check for performance degradation
      _checkPerformanceDegradation(debugReport);
      
      // Check for error pattern escalation
      _checkErrorEscalation(errorReport);
      
      // Check for resource exhaustion
      _checkResourceExhaustion(debugReport);
      
      // Update performance trends
      _updatePerformanceTrends(debugReport);
      
      // Generate predictive alerts
      _generatePredictiveAlerts(overallHealth);
      
      DebugLogger.log('AppMonitoringService', 
        '✅ Health check complete - Overall health: ${(overallHealth * 100).toStringAsFixed(1)}%');
      
    } catch (e, stackTrace) {
      DebugLogger.error('AppMonitoringService', 'Error during health check', e, stackTrace);
      
      _addAlert(MonitoringAlert(
        type: AlertType.error,
        severity: AlertSeverity.high,
        title: 'Health Check Failed',
        message: 'Error during coordinated health check: $e',
        source: 'AppMonitoringService',
        timestamp: DateTime.now(),
      ));
    }
  }

  /// Calculate overall system health score
  double _calculateOverallHealth(
    Map<String, dynamic> debugReport,
    SystemHealthStatus healthStatus,
    Map<String, dynamic> errorReport,
  ) {
    double healthScore = healthStatus.overallHealth;
    
    // Factor in error rates
    final totalErrors = errorReport['system_health']?['total_errors'] ?? 0;
    if (totalErrors > 50) {
      healthScore *= 0.8; // Reduce by 20% for high error count
    }
    
    // Factor in network performance
    final networkStats = debugReport['network_stats'] as Map<String, dynamic>? ?? {};
    final failureRate = networkStats['failure_rate'] as double? ?? 0.0;
    if (failureRate > 0.1) {
      healthScore *= 0.9; // Reduce by 10% for high network failure rate
    }
    
    // Factor in recovery success rate
    final recoveryRate = errorReport['system_health']?['recovery_success_rate'] ?? 1.0;
    healthScore *= recoveryRate;
    
    return healthScore.clamp(0.0, 1.0);
  }

  /// Check for performance degradation
  void _checkPerformanceDegradation(Map<String, dynamic> debugReport) {
    final performanceMetrics = debugReport['performance_metrics'] as Map<String, dynamic>? ?? {};
    
    for (final entry in performanceMetrics.entries) {
      final category = entry.key;
      final metrics = entry.value as Map<String, dynamic>;
      final average = metrics['average'] as double? ?? 0.0;
      
      final baseline = _baselineMetrics['${category}_response_time'] ?? _baselineMetrics['response_time'] ?? 1000.0;
      
      if (average > baseline * 2.0) { // 100% slower than baseline
        _addAlert(MonitoringAlert(
          type: AlertType.performance,
          severity: AlertSeverity.high,
          title: 'Performance Degradation',
          message: '$category response time (${average.toStringAsFixed(1)}ms) is ${((average / baseline - 1) * 100).toStringAsFixed(1)}% slower than baseline',
          source: 'PerformanceMonitor',
          timestamp: DateTime.now(),
        ));
      } else if (average > baseline * 1.5) { // 50% slower than baseline
        _addAlert(MonitoringAlert(
          type: AlertType.performance,
          severity: AlertSeverity.medium,
          title: 'Performance Warning',
          message: '$category response time is elevated (${average.toStringAsFixed(1)}ms vs ${baseline.toStringAsFixed(1)}ms baseline)',
          source: 'PerformanceMonitor',
          timestamp: DateTime.now(),
        ));
      }
    }
  }

  /// Check for error pattern escalation
  void _checkErrorEscalation(Map<String, dynamic> errorReport) {
    final errorPatterns = errorReport['error_patterns'] as Map<String, dynamic>? ?? {};
    final recentErrors = errorReport['recent_errors'] as List? ?? [];
    
    // Check for error spikes
    final now = DateTime.now();
    final lastHourErrors = recentErrors.where((error) {
      final timestamp = DateTime.parse(error['timestamp']);
      return now.difference(timestamp).inHours < 1;
    }).length;
    
    if (lastHourErrors > 20) {
      _addAlert(MonitoringAlert(
        type: AlertType.error,
        severity: AlertSeverity.critical,
        title: 'Error Spike Detected',
        message: '$lastHourErrors errors in the last hour',
        source: 'ErrorMonitor',
        timestamp: DateTime.now(),
      ));
    }
    
    // Check for new error patterns
    for (final entry in errorPatterns.entries) {
      final pattern = entry.value as Map<String, dynamic>;
      final count = pattern['count'] as int? ?? 0;
      
      if (count > 10) {
        _addAlert(MonitoringAlert(
          type: AlertType.error,
          severity: AlertSeverity.medium,
          title: 'Recurring Error Pattern',
          message: 'Error pattern ${entry.key} has occurred $count times',
          source: 'ErrorMonitor',
          timestamp: DateTime.now(),
        ));
      }
    }
  }

  /// Check for resource exhaustion
  void _checkResourceExhaustion(Map<String, dynamic> debugReport) {
    final memoryHistory = debugReport['memory_history'] as List? ?? [];
    
    if (memoryHistory.isNotEmpty) {
      final currentMemory = memoryHistory.last as int;
      final baseline = _baselineMetrics['memory_usage'] ?? 100000.0;
      
      if (currentMemory > baseline * 3.0) { // 300% of baseline
        _addAlert(MonitoringAlert(
          type: AlertType.resource,
          severity: AlertSeverity.critical,
          title: 'Memory Exhaustion Warning',
          message: 'Memory usage ($currentMemory) is ${((currentMemory / baseline - 1) * 100).toStringAsFixed(1)}% above baseline',
          source: 'ResourceMonitor',
          timestamp: DateTime.now(),
        ));
      }
    }
  }

  /// Update performance trends for analysis
  void _updatePerformanceTrends(Map<String, dynamic> debugReport) {
    final performanceMetrics = debugReport['performance_metrics'] as Map<String, dynamic>? ?? {};
    
    for (final entry in performanceMetrics.entries) {
      final category = entry.key;
      final metrics = entry.value as Map<String, dynamic>;
      final average = metrics['average'] as double? ?? 0.0;
      
      _performanceTrends[category] ??= [];
      _performanceTrends[category]!.add(average);
      
      // Keep only last 100 data points
      if (_performanceTrends[category]!.length > 100) {
        _performanceTrends[category]!.removeAt(0);
      }
    }
  }

  /// Generate predictive alerts based on trends
  void _generatePredictiveAlerts(double overallHealth) {
    // Check for declining health trend
    if (overallHealth < 0.7) {
      _addAlert(MonitoringAlert(
        type: AlertType.predictive,
        severity: AlertSeverity.medium,
        title: 'System Health Declining',
        message: 'Overall system health is ${(overallHealth * 100).toStringAsFixed(1)}% - consider investigation',
        source: 'PredictiveMonitor',
        timestamp: DateTime.now(),
      ));
    }
    
    // Analyze performance trends for prediction
    for (final entry in _performanceTrends.entries) {
      final category = entry.key;
      final trends = entry.value;
      
      if (trends.length >= 10) {
        final recent = trends.takeLast(5).reduce((a, b) => a + b) / 5;
        final older = trends.takeLast(10).take(5).reduce((a, b) => a + b) / 5;
        
        if (recent > older * 1.3) { // 30% increase in recent measurements
          _addAlert(MonitoringAlert(
            type: AlertType.predictive,
            severity: AlertSeverity.medium,
            title: 'Performance Trend Warning',
            message: '$category performance is trending upward (${((recent / older - 1) * 100).toStringAsFixed(1)}% increase)',
            source: 'TrendAnalyzer',
            timestamp: DateTime.now(),
          ));
        }
      }
    }
  }

  /// Add monitoring alert with deduplication
  void _addAlert(MonitoringAlert alert) {
    final alertKey = '${alert.type.name}_${alert.title}';
    final lastAlertTime = _lastAlertTimes[alertKey];
    
    // Implement alert cooldown to prevent spam
    if (lastAlertTime != null && 
        DateTime.now().difference(lastAlertTime) < alertCooldown) {
      return;
    }
    
    _activeAlerts.add(alert);
    _lastAlertTimes[alertKey] = alert.timestamp;
    
    // Keep only recent alerts
    if (_activeAlerts.length > maxActiveAlerts) {
      _activeAlerts.removeAt(0);
    }
    
    DebugLogger.log('AppMonitoringService', 
      '🚨 Alert: [${alert.severity.name.toUpperCase()}] ${alert.title} - ${alert.message}');
  }

  /// Process and manage active alerts
  void _processAlerts() {
    final now = DateTime.now();
    final criticalAlerts = _activeAlerts.where((a) => a.severity == AlertSeverity.critical).length;
    final highAlerts = _activeAlerts.where((a) => a.severity == AlertSeverity.high).length;

    if (criticalAlerts > 0 || highAlerts > 3) {
      DebugLogger.log('AppMonitoringService',
        '🚨 Alert Summary at ${now.toString().substring(11, 19)}: $criticalAlerts critical, $highAlerts high priority alerts active');
    }
  }

  /// Escalate alerts if needed
  void _escalateAlertsIfNeeded() {
    final criticalAlerts = _activeAlerts.where((a) => 
      a.severity == AlertSeverity.critical && 
      DateTime.now().difference(a.timestamp).inMinutes > 10
    ).toList();
    
    if (criticalAlerts.isNotEmpty) {
      DebugLogger.log('AppMonitoringService', 
        '🚨 ESCALATION: ${criticalAlerts.length} critical alerts unresolved for >10 minutes');
    }
  }

  /// Clean up old alerts
  void _cleanupOldAlerts() {
    final cutoffTime = DateTime.now().subtract(const Duration(hours: 24));
    _activeAlerts.removeWhere((alert) => alert.timestamp.isBefore(cutoffTime));
  }

  /// Load historical monitoring data
  Future<void> _loadMonitoringHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final historyData = prefs.getString('monitoring_history');
      
      if (historyData != null) {
        final data = jsonDecode(historyData) as Map<String, dynamic>;
        DebugLogger.log('AppMonitoringService', '📚 Loaded monitoring history: ${data.keys.length} sections');
      }
    } catch (e, stackTrace) {
      DebugLogger.error('AppMonitoringService', 'Error loading monitoring history', e, stackTrace);
    }
  }

  /// Save performance baselines
  Future<void> _savePerformanceBaselines() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('performance_baselines', jsonEncode(_baselineMetrics));
    } catch (e, stackTrace) {
      DebugLogger.error('AppMonitoringService', 'Error saving baselines', e, stackTrace);
    }
  }

  /// Get comprehensive monitoring report
  Map<String, dynamic> getMonitoringReport() {
    return {
      'timestamp': DateTime.now().toIso8601String(),
      'is_initialized': _isInitialized,
      'active_alerts': _activeAlerts.map((a) => a.toJson()).toList(),
      'performance_trends': _performanceTrends.map((k, v) => MapEntry(k, {
        'current': v.isNotEmpty ? v.last : 0,
        'average': v.isNotEmpty ? v.reduce((a, b) => a + b) / v.length : 0,
        'trend_direction': v.length >= 2 ? (v.last > v[v.length - 2] ? 'up' : 'down') : 'stable',
      })),
      'baseline_metrics': _baselineMetrics,
      'system_reports': {
        'debug': _debugSystem.getDebugReport(),
        'health': _healthMonitor.getSystemHealthStatus(),
        'errors': _errorHandler.getErrorReport(),
      },
    };
  }

  /// Get active alerts
  List<MonitoringAlert> getActiveAlerts() => List.from(_activeAlerts);

  /// Dispose of the monitoring service
  void dispose() {
    _monitoringTimer?.cancel();
    _alertTimer?.cancel();
    _debugSystem.dispose();
    _healthMonitor.dispose();
    _errorHandler.dispose();
    _isInitialized = false;
    DebugLogger.log('AppMonitoringService', '🛑 App monitoring service disposed');
  }
}

/// Monitoring alert data class
class MonitoringAlert {
  final AlertType type;
  final AlertSeverity severity;
  final String title;
  final String message;
  final String source;
  final DateTime timestamp;
  
  MonitoringAlert({
    required this.type,
    required this.severity,
    required this.title,
    required this.message,
    required this.source,
    required this.timestamp,
  });
  
  Map<String, dynamic> toJson() => {
    'type': type.name,
    'severity': severity.name,
    'title': title,
    'message': message,
    'source': source,
    'timestamp': timestamp.toIso8601String(),
  };
}

/// Alert types
enum AlertType { info, performance, error, resource, predictive, security }

/// Alert severity levels
enum AlertSeverity { low, medium, high, critical }

/// Extension for List.takeLast
extension ListExtension<T> on List<T> {
  List<T> takeLast(int count) {
    if (count >= length) return this;
    return sublist(length - count);
  }
}
