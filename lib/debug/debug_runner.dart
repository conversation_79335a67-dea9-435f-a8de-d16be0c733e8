// 📁 lib/debug/debug_runner.dart

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../controller/user_controller2.dart';
import '../theme/colors.dart';
import 'comprehensive_debug_session.dart';

/// Simple debug runner widget for testing the coach system
class DebugRunner extends StatefulWidget {
  const DebugRunner({super.key});

  @override
  State<DebugRunner> createState() => _DebugRunnerState();
}

class _DebugRunnerState extends State<DebugRunner> {
  bool _isRunning = false;
  DebugSessionResult? _lastResult;
  String _status = 'Ready to run debug tests';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        title: const Text(
          'Coach System Debug Runner',
          style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Status Card
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.grey[900],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: _isRunning 
                      ? MolColors.cyan 
                      : _lastResult?.overallSuccess == true 
                          ? Colors.green 
                          : _lastResult != null 
                              ? Colors.red 
                              : Colors.grey,
                  width: 2,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        _isRunning 
                            ? Icons.hourglass_empty 
                            : _lastResult?.overallSuccess == true 
                                ? Icons.check_circle 
                                : _lastResult != null 
                                    ? Icons.error 
                                    : Icons.play_circle,
                        color: _isRunning 
                            ? MolColors.cyan 
                            : _lastResult?.overallSuccess == true 
                                ? Colors.green 
                                : _lastResult != null 
                                    ? Colors.red 
                                    : Colors.grey,
                        size: 32,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          _status,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                  if (_lastResult != null) ...[
                    const SizedBox(height: 16),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        _buildMetric(
                          'Success Rate',
                          '${(_lastResult!.successRate * 100).toStringAsFixed(1)}%',
                          _lastResult!.successRate > 0.8 ? Colors.green : Colors.red,
                        ),
                        _buildMetric(
                          'Phases Passed',
                          '${_lastResult!.successfulPhases}/${_lastResult!.totalPhases}',
                          _lastResult!.successfulPhases == _lastResult!.totalPhases 
                              ? Colors.green 
                              : Colors.orange,
                        ),
                        _buildMetric(
                          'Total Time',
                          '${(_lastResult!.totalTimeMs / 1000).toStringAsFixed(1)}s',
                          Colors.cyan,
                        ),
                      ],
                    ),
                  ],
                ],
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Run Button
            SizedBox(
              width: double.infinity,
              height: 60,
              child: ElevatedButton(
                onPressed: _isRunning ? null : _runDebugTests,
                style: ElevatedButton.styleFrom(
                  backgroundColor: MolColors.cyan,
                  disabledBackgroundColor: Colors.grey[700],
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: _isRunning
                    ? const Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              color: Colors.white,
                              strokeWidth: 2,
                            ),
                          ),
                          SizedBox(width: 12),
                          Text(
                            'Running Tests...',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      )
                    : const Text(
                        'Run Comprehensive Debug Tests',
                        style: TextStyle(
                          color: Colors.black,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Results Section
            if (_lastResult != null) ...[
              const Text(
                'Last Test Results',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              Expanded(
                child: Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.grey[900],
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Test Summary',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        ..._buildResultsSummary(),
                        const SizedBox(height: 16),
                        Text(
                          'Debug Log (Last 10 entries)',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        ..._buildDebugLog(),
                      ],
                    ),
                  ),
                ),
              ),
            ] else ...[
              Expanded(
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.bug_report,
                        size: 64,
                        color: Colors.grey[600],
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'No debug results yet',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 18,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Run the comprehensive tests to validate your coach system',
                        style: TextStyle(
                          color: Colors.grey[700],
                          fontSize: 14,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildMetric(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          value,
          style: TextStyle(
            color: color,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            color: Colors.grey,
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  List<Widget> _buildResultsSummary() {
    if (_lastResult == null) return [];

    final results = _lastResult!.results;
    return results.entries.map((entry) {
      final phaseName = entry.key.replaceAll('_', ' ').toUpperCase();
      final phaseResult = entry.value;
      
      bool success = false;
      String details = '';
      
      if (phaseResult is Map) {
        success = phaseResult['success'] == true || 
                 (phaseResult['success_rate'] ?? 0.0) > 0.8;
        
        if (phaseResult.containsKey('success_rate')) {
          final rate = (phaseResult['success_rate'] * 100).toStringAsFixed(1);
          details = '$rate% success rate';
        } else if (phaseResult.containsKey('total_tests')) {
          details = '${phaseResult['passed_tests']}/${phaseResult['total_tests']} passed';
        } else {
          details = success ? 'Passed' : 'Failed';
        }
      }
      
      return Padding(
        padding: const EdgeInsets.symmetric(vertical: 4),
        child: Row(
          children: [
            Icon(
              success ? Icons.check_circle : Icons.error,
              color: success ? Colors.green : Colors.red,
              size: 16,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                phaseName,
                style: const TextStyle(color: Colors.white),
              ),
            ),
            Text(
              details,
              style: TextStyle(
                color: success ? Colors.green : Colors.red,
                fontSize: 12,
              ),
            ),
          ],
        ),
      );
    }).toList();
  }

  List<Widget> _buildDebugLog() {
    if (_lastResult == null) return [];

    final logEntries = _lastResult!.debugLog.reversed.take(10).toList();
    
    return logEntries.map((entry) {
      // Extract timestamp and message
      final parts = entry.split(': ');
      final message = parts.length > 1 ? parts.sublist(1).join(': ') : entry;
      
      return Padding(
        padding: const EdgeInsets.symmetric(vertical: 2),
        child: Text(
          message,
          style: TextStyle(
            color: message.startsWith('✅') 
                ? Colors.green 
                : message.startsWith('❌') 
                    ? Colors.red 
                    : message.startsWith('⚠️') 
                        ? Colors.orange 
                        : Colors.grey[400],
            fontSize: 12,
            fontFamily: 'monospace',
          ),
        ),
      );
    }).toList();
  }

  Future<void> _runDebugTests() async {
    final userController = Provider.of<UserController2>(context, listen: false);
    final currentUser = userController.user;

    if (currentUser == null) {
      setState(() {
        _status = 'Error: No user available for testing';
      });
      return;
    }

    setState(() {
      _isRunning = true;
      _status = 'Running comprehensive debug tests...';
    });

    try {
      final result = await ComprehensiveDebugSession.runCompleteValidation(
        testUser: currentUser,
        verboseLogging: true,
      );

      setState(() {
        _isRunning = false;
        _lastResult = result;
        _status = result.overallSuccess 
            ? '✅ All tests passed! System is ready for deployment.'
            : '⚠️ Some tests failed. Review results before deployment.';
      });

    } catch (e) {
      setState(() {
        _isRunning = false;
        _status = '❌ Debug tests failed: $e';
      });
    }
  }
}
