import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../utils/debug_logger.dart';

/// Enterprise-grade debug system for comprehensive app monitoring
/// 
/// Features:
/// - Real-time performance monitoring
/// - Memory leak detection
/// - Network request tracking
/// - Error pattern analysis
/// - System health checks
/// - Automated failure point detection
/// - Debug metrics collection
/// - Performance bottleneck identification
class EnterpriseDebugSystem {
  static final EnterpriseDebugSystem _instance = EnterpriseDebugSystem._internal();
  factory EnterpriseDebugSystem() => _instance;
  EnterpriseDebugSystem._internal();

  // System monitoring
  Timer? _monitoringTimer;
  final Map<String, dynamic> _systemMetrics = {};
  final List<DebugEvent> _debugEvents = [];
  final Map<String, int> _errorCounts = {};
  final Map<String, DateTime> _lastErrorTimes = {};
  
  // Performance tracking
  final Map<String, List<int>> _performanceMetrics = {};
  final Map<String, Stopwatch> _activeOperations = {};
  
  // Memory monitoring
  final List<int> _memoryHistory = [];
  
  // Network monitoring
  final List<NetworkRequest> _networkRequests = [];
  int _totalNetworkRequests = 0;
  int _failedNetworkRequests = 0;
  
  // System health
  bool _isHealthy = true;
  final List<String> _healthIssues = [];
  
  /// Initialize the enterprise debug system
  Future<bool> initialize() async {
    try {
      DebugLogger.log('EnterpriseDebugSystem', '🚀 Initializing enterprise debug system...');
      
      // Start system monitoring
      _startSystemMonitoring();
      
      // Initialize performance tracking
      _initializePerformanceTracking();
      
      // Setup memory monitoring
      _setupMemoryMonitoring();
      
      // Configure error tracking
      _configureErrorTracking();
      
      // Start health checks
      _startHealthChecks();
      
      DebugLogger.log('EnterpriseDebugSystem', '✅ Enterprise debug system initialized successfully');
      return true;
      
    } catch (e, stackTrace) {
      DebugLogger.error('EnterpriseDebugSystem', 'Failed to initialize debug system', e, stackTrace);
      return false;
    }
  }
  
  /// Start comprehensive system monitoring
  void _startSystemMonitoring() {
    _monitoringTimer?.cancel();
    _monitoringTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      _collectSystemMetrics();
      _analyzePerformancePatterns();
      _detectMemoryLeaks();
      _performBasicHealthCheck();
    });
    
    DebugLogger.log('EnterpriseDebugSystem', '📊 System monitoring started (30s intervals)');
  }
  
  /// Initialize performance tracking capabilities
  void _initializePerformanceTracking() {
    _performanceMetrics.clear();
    _activeOperations.clear();
    
    // Pre-initialize common operation categories
    _performanceMetrics['ui_rendering'] = [];
    _performanceMetrics['data_loading'] = [];
    _performanceMetrics['network_requests'] = [];
    _performanceMetrics['database_operations'] = [];
    _performanceMetrics['image_processing'] = [];
    
    DebugLogger.log('EnterpriseDebugSystem', '⚡ Performance tracking initialized');
  }
  
  /// Setup memory monitoring
  void _setupMemoryMonitoring() {
    _memoryHistory.clear();

    DebugLogger.log('EnterpriseDebugSystem', '🧠 Memory monitoring configured');
  }
  
  /// Configure comprehensive error tracking
  void _configureErrorTracking() {
    _errorCounts.clear();
    _lastErrorTimes.clear();
    
    // Setup Flutter error handling
    FlutterError.onError = (FlutterErrorDetails details) {
      _recordError('flutter_error', details.exception.toString(), details.stack.toString());
      DebugLogger.error('FlutterError', details.exception.toString(), details.exception, details.stack);
    };
    
    DebugLogger.log('EnterpriseDebugSystem', '🚨 Error tracking configured');
  }
  
  /// Start automated health checks
  void _startHealthChecks() {
    Timer.periodic(const Duration(minutes: 5), (timer) {
      _performHealthCheck();
    });
    
    DebugLogger.log('EnterpriseDebugSystem', '💚 Health checks started (5min intervals)');
  }
  
  /// Collect comprehensive system metrics
  void _collectSystemMetrics() {
    try {
      final now = DateTime.now();
      
      _systemMetrics['timestamp'] = now.toIso8601String();
      _systemMetrics['uptime'] = now.difference(DateTime.now().subtract(Duration(milliseconds: DateTime.now().millisecondsSinceEpoch))).inSeconds;
      _systemMetrics['debug_events_count'] = _debugEvents.length;
      _systemMetrics['error_count'] = _errorCounts.values.fold(0, (sum, count) => sum + count);
      _systemMetrics['network_requests'] = _totalNetworkRequests;
      _systemMetrics['failed_requests'] = _failedNetworkRequests;
      _systemMetrics['memory_usage'] = _getCurrentMemoryUsage();
      _systemMetrics['is_healthy'] = _isHealthy;
      _systemMetrics['health_issues'] = _healthIssues.length;
      
      // Add to debug events
      _addDebugEvent(DebugEvent(
        type: DebugEventType.systemMetrics,
        message: 'System metrics collected',
        data: Map.from(_systemMetrics),
        timestamp: now,
      ));
      
    } catch (e, stackTrace) {
      DebugLogger.error('EnterpriseDebugSystem', 'Error collecting system metrics', e, stackTrace);
    }
  }
  
  /// Start tracking a performance operation
  void startOperation(String operationName) {
    _activeOperations[operationName] = Stopwatch()..start();
    DebugLogger.log('EnterpriseDebugSystem', '⏱️ Started tracking: $operationName');
  }
  
  /// End tracking a performance operation
  void endOperation(String operationName) {
    final stopwatch = _activeOperations.remove(operationName);
    if (stopwatch != null) {
      stopwatch.stop();
      final duration = stopwatch.elapsedMilliseconds;
      
      // Categorize the operation
      String category = _categorizeOperation(operationName);
      _performanceMetrics[category] ??= [];
      _performanceMetrics[category]!.add(duration);
      
      // Keep only last 100 measurements per category
      if (_performanceMetrics[category]!.length > 100) {
        _performanceMetrics[category]!.removeAt(0);
      }
      
      DebugLogger.log('EnterpriseDebugSystem', '✅ Completed $operationName: ${duration}ms');
      
      // Check for performance issues
      if (duration > 5000) { // 5 seconds
        _recordError('performance_issue', 'Slow operation: $operationName took ${duration}ms', '');
      }
    }
  }
  
  /// Record an error with comprehensive tracking
  void recordError(String category, String message, [String? stackTrace]) {
    _recordError(category, message, stackTrace ?? '');
  }
  
  void _recordError(String category, String message, String stackTrace) {
    final now = DateTime.now();
    
    _errorCounts[category] = (_errorCounts[category] ?? 0) + 1;
    _lastErrorTimes[category] = now;
    
    _addDebugEvent(DebugEvent(
      type: DebugEventType.error,
      message: message,
      data: {
        'category': category,
        'stack_trace': stackTrace,
        'error_count': _errorCounts[category],
      },
      timestamp: now,
    ));
    
    // Check for error patterns
    _analyzeErrorPatterns(category);
  }
  
  /// Record network request for monitoring
  void recordNetworkRequest(String url, int statusCode, int duration, [String? error]) {
    _totalNetworkRequests++;
    if (statusCode >= 400 || error != null) {
      _failedNetworkRequests++;
    }
    
    final request = NetworkRequest(
      url: url,
      statusCode: statusCode,
      duration: duration,
      error: error,
      timestamp: DateTime.now(),
    );
    
    _networkRequests.add(request);
    
    // Keep only last 100 requests
    if (_networkRequests.length > 100) {
      _networkRequests.removeAt(0);
    }
    
    DebugLogger.log('EnterpriseDebugSystem', '🌐 Network: $url ($statusCode) ${duration}ms');
  }
  
  /// Get current memory usage (simplified)
  int _getCurrentMemoryUsage() {
    // This is a simplified implementation
    // In a real app, you'd use platform-specific memory APIs
    return DateTime.now().millisecondsSinceEpoch % 1000000; // Mock value
  }
  
  /// Categorize operation for performance tracking
  String _categorizeOperation(String operationName) {
    if (operationName.contains('ui') || operationName.contains('render')) return 'ui_rendering';
    if (operationName.contains('network') || operationName.contains('http')) return 'network_requests';
    if (operationName.contains('database') || operationName.contains('storage')) return 'database_operations';
    if (operationName.contains('image') || operationName.contains('photo')) return 'image_processing';
    return 'data_loading';
  }
  
  /// Add debug event to history
  void _addDebugEvent(DebugEvent event) {
    _debugEvents.add(event);
    
    // Keep only last 1000 events
    if (_debugEvents.length > 1000) {
      _debugEvents.removeAt(0);
    }
  }
  
  /// Analyze performance patterns for bottlenecks
  void _analyzePerformancePatterns() {
    for (final category in _performanceMetrics.keys) {
      final metrics = _performanceMetrics[category]!;
      if (metrics.isNotEmpty) {
        final average = metrics.reduce((a, b) => a + b) / metrics.length;
        final max = metrics.reduce((a, b) => a > b ? a : b);
        
        if (average > 2000) { // 2 seconds average
          _recordError('performance_pattern', 'High average response time in $category: ${average.toStringAsFixed(1)}ms', '');
        }
        
        if (max > 10000) { // 10 seconds max
          _recordError('performance_spike', 'Performance spike in $category: ${max}ms', '');
        }
      }
    }
  }
  
  /// Detect potential memory leaks
  void _detectMemoryLeaks() {
    final currentMemory = _getCurrentMemoryUsage();
    _memoryHistory.add(currentMemory);
    
    // Keep only last 20 measurements
    if (_memoryHistory.length > 20) {
      _memoryHistory.removeAt(0);
    }
    
    // Check for consistent memory growth
    if (_memoryHistory.length >= 10) {
      final recent = _memoryHistory.sublist(_memoryHistory.length - 5);
      final older = _memoryHistory.sublist(_memoryHistory.length - 10, _memoryHistory.length - 5);
      
      final recentAvg = recent.reduce((a, b) => a + b) / recent.length;
      final olderAvg = older.reduce((a, b) => a + b) / older.length;
      
      if (recentAvg > olderAvg * 1.2) { // 20% increase
        _recordError('memory_leak', 'Potential memory leak detected: ${((recentAvg - olderAvg) / olderAvg * 100).toStringAsFixed(1)}% increase', '');
      }
    }
  }
  
  /// Analyze error patterns for systemic issues
  void _analyzeErrorPatterns(String category) {
    final count = _errorCounts[category] ?? 0;
    final lastTime = _lastErrorTimes[category];
    
    if (count > 10) {
      _recordError('error_pattern', 'High error frequency in $category: $count errors', '');
    }
    
    if (lastTime != null) {
      final timeSinceLastError = DateTime.now().difference(lastTime).inMinutes;
      if (timeSinceLastError < 1 && count > 3) {
        _recordError('error_burst', 'Error burst detected in $category: $count errors in 1 minute', '');
      }
    }
  }
  
  /// Perform comprehensive health check
  void _performHealthCheck() {
    _healthIssues.clear();
    
    // Check error rates
    final totalErrors = _errorCounts.values.fold(0, (sum, count) => sum + count);
    if (totalErrors > 50) {
      _healthIssues.add('High error count: $totalErrors');
    }
    
    // Check network failure rate
    if (_totalNetworkRequests > 0) {
      final failureRate = _failedNetworkRequests / _totalNetworkRequests;
      if (failureRate > 0.1) { // 10% failure rate
        _healthIssues.add('High network failure rate: ${(failureRate * 100).toStringAsFixed(1)}%');
      }
    }
    
    // Check memory usage
    if (_memoryHistory.isNotEmpty) {
      final currentMemory = _memoryHistory.last;
      if (currentMemory > 500000) { // Mock threshold
        _healthIssues.add('High memory usage: $currentMemory');
      }
    }
    
    // Check performance
    for (final category in _performanceMetrics.keys) {
      final metrics = _performanceMetrics[category]!;
      if (metrics.isNotEmpty) {
        final average = metrics.reduce((a, b) => a + b) / metrics.length;
        if (average > 3000) { // 3 seconds
          _healthIssues.add('Slow $category: ${average.toStringAsFixed(1)}ms avg');
        }
      }
    }
    
    _isHealthy = _healthIssues.isEmpty;
    
    DebugLogger.log('EnterpriseDebugSystem', 
      _isHealthy ? '💚 System health: GOOD' : '🔴 System health: ISSUES (${_healthIssues.length})');
  }
  
  /// Get comprehensive debug report
  Map<String, dynamic> getDebugReport() {
    return {
      'system_metrics': _systemMetrics,
      'performance_metrics': _performanceMetrics.map((k, v) => MapEntry(k, {
        'count': v.length,
        'average': v.isNotEmpty ? v.reduce((a, b) => a + b) / v.length : 0,
        'max': v.isNotEmpty ? v.reduce((a, b) => a > b ? a : b) : 0,
        'min': v.isNotEmpty ? v.reduce((a, b) => a < b ? a : b) : 0,
      })),
      'error_counts': _errorCounts,
      'network_stats': {
        'total_requests': _totalNetworkRequests,
        'failed_requests': _failedNetworkRequests,
        'failure_rate': _totalNetworkRequests > 0 ? _failedNetworkRequests / _totalNetworkRequests : 0,
      },
      'memory_history': _memoryHistory,
      'health_status': {
        'is_healthy': _isHealthy,
        'issues': _healthIssues,
      },
      'recent_events': _debugEvents.take(50).map((e) => e.toJson()).toList(),
    };
  }
  
  /// Perform basic health check
  void _performBasicHealthCheck() {
    try {
      final totalErrors = _errorCounts.values.fold(0, (sum, count) => sum + count);
      final isHealthy = totalErrors < 10 && _healthIssues.length < 3;

      _systemMetrics['is_healthy'] = isHealthy;
      _systemMetrics['total_errors'] = totalErrors;

      if (!isHealthy) {
        DebugLogger.log('EnterpriseDebugSystem', '⚠️ Health check: Issues detected');
      }
    } catch (e, stackTrace) {
      DebugLogger.error('EnterpriseDebugSystem', 'Error in health check', e, stackTrace);
    }
  }

  /// Dispose of the debug system
  void dispose() {
    _monitoringTimer?.cancel();
    _activeOperations.clear();
    DebugLogger.log('EnterpriseDebugSystem', '🛑 Enterprise debug system disposed');
  }
}

/// Debug event data class
class DebugEvent {
  final DebugEventType type;
  final String message;
  final Map<String, dynamic> data;
  final DateTime timestamp;
  
  DebugEvent({
    required this.type,
    required this.message,
    required this.data,
    required this.timestamp,
  });
  
  Map<String, dynamic> toJson() => {
    'type': type.toString(),
    'message': message,
    'data': data,
    'timestamp': timestamp.toIso8601String(),
  };
}

/// Network request data class
class NetworkRequest {
  final String url;
  final int statusCode;
  final int duration;
  final String? error;
  final DateTime timestamp;
  
  NetworkRequest({
    required this.url,
    required this.statusCode,
    required this.duration,
    this.error,
    required this.timestamp,
  });
}

/// Debug event types
enum DebugEventType {
  systemMetrics,
  error,
  performance,
  network,
  memory,
  health,
}
