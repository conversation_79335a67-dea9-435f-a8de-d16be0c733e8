import 'package:flutter/material.dart';
import 'dart:convert';
import 'enterprise_debug_system.dart';
import '../utils/debug_logger.dart';

/// Enterprise debug dashboard for comprehensive system monitoring
/// 
/// Features:
/// - Real-time system metrics display
/// - Performance analytics visualization
/// - Error tracking and analysis
/// - Memory usage monitoring
/// - Network request analytics
/// - Health status indicators
/// - Debug event timeline
/// - Export debug reports
class EnterpriseDebugDashboard extends StatefulWidget {
  const EnterpriseDebugDashboard({super.key});

  @override
  State<EnterpriseDebugDashboard> createState() => _EnterpriseDebugDashboardState();
}

class _EnterpriseDebugDashboardState extends State<EnterpriseDebugDashboard> {
  final EnterpriseDebugSystem _debugSystem = EnterpriseDebugSystem();
  Map<String, dynamic> _debugReport = {};
  bool _isLoading = true;
  int _selectedTabIndex = 0;

  @override
  void initState() {
    super.initState();
    _loadDebugReport();
    
    // Refresh every 30 seconds
    Stream.periodic(const Duration(seconds: 30)).listen((_) {
      if (mounted) _loadDebugReport();
    });
  }

  Future<void> _loadDebugReport() async {
    try {
      final report = _debugSystem.getDebugReport();
      setState(() {
        _debugReport = report;
        _isLoading = false;
      });
    } catch (e, stackTrace) {
      DebugLogger.error('EnterpriseDebugDashboard', 'Error loading debug report', e, stackTrace);
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('🔧 Enterprise Debug Dashboard'),
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadDebugReport,
          ),
          IconButton(
            icon: const Icon(Icons.download),
            onPressed: _exportDebugReport,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                _buildHealthStatusBar(),
                _buildTabBar(),
                Expanded(
                  child: _buildTabContent(),
                ),
              ],
            ),
    );
  }

  Widget _buildHealthStatusBar() {
    final healthStatus = _debugReport['health_status'] as Map<String, dynamic>? ?? {};
    final isHealthy = healthStatus['is_healthy'] as bool? ?? false;
    final issues = healthStatus['issues'] as List? ?? [];

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isHealthy ? Colors.green.withValues(alpha: 0.2) : Colors.red.withValues(alpha: 0.2),
        border: Border(
          bottom: BorderSide(
            color: isHealthy ? Colors.green : Colors.red,
            width: 2,
          ),
        ),
      ),
      child: Row(
        children: [
          Icon(
            isHealthy ? Icons.check_circle : Icons.error,
            color: isHealthy ? Colors.green : Colors.red,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  isHealthy ? 'System Health: EXCELLENT' : 'System Health: ISSUES DETECTED',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: isHealthy ? Colors.green : Colors.red,
                    fontSize: 16,
                  ),
                ),
                if (!isHealthy && issues.isNotEmpty)
                  Text(
                    '${issues.length} issue(s): ${issues.take(2).join(", ")}${issues.length > 2 ? "..." : ""}',
                    style: const TextStyle(fontSize: 12, color: Colors.grey),
                  ),
              ],
            ),
          ),
          Text(
            DateTime.now().toString().substring(11, 19),
            style: const TextStyle(fontSize: 12, color: Colors.grey),
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    final tabs = ['Overview', 'Performance', 'Errors', 'Network', 'Memory', 'Events'];
    
    return Container(
      decoration: const BoxDecoration(
        color: Colors.black,
        border: Border(bottom: BorderSide(color: Colors.grey)),
      ),
      child: Row(
        children: tabs.asMap().entries.map((entry) {
          final index = entry.key;
          final tab = entry.value;
          final isSelected = index == _selectedTabIndex;
          
          return Expanded(
            child: GestureDetector(
              onTap: () => setState(() => _selectedTabIndex = index),
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: isSelected ? Colors.blue.withValues(alpha: 0.3) : Colors.transparent,
                  border: Border(
                    bottom: BorderSide(
                      color: isSelected ? Colors.blue : Colors.transparent,
                      width: 2,
                    ),
                  ),
                ),
                child: Text(
                  tab,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: isSelected ? Colors.blue : Colors.white,
                    fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                    fontSize: 12,
                  ),
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildTabContent() {
    switch (_selectedTabIndex) {
      case 0: return _buildOverviewTab();
      case 1: return _buildPerformanceTab();
      case 2: return _buildErrorsTab();
      case 3: return _buildNetworkTab();
      case 4: return _buildMemoryTab();
      case 5: return _buildEventsTab();
      default: return _buildOverviewTab();
    }
  }

  Widget _buildOverviewTab() {
    final systemMetrics = _debugReport['system_metrics'] as Map<String, dynamic>? ?? {};
    final networkStats = _debugReport['network_stats'] as Map<String, dynamic>? ?? {};
    final errorCounts = _debugReport['error_counts'] as Map<String, dynamic>? ?? {};
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildMetricCard('System Overview', [
            _buildMetricRow('Uptime', '${systemMetrics['uptime'] ?? 0}s'),
            _buildMetricRow('Debug Events', '${systemMetrics['debug_events_count'] ?? 0}'),
            _buildMetricRow('Total Errors', '${systemMetrics['error_count'] ?? 0}'),
            _buildMetricRow('Memory Usage', '${systemMetrics['memory_usage'] ?? 0}'),
          ]),
          const SizedBox(height: 16),
          _buildMetricCard('Network Statistics', [
            _buildMetricRow('Total Requests', '${networkStats['total_requests'] ?? 0}'),
            _buildMetricRow('Failed Requests', '${networkStats['failed_requests'] ?? 0}'),
            _buildMetricRow('Failure Rate', '${((networkStats['failure_rate'] ?? 0) * 100).toStringAsFixed(1)}%'),
          ]),
          const SizedBox(height: 16),
          _buildMetricCard('Error Categories', 
            errorCounts.entries.map((e) => _buildMetricRow(e.key, '${e.value}')).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildPerformanceTab() {
    final performanceMetrics = _debugReport['performance_metrics'] as Map<String, dynamic>? ?? {};
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: performanceMetrics.entries.map((entry) {
          final category = entry.key;
          final metrics = entry.value as Map<String, dynamic>;
          
          return Padding(
            padding: const EdgeInsets.only(bottom: 16),
            child: _buildMetricCard(category.replaceAll('_', ' ').toUpperCase(), [
              _buildMetricRow('Count', '${metrics['count'] ?? 0}'),
              _buildMetricRow('Average', '${(metrics['average'] ?? 0).toStringAsFixed(1)}ms'),
              _buildMetricRow('Maximum', '${metrics['max'] ?? 0}ms'),
              _buildMetricRow('Minimum', '${metrics['min'] ?? 0}ms'),
            ]),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildErrorsTab() {
    final errorCounts = _debugReport['error_counts'] as Map<String, dynamic>? ?? {};
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildMetricCard('Error Summary', 
            errorCounts.entries.map((e) => _buildMetricRow(e.key, '${e.value} errors')).toList(),
          ),
          const SizedBox(height: 16),
          _buildMetricCard('Error Analysis', [
            _buildMetricRow('Total Categories', '${errorCounts.length}'),
            _buildMetricRow('Most Frequent', errorCounts.isNotEmpty 
              ? errorCounts.entries.reduce((a, b) => a.value > b.value ? a : b).key 
              : 'None'),
            _buildMetricRow('Total Errors', '${errorCounts.values.fold<int>(0, (sum, count) => sum + (count as int))}'),
          ]),
        ],
      ),
    );
  }

  Widget _buildNetworkTab() {
    final networkStats = _debugReport['network_stats'] as Map<String, dynamic>? ?? {};
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildMetricCard('Network Performance', [
            _buildMetricRow('Total Requests', '${networkStats['total_requests'] ?? 0}'),
            _buildMetricRow('Successful', '${(networkStats['total_requests'] ?? 0) - (networkStats['failed_requests'] ?? 0)}'),
            _buildMetricRow('Failed', '${networkStats['failed_requests'] ?? 0}'),
            _buildMetricRow('Success Rate', '${(100 - (networkStats['failure_rate'] ?? 0) * 100).toStringAsFixed(1)}%'),
          ]),
        ],
      ),
    );
  }

  Widget _buildMemoryTab() {
    final memoryHistory = _debugReport['memory_history'] as List? ?? [];
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildMetricCard('Memory Usage', [
            _buildMetricRow('Current', memoryHistory.isNotEmpty ? '${memoryHistory.last}' : '0'),
            _buildMetricRow('Samples', '${memoryHistory.length}'),
            _buildMetricRow('Average', memoryHistory.isNotEmpty
              ? '${(memoryHistory.reduce((a, b) => a + b) / memoryHistory.length).round()}'
              : '0'),
            _buildMetricRow('Peak', memoryHistory.isNotEmpty 
              ? '${memoryHistory.reduce((a, b) => a > b ? a : b)}'
              : '0'),
          ]),
          const SizedBox(height: 16),
          if (memoryHistory.isNotEmpty)
            Container(
              height: 200,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.8),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.blue, width: 2),
              ),
              child: const Center(
                child: Text(
                  'Memory Usage Chart\n(Visual chart would be implemented here)',
                  textAlign: TextAlign.center,
                  style: TextStyle(color: Colors.white),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildEventsTab() {
    final recentEvents = _debugReport['recent_events'] as List? ?? [];
    
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: recentEvents.length,
      itemBuilder: (context, index) {
        final event = recentEvents[index] as Map<String, dynamic>;
        final type = event['type'] ?? 'unknown';
        final message = event['message'] ?? '';
        final timestamp = event['timestamp'] ?? '';
        
        Color typeColor = Colors.grey;
        IconData typeIcon = Icons.info;
        
        if (type.contains('error')) {
          typeColor = Colors.red;
          typeIcon = Icons.error;
        } else if (type.contains('performance')) {
          typeColor = Colors.orange;
          typeIcon = Icons.speed;
        } else if (type.contains('network')) {
          typeColor = Colors.blue;
          typeIcon = Icons.network_check;
        }
        
        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          color: Colors.black.withValues(alpha: 0.8),
          child: ListTile(
            leading: Icon(typeIcon, color: typeColor),
            title: Text(
              message,
              style: const TextStyle(color: Colors.white, fontSize: 14),
            ),
            subtitle: Text(
              '$type • ${timestamp.substring(11, 19)}',
              style: const TextStyle(color: Colors.grey, fontSize: 12),
            ),
          ),
        );
      },
    );
  }

  Widget _buildMetricCard(String title, List<Widget> metrics) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.8),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue, width: 2),
        boxShadow: [
          BoxShadow(
            color: Colors.blue.withValues(alpha: 0.3),
            blurRadius: 10,
            spreadRadius: 2,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          ...metrics,
        ],
      ),
    );
  }

  Widget _buildMetricRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(color: Colors.grey, fontSize: 14),
          ),
          Text(
            value,
            style: const TextStyle(color: Colors.white, fontSize: 14, fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  void _exportDebugReport() {
    try {
      final reportJson = jsonEncode(_debugReport);
      DebugLogger.log('EnterpriseDebugDashboard', 'Debug report exported: ${reportJson.length} characters');
      
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Debug report exported to logs'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e, stackTrace) {
      DebugLogger.error('EnterpriseDebugDashboard', 'Error exporting debug report', e, stackTrace);
      
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Error exporting debug report'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}
