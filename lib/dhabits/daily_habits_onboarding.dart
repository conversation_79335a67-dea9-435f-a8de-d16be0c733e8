import 'package:flutter/material.dart';
import '../models/user_model.dart';
import '../models/habit_model.dart';
import '../services/user_service.dart';
import '../bulletproof/error_handler.dart';
//import '../bulletproof/app_error.dart' as app_error;
import 'habit_manager.dart';
//import 'dart:math';

class DailyHabitsOnboardingPage extends StatefulWidget {
  final User user;
  const DailyHabitsOnboardingPage({super.key, required this.user});

  @override
  State<DailyHabitsOnboardingPage> createState() => _DailyHabitsOnboardingPageState();
}

class _DailyHabitsOnboardingPageState extends State<DailyHabitsOnboardingPage> {
  // ignore: prefer_final_fields
  List<String> _habits = List.filled(10, ''); // Modified in _initializeHabits and onChanged
  final _controllers = List.generate(10, (_) => TextEditingController());
  bool _saving = false;
  String? _errorMessage;
  late final HabitManager _habitManager;
  late final ErrorHandler _errorHandler;
  late final UserService _userService;

  static const List<Color> neonColors = [
    Color(0xFFFF007F), Color(0xFFFFD700), Color(0xFF00FFFF), Color(0xFF39FF14),
    Color(0xFF8A2BE2), Color(0xFFFF4500), Color(0xFF00FFEF), Color(0xFFFF1493),
    Color(0xFFFFFF00), Color(0xFF7CFC00),
  ];

  @override
  void initState() {
    super.initState();
    _errorHandler = ErrorHandler();
    _habitManager = HabitManager();
    _userService = UserService(_errorHandler);
    _initializeHabits();
  }

  void _initializeHabits() {
    final user = widget.user;
    final existingHabits = user.dailyHabits;
    for (var i = 0; i < existingHabits.length && i < 10; i++) {
      final habit = existingHabits[i];
      _habits[i] = habit.name;
      _controllers[i].text = habit.name;
    }
  }

  @override
  void dispose() {
    for (final c in _controllers) {
      c.dispose();
    }
    super.dispose();
  }

  bool get _canSave {
    final set = _habits.map((h) => h.trim()).where((h) => h.isNotEmpty).toSet();
    return set.length == 10 && !_habits.any((h) => h.trim().isEmpty);
  }

  void _validateHabitName(String name, int index) {
    if (name.length > Habit.maxNameLength) {
      setState(() {
        _errorMessage = 'Habit name too long (max ${Habit.maxNameLength} characters)';
      });
      return;
    }
    if (name.trim().isEmpty) {
      setState(() {
        _errorMessage = 'Habit name cannot be empty';
      });
      return;
    // Check for duplicates
  }}

  Future<void> _saveHabits() async {
    if (!_canSave || _saving) return;
    setState(() {
      _saving = true;
      _errorMessage = null;
    });

    try {
      final habits = List<Habit>.generate(10, (i) {
        final color = neonColors[i % neonColors.length];
        return Habit(
          id: '${DateTime.now().millisecondsSinceEpoch}_$i',
          name: _habits[i].trim(),
          description: 'Daily habit: ${_habits[i].trim()}',
          color: color,
          createdAt: DateTime.now(),
          lastModified: DateTime.now(),
        );
      });
      
      // Save habits using HabitManager
      await _habitManager.saveHabits(widget.user, habits);
      
      final updatedUser = widget.user.copyWith(
        dailyHabits: habits,
      );
      
      await _userService.saveUser(updatedUser);
      
      if (mounted) {
        setState(() => _saving = false);
        Navigator.pop(context, updatedUser);
      }
    } catch (e, stackTrace) {
      _errorHandler.handleError(
        e,
        stackTrace,
        context: 'DailyHabitsOnboardingPage._saveHabits',
      );
      if (mounted) {
        setState(() {
          _saving = false;
          _errorMessage = 'Error saving habits: ${e.toString()}';
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_errorMessage!),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
            action: SnackBarAction(
              label: 'Retry',
              onPressed: _saveHabits,
              textColor: Colors.white,
            ),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        title: const Text(
          'Daily Habits',
          style: TextStyle(
            fontFamily: 'Bitsumishi',
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.black,
        elevation: 0,
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const Text(
                'Enter Your Ten\nDaily Habits',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 20,
                  fontFamily: 'Pirulen',
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 20),
              if (_errorMessage != null)
                Container(
                  padding: const EdgeInsets.all(8),
                  margin: const EdgeInsets.only(bottom: 16),
                  decoration: BoxDecoration(
                    color: Colors.red.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.red),
                  ),
                  child: Text(
                    _errorMessage!,
                    style: const TextStyle(color: Colors.red),
                    textAlign: TextAlign.center,
                  ),
                ),
              Expanded(
                child: ListView.builder(
                  itemCount: 10,
                  itemBuilder: (context, index) {
                    return Padding(
                      padding: const EdgeInsets.only(bottom: 12),
                      child: TextField(
                        controller: _controllers[index],
                        style: const TextStyle(color: Colors.white),
                        decoration: InputDecoration(
                          labelText: 'Habit ${index + 1}',
                          labelStyle: const TextStyle(color: Colors.grey),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(10),
                            borderSide: BorderSide(
                              color: neonColors[index % neonColors.length],
                              width: 1.25, // Made 25% thicker for better visual pop
                            ),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(10),
                            borderSide: BorderSide(
                              color: neonColors[index % neonColors.length],
                              width: 2.5, // Made 25% thicker for better visual pop
                            ),
                          ),
                          filled: true,
                          fillColor: Colors.grey[900],
                        ),
                        onChanged: (value) {
                          setState(() {
                            _habits[index] = value;
                            _validateHabitName(value, index);
                          });
                        },
                      ),
                    );
                  },
                ),
              ),
              const SizedBox(height: 20),
              _buildNeonSaveButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNeonSaveButton() {
    final glowColor = widget.user.northStarQuest?.glowColor ?? const Color(0xFF9C27B0);
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        boxShadow: _canSave && !_saving ? [
          // Primary glow
          BoxShadow(
            color: glowColor.withValues(alpha: 0.6),
            blurRadius: 20,
            spreadRadius: 2,
          ),
          // Secondary glow
          BoxShadow(
            color: glowColor.withValues(alpha: 0.4),
            blurRadius: 30,
            spreadRadius: 4,
          ),
        ] : [],
      ),
      child: ElevatedButton(
        onPressed: _canSave && !_saving ? _saveHabits : null,
        style: ElevatedButton.styleFrom(
          backgroundColor: _canSave && !_saving ? glowColor : Colors.grey,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
          disabledBackgroundColor: Colors.grey,
        ),
        child: _saving
            ? const SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : Text(
                'Save Habits',
                style: TextStyle(
                  fontSize: 18,
                  fontFamily: 'Bitsumishi',
                  color: Colors.white,
                  shadows: [
                    // Black border effect
                    Shadow(
                      offset: const Offset(-1, -1),
                      color: Colors.black,
                    ),
                    Shadow(
                      offset: const Offset(1, -1),
                      color: Colors.black,
                    ),
                    Shadow(
                      offset: const Offset(1, 1),
                      color: Colors.black,
                    ),
                    Shadow(
                      offset: const Offset(-1, 1),
                      color: Colors.black,
                    ),
                  ],
                ),
              ),
      ),
    );
  }
}