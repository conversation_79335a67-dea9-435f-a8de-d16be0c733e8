// lib/dhabits/widgets/habit_card_widget.dart

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/user_model.dart';
import '../controller/user_controller2.dart';
import '../models/habit_model.dart';
import '../theme/colors.dart';
import '../bulletproof/error_handler.dart';

/// Renders text with a narrow black outline for contrast.
class StrokeText extends StatelessWidget {
  final String text;
  final TextStyle textStyle;
  final double strokeWidth;
  final Color strokeColor;
  final TextAlign textAlign;

  const StrokeText({
    super.key,
    required this.text,
    required this.textStyle,
    this.strokeWidth = 1.0,
    this.strokeColor = Colors.black,
    this.textAlign = TextAlign.left,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(children: [
      Text(
        text,
        textAlign: textAlign,
        style: textStyle.copyWith(
          foreground: Paint()
            ..style = PaintingStyle.stroke
            ..strokeWidth = strokeWidth
            ..color = strokeColor,
        ),
      ),
      Text(text, textAlign: textAlign, style: textStyle),
    ]);
  }
}

/// 🌈 Neon Rainbow Palette (10 distinct colors)
final List<Color> neonRainbowColors = [
  MolColors.red,
  MolColors.yellow,
  MolColors.blue,
  MolColors.green,
  MolColors.purple,
  MolColors.orange,
  MolColors.cyan,
  MolColors.pink,
  MolColors.lime,
  MolColors.magenta,
];

class HabitCard extends StatelessWidget {
  final Habit habit;
  final Function(User) onUserUpdated;

  const HabitCard({
    super.key,
    required this.habit,
    required this.onUserUpdated,
  });

  Future<void> _completeHabit(BuildContext context) async {
    try {
      final userController = context.read<UserController2>();
      final user = userController.user;
      if (user == null) return;

      final updatedHabit = habit.copyWith(
        lastCompleted: DateTime.now(),
        lastModified: DateTime.now(),
      );
      userController.updateHabit(updatedHabit);

      final updatedUser = user.copyWith(
        dailyHabits: user.dailyHabits.map((h) => 
          h.id == habit.id ? updatedHabit : h
        ).toList(),
      );

      await userController.updateUser(updatedUser);
      onUserUpdated(updatedUser);
    } catch (e) {
      ErrorHandler().handleError(e, StackTrace.current);
      rethrow;
    }
  }

  @override
  Widget build(BuildContext context) {
    final userController = context.read<UserController2>();
    final user = userController.user;
    if (user == null) return const SizedBox.shrink();

    return LayoutBuilder(builder: (context, constraints) {
      return _buildCard(context);
    });
  }

  Widget _buildCard(BuildContext context) {
    final isCompletedToday = habit.isCompletedToday();

    return Dismissible(
      key: Key(habit.id),
      direction: DismissDirection.startToEnd,
      confirmDismiss: (_) async {
        if (!isCompletedToday) {
          await _completeHabit(context);
          return true;
        }
        return false;
      },
      background: Container(
        decoration: BoxDecoration(
          color: Colors.green.withValues(alpha: 0.2),
          borderRadius: BorderRadius.circular(12),
        ),
        alignment: Alignment.centerLeft,
        padding: const EdgeInsets.only(left: 20),
        child: const Icon(
          Icons.check_circle_outline,
          color: Colors.greenAccent,
        ),
      ),
      child: Card(
        color: Colors.grey[900],
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side: BorderSide(
            color: isCompletedToday ? Colors.greenAccent : habit.color,
            width: 1.25, // 25% thicker for more vibrance
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(11), // Reduced by 33% (16->11)
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Text(
                      habit.name,
                      style: TextStyle(
                        color: isCompletedToday ? Colors.greenAccent : Colors.white,
                        fontSize: 12, // Reduced by 33% (18->12)
                        fontFamily: 'Bitsumishi',
                      ),
                    ),
                  ),
                  if (isCompletedToday)
                    const Icon(
                      Icons.check_circle,
                      color: Colors.greenAccent,
                      size: 20,
                    ),
                ],
              ),
              if (habit.description.isNotEmpty) ...[
                const SizedBox(height: 5), // Reduced by 33% (8->5)
                Text(
                  habit.description,
                  style: TextStyle(
                    color: Colors.grey[400],
                    fontSize: 9, // Reduced by 33% (14->9)
                    fontFamily: 'Bitsumishi',
                  ),
                ),
              ],
              const SizedBox(height: 5), // Reduced by 33% (8->5)
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Container(
                    width: 8, // Reduced by 33% (12->8)
                    height: 8, // Reduced by 33% (12->8)
                    decoration: BoxDecoration(
                      color: habit.color,
                      shape: BoxShape.circle,
                    ),
                  ),
                  Text(
                    'Streak: ${habit.streak}',
                    style: const TextStyle(
                      color: Colors.orangeAccent,
                      fontSize: 8, // Reduced by 33% (12->8)
                      fontFamily: 'Digital-7',
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
