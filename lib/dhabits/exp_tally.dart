import 'package:flutter/material.dart';
//import '../models/user_model.dart';
import '../models/habit_model.dart';

class HabitExpTallyWidget extends StatelessWidget {
  final Habit habit;
  final int earnedExp; // EXP earned this week
  final int expTarget; // EXP needed for level-up

  const HabitExpTallyWidget({
    super.key,
    required this.habit,
    required this.earnedExp,
    this.expTarget = 100,
  });

  @override
  Widget build(BuildContext context) {
    final percent = (earnedExp / expTarget).clamp(0.0, 1.0);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 10),
        const Text(
          'Habit EXP Progress',
          style: TextStyle(color: Colors.white70, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 6),
        LinearProgressIndicator(
          value: percent,
          backgroundColor: Colors.grey.shade800,
          color: Colors.tealAccent,
          minHeight: 6,
        ),
        Padding(
          padding: const EdgeInsets.only(top: 4.0),
          child: Text(
            '$earnedExp / $expTarget EXP',
            style: const TextStyle(color: Colors.white54, fontSize: 12),
          ),
        ),
      ],
    );
  }
}
