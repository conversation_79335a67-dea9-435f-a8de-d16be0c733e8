import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
//import '../models/user_model.dart';
import '../models/habit_model.dart';

class WeeklyHabitOverview extends StatelessWidget {
  final Habit habit;

  const WeeklyHabitOverview({
    super.key,
    required this.habit,
  });

  // 🗓 Generate Mon–Sun with completion flags from weeklyLog
  List<Map<String, dynamic>> _generateWeeklySnapshot(Map<String, int> weeklyLog) {
    final now = DateTime.now();
    final days = List.generate(7, (i) {
      final date = now.subtract(Duration(days: 6 - i));
      final label = DateFormat.E().format(date); // Mon, Tue, ...
      final done = (weeklyLog[label] ?? 0) > 0;
      return {
        'label': label,
        'completed': done,
        'isToday': label == DateFormat.E().format(now),
      };
    });
    return days;
  }

  // 🎯 Calculate % consistency this week
  String _calculateConsistency(Map<String, int> log) {
    if (log.isEmpty) return '0%';
    final hits = log.values.where((v) => v > 0).length;
    final percent = ((hits / 7) * 100).round();
    return '$percent%';
  }

  @override
  Widget build(BuildContext context) {
    final snapshot = _generateWeeklySnapshot(habit.weeklyLog);
    final consistency = _calculateConsistency(habit.weeklyLog);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'This Week',
              style: TextStyle(color: Colors.white70, fontWeight: FontWeight.bold),
            ),
            Text(
              'Consistency: $consistency',
              style: const TextStyle(color: Colors.white54, fontSize: 12),
            ),
          ],
        ),
        const SizedBox(height: 6),
        Row(
          children: snapshot.map((day) {
            final completed = day['completed'] as bool;
            final isToday = day['isToday'] as bool;
            final color = completed
                ? Colors.greenAccent
                : isToday
                    ? Colors.purpleAccent
                    : Colors.grey.shade800;

            return Container(
              margin: const EdgeInsets.symmetric(horizontal: 4),
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                color: color,
                shape: BoxShape.circle,
                border: Border.all(color: Colors.white24, width: 1),
              ),
              alignment: Alignment.center,
              child: Text(
                (day['label'] as String).substring(0, 1),
                style: const TextStyle(
                  color: Colors.black,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }
}
