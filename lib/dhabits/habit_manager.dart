// 📁 lib/dhabits/services/habit_manager.dart

import 'dart:async';
import 'dart:convert';
import 'dart:io';
//import 'package:flutter/material.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
//import 'package:synchronized/synchronized.dart';
import '../models/user_model.dart';
import '../models/habit_model.dart';
import '../services/user_service.dart';
import '../bulletproof/error_handler.dart';
import '../bulletproof/backup_manager.dart';
import '../bulletproof/data_integrity_validator.dart';
import '../bulletproof/app_error.dart' as app_error;
import '../performance/performance_monitor.dart';

class HabitManager {
  static final HabitManager _instance = HabitManager._internal();
  factory HabitManager() => _instance;
  
  static const _storage = FlutterSecureStorage();
  final _errorHandler = ErrorHandler();
  final _backupManager = BackupManager();
  final _integrityValidator = DataIntegrityValidator();
  final _performanceMonitor = PerformanceMonitor();
  late final UserService _userService;

  // Stream controllers for real-time updates
  final _habitsController = StreamController<List<Habit>>.broadcast();
  Stream<List<Habit>> get habitsStream => _habitsController.stream;

  // Cache for current habits with versioning
  List<Habit>? _cachedHabits;
  DateTime? _lastCacheUpdate;
  final _lock = Lock();

  HabitManager._internal() {
    _userService = UserService(_errorHandler);
  }

  /// Load habits with error handling and validation
  Future<List<Habit>> loadHabits(User user) async {
    return _lock.synchronized(() async {
      try {
        final stopwatch = _performanceMonitor.startTimer('load_habits');

        // Check cache first
        if (_cachedHabits != null && _lastCacheUpdate != null) {
          final cacheAge = DateTime.now().difference(_lastCacheUpdate!);
          if (cacheAge.inMinutes < 5) { // Cache valid for 5 minutes
            _performanceMonitor.endTimer(stopwatch, 'load_habits');
            return _cachedHabits!;
          }
        }

        // Load from secure storage
        final raw = await _storage.read(key: 'habits_${user.username}');
        if (raw == null) {
          // If no stored habits, return empty list
          _cachedHabits = [];
          _lastCacheUpdate = DateTime.now();
          _habitsController.add(_cachedHabits!);
          return _cachedHabits!;
        }

        // Parse and validate habits
        final List decoded = json.decode(raw);
        final habits = decoded.map((e) => Habit.fromJson(Map<String, dynamic>.from(e))).toList();

        // Validate each habit
        final validHabits = habits.where((h) => h.validate()).toList();
        if (validHabits.length != habits.length) {
          _errorHandler.handleError(
            app_error.AppError(
              'Invalid habits detected',
              'VALIDATION_ERROR',
              {
                'totalHabits': habits.length,
                'validHabits': validHabits.length,
                'invalidHabits': habits.length - validHabits.length,
              },
            ),
            StackTrace.current,
            context: 'HabitManager.loadHabits',
          );
        }

        // Update cache
        _cachedHabits = validHabits;
        _lastCacheUpdate = DateTime.now();
        _habitsController.add(validHabits);

        return validHabits;
      } catch (e, stackTrace) {
        _errorHandler.handleError(
          e,
          stackTrace,
          context: 'HabitManager.loadHabits',
        );
        // Return empty list as fallback
        return [];
      }
    });
  }

  /// Save habits with backup and validation
  Future<void> saveHabits(User user, List<Habit> habits) async {
    return _lock.synchronized(() async {
      try {
        // Validate all habits
        if (!habits.every((h) => h.validate())) {
          throw app_error.AppError(
            'Invalid habits detected',
            'VALIDATION_ERROR',
            {'habits': habits},
          );
        }

        // Create backup before saving
        await _backupManager.createBackup(user);

        // Save to secure storage
        final encoded = json.encode(habits.map((h) => h.toJson()).toList());
        await _storage.write(key: 'habits_${user.username}', value: encoded);

        // Update cache
        _cachedHabits = habits;
        _lastCacheUpdate = DateTime.now();
        _habitsController.add(habits);

        // Validate data integrity
        await _integrityValidator.validateUserData(user);
      } catch (e, stackTrace) {
        _errorHandler.handleError(
          e,
          stackTrace,
          context: 'HabitManager.saveHabits',
        );
        // Attempt recovery from backup
        await _recoverFromBackup(user.username);
      }
    });
  }

  /// Complete a habit with EXP awards
  Future<void> completeHabit({
    required User user,
    required Habit habit,
  }) async {
    return _lock.synchronized(() async {
      try {
        final now = DateTime.now();
        
        // Check if habit is already completed today
        if (habit.isCompletedToday()) {
          throw app_error.AppError(
            'Habit already completed today',
            'ALREADY_COMPLETED',
            {'habit': habit},
          );
        }

        final updatedHabit = habit.copyWith(
          lastCompleted: now,
          streak: habit.streak + 1,
        );

        final currentHabits = await loadHabits(user);
        final updatedHabits = currentHabits.map((h) {
          if (h.id == habit.id) return updatedHabit;
          return h;
        }).toList();

        await saveHabits(user, updatedHabits);

        // Award EXP
        final updatedUser = user.copyWith(
          exp: user.exp + 2, // 1 for Health, 1 for Connection
          categories: {
            ...user.categories,
            'Health': (user.categories['Health'] ?? 0) + 1,
            'Connection': (user.categories['Connection'] ?? 0) + 1,
          },
        );

        await _userService.saveUser(updatedUser);
      } catch (e, stackTrace) {
        _errorHandler.handleError(
          e,
          stackTrace,
          context: 'HabitManager.completeHabit',
        );
        rethrow;
      }
    });
  }

  /// Reset habits for a new day
  Future<List<Habit>> resetHabitsIfNewDay(List<Habit> habits) async {
    return _lock.synchronized(() async {
      try {
        final now = DateTime.now();
        final todayKey = "${now.year}-${now.month}-${now.day}";

        final updatedHabits = habits.map((habit) {
          final last = habit.lastCompleted;
          final isNewDay = last == null ||
              last.year != now.year ||
              last.month != now.month ||
              last.day != now.day;
          
          if (isNewDay) {
            final updatedLog = Map<String, int>.from(habit.weeklyLog);
            updatedLog[todayKey] = habit.streak;
            return habit.copyWith(
              lastCompleted: null,
              streak: 0,
              weeklyLog: updatedLog,
            );
          }
          return habit;
        }).toList();

        // Validate all habits after update
        if (!updatedHabits.every((h) => h.validate())) {
          throw app_error.AppError(
            'Invalid habits after reset',
            'VALIDATION_ERROR',
            {'habits': updatedHabits},
          );
        }

        return updatedHabits;
      } catch (e, stackTrace) {
        _errorHandler.handleError(
          e,
          stackTrace,
          context: 'HabitManager.resetHabitsIfNewDay',
        );
        return habits; // Return original habits on error
      }
    });
  }

  /// Recover habits from backup
  Future<void> _recoverFromBackup(String username) async {
    try {
      final backups = await _backupManager.getAvailableBackups();
      final userBackups = backups.where((b) => b.path.contains('${username}_backup')).toList();
      
      if (userBackups.isNotEmpty) {
        // Sort by timestamp
        userBackups.sort((a, b) => b.timestamp.compareTo(a.timestamp));
        final backup = File(userBackups.first.path);
        
        if (await backup.exists()) {
          final raw = await backup.readAsString();
          final List decoded = json.decode(raw);
          final habits = decoded.map((e) => Habit.fromJson(Map<String, dynamic>.from(e))).toList();
          
          // Validate recovered habits
          final validHabits = habits.where((h) => h.validate()).toList();
          if (validHabits.isNotEmpty) {
            await _storage.write(
              key: 'habits_$username',
              value: json.encode(validHabits.map((h) => h.toJson()).toList()),
            );
            _cachedHabits = validHabits;
            _lastCacheUpdate = DateTime.now();
            _habitsController.add(validHabits);
          }
        }
      }
    } catch (e, stackTrace) {
      _errorHandler.handleError(
        e,
        stackTrace,
        context: 'HabitManager._recoverFromBackup',
      );
    }
  }

  /// Dispose resources
  void dispose() {
    _habitsController.close();
  }
}

/// Simple lock for synchronizing access
class Lock {
  Future<void> Function()? _currentOperation;
  final _queue = <Completer<void>>[];

  Future<T> synchronized<T>(Future<T> Function() operation) async {
    if (_currentOperation != null) {
      final completer = Completer<void>();
      _queue.add(completer);
      await completer.future;
    }

    _currentOperation = () async {
      try {
        await operation();
      } finally {
        _currentOperation = null;
        if (_queue.isNotEmpty) {
          _queue.removeAt(0).complete();
        }
      }
    };

    return operation();
  }
}
