// 📁 lib/dhabits/widgets/complete_habits_widget.dart

import 'package:flutter/material.dart';
import '../models/user_model.dart';
import '../exp_data.dart';
import '../utils/audio_popup_utils.dart';

class CompleteHabitsPopup extends StatefulWidget {
  final User user;
  final void Function(User) onUserUpdated;
  final VoidCallback onDismissed; // ✅ NEW

  const CompleteHabitsPopup({
    super.key,
    required this.user,
    required this.onUserUpdated,
    required this.onDismissed,
  });

  @override
  State<CompleteHabitsPopup> createState() => _CompleteHabitsPopupState();
}

class _CompleteHabitsPopupState extends State<CompleteHabitsPopup>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 600),
    );
    _scaleAnimation =
        CurvedAnimation(parent: _controller, curve: Curves.easeOutBack);
    _controller.forward();

    _finalizeCompletion();
  }

  Future<void> _finalizeCompletion() async {
    await AudioPopupUtils.playSound('Success.mp3');
    final updatedUser1 = await ExpData.finishEntry(
        widget.user, 'Health', 1, '[AUTO] Daily habits complete');
    final updatedUser2 = await ExpData.finishEntry(
        updatedUser1, 'Connection', 1, '[AUTO] Daily habits complete');

    setState(() {
      widget.user.categories.clear();
      widget.user.categories.addAll(updatedUser2.categories);
      widget.user.diary.clear();
      widget.user.diary.addAll(updatedUser2.diary);
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: ScaleTransition(
        scale: _scaleAnimation,
        child: Container(
          width: 320,
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: Colors.black,
            borderRadius: BorderRadius.circular(20),
            border: Border.all(color: Colors.amberAccent, width: 3),
            boxShadow: [
              BoxShadow(
                color: Colors.amber.withValues(alpha: 0.6),
                blurRadius: 20,
                spreadRadius: 2,
              )
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                '✅ Daily Habits Complete!',
                style: TextStyle(
                  fontFamily: 'Pirulen',
                  fontSize: 18,
                  color: Colors.amberAccent,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 12),
              const Text(
                'Great work - Success is built by small consistent actions, keep going 👑',
                style: TextStyle(
                  fontFamily: 'Roboto',
                  fontSize: 14,
                  color: Colors.white70,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.amber,
                  foregroundColor: Colors.black,
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10)),
                ),
                onPressed: () {
                  widget.onDismissed(); // ✅ Trigger external logic
                  Navigator.of(context).pop();
                },
                child: const Text('OK'),
              )
            ],
          ),
        ),
      ),
    );
  }
}
