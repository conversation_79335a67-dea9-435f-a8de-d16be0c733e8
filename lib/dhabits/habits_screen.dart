import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:audioplayers/audioplayers.dart';
import '../models/habit_model.dart';
import '../models/user_model.dart';
import '../controller/user_controller2.dart';
import '../theme/colors.dart';
import 'dart:async';
import '../widgets/interactive_tutorial_overlay.dart';

class HabitsScreen extends StatefulWidget {
  const HabitsScreen({super.key});

  @override
  State<HabitsScreen> createState() => _HabitsScreenState();
}

class _HabitsScreenState extends State<HabitsScreen> {
  late User user;
  late List<Habit> habits;
  late List<Color> neonColors;
  late AudioPlayer _audioPlayer;
  Timer? _midnightTimer;

  @override
  void initState() {
    super.initState();
    final controller = context.read<UserController2>();
    user = controller.user!;
    habits = List<Habit>.from(controller.habits);
    neonColors = [
      MolColors.blue,
      MolColors.yellow,
      MolColors.pink,
      MolColors.red,
      MolColors.green,
      MolColors.purple,
      MolColors.orange,
      MolColors.cyan,
      MolColors.lime,
      MolColors.magenta,
    ];
    _audioPlayer = AudioPlayer();
    _scheduleMidnightReset();
  }

  @override
  void dispose() {
    _audioPlayer.dispose();
    _midnightTimer?.cancel();
    super.dispose();
  }

  void _scheduleMidnightReset() {
    final now = DateTime.now();
    final tomorrow = DateTime(now.year, now.month, now.day + 1);
    final duration = tomorrow.difference(now);
    _midnightTimer = Timer(duration, _resetHabitsForNewDay);
  }

  Future<void> _resetHabitsForNewDay() async {
    final controller = context.read<UserController2>();
    setState(() {
      habits = habits.map((h) => h.copyWith(lastCompleted: null, streak: h.streak)).toList();
    });
    // Update each habit in the controller
    for (final habit in habits) {
      controller.updateHabit(habit);
    }
    // Schedule next reset
    _scheduleMidnightReset();
  }

  Future<void> _completeHabit(int index) async {
    if (habits[index].isCompletedToday()) return;
    
    final controller = context.read<UserController2>();
    final now = DateTime.now();
    final updatedHabit = habits[index].copyWith(
      lastCompleted: now,
      streak: habits[index].streak + 1,
      lastModified: now,
    );
    
    setState(() {
      habits[index] = updatedHabit;
    });
    
    // Update the habit in the controller
    controller.updateHabit(updatedHabit);
    
    // Award EXP
    final updatedCategories = Map<String, int>.from(user.categories);
    updatedCategories['Connection'] = (updatedCategories['Connection'] ?? 0) + 1;
    updatedCategories['Health'] = (updatedCategories['Health'] ?? 0) + 1;
    
    final updatedUser = user.copyWith(
      categories: updatedCategories,
      exp: user.exp + 2,
      lastModified: now,
    );
    
    await context.read<UserController2>().updateUser(updatedUser);
    setState(() {
      user = updatedUser;
    });
    
    // Play sound
    final allComplete = habits.every((h) => h.isCompletedToday());
    if (allComplete) {
      await _audioPlayer.play(AssetSource('sounds/lightning.mp3'));
    } else {
      await _audioPlayer.play(AssetSource('sounds/success.mp3'));
    }
  }

  void _showStatsModal() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.black87,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
      ),
      builder: (context) {
        final screenHeight = MediaQuery.of(context).size.height;
        final maxModalHeight = screenHeight * 0.8; // Max 80% of screen height

        return Container(
          constraints: BoxConstraints(
            maxHeight: maxModalHeight,
          ),
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _strokeText('Habit Statistics', 28),
                const SizedBox(height: 16),
                // Scrollable habits list with responsive text sizing
                Flexible(
                  child: SingleChildScrollView(
                    child: Column(
                      children: habits.map((habit) {
                        // Calculate responsive font size based on habit name length
                        final nameLength = habit.name.length;
                        final baseFontSize = 18.0;
                        final responsiveFontSize = nameLength > 20
                            ? baseFontSize * 0.7  // 30% smaller for long names
                            : nameLength > 15
                                ? baseFontSize * 0.85  // 15% smaller for medium names
                                : baseFontSize;  // Normal size for short names

                        return Padding(
                          padding: const EdgeInsets.symmetric(vertical: 8),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Expanded(
                                flex: 2,
                                child: _strokeText(habit.name, responsiveFontSize),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                flex: 1,
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.end,
                                  children: [
                                    Text('Streak: ${habit.streak}',
                                        style: const TextStyle(color: Colors.white, fontSize: 12)),
                                    Text('Days as Habit: ${_daysAsHabit(habit)}',
                                        style: const TextStyle(color: Colors.white70, fontSize: 10)),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        );
                      }).toList(),
                    ),
                  ),
                ),
                const SizedBox(height: 24),
                const Divider(color: Colors.grey),
                const SizedBox(height: 16),
                // Tutorial replay option
                ListTile(
                  leading: const Icon(Icons.school, color: Colors.cyanAccent),
                  title: const Text(
                    'Replay Tutorial',
                    style: TextStyle(
                      color: Colors.white,
                      fontFamily: 'Pirulen',
                      fontSize: 16,
                    ),
                  ),
                  subtitle: const Text(
                    'Learn how to use the app',
                    style: TextStyle(
                      color: Colors.white70,
                      fontFamily: 'Bitsumishi',
                      fontSize: 12,
                    ),
                  ),
                  onTap: () {
                    Navigator.of(context).pop();
                    _showTutorial();
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _showTutorial() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => InteractiveTutorialOverlay(
        user: user,
        onComplete: () {
          Navigator.of(context).pop();
        },
      ),
    );
  }

  int _daysAsHabit(Habit habit) {
    return DateTime.now().difference(habit.createdAt).inDays + 1;
  }

  Widget _strokeText(String text, double fontSize) {
    return Stack(
      children: [
        Text(
          text,
          textAlign: TextAlign.center,
          style: TextStyle(
            fontFamily: 'Pirulen',
            fontSize: fontSize,
            fontWeight: FontWeight.bold,
            letterSpacing: 2,
            foreground: Paint()
              ..style = PaintingStyle.stroke
              ..strokeWidth = 2
              ..color = Colors.black,
          ),
        ),
        Text(
          text,
          textAlign: TextAlign.center,
          style: TextStyle(
            fontFamily: 'Pirulen',
            fontSize: fontSize,
            fontWeight: FontWeight.bold,
            color: Colors.white,
            letterSpacing: 2,
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final glowColor = user.northStarQuest?.glowColor ?? MolColors.purple;

    // Check if all habits are completed today
    final allHabitsCompleted = habits.isNotEmpty && habits.every((habit) => habit.isCompletedToday());
    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: Column(
          children: [
            // Top bar: Back, Title, Cog
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  SizedBox(
                    width: 44,
                    height: 44,
                    child: IconButton(
                      icon: const Icon(Icons.arrow_back, color: Colors.white),
                      onPressed: () => Navigator.of(context).pop(),
                    ),
                  ),
                  Expanded(
                    child: Center(
                      child: Container(
                        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 24),
                        decoration: BoxDecoration(
                          color: glowColor.withValues(alpha: allHabitsCompleted ? 1.0 : 0.5),
                          borderRadius: BorderRadius.circular(24),
                          boxShadow: [
                            BoxShadow(
                              color: glowColor.withValues(alpha: allHabitsCompleted ? 1.0 : 0.805),
                              blurRadius: allHabitsCompleted ? 57 : 36.8,
                              spreadRadius: allHabitsCompleted ? 15 : 9.2,
                            ),
                          ],
                        ),
                        child: _strokeText('DAILY HABITS', 36),
                      ),
                    ),
                  ),
                  SizedBox(
                    width: 44,
                    height: 44,
                    child: IconButton(
                      icon: const Icon(Icons.settings, color: Colors.white),
                      onPressed: _showStatsModal,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            // Habits list
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                itemCount: habits.length,
                itemBuilder: (context, index) {
                  final habit = habits[index];
                  final color = neonColors[index % neonColors.length];
                  final isCompleted = habit.isCompletedToday();
                  return Dismissible(
                    key: ValueKey('${habit.id}_${habit.lastCompleted}'),
                    direction: isCompleted ? DismissDirection.none : DismissDirection.startToEnd,
                    onDismissed: (_) => _completeHabit(index),
                    background: Container(
                      alignment: Alignment.centerLeft,
                      padding: const EdgeInsets.only(left: 24), // Reduced by 25% (32->24)
                      decoration: BoxDecoration(
                        color: color.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(12), // Reduced by 25% (16->12)
                        boxShadow: [
                          BoxShadow(
                            color: color.withValues(alpha: 0.4),
                            blurRadius: 12, // Reduced by 25% (16->12)
                            spreadRadius: 3, // Reduced by 25% (4->3)
                          ),
                        ],
                      ),
                      child: const Icon(Icons.check, color: Colors.white, size: 24), // Reduced by 25% (32->24)
                    ),
                    child: Container(
                      margin: const EdgeInsets.symmetric(vertical: 7.5), // Reduced by 25% (10->7.5)
                      decoration: BoxDecoration(
                        color: color.withValues(alpha: isCompleted ? 1.0 : 0.33),
                        borderRadius: BorderRadius.circular(12), // Reduced by 25% (16->12)
                        boxShadow: [
                          BoxShadow(
                            color: color.withValues(alpha: isCompleted ? 0.8 : 0.3),
                            blurRadius: isCompleted ? 24 : 12, // Reduced by 25% (32->24, 16->12)
                            spreadRadius: isCompleted ? 6 : 3, // Reduced by 25% (8->6, 4->3)
                          ),
                        ],
                      ),
                      child: Center(
                        child: _strokeText(habit.name, 21), // Reduced by 25% (28->21)
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
} 