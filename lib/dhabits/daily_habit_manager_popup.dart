// lib/dhabits/daily_habit_manager_popup.dart

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/user_model.dart';
import '../controller/user_controller2.dart';
import '../models/habit_model.dart';

/// Renders text with a narrow black outline.
class StrokeText extends StatelessWidget {
  final String text;
  final TextStyle textStyle;
  final double strokeWidth;
  final Color strokeColor;
  final TextAlign textAlign;

  const StrokeText({
    super.key,
    required this.text,
    required this.textStyle,
    this.strokeWidth = 1.0,
    this.strokeColor = Colors.black,
    this.textAlign = TextAlign.center,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(children: [
      Text(
        text,
        textAlign: textAlign,
        style: textStyle.copyWith(
          foreground: Paint()
            ..style = PaintingStyle.stroke
            ..strokeWidth = strokeWidth
            ..color = strokeColor,
        ),
      ),
      Text(text, textAlign: textAlign, style: textStyle),
    ]);
  }
}

/// Popup dialog to manage Daily Habits with neon styling and persistent save.
class DailyHabitManagerPopup extends StatefulWidget {
  final User user;
  final Function(User) onUserUpdated;

  const DailyHabitManagerPopup({
    super.key,
    required this.user,
    required this.onUserUpdated,
  });

  @override
  State<DailyHabitManagerPopup> createState() => _DailyHabitManagerPopupState();
}

class _DailyHabitManagerPopupState extends State<DailyHabitManagerPopup> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    // Start with empty text field
  }

  @override
  void dispose() {
    _titleController.dispose();
    super.dispose();
  }

  Future<void> _saveHabit() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final userController = context.read<UserController2>();
      final user = userController.user;
      if (user == null) return;

      final habitName = _titleController.text.trim();

      // Check if user already has 10 habits (max limit)
      if (user.dailyHabits.length >= 10) {
        if (!mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Max 10 habits allowed'),
            backgroundColor: Colors.orange,
          ),
        );
        return;
      }

      // Check for duplicate habit names (case-insensitive)
      final existingNames = user.dailyHabits.map((h) => h.name.toLowerCase()).toList();
      if (existingNames.contains(habitName.toLowerCase())) {
        if (!mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Habit name already exists'),
            backgroundColor: Colors.orange,
          ),
        );
        return;
      }

      final newHabit = Habit.create(
        name: habitName,
        description: 'Daily habit for Health & Connection',
        color: Colors.cyanAccent,
      );

      final updatedHabits = [...user.dailyHabits, newHabit];
      final updatedUser = user.copyWith(dailyHabits: updatedHabits);
      await userController.updateUser(updatedUser);

      if (!mounted) return;
      widget.onUserUpdated(updatedUser);
      Navigator.of(context).pop();
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error saving habit: $e')),
      );
    } finally {
      if (mounted) setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    final glowColor = widget.user.northStarQuest?.glowColor ?? Colors.purple;

    return Dialog(
      backgroundColor: Colors.grey[900],
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(color: glowColor, width: 2),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'New Daily Habit',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 20,
                  fontFamily: 'Bitsumishi',
                ),
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _titleController,
                style: const TextStyle(color: Colors.white),
                decoration: InputDecoration(
                  labelText: 'Title',
                  labelStyle: const TextStyle(color: Colors.white70),
                  enabledBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey[800]!),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderSide: const BorderSide(color: Colors.cyanAccent),
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a title';
                  }

                  // Check max limit
                  if (widget.user.dailyHabits.length >= 10) {
                    return 'Max 10 habits allowed';
                  }

                  // Check for duplicates (case-insensitive)
                  final existingNames = widget.user.dailyHabits.map((h) => h.name.toLowerCase()).toList();
                  if (existingNames.contains(value.trim().toLowerCase())) {
                    return 'Habit name already exists';
                  }

                  return null;
                },
              ),
              const SizedBox(height: 24),
              Row(
                children: [
                  // Cancel Button
                  Expanded(
                    child: ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.grey[800],
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      onPressed: () => Navigator.of(context).pop(),
                      child: const Text(
                        'Cancel',
                        style: TextStyle(
                          color: Colors.white,
                          fontFamily: 'Bitsumishi',
                          fontSize: 18,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  // Save Button
                  Expanded(
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: glowColor.withValues(alpha: 0.6),
                            blurRadius: 20,
                            spreadRadius: 2,
                          ),
                        ],
                      ),
                      child: ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          backgroundColor: glowColor,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        onPressed: _isLoading ? null : _saveHabit,
                        child: _isLoading
                            ? const SizedBox(
                                width: 24,
                                height: 24,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                ),
                              )
                            : StrokeText(
                                text: 'SAVE HABIT',
                                textStyle: const TextStyle(
                                  color: Colors.white,
                                  fontFamily: 'Bitsumishi',
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                                strokeWidth: 1.0,
                                strokeColor: Colors.black,
                              ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
