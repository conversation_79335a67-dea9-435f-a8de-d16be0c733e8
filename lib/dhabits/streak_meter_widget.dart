import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
//import '../models/user_model.dart';
import '../models/habit_model.dart';

class StreakMeterWidget extends StatelessWidget {
  final Habit habit;
  final Color color;

  const StreakMeterWidget({
    super.key,
    required this.habit,
    required this.color,
  });

  // 🎯 Calculate 7-day snapshot
  List<bool> _generateWeeklyLog(Map<String, int> weeklyLog) {
    final now = DateTime.now();
    final weekdays = List.generate(7, (i) {
      return DateFormat.E().format(now.subtract(Duration(days: 6 - i)));
    });

    return weekdays.map((day) => (weeklyLog[day] ?? 0) > 0).toList();
  }

  // 🎖 Determine if badge should show
  bool _shouldShowBadge(int streak) => [3, 7, 14].contains(streak);

  // 📊 Stub EXP value (replace with real logic if needed)
  int get habitExp => (habit.streak * 1); // mock value
  int get habitExpTarget => 21;

  @override
  Widget build(BuildContext context) {
    final weeklyStates = _generateWeeklyLog(habit.weeklyLog);
    final expPercent = (habitExp / habitExpTarget).clamp(0.0, 1.0);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 🔢 Streak Header
        Row(
          children: [
            Text(
              '${habit.streak}-Day Streak',
              style: const TextStyle(color: Colors.orangeAccent, fontWeight: FontWeight.bold),
            ),
            const SizedBox(width: 8),
            if (_shouldShowBadge(habit.streak))
              const Icon(Icons.emoji_events, color: Colors.amberAccent),
          ],
        ),
        const SizedBox(height: 4),

        // 📆 Weekly Heatmap Row
        Row(
          children: List.generate(weeklyStates.length, (i) {
            final isDone = weeklyStates[i];
            final color = isDone
                ? Colors.green[300 + (i * 100 % 600)]
                : Colors.grey[800];

            return Container(
              margin: const EdgeInsets.symmetric(horizontal: 2),
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                color: color,
                borderRadius: BorderRadius.circular(4),
              ),
            );
          }),
        ),
        const SizedBox(height: 6),

        // 📊 EXP Progress Bar
        LinearProgressIndicator(
          value: expPercent,
          backgroundColor: color.withValues(alpha: 0.2),
          color: color,
          minHeight: 6,
        ),
        Padding(
          padding: const EdgeInsets.only(top: 2),
          child: Text(
            '$habitExp / $habitExpTarget EXP',
            style: const TextStyle(color: Colors.white54, fontSize: 12),
          ),
        ),
        const SizedBox(height: 6),

        // ⏱ Last Completed
        if (habit.lastCompleted != null)
          Text(
            'Last done: ${DateFormat.yMMMd().format(habit.lastCompleted!)}',
            style: const TextStyle(color: Colors.white38, fontSize: 12),
          ),
      ],
    );
  }
}
