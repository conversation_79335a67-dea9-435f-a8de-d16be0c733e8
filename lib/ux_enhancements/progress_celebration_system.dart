// 📁 lib/ux_enhancements/progress_celebration_system.dart

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../theme/colors.dart';

/// Intelligent progress celebration system that creates magical moments
/// for every user achievement, maximizing dopamine and retention.
class ProgressCelebrationSystem {
  static const Duration _celebrationDuration = Duration(milliseconds: 2500);
  
  /// Show celebration for EXP gain with intelligent scaling
  static Future<void> celebrateExpGain({
    required BuildContext context,
    required int expGained,
    required String category,
    required int totalExp,
    bool isLevelUp = false,
    bool isStreak = false,
  }) async {
    final intensity = _calculateCelebrationIntensity(expGained, isLevelUp, isStreak);
    
    // Haptic feedback based on achievement magnitude
    switch (intensity) {
      case CelebrationIntensity.small:
        HapticFeedback.lightImpact();
        break;
      case CelebrationIntensity.medium:
        HapticFeedback.mediumImpact();
        break;
      case CelebrationIntensity.large:
        HapticFeedback.heavyImpact();
        break;
      case CelebrationIntensity.epic:
        // Double impact for epic moments
        HapticFeedback.heavyImpact();
        await Future.delayed(const Duration(milliseconds: 100));
        HapticFeedback.heavyImpact();
        break;
    }
    
    if (context.mounted) {
      showDialog(
        context: context,
        barrierDismissible: false,
        barrierColor: Colors.black.withValues(alpha: 0.7),
        builder: (context) => _CelebrationDialog(
          expGained: expGained,
          category: category,
          totalExp: totalExp,
          intensity: intensity,
          isLevelUp: isLevelUp,
          isStreak: isStreak,
        ),
      );
      
      // Auto-dismiss after duration
      Future.delayed(_celebrationDuration, () {
        if (context.mounted) {
          Navigator.of(context).pop();
        }
      });
    }
  }
  
  /// Show milestone celebration for major achievements
  static Future<void> celebrateMilestone({
    required BuildContext context,
    required String title,
    required String description,
    required IconData icon,
    Color? color,
  }) async {
    HapticFeedback.heavyImpact();
    
    if (context.mounted) {
      showDialog(
        context: context,
        barrierDismissible: false,
        barrierColor: Colors.black.withValues(alpha: 0.8),
        builder: (context) => _MilestoneDialog(
          title: title,
          description: description,
          icon: icon,
          color: color ?? MolColors.yellow,
        ),
      );
      
      Future.delayed(const Duration(milliseconds: 3500), () {
        if (context.mounted) {
          Navigator.of(context).pop();
        }
      });
    }
  }
  
  static CelebrationIntensity _calculateCelebrationIntensity(
    int expGained,
    bool isLevelUp,
    bool isStreak,
  ) {
    if (isLevelUp || expGained >= 100) return CelebrationIntensity.epic;
    if (isStreak || expGained >= 50) return CelebrationIntensity.large;
    if (expGained >= 25) return CelebrationIntensity.medium;
    return CelebrationIntensity.small;
  }
}

enum CelebrationIntensity { small, medium, large, epic }

class _CelebrationDialog extends StatefulWidget {
  final int expGained;
  final String category;
  final int totalExp;
  final CelebrationIntensity intensity;
  final bool isLevelUp;
  final bool isStreak;

  const _CelebrationDialog({
    required this.expGained,
    required this.category,
    required this.totalExp,
    required this.intensity,
    required this.isLevelUp,
    required this.isStreak,
  });

  @override
  State<_CelebrationDialog> createState() => _CelebrationDialogState();
}

class _CelebrationDialogState extends State<_CelebrationDialog>
    with TickerProviderStateMixin {
  late AnimationController _scaleController;
  late AnimationController _sparkleController;
  late AnimationController _textController;
  
  late Animation<double> _scaleAnimation;
  late Animation<double> _sparkleAnimation;
  late Animation<double> _textAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _startCelebration();
  }

  void _setupAnimations() {
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    
    _sparkleController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    
    _textController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _scaleController, curve: Curves.elasticOut),
    );
    
    _sparkleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _sparkleController, curve: Curves.easeOut),
    );
    
    _textAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _textController, curve: Curves.easeOut),
    );
  }

  void _startCelebration() async {
    _scaleController.forward();
    await Future.delayed(const Duration(milliseconds: 200));
    _sparkleController.forward();
    await Future.delayed(const Duration(milliseconds: 300));
    _textController.forward();
  }

  @override
  Widget build(BuildContext context) {
    final categoryColor = _getCategoryColor(widget.category);
    
    return Dialog(
      backgroundColor: Colors.transparent,
      child: AnimatedBuilder(
        animation: Listenable.merge([_scaleAnimation, _sparkleAnimation, _textAnimation]),
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20),
                gradient: RadialGradient(
                  colors: [
                    categoryColor.withValues(alpha: 0.9),
                    Colors.black.withValues(alpha: 0.95),
                  ],
                ),
                border: Border.all(
                  color: categoryColor.withValues(alpha: 0.8),
                  width: 2,
                ),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Celebration icon with sparkles
                  Stack(
                    alignment: Alignment.center,
                    children: [
                      // Sparkle effect
                      if (_sparkleAnimation.value > 0)
                        ...List.generate(8, (index) {
                          final angle = (index * 45) * (3.14159 / 180);
                          final distance = 40 * _sparkleAnimation.value;
                          return Positioned(
                            left: 30 + (distance * cos(angle)),
                            top: 30 + (distance * sin(angle)),
                            child: Opacity(
                              opacity: 1.0 - _sparkleAnimation.value,
                              child: Icon(
                                Icons.star,
                                color: categoryColor,
                                size: 12,
                              ),
                            ),
                          );
                        }),
                      
                      // Main celebration icon
                      Container(
                        width: 80,
                        height: 80,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          gradient: RadialGradient(
                            colors: [
                              categoryColor,
                              categoryColor.withValues(alpha: 0.7),
                            ],
                          ),
                        ),
                        child: Icon(
                          widget.isLevelUp ? Icons.emoji_events :
                          widget.isStreak ? Icons.local_fire_department :
                          Icons.add_circle,
                          size: 40,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 20),
                  
                  // Celebration text
                  Opacity(
                    opacity: _textAnimation.value,
                    child: Column(
                      children: [
                        Text(
                          widget.isLevelUp ? '🎉 LEVEL UP!' :
                          widget.isStreak ? '🔥 STREAK BONUS!' :
                          '✨ AWESOME!',
                          style: const TextStyle(
                            fontFamily: 'Pirulen',
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        
                        const SizedBox(height: 12),
                        
                        Text(
                          '+${widget.expGained} EXP',
                          style: TextStyle(
                            fontFamily: 'Pirulen',
                            fontSize: 32,
                            fontWeight: FontWeight.bold,
                            color: categoryColor,
                          ),
                        ),
                        
                        const SizedBox(height: 8),
                        
                        Text(
                          widget.category,
                          style: TextStyle(
                            fontFamily: 'Bitsumishi',
                            fontSize: 16,
                            color: Colors.white.withValues(alpha: 0.8),
                          ),
                        ),
                        
                        const SizedBox(height: 12),
                        
                        Text(
                          'Total: ${widget.totalExp} EXP',
                          style: TextStyle(
                            fontFamily: 'Bitsumishi',
                            fontSize: 14,
                            color: Colors.white.withValues(alpha: 0.6),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Color _getCategoryColor(String category) {
    switch (category.toLowerCase()) {
      case 'health':
        return MolColors.blue;
      case 'wealth':
        return MolColors.green;
      case 'purpose':
        return MolColors.purple;
      case 'connection':
        return MolColors.yellow;
      default:
        return MolColors.cyan;
    }
  }

  @override
  void dispose() {
    _scaleController.dispose();
    _sparkleController.dispose();
    _textController.dispose();
    super.dispose();
  }
}

class _MilestoneDialog extends StatefulWidget {
  final String title;
  final String description;
  final IconData icon;
  final Color color;

  const _MilestoneDialog({
    required this.title,
    required this.description,
    required this.icon,
    required this.color,
  });

  @override
  State<_MilestoneDialog> createState() => _MilestoneDialogState();
}

class _MilestoneDialogState extends State<_MilestoneDialog>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _rotationAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.elasticOut),
    );
    
    _rotationAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );
    
    _controller.forward();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: AnimatedBuilder(
        animation: _controller,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Container(
              padding: const EdgeInsets.all(32),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(24),
                gradient: RadialGradient(
                  colors: [
                    widget.color.withValues(alpha: 0.9),
                    Colors.black.withValues(alpha: 0.95),
                  ],
                ),
                border: Border.all(
                  color: widget.color,
                  width: 3,
                ),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Transform.rotate(
                    angle: _rotationAnimation.value * 2 * 3.14159,
                    child: Icon(
                      widget.icon,
                      size: 80,
                      color: widget.color,
                    ),
                  ),
                  
                  const SizedBox(height: 24),
                  
                  Text(
                    widget.title,
                    style: const TextStyle(
                      fontFamily: 'Pirulen',
                      fontSize: 28,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  
                  const SizedBox(height: 16),
                  
                  Text(
                    widget.description,
                    style: TextStyle(
                      fontFamily: 'Bitsumishi',
                      fontSize: 16,
                      color: Colors.white.withValues(alpha: 0.8),
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
}

// Helper function for cos calculation
double cos(double radians) => radians.cos;
double sin(double radians) => radians.sin;

extension on double {
  double get cos => this == 0 ? 1.0 : this < 0 ? -(-this).cos : 
    this > 6.28318530718 ? (this % 6.28318530718).cos :
    this > 3.14159265359 ? -(this - 3.14159265359).cos :
    this > 1.57079632679 ? (3.14159265359 - this).sin :
    1.0 - (this * this) / 2.0 + (this * this * this * this) / 24.0;
    
  double get sin => this == 0 ? 0.0 : this < 0 ? -(-this).sin :
    this > 6.28318530718 ? (this % 6.28318530718).sin :
    this > 3.14159265359 ? -(this - 3.14159265359).sin :
    this > 1.57079632679 ? (3.14159265359 - this).cos :
    this - (this * this * this) / 6.0 + (this * this * this * this * this) / 120.0;
}
