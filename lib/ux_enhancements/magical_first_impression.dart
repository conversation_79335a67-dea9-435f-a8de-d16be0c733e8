// 📁 lib/ux_enhancements/magical_first_impression.dart

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/user_model.dart';
import '../models/diary_entry_model.dart';
import '../models/habit_model.dart';
import '../models/onboarding_progress.dart';
import '../theme/colors.dart';

/// Creates a magical first impression for new users with instant gratification
/// and seamless onboarding that maximizes retention from the first interaction.
class MagicalFirstImpression extends StatefulWidget {
  final Function(User) onComplete;
  final String username;
  final String gender;

  const MagicalFirstImpression({
    super.key,
    required this.onComplete,
    required this.username,
    required this.gender,
  });

  @override
  State<MagicalFirstImpression> createState() => _MagicalFirstImpressionState();
}

class _MagicalFirstImpressionState extends State<MagicalFirstImpression>
    with TickerProviderStateMixin {
  late AnimationController _sparkleController;
  late AnimationController _textController;
  late AnimationController _progressController;
  
  late Animation<double> _sparkleAnimation;
  late Animation<double> _textFadeAnimation;
  late Animation<double> _progressAnimation;
  
  int _currentStep = 0;
  
  final List<String> _magicalSteps = [
    "🌟 Creating your personalized universe...",
    "🎯 Calibrating your success trajectory...", 
    "🚀 Activating your potential multipliers...",
    "💎 Finalizing your diamond-tier experience...",
    "✨ Welcome to your transformation journey!"
  ];

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _startMagicalSequence();
  }

  void _setupAnimations() {
    _sparkleController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _textController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _progressController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );
    
    _sparkleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _sparkleController, curve: Curves.easeInOut),
    );
    
    _textFadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _textController, curve: Curves.easeOut),
    );
    
    _progressAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _progressController, curve: Curves.easeInOut),
    );
    
    _sparkleController.repeat();
  }

  Future<void> _startMagicalSequence() async {
    
    for (int i = 0; i < _magicalSteps.length; i++) {
      setState(() => _currentStep = i);
      
      // Animate text appearance
      _textController.reset();
      _textController.forward();
      
      // Update progress
      _progressController.animateTo((i + 1) / _magicalSteps.length);
      
      // Add haptic feedback for premium feel
      HapticFeedback.lightImpact();
      
      // Wait for step completion
      await Future.delayed(Duration(milliseconds: i == _magicalSteps.length - 1 ? 1500 : 1000));
    }
    
    // Create the user with instant success
    await _createUserWithMagic();
  }

  Future<void> _createUserWithMagic() async {
    // Create user with optimized defaults for immediate success
    final now = DateTime.now();
    final user = User(
      id: now.millisecondsSinceEpoch.toString(),
      username: widget.username,
      gender: widget.gender,
      exp: 50, // Start with some EXP for instant gratification!
      streak: 1, // Start with a streak to build momentum
      level: 1,
      rank: 'Novice',
      rankProgress: 0.5,
      categories: {
        'Health': 15,
        'Wealth': 10,
        'Purpose': 15,
        'Connection': 10,
      },
      diaryEntries: [
        // Add a welcome entry to show immediate value
        DiaryEntry(
          id: 'welcome_${now.millisecondsSinceEpoch}',
          category: 'Purpose',
          note: '🎉 Welcome to MXD! Your transformation journey begins now.',
          exp: 25,
          timestamp: now,
          createdAt: now,
          lastModified: now,
        ),
      ],
      northStarQuest: null,
      createdAt: now,
      lastLoginAt: now,
      dailyHabits: _createStarterHabits(),
      customCategories: [],
      assignedCoaches: _getOptimalCoaches(),
      streakDays: 1,
      challengeExp: 0,
      quests: [],
      lastModified: now,
      diary: {},
      dailyGoalProgress: 25,
      lastNotificationQuote: null,
      lastUpdated: now,
      showHomeLevelWidget: true,
      showLockScreenWidget: true,
      onboardingProgress: OnboardingProgress(
        hasCompletedWelcome: true,
        hasCreatedNorthStar: false,
        hasSetCategories: true,
        hasMetCoaches: true,
        hasSetHabits: true,
        hasCompletedTutorial: false,
        lastUpdated: now,
      ),
      email: null,
      passwordHash: null,
      isEmailVerified: false,
      klaviyoSubscribed: false,
    );
    
    // Add final celebration
    HapticFeedback.heavyImpact();
    
    await Future.delayed(const Duration(milliseconds: 500));
    widget.onComplete(user);
  }

  List<Habit> _createStarterHabits() {
    // Create 3 easy starter habits for immediate wins
    return [
      Habit(
        id: 'starter_1',
        name: '💧 Drink Water',
        description: 'Start your day with hydration',
        color: MolColors.blue,
        createdAt: DateTime.now(),
        lastModified: DateTime.now(),
      ),
      Habit(
        id: 'starter_2', 
        name: '🌅 Morning Gratitude',
        description: 'Think of one thing you\'re grateful for',
        color: MolColors.yellow,
        createdAt: DateTime.now(),
        lastModified: DateTime.now(),
      ),
      Habit(
        id: 'starter_3',
        name: '📱 Open MXD',
        description: 'Check in with your progress',
        color: MolColors.purple,
        createdAt: DateTime.now(),
        lastModified: DateTime.now(),
      ),
    ];
  }

  Map<String, String> _getOptimalCoaches() {
    // Assign coaches based on gender for optimal personalization
    if (widget.gender.toLowerCase() == 'female') {
      return {
        'Health': 'Aria',
        'Wealth': 'Marion',
        'Purpose': 'Seraphina',
        'Connection': 'Amara',
      };
    } else if (widget.gender.toLowerCase() == 'male') {
      return {
        'Health': 'Kai-Tholo',
        'Wealth': 'Sterling',
        'Purpose': 'Ves-ar',
        'Connection': 'Zen',
      };
    } else {
      // Non-gender: Random assignment for variety
      return {
        'Health': 'Aria',
        'Wealth': 'Sterling',
        'Purpose': 'Seraphina',
        'Connection': 'Zen',
      };
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Container(
        decoration: BoxDecoration(
          gradient: RadialGradient(
            center: Alignment.center,
            radius: 1.5,
            colors: [
              MolColors.purple.withValues(alpha: 0.3),
              Colors.black,
              Colors.black,
            ],
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Magical sparkle animation
                AnimatedBuilder(
                  animation: _sparkleAnimation,
                  builder: (context, child) {
                    return Transform.scale(
                      scale: 1.0 + (_sparkleAnimation.value * 0.1),
                      child: Container(
                        width: 120,
                        height: 120,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          gradient: RadialGradient(
                            colors: [
                              MolColors.cyan.withValues(alpha: 0.8),
                              MolColors.purple.withValues(alpha: 0.6),
                              Colors.transparent,
                            ],
                          ),
                        ),
                        child: const Icon(
                          Icons.auto_awesome,
                          size: 60,
                          color: Colors.white,
                        ),
                      ),
                    );
                  },
                ),
                
                const SizedBox(height: 40),
                
                // Welcome message
                Text(
                  'Welcome, ${widget.username}!',
                  style: const TextStyle(
                    fontFamily: 'Pirulen',
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                  textAlign: TextAlign.center,
                ),
                
                const SizedBox(height: 16),
                
                Text(
                  'Preparing your personalized\ntransformation experience...',
                  style: TextStyle(
                    fontFamily: 'Bitsumishi',
                    fontSize: 16,
                    color: Colors.white.withValues(alpha: 0.8),
                  ),
                  textAlign: TextAlign.center,
                ),
                
                const SizedBox(height: 60),
                
                // Magical step text
                AnimatedBuilder(
                  animation: _textFadeAnimation,
                  builder: (context, child) {
                    return Opacity(
                      opacity: _textFadeAnimation.value,
                      child: Text(
                        _magicalSteps[_currentStep],
                        style: TextStyle(
                          fontFamily: 'Bitsumishi',
                          fontSize: 18,
                          color: MolColors.cyan,
                          fontWeight: FontWeight.w500,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    );
                  },
                ),
                
                const SizedBox(height: 40),
                
                // Progress bar
                AnimatedBuilder(
                  animation: _progressAnimation,
                  builder: (context, child) {
                    return Column(
                      children: [
                        Container(
                          width: double.infinity,
                          height: 6,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(3),
                            color: Colors.white.withValues(alpha: 0.2),
                          ),
                          child: FractionallySizedBox(
                            alignment: Alignment.centerLeft,
                            widthFactor: _progressAnimation.value,
                            child: Container(
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(3),
                                gradient: LinearGradient(
                                  colors: [MolColors.cyan, MolColors.purple],
                                ),
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(height: 12),
                        Text(
                          '${(_progressAnimation.value * 100).toInt()}%',
                          style: TextStyle(
                            fontFamily: 'Bitsumishi',
                            fontSize: 14,
                            color: Colors.white.withValues(alpha: 0.7),
                          ),
                        ),
                      ],
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _sparkleController.dispose();
    _textController.dispose();
    _progressController.dispose();
    super.dispose();
  }
}
