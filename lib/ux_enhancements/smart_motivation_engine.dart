// 📁 lib/ux_enhancements/smart_motivation_engine.dart

import 'package:flutter/foundation.dart';
import '../models/user_model.dart';
import '../models/diary_entry_model.dart';

/// Intelligent motivation engine that analyzes user behavior patterns
/// and delivers personalized motivation at optimal moments for maximum impact.
class SmartMotivationEngine {
  static const int _analysisWindowDays = 7;
  static const int _minEntriesForAnalysis = 3;
  
  /// Analyze user patterns and generate personalized motivation
  static Future<MotivationInsight> generateMotivationInsight(User user) async {
    try {
      final patterns = await _analyzeUserPatterns(user);
      final motivationType = _determineOptimalMotivationType(patterns);
      final message = _generatePersonalizedMessage(user, patterns, motivationType);
      
      return MotivationInsight(
        message: message,
        type: motivationType,
        confidence: patterns.confidence,
        suggestedActions: _generateActionSuggestions(user, patterns),
        optimalTiming: _calculateOptimalTiming(patterns),
      );
    } catch (e) {
      if (kDebugMode) print('❌ Failed to generate motivation insight: $e');
      return _getFallbackMotivation(user);
    }
  }
  
  /// Get contextual motivation based on current state
  static String getContextualMotivation(User user, {String? context}) {
    final timeOfDay = DateTime.now().hour;
    final streakStatus = _analyzeStreakStatus(user);
    final recentActivity = _analyzeRecentActivity(user);
    
    // Morning motivation (6-11 AM)
    if (timeOfDay >= 6 && timeOfDay < 12) {
      if (streakStatus == StreakStatus.strong) {
        return "🌅 Good morning, ${user.username}! Your ${user.streakDays}-day streak is incredible. Let's make today even better!";
      } else if (recentActivity == ActivityLevel.low) {
        return "🌟 Morning, ${user.username}! Today is a fresh start. One small win can change everything.";
      } else {
        return "☀️ Rise and shine, ${user.username}! Your momentum is building. What will you conquer today?";
      }
    }
    
    // Afternoon motivation (12-17 PM)
    else if (timeOfDay >= 12 && timeOfDay < 18) {
      if (recentActivity == ActivityLevel.high) {
        return "🚀 You're on fire today, ${user.username}! Keep that energy flowing.";
      } else {
        return "⚡ Afternoon power-up time, ${user.username}! A quick win now can energize your whole day.";
      }
    }
    
    // Evening motivation (18-22 PM)
    else if (timeOfDay >= 18 && timeOfDay < 23) {
      final todayEntries = _getTodayEntries(user);
      if (todayEntries.isNotEmpty) {
        return "🌙 Great work today, ${user.username}! You've earned ${todayEntries.fold(0, (sum, e) => sum + e.exp)} EXP. Tomorrow awaits!";
      } else {
        return "🌆 Evening reflection time, ${user.username}. Even a small entry can cap off your day perfectly.";
      }
    }
    
    // Late night/early morning (23-5 AM)
    else {
      return "🌟 Rest well, ${user.username}. Tomorrow is another chance to level up your life.";
    }
  }
  
  /// Get streak-specific motivation
  static String getStreakMotivation(User user) {
    final streak = user.streakDays;
    
    if (streak == 0) {
      return "🎯 Every expert was once a beginner. Start your streak today, ${user.username}!";
    } else if (streak == 1) {
      return "🔥 Day 1 complete! The hardest part is behind you. Keep the momentum going!";
    } else if (streak < 7) {
      return "⚡ $streak days strong! You're building an unstoppable habit. Don't break the chain!";
    } else if (streak < 30) {
      return "🚀 $streak days of excellence! You're in the top 10% of users. Legendary status awaits!";
    } else if (streak < 100) {
      return "💎 $streak days of pure dedication! You're a true champion. Nothing can stop you now!";
    } else {
      return "👑 $streak days of mastery! You're an inspiration to everyone. You've transcended ordinary!";
    }
  }
  
  static Future<UserPatterns> _analyzeUserPatterns(User user) async {
    final recentEntries = user.diaryEntries
        .where((entry) => DateTime.now().difference(entry.timestamp).inDays <= _analysisWindowDays)
        .toList();
    
    if (recentEntries.length < _minEntriesForAnalysis) {
      return UserPatterns.insufficient();
    }
    
    // Analyze activity patterns
    final dailyActivity = <int, int>{};
    final categoryPreferences = <String, int>{};
    final timePatterns = <int, int>{};
    
    for (final entry in recentEntries) {
      final day = entry.timestamp.weekday;
      final hour = entry.timestamp.hour;
      
      dailyActivity[day] = (dailyActivity[day] ?? 0) + 1;
      categoryPreferences[entry.category] = (categoryPreferences[entry.category] ?? 0) + entry.exp;
      timePatterns[hour] = (timePatterns[hour] ?? 0) + 1;
    }
    
    return UserPatterns(
      preferredDays: _getTopKeys(dailyActivity, 3),
      preferredCategories: _getTopKeys(categoryPreferences, 2),
      preferredTimes: _getTopKeys(timePatterns, 2),
      averageExpPerEntry: recentEntries.fold(0, (sum, e) => sum + e.exp) / recentEntries.length,
      activityFrequency: recentEntries.length / _analysisWindowDays,
      confidence: _calculateConfidence(recentEntries.length),
    );
  }
  
  static MotivationType _determineOptimalMotivationType(UserPatterns patterns) {
    if (patterns.activityFrequency >= 1.0) {
      return MotivationType.momentum; // High activity users need momentum maintenance
    } else if (patterns.activityFrequency >= 0.5) {
      return MotivationType.encouragement; // Moderate users need encouragement
    } else {
      return MotivationType.gentle; // Low activity users need gentle nudges
    }
  }
  
  static String _generatePersonalizedMessage(User user, UserPatterns patterns, MotivationType type) {
    final topCategory = patterns.preferredCategories.isNotEmpty ? patterns.preferredCategories.first : 'Health';
    
    switch (type) {
      case MotivationType.momentum:
        return "🚀 ${user.username}, your ${patterns.activityFrequency.toStringAsFixed(1)}x daily activity rate is incredible! Your focus on $topCategory is paying off. Keep this unstoppable momentum!";
      
      case MotivationType.encouragement:
        return "⭐ ${user.username}, you're making solid progress! Your dedication to $topCategory shows real commitment. A little more consistency will unlock amazing results!";
      
      case MotivationType.gentle:
        return "🌱 ${user.username}, every small step counts. Your interest in $topCategory is a great foundation. What's one tiny action you could take today?";
    }
  }
  
  static List<String> _generateActionSuggestions(User user, UserPatterns patterns) {
    final suggestions = <String>[];
    
    if (patterns.activityFrequency < 0.5) {
      suggestions.add("Set a daily 5-minute reminder to log one small win");
      suggestions.add("Choose your easiest category and aim for just 10 EXP today");
    } else if (patterns.activityFrequency < 1.0) {
      suggestions.add("Try logging in your strongest category: ${patterns.preferredCategories.first}");
      suggestions.add("Aim for a 3-day streak to build momentum");
    } else {
      suggestions.add("Challenge yourself with a new category");
      suggestions.add("Set a weekly EXP goal to maintain your amazing pace");
    }
    
    return suggestions;
  }
  
  static List<int> _getTopKeys(Map<dynamic, int> map, int count) {
    final sorted = map.entries.toList()..sort((a, b) => b.value.compareTo(a.value));
    return sorted.take(count).map((e) => e.key as int).toList();
  }
  
  static double _calculateConfidence(int dataPoints) {
    if (dataPoints >= 10) return 0.9;
    if (dataPoints >= 5) return 0.7;
    if (dataPoints >= 3) return 0.5;
    return 0.3;
  }
  
  static StreakStatus _analyzeStreakStatus(User user) {
    if (user.streakDays >= 7) return StreakStatus.strong;
    if (user.streakDays >= 3) return StreakStatus.building;
    if (user.streakDays >= 1) return StreakStatus.starting;
    return StreakStatus.broken;
  }
  
  static ActivityLevel _analyzeRecentActivity(User user) {
    final recentEntries = user.diaryEntries
        .where((entry) => DateTime.now().difference(entry.timestamp).inDays <= 3)
        .length;
    
    if (recentEntries >= 5) return ActivityLevel.high;
    if (recentEntries >= 2) return ActivityLevel.medium;
    return ActivityLevel.low;
  }
  
  static List<DiaryEntry> _getTodayEntries(User user) {
    final today = DateTime.now();
    return user.diaryEntries.where((entry) {
      final entryDate = entry.timestamp;
      return entryDate.year == today.year &&
             entryDate.month == today.month &&
             entryDate.day == today.day;
    }).toList();
  }
  
  static String _calculateOptimalTiming(UserPatterns patterns) {
    if (patterns.preferredTimes.isEmpty) return "anytime";
    
    final hour = patterns.preferredTimes.first;
    if (hour >= 6 && hour < 12) return "morning";
    if (hour >= 12 && hour < 18) return "afternoon";
    if (hour >= 18 && hour < 22) return "evening";
    return "night";
  }
  
  static MotivationInsight _getFallbackMotivation(User user) {
    return MotivationInsight(
      message: "🌟 Keep growing, ${user.username}! Every step forward is progress worth celebrating.",
      type: MotivationType.encouragement,
      confidence: 0.5,
      suggestedActions: ["Log one small win today", "Check in with your progress"],
      optimalTiming: "anytime",
    );
  }
}

class MotivationInsight {
  final String message;
  final MotivationType type;
  final double confidence;
  final List<String> suggestedActions;
  final String optimalTiming;

  MotivationInsight({
    required this.message,
    required this.type,
    required this.confidence,
    required this.suggestedActions,
    required this.optimalTiming,
  });
}

class UserPatterns {
  final List<int> preferredDays;
  final List<int> preferredCategories;
  final List<int> preferredTimes;
  final double averageExpPerEntry;
  final double activityFrequency;
  final double confidence;

  UserPatterns({
    required this.preferredDays,
    required this.preferredCategories,
    required this.preferredTimes,
    required this.averageExpPerEntry,
    required this.activityFrequency,
    required this.confidence,
  });
  
  factory UserPatterns.insufficient() {
    return UserPatterns(
      preferredDays: [],
      preferredCategories: [],
      preferredTimes: [],
      averageExpPerEntry: 0.0,
      activityFrequency: 0.0,
      confidence: 0.0,
    );
  }
}

enum MotivationType { momentum, encouragement, gentle }
enum StreakStatus { broken, starting, building, strong }
enum ActivityLevel { low, medium, high }
