import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/user_model.dart';
import '../controller/user_controller2.dart';
//import './state_sync_manager.dart';
import './data_integrity_validator.dart';
import './backup_manager.dart';
import './error_handler.dart';

class MonitoringDashboard extends StatefulWidget {
  const MonitoringDashboard({super.key});

  @override
  State<MonitoringDashboard> createState() => _MonitoringDashboardState();
}

class _MonitoringDashboardState extends State<MonitoringDashboard> {
  //final StateSyncManager _stateSyncManager = StateSyncManager();
  final DataIntegrityValidator _integrityValidator = DataIntegrityValidator();
  final BackupManager _backupManager = BackupManager();
  final ErrorHandler _errorHandler = ErrorHandler();
  late final UserController2 _userController;

  bool _isLoading = false;
  String? _error;
  bool _isHealthy = true;
  List<String> _warnings = [];
  bool _isBackupHealthy = true;

  @override
  void initState() {
    super.initState();
    _userController = Provider.of<UserController2>(context, listen: false);
    _integrityValidator.initialize(_userController, _errorHandler);
    _backupManager.initialize(_userController, _errorHandler);
    _loadSystemStatus();
  }

  @override
  void dispose() {
    _errorHandler.dispose();
    super.dispose();
  }

  Future<void> _loadSystemStatus() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _error = null;
      _warnings = [];
    });

    try {
      final user = Provider.of<User>(context, listen: false);
      
      // Check data integrity
      final integrityResult = await _integrityValidator.validateUserData(user);
      
      // Check backup status
      final backupResult = await _backupManager.testBackupCreation(user);
      
      // Check state sync
      await _userController.refreshFromDisk();
      final syncResult = _userController.user != null;

      if (!mounted) return;

      setState(() {
        _isHealthy = integrityResult.isValid && backupResult == true && syncResult == true;
        _isBackupHealthy = backupResult == true;

        if (!integrityResult.isValid) {
          _warnings.add('Data integrity issues detected');
          _warnings.addAll(integrityResult.errors);
        }
        if (backupResult != true) _warnings.add('Backup issues detected');
        if (syncResult != true) _warnings.add('State sync issues detected');
        _isLoading = false;
      });
    } catch (e, stackTrace) {
      if (!mounted) return;
      
      await _errorHandler.handleError(e, stackTrace, context: 'loadSystemStatus');
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _attemptRepair() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final user = Provider.of<User>(context, listen: false);
      
      // Attempt data integrity repair
      final repairResult = await _integrityValidator.attemptRepair(user);
      if (!repairResult.success) {
        _warnings.addAll(repairResult.errors);
      }

      // Attempt backup repair
      if (!_isBackupHealthy) {
        final backupResult = await _backupManager.restoreBackup(user.username);
        if (!backupResult) {
          _warnings.add('Failed to restore from backup');
        }
      }

      // Refresh state
      await _userController.refreshFromDisk();

      if (!mounted) return;

      setState(() {
        _isLoading = false;
      });

      // Reload status after repair
      await _loadSystemStatus();
    } catch (e, stackTrace) {
      if (!mounted) return;
      
      await _errorHandler.handleError(e, stackTrace, context: 'attemptRepair');
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        title: const Text(
          'System Monitor',
          style: TextStyle(
            color: Colors.white,
            fontFamily: 'Bitsumishi',
          ),
        ),
        backgroundColor: Colors.grey[900],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _error != null
              ? Center(
                  child: Text(
                    'Error: $_error',
                    style: const TextStyle(color: Colors.red),
                  ),
                )
              : SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildSystemHealth(),
                      const SizedBox(height: 24),
                      _buildBackupStatus(),
                      const SizedBox(height: 24),
                      _buildWarnings(),
                      const SizedBox(height: 24),
                      _buildActionButtons(),
                    ],
                  ),
                ),
    );
  }

  Widget _buildSystemHealth() {
    return Card(
      color: Colors.grey[900],
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'System Health',
              style: TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontFamily: 'Bitsumishi',
              ),
            ),
            const SizedBox(height: 16),
            LinearProgressIndicator(
              value: _isHealthy ? 1.0 : 0.0,
              backgroundColor: Colors.grey[800],
              valueColor: AlwaysStoppedAnimation<Color>(
                _isHealthy ? Colors.green : Colors.red,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _isHealthy ? '100% Healthy' : 'System Issues Detected',
              style: TextStyle(
                color: _isHealthy ? Colors.green : Colors.red,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBackupStatus() {
    return Card(
      color: Colors.grey[900],
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Backup Status',
              style: TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontFamily: 'Bitsumishi',
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Icon(
                  _isBackupHealthy ? Icons.check_circle : Icons.error,
                  color: _isBackupHealthy ? Colors.green : Colors.red,
                ),
                const SizedBox(width: 8),
                Text(
                  _isBackupHealthy ? 'Backups are healthy' : 'Backup issues detected',
                  style: TextStyle(
                    color: _isBackupHealthy ? Colors.green : Colors.red,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWarnings() {
    if (_warnings.isEmpty) {
      return const SizedBox.shrink();
    }

    return Card(
      color: Colors.grey[900],
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Warnings',
              style: TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontFamily: 'Bitsumishi',
              ),
            ),
            const SizedBox(height: 16),
            ..._warnings.map((warning) => Padding(
                  padding: const EdgeInsets.only(bottom: 8),
                  child: Row(
                    children: [
                      const Icon(Icons.warning, color: Colors.orange),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          warning,
                          style: const TextStyle(color: Colors.orange),
                        ),
                      ),
                    ],
                  ),
                )),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        ElevatedButton(
          onPressed: _loadSystemStatus,
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.blue,
          ),
          child: const Text('Refresh Status'),
        ),
        ElevatedButton(
          onPressed: _isHealthy ? null : _attemptRepair,
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.green,
            disabledBackgroundColor: Colors.grey,
          ),
          child: const Text('Attempt Repair'),
        ),
      ],
    );
  }
} 