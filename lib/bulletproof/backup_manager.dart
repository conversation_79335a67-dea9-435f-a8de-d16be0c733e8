import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import '../controller/user_controller2.dart';
import '../models/user_model.dart';
import '../services/exp_log_secure_service.dart';
import 'error_handler.dart';

/// Information about a backup file
class BackupInfo {
  final String path;
  final int timestamp;
  final int size;

  BackupInfo({
    required this.path,
    required this.timestamp,
    required this.size,
  });

  DateTime get date => DateTime.fromMillisecondsSinceEpoch(timestamp);
  String get formattedDate => '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute}';
  String get formattedSize {
    if (size < 1024) return '$size B';
    if (size < 1024 * 1024) return '${(size / 1024).toStringAsFixed(1)} KB';
    return '${(size / (1024 * 1024)).toStringAsFixed(1)} MB';
  }
}

/// Manages user data backups and recovery.
class BackupManager {
  static final BackupManager _instance = BackupManager._internal();
  factory BackupManager() => _instance;

  late final ErrorHandler _errorHandler;

  BackupManager._internal();

  /// Initialize the manager with required dependencies.
  void initialize(UserController2 userController, ErrorHandler errorHandler) {
    _errorHandler = errorHandler;
  }

  final Map<String, DateTime> _lastBackupTime = {};
  final Duration _backupInterval = const Duration(hours: 1);
  final Map<String, bool> _backupInProgress = {};

  /// Create a backup of the current user data
  Future<bool> createBackup(User user) async {
    if (_backupInProgress[user.username] == true) {
      debugPrint('⚠️ Backup already in progress for ${user.username}');
      return false;
    }

    _backupInProgress[user.username] = true;

    try {
      final now = DateTime.now();
      final lastBackup = _lastBackupTime[user.username];

      // Check if backup is needed
      if (lastBackup != null && now.difference(lastBackup) < _backupInterval) {
        return true; // Already backed up recently
      }

      // Get backup directory
      final backupDir = await _getBackupDirectory();
      if (backupDir == null) {
        throw Exception('Failed to get backup directory');
      }

      // Create user backup
      final userBackup = {
        'timestamp': now.toIso8601String(),
        'user': user.toJson(),
        'version': '1.0', // Add version for future compatibility
      };

      // Create EXP log backup
      final expLog = await ExpLogSecureService.loadDiary(user.username);
      if (expLog.isNotEmpty) {
        userBackup['exp_log'] = expLog;
      }

      // Create backup file with temporary name
      final tempFile = File('${backupDir.path}/${user.username}_backup_${now.millisecondsSinceEpoch}_temp.json');
      final backupFile = File('${backupDir.path}/${user.username}_backup_${now.millisecondsSinceEpoch}.json');

      // Write to temporary file first
      await tempFile.writeAsString(jsonEncode(userBackup));

      // Validate the backup
      if (!await _validateBackupFile(tempFile)) {
        await tempFile.delete();
        throw Exception('Backup validation failed');
      }

      // Rename to final file
      await tempFile.rename(backupFile.path);

      // Update last backup time
      _lastBackupTime[user.username] = now;

      // Clean up old backups
      await _cleanupOldBackups(user.username);

      debugPrint('✅ Created backup for ${user.username}');
      return true;
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'createBackup');
      return false;
    } finally {
      _backupInProgress[user.username] = false;
    }
  }

  /// Restore from the most recent backup
  Future<bool> restoreBackup(String username) async {
    if (_backupInProgress[username] == true) {
      debugPrint('⚠️ Restore already in progress for $username');
      return false;
    }

    _backupInProgress[username] = true;

    try {
      // Get backup directory
      final backupDir = await _getBackupDirectory();
      if (backupDir == null) {
        throw Exception('Failed to get backup directory');
      }

      // Find latest backup
      final backupFile = await _getLatestBackup(username);
      if (backupFile == null) {
        throw Exception('No backup found');
      }

      // Read and validate backup
      final backupData = jsonDecode(await backupFile.readAsString());
      if (!_validateBackupData(backupData)) {
        throw Exception('Invalid backup data');
      }
      
      // Create recovery point before restore
      final userController = UserController2();
      final currentUser = userController.user;
      if (currentUser != null) {
        await createBackup(currentUser);
      }

      // Restore user data
      final user = User.fromJson(backupData['user']);
      userController.user = user;

      // Restore EXP log
      if (backupData['exp_log'] != null) {
        await ExpLogSecureService.clearDiary(username);
        for (final entry in backupData['exp_log']) {
          if (!_validateExpLogEntry(entry)) {
            debugPrint('⚠️ Skipping invalid EXP log entry: $entry');
            continue;
          }
          await ExpLogSecureService.saveEntry(
            user,
            entry['category'],
            entry['exp'] as int,
            entry['note'] ?? '',
          );
        }
      }

      debugPrint('✅ Restored backup for $username');
      return true;
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'restoreBackup');
      return false;
    } finally {
      _backupInProgress[username] = false;
    }
  }

  bool _validateBackupData(Map<String, dynamic> data) {
    if (data['timestamp'] == null || data['user'] == null) {
      return false;
    }

    try {
      DateTime.parse(data['timestamp']);
      User.fromJson(data['user']);
      return true;
    } catch (_) {
      return false;
    }
  }

  bool _validateExpLogEntry(Map<String, dynamic> entry) {
    return entry['category'] != null && 
           entry['exp'] != null && 
           entry['exp'] is num;
  }

  Future<bool> _validateBackupFile(File file) async {
    try {
      final content = await file.readAsString();
      final data = jsonDecode(content);
      return _validateBackupData(data);
    } catch (_) {
      return false;
    }
  }

  Future<void> _cleanupOldBackups(String username) async {
    try {
      final backupDir = await _getBackupDirectory();
      if (backupDir == null) return;

      final backups = await _getSortedBackups(backupDir);
      final userBackups = backups.where((file) => 
        file.path.contains('${username}_backup') && 
        !file.path.contains('_temp')).toList();

      // Keep only the last 5 backups
      if (userBackups.length > 5) {
        for (var i = 5; i < userBackups.length; i++) {
          await userBackups[i].delete();
        }
      }
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'cleanupOldBackups');
    }
  }

  /// Get list of available backups
  Future<List<BackupInfo>> getAvailableBackups() async {
    try {
      final backupDir = await _getBackupDirectory();
      if (backupDir == null) {
        throw Exception('Failed to get backup directory');
      }

      final backups = await _getSortedBackups(backupDir);
      
      return backups.map((file) {
        final timestamp = int.parse(file.path.split('_').last.split('.').first);
        return BackupInfo(
          path: file.path,
          timestamp: timestamp,
          size: file.lengthSync(),
        );
      }).toList();
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'getAvailableBackups');
      return [];
    }
  }

  /// Delete a specific backup
  Future<bool> deleteBackup(String backupPath) async {
    try {
      final file = File(backupPath);
      if (!await file.exists()) {
        return false;
      }

      // Validate path is within backup directory
      final backupDir = await _getBackupDirectory();
      if (backupDir == null || !file.path.startsWith(backupDir.path)) {
        throw Exception('Invalid backup path');
      }

      await file.delete();
      debugPrint('✅ Deleted backup: $backupPath');
      return true;
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'deleteBackup');
      return false;
    }
  }

  Future<Directory?> _getBackupDirectory() async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final backupDir = Directory('${appDir.path}/backups');
      
      if (!await backupDir.exists()) {
        await backupDir.create(recursive: true);
      }

      return backupDir;
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'getBackupDirectory');
      return null;
    }
  }

  Future<List<File>> _getSortedBackups(Directory backupDir) async {
    try {
      final files = await backupDir.list().toList();
      return files
          .whereType<File>()
          .where((file) => file.path.endsWith('.json') && !file.path.contains('_temp'))
          .toList()
        ..sort((a, b) {
          final aTime = int.parse(a.path.split('_').last.split('.').first);
          final bTime = int.parse(b.path.split('_').last.split('.').first);
          return bTime.compareTo(aTime); // Most recent first
        });
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'getSortedBackups');
      return [];
    }
  }

  Future<File?> _getLatestBackup(String username) async {
    try {
      final backupDir = await _getBackupDirectory();
      if (backupDir == null) {
        return null;
      }

      // List backup files
      final backupFiles = await backupDir
          .list()
          .where((entity) => 
            entity.path.contains('${username}_backup') && 
            !entity.path.contains('_temp'))
          .toList();

      if (backupFiles.isEmpty) {
        return null;
      }

      // Sort by modification time
      backupFiles.sort((a, b) => b.statSync().modified.compareTo(a.statSync().modified));

      return File(backupFiles.first.path);
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'getLatestBackup');
      return null;
    }
  }

  Future<bool> testBackupCreation(User user) async {
    try {
      // Create backup
      final backupResult = await createBackup(user);
      if (!backupResult) {
        throw Exception('Backup creation failed');
      }

      // Verify backup exists
      final backupFile = await _getLatestBackup(user.username);
      if (backupFile == null) {
        throw Exception('Backup file not found');
      }

      // Verify backup content
      final content = await backupFile.readAsString();
      final data = jsonDecode(content);
      if (!_validateBackupData(data)) {
        throw Exception('Invalid backup data');
      }

      // Clean up test backup
      await backupFile.delete();

      return true;
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'testBackupCreation');
      return false;
    }
  }

  void dispose() {
    _errorHandler.dispose();
  }
} 