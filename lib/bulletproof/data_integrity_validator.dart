import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import '../controller/user_controller2.dart';
import '../models/user_model.dart';
import '../services/exp_log_secure_service.dart';
import 'error_handler.dart';

class ValidationResult {
  final bool isValid;
  final List<String> errors;
  final List<String> repairs;

  ValidationResult({
    required this.isValid,
    required this.errors,
    required this.repairs,
  });
}

/// Validates user data integrity and repairs any issues found.
class DataIntegrityValidator {
  static final DataIntegrityValidator _instance = DataIntegrityValidator._internal();
  factory DataIntegrityValidator() => _instance;

  late final UserController2 _userController;

  DataIntegrityValidator._internal();

  /// Initialize the validator with required dependencies.
  void initialize(UserController2 userController, ErrorHandler errorHandler) {
    _userController = userController;
  }

  Future<ValidationResult> validateUserData(User user) async {
    try {
      final errors = <String>[];
      final repairs = <String>[];

      // Validate user object
      if (!_validateUserObject(user, errors, repairs)) {
        return ValidationResult(isValid: false, errors: errors, repairs: repairs);
      }

      // Validate EXP log
      if (!await _validateExpLog(user, errors, repairs)) {
        return ValidationResult(isValid: false, errors: errors, repairs: repairs);
      }

      // Validate categories
      if (!await _validateCategories(user, errors, repairs)) {
        return ValidationResult(isValid: false, errors: errors, repairs: repairs);
      }

      // Validate user state consistency
      if (!_validateUserState(user, errors, repairs)) {
        return ValidationResult(isValid: false, errors: errors, repairs: repairs);
      }

      return ValidationResult(
        isValid: errors.isEmpty,
        errors: errors,
        repairs: repairs,
      );
    } catch (e, stackTrace) {
      debugPrint('Data integrity validation failed: $e\n$stackTrace');
      return ValidationResult(
        isValid: false,
        errors: ['Validation failed: $e'],
        repairs: [],
      );
    }
  }

  bool _validateUserObject(User user, List<String> errors, List<String> repairs) {
    // Check required fields
    if (user.username.isEmpty) {
      errors.add('Username is empty');
    }

    // Validate custom categories
    for (final category in user.customCategories) {
      if (category.isEmpty) {
        errors.add('Empty category name found');
      } else if (category.contains('N.S.')) {
        repairs.add('Category contains reserved prefix: $category');
      }
    }

    // Validate EXP values
    if (user.totalExp < 0) {
      errors.add('Negative total EXP value');
    }
    if (user.level < 1) {
      errors.add('Invalid level value');
    }

    return errors.isEmpty;
  }

  bool _validateUserState(User user, List<String> errors, List<String> repairs) {
    // Validate level consistency
    final expectedLevel = _calculateLevel(user.totalExp);
    if (user.level != expectedLevel) {
      errors.add('Level inconsistency detected. Expected: $expectedLevel, Actual: ${user.level}');
    }

    // Validate EXP consistency
    if (user.exp < 0 || user.exp > user.totalExp) {
      errors.add('EXP inconsistency detected. Current EXP: ${user.exp}, Total EXP: ${user.totalExp}');
    }

    // Validate rank consistency
    final expectedRankLevel = _calculateRank(user.totalExp);
    final expectedRankName = _getRankName(expectedRankLevel);
    if (user.rank != expectedRankName) {
      errors.add('Rank inconsistency detected. Expected: $expectedRankName, Actual: ${user.rank}');
    }

    return errors.isEmpty;
  }

  int _calculateLevel(int totalExp) {
    // Implement your level calculation logic here
    return (totalExp / 1000).floor() + 1;
  }

  int _calculateRank(int totalExp) {
    // Implement your rank calculation logic here
    return (totalExp / 5000).floor() + 1;
  }

  String _getRankName(int rankLevel) {
    // Convert rank level to rank name
    switch (rankLevel) {
      case 1:
        return 'Novice';
      case 2:
        return 'Apprentice';
      case 3:
        return 'Journeyman';
      case 4:
        return 'Expert';
      case 5:
        return 'Master';
      default:
        return rankLevel > 5 ? 'Grandmaster' : 'Novice';
    }
  }

  Future<bool> _validateExpLog(User user, List<String> errors, List<String> repairs) async {
    try {
      final expLog = await ExpLogSecureService.loadDiary(user.username);
      if (expLog.isEmpty) {
        repairs.add('No EXP log found');
        return true;
      }

      // Validate each entry
      for (final entry in expLog) {
        if (entry['timestamp'] == null) {
          errors.add('Missing timestamp in EXP log entry');
          continue;
        }
        if (entry['category'] == null) {
          errors.add('Missing category in EXP log entry');
          continue;
        }
        if (entry['exp'] == null) {
          errors.add('Missing amount in EXP log entry');
          continue;
        }

        // Validate timestamp
        try {
          final timestamp = DateTime.parse(entry['timestamp']);
          if (timestamp.isAfter(DateTime.now())) {
            errors.add('Future timestamp found in EXP log');
          }
        } catch (e) {
          errors.add('Invalid timestamp format in EXP log');
        }

        // Validate amount
        final amount = entry['exp'] as num;
        if (amount <= 0) {
          errors.add('Non-positive EXP amount found');
        }

        // Validate category
        final category = entry['category'] as String;
        if (!user.customCategories.contains(category) && !category.startsWith('N.S.')) {
          errors.add('Invalid category in EXP log: $category');
        }
      }

      return errors.isEmpty;
    } catch (e, stackTrace) {
      debugPrint('EXP log validation failed: $e\n$stackTrace');
      errors.add('EXP log validation failed: $e');
      return false;
    }
  }

  Future<bool> _validateCategories(User user, List<String> errors, List<String> repairs) async {
    try {
      // Load user data to verify persistence
      await _userController.refreshFromDisk();
      final loadedUser = _userController.user;
      
      if (loadedUser == null) {
        errors.add('User data not found in persistence');
        return false;
      }

      // Compare categories
      if (loadedUser.customCategories.length != user.customCategories.length) {
        errors.add('Category count mismatch between memory and persistence');
      }

      for (final category in user.customCategories) {
        if (!loadedUser.customCategories.contains(category)) {
          errors.add('Category not found in persistence: $category');
        }
      }

      return errors.isEmpty;
    } catch (e) {
      debugPrint('Category validation failed: $e');
      errors.add('Category validation failed: $e');
      return false;
    }
  }

  Future<ValidationResult> validateCategory(User user, String category) async {
    final errors = <String>[];
    final repairs = <String>[];

    // Check for reserved prefixes
    if (category.startsWith('N.S.')) {
      errors.add('Category contains reserved prefix');
    }

    // Check for duplicates
    if (user.customCategories.contains(category)) {
      errors.add('Category already exists');
    }

    // Check length
    if (category.isEmpty) {
      errors.add('Category name is empty');
    } else if (category.length > 50) {
      errors.add('Category name is too long');
    }

    // Check for invalid characters
    if (category.contains(RegExp(r'[<>{}[\]\\]'))) {
      errors.add('Category contains invalid characters');
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      repairs: repairs,
    );
  }

  Future<RepairResult> attemptRepair(User user) async {
    try {
      final repairs = <String>[];
      final errors = <String>[];
      
      // Validate and fix categories
      final validCategories = <String>[];
      for (final cat in user.customCategories) {
        final result = await validateCategory(user, cat);
        if (result.isValid) {
          validCategories.add(cat);
        }
      }

      if (validCategories.length != user.customCategories.length) {
        repairs.add('Removed invalid categories');
        user = user.copyWith(customCategories: validCategories);
      }

      // Validate and fix EXP values
      if (user.exp < 0) {
        repairs.add('Fixed negative EXP value');
        user = user.copyWith(exp: 0);
      }

      // Save repaired user
      try {
        await _userController.updateUser(user);
        repairs.add('Saved repaired user data');
      } catch (e) {
        errors.add('Failed to save repaired user: $e');
      }

      return RepairResult(
        true,
        repairs,
        errors,
      );
    } catch (e) {
      return RepairResult(
        false,
        [],
        ['Repair failed: $e'],
      );
    }
  }
}

class RepairResult {
  final bool success;
  final List<String> repairs;
  final List<String> errors;

  RepairResult(this.success, this.repairs, [this.errors = const []]);
} 