import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import '../models/user_model.dart';
import '../services/user_service.dart';
import '../cstcat/custom_category_validator.dart';
import '../cstcat/custom_category_state_manager.dart';
import '../cstcat/custom_category_integrity_checker.dart';
// Removed duplicate utils error handler import
import '../notify/notify_bridge.dart';

class AppError implements Exception {
  final String message;
  final String code;
  final Map<String, dynamic>? details;
  final StackTrace? stackTrace;
  final ErrorSeverity severity;

  AppError(this.message, this.code, [this.details, this.stackTrace, this.severity = ErrorSeverity.error]);

  @override
  String toString() => 'AppError: $message (Code: $code, Severity: $severity)';
}

enum ErrorSeverity {
  debug,
  info,
  warning,
  error,
  critical
}

class ErrorLog {
  final DateTime timestamp;
  final dynamic error;
  final StackTrace stackTrace;
  final String? context;
  final ErrorSeverity severity;
  final Map<String, dynamic>? metadata;

  ErrorLog(
    this.error,
    this.stackTrace, {
    this.context,
    this.severity = ErrorSeverity.error,
    this.metadata,
  }) : timestamp = DateTime.now();

  @override
  String toString() => '''
Error Log:
Timestamp: $timestamp
Error: $error
Context: ${context ?? 'No context'}
Severity: $severity
Metadata: ${metadata ?? 'No metadata'}
Stack Trace:
$stackTrace
''';
}

class ErrorHandler {
  static final ErrorHandler _instance = ErrorHandler._internal();
  factory ErrorHandler() => _instance;
  ErrorHandler._internal();

  final _errorController = StreamController<AppError>.broadcast();
  Stream<AppError> get errorStream => _errorController.stream;

  final List<ErrorLog> _errorLogs = [];
  final int _maxLogSize = 1000;
  BuildContext? _currentContext;
  bool _isDisposed = false;

  // Error codes
  static const String _errorUserNotFound = 'USER_NOT_FOUND';
  static const String _errorInvalidCategory = 'INVALID_CATEGORY';
  static const String _errorPersistence = 'PERSISTENCE_ERROR';
  static const String _errorState = 'STATE_ERROR';
  static const String _errorIntegrity = 'INTEGRITY_ERROR';
  static const String _errorValidation = 'VALIDATION_ERROR';
  static const String _errorRecovery = 'RECOVERY_ERROR';
  //static const String _errorUnknown = 'UNKNOWN_ERROR';
  static const String _errorNetwork = 'NETWORK_ERROR';
  static const String _errorAuth = 'AUTH_ERROR';
  static const String _errorStorage = 'STORAGE_ERROR';

  void setContext(BuildContext context) {
    _currentContext = context;
  }

  /// Handle any error in the app
  Future<void> handleError(dynamic error, StackTrace stackTrace, {String? context}) async {
    if (_isDisposed) return;

    // Determine error severity
    final severity = _determineErrorSeverity(error);

    // Create error log
    final errorLog = ErrorLog(
      error,
      stackTrace,
      context: context,
      severity: severity,
      metadata: _gatherErrorMetadata(error),
    );
    _errorLogs.add(errorLog);
    if (_errorLogs.length > _maxLogSize) {
      _errorLogs.removeAt(0);
    }

    // Log to file
    await _logToFile(errorLog);

    // Log error to console for debugging
    debugPrint('🔥 Error: $error');
    if (context != null) debugPrint('📍 Context: $context');

    // Additional app-specific error handling
    if (error is AppError) {
      _errorController.add(error);
      await _attemptRecovery(error);
    }

    // Show error dialog if context is available and severity warrants it
    if (severity.index >= ErrorSeverity.error.index) {
      _showErrorDialog(error);
    }

    // Send error notification if severity is high
    if (severity.index >= ErrorSeverity.critical.index) {
      await _sendErrorNotification(error);
    }
  }

  ErrorSeverity _determineErrorSeverity(dynamic error) {
    if (error is AppError) {
      return error.severity;
    } else if (error is FileSystemException) {
      return ErrorSeverity.error;
    } else if (error is TimeoutException) {
      return ErrorSeverity.warning;
    } else if (error is FormatException) {
      return ErrorSeverity.error;
    } else {
      return ErrorSeverity.error;
    }
  }

  Map<String, dynamic>? _gatherErrorMetadata(dynamic error) {
    final metadata = <String, dynamic>{};
    
    if (error is AppError) {
      metadata['code'] = error.code;
      metadata['details'] = error.details;
    } else if (error is FileSystemException) {
      metadata['path'] = error.path;
      metadata['message'] = error.message;
    } else if (error is TimeoutException) {
      metadata['duration'] = error.duration?.toString();
    }

    return metadata.isNotEmpty ? metadata : null;
  }

  void _showErrorDialog(dynamic error) {
    if (_currentContext == null) {
      debugPrint('No context available for error dialog');
      return;
    }

    // Check if context is still mounted
    if (!_currentContext!.mounted) {
      debugPrint('Context is no longer mounted');
      return;
    }

    showDialog(
      context: _currentContext!,
      builder: (context) => AlertDialog(
        title: const Text('Error'),
        content: Text(_getUserFriendlyMessage(error)),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  Future<void> _sendErrorNotification(AppError error) async {
    try {
      final notifyBridge = NotifyBridge();
      await notifyBridge.showErrorNotification(
        'Critical Error',
        '${error.message}\nDetails: ${error.details ?? "No details provided"}',
      );
    } catch (e) {
      debugPrint('Failed to send error notification: $e');
    }
  }

  String _getUserFriendlyMessage(dynamic error) {
    if (error is AppError) {
      switch (error.code) {
        case _errorUserNotFound:
          return 'User data not found. Please try logging in again.';
        case _errorInvalidCategory:
          return 'Invalid category detected. The category has been removed.';
        case _errorPersistence:
          return 'Failed to save data. Please try again.';
        case _errorState:
          return 'State synchronization error. Please restart the app.';
        case _errorIntegrity:
          return 'Data integrity check failed. Attempting recovery...';
        case _errorValidation:
          return 'Invalid data detected. Please check your input.';
        case _errorRecovery:
          return 'Recovery failed. Please contact support.';
        case _errorNetwork:
          return 'Network connection error. Please check your internet connection.';
        case _errorAuth:
          return 'Authentication error. Please log in again.';
        case _errorStorage:
          return 'Storage error. Please check your device storage.';
        default:
          return 'An unexpected error occurred. Please try again.';
      }
    } else if (error is FileSystemException) {
      return 'File system error: ${error.message}';
    } else if (error is TimeoutException) {
      return 'Operation timed out. Please try again.';
    } else {
      return 'An unexpected error occurred: ${error.toString()}';
    }
  }

  List<ErrorLog> getRecentErrors() {
    return List.from(_errorLogs.reversed);
  }

  Future<void> clearErrorLogs() async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final logFile = File('${appDir.path}/logs/error_log.txt');
      
      if (await logFile.exists()) {
        await logFile.delete();
      }

      _errorLogs.clear();
    } catch (e) {
      debugPrint('Failed to clear error logs: $e');
    }
  }

  /// Attempt to recover from an error
  Future<void> _attemptRecovery(AppError error) async {
    try {
      switch (error.code) {
        case _errorUserNotFound:
          await _recoverUserNotFound(error);
          break;
        case _errorInvalidCategory:
          await _recoverInvalidCategory(error);
          break;
        case _errorPersistence:
          await _recoverPersistence(error);
          break;
        case _errorState:
          await _recoverState(error);
          break;
        case _errorIntegrity:
          await _recoverIntegrity(error);
          break;
        case _errorValidation:
          await _recoverValidation(error);
          break;
        case _errorRecovery:
          await _recoverRecovery(error);
          break;
        case _errorNetwork:
          await _recoverNetwork(error);
          break;
        case _errorAuth:
          await _recoverAuth(error);
          break;
        case _errorStorage:
          await _recoverStorage(error);
          break;
        default:
          await _recoverUnknown(error);
      }
    } catch (e) {
      debugPrint('❌ Recovery failed: $e');
    }
  }

  Future<void> _recoverUserNotFound(AppError error) async {
    final username = error.details?['username'] as String?;
    if (username != null) {
      final userService = UserService(this);
      final backup = await userService.loadUser();
      if (backup != null && backup.username == username) {
        await userService.saveUser(backup);
      }
    }
  }

  Future<void> _recoverInvalidCategory(AppError error) async {
    final user = error.details?['user'] as User?;
    final category = error.details?['category'] as String?;
    if (user != null && category != null) {
      final stateManager = CustomCategoryStateManager();
      await stateManager.removeCategory(user, category);
    }
  }

  Future<void> _recoverPersistence(AppError error) async {
    final user = error.details?['user'] as User?;
    if (user != null) {
      final userService = UserService(this);
      await userService.saveUser(user);
    }
  }

  Future<void> _recoverState(AppError error) async {
    final user = error.details?['user'] as User?;
    if (user != null) {
      final stateManager = CustomCategoryStateManager();
      await stateManager.updateCategories(user, user.customCategories);
    }
  }

  Future<void> _recoverIntegrity(AppError error) async {
    final user = error.details?['user'] as User?;
    if (user != null) {
      final integrityChecker = CustomCategoryIntegrityChecker();
      await integrityChecker.performManualCheck();
    }
  }

  Future<void> _recoverValidation(AppError error) async {
    final user = error.details?['user'] as User?;
    if (user != null) {
      CustomCategoryValidator.validateUserCategorySetup(user);
    }
  }

  Future<void> _recoverRecovery(AppError error) async {
    // Log the recovery failure
    debugPrint('❌ Recovery failed: ${error.message}');
  }

  Future<void> _recoverNetwork(AppError error) async {
    // Implement network recovery logic
    debugPrint('Attempting network recovery...');
  }

  Future<void> _recoverAuth(AppError error) async {
    // Implement auth recovery logic
    debugPrint('Attempting auth recovery...');
  }

  Future<void> _recoverStorage(AppError error) async {
    // Implement storage recovery logic
    debugPrint('Attempting storage recovery...');
  }

  Future<void> _recoverUnknown(AppError error) async {
    // Log unknown error
    debugPrint('❌ Unknown error: ${error.message}');
  }

  /// Clear logs older than 7 days
  void clearOldLogs() {
    final now = DateTime.now();
    _errorLogs.removeWhere((log) => 
      now.difference(log.timestamp).inDays > 7);
  }

  Future<void> _logToFile(ErrorLog errorLog) async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final logDir = Directory('${appDir.path}/logs');
      
      if (!await logDir.exists()) {
        await logDir.create(recursive: true);
      }

      final logFile = File('${logDir.path}/error_log.txt');
      final logEntry = '''
Timestamp: ${errorLog.timestamp}
Error: ${errorLog.error}
Context: ${errorLog.context ?? 'No context'}
Severity: ${errorLog.severity}
Metadata: ${errorLog.metadata ?? 'No metadata'}
Stack Trace:
${errorLog.stackTrace}
----------------------------------------
''';

      await logFile.writeAsString(logEntry, mode: FileMode.append);
    } catch (e) {
      // Fallback to debug print if file logging fails
      debugPrint('Failed to log error to file: $e');
      debugPrint(errorLog.toString());
    }
  }

  /// Dispose of the error handler
  Future<void> dispose() async {
    if (!_isDisposed) {
      _errorController.close();
      _currentContext = null;
      _errorLogs.clear();
      _isDisposed = true;
    }
  }

  void handleWarning(String warning) {
    debugPrint('⚠️ Warning: $warning');
  }

  Future<void> handleAsyncWarning(String warning) async {
    handleWarning(warning);
  }

  void logInfo(String message) {
    debugPrint('ℹ️ Info: $message');
  }

  void logDebug(String message) {
    debugPrint('🔍 Debug: $message');
  }

  void logSuccess(String message) {
    debugPrint('✅ Success: $message');
  }
} 