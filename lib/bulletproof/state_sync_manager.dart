import 'dart:async';
import 'package:flutter/material.dart';
import '../models/user_model.dart';
import '../services/user_service.dart';
import '../cstcat/custom_category_state_manager.dart';
import '../cstcat/custom_category_integrity_checker.dart';
import './error_handler.dart';
import '../controller/user_controller2.dart';
import '../services/exp_log_secure_service.dart';
import 'package:flutter/foundation.dart';

/// Manages synchronization of user state across different parts of the app.
class StateSyncManager {
  static final StateSyncManager _instance = StateSyncManager._internal();
  factory StateSyncManager() => _instance;

  late final UserController2 _userController;
  late final ErrorHandler _errorHandler;

  StateSyncManager._internal();

  final _syncController = StreamController<User>.broadcast();
  Stream<User> get syncStream => _syncController.stream;

  late final CustomCategoryStateManager _stateManager;
  late final CustomCategoryIntegrityChecker _integrityChecker;
  late final UserService _userService;

  Timer? _syncTimer;
  bool _isSyncing = false;
  User? _lastSyncedUser;

  final Map<String, User> _userCache = {};
  final Map<String, DateTime> _lastSyncTime = {};
  final Duration _syncInterval = const Duration(minutes: 5);

  /// Initialize the manager with required dependencies.
  void initialize(UserController2 userController, ErrorHandler errorHandler) {
    _userController = userController;
    _errorHandler = errorHandler;
    _stateManager = CustomCategoryStateManager();
    _integrityChecker = CustomCategoryIntegrityChecker();
    _userService = UserService(_errorHandler);

    // Start periodic sync
    _syncTimer = Timer.periodic(const Duration(seconds: 30), (_) {
      _performSync();
    });
  }

  /// Perform a sync operation
  Future<void> _performSync() async {
    if (_isSyncing) return;
    _isSyncing = true;

    try {
      final currentUser = await _userService.loadUser();
      if (currentUser == null) return;

      // Check if we need to sync
      if (_lastSyncedUser != null && 
          _lastSyncedUser!.toJson().toString() == currentUser.toJson().toString()) {
        return;
      }

      // Start transaction
      _stateManager.beginTransaction();

      // Validate state
      if (!await _validateState(currentUser)) {
        await _stateManager.rollbackTransaction();
        return;
      }

      // Save state
      await _userService.saveUser(currentUser);
      _lastSyncedUser = currentUser;

      // Commit transaction
      await _stateManager.commitTransaction();

      // Broadcast sync
      _syncController.add(currentUser);

      debugPrint('✅ State sync completed successfully');
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'performSync');
      await _stateManager.rollbackTransaction();
    } finally {
      _isSyncing = false;
    }
  }

  /// Validate the current state
  Future<bool> _validateState(User user) async {
    try {
      // Check category integrity
      final integrityResult = await _integrityChecker.performManualCheck();
      if (integrityResult['checks'].values.contains(false)) {
        debugPrint('❌ Integrity check failed: ${integrityResult['errors']}');
        return false;
      }

      // Validate category state
      for (final category in user.customCategories) {
        await _stateManager.updateCategories(user, [category], createRecoveryPoint: false);
      }

      return true;
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'validateState');
      return false;
    }
  }

  /// Force an immediate sync for all users
  Future<void> forceSync() async {
    await _performSync();
  }

  /// Update user state
  Future<void> updateState(User user) async {
    try {
      _stateManager.beginTransaction();
      
      // Validate and update
      if (await _validateState(user)) {
        await _userService.saveUser(user);
        _lastSyncedUser = user;
        await _stateManager.commitTransaction();
        _syncController.add(user);
      } else {
        await _stateManager.rollbackTransaction();
      }
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'updateState');
      await _stateManager.rollbackTransaction();
    }
  }

  /// Dispose resources
  Future<void> dispose() async {
    _syncTimer?.cancel();
    await _syncController.close();
    await _errorHandler.dispose();
  }

  Future<bool> syncUserState(User user) async {
    try {
      final now = DateTime.now();
      final lastSync = _lastSyncTime[user.username];

      // Check if sync is needed
      if (lastSync != null && now.difference(lastSync) < _syncInterval) {
        return true; // Already synced recently
      }

      // Load latest user data
      await _userController.refreshFromDisk();
      final latestUser = _userController.user;
      if (latestUser == null) {
        throw Exception('Failed to load user data during sync');
      }

      // Update cache
      _userCache[user.username] = latestUser;
      _lastSyncTime[user.username] = now;

      // Sync diary
      final diary = await ExpLogSecureService.loadDiary(user.username);
      if (diary.isNotEmpty) {
        await ExpLogSecureService.saveEntry(
          user,
          diary.last['category'],
          diary.last['exp'],
          diary.last['note'],
        );
      }

      return true;
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'syncUserState');
      return false;
    }
  }

  Future<bool> testTransactionRollback(User user) async {
    try {
      // Start transaction
      await _userController.refreshFromDisk();
      final originalUser = _userController.user;
      if (originalUser == null) {
        throw Exception('Failed to load original user data');
      }

      // Make changes
      final modifiedUser = originalUser.copyWith(
        customCategories: [...originalUser.customCategories, 'Test Category']
      );

      // Save changes
      await _userController.updateUser(modifiedUser);

      // Simulate error
      throw Exception('Test error');

      // This should not be reached
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'testTransactionRollback');
      
      // Verify rollback
      await _userController.refreshFromDisk();
      final currentUser = _userController.user;
      if (currentUser == null) {
        return false;
      }

      // Check if changes were rolled back
      return currentUser.customCategories.length == user.customCategories.length;
    }
  }

  User? getCachedUser(String username) {
    return _userCache[username];
  }

  void clearCache() {
    _userCache.clear();
    _lastSyncTime.clear();
  }

  /// Force an immediate sync for a specific user
  Future<void> forceSyncUser(String username) async {
    _lastSyncTime.remove(username);
    final user = _userCache[username];
    if (user != null) {
      await syncUserState(user);
    }
  }
} 