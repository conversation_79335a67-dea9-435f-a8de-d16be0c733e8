import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import '../models/user_model.dart';
import '../controller/user_controller2.dart';
import './error_handler.dart';
import './state_sync_manager.dart';
import './data_integrity_validator.dart';
import './backup_manager.dart';
import 'dart:async';

/// Represents the severity level of an error
enum ErrorSeverity {
  info,
  warning,
  error,
  critical
}

/// Represents an application error with detailed information
class AppError {
  final String message;
  final String code;
  final Map<String, dynamic>? details;
  final StackTrace? stackTrace;
  final ErrorSeverity severity;

  AppError(
    this.message,
    this.code,
    this.details,
    this.stackTrace,
    this.severity,
  );

  @override
  String toString() => '[$code] $message${details != null ? ' - $details' : ''}';
}

/// Manages the bulletproof system that ensures data integrity and reliability.
class BulletproofManager {
  static final BulletproofManager _instance = BulletproofManager._internal();
  factory BulletproofManager() => _instance;

  late final UserController2 _userController;
  late final ErrorHandler _errorHandler;
  late final BackupManager _backupManager;
  late final DataIntegrityValidator _integrityValidator;
  late final StateSyncManager _stateSyncManager;

  BulletproofManager._internal();

  /// Initialize the bulletproof system with required dependencies.
  Future<void> initialize() async {
    _errorHandler = ErrorHandler();
    _userController = UserController2();
    _backupManager = BackupManager();
    _integrityValidator = DataIntegrityValidator();
    _stateSyncManager = StateSyncManager();

    // Initialize all managers with dependencies
    _backupManager.initialize(_userController, _errorHandler);
    _integrityValidator.initialize(_userController, _errorHandler);
    _stateSyncManager.initialize(_userController, _errorHandler);

    _isInitialized = true;
  }

  final _errorController = StreamController<AppError>.broadcast();
  Stream<AppError> get errorStream => _errorController.stream;

  bool _isInitialized = false;
  bool _isDisposed = false;

  /// Dispose of the bulletproof system
  void dispose() {
    if (!_isDisposed) {
      _errorController.close();
      _isDisposed = true;
      debugPrint('✅ Bulletproof system disposed');
    }
  }

  /// Create a backup of the current user data
  Future<void> createBackup(User user) async {
    if (!_isInitialized) {
      throw StateError('Bulletproof system not initialized');
    }

    try {
      // Validate data before backup
      final validationResult = await _integrityValidator.validateUserData(user);
      if (!validationResult.isValid) {
        debugPrint('⚠️ Creating backup with validation issues: ${validationResult.repairs.join(", ")}');
      }

      // Create backup
      await _backupManager.createBackup(user);
      debugPrint('✅ Backup created successfully');
    } catch (e, stackTrace) {
      final error = AppError(
        'Failed to create backup',
        'BACKUP_ERROR',
        {'error': e.toString()},
        stackTrace,
        ErrorSeverity.error,
      );
      await _errorHandler.handleError(e, stackTrace, context: error.message);
      _errorController.add(error);
      rethrow;
    }
  }

  /// Check system health and return status
  Future<SystemCheckResult> checkSystemHealth() async {
    if (!_isInitialized) {
      throw StateError('Bulletproof system not initialized');
    }

    try {
      final user = _userController.user;
      if (user == null) {
        return SystemCheckResult(
          false,
          ['No user data found'],
          [],
        );
      }

      // Validate data integrity
      final integrityResult = await _integrityValidator.validateUserData(user);
      if (!integrityResult.isValid) {
        _errorController.add(AppError(
          'Data integrity issues detected',
          'INTEGRITY_ERROR',
          {'errors': integrityResult.errors},
          StackTrace.current,
          ErrorSeverity.error,
        ));
        return SystemCheckResult(
          false,
          integrityResult.errors,
          integrityResult.repairs,
        );
      }

      return SystemCheckResult(
        true,
        [],
        [],
      );
    } catch (e, stackTrace) {
      final error = AppError(
        'System health check failed',
        'HEALTH_CHECK_ERROR',
        {'error': e.toString()},
        stackTrace,
        ErrorSeverity.error,
      );
      await _errorHandler.handleError(e, stackTrace, context: error.message);
      _errorController.add(error);
      rethrow;
    }
  }

  /// Attempt to repair system issues
  Future<RepairResult> attemptRepair() async {
    if (!_isInitialized) {
      throw StateError('Bulletproof system not initialized');
    }

    try {
      final user = _userController.user;
      if (user == null) {
        // Try to restore from backup
        final username = _userController.user?.username ?? '';
        final restored = await _backupManager.restoreBackup(username);
        if (!restored) {
          return RepairResult(
            false,
            ['Failed to restore from backup'],
          );
        }
      }

      // Attempt data repair
      final repairResult = await _integrityValidator.attemptRepair(user!);
      if (!repairResult.success) {
        _errorController.add(AppError(
          'Data repair failed',
          'REPAIR_ERROR',
          {'errors': repairResult.errors},
          StackTrace.current,
          ErrorSeverity.error,
        ));
        return RepairResult(
          false,
          repairResult.errors,
        );
      }

      return RepairResult(
        true,
        [],
      );
    } catch (e, stackTrace) {
      final error = AppError(
        'System repair failed',
        'REPAIR_ERROR',
        {'error': e.toString()},
        stackTrace,
        ErrorSeverity.error,
      );
      await _errorHandler.handleError(e, stackTrace, context: error.message);
      _errorController.add(error);
      rethrow;
    }
  }

  /// Get current system status
  Future<SystemStatus> getSystemStatus() async {
    if (!_isInitialized) {
      throw StateError('Bulletproof system not initialized');
    }

    try {
      final user = _userController.user;
      if (user == null) {
        return SystemStatus(
          isHealthy: false,
          lastBackup: null,
          errors: ['No user data found'],
          repairs: [],
        );
      }

      // Check data integrity
      final integrityResult = await _integrityValidator.validateUserData(user);
      
      if (!integrityResult.isValid) {
        // Get latest backup info
        final backups = await _backupManager.getAvailableBackups();
        final userBackups = backups.where((b) => b.path.contains('${user.username}_backup')).toList();
        final latestBackup = userBackups.isNotEmpty ? userBackups.first : null;
        
        return SystemStatus(
          isHealthy: false,
          lastBackup: latestBackup,
          errors: integrityResult.errors,
          repairs: integrityResult.repairs,
        );
      }

      // Get latest backup info
      final backups = await _backupManager.getAvailableBackups();
      final userBackups = backups.where((b) => b.path.contains('${user.username}_backup')).toList();
      final latestBackup = userBackups.isNotEmpty ? userBackups.first : null;

      return SystemStatus(
        isHealthy: true,
        lastBackup: latestBackup,
        errors: [],
        repairs: [],
      );
    } catch (e, stackTrace) {
      final error = AppError(
        'Failed to get system status',
        'STATUS_ERROR',
        {'error': e.toString()},
        stackTrace,
        ErrorSeverity.warning,
      );
      await _errorHandler.handleError(e, stackTrace, context: error.message);
      _errorController.add(error);
      rethrow;
    }
  }
}

class SystemCheckResult {
  final bool isHealthy;
  final List<String> errors;
  final List<String> repairs;

  SystemCheckResult(this.isHealthy, this.errors, this.repairs);
}

class RepairResult {
  final bool success;
  final List<String> errors;

  RepairResult(this.success, this.errors);
}

class SystemStatus {
  final bool isHealthy;
  final BackupInfo? lastBackup;
  final List<String> errors;
  final List<String> repairs;

  SystemStatus({
    required this.isHealthy,
    required this.lastBackup,
    required this.errors,
    required this.repairs,
  });
} 