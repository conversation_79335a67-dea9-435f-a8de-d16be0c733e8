// 📁 lib/utils/category_utils.dart

/// The four core built‐in categories in MOL.
const List<String> builtInCategories = [
  'Health',
  'Wealth',
  'Purpose',
  'Connection',
];

/// Returns true if [cat] is one of the core built‐in categories.
bool isBuiltInCategory(String cat) {
  return builtInCategories.contains(cat);
}

/// Returns true if [cat] is a valid EXP category for display. 
/// This rejects any "N.S. – " (North Star) prefixed categories.
bool isValidExpCategory(String cat) {
  if (cat.trim().toLowerCase().startsWith('n.s.')) return false;
  return true;
}

/// Filters out ANY category that:
///  • Is not built‐in AND not in [customCategories], OR
///  • Starts with "N.S." (North Star scraps), OR
///  • Has zero or negative EXP, OR
///  • Is literally "Unknown".
///
/// [allCategoriesMap] represents the user's `Map<String,int> categories`.
/// [customCategories] is the user's `Map<String,int> customCategories`.
List<String> getDisplayCategories(
  Map<String, int> allCategoriesMap,
  Map<String, int> customCategories,
) {
  final List<String> valid = [];

  // Step 1: gather any built‐in that also has strictly positive EXP, is not NS, and not "Unknown".
  for (final builtin in builtInCategories) {
    if (allCategoriesMap.containsKey(builtin) &&
        isValidExpCategory(builtin) &&
        builtin != 'Unknown' &&                       // ← filter out "Unknown"
        (allCategoriesMap[builtin] != null && allCategoriesMap[builtin]! > 0)) {            // ← only strictly positive
      valid.add(builtin);
    }
  }

  // Step 2: gather any custom categories that exist, are strictly positive, not NS‐prefixed, and not "Unknown".
  for (final custom in customCategories.keys) {
    if (allCategoriesMap.containsKey(custom) &&
        isValidExpCategory(custom) &&
        custom != 'Unknown' &&                        // ← filter out "Unknown"
        (allCategoriesMap[custom]! > 0)) {             // ← only strictly positive
      valid.add(custom);
    }
  }

  return valid;
}

/// Returns EXP sorted (descending) for UI breakdowns.
/// Filters out "N.S." categories and zero/negative or "Unknown" automatically.
List<MapEntry<String, int>> sortCategoriesDescending(
  Map<String, int> categoriesMap,
  Map<String, int> customCategories,
) {
  final displayCats = getDisplayCategories(categoriesMap, customCategories);
  final filteredEntries = categoriesMap.entries
      .where((e) => displayCats.contains(e.key))
      .toList();

  filteredEntries.sort((a, b) => b.value.compareTo(a.value));
  return filteredEntries;
}
