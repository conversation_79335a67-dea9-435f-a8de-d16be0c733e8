import 'dart:developer' as developer;

/// Debug logging utility for the MXD app
/// 
/// Provides structured logging with categories and conditional output
/// based on debug mode. Helps track system behavior and identify
/// failure points during development and testing.
class DebugLogger {
  static const bool _debugMode = true; // Set to false for production
  
  /// Log a debug message with category and content
  /// 
  /// [category] - The system/component generating the log
  /// [message] - The log message content
  /// [data] - Optional additional data to include
  static void log(String category, String message, [dynamic data]) {
    if (!_debugMode) return;
    
    final timestamp = DateTime.now().toIso8601String();
    final logMessage = '[$timestamp] [$category] $message';
    
    if (data != null) {
      developer.log('$logMessage\nData: $data', name: category);
    } else {
      developer.log(logMessage, name: category);
    }
  }
  
  /// Log an error with stack trace
  static void error(String category, String message, [dynamic error, StackTrace? stackTrace]) {
    if (!_debugMode) return;
    
    final timestamp = DateTime.now().toIso8601String();
    final logMessage = '[$timestamp] [ERROR] [$category] $message';
    
    developer.log(
      logMessage,
      name: category,
      error: error,
      stackTrace: stackTrace,
    );
  }
  
  /// Log a warning message
  static void warn(String category, String message, [dynamic data]) {
    if (!_debugMode) return;
    
    final timestamp = DateTime.now().toIso8601String();
    final logMessage = '[$timestamp] [WARN] [$category] $message';
    
    if (data != null) {
      developer.log('$logMessage\nData: $data', name: category);
    } else {
      developer.log(logMessage, name: category);
    }
  }
  
  /// Log system health check
  static void health(String category, String component, bool isHealthy, [String? details]) {
    if (!_debugMode) return;
    
    final status = isHealthy ? 'HEALTHY' : 'UNHEALTHY';
    final timestamp = DateTime.now().toIso8601String();
    final logMessage = '[$timestamp] [HEALTH] [$category] $component: $status';
    
    if (details != null) {
      developer.log('$logMessage - $details', name: category);
    } else {
      developer.log(logMessage, name: category);
    }
  }
  
  /// Log performance metrics
  static void performance(String category, String operation, Duration duration, [Map<String, dynamic>? metrics]) {
    if (!_debugMode) return;
    
    final timestamp = DateTime.now().toIso8601String();
    final logMessage = '[$timestamp] [PERF] [$category] $operation: ${duration.inMilliseconds}ms';
    
    if (metrics != null) {
      developer.log('$logMessage\nMetrics: $metrics', name: category);
    } else {
      developer.log(logMessage, name: category);
    }
  }
}
