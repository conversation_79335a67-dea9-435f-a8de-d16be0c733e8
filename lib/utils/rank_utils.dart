// 📁 lib/utils/rank_utils.dart
// ignore_for_file: unintended_html_in_doc_comment

import 'dart:math';

/// =================================================================================
/// CORE CONSTANTS: Shared between LevelUtils and RankUtils
/// =================================================================================

/// EXP required to gain one “unit” of Level or Rank (100 EXP = 1 Level/Rank).
const int kExpPerLevel = 100;

/// Maximum Level/Rank cap (100).
const int kMaxLevel = 100;

/// Maximum EXP cap overall (100 * 100 = 10,000).
const int kMaxExp = kExpPerLevel * kMaxLevel;

/// =================================================================================
/// PRIVATE HELPER: Compute Level/Rank from EXP
///
///   • If [isOneBased] == true:
///       0–99 EXP  → Level/Rank 1
///       100–199   → Level/Rank 2
///       ...
///       ≥ 10,000  → Level/Rank 100
///
///   • If [isOneBased] == false:
///       0–99 EXP  → Level 0
///       100–199   → Level 1
///       ...
///       ≥ 10,000  → Level 100
/// =================================================================================
int _computeLevel(int exp, {required bool isOneBased}) {
  final clamped = exp.clamp(0, kMaxExp);
  if (clamped >= kMaxExp) return kMaxLevel;
  final raw = clamped ~/ kExpPerLevel;
  if (isOneBased) {
    // 0–99 → 1, 100–199 → 2, ...
    return (raw + 1).clamp(1, kMaxLevel);
  } else {
    // 0–99 → 0, 100–199 → 1, ...
    return raw.clamp(0, kMaxLevel);
  }
}

/// =================================================================================
/// SECTION 1: NORTH STAR QUEST RANK UTILITIES
/// =================================================================================

/// “RankUtils” handles all North Star Quest ranking logic:
///   • Converts hours → EXP (10 EXP per hour)
///   • Converts EXP → NS Rank Level (1…100)
///   • Converts EXP → NS Rank Name (“Initiate 1”, “Master 2”, … “Undeniable Supremacy of the Domain”)
///   • Reports EXP to next NS Rank and progress percentage toward next NS Rank.
class RankUtils {
  /// Maximum NS EXP cap (same as kMaxExp = 10,000).
  static const int maxExp = kMaxExp;

  /// Maximum NS Rank level (100).
  static const int maxLevel = kMaxLevel;

  /// EXP required per NS Rank level (100).
  static const int expPerRank = kExpPerLevel;

  /// Converts a fractional [hours] value into EXP.
  /// Each hour = 10 EXP. Rounds to nearest integer and clamps ≥ 0.
  static int expFromHours(double hours) {
    final rawExp = (hours * 10).round();
    return max(0, rawExp);
  }

  /// Given total EXP, returns the current NS Rank level (1…100).
  /// 0–99 EXP  → NS Rank 1
  /// 100–199   → NS Rank 2
  /// ...
  /// ≥ 10,000  → NS Rank 100
  static int determineRankLevel(int exp) {
    return _computeLevel(exp, isOneBased: true);
  }

  /// Given total [hours], converts → EXP → returns NS Rank level (1…100).
  static int determineRankLevelFromHours(double hours) {
    final exp = expFromHours(hours);
    return determineRankLevel(exp);
  }

  /// Returns a human‐readable NS Rank name based on total EXP.
  ///
  /// Ranges (using placeholders for level values):
  ///   1–9   → “Initiate <level>”
  ///   10–19 → “Master <level - 9>”
  ///   20–29 → “Sage <level - 19>”
  ///   30–39 → “Wizard <level - 29>”
  ///   40–49 → “Prophet <level - 39>”
  ///   50–59 → “Mystic <level - 49>”
  ///   60–69 → “Divine <level - 59>”
  ///   70–79 → “Transcendent <level - 69>”
  ///   80–89 → “Immortal <level - 79>”
  ///   90–99 → “Omnipotent <level - 89>”
  ///   100   → “Undeniable Supremacy of the Domain”

  static String getRankName(int exp) {
    final clamped = exp.clamp(0, maxExp);
    if (clamped >= maxExp) {
      return 'Undeniable Supremacy of the Domain';
    }

    final lvl = determineRankLevel(clamped);
    if (lvl <= 9) {
      return 'Initiate $lvl';
    } else if (lvl <= 19) {
      return 'Master ${lvl - 9}';
    } else if (lvl <= 29) {
      return 'Sage ${lvl - 19}';
    } else if (lvl <= 39) {
      return 'Wizard ${lvl - 29}';
    } else if (lvl <= 49) {
      return 'Prophet ${lvl - 39}';
    } else if (lvl <= 59) {
      return 'Mystic ${lvl - 49}';
    } else if (lvl <= 69) {
      return 'Divine ${lvl - 59}';
    } else if (lvl <= 79) {
      return 'Transcendent ${lvl - 69}';
    } else if (lvl <= 89) {
      return 'Immortal ${lvl - 79}';
    } else if (lvl <= 99) {
      return 'Omnipotent ${lvl - 89}';
    }

    // lvl == 100 handled above
    return 'Unranked';
  }

  /// Given total [hours], returns human‐readable NS Rank name.
  static String getRankNameFromHours(double hours) {
    final exp = expFromHours(hours);
    return getRankName(exp);
  }

  /// Calculates how much EXP is needed to reach the next NS Rank level.
  /// If already at or above maxExp (10,000), returns 0.
  ///
  /// Example: exp = 250 → current NS Rank = 3 (0–99→1,100–199→2,200–299→3)
  /// nextLevelExp = 3 * 100 = 300 → needed = 300 – 250 = 50.
  static int getExpToNextRank(int exp) {
    final clamped = exp.clamp(0, maxExp);
    if (clamped >= maxExp) return 0;
    final lvl = determineRankLevel(clamped);
    final nextExp = lvl * expPerRank;
    return max(0, min(nextExp, maxExp) - clamped);
  }

  /// Given total [hours], returns EXP needed to reach next NS Rank.
  static int getExpToNextRankFromHours(double hours) {
    final exp = expFromHours(hours);
    return getExpToNextRank(exp);
  }

  /// Returns a progress percentage (0.0–1.0) toward the next NS Rank based on EXP.
  /// If exp ≥ maxExp, returns 1.0.
  static double getProgressPercent(int exp) {
    final clamped = exp.clamp(0, maxExp);
    if (clamped >= maxExp) return 1.0;
    final lvl = determineRankLevel(clamped);
    final minForThis = (lvl - 1) * expPerRank;
    final maxForThis = lvl * expPerRank;
    final range = maxForThis - minForThis;
    if (range <= 0) return 1.0;
    return (clamped - minForThis) / range;
  }

  /// Given total [hours], returns NS Rank progress percentage (0.0–1.0).
  static double getProgressPercentFromHours(double hours) {
    final exp = expFromHours(hours);
    return getProgressPercent(exp);
  }

  /// A direct lookup map from NS Rank level (1–100) to its human‐readable name.
  static final Map<int, String> rankLevelNames = {
    for (int i = 1; i <= 9; i++)    i: 'Initiate $i',
    for (int i = 10; i <= 19; i++)  i: 'Master ${i - 9}',
    for (int i = 20; i <= 29; i++)  i: 'Sage ${i - 19}',
    for (int i = 30; i <= 39; i++)  i: 'Wizard ${i - 29}',
    for (int i = 40; i <= 49; i++)  i: 'Prophet ${i - 39}',
    for (int i = 50; i <= 59; i++)  i: 'Mystic ${i - 49}',
    for (int i = 60; i <= 69; i++)  i: 'Divine ${i - 59}',
    for (int i = 70; i <= 79; i++)  i: 'Transcendent ${i - 69}',
    for (int i = 80; i <= 89; i++)  i: 'Immortal ${i - 79}',
    for (int i = 90; i <= 99; i++)  i: 'Omnipotent ${i - 89}',
    100: 'Undeniable Supremacy of the Domain',
  };

  /// Given a known NS Rank [level] (1–100), returns its human‐readable name.
  static String getRankNameForLevel(int level) {
    final clampedLevel = level.clamp(1, maxLevel);
    return rankLevelNames[clampedLevel] ?? 'Unranked';
  }
}

/// =================================================================================
/// SECTION 2: OVERALL USER LEVEL UTILITIES (renamed to “LevelUtils”)
/// =================================================================================

/// “LevelUtils” handles the user’s global leveling logic:
///   • Converts total EXP → overall Level (0…100)
///   • Converts EXP → Level Name (“Bronze 1”, “Silver 2”, … “Undeniable Supremacy”)
///   • Reports EXP to next Level and progress percentage toward next Level.
class LevelUtils {
  /// EXP required to gain one overall Level (100).
  static const int expPerLevel = kExpPerLevel;

  /// Maximum overall Level (100).
  static const int maxLevel = kMaxLevel;

  /// Maximum overall EXP cap (10,000).
  static const int maxExp = kMaxExp;

  /// Given total EXP, returns the overall user Level (0…100).
  /// 0–99 EXP  → Level 0
  /// 100–199   → Level 1
  /// ...
  /// ≥ 10,000  → Level 100
  static int getRankLevel(int exp) {
    return _computeLevel(exp, isOneBased: false);
  }

  /// Calculates how much EXP is needed to reach the next overall Level.
  /// If already at or above maxExp (10,000), returns 0.
  ///
  /// Example: exp = 250 → current Level = 2 (0–99→0,100–199→1,200–299→2)
  /// nextLevelExp = (2 + 1) * 100 = 300 → needed = 300 – 250 = 50.
  static int getExpToNextRank(int exp) {
    final clamped = exp.clamp(0, maxExp);
    if (clamped >= maxExp) return 0;
    final lvl = getRankLevel(clamped);
    final nextExp = (lvl + 1) * expPerLevel;
    return max(0, min(nextExp, maxExp) - clamped);
  }

  /// Returns a progress percentage (0.0–1.0) toward the next overall Level.
  /// If exp ≥ maxExp, returns 1.0.
  static double getRankProgressPct(int exp) {
    final clamped = exp.clamp(0, maxExp);
    if (clamped >= maxExp) return 1.0;
    final lvl = getRankLevel(clamped);
    final minForThis = lvl * expPerLevel;
    final maxForThis = (lvl + 1) * expPerLevel;
    final range = maxForThis - minForThis;
    if (range <= 0) return 1.0;
    return (clamped - minForThis) / range;
  }

  /// Returns a human‐readable overall Level name for given EXP.
  /// Map:
  ///   0–99 EXP   → “Bronze 1”
  ///   100–199    → “Bronze 2”
  ///   200–299    → “Bronze 3”
  ///   300–399    → “Silver 1”
  ///   400–499    → “Silver 2”
  ///   500–599    → “Silver 3”
  ///   600–699    → “Gold 1”
  ///   … and so on, up to Level 100 → “Undeniable Supremacy”.
  static String getRankName(int exp) {
    final lvl = getRankLevel(exp);
    // If lvl = 0, show “Unranked” or map to Bronze 1? Adjust as needed.
    if (lvl <= 0) return 'Unranked';
    return _levelNameMap[lvl] ?? 'Unranked';
  }

  /// Converts a known Level [level] (1–100) into its human‐readable Level name.
  static String getRankNameForLevel(int level) {
    final clampedLevel = level.clamp(1, maxLevel);
    return _levelNameMap[clampedLevel] ?? 'Unranked';
  }

  /// Direct map of overall Level (1–100) to its human‐readable name.
  static const Map<int, String> _levelNameMap = {
    1: 'Bronze 1',
    2: 'Bronze 2',
    3: 'Bronze 3',
    4: 'Silver 1',
    5: 'Silver 2',
    6: 'Silver 3',
    7: 'Gold 1',
    8: 'Gold 2',
    9: 'Gold 3',
    10: 'Platinum 1',
    11: 'Platinum 2',
    12: 'Platinum 3',
    13: 'Diamond 1',
    14: 'Diamond 2',
    15: 'Diamond 3',
    16: 'Master 1',
    17: 'Master 2',
    18: 'Master 3',
    19: 'Grandmaster 1',
    20: 'Grandmaster 2',
    21: 'Grandmaster 3',
    22: 'Ethereal 1',
    23: 'Ethereal 2',
    24: 'Ethereal 3',
    25: 'Mythic 1',
    26: 'Mythic 2',
    27: 'Mythic 3',
    28: 'Legendary 1',
    29: 'Legendary 2',
    30: 'Legendary 3',
    31: 'Ascendant 1',
    32: 'Ascendant 2',
    33: 'Ascendant 3',
    34: 'Supreme 1',
    35: 'Supreme 2',
    36: 'Supreme 3',
    37: 'Divine 1',
    38: 'Divine 2',
    39: 'Divine 3',
    40: 'Transcendent 1',
    41: 'Transcendent 2',
    42: 'Transcendent 3',
    43: 'Immortal 1',
    44: 'Immortal 2',
    45: 'Immortal 3',
    46: 'Omnipotent 1',
    47: 'Omnipotent 2',
    48: 'Omnipotent 3',
    49: 'Supremacy 1',
    50: 'Supremacy 2',
    51: 'Supremacy 3',
    52: 'Oversoul 1',
    53: 'Oversoul 2',
    54: 'Oversoul 3',
    55: 'Primordial 1',
    56: 'Primordial 2',
    57: 'Primordial 3',
    58: 'Transcendent 4',
    59: 'Transcendent 5',
    60: 'Transcendent 6',
    61: 'Divine 4',
    62: 'Divine 5',
    63: 'Divine 6',
    64: 'Immortal 4',
    65: 'Immortal 5',
    66: 'Immortal 6',
    67: 'Omnipotent 4',
    68: 'Omnipotent 5',
    69: 'Omnipotent 6',
    70: 'Transcendent 7',
    71: 'Transcendent 8',
    72: 'Transcendent 9',
    73: 'Immortal 7',
    74: 'Immortal 8',
    75: 'Immortal 9',
    76: 'Omnipotent 7',
    77: 'Omnipotent 8',
    78: 'Omnipotent 9',
    79: 'Supreme 4',
    80: 'Supreme 5',
    81: 'Supreme 6',
    82: 'Supremacy 4',
    83: 'Supremacy 5',
    84: 'Supremacy 6',
    85: 'Ethereal 4',
    86: 'Ethereal 5',
    87: 'Ethereal 6',
    88: 'Legendary 4',
    89: 'Legendary 5',
    90: 'Legendary 6',
    91: 'Ascendant 4',
    92: 'Ascendant 5',
    93: 'Ascendant 6',
    94: 'Mythic 4',
    95: 'Mythic 5',
    96: 'Mythic 6',
    97: 'Primordial 4',
    98: 'Primordial 5',
    99: 'Primordial 6',
    100: 'Undeniable Supremacy',
  };
}
