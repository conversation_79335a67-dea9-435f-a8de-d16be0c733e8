// 📁 lib/utils/dperfecter_util.dart

//import 'package:uuid/uuid.dart';
import '../models/user_model.dart';


/// A single item to display in the unified diary feed.
/// Includes both standard DiaryEntry items and NorthStarQuest logs.
class FeedItem {
  /// Unique identifier for deduplication (e.g. timestamp + note hash).
  final String id;

  /// When this entry occurred.
  final DateTime timestamp;

  /// The category under which this entry was logged.
  final String category;

  /// The note or description for this entry.
  /// For North-Star entries, will be formatted as:
  ///   "[N.S.] Original Note"
  final String note;

  /// True if this entry originated from a North-Star quest log.
  final bool isNorthStar;

  FeedItem({
    required this.id,
    required this.timestamp,
    required this.category,
    required this.note,
    required this.isNorthStar,
  });
}

class DiaryPerfecterUtil {
  //static const _uuid = Uuid();

  /// Generates a unified, de-duplicated, and timestamp-sorted list of FeedItem
  /// from a given [user]. This merges both:
  ///   1) user.diary (List of DiaryEntry) and
  ///   2) user.northStarQuest.logs (List of NorthStarLog)
  /// Any exact-duplicate (same timestamp + note) appears only once (first occurrence).
  ///
  /// Newest entries (by timestamp) appear first in the returned list.
  static List<FeedItem> generateFeed(User user) {
    print('📝 DiaryPerfecterUtil: Generating feed for user ${user.username}');
    print('📝 Diary entries count: ${user.diaryEntries.length}');
    print('📝 North Star logs count: ${user.northStarQuest?.logs.length ?? 0}');

    final Map<String, FeedItem> seen = {};
    final List<FeedItem> result = [];

    // 1) Process all existing DiaryEntry objects
    for (final entry in user.diaryEntries) {
      final ts = entry.timestamp;
      final note = entry.note;
      final category = entry.category;
      final isNS = note.startsWith('[N.S.]') || note.contains('[N.S. -');

      // Create a normalized content key for better duplicate detection
      String normalizedNote = note;
      if (note.contains('[N.S. - ')) {
        // Extract the actual content after "[N.S. - Category] "
        final match = RegExp(r'^\[N\.S\. - .*?\]\s*(.*)').firstMatch(note);
        if (match != null) {
          normalizedNote = match.group(1) ?? note;
        }
      } else if (note.startsWith('[N.S.]')) {
        normalizedNote = note.substring(6).trim();
      }

      // Build a stable unique key: category + normalized content + rough timestamp
      final timeKey = '${ts.year}-${ts.month.toString().padLeft(2, '0')}-${ts.day.toString().padLeft(2, '0')}-${ts.hour.toString().padLeft(2, '0')}-${ts.minute.toString().padLeft(2, '0')}';
      final contentKey = '${category}_${normalizedNote.hashCode}';
      final key = '${timeKey}_$contentKey';

      if (!seen.containsKey(key)) {
        final item = FeedItem(
          id: entry.id, // Use original entry ID
          timestamp: ts,
          category: category,
          note: note,
          isNorthStar: isNS,
        );
        seen[key] = item;
        result.add(item);
        print('📝 Added diary entry: $category - ${note.substring(0, note.length > 50 ? 50 : note.length)}... (key: $key)');
      } else {
        print('📝 Skipped duplicate diary entry: $key');
      }
    }

    // 2) Process NorthStarLog entries (to catch any orphans)
    final quest = user.northStarQuest;
    if (quest != null) {
      for (final log in quest.logs) {
        final ts = log.loggedAt;
        final rawEntryText = log.entryText;

        // (A) Determine the category from the log's entryText via its getter:
        final logCategory = log.category;

        // (B) Strip away "[N.S. - <Category>]" if present,
        //     otherwise strip "[N.S.]" if that pattern is found.
        String bareOriginalNote;
        final bracketedPattern = RegExp(r'^\[N\.S\. - .*?\]\s*');
        if (bracketedPattern.hasMatch(rawEntryText)) {
          bareOriginalNote = rawEntryText.replaceFirst(bracketedPattern, '').trim();
        } else if (rawEntryText.startsWith('[N.S.]')) {
          bareOriginalNote = rawEntryText.substring(6).trim();
        } else {
          bareOriginalNote = rawEntryText;
        }

        // (C) Build the feed note exactly as "[N.S.] <bareOriginalNote>"
        final note = '[N.S.] $bareOriginalNote';

        // Use the same deduplication logic as diary entries
        final timeKey = '${ts.year}-${ts.month.toString().padLeft(2, '0')}-${ts.day.toString().padLeft(2, '0')}-${ts.hour.toString().padLeft(2, '0')}-${ts.minute.toString().padLeft(2, '0')}';
        final contentKey = '${logCategory}_${bareOriginalNote.hashCode}';
        final key = '${timeKey}_$contentKey';

        if (!seen.containsKey(key)) {
          final item = FeedItem(
            id: log.id, // Use original log ID
            timestamp: ts,
            category: logCategory,
            note: note,
            isNorthStar: true,
          );
          seen[key] = item;
          result.add(item);
          print('📝 Added North Star log: $logCategory - ${note.substring(0, note.length > 50 ? 50 : note.length)}... (key: $key)');
        } else {
          print('📝 Skipped duplicate North Star log: $key');
        }
      }
    }

    // 3) Sort descending by timestamp (newest first)
    result.sort((a, b) => b.timestamp.compareTo(a.timestamp));

    print('📝 Final feed generated: ${result.length} total items');
    print('📝 Unique keys tracked: ${seen.length}');

    return result;
  }
}
