import 'package:flutter/widgets.dart';
import '../utils/route_observer.dart';

/// A widget that triggers a refresh callback whenever its route gains focus.
///
/// Use this to reload data from disk or call Provider refresh logic when
/// returning to a screen or when it's first pushed.
///
/// Example:
/// ```dart
/// RefreshOnFocus(
///   onFocus: () async {
///     await context.read<UserController>().refreshFromDisk();
///   },
///   child: HomeScreen3(),
/// )
/// ```
class RefreshOnFocus extends StatefulWidget {
  /// The widget subtree to display.
  final Widget child;
  
  /// Called when the route is first pushed and every time it's uncovered.
  final Future<void> Function()? onFocus;

  const RefreshOnFocus({
    super.key,
    required this.child,
    this.onFocus,
  });

  @override
  // ignore: library_private_types_in_public_api
  _RefreshOnFocusState createState() => _RefreshOnFocusState();
}

class _RefreshOnFocusState extends State<RefreshOnFocus> with RouteAware {
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final route = ModalRoute.of(context);
    if (route is PageRoute) {
      routeObserver.subscribe(this, route);
    }
  }

  @override
  void dispose() {
    routeObserver.unsubscribe(this);
    super.dispose();
  }

  @override
  void didPush() {
    _invokeOnFocusDelayed();
  }

  @override
  void didPopNext() {
    _invokeOnFocusDelayed();
  }

  void _invokeOnFocusDelayed() {
    // Use post-frame callback to avoid setState during build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted && widget.onFocus != null) {
        widget.onFocus!().catchError((e, stack) {
          // Handle errors gracefully
          debugPrint('Error in RefreshOnFocus onFocus: $e');
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}
