
//---------------------------
// 📁 lib/utils/audio_popup_utils.dart

import 'package:flutter/material.dart';
import 'package:audioplayers/audioplayers.dart';

class AudioPopupUtils {
  static final _audioPlayer = AudioPlayer();

  /// Play a sound file from assets/sounds/
  static Future<void> playSound(String filename) async {
    try {
      await _audioPlayer.play(AssetSource('sounds/$filename'));
    } catch (e) {
      debugPrint("❌ Error playing sound: $filename — $e");
    }
  }

  /// Show a dark-themed success popup with confirmation
  static Future<void> showSuccessPopup(BuildContext context, String message) async {
    await playSound('success.mp3');
    await showDialog(
      // ignore: use_build_context_synchronously
      context: context,
      builder: (_) => AlertDialog(
        backgroundColor: Colors.black,
        title: const Text("✅ success", style: TextStyle(color: Colors.white)),
        content: Text(message, style: const TextStyle(color: Colors.white70)),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text("OK", style: TextStyle(color: Colors.amber)),
          ),
        ],
      ),
    );
  }

  /// Show a dark-themed error popup with error sound
  static Future<void> showErrorPopup(BuildContext context, String message) async {
    await playSound('HSError2.mp3');
    await showDialog(
      // ignore: use_build_context_synchronously
      context: context,
      builder: (_) => AlertDialog(
        backgroundColor: Colors.black,
        title: const Text("❌ Error", style: TextStyle(color: Colors.redAccent)),
        content: Text(message, style: const TextStyle(color: Colors.white70)),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text("OK", style: TextStyle(color: Colors.redAccent)),
          ),
        ],
      ),
    );
  }
}
