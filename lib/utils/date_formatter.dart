import 'package:intl/intl.dart';

/// Utility class for formatting dates and times consistently across the app
class DateFormatter {
  static final _dateFormat = DateFormat('MMM d, yyyy');
  static final _dateTimeFormat = DateFormat('MMM d, yyyy h:mm a');

  /// Format a DateTime to a human-readable date string (e.g., "Jan 1, 2024")
  static String formatDate(DateTime date) => _dateFormat.format(date);

  /// Format a DateTime to a human-readable date and time string (e.g., "Jan 1, 2024 3:45 PM")
  static String formatDateTime(DateTime dateTime) => _dateTimeFormat.format(dateTime);

  /// Format a DateTime to an ISO 8601 string for storage
  static String toIso8601(DateTime dateTime) => dateTime.toIso8601String();

  /// Parse an ISO 8601 string to a DateTime
  static DateTime fromIso8601(String isoString) => DateTime.parse(isoString);
}
