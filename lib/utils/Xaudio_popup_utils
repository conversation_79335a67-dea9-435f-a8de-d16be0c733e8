// 📁 lib/utils/audio_popup_utils.dart

import 'package:flutter/material.dart';
import 'package:audioplayers/audioplayers.dart';

/// Audio + popup utilities. Each call creates its own AudioPlayer
/// to avoid “file locked” errors on Windows/macOS.
class AudioPopupUtils {
  /// Play a single sound from assets/sounds/.
  ///
  /// Example: playSound('success.mp3') will load "assets/sounds/success.mp3".
  static Future<void> playSound(String filename) async {
    final player = AudioPlayer();
    try {
      // IMPORTANT: do NOT include "assets/" here—just "sounds/..."
      await player.play(AssetSource('sounds/$filename'));

      // Dispose when playback completes so the temp file is released.
      player.onPlayerComplete.listen((_) {
        player.dispose();
      });
    } catch (e) {
      debugPrint("❌ Error playing sound: $filename — $e");
      player.dispose();
    }
  }

  /// Show a dark‐themed success dialog, playing “success.mp3” first.
  static Future<void> showSuccessPopup(
      BuildContext context, String message) async {
    // Plays: assets/sounds/success.mp3
    await playSound('success.mp3');

    await showDialog(
      context: context,
      builder: (_) => AlertDialog(
        backgroundColor: Colors.black,
        title: const Text(
          "✅ Success",
          style: TextStyle(color: Colors.white),
        ),
        content: Text(
          message,
          style: const TextStyle(color: Colors.white70),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text("OK", style: TextStyle(color: Colors.amber)),
          ),
        ],
      ),
    );
  }

  /// Show a dark‐themed error dialog, playing “HSError2.mp3” first.
  static Future<void> showErrorPopup(
      BuildContext context, String message) async {
    // Plays: assets/sounds/HSError2.mp3
    await playSound('HSError2.mp3');

    await showDialog(
      context: context,
      builder: (_) => AlertDialog(
        backgroundColor: Colors.black,
        title: const Text(
          "❌ Error",
          style: TextStyle(color: Colors.redAccent),
        ),
        content: Text(
          message,
          style: const TextStyle(color: Colors.white70),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text("OK", style: TextStyle(color: Colors.redAccent)),
          ),
        ],
      ),
    );
  }

  /// Show a dark‐themed “lightning” dialog, playing “lightning.mp3” first.
  ///
  /// Can be called whenever you want a flashy rank-up effect.
  static Future<void> showLightningPopup(
      BuildContext context, String message) async {
    // Plays: assets/sounds/lightning.mp3
    await playSound('lightning.mp3');

    await showDialog(
      context: context,
      builder: (_) => AlertDialog(
        backgroundColor: Colors.black,
        title: const Text(
          "⚡ Level Up!",
          style: TextStyle(color: Colors.cyanAccent),
        ),
        content: Text(
          message,
          style: const TextStyle(color: Colors.white70),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text(
              "OK",
              style: TextStyle(color: Color.fromARGB(255, 253, 0, 0)),
            ),
          ),
        ],
      ),
    );
  }
}