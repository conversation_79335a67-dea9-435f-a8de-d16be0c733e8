import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';

/// Utility functions for file operations
class FileUtils {
  /// Save a file to the app's documents directory
  static Future<File> saveToDocuments(String fileName, String content) async {
    final directory = await getApplicationDocumentsDirectory();
    final file = File('${directory.path}/$fileName');
    return await file.writeAsString(content);
  }
  
  /// Export a file to the device's Downloads folder (Android) or Documents (iOS)
  static Future<bool> exportToDownloads(String fileName, String content) async {
    try {
      if (Platform.isAndroid) {
        // Request storage permission
        if (!await Permission.storage.request().isGranted) {
          return false;
        }
        
        // Get downloads directory
        final directory = Directory('/storage/emulated/0/Download');
        if (!await directory.exists()) {
          await directory.create(recursive: true);
        }
        
        final file = File('${directory.path}/$fileName');
        await file.writeAsString(content);
        return true;
      } else if (Platform.isIOS) {
        // For iOS, we'll save to documents and then share
        final directory = await getApplicationDocumentsDirectory();
        final file = File('${directory.path}/$fileName');
        await file.writeAsString(content);
        return true;
      }
      return false;
    } catch (e) {
      print('Error exporting file: $e');
      return false;
    }
  }
}