// lib/screens/email_signup_testing_dashboard.dart

import 'package:flutter/material.dart';
import 'package:maxed_out_life/services/signup_flow_validator.dart';
import 'package:maxed_out_life/services/email_delivery_testing_service.dart';
import 'package:maxed_out_life/services/klaviyo_service.dart';
import 'package:maxed_out_life/services/comprehensive_logging_service.dart';

/// 🔧 Email Signup Testing Dashboard
/// 
/// Comprehensive testing interface for validating email signup system
/// before App Store deployment. Provides real-time testing, monitoring,
/// and validation of all signup components.
/// 
/// Features:
/// - Complete signup flow testing
/// - Email delivery validation
/// - Klaviyo API integration testing
/// - Real-time monitoring dashboard
/// - App Store readiness assessment
/// - Detailed test reports
class EmailSignupTestingDashboard extends StatefulWidget {
  const EmailSignupTestingDashboard({super.key});

  @override
  State<EmailSignupTestingDashboard> createState() => _EmailSignupTestingDashboardState();
}

class _EmailSignupTestingDashboardState extends State<EmailSignupTestingDashboard> {
  final SignupFlowValidator _signupValidator = SignupFlowValidator();
  final EmailDeliveryTestingService _emailTesting = EmailDeliveryTestingService();
  
  bool _isInitialized = false;
  bool _isTesting = false;
  SignupFlowValidationResult? _lastValidationResult;
  EmailDeliveryTestResult? _lastDeliveryResult;
  Map<String, dynamic>? _klaviyoHealth;

  @override
  void initState() {
    super.initState();
    _initializeServices();
  }

  Future<void> _initializeServices() async {
    try {
      await _signupValidator.initialize();
      await _emailTesting.initialize();
      await _updateKlaviyoHealth();
      
      setState(() {
        _isInitialized = true;
      });
    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Failed to initialize testing services: $e');
    }
  }

  Future<void> _updateKlaviyoHealth() async {
    try {
      final health = await KlaviyoService.getServiceHealth();
      setState(() {
        _klaviyoHealth = health;
      });
    } catch (e) {
      setState(() {
        _klaviyoHealth = {'isHealthy': false, 'error': e.toString()};
      });
    }
  }

  Future<void> _runComprehensiveTest() async {
    if (_isTesting) return;

    setState(() {
      _isTesting = true;
    });

    try {
      await ComprehensiveLoggingService.logInfo('🚀 Starting comprehensive email signup test');

      // Generate test data
      final testData = SignupTestData(
        email: 'test.${DateTime.now().millisecondsSinceEpoch}@example.com',
        username: 'testuser${DateTime.now().millisecondsSinceEpoch}',
        password: 'SecurePassword123!',
        gender: 'male',
      );

      // Run signup flow validation
      final validationResult = await _signupValidator.validateCompleteSignupFlow(testData);
      
      // Run email delivery test
      final deliveryResult = await _emailTesting.runComprehensiveDeliveryTest();

      // Update Klaviyo health
      await _updateKlaviyoHealth();

      setState(() {
        _lastValidationResult = validationResult;
        _lastDeliveryResult = deliveryResult;
      });

      await ComprehensiveLoggingService.logInfo('✅ Comprehensive email signup test completed');

    } catch (e) {
      await ComprehensiveLoggingService.logError('❌ Comprehensive test failed: $e');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Test failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isTesting = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        title: const Text(
          'EMAIL SIGNUP TESTING',
          style: TextStyle(
            fontFamily: 'Pirulen',
            fontSize: 18,
            color: Colors.cyanAccent,
          ),
        ),
        backgroundColor: Colors.black,
        elevation: 0,
      ),
      body: !_isInitialized
          ? const Center(
              child: CircularProgressIndicator(color: Colors.cyanAccent),
            )
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header Section
                  _buildHeaderSection(),
                  const SizedBox(height: 24),
                  
                  // Quick Actions
                  _buildQuickActionsSection(),
                  const SizedBox(height: 24),
                  
                  // System Health Overview
                  _buildSystemHealthSection(),
                  const SizedBox(height: 24),
                  
                  // Test Results
                  if (_lastValidationResult != null) ...[
                    _buildValidationResultsSection(),
                    const SizedBox(height: 24),
                  ],
                  
                  if (_lastDeliveryResult != null) ...[
                    _buildDeliveryResultsSection(),
                    const SizedBox(height: 24),
                  ],
                  
                  // App Store Readiness Assessment
                  _buildAppStoreReadinessSection(),
                ],
              ),
            ),
    );
  }

  Widget _buildHeaderSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.cyanAccent.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '🔧 EMAIL SIGNUP TESTING DASHBOARD',
            style: TextStyle(
              fontFamily: 'Pirulen',
              fontSize: 16,
              color: Colors.cyanAccent,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Comprehensive validation system for App Store readiness',
            style: TextStyle(
              fontFamily: 'Bitsumishi',
              fontSize: 14,
              color: Colors.white70,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Icon(
                _isInitialized ? Icons.check_circle : Icons.pending,
                color: _isInitialized ? Colors.green : Colors.orange,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                _isInitialized ? 'All services initialized' : 'Initializing services...',
                style: const TextStyle(
                  fontFamily: 'Bitsumishi',
                  fontSize: 12,
                  color: Colors.white70,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActionsSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.pinkAccent.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '⚡ QUICK ACTIONS',
            style: TextStyle(
              fontFamily: 'Pirulen',
              fontSize: 14,
              color: Colors.pinkAccent,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: ElevatedButton(
                  onPressed: _isTesting ? null : _runComprehensiveTest,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.cyanAccent,
                    foregroundColor: Colors.black,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                  child: _isTesting
                      ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            color: Colors.black,
                          ),
                        )
                      : const Text(
                          'RUN COMPREHENSIVE TEST',
                          style: TextStyle(
                            fontFamily: 'Pirulen',
                            fontSize: 12,
                          ),
                        ),
                ),
              ),
              const SizedBox(width: 12),
              ElevatedButton(
                onPressed: _updateKlaviyoHealth,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                  foregroundColor: Colors.black,
                  padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                ),
                child: const Text(
                  'REFRESH',
                  style: TextStyle(
                    fontFamily: 'Pirulen',
                    fontSize: 12,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSystemHealthSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.greenAccent.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '🏥 SYSTEM HEALTH',
            style: TextStyle(
              fontFamily: 'Pirulen',
              fontSize: 14,
              color: Colors.greenAccent,
            ),
          ),
          const SizedBox(height: 16),
          _buildHealthItem(
            'Klaviyo API',
            _klaviyoHealth?['isHealthy'] == true,
            _klaviyoHealth?['responseTime']?.toString() ?? 'Unknown',
          ),
          _buildHealthItem('Email Validation', _isInitialized, 'Ready'),
          _buildHealthItem('Storage System', _isInitialized, 'Ready'),
          _buildHealthItem('Email Delivery', _isInitialized, 'Ready'),
        ],
      ),
    );
  }

  Widget _buildHealthItem(String name, bool isHealthy, String details) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(
            isHealthy ? Icons.check_circle : Icons.error,
            color: isHealthy ? Colors.green : Colors.red,
            size: 16,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              name,
              style: const TextStyle(
                fontFamily: 'Bitsumishi',
                fontSize: 12,
                color: Colors.white,
              ),
            ),
          ),
          Text(
            details,
            style: const TextStyle(
              fontFamily: 'Bitsumishi',
              fontSize: 10,
              color: Colors.white70,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildValidationResultsSection() {
    final result = _lastValidationResult!;
    
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: result.overallSuccess
              ? Colors.green.withValues(alpha: 0.3)
              : Colors.red.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                '📊 SIGNUP FLOW VALIDATION',
                style: TextStyle(
                  fontFamily: 'Pirulen',
                  fontSize: 14,
                  color: result.overallSuccess ? Colors.green : Colors.red,
                ),
              ),
              const Spacer(),
              Text(
                '${result.successRate.toStringAsFixed(1)}%',
                style: TextStyle(
                  fontFamily: 'Pirulen',
                  fontSize: 14,
                  color: result.overallSuccess ? Colors.green : Colors.red,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            'Steps: ${result.successfulSteps}/${result.totalSteps} successful',
            style: const TextStyle(
              fontFamily: 'Bitsumishi',
              fontSize: 12,
              color: Colors.white70,
            ),
          ),
          Text(
            'Duration: ${result.totalDuration.inMilliseconds}ms',
            style: const TextStyle(
              fontFamily: 'Bitsumishi',
              fontSize: 12,
              color: Colors.white70,
            ),
          ),
          if (result.failedStepsList.isNotEmpty) ...[
            const SizedBox(height: 12),
            const Text(
              'Failed Steps:',
              style: TextStyle(
                fontFamily: 'Bitsumishi',
                fontSize: 12,
                color: Colors.red,
              ),
            ),
            ...result.failedStepsList.map((step) => Padding(
              padding: const EdgeInsets.only(left: 16, top: 4),
              child: Text(
                '• ${step.stepName}: ${step.message}',
                style: const TextStyle(
                  fontFamily: 'Bitsumishi',
                  fontSize: 10,
                  color: Colors.white70,
                ),
              ),
            )),
          ],
        ],
      ),
    );
  }

  Widget _buildDeliveryResultsSection() {
    final result = _lastDeliveryResult!;
    
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: result.isAppStoreReady
              ? Colors.green.withValues(alpha: 0.3)
              : Colors.orange.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                '📧 EMAIL DELIVERY TEST',
                style: TextStyle(
                  fontFamily: 'Pirulen',
                  fontSize: 14,
                  color: result.isAppStoreReady ? Colors.green : Colors.orange,
                ),
              ),
              const Spacer(),
              Text(
                '${result.overallDeliveryRate.toStringAsFixed(1)}%',
                style: TextStyle(
                  fontFamily: 'Pirulen',
                  fontSize: 14,
                  color: result.isAppStoreReady ? Colors.green : Colors.orange,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            'Delivered: ${result.deliveryConfirmed}/${result.totalTests}',
            style: const TextStyle(
              fontFamily: 'Bitsumishi',
              fontSize: 12,
              color: Colors.white70,
            ),
          ),
          Text(
            'Duration: ${result.totalDuration.inSeconds}s',
            style: const TextStyle(
              fontFamily: 'Bitsumishi',
              fontSize: 12,
              color: Colors.white70,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAppStoreReadinessSection() {
    final signupReady = _lastValidationResult?.isAppStoreReady ?? false;
    final deliveryReady = _lastDeliveryResult?.isAppStoreReady ?? false;
    final klaviyoHealthy = _klaviyoHealth?['isHealthy'] == true;
    
    final overallReady = signupReady && deliveryReady && klaviyoHealthy;
    
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: overallReady
              ? Colors.green.withValues(alpha: 0.5)
              : Colors.red.withValues(alpha: 0.5),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                overallReady ? Icons.verified : Icons.warning,
                color: overallReady ? Colors.green : Colors.red,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'APP STORE READINESS',
                style: TextStyle(
                  fontFamily: 'Pirulen',
                  fontSize: 14,
                  color: overallReady ? Colors.green : Colors.red,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildReadinessItem('Signup Flow Validation', signupReady),
          _buildReadinessItem('Email Delivery System', deliveryReady),
          _buildReadinessItem('Klaviyo API Health', klaviyoHealthy),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: overallReady ? Colors.green.withValues(alpha: 0.1) : Colors.red.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              overallReady 
                  ? '✅ READY FOR APP STORE DEPLOYMENT'
                  : '⚠️ NOT READY - ISSUES NEED TO BE RESOLVED',
              style: TextStyle(
                fontFamily: 'Pirulen',
                fontSize: 12,
                color: overallReady ? Colors.green : Colors.red,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReadinessItem(String name, bool isReady) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(
            isReady ? Icons.check_circle : Icons.cancel,
            color: isReady ? Colors.green : Colors.red,
            size: 16,
          ),
          const SizedBox(width: 8),
          Text(
            name,
            style: const TextStyle(
              fontFamily: 'Bitsumishi',
              fontSize: 12,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }
}
