// lib/screens/app_store_readiness_screen.dart

import 'package:flutter/material.dart';
import '../services/app_store_readiness_service.dart';
import '../services/auth_flow_validator.dart';
import '../widgets/legal_links_widget.dart';
import '../widgets/permission_status_widget.dart';

/// Screen to check and display App Store readiness status
class AppStoreReadinessScreen extends StatefulWidget {
  const AppStoreReadinessScreen({super.key});
  
  @override
  State<AppStoreReadinessScreen> createState() => _AppStoreReadinessScreenState();
}

class _AppStoreReadinessScreenState extends State<AppStoreReadinessScreen> {
  AppStoreReadinessResult? _readinessResult;
  AuthFlowValidationResult? _authResult;
  bool _isLoading = false;
  String _statusMessage = '';
  
  @override
  void initState() {
    super.initState();
    _performReadinessCheck();
  }
  
  Future<void> _performReadinessCheck() async {
    setState(() {
      _isLoading = true;
      _statusMessage = 'Checking App Store readiness...';
    });
    
    try {
      // Run readiness check
      setState(() => _statusMessage = 'Checking legal compliance...');
      final readinessResult = await AppStoreReadinessService.performReadinessCheck();
      
      // Run auth flow validation
      setState(() => _statusMessage = 'Validating authentication flow...');
      final authResult = await AuthFlowValidator.validateAuthFlow();
      
      setState(() {
        _readinessResult = readinessResult;
        _authResult = authResult;
        _isLoading = false;
        _statusMessage = 'Readiness check complete';
      });
      
    } catch (e) {
      setState(() {
        _isLoading = false;
        _statusMessage = 'Error during readiness check: $e';
      });
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        title: const Text(
          'App Store Readiness',
          style: TextStyle(
            color: Colors.white,
            fontFamily: 'Pirulen',
            fontSize: 18,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh, color: Colors.white),
            onPressed: _isLoading ? null : _performReadinessCheck,
          ),
        ],
      ),
      body: _isLoading ? _buildLoadingView() : _buildReadinessView(),
    );
  }
  
  Widget _buildLoadingView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(color: Colors.purple),
          const SizedBox(height: 24),
          Text(
            _statusMessage,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontFamily: 'Bitsumishi',
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
  
  Widget _buildReadinessView() {
    if (_readinessResult == null || _authResult == null) {
      return const Center(
        child: Text(
          'Failed to load readiness data',
          style: TextStyle(color: Colors.white),
        ),
      );
    }
    
    final overallReady = _readinessResult!.isReady && _authResult!.isValid;
    final overallScore = ((_readinessResult!.score + _authResult!.score) / 2).round();
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Overall Status
          _buildOverallStatus(overallReady, overallScore),
          const SizedBox(height: 24),
          
          // App Store Readiness
          _buildReadinessSection(),
          const SizedBox(height: 24),
          
          // Authentication Flow
          _buildAuthSection(),
          const SizedBox(height: 24),
          
          // Permissions
          const PermissionStatusWidget(showInSettings: true),
          const SizedBox(height: 24),
          
          // Legal Documents
          const LegalSettingsSection(),
          const SizedBox(height: 24),
          
          // Actions
          _buildActionButtons(overallReady),
        ],
      ),
    );
  }
  
  Widget _buildOverallStatus(bool isReady, int score) {
    final color = isReady ? Colors.green : (score > 70 ? Colors.orange : Colors.red);
    
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color, width: 2),
      ),
      child: Column(
        children: [
          Icon(
            isReady ? Icons.check_circle : Icons.warning,
            color: color,
            size: 48,
          ),
          const SizedBox(height: 12),
          Text(
            isReady ? 'Ready for App Store!' : 'Needs Attention',
            style: TextStyle(
              color: color,
              fontSize: 24,
              fontWeight: FontWeight.bold,
              fontFamily: 'Pirulen',
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Overall Score: $score%',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontFamily: 'Bitsumishi',
            ),
          ),
          const SizedBox(height: 12),
          LinearProgressIndicator(
            value: score / 100,
            backgroundColor: Colors.grey[800],
            valueColor: AlwaysStoppedAnimation<Color>(color),
          ),
        ],
      ),
    );
  }
  
  Widget _buildReadinessSection() {
    return _buildSection(
      'App Store Readiness',
      Icons.store,
      Colors.blue,
      _readinessResult!.score,
      _readinessResult!.checks.map((c) => CheckItem(
        name: c.name,
        passed: c.passed,
        description: c.description,
        issues: c.issues,
      )).toList(),
    );
  }
  
  Widget _buildAuthSection() {
    return _buildSection(
      'Authentication Flow',
      Icons.security,
      Colors.green,
      _authResult!.score,
      _authResult!.tests.map((t) => CheckItem(
        name: t.name,
        passed: t.passed,
        description: t.description,
        issues: t.issues,
      )).toList(),
    );
  }
  
  Widget _buildSection(
    String title,
    IconData icon,
    Color color,
    int score,
    List<CheckItem> items,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[700]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 24),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  title,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    fontFamily: 'Pirulen',
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: color),
                ),
                child: Text(
                  '$score%',
                  style: TextStyle(
                    color: color,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          ...items.map((item) => _buildCheckItem(item)),
        ],
      ),
    );
  }
  
  Widget _buildCheckItem(CheckItem item) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[800],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: item.passed ? Colors.green : Colors.red,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                item.passed ? Icons.check_circle : Icons.error,
                color: item.passed ? Colors.green : Colors.red,
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  item.name,
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            item.description,
            style: const TextStyle(
              color: Colors.grey,
              fontSize: 12,
            ),
          ),
          if (item.issues.isNotEmpty) ...[
            const SizedBox(height: 8),
            ...item.issues.map((issue) => Padding(
              padding: const EdgeInsets.only(left: 16, bottom: 4),
              child: Text(
                '• $issue',
                style: const TextStyle(
                  color: Colors.red,
                  fontSize: 12,
                ),
              ),
            )),
          ],
        ],
      ),
    );
  }
  
  Widget _buildActionButtons(bool isReady) {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: _generateReport,
            icon: const Icon(Icons.description),
            label: const Text('Generate Detailed Report'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ),
        const SizedBox(height: 12),
        if (isReady) ...[
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _showSubmissionGuidance,
              icon: const Icon(Icons.rocket_launch),
              label: const Text('Ready for Submission!'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),
        ] else ...[
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _showImprovementGuidance,
              icon: const Icon(Icons.build),
              label: const Text('View Improvement Guide'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),
        ],
      ],
    );
  }
  
  void _generateReport() {
    final readinessReport = AppStoreReadinessService.generateDetailedReport(_readinessResult!);
    final authReport = AuthFlowValidator.generateDetailedReport(_authResult!);
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.black,
        title: const Text(
          'Detailed Report',
          style: TextStyle(color: Colors.white, fontFamily: 'Pirulen'),
        ),
        content: SizedBox(
          width: double.maxFinite,
          height: 400,
          child: SingleChildScrollView(
            child: Text(
              '$readinessReport\n\n$authReport',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontFamily: 'monospace',
              ),
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }
  
  void _showSubmissionGuidance() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.black,
        title: const Text(
          '🚀 Ready for App Store!',
          style: TextStyle(color: Colors.green, fontFamily: 'Pirulen'),
        ),
        content: const Text(
          'Congratulations! Your app has passed all readiness checks and is ready for App Store submission.\n\nNext steps:\n• Build release version\n• Test on physical devices\n• Submit to App Store Connect\n• Complete App Store metadata',
          style: TextStyle(color: Colors.white),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Got it!', style: TextStyle(color: Colors.green)),
          ),
        ],
      ),
    );
  }
  
  void _showImprovementGuidance() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.black,
        title: const Text(
          '🔧 Improvement Needed',
          style: TextStyle(color: Colors.orange, fontFamily: 'Pirulen'),
        ),
        content: const Text(
          'Some areas need attention before App Store submission. Review the failed checks above and address each issue.\n\nCommon fixes:\n• Update legal documents\n• Fix permission descriptions\n• Test authentication flow\n• Optimize performance',
          style: TextStyle(color: Colors.white),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Understood', style: TextStyle(color: Colors.orange)),
          ),
        ],
      ),
    );
  }
}

class CheckItem {
  final String name;
  final bool passed;
  final String description;
  final List<String> issues;
  
  const CheckItem({
    required this.name,
    required this.passed,
    required this.description,
    required this.issues,
  });
}
