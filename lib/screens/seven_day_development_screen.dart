// 📁 lib/screens/seven_day_development_screen.dart

import 'package:flutter/material.dart';
import '../models/user_model.dart';
import '../widgets/exp_pie_chart_popup.dart';
import '../utils/rank_utils.dart'; // Provides LevelUtils
import '../theme/colors.dart';

/// Seven‐Day Progress Screen: shows one horizontal bar per category,
/// with current week EXP & previous week (7-day) EXP, overall level, and streak info.
/// Bars animate on first build; a pulsing streak badge appears if streak > 1.
/// An AppBar icon opens the ExpPieChartPopup.
class SevenDayDevelopmentScreen extends StatefulWidget {
  final User user;

  const SevenDayDevelopmentScreen({super.key, required this.user});

  /// Height of each category bar.
  static const double barHeight = 16.0;

  @override
  State<SevenDayDevelopmentScreen> createState() =>
      _SevenDayDevelopmentScreenState();
}

class _SevenDayDevelopmentScreenState
    extends State<SevenDayDevelopmentScreen> {
  // Track which categories have animated their bars already.
  final Map<String, bool> _hasAnimated = {};

  // Animation durations
  static const Duration _barAnimDuration = Duration(milliseconds: 1200);
  static const Duration _streakPulseDuration = Duration(milliseconds: 800);

    @override
  void initState() {
    super.initState();
    // Initialize map entries to false for each category (union of current + last week)
    final currentWeekExp = widget.user.categories;
    final lastWeekExp = widget.user.lastWeekExp;
    final allCats = <String>{
      ...currentWeekExp.keys,
      ...lastWeekExp.keys,
    };
    for (final cat in allCats) {
      _hasAnimated[cat] = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    final currentWeekExp = widget.user.categories;
    final lastWeekExp = widget.user.lastWeekExp;
    final allCats = <String>{
      ...currentWeekExp.keys,
      ...lastWeekExp.keys,
    }.toList();

    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        title: const Text(
          '7-Day Progress',
          style: TextStyle(
            fontFamily: 'Bitsumishi',
            letterSpacing: 1.1,
            fontSize: 20,
            color: Colors.purpleAccent,
          ),
        ),
        backgroundColor: Colors.black,
        leading: const BackButton(color: Colors.purpleAccent),
        actions: [
          SizedBox(
            width: 44,
            height: 44,
            child: IconButton(
              icon: const Icon(Icons.pie_chart, color: Colors.purpleAccent),
              onPressed: () {
                showDialog(
                  context: context,
                  builder: (_) => ExpPieChartPopup(),
                );
              },
            ),
          ),
        ],
        elevation: 0,
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
          child: SingleChildScrollView(
            physics: const BouncingScrollPhysics(),
            child: Column(
              children: [
                for (final cat in allCats)
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    child: _CategoryDualBar(
                      category: cat,
                      currentExp: currentWeekExp[cat] ?? 0,
                      prevExp: lastWeekExp[cat] ?? 0,
                      streakDays: widget.user.streakDays,
                      animateOnce: !_hasAnimated[cat]!,
                      onAnimated: () => _hasAnimated[cat] = true,
                      customCategories: widget.user.customCategories,
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// Displays two bars for a single category:
///  • Current week (neon gradient colored) with animated fill.
///  • Previous week / 7-day (grey background with neon-white fill) with animated fill.
/// Also shows level and pulsing streak badge if streakDays > 1.
class _CategoryDualBar extends StatefulWidget {
  final String category;
  final int currentExp;
  final int prevExp; // last 7‐day EXP
  final int streakDays;
  final bool animateOnce;
  final VoidCallback onAnimated;
  final List<String> customCategories;

  const _CategoryDualBar({
    required this.category,
    required this.currentExp,
    required this.prevExp,
    required this.streakDays,
    required this.animateOnce,
    required this.onAnimated,
    required this.customCategories,
  });

  @override
  State<_CategoryDualBar> createState() => _CategoryDualBarState();
}

class _CategoryDualBarState extends State<_CategoryDualBar>
    with SingleTickerProviderStateMixin {
  late final AnimationController _streakController;
  late final Animation<double> _streakAnim;

  @override
  void initState() {
    super.initState();
    _streakController = AnimationController(
      vsync: this,
      duration: _SevenDayDevelopmentScreenState._streakPulseDuration,
    );
    _streakAnim = Tween<double>(begin: 1.0, end: 1.4).animate(
      CurvedAnimation(parent: _streakController, curve: Curves.easeInOut),
    );
    if (widget.streakDays > 1) {
      _streakController.repeat(reverse: true);
    }
  }

  @override
  void dispose() {
    _streakController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final color = _getCategoryColor(widget.category);
    // Compute overall levels
    final currentLevel = LevelUtils.getRankLevel(widget.currentExp);
    final prevLevel = LevelUtils.getRankLevel(widget.prevExp);
    final prevPct =
        LevelUtils.getRankProgressPct(widget.prevExp).clamp(0.0, 1.0);
    final currPct =
        LevelUtils.getRankProgressPct(widget.currentExp).clamp(0.0, 1.0);

    // Staggered animation delay
    final delayMs = (widget.category.hashCode & 0xFF) % 200;
    final startDelay = widget.animateOnce
        ? Duration(milliseconds: 100 + delayMs)
        : Duration.zero;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label: “CATEGORY | CUR: 165 (L1) | PREV: 90 (L0) | STREAK: X DAYS”
        Row(
          children: [
            Expanded(
              child: Text(
                '${_formatCategory(widget.category).toUpperCase()}  '
                'CUR: ${widget.currentExp} (L$currentLevel)  '
                'PREV: ${widget.prevExp} (L$prevLevel)  '
                'STREAK: ${widget.streakDays} DAY${widget.streakDays == 1 ? '' : 'S'}',
                style: TextStyle(
                  fontFamily: 'Digital-7',
                  fontSize: 12,
                  color: color,
                  letterSpacing: 0.5,
                ),
              ),
            ),
            if (widget.streakDays > 1)
              ScaleTransition(
                scale: _streakAnim,
                child: Icon(
                  Icons.local_fire_department,
                  color: Colors.orangeAccent,
                  size: 20,
                ),
              ),
          ],
        ),
        const SizedBox(height: 6),

        // Current Week Bar (neon gradient)
        LayoutBuilder(builder: (context, constraints) {
          final fullW = constraints.maxWidth;
          return FutureBuilder(
            future: Future.delayed(startDelay),
            builder: (context, snapshot) {
              final animatePct =
                  (snapshot.connectionState == ConnectionState.done)
                      ? currPct
                      : 0.0;
              return TweenAnimationBuilder<double>(
                tween: Tween<double>(begin: 0.0, end: animatePct),
                duration: animatePct > 0
                    ? _SevenDayDevelopmentScreenState._barAnimDuration
                    : Duration.zero,
                curve: Curves.easeOutCubic,
                onEnd: () {
                  if (widget.animateOnce) widget.onAnimated();
                },
                builder: (context, animatedPct, child) {
                  return Stack(
                    children: [
                      Container(
                        height: SevenDayDevelopmentScreen.barHeight,
                        decoration: BoxDecoration(
                          color: Colors.grey.shade900,
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      Container(
                        height: SevenDayDevelopmentScreen.barHeight,
                        width: fullW * animatedPct,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              color.withValues(alpha: 0.8),
                              color,
                            ],
                            begin: Alignment.centerLeft,
                            end: Alignment.centerRight,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: color.withValues(alpha: 0.5),
                              blurRadius: 8,
                              spreadRadius: 1,
                              offset: const Offset(0, 0),
                            ),
                          ],
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                    ],
                  );
                },
              );
            },
          );
        }),
        const SizedBox(height: 4),
        // Label on the right for currentExp
        Align(
          alignment: Alignment.centerRight,
          child: Text(
            '${widget.currentExp} EXP',
            style: const TextStyle(
              fontFamily: 'Digital-7',
              fontSize: 10,
              color: Colors.white,
            ),
          ),
        ),
        const SizedBox(height: 12),

        // Previous Week Bar (grey background + neon‐white fill)
        LayoutBuilder(builder: (context, constraints) {
          final fullW = constraints.maxWidth;
          return FutureBuilder(
            future: Future.delayed(startDelay),
            builder: (context, snapshot) {
              final animatePct =
                  (snapshot.connectionState == ConnectionState.done)
                      ? prevPct
                      : 0.0;
              return TweenAnimationBuilder<double>(
                tween: Tween<double>(begin: 0.0, end: animatePct),
                duration: animatePct > 0
                    ? _SevenDayDevelopmentScreenState._barAnimDuration
                    : Duration.zero,
                curve: Curves.easeOutCubic,
                builder: (context, animatedPct, child) {
                  return Stack(
                    children: [
                      Container(
                        height: SevenDayDevelopmentScreen.barHeight,
                        decoration: BoxDecoration(
                          color: Colors.grey.shade800,
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      Container(
                        height: SevenDayDevelopmentScreen.barHeight,
                        width: fullW * animatedPct,
                        decoration: BoxDecoration(
                          gradient: const LinearGradient(
                            colors: [
                              Colors.white24,
                              Colors.white70,
                            ],
                            begin: Alignment.centerLeft,
                            end: Alignment.centerRight,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.white.withValues(alpha: 0.4),
                              blurRadius: 6,
                              spreadRadius: 1,
                              offset: const Offset(0, 0),
                            ),
                          ],
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                    ],
                  );
                },
              );
            },
          );
        }),
        const SizedBox(height: 4),
        // Label on the right for prevExp
        Align(
          alignment: Alignment.centerRight,
          child: Text(
            '${widget.prevExp} EXP',
            style: const TextStyle(
              fontFamily: 'Digital-7',
              fontSize: 10,
              color: Colors.white70,
            ),
          ),
        ),
      ],
    );
  }

  /// Returns a neon color based on category name using the centralized color system.
  Color _getCategoryColor(String category) {
    // Use the centralized color system for consistency
    return getCategoryColor(
      category,
      customCategories: widget.customCategories,
    );
  }

  String _formatCategory(String category) {
    if (category.isEmpty) return '';
    return category[0].toUpperCase() + category.substring(1);
  }
}
