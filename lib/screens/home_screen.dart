// 📁 lib/screens/home_screen.dart

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/user_model.dart';
import '../controller/user_controller2.dart';
import '../services/coach_checkin_coordinator.dart';

//import 'package:maxed_out_life/services/music_service.dart';
import '../home/<USER>';

//import '../home/<USER>';
import '../home/<USER>';
import '../home/<USER>';
import '../home/<USER>';
import '../home/<USER>';
import '../home/<USER>';
import '../home/<USER>';
import '../diary/super_feed.dart';
import '../home/<USER>';
import '../home/<USER>';
import '../home/<USER>';
import '../home/<USER>';
import '../widgets/interactive_tutorial_overlay.dart';
import '../widgets/bounty_hunter_section.dart';
import '../widgets/training_tracker_section.dart';

import '../theme/colors.dart';
//import '../quests/north_star_model.dart';

// Define base categories
const List<String> baseCategories = [
  'Health',
  'Wealth',
  'Purpose',
  'Connection',
];

class HomeScreen extends StatefulWidget {
  final User user;
  final VoidCallback onSignOut;
  final Function(User?) onReset;
  final VoidCallback toggleTheme;
  final Function(User) onUserUpdated;

  const HomeScreen({
    super.key,
    required this.user,
    required this.onSignOut,
    required this.onReset,
    required this.toggleTheme,
    required this.onUserUpdated,
  });

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with RouteAware {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _initializeCheckinSystem();
    _checkAndShowTutorial();
    _initializeContextAwareLoading();
  }

  /// Initialize the coach check-in system (managed by Smart Service Manager)
  Future<void> _initializeCheckinSystem() async {
    try {
      // Check-in system is now managed by Smart Service Manager
      // It will be initialized intelligently in the background
      debugPrint('🧠 Check-in system managed by Smart Service Manager');
    } catch (e) {
      // Silently handle errors - check-ins are not critical for app function
      debugPrint('❌ Failed to initialize check-in system: $e');
    }
  }

  /// Initialize context-aware service loading (now handled by Smart Service Manager)
  Future<void> _initializeContextAwareLoading() async {
    try {
      // Services are now automatically managed by Smart Service Manager
      debugPrint('🧠 Services managed by Smart Service Manager');
    } catch (e) {
      debugPrint('❌ Failed to initialize services: $e');
    }
  }

  /// Check if tutorial needs to be shown and display it immediately after onboarding
  void _checkAndShowTutorial() {
    // Only show tutorial if onboarding is complete but tutorial hasn't been completed
    final onboardingProgress = widget.user.onboardingProgress;
    final hasCompletedOnboarding = onboardingProgress.hasCompletedWelcome &&
        onboardingProgress.hasCreatedNorthStar &&
        onboardingProgress.hasSetCategories &&
        onboardingProgress.hasMetCoaches &&
        onboardingProgress.hasSetHabits;

    final hasCompletedTutorial = onboardingProgress.hasCompletedTutorial;

    if (hasCompletedOnboarding && !hasCompletedTutorial) {
      // Use addPostFrameCallback to ensure the widget is fully built before showing tutorial
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _showInteractiveTutorial();
      });
    }
  }

  /// Show the interactive tutorial overlay
  void _showInteractiveTutorial() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => InteractiveTutorialOverlay(
        user: widget.user,
        onComplete: () {
          Navigator.of(context).pop();
          // Mark tutorial as completed and update user
          final tutorialCompletedUser = widget.user.copyWith(
            onboardingProgress: widget.user.onboardingProgress.copyWith(
              hasCompletedTutorial: true,
            ),
          );
          widget.onUserUpdated(tutorialCompletedUser);
        },
      ),
    );
  }

  @override
  void dispose() {
    _scrollController.dispose();
    CoachCheckinCoordinator.stop();
    super.dispose();
  }

  void _refreshUser(User updated) {
    widget.onUserUpdated(updated);
  }

  void _openMusicModal() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (_) => const MusicControlModal(),
      isScrollControlled: true,
      enableDrag: true,
      showDragHandle: true,
    );
  }

  @override
  Widget build(BuildContext context) {
    final userController = Provider.of<UserController2>(context);
    final user = userController.user;
    if (user == null) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    final quest = user.northStarQuest;
    final glowColor = quest?.glowColor ?? Colors.purpleAccent;

    // Build category list from user's categories map (avoid duplicates)
    final allCategories = [
      ...baseCategories,
      ...user.categories.keys.where((c) => !baseCategories.contains(c))
    ].where((c) => !c.startsWith('N.S.')).toList();

    // Category → Color map
    final Map<String, Color> defaultCategoryColorsMap = {
      for (final cat in allCategories)
        cat: getCategoryColor(
          cat,
          customCategories: user.customCategories,
        )
    };

    final List<Color> fallbackColorsList = List<Color>.filled(
      allCategories.length,
      Colors.black,
    );

    return HmRefreshWrapper(
      onUserUpdated: widget.onUserUpdated,
      updateLocalUser: _refreshUser,
      child: Scaffold(
        backgroundColor: Colors.black,
        body: SafeArea(
          child: CustomScrollView(
            controller: _scrollController,
            slivers: [
              // ─── Welcome & Top Buttons (Fixed sliver implementation)
              SliverPadding(
                padding: const EdgeInsets.all(16),
                sliver: SliverList(
                  delegate: SliverChildListDelegate([
                    HmWelcomeHeader(username: user.username),
                    HmTopNavigationButtons(
                      currentUser: user,
                      onUserUpdated: _refreshUser,
                    ),
                    const SizedBox(height: 12),
                  ]),
                ),
              ),

              // ─── EXP Bar
              SliverToBoxAdapter(
                child: HmExpBar(user: user),
              ),

              // ─── EXP Categories (Optimized for performance)
              SliverToBoxAdapter(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: HmExpTrackers(
                    user: user,
                    categories: allCategories,
                    onUserUpdated: _refreshUser,
                  ),
                ),
              ),

              // ─── Supercharge, 7DD, Streak, Diary (Fixed sliver implementation)
              SliverPadding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                sliver: SliverList(
                  delegate: SliverChildListDelegate([
                    const SizedBox(height: 30),
                    HmSuperchargeButton(glowColor: glowColor),
                    const SizedBox(height: 20),
                    HmSevenDayDevelopmentButton(user: user),
                    const SizedBox(height: 20),
                    HmStreakDisplay(
                      user: user,
                      glowColor: glowColor,
                    ),
                    const SizedBox(height: 30),
                    SuperFeed(
                      user: user,
                      scrollController: _scrollController,
                      fallbackColors: fallbackColorsList,
                      defaultCategoryColors: defaultCategoryColorsMap,
                    ),
                  ]),
                ),
              ),

              // ─── North Star Quest Section
              if (quest != null) ...[
                SliverToBoxAdapter(
                  child: const SizedBox(height: 30),
                ),
                ...buildNorthStarSlivers(
                  user: user,
                  onQuestUpdate: _refreshUser,
                ),
              ],

              // ─── Training Tracker Section
              SliverToBoxAdapter(
                child: const SizedBox(height: 30),
              ),
              SliverToBoxAdapter(
                child: TrainingTrackerSection(
                  user: user,
                  onUserUpdate: _refreshUser,
                ),
              ),

              // ─── Bounty Hunter Section
              SliverToBoxAdapter(
                child: const SizedBox(height: 30),
              ),
              SliverToBoxAdapter(
                child: BountyHunterSection(
                  user: user,
                  onUserUpdate: _refreshUser,
                ),
              ),

              // ─── Admin Tools
              SliverPadding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                sliver: SliverList(
                  delegate: SliverChildListDelegate([
                    const SizedBox(height: 30),
                    HmAdminControls(
                      user: user,
                      onSignOut: widget.onSignOut,
                      onReset: widget.onReset,
                      toggleTheme: widget.toggleTheme,
                    ),
                  ]),
                ),
              ),

              // ─── Extra bottom spacing so modal button doesn't overlap
              SliverToBoxAdapter(child: SizedBox(height: 80)),
            ],
          ),
        ),

        // ─── Floating "Music" Button at bottom right ───
        floatingActionButton: SizedBox(
          width: 56, // Restored to standard 56px for proper touch target
          height: 56,
          child: FloatingActionButton(
            heroTag: "home_music_fab", // Unique hero tag
            backgroundColor: Colors.black87,
            onPressed: _openMusicModal,
            child: const Icon(Icons.music_note, color: Colors.cyanAccent, size: 24), // Restored to standard size
          ),
        ),
      ),
    );
  }
}
