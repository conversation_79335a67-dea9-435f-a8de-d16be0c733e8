// 📁 lib/screens/coach_chat_screen.dart

import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'dart:convert';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../models/user_model.dart';
import '../theme/colors.dart'; // ← Import the new colors.dart
import 'package:file_picker/file_picker.dart';
import 'dart:io';
import '../services/coach_orchestration_service.dart';
import '../services/coach_icon_service.dart';
import '../services/coach_checkin_service.dart';
import '../services/superintelligent_thinking_service.dart';
import '../services/user_spiritual_profile_service.dart';
import '../widgets/thinking_visualization_widget.dart';
import '../widgets/spiritual_profile_modal.dart';

import '../prompts/mxd_life_coaches.dart';
import '../home/<USER>'; // For StrokeText

class ChatMessage {
  final String text;
  final bool isUser;
  final DateTime timestamp;

  ChatMessage({
    required this.text,
    required this.isUser,
    required this.timestamp,
  });

  Map<String, dynamic> toJson() => {
        'text': text,
        'isUser': isUser,
        'timestamp': timestamp.toIso8601String(),
      };

  factory ChatMessage.fromJson(Map<String, dynamic> json) => ChatMessage(
        text: json['text'],
        isUser: json['isUser'],
        timestamp: DateTime.parse(json['timestamp']),
      );
}

class CoachChatScreen extends StatefulWidget {
  final String category;
  final User user;

  const CoachChatScreen({
    super.key,
    required this.category,
    required this.user,
  });

  @override
  State<CoachChatScreen> createState() => _CoachChatScreenState();
}

class _CoachChatScreenState extends State<CoachChatScreen> {
  final TextEditingController _controller = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final List<ChatMessage> _messages = [];
  final FlutterSecureStorage _secureStorage = const FlutterSecureStorage();
  bool _isLoading = false;
  String _thinkingMessage = '';
  Stream<ThinkingState>? _thinkingStream;

  String get _chatKey =>
      'secure_chatlog_${widget.user.username}_${widget.category}';

  /// Get the correct coach name based on user gender and category
  String get _coachName {
    // Handle non-gender users with assigned coaches
    String effectiveGender = widget.user.gender;
    if (widget.user.gender.toLowerCase() == 'non-gender' && widget.user.assignedCoaches != null) {
      effectiveGender = widget.user.assignedCoaches![widget.category] ?? 'male';
    }

    // Handle custom categories with hardcoded coach assignments
    if (widget.category == 'Custom Category 1') {
      return effectiveGender.toLowerCase() == 'female' ? 'Luna' : 'Aether';
    } else if (widget.category == 'Custom Category 2') {
      return effectiveGender.toLowerCase() == 'female' ? 'Elysia' : 'Chronos';
    }

    // Find the coach for this category
    final coach = mxdLifeCoaches.firstWhere(
      (coach) => coach.category == widget.category,
      orElse: () => mxdLifeCoaches.first,
    );

    return effectiveGender.toLowerCase() == 'male' ? coach.maleName : coach.femaleName;
  }

  /// Get the display category name (actual custom category name instead of "Custom Category 1/2")
  String get _displayCategoryName {
    if (widget.category == 'Custom Category 1' && widget.user.customCategories.isNotEmpty) {
      return widget.user.customCategories[0];
    } else if (widget.category == 'Custom Category 2' && widget.user.customCategories.length > 1) {
      return widget.user.customCategories[1];
    }
    return widget.category;
  }

  @override
  void initState() {
    super.initState();
    _loadChatHistory();
    _initializeCheckinSystem();
    _checkSpiritualProfileSetup();
    _checkEmailVerificationStatus();
  }

  /// 🎯 PHASE 2: Check email verification status (informational only)
  void _checkEmailVerificationStatus() {
    // Email verification no longer required for coach access
    // This method is kept for compatibility but no longer shows blocking warnings
    if (!widget.user.isEmailVerified) {
      if (kDebugMode) print('ℹ️ User accessing coach chat without email verification (allowed)');
      // No blocking modal - users can access coaches directly
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _loadChatHistory() async {
    final stored = await _secureStorage.read(key: _chatKey);
    if (stored != null) {
      final decoded = json.decode(stored) as List;
      setState(() {
        _messages
            .addAll(decoded.map((e) => ChatMessage.fromJson(e)).toList());
      });
    }
  }

  Future<void> _saveChatHistory() async {
    final encoded =
        json.encode(_messages.map((m) => m.toJson()).toList());
    await _secureStorage.write(key: _chatKey, value: encoded);
  }

  Future<void> _sendMessage() async {
    final userText = _controller.text.trim();
    if (userText.isEmpty || _isLoading) return;

    final userMessage = ChatMessage(
      text: userText,
      isUser: true,
      timestamp: DateTime.now(),
    );

    setState(() {
      _messages.insert(0, userMessage);
      _controller.clear();
      _isLoading = true;
      _thinkingMessage = '*$_coachName is thinking...*';

      // Start superintelligent thinking visualization
      _thinkingStream = SuperintelligentThinkingService.startThinking(
        coachCategory: widget.category,
        userGender: widget.user.gender,
        userMessage: userText,
        assignedCoaches: widget.user.assignedCoaches,
      );
    });

    try {
      await _saveChatHistory();
    } catch (e) {
      debugPrint('⚠️ Failed to save chat history: $e');
      // Continue anyway - don't block user interaction
    }

    String responseText;
    try {
      responseText = await _callOpenAI(userText);
    } catch (e) {
      debugPrint('❌ Failed to get AI response: $e');

      // Provide fallback response to maintain user experience
      responseText = _getFallbackResponse(userText);

      // Show user-friendly error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Connection issue - using offline response. Please check your internet connection.',
              style: TextStyle(color: Colors.white),
            ),
            backgroundColor: Colors.orange[700],
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }

    final botMessage = ChatMessage(
      text: responseText,
      isUser: false,
      timestamp: DateTime.now(),
    );

    setState(() {
      _messages.insert(0, botMessage);
      _isLoading = false;

      // Stop superintelligent thinking visualization
      SuperintelligentThinkingService.stopThinking(
        widget.category,
        widget.user.gender,
        widget.user.assignedCoaches,
      );
      _thinkingStream = null;
    });

    try {
      await _saveChatHistory();
    } catch (e) {
      debugPrint('⚠️ Failed to save chat history after response: $e');
      // Continue anyway - user can still see the conversation
    }

    try {
      await Future.delayed(const Duration(milliseconds: 200));
      _scrollController.animateTo(
        0.0,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    } catch (e) {
      debugPrint('⚠️ Failed to scroll to bottom: $e');
      // Non-critical - continue anyway
    }

    try {
      // Update last message time for check-in system
      await _updateLastMessageTime();
    } catch (e) {
      debugPrint('⚠️ Failed to update last message time: $e');
      // Non-critical - continue anyway
    }
  }

  Future<String> _callOpenAI(String userPrompt) async {
    // Use new orchestration service with full context and intelligent routing
    return await CoachOrchestrationService.generateSuperintelligentResponse(
      category: widget.category,
      userPrompt: userPrompt,
      user: widget.user,
    );
  }

  /// Provide fallback response when AI service fails
  ///
  /// This ensures users always get a response, maintaining engagement
  /// even when network or AI services are unavailable.
  String _getFallbackResponse(String userMessage) {
    final lowerMessage = userMessage.toLowerCase();

    // Category-specific fallback responses
    switch (widget.category.toLowerCase()) {
      case 'health':
        if (lowerMessage.contains('exercise') || lowerMessage.contains('workout')) {
          return "I understand you're asking about exercise. While I'm having connection issues, here's a quick tip: Start with just 10 minutes of movement today - even a short walk counts! I'll be back online soon to give you more personalized advice.";
        } else if (lowerMessage.contains('diet') || lowerMessage.contains('food')) {
          return "I hear you asking about nutrition. While I'm offline, remember: focus on whole foods, stay hydrated, and listen to your body. I'll be back soon with more detailed guidance!";
        }
        return "I'm having connection issues right now, but I want you to know that taking care of your health is always the right choice. I'll be back online soon to help you with more specific advice!";

      case 'wealth':
        if (lowerMessage.contains('money') || lowerMessage.contains('budget')) {
          return "I see you're thinking about finances. While I'm offline, here's a fundamental truth: track your spending today, even if it's just writing down one purchase. Small steps build wealth! I'll be back soon with personalized strategies.";
        }
        return "I'm temporarily offline, but your focus on building wealth shows great mindset! Remember: every dollar saved is a dollar that can work for you. I'll be back soon with specific advice!";

      case 'purpose':
        return "I'm having connection issues, but I want you to know that seeking purpose is one of the most important journeys in life. Take a moment to reflect on what truly matters to you today. I'll be back soon to explore this deeper with you!";

      default:
        // Custom categories or unknown
        return "I'm temporarily offline, but I appreciate you reaching out! Your commitment to growth and improvement is inspiring. I'll be back online soon to give you the personalized guidance you deserve!";
    }
  }

  /// Pick a file (image or document) and send it for analysis
  Future<void> _pickAndSendFile() async {
    if (_isLoading) return;

    final result = await FilePicker.platform.pickFiles(
      type: FileType.custom,
      allowedExtensions: ['png', 'jpg', 'jpeg', 'pdf', 'txt', 'docx'],
      allowMultiple: false,
    );

    if (result == null) {
      // User cancelled the picker
      return;
    }

    final pickedFile = result.files.single;
    if (pickedFile.path == null) {
      return;
    }

    final File file = File(pickedFile.path!);
    final fileName = pickedFile.name;

    try {
      // Validate file size (max 10MB)
      if (pickedFile.size > 10 * 1024 * 1024) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'File too large. Please select a file smaller than 10MB.',
                style: TextStyle(color: Colors.white),
              ),
              backgroundColor: Colors.red[700],
              duration: const Duration(seconds: 3),
            ),
          );
        }
        return;
      }

      // Show placeholder message in chat
      final userMessage = ChatMessage(
        text: '📤 Uploaded file: $fileName',
        isUser: true,
        timestamp: DateTime.now(),
      );
      setState(() {
        _messages.insert(0, userMessage);
        _isLoading = true;
        _thinkingMessage = '*$_coachName is analyzing your file...*';
      });
      await _saveChatHistory();
      _scrollController.animateTo(
        0.0,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );

      // Read file content and analyze using orchestration service
      final fileContent = await file.readAsString();
      final analysisText = await CoachOrchestrationService.generateSuperintelligentResponse(
        category: widget.category,
        userPrompt: 'Please analyze this file and provide insights.',
        user: widget.user,
        fileContent: fileContent,
        fileName: file.path.split('/').last,
        interactionType: 'file_analysis',
      );

      final botFileMessage = ChatMessage(
        text: analysisText,
        isUser: false,
        timestamp: DateTime.now(),
      );
      setState(() {
        _messages.insert(0, botFileMessage);
        _isLoading = false;
      });
      await _saveChatHistory();
    } catch (e) {
      debugPrint('❌ File upload failed: $e');

      setState(() {
        _isLoading = false;
      });

      // Show error message to user
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Failed to upload file. Please check your connection and try again.',
              style: TextStyle(color: Colors.white),
            ),
            backgroundColor: Colors.red[700],
            duration: const Duration(seconds: 4),
            action: SnackBarAction(
              label: 'Retry',
              textColor: Colors.white,
              onPressed: _pickAndSendFile,
            ),
          ),
        );
      }

      // Add error message to chat
      final errorMessage = ChatMessage(
        text: '❌ Sorry, I couldn\'t analyze your file. Please try uploading it again or check your internet connection.',
        isUser: false,
        timestamp: DateTime.now(),
      );

      setState(() {
        _messages.insert(0, errorMessage);
      });

      try {
        await _saveChatHistory();
      } catch (saveError) {
        debugPrint('⚠️ Failed to save chat history after error: $saveError');
      }
    }
    _scrollController.animateTo(
      0.0,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeOut,
    );

    // The old prompt-based approach is retained but removed from execution
    // by commenting out to preserve code history without duplication:
    /*
    // Send a prompt indicating a file upload; actual file handling occurs in coach_service
    final prompt = '<FILE_UPLOAD>:$fileName';
    final responseText = await _callOpenAI(prompt);
    final botMessage = ChatMessage(
      text: responseText,
      isUser: false,
      timestamp: DateTime.now(),
    );

    setState(() {
      _messages.insert(0, botMessage);
      _isLoading = false;
    });
    await _saveChatHistory();
    _scrollController.animateTo(
      0.0,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeOut,
    );
    */
  }

  /// Check if spiritual profile setup is needed for Connection coaches
  Future<void> _checkSpiritualProfileSetup() async {
    // Only check for Connection coaches
    if (widget.category.toLowerCase() != 'connection') {
      return;
    }

    try {
      final needsSetup = await UserSpiritualProfileService.needsProfileSetup(widget.user.id);

      if (needsSetup && mounted) {
        // Show spiritual profile modal after a brief delay
        Future.delayed(const Duration(milliseconds: 1000), () {
          if (mounted) {
            _showSpiritualProfileModal();
          }
        });
      }
    } catch (e) {
      if (kDebugMode) print('❌ Error checking spiritual profile setup: $e');
      // Don't show modal on error - fail silently
    }
  }

  /// Show spiritual profile setup modal
  void _showSpiritualProfileModal() {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) => SpiritualProfileModal(
        userId: widget.user.id,
        onComplete: () {
          if (kDebugMode) print('✅ Spiritual profile setup completed');
          // Optionally show a welcome message from the Connection coach
          _addWelcomeMessage();
        },
      ),
    );
  }

  /// Add welcome message after spiritual profile setup
  void _addWelcomeMessage() {
    final welcomeMessage = ChatMessage(
      text: "Thank you for sharing that with me. I'm honored to be part of your journey, and I'll make sure my guidance resonates with your beliefs and values. How can I support you today?",
      isUser: false,
      timestamp: DateTime.now(),
    );

    setState(() {
      _messages.insert(0, welcomeMessage);
    });

    _saveChatHistory();
  }

  @override
  Widget build(BuildContext context) {
    // 1️⃣ Determine box color for this category via the centralized helper:
    // Use display category name for color determination
    final color = getCategoryColor(
      _displayCategoryName,
      customCategories: widget.user.customCategories,
    );

    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: color,
        title: Row(
          children: [
            // Coach icon
            Container(
              width: 32,
              height: 32,
              margin: const EdgeInsets.only(right: 12),
              decoration: const BoxDecoration(
                shape: BoxShape.circle,
              ),
              child: ClipOval(
                child: Image.asset(
                  CoachIconService.getCoachIconPath(widget.category, widget.user.gender),
                  width: 32,
                  height: 32,
                  fit: BoxFit.cover, // Changed to cover to fill the entire circle
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      width: 32,
                      height: 32,
                      decoration: const BoxDecoration(
                        color: Colors.white24,
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.person,
                        color: Colors.white,
                        size: 20,
                      ),
                    );
                  },
                ),
              ),
            ),
            // Coach name
            Expanded(
              child: StrokeText(
                text: '$_displayCategoryName: $_coachName',
                textStyle: const TextStyle(
                  fontFamily: 'Bitsumishi',
                  color: Colors.white,
                  fontSize: 16,
                ),
                strokeWidth: 1.0,
                strokeColor: Colors.black,
              ),
            ),
          ],
        ),
        actions: [
          // Spiritual Profile button (only for Connection coach)
          if (widget.category == 'Connection')
            IconButton(
              icon: const Icon(Icons.star),
              color: Colors.white,
              tooltip: 'Choose Your Faith',
              onPressed: _showSpiritualProfileModal,
            ),
          IconButton(
            icon: const Icon(Icons.upload_file),
            color: Colors.white,
            tooltip: 'Upload File for Analysis',
            onPressed: _pickAndSendFile,
          ),
          IconButton(
            icon: const Icon(Icons.notifications),
            color: Colors.white,
            tooltip: 'Check-in Settings',
            onPressed: _showCheckinSettings,
          ),
        ],
      ),
      body: Column(
        children: [
          Expanded(
            child: ListView.builder(
              controller: _scrollController,
              reverse: true,
              padding: const EdgeInsets.all(16),
              itemCount: _messages.length + (_isLoading && _thinkingStream != null ? 1 : 0),
              // Performance optimizations
              cacheExtent: 1000, // Cache more items for smoother scrolling
              addAutomaticKeepAlives: false, // Don't keep all items alive
              addRepaintBoundaries: true, // Optimize repainting
              itemBuilder: (_, index) {
                // Show thinking visualization at the top (index 0) when loading
                if (_isLoading && _thinkingStream != null && index == 0) {
                  return ThinkingVisualizationWidget(
                    thinkingStream: _thinkingStream!,
                    primaryColor: color,
                    coachCategory: widget.category,
                  );
                }

                // Adjust index for messages when thinking widget is shown
                final messageIndex = _isLoading && _thinkingStream != null ? index - 1 : index;
                if (messageIndex < 0 || messageIndex >= _messages.length) {
                  return const SizedBox.shrink();
                }

                final msg = _messages[messageIndex];

                if (msg.isUser) {
                  // User message - no icon, right aligned
                  return Align(
                    alignment: Alignment.centerRight,
                    child: Container(
                      margin: const EdgeInsets.symmetric(vertical: 4),
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: color.withValues(alpha: 0.9),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            msg.text,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 14,
                              fontFamily: 'Roboto',
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            '${msg.timestamp.hour.toString().padLeft(2, '0')}:${msg.timestamp.minute.toString().padLeft(2, '0')}',
                            style: const TextStyle(
                              fontSize: 10,
                              color: Colors.white70,
                              fontFamily: 'Digital-7',
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                } else {
                  // Coach message - with icon on the left
                  // Use consistent coach info logic for all categories
                  final coachInfo = CoachIconService.getCoachInfo(
                    widget.category,
                    widget.user.gender,
                    assignedCoaches: widget.user.assignedCoaches,
                  );

                  return Align(
                    alignment: Alignment.centerLeft,
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Coach icon (32x32)
                        Container(
                          width: 32,
                          height: 32,
                          margin: const EdgeInsets.only(right: 8, top: 4),
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            border: Border.all(color: color.withValues(alpha: 0.3), width: 1),
                          ),
                          child: ClipOval(
                            child: Image.asset(
                              coachInfo.iconPath,
                              width: 32,
                              height: 32,
                              fit: BoxFit.cover, // Changed to cover to fill the entire circle
                              // Enable caching for better performance
                              cacheWidth: 64, // 2x for high DPI
                              cacheHeight: 64,
                              errorBuilder: (context, error, stackTrace) {
                                // Fallback if icon is missing
                                return Container(
                                  width: 32,
                                  height: 32,
                                  decoration: BoxDecoration(
                                    color: color.withValues(alpha: 0.3),
                                    shape: BoxShape.circle,
                                  ),
                                  child: Icon(
                                    Icons.person,
                                    color: Colors.white,
                                    size: 20,
                                  ),
                                );
                              },
                            ),
                          ),
                        ),
                        // Message content
                        Expanded(
                          child: Container(
                            margin: const EdgeInsets.symmetric(vertical: 4),
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: Colors.white10,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // Coach name
                                Text(
                                  coachInfo.name,
                                  style: TextStyle(
                                    color: color,
                                    fontSize: 12,
                                    fontFamily: 'Pirulen',
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                // Message text
                                Text(
                                  msg.text,
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 14,
                                    fontFamily: 'Roboto',
                                  ),
                                ),
                                const SizedBox(height: 4),
                                // Timestamp
                                Text(
                                  '${msg.timestamp.hour.toString().padLeft(2, '0')}:${msg.timestamp.minute.toString().padLeft(2, '0')}',
                                  style: const TextStyle(
                                    fontSize: 10,
                                    color: Colors.white70,
                                    fontFamily: 'Digital-7',
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                }
              },
            ),
          ),
          if (_isLoading)
            Padding(
              padding: const EdgeInsets.only(bottom: 16),
              child: Column(
                children: [
                  const CircularProgressIndicator(color: Colors.white),
                  const SizedBox(height: 8),
                  Text(
                    _thinkingMessage,
                    style: const TextStyle(
                      color: Colors.white70,
                      fontSize: 14,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ],
              ),
            ),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(color: Colors.grey[900]),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Expanded(
                  child: ConstrainedBox(
                    constraints: const BoxConstraints(
                      minHeight: 48, // Minimum height for single line
                      maxHeight: 200, // Maximum height to prevent excessive expansion
                    ),
                    child: TextField(
                      controller: _controller,
                      style: TextStyle(color: color),
                      maxLines: null, // Allow unlimited lines
                      minLines: 1, // Start with single line
                      keyboardType: TextInputType.multiline,
                      textInputAction: TextInputAction.newline,
                      decoration: InputDecoration(
                        hintText: 'Ask your coach...',
                        hintStyle: TextStyle(color: color.withValues(alpha: 0.6)),
                        filled: true,
                        fillColor: Colors.black,
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide.none,
                        ),
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 12,
                        ),
                      ),
                      onSubmitted: (value) {
                        // Only send on Enter if Shift is not pressed
                        // For multiline, users can use Shift+Enter for new lines
                        if (!value.contains('\n')) {
                          _sendMessage();
                        }
                      },
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Padding(
                  padding: const EdgeInsets.only(bottom: 4), // Align with text field bottom
                  child: IconButton(
                    icon: Icon(Icons.send, color: color),
                    onPressed: _sendMessage,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Initialize the check-in system for this coach
  Future<void> _initializeCheckinSystem() async {
    try {
      await CoachCheckinService.initializeForUser(widget.user);

      // Update last message time if there are existing messages
      if (_messages.isNotEmpty) {
        await _updateLastMessageTime();
      }
    } catch (e) {
      if (kDebugMode) print('❌ Failed to initialize check-in system: $e');
    }
  }

  /// Update last message time for check-in scheduling
  Future<void> _updateLastMessageTime() async {
    try {
      final coachName = _coachName;
      await CoachCheckinService.updateLastMessageTime(widget.category, coachName);
    } catch (e) {
      if (kDebugMode) print('❌ Failed to update last message time: $e');
    }
  }

  /// Add a check-in message to the chat (called by notification system)
  Future<void> addCheckinMessage(String message) async {
    final checkinMessage = ChatMessage(
      text: message,
      isUser: false,
      timestamp: DateTime.now(),
    );

    setState(() {
      _messages.insert(0, checkinMessage);
    });

    await _saveChatHistory();

    // Scroll to show the new message
    if (_scrollController.hasClients) {
      await Future.delayed(const Duration(milliseconds: 100));
      _scrollController.animateTo(
        0.0,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }

  /// Show check-in settings dialog
  void _showCheckinSettings() async {
    final coachName = _coachName;
    final isEnabled = await CoachCheckinService.isCoachCheckinEnabled(widget.category, coachName);

    if (!mounted) return;

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              backgroundColor: Colors.grey[900],
              title: Text(
                'Check-in Settings',
                style: TextStyle(
                  color: Colors.white,
                  fontFamily: 'Pirulen',
                  fontSize: 18,
                ),
              ),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Coach: $coachName',
                    style: TextStyle(
                      color: Colors.white,
                      fontFamily: 'Bitsumishi',
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Proactive check-ins help keep you engaged with your ${widget.category.toLowerCase()} journey. Your coach will reach out every 24-48 hours with personalized questions.',
                    style: TextStyle(
                      color: Colors.grey[300],
                      fontFamily: 'Bitsumishi',
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(height: 20),
                  Row(
                    children: [
                      Switch(
                        value: isEnabled,
                        onChanged: (value) async {
                          final scaffoldMessenger = ScaffoldMessenger.of(context);

                          await CoachCheckinService.toggleCoachCheckins(
                            widget.category,
                            coachName,
                            value,
                          );
                          setState(() {
                            // Switch will update automatically
                          });

                          if (mounted) {
                            scaffoldMessenger.showSnackBar(
                              SnackBar(
                                content: Text(
                                  value
                                    ? 'Check-ins enabled for $coachName'
                                    : 'Check-ins disabled for $coachName',
                                  style: const TextStyle(fontFamily: 'Bitsumishi'),
                                ),
                                backgroundColor: value ? Colors.green : Colors.orange,
                                duration: const Duration(seconds: 2),
                              ),
                            );
                          }
                        },
                        activeColor: Colors.cyan,
                        inactiveThumbColor: Colors.grey,
                        inactiveTrackColor: Colors.grey[700],
                      ),
                      const SizedBox(width: 12),
                      Text(
                        isEnabled ? 'Enabled' : 'Disabled',
                        style: TextStyle(
                          color: isEnabled ? Colors.cyan : Colors.grey,
                          fontFamily: 'Bitsumishi',
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: Text(
                    'Close',
                    style: TextStyle(
                      color: Colors.cyan,
                      fontFamily: 'Bitsumishi',
                      fontSize: 16,
                    ),
                  ),
                ),
              ],
            );
          },
        );
      },
    );
  }


}
