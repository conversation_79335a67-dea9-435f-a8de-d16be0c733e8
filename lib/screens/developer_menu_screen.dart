import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'package:url_launcher/url_launcher.dart';
import 'dart:io';
import 'downloaded_transcripts_screen.dart';
import '../services/phase7_debug_service.dart';
import '../services/phase7_system_validator.dart';
import '../debug_superintelligent_test.dart';
import '../services/offline_mode_service.dart';
import '../services/release_config_service.dart';

class DeveloperMenuScreen extends StatefulWidget {
  const DeveloperMenuScreen({super.key});

  @override
  State<DeveloperMenuScreen> createState() => _DeveloperMenuScreenState();
}

class _DeveloperMenuScreenState extends State<DeveloperMenuScreen> {
  bool _isLoading = false;
  String _statusMessage = '';
  bool _phase7DebugEnabled = false;
  bool _phase7UserVisibilityEnabled = false;

  @override
  void dispose() {
    super.dispose();
  }

  /// Upload transcript file from user's device
  Future<void> _uploadTranscriptFile() async {
    try {
      setState(() {
        _isLoading = true;
        _statusMessage = '📁 Selecting transcript file...';
      });

      // Open file picker for .txt files
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['txt'],
        allowMultiple: false,
      );

      if (result != null && result.files.single.path != null) {
        final file = File(result.files.single.path!);
        final fileName = result.files.single.name;
        
        setState(() {
          _statusMessage = '💾 Saving transcript: $fileName...';
        });

        // Read file content
        final content = await file.readAsString();
        
        // Save to assets/transcripts/ directory
        final transcriptsDir = Directory('assets/transcripts');
        if (!await transcriptsDir.exists()) {
          await transcriptsDir.create(recursive: true);
        }
        
        final savedFile = File('assets/transcripts/$fileName');
        await savedFile.writeAsString(content);
        
        setState(() {
          _isLoading = false;
          _statusMessage = '✅ Transcript saved successfully: $fileName';
        });

        // Show success dialog
        _showSuccessDialog('Transcript Uploaded', 
          'File "$fileName" has been saved to your transcript library.\n\nYour AI coaches can now access this content!');
        
      } else {
        setState(() {
          _isLoading = false;
          _statusMessage = '❌ No file selected';
        });
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _statusMessage = '❌ Error uploading file: $e';
      });
    }
  }

  /// Open youtubetranscripts.io website
  Future<void> _openTranscriptWebsite() async {
    const url = 'https://www.youtubetranscripts.io/';
    try {
      if (await canLaunchUrl(Uri.parse(url))) {
        await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
        
        // Show helpful dialog
        _showInfoDialog('Get Transcripts', 
          'Opening youtubetranscripts.io...\n\n'
          '1. Enter a YouTube channel URL\n'
          '2. Download the transcript file\n'
          '3. Come back and upload it using the "📤 UPLOAD TRANSCRIPT FILE" button');
      } else {
        setState(() {
          _statusMessage = '❌ Could not open website';
        });
      }
    } catch (e) {
      setState(() {
        _statusMessage = '❌ Error opening website: $e';
      });
    }
  }

  /// Show success dialog
  void _showSuccessDialog(String title, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.grey[900],
        title: Text(
          title,
          style: TextStyle(
            color: Colors.green,
            fontFamily: 'Digital-7',
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Text(
          message,
          style: TextStyle(
            color: Colors.white,
            fontFamily: 'Digital-7',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'OK',
              style: TextStyle(
                color: Colors.green,
                fontFamily: 'Digital-7',
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Show info dialog
  void _showInfoDialog(String title, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.grey[900],
        title: Text(
          title,
          style: TextStyle(
            color: Colors.cyan,
            fontFamily: 'Digital-7',
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Text(
          message,
          style: TextStyle(
            color: Colors.white,
            fontFamily: 'Digital-7',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'GOT IT',
              style: TextStyle(
                color: Colors.cyan,
                fontFamily: 'Digital-7',
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Show App Store screenshot mode instructions
  void _showAppStoreInstructions() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.grey[900],
        title: Text(
          '🍎 App Store Screenshot Mode',
          style: TextStyle(
            color: Colors.indigo,
            fontFamily: 'Digital-7',
            fontWeight: FontWeight.bold,
          ),
        ),
        content: SingleChildScrollView(
          child: Text(
            ReleaseConfigService.appStoreScreenshotInstructions,
            style: TextStyle(
              color: Colors.white,
              fontFamily: 'Digital-7',
              fontSize: 12,
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'GOT IT',
              style: TextStyle(
                color: Colors.indigo,
                fontFamily: 'Digital-7',
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        title: Text(
          'DEVELOPER TOOLS',
          style: TextStyle(
            color: Colors.white,
            fontFamily: 'Pirulen',
            fontSize: 18,
            fontWeight: FontWeight.bold,
            shadows: [
              Shadow(
                offset: const Offset(1, 1),
                blurRadius: 2,
                color: Colors.black.withValues(alpha: 0.8),
              ),
            ],
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
            // Header
            Text(
              'TRANSCRIPT MANAGEMENT',
              style: TextStyle(
                color: Colors.white,
                fontFamily: 'Digital-7',
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),

            // Description
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[900],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.cyan),
              ),
              child: Text(
                'Upload transcript files to enhance your AI coaches with expert knowledge. '
                'All coaches will have access to uploaded transcripts.',
                style: TextStyle(
                  color: Colors.white,
                  fontFamily: 'Digital-7',
                  fontSize: 14,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            const SizedBox(height: 30),

            // 🌐 Get Transcripts from youtubetranscripts.io
            ElevatedButton.icon(
              onPressed: _openTranscriptWebsite,
              icon: const Icon(Icons.web, color: Colors.white),
              label: Text(
                '🌐 GET TRANSCRIPTS',
                style: TextStyle(
                  color: Colors.white,
                  fontFamily: 'Digital-7',
                  fontWeight: FontWeight.bold,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.teal,
                padding: const EdgeInsets.symmetric(vertical: 15),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
            const SizedBox(height: 12),

            // 📤 Upload Transcript File Button
            ElevatedButton.icon(
              onPressed: _isLoading ? null : _uploadTranscriptFile,
              icon: const Icon(Icons.upload_file, color: Colors.white),
              label: Text(
                '📤 UPLOAD TRANSCRIPT FILE',
                style: TextStyle(
                  color: Colors.white,
                  fontFamily: 'Digital-7',
                  fontWeight: FontWeight.bold,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.deepOrange,
                padding: const EdgeInsets.symmetric(vertical: 15),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
            const SizedBox(height: 12),

            // 📁 Previously Downloaded Button
            ElevatedButton.icon(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const DownloadedTranscriptsScreen(),
                  ),
                );
              },
              icon: const Icon(Icons.folder, color: Colors.white),
              label: Text(
                '📁 PREVIOUSLY DOWNLOADED',
                style: TextStyle(
                  color: Colors.white,
                  fontFamily: 'Digital-7',
                  fontWeight: FontWeight.bold,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.purple,
                padding: const EdgeInsets.symmetric(vertical: 15),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
            const SizedBox(height: 12),

            // 🍎 App Store Screenshot Mode Instructions
            ElevatedButton.icon(
              onPressed: () => _showAppStoreInstructions(),
              icon: const Icon(Icons.camera_alt, color: Colors.white),
              label: Text(
                '🍎 APP STORE SCREENSHOTS',
                style: TextStyle(
                  color: Colors.white,
                  fontFamily: 'Digital-7',
                  fontWeight: FontWeight.bold,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.indigo,
                padding: const EdgeInsets.symmetric(vertical: 15),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
            const SizedBox(height: 30),

            // Phase 7 Debug Section
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[900],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.purple),
              ),
              child: Column(
                children: [
                  Text(
                    '🔍 PHASE 7 DEBUG CONTROLS',
                    style: TextStyle(
                      color: Colors.purple,
                      fontFamily: 'Digital-7',
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Debug Mode Toggle
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Debug Mode:',
                        style: TextStyle(
                          color: Colors.white,
                          fontFamily: 'Digital-7',
                          fontSize: 14,
                        ),
                      ),
                      Switch(
                        value: _phase7DebugEnabled,
                        onChanged: (value) async {
                          setState(() {
                            _phase7DebugEnabled = value;
                          });
                          await Phase7DebugService.setDebugEnabled(value);
                          setState(() {
                            _statusMessage = value
                                ? '✅ Phase 7 Debug Mode Enabled'
                                : '✅ Phase 7 Debug Mode Disabled';
                          });
                        },
                        activeColor: Colors.purple,
                      ),
                    ],
                  ),

                  // User Visibility Toggle
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'User Visibility:',
                        style: TextStyle(
                          color: Colors.white,
                          fontFamily: 'Digital-7',
                          fontSize: 14,
                        ),
                      ),
                      Switch(
                        value: _phase7UserVisibilityEnabled,
                        onChanged: (value) async {
                          setState(() {
                            _phase7UserVisibilityEnabled = value;
                          });
                          await Phase7DebugService.setUserVisibilityEnabled(value);
                          setState(() {
                            _statusMessage = value
                                ? '✅ Phase 7 User Visibility Enabled'
                                : '✅ Phase 7 User Visibility Disabled';
                          });
                        },
                        activeColor: Colors.purple,
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // Phase 7 System Validation Button
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: _isLoading ? null : () async {
                        setState(() {
                          _isLoading = true;
                          _statusMessage = '🔧 Running Phase 7 System Validation...';
                        });

                        try {
                          final results = await Phase7SystemValidator.validateSystem();
                          setState(() {
                            _statusMessage = results.overallHealth >= 0.95
                                ? '✅ Phase 7 System Validation: DIAMOND-STANDARD QUALITY (${(results.overallHealth * 100).toInt()}%)'
                                : results.overallHealth >= 0.8
                                    ? '🟡 Phase 7 System Validation: HIGH QUALITY (${(results.overallHealth * 100).toInt()}%)'
                                    : '🔴 Phase 7 System Validation: ISSUES DETECTED (${(results.overallHealth * 100).toInt()}%)';
                          });

                          if (mounted) {
                            print(results.finalAssessment);
                          }
                        } catch (e) {
                          setState(() {
                            _statusMessage = '❌ Phase 7 Validation Failed: $e';
                          });
                        } finally {
                          setState(() {
                            _isLoading = false;
                          });
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.purple,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                      child: Text(
                        '🔧 VALIDATE PHASE 7 SYSTEM',
                        style: TextStyle(
                          fontFamily: 'Digital-7',
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 20),

            // Superintelligent AI Test Section
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[900],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.cyan),
              ),
              child: Column(
                children: [
                  Text(
                    '🧠 SUPERINTELLIGENT AI TEST',
                    style: TextStyle(
                      color: Colors.cyan,
                      fontFamily: 'Digital-7',
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),

                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: _isLoading ? null : () async {
                        setState(() {
                          _isLoading = true;
                          _statusMessage = '🧠 Running Superintelligent AI Test...';
                        });

                        try {
                          await SuperintelligentDebugTest.runDebugTest();
                          setState(() {
                            _statusMessage = '✅ Superintelligent AI Test Complete! Check console for detailed results.';
                          });
                        } catch (e) {
                          setState(() {
                            _statusMessage = '❌ Superintelligent AI Test Failed: $e';
                          });
                        } finally {
                          setState(() {
                            _isLoading = false;
                          });
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.cyan,
                        foregroundColor: Colors.black,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                      child: Text(
                        '🧠 TEST SUPERINTELLIGENT AI',
                        style: TextStyle(
                          fontFamily: 'Digital-7',
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 20),

            // AI System Controls Section
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[900],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green),
              ),
              child: Column(
                children: [
                  Text(
                    '🌐 AI SYSTEM CONTROLS',
                    style: TextStyle(
                      color: Colors.green,
                      fontFamily: 'Digital-7',
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),

                  Row(
                    children: [
                      Flexible(
                        child: ElevatedButton(
                          onPressed: _isLoading ? null : () async {
                            setState(() {
                              _isLoading = true;
                              _statusMessage = '🌐 Forcing online mode...';
                            });

                            try {
                              await OfflineModeService.forceOnlineMode();
                              setState(() {
                                _statusMessage = '✅ AI coaches are now ONLINE! Try chatting with them.';
                              });
                            } catch (e) {
                              setState(() {
                                _statusMessage = '❌ Failed to force online mode: $e';
                              });
                            } finally {
                              setState(() {
                                _isLoading = false;
                              });
                            }
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.green,
                            foregroundColor: Colors.black,
                            padding: const EdgeInsets.symmetric(vertical: 12),
                          ),
                          child: Text(
                            '🌐 FORCE ONLINE',
                            style: TextStyle(
                              fontFamily: 'Digital-7',
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Flexible(
                        child: ElevatedButton(
                          onPressed: _isLoading ? null : () async {
                            setState(() {
                              _isLoading = true;
                              _statusMessage = '🔍 Checking AI status...';
                            });

                            try {
                              final isOffline = OfflineModeService.isOfflineMode;
                              final shouldUseOffline = await OfflineModeService.shouldUseOfflineMode();

                              setState(() {
                                _statusMessage = isOffline
                                  ? '📱 AI coaches are OFFLINE (should use offline: $shouldUseOffline)'
                                  : '🌐 AI coaches are ONLINE (should use offline: $shouldUseOffline)';
                              });
                            } catch (e) {
                              setState(() {
                                _statusMessage = '❌ Failed to check AI status: $e';
                              });
                            } finally {
                              setState(() {
                                _isLoading = false;
                              });
                            }
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.blue,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 12),
                          ),
                          child: Text(
                            '🔍 CHECK STATUS',
                            style: TextStyle(
                              fontFamily: 'Digital-7',
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            const SizedBox(height: 20),

            // Status Message
            if (_statusMessage.isNotEmpty)
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey[900],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.green),
                ),
                child: Text(
                  _statusMessage,
                  style: TextStyle(
                    color: Colors.white,
                    fontFamily: 'Digital-7',
                    fontSize: 14,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),

            const SizedBox(height: 32),

            // Note Section
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[900],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'NOTE: UPLOADED TRANSCRIPTS ARE',
                    style: TextStyle(
                      color: Colors.orange,
                      fontFamily: 'Digital-7',
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                  Text(
                    'IMMEDIATELY AVAILABLE TO ALL',
                    style: TextStyle(
                      color: Colors.orange,
                      fontFamily: 'Digital-7',
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                  Text(
                    'AI COACHES.',
                    style: TextStyle(
                      color: Colors.orange,
                      fontFamily: 'Digital-7',
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    ),
    );
  }
}
