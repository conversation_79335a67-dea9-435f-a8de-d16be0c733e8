// lib/screens/set_oswidgets.dart

import 'package:flutter/material.dart';
import '../theme/colors.dart';
import 'package:provider/provider.dart';

import '../notify/widget_controller.dart';
import '../notify/widget_preferences.dart';
import '../bulletproof/error_handler.dart';
import '../controller/user_controller2.dart';

/// But<PERSON> to open a modal for configuring OS-level widgets.
class SetOsWidgetsButton extends StatelessWidget {
  const SetOsWidgetsButton({super.key});

  @override
  Widget build(BuildContext context) {
    return ElevatedButton.icon(
      icon: const Icon(Icons.widgets, color: Colors.white),
      label: const Text(
        'Configure OS Widgets',
        style: TextStyle(
          color: Colors.white,
          fontFamily: 'Bitsumishi',
          fontSize: 18,
          letterSpacing: 1.1,
        ),
      ),
      style: ElevatedButton.styleFrom(
        padding: const EdgeInsets.symmetric(horizontal: 28, vertical: 18),
        backgroundColor: MolColors.cyan,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        elevation: 6,
        shadowColor: MolColors.cyan.withValues(alpha: 0.4),
      ),
      onPressed: () => showModalBottomSheet(
        context: context,
        backgroundColor: MolColors.black,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
        ),
        isScrollControlled: true,
        builder: (_) => _buildBottomSheet(context),
      ),
    );
  }
  
  Widget _buildBottomSheet(BuildContext context) {
    final userController = Provider.of<UserController2>(context, listen: false);
    final controller = WidgetController(userController, ErrorHandler());
    return FutureBuilder<WidgetPreferences>(
      future: controller.loadPreferences(),
      builder: (context, snapshot) {
        if (!snapshot.hasData) {
          return const Center(child: CircularProgressIndicator());
        }
        final prefs = snapshot.data!;
        bool showHome = prefs.showHomeLevelWidget;
        bool showNorthStar = prefs.showNorthStarWidget;
        bool showLock = prefs.showLockScreenWidget;
        return StatefulBuilder(
          builder: (context, setState) => Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 32),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Center(
                  child: Text(
                    'OS Widget Settings',
                    style: const TextStyle(
                      color: Colors.cyanAccent,
                      fontFamily: 'Bitsumishi',
                      fontSize: 22,
                      letterSpacing: 1.2,
                    ),
                  ),
                ),
                const SizedBox(height: 24),
                SwitchListTile(
                  value: showHome,
                  onChanged: (val) async {
                    setState(() => showHome = val);
                    final updated = prefs.copyWith(showHomeLevelWidget: val);
                    await controller.updatePreferences(updated);
                  },
                  title: const Text('Show Home Level Widget', style: TextStyle(color: Colors.white, fontFamily: 'Bitsumishi')),
                  activeColor: MolColors.cyan,
                  inactiveThumbColor: Colors.grey,
                  inactiveTrackColor: Colors.grey[800],
                ),
                SwitchListTile(
                  value: showNorthStar,
                  onChanged: (val) async {
                    setState(() => showNorthStar = val);
                    final updated = prefs.copyWith(showNorthStarWidget: val);
                    await controller.updatePreferences(updated);
                  },
                  title: const Text('Show North Star Widget', style: TextStyle(color: Colors.white, fontFamily: 'Bitsumishi')),
                  activeColor: MolColors.cyan,
                  inactiveThumbColor: Colors.grey,
                  inactiveTrackColor: Colors.grey[800],
                ),
                SwitchListTile(
                  value: showLock,
                  onChanged: (val) async {
                    setState(() => showLock = val);
                    final updated = prefs.copyWith(showLockScreenWidget: val);
                    await controller.updatePreferences(updated);
                  },
                  title: const Text('Show Lock Screen Widget', style: TextStyle(color: Colors.white, fontFamily: 'Bitsumishi')),
                  activeColor: MolColors.cyan,
                  inactiveThumbColor: Colors.grey,
                  inactiveTrackColor: Colors.grey[800],
                ),
                const SizedBox(height: 24),
                Center(
                  child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: MolColors.cyan,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                    ),
                    onPressed: () async {
                      final navigator = Navigator.of(context);
                      final messenger = ScaffoldMessenger.of(context);

                      await controller.updateWidgets(prefs);
                      navigator.pop();
                      messenger.showSnackBar(
                        const SnackBar(
                          content: Text('Widget settings synced!', style: TextStyle(fontFamily: 'Bitsumishi')),
                          backgroundColor: Colors.cyan,
                        ),
                      );
                    },
                    child: const Text('Sync Now', style: TextStyle(color: Colors.black, fontFamily: 'Bitsumishi', fontSize: 16)),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
} 