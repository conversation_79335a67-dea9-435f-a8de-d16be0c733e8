// 📁 lib/screens/guest_home_screen.dart

import 'package:flutter/material.dart';
import '../models/guest_user_model.dart';
import '../services/guest_session_service.dart';
import '../screens/home_screen.dart';
import '../screens/auth_screen.dart';
import '../widgets/guest_signup_prompt_modal.dart';

/// Home screen for guest users
/// 
/// Provides a limited version of the main home screen that allows guests
/// to explore the app without creating an account. Shows signup prompts
/// when guests try to access account-required features.
class GuestHomeScreen extends StatefulWidget {
  final GuestUser guestUser;

  const GuestHomeScreen({
    super.key,
    required this.guestUser,
  });

  @override
  State<GuestHomeScreen> createState() => _GuestHomeScreenState();
}

class _GuestHomeScreenState extends State<GuestHomeScreen> {
  final GuestSessionService _guestService = GuestSessionService();
  late GuestUser _currentGuest;

  @override
  void initState() {
    super.initState();
    _currentGuest = widget.guestUser;
    
    // Mark that guest has viewed the home screen
    _guestService.markFeatureViewed('home_screen');
    
    // Check if we should prompt for signup after a delay
    Future.delayed(const Duration(seconds: 10), () {
      if (mounted && _guestService.shouldPromptForSignup()) {
        _showSignupPrompt();
      }
    });
  }

  /// Show signup prompt modal
  void _showSignupPrompt() {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) => GuestSignupPromptModal(
        message: _guestService.getSignupPromptMessage(),
        onSignUp: _navigateToSignup,
        onContinueAsGuest: () {
          Navigator.of(context).pop();
        },
      ),
    );
  }

  /// Navigate to signup/auth screen
  void _navigateToSignup() {
    Navigator.of(context).pushAndRemoveUntil(
      MaterialPageRoute(builder: (context) => const AuthScreen()),
      (route) => false,
    );
  }

  /// Handle guest sign out (return to auth screen)
  void _handleGuestSignOut() {
    _guestService.endGuestSession();
    Navigator.of(context).pushAndRemoveUntil(
      MaterialPageRoute(builder: (context) => const AuthScreen()),
      (route) => false,
    );
  }



  @override
  Widget build(BuildContext context) {
    // Convert guest user to User model for compatibility with HomeScreen
    final userModel = _currentGuest.toUserModel();

    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          // Use the existing HomeScreen but with limited functionality
          HomeScreen(
            user: userModel,
            onUserUpdated: (updatedUser) {
              // For guests, we don't actually update the user
              // This is just for UI compatibility
            },
            onSignOut: _handleGuestSignOut,
            onReset: (user) {
              // Reset guest session
              _guestService.resetGuestSession();
            },
            toggleTheme: () {
              // Theme toggle works for guests
            },
          ),
          
          // Guest mode indicator
          Positioned(
            top: MediaQuery.of(context).padding.top + 10,
            right: 10,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Colors.orange.withValues(alpha: 0.9),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(color: Colors.orange, width: 1),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(
                    Icons.visibility,
                    color: Colors.white,
                    size: 16,
                  ),
                  const SizedBox(width: 4),
                  const Text(
                    'Guest Mode',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontFamily: 'Bitsumishi',
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          // Floating signup button
          Positioned(
            bottom: 20,
            right: 20,
            child: FloatingActionButton.extended(
              heroTag: "guest_signup_fab",
              onPressed: _navigateToSignup,
              backgroundColor: Colors.cyan,
              foregroundColor: Colors.black,
              icon: const Icon(Icons.person_add),
              label: const Text(
                'Sign Up',
                style: TextStyle(
                  fontFamily: 'Pirulen',
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
