// 📁 lib/screens/countdown_timer.dart

import 'dart:async';
import 'package:flutter/material.dart';
import '../exp_data.dart';
import '../utils/audio_popup_utils.dart';
import '../models/user_model.dart';
import '../services/user_service.dart';
import '../widgets/cancel_button.dart';
import '../bulletproof/error_handler.dart';

class CountdownTimerScreen extends StatefulWidget {
  final User user;
  final String category;
  final int durationSeconds;

  const CountdownTimerScreen({
    super.key,
    required this.user,
    required this.category,
    required this.durationSeconds,
  });

  @override
  State<CountdownTimerScreen> createState() => _CountdownTimerScreenState();
}

class _CountdownTimerScreenState extends State<CountdownTimerScreen> {
  late int _remainingSeconds;
  Timer? _timer;

  @override
  void initState() {
    super.initState();

    // Use the actual duration passed from the exp entry screen
    _remainingSeconds = widget.durationSeconds;

    _startTimer();
  }

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() => _remainingSeconds--);

      if (_remainingSeconds <= 0) {
        _timer?.cancel();
        _handleTimerComplete();
      }
    });
  }

  Future<void> _handleTimerComplete() async {
    final note = await _promptForNote();

    if (note != null && note.trim().isNotEmpty) {
      final points = widget.durationSeconds == 1800 ? expFor30Min : expFor1Hour;

      await ExpData.finishEntry(widget.user, widget.category, points, note);

      final updatedUser = await UserService(ErrorHandler()).loadUser();
      if (!mounted) return;

      final navigator = Navigator.of(context);
      await AudioPopupUtils.showSuccessPopup(context, "EXP saved - keep going!");
      navigator.pop(updatedUser);
    }
  }

  Future<String?> _promptForNote() async {
    final controller = TextEditingController();

    return await showDialog<String>(
      context: context,
      builder: (_) => AlertDialog(
        backgroundColor: Colors.black,
        title: const Text("Diary Entry", style: TextStyle(color: Colors.white)),
        content: TextField(
          controller: controller,
          style: const TextStyle(color: Colors.white),
          decoration: const InputDecoration(
            hintText: 'What did you do?',
            hintStyle: TextStyle(color: Colors.grey),
            filled: true,
            fillColor: Colors.grey,
          ),
        ),
        actions: [
          TextButton(
            child: const Text("SAVE", style: TextStyle(color: Colors.amber)),
            onPressed: () => Navigator.of(context).pop(controller.text.trim()),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  String get _formattedTime {
    final minutes = _remainingSeconds ~/ 60;
    final seconds = _remainingSeconds % 60;
    return '$minutes:${seconds.toString().padLeft(2, '0')}';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          Center(
            child: Text(
              _formattedTime,
              style: const TextStyle(
                fontFamily: 'Digital-7',
                fontSize: 72,
                color: Colors.yellow,
              ),
            ),
          ),
          CancelButton(
            onCancel: () {
              _timer?.cancel();
              Navigator.of(context).pop();
            },
          ),
        ],
      ),
    );
  }
}
