// 📁 lib/screens/exp_entry_screen.dart

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../screens/countdown_timer.dart';
import '../models/user_model.dart';
import '../widgets/exp_progress.dart';
import '../utils/audio_popup_utils.dart';
import '../models/diary_entry_model.dart';
import '../utils/date_formatter.dart';
import '../services/super_entry_service.dart';
import 'package:provider/provider.dart';
import '../controller/user_controller2.dart';
import '../theme/colors.dart';

const int levelExp = 100;
const int expFor30Min = 5;
const int expFor1Hour = 10;

class ExpEntryScreen extends StatefulWidget {
  final User user;
  final String category;
  final Function(User) onUserUpdated;

  const ExpEntryScreen({
    super.key,
    required this.user,
    required this.category,
    required this.onUserUpdated,
  });

  @override
  State<ExpEntryScreen> createState() => _ExpEntryScreenState();
}

class _ExpEntryScreenState extends State<ExpEntryScreen> {
  late User currentUser;
  late int points;
  late int level;
  late int progress;
  late double percent;
  late Color color;
  late List<DiaryEntry> diaryEntries;

  @override
  void initState() {
    super.initState();
    currentUser = widget.user;
    _refresh();
  }

  void _refresh() {
    // Calculate points, level, progress, percent
    points = currentUser.getExp(widget.category);
    level = points ~/ levelExp;
    progress = points % levelExp;
    percent = progress / levelExp;

    // Determine color:
    if (currentUser.customCategories.isNotEmpty &&
        widget.category == currentUser.customCategories[0]) {
      color = MolColors.red;
    } else if (currentUser.customCategories.length > 1 &&
        widget.category == currentUser.customCategories[1]) {
      color = Colors.orange;
    } else {
      color = getCategoryColor(widget.category);
    }

    // Combine diary entries and North Star logs for this category with deduplication
    final List<DiaryEntry> allEntries = [];
    final Set<String> seenEntries = {};

    // Add regular diary entries
    final diaryList = currentUser.diaryEntries
        .where((e) => e.category == widget.category)
        .toList();

    for (final entry in diaryList) {
      // Create normalized content key for deduplication
      String normalizedNote = entry.note;
      if (entry.note.contains('[N.S. - ')) {
        final match = RegExp(r'^\[N\.S\. - .*?\]\s*(.*)').firstMatch(entry.note);
        if (match != null) {
          normalizedNote = match.group(1) ?? entry.note;
        }
      } else if (entry.note.startsWith('[N.S.]')) {
        normalizedNote = entry.note.substring(6).trim();
      }

      final timeKey = '${entry.timestamp.year}-${entry.timestamp.month.toString().padLeft(2, '0')}-${entry.timestamp.day.toString().padLeft(2, '0')}-${entry.timestamp.hour.toString().padLeft(2, '0')}-${entry.timestamp.minute.toString().padLeft(2, '0')}';
      final contentKey = '${entry.category}_${normalizedNote.hashCode}';
      final key = '${timeKey}_$contentKey';

      if (seenEntries.add(key)) {
        allEntries.add(entry);
        print('📊 EXP Entry: Added diary entry - ${entry.note.substring(0, entry.note.length > 30 ? 30 : entry.note.length)}... (key: $key)');
      } else {
        print('📊 EXP Entry: Skipped duplicate diary entry - $key');
      }
    }

    // Add North Star logs as diary entries if they match this category (only if not already added)
    if (currentUser.northStarQuest != null) {
      for (final log in currentUser.northStarQuest!.logs) {
        if (log.category == widget.category) {
          // Extract bare content from North Star log
          String bareOriginalNote = log.entryText;
          if (log.entryText.startsWith('[N.S. - ')) {
            final match = RegExp(r'^\[N\.S\. - .*?\]\s*(.*)').firstMatch(log.entryText);
            if (match != null) {
              bareOriginalNote = match.group(1) ?? log.entryText;
            }
          }

          final timeKey = '${log.loggedAt.year}-${log.loggedAt.month.toString().padLeft(2, '0')}-${log.loggedAt.day.toString().padLeft(2, '0')}-${log.loggedAt.hour.toString().padLeft(2, '0')}-${log.loggedAt.minute.toString().padLeft(2, '0')}';
          final contentKey = '${log.category}_${bareOriginalNote.hashCode}';
          final key = '${timeKey}_$contentKey';

          if (seenEntries.add(key)) {
            allEntries.add(DiaryEntry(
              id: log.id,
              timestamp: log.loggedAt,
              category: log.category,
              note: log.entryText,
              exp: (log.hours * 10).round(),
              createdAt: log.loggedAt,
              lastModified: log.loggedAt,
            ));
            print('📊 EXP Entry: Added North Star log - ${log.entryText.substring(0, log.entryText.length > 30 ? 30 : log.entryText.length)}... (key: $key)');
          } else {
            print('📊 EXP Entry: Skipped duplicate North Star log - $key');
          }
        }
      }
    }

    // Sort all entries by timestamp (newest first)
    allEntries.sort((a, b) => b.timestamp.compareTo(a.timestamp));
    diaryEntries = allEntries;

    // Debug: Log all entries for troubleshooting
    if (kDebugMode) {
      final regularCount = diaryList.length;
      final nsCount = allEntries.length - regularCount;
      print('📝 EXP Entry Screen: Found ${allEntries.length} total entries for ${widget.category}');
      print('   Regular diary entries: $regularCount');
      print('   North Star logs: $nsCount');
      if (allEntries.isNotEmpty) {
        print('📝 Latest entry: ${allEntries.first.note} (+${allEntries.first.exp} EXP)');
      }
    }
  }

  Future<void> _addExp(int durationMinutes) async {
    final noteController = TextEditingController();
    final userController = context.read<UserController2>();
    final navigator = Navigator.of(context);

    final result = await showDialog<String>(
      context: context,
      builder: (_) {
        // Responsive font sizes inside dialog
        final width = MediaQuery.of(context).size.width;
        final titleFontSize = (width / 1080) * 20;
        final inputFontSize = (width / 1080) * 18;
        final buttonFontSize = (width / 1080) * 18;

        return AlertDialog(
          backgroundColor: Colors.black,
          title: Text(
            "Diary Entry",
            style: TextStyle(
              color: Colors.white,
              fontFamily: 'Bitsumishi',
              fontSize: titleFontSize.clamp(18.0, 24.0),
            ),
          ),
          content: TextField(
            controller: noteController,
            style: TextStyle(
              color: Colors.white,
              fontSize: inputFontSize.clamp(16.0, 22.0),
            ),
            decoration: InputDecoration(
              hintText: 'What did you do?',
              hintStyle: TextStyle(
                color: Colors.grey,
                fontSize: (inputFontSize * 0.9).clamp(14.0, 20.0),
              ),
              filled: true,
              fillColor: Colors.grey[900],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () =>
                  Navigator.pop(context, noteController.text.trim()),
              child: Text(
                'SAVE',
                style: TextStyle(
                  color: Colors.amber,
                  fontSize: buttonFontSize.clamp(16.0, 20.0),
                ),
              ),
            ),
          ],
        );
      },
    );

    if (result != null && result.isNotEmpty) {
      final expEarned = (durationMinutes == 30) ? expFor30Min : expFor1Hour;

      final updatedUser = await SuperEntryService().addDiaryEntry(
        userController: userController,
        category: widget.category,
        note: result,
        exp: expEarned,
      );

      if (!mounted) return;
      await AudioPopupUtils.playSound('success.mp3');
      await AudioPopupUtils.showSuccessPopup(
        // ignore: use_build_context_synchronously
        context,
        "EXP saved - keep going!",
      );

      setState(() {
        currentUser = updatedUser;
        _refresh();
      });
      widget.onUserUpdated(updatedUser);
      navigator.pop(updatedUser);
    }
  }

  void _startTimer(int minutes) async {
    final result = await Navigator.push<User?>(
      context,
      MaterialPageRoute(
        builder: (_) => CountdownTimerScreen(
          durationSeconds: minutes * 60,
          category: widget.category,
          user: currentUser,
        ),
      ),
    );

    if (result != null) {
      setState(() {
        currentUser = result;
        _refresh();
      });
      widget.onUserUpdated(result);
      // No Navigator.pop here, because we only pop from the timer returning null
    }
  }

  Future<void> _editEntry(DiaryEntry oldEntry) async {
    final noteController = TextEditingController(text: oldEntry.note);
    final controller = context.read<UserController2>();

    final newNote = await showDialog<String>(
      context: context,
      builder: (_) {
        // Responsive font sizes inside Edit dialog
        final width = MediaQuery.of(context).size.width;
        final titleFontSize = (width / 1080) * 20;
        final inputFontSize = (width / 1080) * 18;
        final buttonFontSize = (width / 1080) * 18;

        return AlertDialog(
          backgroundColor: Colors.black,
          title: Text(
            "Edit Entry",
            style: TextStyle(
              color: Colors.white,
              fontFamily: 'Bitsumishi',
              fontSize: titleFontSize.clamp(18.0, 24.0),
            ),
          ),
          content: TextField(
            controller: noteController,
            maxLines: 3,
            style: TextStyle(
              color: Colors.white,
              fontSize: inputFontSize.clamp(16.0, 22.0),
            ),
            decoration: const InputDecoration(
              hintText: 'Update your note...',
              hintStyle: TextStyle(color: Colors.grey),
              filled: true,
              fillColor: Colors.grey,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(
                'CANCEL',
                style: TextStyle(
                  color: Colors.grey,
                  fontSize: buttonFontSize.clamp(16.0, 20.0),
                ),
              ),
            ),
            TextButton(
              onPressed: () => Navigator.pop(context, noteController.text.trim()),
              child: Text(
                'SAVE',
                style: TextStyle(
                  color: Colors.cyanAccent,
                  fontSize: buttonFontSize.clamp(16.0, 20.0),
                ),
              ),
            ),
          ],
        );
      },
    );

    if (newNote != null && newNote.isNotEmpty && newNote != oldEntry.note) {
      final updatedEntry = DiaryEntry(
        id: oldEntry.id,
        timestamp: oldEntry.timestamp,
        category: oldEntry.category,
        note: newNote,
        exp: oldEntry.exp,
        createdAt: oldEntry.createdAt,
        lastModified: DateTime.now(),
      );

      controller.updateDiaryEntry(updatedEntry);
      
      setState(() {
        currentUser = controller.user!;
        _refresh();
      });
      widget.onUserUpdated(controller.user!);
    }
  }

  @override
  Widget build(BuildContext context) {
    // Compute responsive sizing factors based on screen width
    final width = MediaQuery.of(context).size.width;
    // Base title size: 26 at 1080px → scale to current width
    final titleFontSize = (width / 1080) * 26;
    // Entry text size: 22 at 1080px → scale
    final entryFontSize = (width / 1080) * 22;
    // Subtitle date size: 18 at 1080px → scale
    final subtitleFontSize = (width / 1080) * 18;
    // Button text size: 22 at 1080px → scale
    final buttonFontSize = (width / 1080) * 22;

    return Scaffold(
      backgroundColor: MolColors.black,
      appBar: AppBar(
        backgroundColor: MolColors.black,
        title: Stack(
          alignment: Alignment.centerLeft,
          children: [
            // Black outline behind white text
            Text(
              '${widget.category} Entry',
              style: TextStyle(
                fontFamily: 'Bitsumishi',
                fontSize: titleFontSize.clamp(20.0, 32.0),
                foreground: Paint()
                  ..style = PaintingStyle.stroke
                  ..strokeWidth = (titleFontSize / 16).clamp(1.0, 2.5)
                  ..color = Colors.black,
              ),
            ),
            Text(
              '${widget.category} Entry',
              style: TextStyle(
                color: Colors.white,
                fontFamily: 'Bitsumishi',
                fontSize: titleFontSize.clamp(20.0, 32.0),
              ),
            ),
          ],
        ),
      ),
      body: Padding(
        padding: EdgeInsets.all((width / 1080 * 16).clamp(12.0, 24.0)),
        child: ListView(
          children: [
            const SizedBox(height: 16),
            Center(
              child: Stack(
                alignment: Alignment.center,
                children: [
                  Text(
                    '${widget.category}: $points EXP | Level $level',
                    style: TextStyle(
                      fontFamily: 'Bitsumishi',
                      fontSize: entryFontSize.clamp(20.0, 30.0),
                      foreground: Paint()
                        ..style = PaintingStyle.stroke
                        ..strokeWidth = (entryFontSize / 16).clamp(1.0, 2.5)
                        ..color = Colors.black,
                    ),
                  ),
                  Text(
                    '${widget.category}: $points EXP | Level $level',
                    style: TextStyle(
                      color: color,
                      fontFamily: 'Bitsumishi',
                      fontSize: entryFontSize.clamp(20.0, 30.0),
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: (width / 1080 * 24).clamp(16.0, 32.0)),

            // EXP Progress Bar
            ExpProgress(percent: percent, color: color),
            SizedBox(height: (width / 1080 * 36).clamp(24.0, 48.0)),

            // "Add 30-Min Entry Instantly" button
            ElevatedButton(
              onPressed: () => _addExp(30),
              style: ElevatedButton.styleFrom(
                backgroundColor: color,
                padding: EdgeInsets.symmetric(
                  vertical: (width / 1080 * 16).clamp(12.0, 20.0),
                  horizontal: (width / 1080 * 24).clamp(16.0, 32.0),
                ),
              ),
              child: Stack(
                alignment: Alignment.center,
                children: [
                  Text(
                    'ADD 30-MIN ENTRY INSTANTLY',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontFamily: 'Bitsumishi',
                      fontSize: buttonFontSize.clamp(18.0, 28.0),
                      foreground: Paint()
                        ..style = PaintingStyle.stroke
                        ..strokeWidth = (buttonFontSize / 16).clamp(1.0, 2.5)
                        ..color = Colors.black,
                    ),
                  ),
                  Text(
                    'ADD 30-MIN ENTRY INSTANTLY',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontFamily: 'Bitsumishi',
                      fontSize: buttonFontSize.clamp(18.0, 28.0),
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: (width / 1080 * 16).clamp(12.0, 24.0)),

            // "Add 1-Hour Entry Instantly" button
            ElevatedButton(
              onPressed: () => _addExp(60),
              style: ElevatedButton.styleFrom(
                backgroundColor: color,
                padding: EdgeInsets.symmetric(
                  vertical: (width / 1080 * 16).clamp(12.0, 20.0),
                  horizontal: (width / 1080 * 24).clamp(16.0, 32.0),
                ),
              ),
              child: Stack(
                alignment: Alignment.center,
                children: [
                  Text(
                    'ADD 1-HOUR ENTRY INSTANTLY',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontFamily: 'Bitsumishi',
                      fontSize: buttonFontSize.clamp(18.0, 28.0),
                      foreground: Paint()
                        ..style = PaintingStyle.stroke
                        ..strokeWidth = (buttonFontSize / 16).clamp(1.0, 2.5)
                        ..color = Colors.black,
                    ),
                  ),
                  Text(
                    'ADD 1-HOUR ENTRY INSTANTLY',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontFamily: 'Bitsumishi',
                      fontSize: buttonFontSize.clamp(18.0, 28.0),
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: (width / 1080 * 16).clamp(12.0, 24.0)),

            // "Start 30 Min Timer" button
            ElevatedButton(
              onPressed: () => _startTimer(30),
              style: ElevatedButton.styleFrom(
                backgroundColor: color,
                padding: EdgeInsets.symmetric(
                  vertical: (width / 1080 * 16).clamp(12.0, 20.0),
                  horizontal: (width / 1080 * 24).clamp(16.0, 32.0),
                ),
              ),
              child: Stack(
                alignment: Alignment.center,
                children: [
                  Text(
                    'START 30 MIN TIMER',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontFamily: 'Bitsumishi',
                      fontSize: buttonFontSize.clamp(18.0, 28.0),
                      foreground: Paint()
                        ..style = PaintingStyle.stroke
                        ..strokeWidth = (buttonFontSize / 16).clamp(1.0, 2.5)
                        ..color = Colors.black,
                    ),
                  ),
                  Text(
                    'START 30 MIN TIMER',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontFamily: 'Bitsumishi',
                      fontSize: buttonFontSize.clamp(18.0, 28.0),
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: (width / 1080 * 16).clamp(12.0, 24.0)),

            // "Start 1 Hour Timer" button
            ElevatedButton(
              onPressed: () => _startTimer(60),
              style: ElevatedButton.styleFrom(
                backgroundColor: color,
                padding: EdgeInsets.symmetric(
                  vertical: (width / 1080 * 16).clamp(12.0, 20.0),
                  horizontal: (width / 1080 * 24).clamp(16.0, 32.0),
                ),
              ),
              child: Stack(
                alignment: Alignment.center,
                children: [
                  Text(
                    'START 1 HOUR TIMER',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontFamily: 'Bitsumishi',
                      fontSize: buttonFontSize.clamp(18.0, 28.0),
                      foreground: Paint()
                        ..style = PaintingStyle.stroke
                        ..strokeWidth = (buttonFontSize / 16).clamp(1.0, 2.5)
                        ..color = Colors.black,
                    ),
                  ),
                  Text(
                    'START 1 HOUR TIMER',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontFamily: 'Bitsumishi',
                      fontSize: buttonFontSize.clamp(18.0, 28.0),
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
            ),
            const Divider(color: Colors.white24, height: 40),

            // ─── List each diary entry with Edit icon ───
            for (final entry in diaryEntries) ...[
              ListTile(
                contentPadding: EdgeInsets.symmetric(
                  horizontal: (width / 1080 * 16).clamp(12.0, 24.0),
                  vertical: (width / 1080 * 8).clamp(6.0, 12.0),
                ),
                title: Stack(
                  alignment: Alignment.centerLeft,
                  children: [
                    Text(
                      entry.note,
                      style: TextStyle(
                        fontFamily: 'Bitsumishi',
                        fontSize: entryFontSize.clamp(20.0, 28.0),
                        foreground: Paint()
                          ..style = PaintingStyle.stroke
                          ..strokeWidth = (entryFontSize / 16).clamp(1.0, 2.5)
                          ..color = Colors.black,
                      ),
                    ),
                    Text(
                      entry.note,
                      style: TextStyle(
                        color: Colors.white,
                        fontFamily: 'Bitsumishi',
                        fontSize: entryFontSize.clamp(20.0, 28.0),
                      ),
                    ),
                  ],
                ),
                subtitle: Text(
                  DateFormatter.formatDateTime(entry.timestamp),
                  style: TextStyle(
                    color: Colors.grey,
                    fontSize: subtitleFontSize.clamp(16.0, 24.0),
                  ),
                ),
                trailing: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      '+${entry.exp}',
                      style: TextStyle(
                        color: color,
                        fontFamily: 'Bitsumishi',
                        fontSize: entryFontSize.clamp(20.0, 28.0),
                      ),
                    ),
                    SizedBox(width: (width / 1080 * 12).clamp(8.0, 16.0)),
                    IconButton(
                      icon: Icon(Icons.edit, size: (width / 1080 * 24).clamp(20.0, 32.0)),
                      color: color,
                      onPressed: () => _editEntry(entry),
                      splashRadius: (width / 1080 * 24).clamp(20.0, 32.0),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
