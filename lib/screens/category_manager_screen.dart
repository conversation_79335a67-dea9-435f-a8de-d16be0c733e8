// 📁 lib/screens/category_manager_screen.dart

import 'package:flutter/material.dart';
import '../models/user_model.dart';
import '../widgets/maxedout_appbar.dart';
import '../cstcat/custom_category_service.dart';

class CategoryManagerScreen extends StatefulWidget {
  final User user;
  final Function(User) onUserUpdated;

  const CategoryManagerScreen({
    super.key,
    required this.user,
    required this.onUserUpdated,
  });

  @override
  State<CategoryManagerScreen> createState() => _CategoryManagerScreenState();
}

class _CategoryManagerScreenState extends State<CategoryManagerScreen> {
  late User user;
  late final CustomCategoryService _categoryService;

  @override
  void initState() {
    super.initState();
    user = widget.user;
    _categoryService = CustomCategoryService();
  }

  void _addCategory() async {
    if (user.customCategories.length >= 2) {
      _showDialog("Limit Reached", "You can only have 2 custom categories.");
      return;
    }

    final controller = TextEditingController();
    final result = await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.black,
        title: const Text('New Category', style: TextStyle(color: Colors.white)),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(
            hintText: "Enter category name",
            hintStyle: TextStyle(color: Colors.grey),
            filled: true,
            fillColor: Colors.grey,
          ),
          style: TextStyle(color: Colors.white),
        ),
        actions: [
          TextButton(
            child: const Text('Cancel', style: TextStyle(color: Colors.grey)),
            onPressed: () => Navigator.pop(context),
          ),
          TextButton(
            child: const Text('Add', style: TextStyle(color: Colors.amber)),
            onPressed: () => Navigator.pop(context, controller.text.trim()),
          ),
        ],
      ),
    );

    if (result != null && result.isNotEmpty) {
      try {
        final updated = await _categoryService.addCustomCategory(user, result);
        final latest = await _categoryService.loadUser(user.username);
        setState(() {
          user = latest ?? updated;
        });
        widget.onUserUpdated(user);
      } catch (e) {
        _showDialog("Error", e.toString());
      }
    }
  }

  void _removeCategory(String name) async {
    final confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.black,
        title: const Text("Remove Category", style: TextStyle(color: Colors.white)),
        content: Text("Are you sure you want to remove '$name'?",
            style: TextStyle(color: Colors.white70)),
        actions: [
          TextButton(
            child: const Text("Cancel", style: TextStyle(color: Colors.grey)),
            onPressed: () => Navigator.pop(context, false),
          ),
          TextButton(
            child: const Text("Remove", style: TextStyle(color: Colors.redAccent)),
            onPressed: () => Navigator.pop(context, true),
          ),
        ],
      ),
    );

    if (confirm == true) {
      try {
        final updated = await _categoryService.removeCustomCategory(user, name);
        final latest = await _categoryService.loadUser(user.username);
        setState(() {
          user = latest ?? updated;
        });
        widget.onUserUpdated(user);
      } catch (e) {
        _showDialog("Error", e.toString());
      }
    }
  }

  void _showDialog(String title, String message) {
    showDialog(
      context: context,
      builder: (_) => AlertDialog(
        backgroundColor: Colors.black,
        title: Text(title, style: TextStyle(color: Colors.white)),
        content: Text(message, style: TextStyle(color: Colors.white70)),
        actions: [
          TextButton(
            child: const Text("OK", style: TextStyle(color: Colors.amber)),
            onPressed: () => Navigator.pop(context),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final customCats = user.customCategories;

    return Scaffold(
      backgroundColor: Colors.black,
      appBar: const MaxedOutAppBar(title: "Manage EXP Categories"),
      body: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text("🛠 Custom Categories",
                style: TextStyle(color: Colors.white, fontSize: 20)),
            const SizedBox(height: 16),
            if (customCats.isEmpty)
              const Text("No custom categories yet.",
                  style: TextStyle(color: Colors.white)),
            ...customCats.map((cat) => ListTile(
                  title: Text(cat, style: TextStyle(color: Colors.white)),
                  trailing: SizedBox(
                    width: 44,
                    height: 44,
                    child: IconButton(
                      icon: const Icon(Icons.delete, color: Colors.redAccent),
                      onPressed: () => _removeCategory(cat),
                    ),
                  ),
                )),
            const SizedBox(height: 20),
            ElevatedButton.icon(
              onPressed: _addCategory,
              icon: const Icon(Icons.add),
              label: const Text("Add New Category"),
              style: ElevatedButton.styleFrom(backgroundColor: Colors.blueGrey),
            ),
            const Spacer(),
            const Center(
              child: Text("Keep it tidy 🧽",
                  style: TextStyle(color: Colors.white38)),
            ),
          ],
        ),
      ),
    );
  }
}
