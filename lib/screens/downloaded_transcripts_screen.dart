import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'dart:io';

class DownloadedTranscriptsScreen extends StatefulWidget {
  const DownloadedTranscriptsScreen({super.key});

  @override
  State<DownloadedTranscriptsScreen> createState() => _DownloadedTranscriptsScreenState();
}

class _DownloadedTranscriptsScreenState extends State<DownloadedTranscriptsScreen> {
  List<Map<String, dynamic>> _transcripts = [];
  bool _isLoading = true;
  String? _editingFileName;
  final TextEditingController _editController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadTranscripts();
  }

  @override
  void dispose() {
    _editController.dispose();
    super.dispose();
  }

  Future<void> _loadTranscripts() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final transcriptsDir = Directory('assets/transcripts');
      if (await transcriptsDir.exists()) {
        final files = await transcriptsDir.list().toList();
        final transcriptFiles = <Map<String, dynamic>>[];
        
        for (final file in files) {
          if (file.path.endsWith('.txt')) {
            final stat = await file.stat();
            transcriptFiles.add({
              'fileName': file.path.split('/').last,
              'filePath': file.path,
              'size': '${(stat.size / 1024).toStringAsFixed(1)} KB',
              'lastModified': stat.modified.toString().split('.')[0],
            });
          }
        }
        
        setState(() {
          _transcripts = transcriptFiles;
          _isLoading = false;
        });
      } else {
        setState(() {
          _transcripts = [];
          _isLoading = false;
        });
      }
    } catch (e) {
      if (kDebugMode) print('❌ Error loading transcripts: $e');
      setState(() {
        _isLoading = false;
      });
      _showSnackBar('❌ Error loading transcripts: $e');
    }
  }

  Future<void> _renameFile(String oldFileName, String newFileName) async {
    if (newFileName.trim().isEmpty) return;

    try {
      final oldFile = File('assets/transcripts/$oldFileName');
      final newFile = File('assets/transcripts/$newFileName');
      
      if (await oldFile.exists()) {
        await oldFile.rename(newFile.path);
        _showSnackBar('✅ File renamed successfully', isError: false);
        _loadTranscripts(); // Refresh the list
      } else {
        _showSnackBar('❌ File not found');
      }
    } catch (e) {
      _showSnackBar('❌ Error renaming file: $e');
    }
  }

  Future<void> _deleteFile(String fileName) async {
    final confirmed = await _showDeleteConfirmation(fileName);
    if (!confirmed) return;

    try {
      final file = File('assets/transcripts/$fileName');
      if (await file.exists()) {
        await file.delete();
        _showSnackBar('🗑️ File deleted successfully', isError: false);
        _loadTranscripts(); // Refresh the list
      } else {
        _showSnackBar('❌ File not found');
      }
    } catch (e) {
      _showSnackBar('❌ Error deleting file: $e');
    }
  }

  Future<bool> _showDeleteConfirmation(String fileName) async {
    return await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.grey[900],
        title: Text(
          'Delete File',
          style: TextStyle(
            color: Colors.red,
            fontFamily: 'Digital-7',
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Text(
          'Are you sure you want to delete "$fileName"?\n\nThis action cannot be undone.',
          style: TextStyle(
            color: Colors.white,
            fontFamily: 'Digital-7',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text(
              'CANCEL',
              style: TextStyle(
                color: Colors.grey,
                fontFamily: 'Digital-7',
              ),
            ),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: Text(
              'DELETE',
              style: TextStyle(
                color: Colors.red,
                fontFamily: 'Digital-7',
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    ) ?? false;
  }

  void _showSnackBar(String message, {bool isError = true}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: TextStyle(
            fontFamily: 'Digital-7',
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: isError ? Colors.red : Colors.green,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        title: Text(
          'DOWNLOADED TRANSCRIPTS',
          style: TextStyle(
            color: Colors.white,
            fontFamily: 'Pirulen',
            fontSize: 16,
            fontWeight: FontWeight.bold,
            shadows: [
              Shadow(
                offset: const Offset(1, 1),
                blurRadius: 2,
                color: Colors.black.withValues(alpha: 0.8),
              ),
            ],
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Colors.cyan),
              ),
            )
          : _transcripts.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.folder_open,
                        size: 64,
                        color: Colors.grey[600],
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'NO TRANSCRIPTS FOUND',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontFamily: 'Digital-7',
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Upload some transcript files to get started!',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontFamily: 'Digital-7',
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                )
              : ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: _transcripts.length,
                  itemBuilder: (context, index) {
                    final transcript = _transcripts[index];
                    final fileName = transcript['fileName'] as String;
                    final isEditing = _editingFileName == fileName;

                    return Card(
                      color: Colors.grey[900],
                      margin: const EdgeInsets.only(bottom: 12),
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // File Name (editable)
                            if (isEditing)
                              Row(
                                children: [
                                  Expanded(
                                    child: TextField(
                                      controller: _editController,
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontFamily: 'Digital-7',
                                        fontWeight: FontWeight.bold,
                                      ),
                                      decoration: InputDecoration(
                                        border: OutlineInputBorder(
                                          borderSide: BorderSide(color: Colors.cyan),
                                        ),
                                        focusedBorder: OutlineInputBorder(
                                          borderSide: BorderSide(color: Colors.cyan),
                                        ),
                                      ),
                                    ),
                                  ),
                                  IconButton(
                                    icon: const Icon(Icons.check, color: Colors.green),
                                    onPressed: () {
                                      _renameFile(fileName, _editController.text);
                                      setState(() {
                                        _editingFileName = null;
                                      });
                                    },
                                  ),
                                  IconButton(
                                    icon: const Icon(Icons.close, color: Colors.red),
                                    onPressed: () {
                                      setState(() {
                                        _editingFileName = null;
                                      });
                                    },
                                  ),
                                ],
                              )
                            else
                              Row(
                                children: [
                                  Expanded(
                                    child: Text(
                                      fileName,
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontFamily: 'Digital-7',
                                        fontWeight: FontWeight.bold,
                                        fontSize: 16,
                                      ),
                                    ),
                                  ),
                                  IconButton(
                                    icon: const Icon(Icons.edit, color: Colors.cyan),
                                    onPressed: () {
                                      setState(() {
                                        _editingFileName = fileName;
                                        _editController.text = fileName;
                                      });
                                    },
                                  ),
                                  IconButton(
                                    icon: const Icon(Icons.delete, color: Colors.red),
                                    onPressed: () => _deleteFile(fileName),
                                  ),
                                ],
                              ),

                            const SizedBox(height: 8),

                            // File Info
                            Row(
                              children: [
                                Text(
                                  'Size: ${transcript['size']}',
                                  style: TextStyle(
                                    color: Colors.grey[400],
                                    fontFamily: 'Digital-7',
                                    fontSize: 12,
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: Text(
                                    'Modified: ${transcript['lastModified']}',
                                    style: TextStyle(
                                      color: Colors.grey[400],
                                      fontFamily: 'Digital-7',
                                      fontSize: 12,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
      floatingActionButton: FloatingActionButton(
        onPressed: _loadTranscripts,
        backgroundColor: Colors.cyan,
        child: const Icon(Icons.refresh, color: Colors.black),
      ),
    );
  }
}
