// 📁 lib/screens/life_coach_screen.dart

import 'package:flutter/material.dart';
import '../models/user_model.dart';
import 'coach_chat_screen.dart';
import '../widgets/coach_access_warning_modal.dart';
import '../services/comprehensive_logging_service.dart';
import '../theme/colors.dart'; // ← Import the new colors.dart

class LifeCoachScreen extends StatelessWidget {
  final User user;

  const LifeCoachScreen({super.key, required this.user});

  /// Handle coach access with email verification check
  void _handleCoachAccess(BuildContext context, String category, User user) {
    ComprehensiveLoggingService.logInfo('🎯 Coach access requested: $category, verified: ${user.isEmailVerified}');

    // Allow direct access to coaches - email verification no longer required
    ComprehensiveLoggingService.logInfo('🎯 Proceeding to coach: $category');

    // Show info modal for unverified users (optional, non-blocking)
    if (!user.isEmailVerified) {
      ComprehensiveLoggingService.logInfo('ℹ️ Showing coach access info for unverified user');

      showDialog(
        context: context,
        barrierDismissible: true,
        builder: (context) => CoachAccessWarningModal(
          onContinue: () {
            // User chose to continue - allow access
            ComprehensiveLoggingService.logInfo('✅ User proceeding to coach: $category');
            _navigateToCoach(context, category, user);
          },
        ),
      );
    } else {
      // Email is verified - proceed directly
      ComprehensiveLoggingService.logInfo('✅ Email verified - proceeding to coach: $category');
      _navigateToCoach(context, category, user);
    }
  }

  /// Navigate to coach chat screen
  void _navigateToCoach(BuildContext context, String category, User user) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => CoachChatScreen(category: category, user: user),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // Determine the two custom category names (or fall back to placeholders).
    final custom1 = user.customCategories.isNotEmpty
        ? user.customCategories[0]
        : 'Custom Category 1';
    final custom2 = user.customCategories.length > 1
        ? user.customCategories[1]
        : 'Custom Category 2';

    // Build the six‐item category list: 4 built‐ins + 2 customs.
    final coachCategories = [
      'Health',
      'Wealth',
      'Purpose',
      'Connection',
      custom1,
      custom2,
    ];

    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        title: const Text(
          'Life Coach',
          style: TextStyle(fontFamily: 'Bitsumishi'),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Your Maxed Out Life Coaches are here.',
              style: TextStyle(
                fontSize: 20,
                color: Colors.white,
                fontFamily: 'Bitsumishi',
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'Total EXP: ${user.totalExp}',
              style: const TextStyle(
                fontSize: 16,
                color: Colors.amberAccent,
                fontFamily: 'Bitsumishi',
              ),
            ),
            const SizedBox(height: 30),
            Expanded(
              child: Center(
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: coachCategories.map((category) {
                      return Padding(
                        padding: const EdgeInsets.symmetric(vertical: 20.0),
                        child: _buildCoachCard(context, category, user),
                      );
                    }).toList(),
                  ),
                ),
              ),
            ),
            const SizedBox(height: 1),
            const Text(
              'Tap a button to counsel with your category coach.',
              style: TextStyle(color: Colors.white70, fontSize: 13),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCoachCard(BuildContext context, String category, User user) {
    // Use the centralized getCategoryColor, passing the user's custom list.
    final color = getCategoryColor(
      category,
      customCategories: user.customCategories,
    );

    // Map custom category names to proper coach categories
    String coachCategory = category;
    if (user.customCategories.contains(category)) {
      final index = user.customCategories.indexOf(category);
      coachCategory = index == 0 ? 'Custom Category 1' : 'Custom Category 2';
    }

    // Get screen width for 66% sizing
    final screenWidth = MediaQuery.of(context).size.width;
    final buttonWidth = screenWidth * 0.66;

    return GestureDetector(
      onTap: () {
        _handleCoachAccess(context, coachCategory, user);
      },
        child: Container(
          width: buttonWidth,
          height: 50,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(18),
            boxShadow: [
              // Neon glow effect
              BoxShadow(
                color: color.withValues(alpha: 0.8),
                blurRadius: 10,
                spreadRadius: 2,
              ),
              BoxShadow(
                color: color.withValues(alpha: 0.6),
                blurRadius: 15,
                spreadRadius: 4,
              ),
              BoxShadow(
                color: color.withValues(alpha: 0.4),
                blurRadius: 5,
                spreadRadius: 4,
              ),
            ],
          ),
          child: Center(
            child: Stack(
              alignment: Alignment.center,
              children: [
                // Outline text
                Text(
                  category,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontFamily: 'Bitsumishi',
                    fontSize: 32,
                    fontWeight: FontWeight.bold,
                    foreground: Paint()
                      ..style = PaintingStyle.stroke
                      ..strokeWidth = 1
                      ..color = Colors.black,
                  ),
                ),
                // Filled text
                Text(
                  category,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontFamily: 'Bitsumishi',
                    fontSize: 32,
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ),
    );
  }
}
