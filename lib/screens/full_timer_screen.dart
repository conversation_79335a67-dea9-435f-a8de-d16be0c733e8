// 📁 lib/screens/full_timer_screen.dart

import 'dart:async';
import 'package:flutter/material.dart';
import '../models/user_model.dart';
import 'package:audioplayers/audioplayers.dart';
import 'dart:math'; 

class FullTimerScreen extends StatefulWidget {
  final User user;
  final String category;
  final int durationSeconds;

  const FullTimerScreen({
    super.key,
    required this.user,
    required this.category,
    required this.durationSeconds,
  });

  @override
  State<FullTimerScreen> createState() => _FullTimerScreenState();
}

class _FullTimerScreenState extends State<FullTimerScreen> {
  late int _remainingSeconds;
  Timer? _timer;
  bool _isPaused = false;

  @override
  void initState() {
    super.initState();
    _remainingSeconds = widget.durationSeconds;
    _startTimer();
  }

  void _startTimer() {
    // Run tick every real second
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_isPaused) return;

      if (_remainingSeconds <= 0) {
        timer.cancel();
        _playFinishSound();
        // Optionally switch to break screen here
      } else {
        setState(() => _remainingSeconds--);
      }
    });
  }

  Future<void> _playFinishSound() async {
    final player = AudioPlayer();
    await player.play(AssetSource('assets/sounds/lightning.mp3'));
  }

  void _togglePause() {
    setState(() {
      _isPaused = !_isPaused;
    });
  }

  void _cancelTimer() {
    _timer?.cancel();
    Navigator.pop(context);
  }

  String _formatTime(int seconds) {
    final m = seconds ~/ 60;
    final s = seconds % 60;
    return '${m.toString().padLeft(2, '0')}:${s.toString().padLeft(2, '0')}';
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final glow = Colors.greenAccent; // or get from widget.category

    // Calculate responsive sizes based on screen width:
    final width = MediaQuery.of(context).size.width;
    final baseFontSize = (width / 1920) * 64;
    final timerFontSize = baseFontSize.clamp(32.0, 96.0);

    // Ring diameter: 15% of width, clamped 200–400px
    final ringDiameter = (width * 0.15).clamp(200.0, 400.0);

    final double progress =
        _remainingSeconds / widget.durationSeconds.clamp(1, double.infinity);

    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: Column(
          children: [
            const SizedBox(height: 24),

            // 1) Linear progress bar at top
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32.0),
              child: LinearProgressIndicator(
                value: progress,
                backgroundColor: Colors.grey[850],
                valueColor: AlwaysStoppedAnimation<Color>(glow),
                minHeight: 8,
              ),
            ),
            const SizedBox(height: 24),

            // 2) Circular ring + time text
            SizedBox(
              height: ringDiameter,
              width: ringDiameter,
              child: CustomPaint(
                painter: _RingPainter(progress: progress, glowColor: glow),
                child: Center(
                  child: Stack(
                    alignment: Alignment.center,
                    children: [
                      // Black‐outline digital time
                      Text(
                        _formatTime(_remainingSeconds),
                        style: TextStyle(
                          fontFamily: 'Digital-7',
                          fontSize: timerFontSize,
                          foreground: Paint()
                            ..style = PaintingStyle.stroke
                            ..strokeWidth = (timerFontSize / 32)
                            ..color = Colors.black,
                        ),
                      ),
                      Text(
                        _formatTime(_remainingSeconds),
                        style: TextStyle(
                          fontFamily: 'Digital-7',
                          fontSize: timerFontSize,
                          color: glow,
                          shadows: [
                            Shadow(
                              color: glow.withValues(alpha: 0.8),
                              blurRadius: 12,
                              offset: const Offset(0, 0),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),

            const SizedBox(height: 50),

            // 3) Pause/Resume and Cancel buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ElevatedButton(
                  onPressed: _togglePause,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: glow,
                    padding:
                        const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(24),
                    ),
                  ),
                  child: Text(
                    _isPaused ? 'RESUME' : 'PAUSE',
                    style: TextStyle(
                      fontSize: 26,
                      fontFamily: 'Bitsumishi',
                      color: Colors.white,
                      shadows: [
                        const Shadow(
                          blurRadius: 8,
                          color: Colors.black,
                          offset: Offset(0, 0),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(width: 24),
                ElevatedButton(
                  onPressed: _cancelTimer,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.redAccent,
                    padding:
                        const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(24),
                    ),
                  ),
                  child: const Text(
                    'CANCEL',
                    style: TextStyle(
                      fontSize: 26,
                      fontFamily: 'Bitsumishi',
                      color: Colors.white,
                    ),
                  ),
                ),
              ],
            ),

            const Spacer(),

            // 4) (Optional) Motivational quote
            Padding(
              padding: const EdgeInsets.only(bottom: 24.0),
              child: Text(
                '“Focus on progress, not perfection.”',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontFamily: 'Pirulen',
                  fontSize: (width / 1920 * 32).clamp(20.0, 36.0),
                  color: Colors.white70,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _RingPainter extends CustomPainter {
  final double progress;
  final Color glowColor;

  _RingPainter({required this.progress, required this.glowColor});

  @override
  void paint(Canvas canvas, Size size) {
    final double strokeWidth = 12.0;
    final Paint backgroundPaint = Paint()
      ..color = Colors.grey[900]!
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke;
    final Paint progressPaint = Paint()
      ..color = glowColor
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 8);

    final center = Offset(size.width / 2, size.height / 2);
    final radius = (size.width - strokeWidth) / 2;

    // Draw background circle
    canvas.drawCircle(center, radius, backgroundPaint);

    // Draw progress arc
    final double sweepAngle = 2 * pi * progress;
    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      -pi / 2,
      sweepAngle,
      false,
      progressPaint,
    );
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => true;
}
