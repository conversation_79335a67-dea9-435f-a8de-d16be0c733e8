import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../onboarding/onboarding_screen_02.dart';
import '../screens/home_screen.dart';
import '../controller/user_controller2.dart';
import '../controller/firebase_auth_controller.dart';
import '../models/user_model.dart';
import '../widgets/firebase_auth_signin_modal.dart';
import '../home/<USER>';
import '../services/guest_session_service.dart';
import '../screens/guest_home_screen.dart';

class AuthScreen extends StatelessWidget {
  const AuthScreen({super.key});

  void _openMusicModal(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (_) => const MusicControlModal(),
      isScrollControlled: true,
      enableDrag: true,
      showDragHandle: true,
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        // Dismiss keyboard when tapping anywhere on the screen
        FocusScope.of(context).unfocus();
      },
      child: Scaffold(
        body: Stack(
        fit: StackFit.expand,
        children: [
          Positioned.fill(
            child: Image.asset(
              'assets/images/MXD_Logo_2.1.jpg',
              fit: BoxFit.cover,
            ),
          ),
          Positioned(
            top: MediaQuery.of(context).size.height * 0.71, // Position underneath the X
            left: 0,
            right: 0,
            child: Center(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                    Opacity(
                      opacity: 0.8,
                      child: SizedBox(
                        width: 120, // Smaller width, centered above Sign In button
                        height: 36, // 7.5% smaller than sign in button (36 * 0.925)
                        child: ElevatedButton(
                          onPressed: () async {
                            try {
                              final result = await Navigator.push<User?>(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => const OnboardingScreen(),
                                ),
                              );

                              // 🛡️ PHASE 3: Enhanced context checking and error handling
                              if (result != null && context.mounted) {
                                final userController = Provider.of<UserController2>(context, listen: false);
                                await userController.updateUser(result);

                                if (context.mounted) {
                                  // Navigate to HomeScreen and remove all previous routes
                                  Navigator.of(context).pushAndRemoveUntil(
                                    MaterialPageRoute(
                                      builder: (context) => HomeScreen(
                                        user: result,
                                        onUserUpdated: (updatedUser) {
                                          if (context.mounted) {
                                            userController.updateUser(updatedUser);
                                          }
                                        },
                                        onSignOut: () {
                                          if (context.mounted) {
                                            // Handle sign out - navigate back to auth
                                            Navigator.of(context).pushAndRemoveUntil(
                                              MaterialPageRoute(builder: (context) => const AuthScreen()),
                                              (route) => false,
                                            );
                                          }
                                        },
                                        onReset: (user) {
                                          // Handle reset - could clear user data
                                          userController.clearAuthData();
                                        },
                                        toggleTheme: () {
                                          // Handle theme toggle - placeholder for now
                                        },
                                      ),
                                    ),
                                    (route) => false, // Remove all previous routes
                                  );
                                }
                              }
                            } catch (e) {
                              // Handle any navigation errors gracefully
                              debugPrint('❌ Error during signup navigation: $e');
                            }
                          },
                          style: ElevatedButton.styleFrom(
                            padding: EdgeInsets.zero,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(18),
                            ),
                            elevation: 8,
                            backgroundColor: Colors.transparent,
                          ),
                          child: Ink(
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [
                                  const Color.fromARGB(255, 120, 0, 180), // Deep purple
                                  const Color.fromARGB(255, 0, 3, 103), // Deep blue
                                ],
                                begin: Alignment.centerLeft,
                                end: Alignment.centerRight,
                              ),
                              borderRadius: BorderRadius.circular(18),
                            ),
                            child: Container(
                              alignment: Alignment.center,
                              child: Text(
                                'SIGN UP',
                                style: TextStyle(
                                  fontFamily: 'Pirulen',
                                  fontSize: 14,
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                  letterSpacing: 2,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 7),
                    Opacity(
                      opacity: 0.8,
                      child: SizedBox(
                        width: 140, // Width to match the X in MXD logo
                        height: 36, // Original height for sign in button
                        child: ElevatedButton(
                          onPressed: () async {
                            try {
                              // 🛡️ PHASE 3: Enhanced error handling for sign-in
                              // Show sign-in modal for credential input
                              showDialog(
                              context: context,
                              barrierDismissible: false,
                              builder: (context) => FirebaseAuthSignInModal(
                                onSuccess: (email) async {
                                  Navigator.of(context).pop(); // Close modal

                                  // Get the signed-in user from Firebase controller
                                  final firebaseController = Provider.of<FirebaseAuthController>(context, listen: false);
                                  User? result = firebaseController.localUser;

                                  // If we have a user, proceed to home
                                  if (result != null && context.mounted) {
                                    final userController = Provider.of<UserController2>(context, listen: false);
                                    await userController.updateUser(result);

                                    if (context.mounted) {
                                      // Navigate to HomeScreen and remove all previous routes
                                      Navigator.of(context).pushAndRemoveUntil(
                                        MaterialPageRoute(
                                          builder: (context) => HomeScreen(
                                            user: result,
                                            onUserUpdated: (updatedUser) {
                                              userController.updateUser(updatedUser);
                                            },
                                            onSignOut: () {
                                              // Handle sign out - navigate back to auth
                                              Navigator.of(context).pushAndRemoveUntil(
                                                MaterialPageRoute(builder: (context) => const AuthScreen()),
                                                (route) => false,
                                              );
                                            },
                                            onReset: (user) {
                                              // Handle reset - could clear user data
                                              userController.clearAuthData();
                                            },
                                            toggleTheme: () {
                                              // Handle theme toggle - placeholder for now
                                            },
                                          ),
                                        ),
                                        (route) => false, // Remove all previous routes
                                      );
                                    }
                                  } else {
                                    // If no user found, show error
                                    if (context.mounted) {
                                      ScaffoldMessenger.of(context).showSnackBar(
                                        const SnackBar(
                                          content: Text('Sign in failed. Please try again.'),
                                          backgroundColor: Colors.red,
                                        ),
                                      );
                                    }
                                  }
                                },
                                onBack: () {
                                  Navigator.of(context).pop(); // Close sign in modal
                                },
                              ),
                            );
                            } catch (e) {
                              // Handle any sign-in errors gracefully
                              debugPrint('❌ Error during sign-in: $e');
                            }
                          },
                          style: ElevatedButton.styleFrom(
                            padding: EdgeInsets.zero,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(18),
                            ),
                            elevation: 8,
                            backgroundColor: Colors.transparent,
                          ),
                          child: Ink(
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [
                                  const Color.fromARGB(255, 0, 3, 103), // Deep blue
                                  const Color.fromARGB(255, 120, 0, 180), // Deep purple
                                ],
                                begin: Alignment.centerLeft,
                                end: Alignment.centerRight,
                              ),
                              borderRadius: BorderRadius.circular(18),
                            ),
                            child: Container(
                              alignment: Alignment.center,
                              child: Text(
                                'SIGN IN',
                                style: TextStyle(
                                  fontFamily: 'Pirulen',
                                  fontSize: 14,
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                  letterSpacing: 2,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),

                    const SizedBox(height: 125),

                    // Guest mode button
                    Opacity(
                      opacity: 0.7,
                      child: SizedBox(
                        width: 100,
                        height: 32,
                        child: ElevatedButton(
                          onPressed: () async {
                            try {
                              // Create guest session and navigate to onboarding
                              final guestService = GuestSessionService();
                              final guestUser = guestService.createGuestSession();

                              // Convert guest user to User model for onboarding
                              final guestAsUser = guestUser.toUserModel();

                              final result = await Navigator.push<User?>(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => OnboardingScreen(initialUser: guestAsUser),
                                ),
                              );

                              // After onboarding, navigate to guest home screen
                              if (result != null && context.mounted) {
                                Navigator.of(context).pushAndRemoveUntil(
                                  MaterialPageRoute(
                                    builder: (context) => GuestHomeScreen(guestUser: guestUser),
                                  ),
                                  (route) => false,
                                );
                              }
                            } catch (e) {
                              print('❌ Guest mode error: $e');
                              if (context.mounted) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text('Failed to start guest mode. Please try again.'),
                                    backgroundColor: Colors.red,
                                  ),
                                );
                              }
                            }
                          },
                          style: ElevatedButton.styleFrom(
                            padding: EdgeInsets.zero,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(16),
                            ),
                            elevation: 4,
                            backgroundColor: Colors.transparent,
                          ),
                          child: Ink(
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [
                                  const Color.fromARGB(255, 0, 3, 103),
                                  const Color.fromARGB(255, 174, 0, 255),
                                ],
                                begin: Alignment.centerLeft,
                                end: Alignment.centerRight,
                              ),
                              borderRadius: BorderRadius.circular(16),
                            ),
                            child: Container(
                              alignment: Alignment.center,
                              child: const Text(
                                'Guest',
                                style: TextStyle(
                                  fontFamily: 'Pirulen',
                                  fontSize: 14,
                                  color: Colors.white,
                                  fontWeight: FontWeight.normal,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
        ],
      ),
      // ─── Floating "Music" Button at bottom right ───
      floatingActionButton: SizedBox(
        width: 56,
        height: 56,
        child: FloatingActionButton(
          heroTag: "auth_music_fab", // Unique hero tag
          backgroundColor: Colors.black87,
          onPressed: () => _openMusicModal(context),
          child: const Icon(Icons.music_note, color: Colors.cyanAccent, size: 24),
        ),
      ),
    ),
    );
  }
}