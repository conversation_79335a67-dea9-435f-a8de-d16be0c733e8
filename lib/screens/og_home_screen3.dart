// 📁 lib/screens/home_screen3.dart

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/user_model.dart';
import '../controller/user_controller2.dart';
//import 'package:maxed_out_life/services/music_service.dart';
import '../home/<USER>';

//import '../home/<USER>';
import '../home/<USER>';
import '../home/<USER>';
import '../home/<USER>';
import '../home/<USER>';
import '../home/<USER>';
import '../home/<USER>';
import '../diary/super_feed.dart';
import '../home/<USER>';
import '../home/<USER>';
import '../home/<USER>';
import '../home/<USER>';

import '../theme/colors.dart';
//import '../quests/north_star_model.dart';

// Define base categories
const List<String> baseCategories = [
  'Health',
  'Wealth',
  'Purpose',
  'Connection',
];

class HomeScreen3 extends StatefulWidget {
  final User user;
  final VoidCallback onSignOut;
  final Function(User?) onReset;
  final VoidCallback toggleTheme;
  final Function(User) onUserUpdated;

  const HomeScreen3({
    super.key,
    required this.user,
    required this.onSignOut,
    required this.onReset,
    required this.toggleTheme,
    required this.onUserUpdated,
  });

  @override
  State<HomeScreen3> createState() => _HomeScreen3State();
}

class _HomeScreen3State extends State<HomeScreen3> with RouteAware {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _refreshUser(User updated) {
    widget.onUserUpdated(updated);
  }

  void _openMusicModal() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (_) => const MusicControlModal(),
      isScrollControlled: true,
      enableDrag: true,
      showDragHandle: true,
    );
  }

  @override
  Widget build(BuildContext context) {
    final userController = Provider.of<UserController2>(context);
    final user = userController.user;
    if (user == null) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    final quest = user.northStarQuest;
    final glowColor = quest?.glowColor ?? Colors.purpleAccent;

    // Build category list from user's categories map
    final allCategories = [
      ...baseCategories,
      ...user.categories.keys
    ].where((c) => !c.startsWith('N.S.')).toList();

    // Category → Color map
    final Map<String, Color> defaultCategoryColorsMap = {
      for (final cat in allCategories)
        cat: getCategoryColor(
          cat,
          customCategories: user.categories.keys.toList(),
        )
    };

    final List<Color> fallbackColorsList = List<Color>.filled(
      allCategories.length,
      Colors.black,
    );

    return HmRefreshWrapper(
      onUserUpdated: widget.onUserUpdated,
      updateLocalUser: _refreshUser,
      child: Scaffold(
        backgroundColor: Colors.black,
        body: SafeArea(
          child: CustomScrollView(
            controller: _scrollController,
            slivers: [
              // ─── Welcome & Top Buttons
              SliverPadding(
                padding: const EdgeInsets.all(16),
                sliver: SliverList(
                  delegate: SliverChildListDelegate([
                    HmWelcomeHeader(username: user.username),
                    HmTopNavigationButtons(
                      currentUser: user,
                      onUserUpdated: _refreshUser,
                    ),
                    const SizedBox(height: 12),
                  ]),
                ),
              ),

              // ─── EXP Bar
              SliverToBoxAdapter(
                child: HmExpBar(user: user),
              ),

              // ─── EXP Categories
              SliverPadding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                sliver: SliverList(
                  delegate: SliverChildListDelegate([
                    HmExpTrackers(
                      user: user,
                      categories: allCategories,
                      onUserUpdated: _refreshUser,
                    ),
                  ]),
                ),
              ),

              // ─── Supercharge, 7DD, Streak, Diary
              SliverPadding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                sliver: SliverList(
                  delegate: SliverChildListDelegate([
                    const SizedBox(height: 30),
                    HmSuperchargeButton(glowColor: glowColor),
                    const SizedBox(height: 20),
                    HmSevenDayDevelopmentButton(user: user),
                    const SizedBox(height: 20),
                    HmStreakDisplay(
                      user: user,
                      glowColor: glowColor,
                    ),
                    const SizedBox(height: 30),
                    SuperFeed(
                      user: user,
                      scrollController: _scrollController,
                      fallbackColors: fallbackColorsList,
                      defaultCategoryColors: defaultCategoryColorsMap,
                    ),
                  ]),
                ),
              ),

              // ─── North Star Quest Section
              if (quest != null) ...[
                SliverPadding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  sliver: SliverList(
                    delegate: SliverChildListDelegate([
                      const SizedBox(height: 30),
                      ...buildNorthStarSlivers(
                        user: user,
                        onQuestUpdate: _refreshUser,
                      ),
                    ]),
                  ),
                ),
              ],

              // ─── Admin Tools
              SliverPadding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                sliver: SliverList(
                  delegate: SliverChildListDelegate([
                    const SizedBox(height: 30),
                    HmAdminControls(
                      user: user,
                      onSignOut: widget.onSignOut,
                      onReset: widget.onReset,
                      toggleTheme: widget.toggleTheme,
                    ),
                  ]),
                ),
              ),

              // ─── Extra bottom spacing so modal button doesn't overlap
              SliverToBoxAdapter(child: SizedBox(height: 80)),
            ],
          ),
        ),

        // ─── Floating "Music" Button at bottom right ───
        floatingActionButton: SizedBox(
          width: 56, // Restored to standard 56px for proper touch target
          height: 56,
          child: FloatingActionButton(
            heroTag: "og_home_music_fab", // Unique hero tag
            backgroundColor: Colors.black87,
            onPressed: _openMusicModal,
            child: const Icon(Icons.music_note, color: Colors.cyanAccent, size: 24), // Restored to standard size
          ),
        ),
      ),
    );
  }
}
