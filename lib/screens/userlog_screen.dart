// 📁 lib/screens/userlog_screen.dart

import 'package:flutter/material.dart';
import '../models/user_model.dart';
import '../services/user_service.dart';
import 'home_screen.dart';
import '../widgets/empty_state_widget.dart';
//import '../screens/home_screen3.dart';

class UserLogScreen extends StatefulWidget {
  final void Function(User user) onUserSelected;
  final VoidCallback toggleTheme;

  const UserLogScreen({
    super.key,
    required this.onUserSelected,
    required this.toggleTheme,
  });

  @override
  State<UserLogScreen> createState() => _UserLogScreenState();
}

class _UserLogScreenState extends State<UserLogScreen> {
  final TextEditingController _controller = TextEditingController();
  List<String> _usernames = [];

  @override
  void initState() {
    super.initState();
    _loadUsernames();
  }

  Future<void> _loadUsernames() async {
    final usernames = await UserService.getAllUsernamesStatic();
    setState(() => _usernames = usernames);
  }

  Future<void> _enterUser(String username) async {
    final user = await UserService.loadUserByUsernameStatic(username);
    if (!mounted || user == null) return;

    Navigator.pushReplacement(
      context,
      MaterialPageRoute(
        builder: (_) => HomeScreen(
          user: user,
          toggleTheme: widget.toggleTheme,
          onSignOut: () => Navigator.pushReplacement(
            context,
            MaterialPageRoute(
              builder: (_) => UserLogScreen(
                onUserSelected: widget.onUserSelected,
                toggleTheme: widget.toggleTheme,
              ),
            ),
          ),
          onReset: (_) => Navigator.pushReplacement(
            context,
            MaterialPageRoute(
              builder: (_) => UserLogScreen(
                onUserSelected: widget.onUserSelected,
                toggleTheme: widget.toggleTheme,
              ),
            ),
          ),
          onUserUpdated: (updatedUser) async {
            await UserService.saveUserByUsername(updatedUser);
          },
        ),
      ),
    );
  }

  Future<void> _deleteUser(String username) async {
    await UserService.deleteUserStatic(username);
    await _loadUsernames();
  }

  Future<void> _createUser(String username) async {
    if (username.isEmpty || _usernames.contains(username) || _usernames.length >= 3) return;

    await UserService.createUser(username);
    _controller.clear();
    await _loadUsernames();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        elevation: 0,
        title: const Text(
          'MAXED OUT LIFE',
          style: TextStyle(
            fontFamily: 'Bitsumishi',
            fontSize: 20,
            color: Colors.amberAccent,
            letterSpacing: 1.2,
          ),
        ),
        actions: [
          IconButton(
            onPressed: _loadUsernames,
            icon: const Icon(Icons.refresh, color: Colors.white),
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 28, vertical: 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Select your profile or create a new one:',
              style: TextStyle(color: Colors.white70, fontSize: 16),
            ),
            const SizedBox(height: 18),
            Expanded(
              child: _usernames.isEmpty
                  ? const EmptyStateWidget(
                      type: EmptyStateType.users,
                      showAction: false,
                    )
                  : ListView.separated(
                      itemCount: _usernames.length,
                      separatorBuilder: (_, __) => const SizedBox(height: 14),
                      itemBuilder: (context, index) {
                        final username = _usernames[index];
                        return Container(
                          decoration: BoxDecoration(
                            color: Colors.white10,
                            borderRadius: BorderRadius.circular(12),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.tealAccent.withValues(alpha: 0.2),
                                blurRadius: 6,
                                offset: const Offset(0, 2),
                              )
                            ],
                          ),
                          child: ListTile(
                            title: Text(
                              username,
                              style: const TextStyle(
                                fontFamily: 'Bitsumishi',
                                color: Colors.white,
                                fontSize: 16,
                              ),
                            ),
                            trailing: IconButton(
                              icon: const Icon(Icons.delete_forever, color: Colors.redAccent),
                              onPressed: () => _deleteUser(username),
                            ),
                            onTap: () => _enterUser(username),
                          ),
                        );
                      },
                    ),
            ),
            const SizedBox(height: 30),
            if (_usernames.length < 3) ...[
              TextField(
                controller: _controller,
                style: const TextStyle(color: Colors.white),
                decoration: InputDecoration(
                  hintText: 'New username',
                  hintStyle: const TextStyle(color: Colors.white30),
                  filled: true,
                  fillColor: Colors.grey[850],
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide.none,
                  ),
                  contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
                ),
              ),
              const SizedBox(height: 12),
              ElevatedButton.icon(
                onPressed: () => _createUser(_controller.text.trim()),
                icon: const Icon(Icons.add, color: Colors.black),
                label: const Text(
                  'Create Profile',
                  style: TextStyle(fontFamily: 'Bitsumishi'),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.tealAccent,
                  foregroundColor: Colors.black,
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 14),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  textStyle: const TextStyle(fontSize: 16),
                ),
              ),
            ],
            if (_usernames.length >= 3)
              const Text(
                'Maximum of 3 profiles reached.',
                style: TextStyle(color: Colors.redAccent, fontSize: 13),
              ),
          ],
        ),
      ),
    );
  }
}
