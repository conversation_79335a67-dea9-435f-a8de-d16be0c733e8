import 'package:flutter/material.dart';
import 'package:timezone/timezone.dart' as tz;
import '../bulletproof/error_handler.dart';

/// Utility class for notification-related functions.
class NotificationUtils {
  static final _errorHandler = ErrorHandler();

  /// Format a time of day for display.
  static String formatTimeOfDay(TimeOfDay time) {
    final hour = time.hour.toString().padLeft(2, '0');
    final minute = time.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }

  /// Parse a time string into TimeOfDay.
  static TimeOfDay? parseTimeString(String timeString) {
    try {
      final parts = timeString.split(':');
      if (parts.length != 2) return null;

      final hour = int.tryParse(parts[0]);
      final minute = int.tryParse(parts[1]);

      if (hour == null || minute == null) return null;
      if (hour < 0 || hour > 23 || minute < 0 || minute > 59) return null;

      return TimeOfDay(hour: hour, minute: minute);
    } catch (e, stackTrace) {
      _errorHandler.handleError(e, stackTrace, context: 'Parsing time string');
      return null;
    }
  }

  /// Calculate the next notification time.
  static DateTime calculateNextNotificationTime(int hour, int minute) {
    try {
      final now = DateTime.now();
      var scheduledDate = DateTime(
        now.year,
        now.month,
        now.day,
        hour,
        minute,
      );

      if (scheduledDate.isBefore(now)) {
        scheduledDate = scheduledDate.add(const Duration(days: 1));
      }

      return scheduledDate;
    } catch (e, stackTrace) {
      _errorHandler.handleError(e, stackTrace, context: 'Calculating next notification time');
      return DateTime.now().add(const Duration(days: 1));
    }
  }

  /// Format a duration for display.
  static String formatDuration(Duration duration) {
    try {
      final hours = duration.inHours;
      final minutes = duration.inMinutes.remainder(60);

      if (hours > 0) {
        return '$hours hour${hours == 1 ? '' : 's'} '
            '${minutes > 0 ? 'and $minutes minute${minutes == 1 ? '' : 's'}' : ''}';
      }

      return '$minutes minute${minutes == 1 ? '' : 's'}';
    } catch (e, stackTrace) {
      _errorHandler.handleError(e, stackTrace, context: 'Formatting duration');
      return 'Unknown duration';
    }
  }

  /// Get a localized time string.
  static String getLocalizedTimeString(BuildContext context, DateTime dateTime) {
    try {
      return TimeOfDay.fromDateTime(dateTime).format(context);
    } catch (e, stackTrace) {
      _errorHandler.handleError(e, stackTrace, context: 'Getting localized time string');
      return dateTime.toString();
    }
  }

  /// Get a localized date string.
  static String getLocalizedDateString(DateTime dateTime) {
    try {
      return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
    } catch (e, stackTrace) {
      _errorHandler.handleError(e, stackTrace, context: 'Getting localized date string');
      return dateTime.toString();
    }
  }

  /// Get a localized date and time string.
  static String getLocalizedDateTimeString(BuildContext context, DateTime dateTime) {
    try {
      return '${getLocalizedDateString(dateTime)} ${getLocalizedTimeString(context, dateTime)}';
    } catch (e, stackTrace) {
      _errorHandler.handleError(e, stackTrace, context: 'Getting localized date and time string');
      return dateTime.toString();
    }
  }

  /// Get a relative time string (e.g., "2 hours ago").
  static String getRelativeTimeString(DateTime dateTime) {
    try {
      final now = DateTime.now();
      final difference = now.difference(dateTime);

      if (difference.inDays > 0) {
        return '${difference.inDays} day${difference.inDays == 1 ? '' : 's'} ago';
      }

      if (difference.inHours > 0) {
        return '${difference.inHours} hour${difference.inHours == 1 ? '' : 's'} ago';
      }

      if (difference.inMinutes > 0) {
        return '${difference.inMinutes} minute${difference.inMinutes == 1 ? '' : 's'} ago';
      }

      return 'Just now';
    } catch (e, stackTrace) {
      _errorHandler.handleError(e, stackTrace, context: 'Getting relative time string');
      return 'Unknown time';
    }
  }

  /// Get a timezone-aware date time.
  static tz.TZDateTime getTZDateTime(DateTime dateTime) {
    try {
      return tz.TZDateTime.from(dateTime, tz.local);
    } catch (e, stackTrace) {
      _errorHandler.handleError(e, stackTrace, context: 'Getting timezone-aware date time');
      return tz.TZDateTime.now(tz.local);
    }
  }

  /// Get a timezone-aware date time for a specific time.
  static tz.TZDateTime getTZDateTimeForTime(int hour, int minute) {
    try {
      final now = tz.TZDateTime.now(tz.local);
      return tz.TZDateTime(
        tz.local,
        now.year,
        now.month,
        now.day,
        hour,
        minute,
      );
    } catch (e, stackTrace) {
      _errorHandler.handleError(e, stackTrace, context: 'Getting timezone-aware date time for time');
      return tz.TZDateTime.now(tz.local);
    }
  }

  /// Get a timezone-aware date time for tomorrow at a specific time.
  static tz.TZDateTime getTZDateTimeForTomorrow(int hour, int minute) {
    try {
      final now = tz.TZDateTime.now(tz.local);
      return tz.TZDateTime(
        tz.local,
        now.year,
        now.month,
        now.day + 1,
        hour,
        minute,
      );
    } catch (e, stackTrace) {
      _errorHandler.handleError(e, stackTrace, context: 'Getting timezone-aware date time for tomorrow');
      return tz.TZDateTime.now(tz.local).add(const Duration(days: 1));
    }
  }
} 