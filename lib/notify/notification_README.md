# Notification Module

## Overview

The Notification Module provides comprehensive functionality for managing notifications in the Maxed Out Life app. It handles scheduling, displaying, and managing user interactions with notifications.

## Features

- Daily notification scheduling
- Progress update notifications
- Streak notifications
- Achievement notifications
- Customizable notification preferences
- Secure storage of preferences
- Timezone-aware scheduling
- Error handling and logging
- Comprehensive testing

## Components

### Core Components

- `NotificationService`: Core service for managing notifications
- `NotificationController`: State management for notification settings
- `NotificationPreferences`: Model for notification preferences
- `NotificationModels`: Data models for notifications
- `NotificationExceptions`: Custom exceptions for notification errors
- `NotificationUtils`: Utility functions for notifications
- `NotificationConstants`: Constants used in notifications
- `NotificationProvider`: Provider for notification state
- `NotificationWidget`: UI components for notifications

### File Structure

```
lib/notify/
├── notification_service.dart
├── notification_controller.dart
├── notification_preferences.dart
├── notification_models.dart
├── notification_exceptions.dart
├── notification_utils.dart
├── notification_constants.dart
├── notification_provider.dart
├── notification_widget.dart
├── notification_test.dart
├── notification_export.dart
├── notification_import.dart
├── notification_index.dart
└── notification_README.md
```

## Usage

### Initialization

```dart
// Initialize the notification service
final service = NotificationService();
await service.initialize();

// Initialize the notification controller
final controller = NotificationController();
await controller.initialize();
```

### Scheduling Notifications

```dart
// Schedule a daily notification
await service.scheduleDailyNotification(
  hour: 9,
  minute: 0,
  title: 'Daily Reminder',
  body: 'Time to check your progress!',
);

// Show an immediate notification
await service.showNotification(
  title: 'Achievement Unlocked!',
  body: 'You\'ve reached a new milestone!',
);
```

### Managing Preferences

```dart
// Toggle notifications
await controller.toggleNotifications(true);

// Set daily notification time
await controller.setDailyNotificationTime(9, 0);

// Toggle specific notification types
await controller.toggleProgressNotifications(true);
await controller.toggleStreakNotifications(true);
await controller.toggleAchievementNotifications(true);
```

### Using the Widget

```dart
NotificationSettingsWidget(
  onSettingsChanged: (preferences) {
    // Handle settings changes
  },
);
```

## Design Principles

### Separation of Concerns

Each component has a specific responsibility:
- Service: Core notification functionality
- Controller: State management
- Preferences: User settings
- Models: Data structures
- Exceptions: Error handling
- Utils: Helper functions
- Constants: Configuration
- Provider: State management
- Widget: UI components

### Immutability

Models are immutable and use copyWith for updates:
```dart
final newPreferences = preferences.copyWith(
  isEnabled: true,
  dailyHour: 9,
  dailyMinute: 0,
);
```

### Error Handling

Custom exceptions for different error cases:
```dart
try {
  await service.scheduleDailyNotification(...);
} on NotificationSchedulingException catch (e) {
  // Handle scheduling error
} on NotificationPermissionException catch (e) {
  // Handle permission error
}
```

### State Management

Provider pattern for state management:
```dart
Consumer<NotificationController>(
  builder: (context, controller, child) {
    return SwitchListTile(
      value: controller.isEnabled,
      onChanged: (value) => controller.toggleNotifications(value),
    );
  },
);
```

## Testing

Comprehensive test coverage using Mockito and Flutter Test:
```dart
test('scheduleDailyNotification should schedule a notification', () async {
  when(mockService.scheduleDailyNotification(...))
      .thenAnswer((_) async => null);
  await service.scheduleDailyNotification(...);
  verify(mockService.scheduleDailyNotification(...)).called(1);
});
```

## Dependencies

- flutter_local_notifications: Platform notifications
- timezone: Timezone-aware scheduling
- provider: State management
- flutter_secure_storage: Secure storage
- mockito: Testing
- flutter_test: Testing

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests
5. Submit a pull request

## License

This module is part of the Maxed Out Life app and is subject to its license terms.

## Support

For support, please open an issue in the repository or contact the development team. 