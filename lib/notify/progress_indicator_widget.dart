import 'package:flutter/material.dart';
import 'widget_animation_utils.dart';

/// A custom progress indicator widget with lightning and glow effects.
class ProgressIndicatorWidget extends StatefulWidget {
  final double progress;
  final Color fillColor;
  final Color glowColor;
  final double height;
  final double borderRadius;
  final bool showLightning;
  final bool showGlow;

  const ProgressIndicatorWidget({
    super.key,
    required this.progress,
    required this.fillColor,
    required this.glowColor,
    this.height = 8.0,
    this.borderRadius = 4.0,
    this.showLightning = true,
    this.showGlow = true,
  });

  @override
  State<ProgressIndicatorWidget> createState() => _ProgressIndicatorWidgetState();
}

class _ProgressIndicatorWidgetState extends State<ProgressIndicatorWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _lightningAnimation;
  late Animation<double> _glowAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 2000),
    )..repeat();

    _lightningAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));

    _glowAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return CustomPaint(
          size: Size(
            MediaQuery.of(context).size.width - 32.0,
            widget.height,
          ),
          painter: _ProgressPainter(
            progress: widget.progress,
            fillColor: widget.fillColor,
            glowColor: widget.glowColor,
            borderRadius: widget.borderRadius,
            showLightning: widget.showLightning,
            showGlow: widget.showGlow,
            lightningAnimation: _lightningAnimation.value,
            glowAnimation: _glowAnimation.value,
          ),
        );
      },
    );
  }
}

class _ProgressPainter extends CustomPainter {
  final double progress;
  final Color fillColor;
  final Color glowColor;
  final double borderRadius;
  final bool showLightning;
  final bool showGlow;
  final double lightningAnimation;
  final double glowAnimation;

  _ProgressPainter({
    required this.progress,
    required this.fillColor,
    required this.glowColor,
    required this.borderRadius,
    required this.showLightning,
    required this.showGlow,
    required this.lightningAnimation,
    required this.glowAnimation,
  });

  @override
  void paint(Canvas canvas, Size size) {
    try {
      WidgetAnimationUtils.drawProgressBar(
        canvas,
        size,
        progress,
        fillColor,
        glowColor,
        glowAnimation,
        lightningAnimation,
      );
      if (progress >= 1.0) {
        // Draw a strong particle burst at the end of the bar
        final burstColor = Colors.amberAccent;
        final burstPaint = Paint()
          ..color = burstColor
          ..style = PaintingStyle.fill;
        WidgetAnimationUtils.drawParticleBurst(
          canvas,
          Offset(size.width, size.height / 2),
          size.height * 2.5,
          burstPaint,
        );
      }
    } catch (e) {
      debugPrint('Error painting progress indicator: $e');
    }
  }

  @override
  bool shouldRepaint(_ProgressPainter oldDelegate) {
    return oldDelegate.progress != progress ||
        oldDelegate.lightningAnimation != lightningAnimation ||
        oldDelegate.glowAnimation != glowAnimation;
  }
} 