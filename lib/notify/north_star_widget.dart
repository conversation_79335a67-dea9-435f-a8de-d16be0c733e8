import 'package:flutter/material.dart';
import 'package:home_widget/home_widget.dart';
import '../notify/widget_animation_utils.dart';

/// A widget for displaying inspirational quotes on the home screen.
class NorthStarWidget extends StatefulWidget {
  const NorthStarWidget({super.key});

  @override
  State<NorthStarWidget> createState() => _NorthStarWidgetState();
}

class _NorthStarWidgetState extends State<NorthStarWidget>
    with SingleTickerProviderStateMixin {
  String _quote = '';
  String _author = '';
  DateTime? _lastUpdated;
  bool _isLoading = true;
  late AnimationController _controller;
  late Animation<double> _glowAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 2000),
    )..repeat();

    _glowAnimation = Tween<double>(begin: 1.0, end: 1.5).animate(_controller);

    _loadData();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    try {
      final data = await HomeWidget.getWidgetData<Map<String, dynamic>>('north_star_widget');
      if (data != null) {
        setState(() {
          _quote = data['quote'] as String? ?? '';
          _author = data['author'] as String? ?? '';
          _lastUpdated = data['lastUpdated'] != null ? DateTime.parse(data['lastUpdated'] as String) : null;
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('Error loading north star widget data: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Stack(
          alignment: Alignment.center,
          children: [
            // Neon glow behind the quote
            CustomPaint(
              size: Size(MediaQuery.of(context).size.width - 32.0, 60),
              painter: _NeonGlowPainter(_quote, _glowAnimation.value),
            ),
            Container(
              padding: const EdgeInsets.all(16.0),
              decoration: BoxDecoration(
                color: Colors.black.withAlpha((0.8 * 255).toInt()),
                borderRadius: BorderRadius.circular(16.0),
                border: Border.all(
                  color: Colors.amber.withAlpha((0.3 * 255).toInt()),
                  width: 1.0,
                ),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _quote,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 18.0,
                      fontWeight: FontWeight.w500,
                      height: 1.4,
                    ),
                  ),
                  const SizedBox(height: 12.0),
                  Text(
                    '- $_author',
                    style: TextStyle(
                      color: Colors.amber.shade300,
                      fontSize: 14.0,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                  if (_lastUpdated != null) ...[
                    const SizedBox(height: 8.0),
                    Text(
                      'Last updated: ${_formatLastUpdated(_lastUpdated!)}',
                      style: TextStyle(
                        color: Colors.grey.shade400,
                        fontSize: 12.0,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  String _formatLastUpdated(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inDays}d ago';
    }
  }
}

class _NeonGlowPainter extends CustomPainter {
  final String text;
  final double glowScale;
  _NeonGlowPainter(this.text, this.glowScale);
  @override
  void paint(Canvas canvas, Size size) {
    final textPainter = TextPainter(
      text: TextSpan(
        text: text,
        style: const TextStyle(
          fontSize: 18.0,
          fontWeight: FontWeight.w500,
        ),
      ),
      textDirection: TextDirection.ltr,
    )..layout(maxWidth: size.width);
    final offset = Offset((size.width - textPainter.width) / 2, (size.height - textPainter.height) / 2);
    final path = Path();
    textPainter.paint(canvas, offset);
    // Draw a glow behind the text
    path.addRect(Rect.fromLTWH(offset.dx, offset.dy, textPainter.width, textPainter.height));
    WidgetAnimationUtils.drawNeonGlow(
      canvas,
      path,
      Colors.amberAccent,
      8.0 * glowScale,
    );
  }
  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
} 