import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'notification_controller.dart';
import 'notification_service.dart';

/// Provider for notification-related functionality.
class NotificationProvider extends StatelessWidget {
  final Widget child;

  const NotificationProvider({
    super.key,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(
          create: (_) => NotificationController(),
        ),
        Provider(
          create: (_) => NotificationService(),
        ),
      ],
      child: child,
    );
  }
}

/// Extension methods for accessing notification-related providers.
extension NotificationProviderExtension on BuildContext {
  NotificationController get notificationController =>
      Provider.of<NotificationController>(this, listen: false);

  NotificationService get notificationService =>
      Provider.of<NotificationService>(this, listen: false);
}

/// Widget for initializing notification-related services.
class NotificationInitializer extends StatefulWidget {
  final Widget child;

  const NotificationInitializer({
    super.key,
    required this.child,
  });

  @override
  State<NotificationInitializer> createState() => _NotificationInitializerState();
}

class _NotificationInitializerState extends State<NotificationInitializer> {
  @override
  void initState() {
    super.initState();
    _initializeServices();
  }

  Future<void> _initializeServices() async {
    try {
      final controller = context.notificationController;
      final service = context.notificationService;

      await service.initialize();
      await controller.initialize();
    } catch (e) {
      // Error handling is done in the respective services
    }
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}

/// Widget for handling notification-related errors.
class NotificationErrorHandler extends StatelessWidget {
  final Widget child;
  final Widget Function(BuildContext, Object, StackTrace?) onError;

  const NotificationErrorHandler({
    super.key,
    required this.child,
    required this.onError,
  });

  @override
  Widget build(BuildContext context) {
    // NOTE: ErrorWidget.builder should be set at the app level, not here.
    // This widget is a placeholder for future error handling logic.
    return child;
  }
}

/// Widget for displaying notification-related loading states.
class NotificationLoadingIndicator extends StatelessWidget {
  final Widget child;
  final Widget? loadingWidget;

  const NotificationLoadingIndicator({
    super.key,
    required this.child,
    this.loadingWidget,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<NotificationController>(
      builder: (context, controller, _) {
        if (!controller.isInitialized) {
          return loadingWidget ?? const Center(child: CircularProgressIndicator());
        }
        return child;
      },
    );
  }
}

/// Widget for handling notification-related state changes.
class NotificationStateListener extends StatelessWidget {
  final Widget child;
  final void Function(BuildContext, NotificationController)? onStateChanged;

  const NotificationStateListener({
    super.key,
    required this.child,
    this.onStateChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<NotificationController>(
      builder: (context, controller, _) {
        if (onStateChanged != null) {
          onStateChanged!(context, controller);
        }
        return child;
      },
    );
  }
} 