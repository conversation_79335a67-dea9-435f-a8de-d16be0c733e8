import 'dart:convert';

/// Model class representing notification preferences.
class NotificationPreferences {
  final bool isEnabled;
  final int dailyHour;
  final int dailyMinute;
  final bool showProgressNotifications;
  final bool showStreakNotifications;
  final bool showAchievementNotifications;
  final DateTime lastUpdated;

  const NotificationPreferences({
    required this.isEnabled,
    required this.dailyHour,
    required this.dailyMinute,
    required this.showProgressNotifications,
    required this.showStreakNotifications,
    required this.showAchievementNotifications,
    required this.lastUpdated,
  });

  /// Create an empty instance with default values.
  factory NotificationPreferences.empty() {
    return NotificationPreferences(
      isEnabled: false,
      dailyHour: 9,
      dailyMinute: 0,
      showProgressNotifications: true,
      showStreakNotifications: true,
      showAchievementNotifications: true,
      lastUpdated: DateTime.now(),
    );
  }

  /// Create a copy of this instance with the given fields replaced with new values.
  NotificationPreferences copyWith({
    bool? isEnabled,
    int? dailyHour,
    int? dailyMinute,
    bool? showProgressNotifications,
    bool? showStreakNotifications,
    bool? showAchievementNotifications,
    DateTime? lastUpdated,
  }) {
    return NotificationPreferences(
      isEnabled: isEnabled ?? this.isEnabled,
      dailyHour: dailyHour ?? this.dailyHour,
      dailyMinute: dailyMinute ?? this.dailyMinute,
      showProgressNotifications: showProgressNotifications ?? this.showProgressNotifications,
      showStreakNotifications: showStreakNotifications ?? this.showStreakNotifications,
      showAchievementNotifications: showAchievementNotifications ?? this.showAchievementNotifications,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  /// Create a NotificationPreferences from a JSON map.
  factory NotificationPreferences.fromJson(Map<String, dynamic> json) {
    return NotificationPreferences(
      isEnabled: json['isEnabled'] as bool,
      dailyHour: json['dailyHour'] as int,
      dailyMinute: json['dailyMinute'] as int,
      showProgressNotifications: json['showProgressNotifications'] as bool,
      showStreakNotifications: json['showStreakNotifications'] as bool,
      showAchievementNotifications: json['showAchievementNotifications'] as bool,
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
    );
  }

  /// Convert the NotificationPreferences to a JSON map.
  Map<String, dynamic> toJson() {
    return {
      'isEnabled': isEnabled,
      'dailyHour': dailyHour,
      'dailyMinute': dailyMinute,
      'showProgressNotifications': showProgressNotifications,
      'showStreakNotifications': showStreakNotifications,
      'showAchievementNotifications': showAchievementNotifications,
      'lastUpdated': lastUpdated.toIso8601String(),
    };
  }

  /// Convert the NotificationPreferences to a JSON string.
  String toJsonString() {
    return jsonEncode(toJson());
  }

  /// Create a NotificationPreferences from a JSON string.
  factory NotificationPreferences.fromJsonString(String jsonString) {
    return NotificationPreferences.fromJson(jsonDecode(jsonString));
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is NotificationPreferences &&
        other.isEnabled == isEnabled &&
        other.dailyHour == dailyHour &&
        other.dailyMinute == dailyMinute &&
        other.showProgressNotifications == showProgressNotifications &&
        other.showStreakNotifications == showStreakNotifications &&
        other.showAchievementNotifications == showAchievementNotifications &&
        other.lastUpdated == lastUpdated;
  }

  @override
  int get hashCode {
    return Object.hash(
      isEnabled,
      dailyHour,
      dailyMinute,
      showProgressNotifications,
      showStreakNotifications,
      showAchievementNotifications,
      lastUpdated,
    );
  }
} 