import 'package:flutter/material.dart';
import 'package:home_widget/home_widget.dart';
import 'progress_indicator_widget.dart';
import 'widget_animation_utils.dart';

/// A widget for displaying quotes and progress on the lock screen.
class LockScreenWidget extends StatefulWidget {
  const LockScreenWidget({super.key});

  @override
  State<LockScreenWidget> createState() => _LockScreenWidgetState();
}

class _LockScreenWidgetState extends State<LockScreenWidget>
    with SingleTickerProviderStateMixin {
  String _quote = '';
  String _author = '';
  double _progress = 0.0;
  DateTime? _lastUpdated;
  bool _isLoading = true;
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 2000),
    )..repeat();

    _loadData();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    try {
      final data = await HomeWidget.getWidgetData<Map<String, dynamic>>('lock_screen_widget');
      if (data != null) {
        setState(() {
          _quote = data['quote'] as String? ?? '';
          _author = data['author'] as String? ?? '';
          _progress = data['progress'] as double? ?? 0.0;
          _lastUpdated = data['lastUpdated'] != null ? DateTime.parse(data['lastUpdated'] as String) : null;
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('Error loading lock screen widget data: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }
    return Stack(
      alignment: Alignment.center,
      children: [
        Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              _quote,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 18.0,
                fontWeight: FontWeight.w500,
                height: 1.4,
              ),
            ),
            const SizedBox(height: 12.0),
            Text(
              '- $_author',
              style: TextStyle(
                color: Colors.amber.shade300,
                fontSize: 14.0,
                fontStyle: FontStyle.italic,
              ),
            ),
            const SizedBox(height: 16.0),
            ProgressIndicatorWidget(
              progress: _progress,
              fillColor: Colors.amberAccent,
              glowColor: Colors.yellowAccent,
              height: 12.0,
              borderRadius: 6.0,
              showLightning: true,
              showGlow: true,
            ),
            if (_lastUpdated != null)
              Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: Text(
                  'Last updated: ${_formatLastUpdated(_lastUpdated!)}',
                  style: TextStyle(
                    color: Colors.grey.shade400,
                    fontSize: 12.0,
                  ),
                ),
              ),
          ],
        ),
        if (_progress >= 1.0) ...[
          // Particle burst overlay
          Positioned.fill(
            child: IgnorePointer(
              child: CustomPaint(
                painter: _LockScreenBurstPainter(),
              ),
            ),
          ),
        ],
      ],
    );
  }

  String _formatLastUpdated(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inDays}d ago';
    }
  }
}

class _LockScreenBurstPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final burstPaint = Paint()
      ..color = Colors.amberAccent
      ..style = PaintingStyle.fill;
    WidgetAnimationUtils.drawParticleBurst(
      canvas,
      Offset(size.width / 2, size.height / 2),
      size.height * 0.7,
      burstPaint,
    );
  }
  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
} 