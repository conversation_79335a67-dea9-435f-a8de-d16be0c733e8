import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'notification_controller.dart';
//import 'notification_preferences.dart';

/// Widget for displaying and managing notification settings.
class NotificationSettingsWidget extends StatelessWidget {
  const NotificationSettingsWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<NotificationController>(
      builder: (context, controller, child) {
        if (!controller.isInitialized) {
          return const Center(child: CircularProgressIndicator());
        }

        return ListView(
          padding: const EdgeInsets.all(16.0),
          children: [
            _buildSection(
              title: 'Notifications',
              children: [
                SwitchListTile(
                  title: const Text('Enable Notifications'),
                  subtitle: const Text('Receive notifications about your progress'),
                  value: controller.isEnabled,
                  onChanged: (value) => controller.toggleNotifications(value),
                ),
              ],
            ),
            if (controller.isEnabled) ...[
              _buildSection(
                title: 'Daily Notification Time',
                children: [
                  ListTile(
                    title: const Text('Time'),
                    subtitle: Text(
                      '${controller.dailyHour.toString().padLeft(2, '0')}:'
                      '${controller.dailyMinute.toString().padLeft(2, '0')}',
                    ),
                    trailing: const Icon(Icons.access_time),
                    onTap: () => _showTimePicker(context, controller),
                  ),
                ],
              ),
              _buildSection(
                title: 'Notification Types',
                children: [
                  SwitchListTile(
                    title: const Text('Progress Updates'),
                    subtitle: const Text('Get notified about your daily progress'),
                    value: controller.showProgressNotifications,
                    onChanged: (value) => controller.toggleProgressNotifications(value),
                  ),
                  SwitchListTile(
                    title: const Text('Streak Updates'),
                    subtitle: const Text('Get notified about your streak status'),
                    value: controller.showStreakNotifications,
                    onChanged: (value) => controller.toggleStreakNotifications(value),
                  ),
                  SwitchListTile(
                    title: const Text('Achievements'),
                    subtitle: const Text('Get notified when you unlock achievements'),
                    value: controller.showAchievementNotifications,
                    onChanged: (value) => controller.toggleAchievementNotifications(value),
                  ),
                ],
              ),
            ],
          ],
        );
      },
    );
  }

  Widget _buildSection({
    required String title,
    required List<Widget> children,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0),
          child: Text(
            title,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        Card(
          child: Column(
            children: children,
          ),
        ),
        const SizedBox(height: 16),
      ],
    );
  }

  Future<void> _showTimePicker(
    BuildContext context,
    NotificationController controller,
  ) async {
    final TimeOfDay? time = await showTimePicker(
      context: context,
      initialTime: TimeOfDay(
        hour: controller.dailyHour,
        minute: controller.dailyMinute,
      ),
    );

    if (time != null) {
      controller.setDailyNotificationTime(time.hour, time.minute);
    }
  }
}

/// Widget for displaying a notification preview.
class NotificationPreviewWidget extends StatelessWidget {
  final String title;
  final String body;
  final IconData icon;
  final Color color;

  const NotificationPreviewWidget({
    super.key,
    required this.title,
    required this.body,
    required this.icon,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          children: [
            CircleAvatar(
              backgroundColor: color.withValues(alpha: 0.2),
              child: Icon(icon, color: color),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    body,
                    style: TextStyle(
                      color: Theme.of(context).textTheme.bodySmall?.color,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
} 