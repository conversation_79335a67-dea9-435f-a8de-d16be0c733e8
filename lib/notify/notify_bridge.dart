// lib/notify/notify_bridge.dart

import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:home_widget/home_widget.dart';
import 'package:timezone/data/latest_all.dart' as tz;
import 'package:timezone/timezone.dart' as tz;
import 'package:flutter/foundation.dart';

import '../controller/user_controller2.dart';
import '../bulletproof/error_handler.dart';
import 'widget_controller.dart';
import 'widget_sound_service.dart';
import 'widget_state_provider.dart';

/// A bridge class that connects legacy code to the notify system.
/// It handles scheduled notifications and events like level-up,
/// North Star Quest progress, and rank-up.
class NotifyBridge {
  static final NotifyBridge _instance = NotifyBridge._internal();
  factory NotifyBridge() => _instance;
  
  final _errorHandler = ErrorHandler();
  final _userController = UserController2();
  late final WidgetStateProvider _stateProvider;

  NotifyBridge._internal() {
    _stateProvider = WidgetStateProvider(
      controller: WidgetController(_userController, _errorHandler),
      soundService: WidgetSoundService(),
    );
  }

  /// Flutter Local Notifications Plugin instance
  final FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();

  /// Initialize the bridge, setting up scheduled notifications.
  Future<void> initialize() async {
    // Initialize flutter_local_notifications
    const AndroidInitializationSettings initializationSettingsAndroid = AndroidInitializationSettings('@mipmap/ic_launcher');
    const DarwinInitializationSettings initializationSettingsIOS = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );
    const DarwinInitializationSettings initializationSettingsMacOS = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );
    const InitializationSettings initializationSettings = InitializationSettings(
      android: initializationSettingsAndroid,
      iOS: initializationSettingsIOS,
      macOS: initializationSettingsMacOS,
    );
    await _flutterLocalNotificationsPlugin.initialize(initializationSettings);
    // Initialize timezone
    tz.initializeTimeZones();
    try {
      tz.setLocalLocation(tz.getLocation(DateTime.now().timeZoneName));
    } catch (e) {
      try {
        tz.setLocalLocation(tz.getLocation('Australia/Sydney'));
      } catch (e) {
        tz.setLocalLocation(tz.getLocation('UTC'));
      }
    }
    try {
      await _scheduleNotifications();
    } catch (e, st) {
      debugPrint('Failed to schedule notifications: $e\n$st');
      // Optionally, fallback to inexact alarms or inform the user
    }
  }

  /// Schedule notifications at 07:00, 12:45, and 19:45 using flutter_local_notifications.
  Future<void> _scheduleNotifications() async {
    final now = DateTime.now();
    final scheduledTimes = [
      DateTime(now.year, now.month, now.day, 7, 0),
      DateTime(now.year, now.month, now.day, 12, 45),
      DateTime(now.year, now.month, now.day, 19, 45),
    ];

    for (final time in scheduledTimes) {
      if (time.isAfter(now)) {
        try {
          await _flutterLocalNotificationsPlugin.periodicallyShow(
            0,
            'Daily Reminder',
            'Time to check your progress!',
            RepeatInterval.daily,
            const NotificationDetails(
              android: AndroidNotificationDetails(
                'daily_notification_channel',
                'Daily Notifications',
                importance: Importance.high,
                priority: Priority.high,
              ),
              iOS: DarwinNotificationDetails(
                presentAlert: true,
                presentBadge: true,
                presentSound: true,
              ),
            ),
            androidScheduleMode: AndroidScheduleMode.exact,
          );
        } catch (e) {
          debugPrint('Exact alarm not permitted or failed: $e. Trying inexact alarm.');
          try {
            await _flutterLocalNotificationsPlugin.periodicallyShow(
              0,
              'Daily Reminder',
              'Time to check your progress!',
              RepeatInterval.daily,
              const NotificationDetails(
                android: AndroidNotificationDetails(
                  'daily_notification_channel',
                  'Daily Notifications',
                  importance: Importance.high,
                  priority: Priority.high,
                ),
                iOS: DarwinNotificationDetails(
                  presentAlert: true,
                  presentBadge: true,
                  presentSound: true,
                ),
              ),
              androidScheduleMode: AndroidScheduleMode.inexactAllowWhileIdle,
            );
          } catch (e2) {
            debugPrint('Failed to schedule even inexact alarm: $e2');
          }
        }
      }
    }
  }

  /// Handle level-up event. Updates widget data and sends a notification.
  Future<void> onLevelUp(int newLevel) async {
    // Use the existing provider method for daily goal progress
    await _stateProvider.updateDailyGoalProgress(newLevel.toDouble());

    await HomeWidget.saveWidgetData('notification_title', 'Level Up!');
    await HomeWidget.saveWidgetData(
      'notification_body',
      'You\'ve reached level $newLevel.',
    );
    await HomeWidget.updateWidget(
      name: 'HomeWidgetProvider',
      androidName: 'HomeWidgetProvider',
      qualifiedAndroidName: 'com.guardiantape.maxedoutlife.HomeWidgetProvider',
    );

    // Show notification using flutter_local_notifications
    await _flutterLocalNotificationsPlugin.show(
      1,
      'Level Up!',
      'You\'ve reached level $newLevel.',
      const NotificationDetails(
        android: AndroidNotificationDetails(
          'level_up_channel',
          'Level Up Notifications',
          importance: Importance.high,
          priority: Priority.high,
        ),
        iOS: DarwinNotificationDetails(
          presentAlert: true,
          presentBadge: true,
          presentSound: true,
        ),
      ),
    );
  }

  /// Handle North Star Quest progress. Sends a notification every 5 hours of progress.
  Future<void> onNorthStarQuestProgress(double hoursProgress) async {
    if (hoursProgress % 5 == 0) {
      await HomeWidget.saveWidgetData(
        'notification_title',
        'North Star Quest Progress',
      );
      await HomeWidget.saveWidgetData(
        'notification_body',
        'You\'ve made $hoursProgress hours of progress.',
      );
      await HomeWidget.updateWidget(
        name: 'HomeWidgetProvider',
        androidName: 'HomeWidgetProvider',
        qualifiedAndroidName: 'com.guardiantape.maxedoutlife.HomeWidgetProvider',
      );

      // Show notification using flutter_local_notifications
      await _flutterLocalNotificationsPlugin.show(
        2,
        'North Star Quest Progress',
        'You\'ve made $hoursProgress hours of progress.',
        const NotificationDetails(
          android: AndroidNotificationDetails(
            'quest_progress_channel',
            'Quest Progress Notifications',
            importance: Importance.high,
            priority: Priority.high,
          ),
          iOS: DarwinNotificationDetails(
            presentAlert: true,
            presentBadge: true,
            presentSound: true,
          ),
        ),
      );
    }
  }

  /// Handle rank-up event in North Star. Updates widget data and sends a notification.
  Future<void> onNorthStarRankUp(String newRank) async {
    // Use the existing provider method for last-notification quote
    await _stateProvider.updateLastNotificationQuote(newRank);

    await HomeWidget.saveWidgetData('notification_title', 'Rank Up!');
    await HomeWidget.saveWidgetData(
      'notification_body',
      'You\'ve achieved the rank of $newRank.',
    );
    await HomeWidget.updateWidget(
      name: 'HomeWidgetProvider',
      androidName: 'HomeWidgetProvider',
      qualifiedAndroidName: 'com.guardiantape.maxedoutlife.HomeWidgetProvider',
    );

    // Show notification using flutter_local_notifications
    await _flutterLocalNotificationsPlugin.show(
      3,
      'Rank Up!',
      'You\'ve achieved the rank of $newRank.',
      const NotificationDetails(
        android: AndroidNotificationDetails(
          'rank_up_channel',
          'Rank Up Notifications',
          importance: Importance.high,
          priority: Priority.high,
        ),
        iOS: DarwinNotificationDetails(
          presentAlert: true,
          presentBadge: true,
          presentSound: true,
        ),
      ),
    );
  }

  /// Show an error notification
  Future<void> showErrorNotification(String title, String body) async {
    await _flutterLocalNotificationsPlugin.show(
      999, // Use a high ID for error notifications
      title,
      body,
      const NotificationDetails(
        android: AndroidNotificationDetails(
          'error_channel',
          'Error Notifications',
          importance: Importance.high,
          priority: Priority.high,
        ),
        iOS: DarwinNotificationDetails(
          presentAlert: true,
          presentBadge: true,
          presentSound: true,
        ),
      ),
    );
  }

  /// Schedule a notification for a specific time
  Future<void> scheduleNotification({
    required int id,
    required String title,
    required String body,
    DateTime? scheduledDate,
  }) async {
    try {
      // For immediate notifications, use show() method
      await _flutterLocalNotificationsPlugin.show(
        id,
        title,
        body,
        const NotificationDetails(
          android: AndroidNotificationDetails(
            'scheduled_channel',
            'Scheduled Notifications',
            importance: Importance.high,
            priority: Priority.high,
          ),
          iOS: DarwinNotificationDetails(
            presentAlert: true,
            presentBadge: true,
            presentSound: true,
          ),
        ),
      );
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'NotifyBridge.scheduleNotification');
    }
  }

  /// Send a custom notification immediately
  Future<void> sendCustomNotification(String title, String body) async {
    try {
      await _flutterLocalNotificationsPlugin.show(
        DateTime.now().millisecondsSinceEpoch ~/ 1000, // Use timestamp as ID
        title,
        body,
        const NotificationDetails(
          android: AndroidNotificationDetails(
            'custom_channel',
            'Custom Notifications',
            importance: Importance.high,
            priority: Priority.high,
          ),
          iOS: DarwinNotificationDetails(
            presentAlert: true,
            presentBadge: true,
            presentSound: true,
          ),
        ),
      );
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'NotifyBridge.sendCustomNotification');
    }
  }
}
