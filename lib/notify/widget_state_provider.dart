import 'package:flutter/foundation.dart';
//import 'package:maxed_out_life/notify/home_level_widget.dart';
import 'widget_preferences.dart';
import 'widget_controller.dart';
import 'widget_sound_service.dart';

/// Provider class to manage widget state and updates.
class WidgetStateProvider extends ChangeNotifier {
  final WidgetController _controller;
  final WidgetSoundService _soundService;
  WidgetPreferences _preferences;
  bool _isUpdating = false;
  bool _isInitialized = false;

  WidgetStateProvider({
    required WidgetController controller,
    required WidgetSoundService soundService,
  })  : _controller = controller,
        _soundService = soundService,
        _preferences = WidgetPreferences.empty();

  /// Initialize the provider.
  Future<void> init() async {
    if (_isInitialized) return;

    try {
      await _soundService.init();
      final loadedPreferences = await _controller.loadPreferences();
      _preferences = loadedPreferences;
      _isInitialized = true;
      notifyListeners();
    } catch (e) {
      debugPrint('Error initializing widget state provider: $e');
    }
  }

  /// Update widget preferences.
  Future<void> updatePreferences(WidgetPreferences preferences) async {
    if (_isUpdating || !_isInitialized) return;
    _isUpdating = true;

    try {
      await _controller.updateWidgets(preferences);
      _preferences = preferences;
      notifyListeners();
    } catch (e) {
      debugPrint('Error updating widget preferences: $e');
    } finally {
      _isUpdating = false;
    }
  }

  /// Update widget visibility.
  Future<void> updateWidgetVisibility({
    bool? showHomeLevelWidget,
    bool? showNorthStarWidget,
    bool? showLockScreenWidget,
  }) async {
    if (!_isInitialized) return;

    try {
      final updatedPreferences = _preferences.copyWith(
        showHomeLevelWidget: showHomeLevelWidget,
        showNorthStarWidget: showNorthStarWidget,
        showLockScreenWidget: showLockScreenWidget,
      );
      await updatePreferences(updatedPreferences);
    } catch (e) {
      debugPrint('Error updating widget visibility: $e');
    }
  }

  /// Update daily goal progress.
  Future<void> updateDailyGoalProgress(double progress) async {
    if (!_isInitialized) return;

    try {
      final updatedPreferences = _preferences.copyWith(
        dailyGoalProgress: progress,
      );
      await updatePreferences(updatedPreferences);
      await _soundService.playLightningSound();
    } catch (e) {
      debugPrint('Error updating daily goal progress: $e');
    }
  }

  /// Update streak days.
  Future<void> updateStreakDays(int days) async {
    if (!_isInitialized) return;

    try {
      final updatedPreferences = _preferences.copyWith(
        streakDays: days,
      );
      await updatePreferences(updatedPreferences);
      await _soundService.playLevelUpSound();
    } catch (e) {
      debugPrint('Error updating streak days: $e');
    }
  }

  /// Update last notification quote.
  Future<void> updateLastNotificationQuote(String quote) async {
    if (!_isInitialized) return;

    try {
      final updatedPreferences = _preferences.copyWith(
        lastNotificationQuote: quote,
      );
      await updatePreferences(updatedPreferences);
      await _soundService.playNotificationSound();
    } catch (e) {
      debugPrint('Error updating last notification quote: $e');
    }
  }

  /// Get current preferences.
  WidgetPreferences get preferences => _preferences;

  /// Check if the provider is initialized.
  bool get isInitialized => _isInitialized;

  /// Check if the provider is currently updating.
  bool get isUpdating => _isUpdating;
} 