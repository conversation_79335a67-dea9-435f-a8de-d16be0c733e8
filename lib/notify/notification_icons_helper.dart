import 'package:flutter_local_notifications/flutter_local_notifications.dart';

/// Helper to get the correct icon name for male/female and type
String getNotificationIcon({
  required String type, // 'progress', 'rank_up', 'reminder'
  required bool isMale,
}) {
  switch (type) {
    case 'progress':
      return isMale ? 'm_progress' : 'f_progress';
    case 'rank_up':
      return isMale ? 'm_rank_up' : 'f_rank_up';
    case 'reminder':
      return isMale ? 'm_reminder' : 'f_reminder';
    default:
      return 'level_up_small'; // fallback
  }
}

/// Level Up Notification
Future<void> showLevelUpNotification(
    FlutterLocalNotificationsPlugin plugin, bool isMale, int newLevel) async {
  await plugin.show(
    1,
    'Level Up!',
    'You\'ve reached level $newLevel.',
    NotificationDetails(
      android: AndroidNotificationDetails(
        'level_up_channel',
        'Level Up Notifications',
        importance: Importance.high,
        priority: Priority.high,
        icon: 'level_up_small', // Use the generic small icon
        largeIcon: const DrawableResourceAndroidBitmap('level_up'),
      ),
    ),
  );
}

/// Progress Notification
Future<void> showProgressNotification(
    FlutterLocalNotificationsPlugin plugin, bool isMale, String message) async {
  final iconName = getNotificationIcon(type: 'progress', isMale: isMale);
  await plugin.show(
    2,
    'Progress Update',
    message,
    NotificationDetails(
      android: AndroidNotificationDetails(
        'progress_channel',
        'Progress Notifications',
        importance: Importance.high,
        priority: Priority.high,
        icon: iconName,
        largeIcon: DrawableResourceAndroidBitmap(iconName),
      ),
    ),
  );
}

/// Reminder Notification
Future<void> showReminderNotification(
    FlutterLocalNotificationsPlugin plugin, bool isMale, String message) async {
  final iconName = getNotificationIcon(type: 'reminder', isMale: isMale);
  await plugin.show(
    3,
    'Reminder',
    message,
    NotificationDetails(
      android: AndroidNotificationDetails(
        'reminder_channel',
        'Reminders',
        importance: Importance.high,
        priority: Priority.high,
        icon: iconName,
        largeIcon: DrawableResourceAndroidBitmap(iconName),
      ),
    ),
  );
}

/// Quest Progress Notification (gender-neutral)
Future<void> showQuestProgressNotification(
    FlutterLocalNotificationsPlugin plugin, String message) async {
  await plugin.show(
    4,
    'Quest Progress',
    message,
    NotificationDetails(
      android: AndroidNotificationDetails(
        'quest_progress_channel',
        'Quest Progress Notifications',
        importance: Importance.high,
        priority: Priority.high,
        icon: 'quest_progress',
        largeIcon: const DrawableResourceAndroidBitmap('quest_progress'),
      ),
    ),
  );
}

/// Achievement Notification (gender-neutral)
Future<void> showAchievementNotification(
    FlutterLocalNotificationsPlugin plugin, String message) async {
  await plugin.show(
    5,
    'Achievement Unlocked!',
    message,
    NotificationDetails(
      android: AndroidNotificationDetails(
        'achievement_channel',
        'Achievement Notifications',
        importance: Importance.high,
        priority: Priority.high,
        icon: 'achievement',
        largeIcon: const DrawableResourceAndroidBitmap('achievement'),
      ),
    ),
  );
} 