import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'dart:convert';
import '../bulletproof/error_handler.dart';
import 'notification_service.dart';
//import 'widget_controller.dart';

/// Controller for managing notification state and preferences.
class NotificationController extends ChangeNotifier {
  static final NotificationController _instance = NotificationController._internal();
  factory NotificationController() => _instance;
  NotificationController._internal();

  static const _storage = FlutterSecureStorage();
  static const _prefsKey = 'mol_notification_preferences';
  static final _errorHandler = ErrorHandler();
  static final _notificationService = NotificationService();

  bool _isInitialized = false;
  bool _isEnabled = false;
  int _dailyHour = 9; // Default to 9 AM
  int _dailyMinute = 0;
  bool _showProgressNotifications = true;
  bool _showStreakNotifications = true;
  bool _showAchievementNotifications = true;

  /// Whether the controller has been initialized.
  bool get isInitialized => _isInitialized;

  /// Whether notifications are enabled.
  bool get isEnabled => _isEnabled;

  /// Daily notification hour (24-hour format).
  int get dailyHour => _dailyHour;

  /// Daily notification minute.
  int get dailyMinute => _dailyMinute;

  /// Whether to show progress notifications.
  bool get showProgressNotifications => _showProgressNotifications;

  /// Whether to show streak notifications.
  bool get showStreakNotifications => _showStreakNotifications;

  /// Whether to show achievement notifications.
  bool get showAchievementNotifications => _showAchievementNotifications;

  /// Initialize the notification controller.
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _notificationService.initialize();
      await _loadPreferences();
      _isInitialized = true;
      notifyListeners();
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'Initializing notification controller');
    }
  }

  /// Load notification preferences from secure storage.
  Future<void> _loadPreferences() async {
    try {
      final jsonString = await _storage.read(key: _prefsKey);
      if (jsonString == null) return;

      final prefs = Map<String, dynamic>.from(
        const JsonDecoder().convert(jsonString),
      );

      _isEnabled = prefs['isEnabled'] as bool? ?? false;
      _dailyHour = prefs['dailyHour'] as int? ?? 9;
      _dailyMinute = prefs['dailyMinute'] as int? ?? 0;
      _showProgressNotifications = prefs['showProgressNotifications'] as bool? ?? true;
      _showStreakNotifications = prefs['showStreakNotifications'] as bool? ?? true;
      _showAchievementNotifications = prefs['showAchievementNotifications'] as bool? ?? true;

      if (_isEnabled) {
        await _scheduleDailyNotification();
      }
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'Loading notification preferences');
    }
  }

  /// Save notification preferences to secure storage.
  Future<void> _savePreferences() async {
    try {
      final prefs = {
        'isEnabled': _isEnabled,
        'dailyHour': _dailyHour,
        'dailyMinute': _dailyMinute,
        'showProgressNotifications': _showProgressNotifications,
        'showStreakNotifications': _showStreakNotifications,
        'showAchievementNotifications': _showAchievementNotifications,
      };

      await _storage.write(
        key: _prefsKey,
        value: const JsonEncoder().convert(prefs),
      );
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'Saving notification preferences');
    }
  }

  /// Toggle notifications on/off.
  Future<void> toggleNotifications(bool enabled) async {
    try {
      _isEnabled = enabled;
      if (enabled) {
        await _scheduleDailyNotification();
      } else {
        await _notificationService.cancelAllNotifications();
      }
      await _savePreferences();
      notifyListeners();
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'Toggling notifications');
    }
  }

  /// Set the daily notification time.
  Future<void> setDailyNotificationTime(int hour, int minute) async {
    try {
      _dailyHour = hour;
      _dailyMinute = minute;
      if (_isEnabled) {
        await _scheduleDailyNotification();
      }
      await _savePreferences();
      notifyListeners();
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'Setting daily notification time');
    }
  }

  /// Toggle progress notifications.
  Future<void> toggleProgressNotifications(bool enabled) async {
    try {
      _showProgressNotifications = enabled;
      await _savePreferences();
      notifyListeners();
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'Toggling progress notifications');
    }
  }

  /// Toggle streak notifications.
  Future<void> toggleStreakNotifications(bool enabled) async {
    try {
      _showStreakNotifications = enabled;
      await _savePreferences();
      notifyListeners();
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'Toggling streak notifications');
    }
  }

  /// Toggle achievement notifications.
  Future<void> toggleAchievementNotifications(bool enabled) async {
    try {
      _showAchievementNotifications = enabled;
      await _savePreferences();
      notifyListeners();
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'Toggling achievement notifications');
    }
  }

  /// Schedule the daily notification.
  Future<void> _scheduleDailyNotification() async {
    try {
      await _notificationService.scheduleDailyNotification(
        hour: _dailyHour,
        minute: _dailyMinute,
        title: 'Maxed Out Life',
        body: 'Time to check your progress and set new goals!',
      );
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'Scheduling daily notification');
    }
  }

  /// Show a progress notification.
  Future<void> showProgressNotification(String message) async {
    if (!_isEnabled || !_showProgressNotifications) return;

    try {
      await _notificationService.showNotification(
        title: 'Progress Update',
        body: message,
      );
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'Showing progress notification');
    }
  }

  /// Show a streak notification.
  Future<void> showStreakNotification(String message) async {
    if (!_isEnabled || !_showStreakNotifications) return;

    try {
      await _notificationService.showNotification(
        title: 'Streak Update',
        body: message,
      );
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'Showing streak notification');
    }
  }

  /// Show an achievement notification.
  Future<void> showAchievementNotification(String message) async {
    if (!_isEnabled || !_showAchievementNotifications) return;

    try {
      await _notificationService.showNotification(
        title: 'Achievement Unlocked!',
        body: message,
      );
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'Showing achievement notification');
    }
  }
} 