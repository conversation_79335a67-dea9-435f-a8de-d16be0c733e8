/// Base class for notification-related exceptions.
class NotificationException implements Exception {
  final String message;
  final String? code;
  final dynamic originalError;
  final StackTrace? stackTrace;

  const NotificationException(
    this.message, {
    this.code,
    this.originalError,
    this.stackTrace,
  });

  @override
  String toString() {
    final buffer = StringBuffer('NotificationException: $message');
    if (code != null) {
      buffer.write(' (Code: $code)');
    }
    if (originalError != null) {
      buffer.write('\nOriginal Error: $originalError');
    }
    if (stackTrace != null) {
      buffer.write('\nStackTrace: $stackTrace');
    }
    return buffer.toString();
  }
}

/// Exception thrown when notification permissions are not granted.
class NotificationPermissionException extends NotificationException {
  const NotificationPermissionException({
    String? message,
    String? code,
    dynamic originalError,
    StackTrace? stackTrace,
  }) : super(
          message ?? 'Notification permissions are not granted',
          code: code ?? 'PERMISSION_DENIED',
          originalError: originalError,
          stackTrace: stackTrace,
        );
}

/// Exception thrown when notification channel creation fails.
class NotificationChannelException extends NotificationException {
  const NotificationChannelException({
    String? message,
    String? code,
    dynamic originalError,
    StackTrace? stackTrace,
  }) : super(
          message ?? 'Failed to create notification channel',
          code: code ?? 'CHANNEL_CREATION_FAILED',
          originalError: originalError,
          stackTrace: stackTrace,
        );
}

/// Exception thrown when notification scheduling fails.
class NotificationSchedulingException extends NotificationException {
  const NotificationSchedulingException({
    String? message,
    String? code,
    dynamic originalError,
    StackTrace? stackTrace,
  }) : super(
          message ?? 'Failed to schedule notification',
          code: code ?? 'SCHEDULING_FAILED',
          originalError: originalError,
          stackTrace: stackTrace,
        );
}

/// Exception thrown when notification cancellation fails.
class NotificationCancellationException extends NotificationException {
  const NotificationCancellationException({
    String? message,
    String? code,
    dynamic originalError,
    StackTrace? stackTrace,
  }) : super(
          message ?? 'Failed to cancel notification',
          code: code ?? 'CANCELLATION_FAILED',
          originalError: originalError,
          stackTrace: stackTrace,
        );
}

/// Exception thrown when notification payload is invalid.
class NotificationPayloadException extends NotificationException {
  const NotificationPayloadException({
    String? message,
    String? code,
    dynamic originalError,
    StackTrace? stackTrace,
  }) : super(
          message ?? 'Invalid notification payload',
          code: code ?? 'INVALID_PAYLOAD',
          originalError: originalError,
          stackTrace: stackTrace,
        );
}

/// Exception thrown when notification action is invalid.
class NotificationActionException extends NotificationException {
  const NotificationActionException({
    String? message,
    String? code,
    dynamic originalError,
    StackTrace? stackTrace,
  }) : super(
          message ?? 'Invalid notification action',
          code: code ?? 'INVALID_ACTION',
          originalError: originalError,
          stackTrace: stackTrace,
        );
}

/// Exception thrown when notification preferences are invalid.
class NotificationPreferencesException extends NotificationException {
  const NotificationPreferencesException({
    String? message,
    String? code,
    dynamic originalError,
    StackTrace? stackTrace,
  }) : super(
          message ?? 'Invalid notification preferences',
          code: code ?? 'INVALID_PREFERENCES',
          originalError: originalError,
          stackTrace: stackTrace,
        );
}

/// Exception thrown when notification service is not initialized.
class NotificationServiceException extends NotificationException {
  const NotificationServiceException({
    String? message,
    String? code,
    dynamic originalError,
    StackTrace? stackTrace,
  }) : super(
          message ?? 'Notification service is not initialized',
          code: code ?? 'SERVICE_NOT_INITIALIZED',
          originalError: originalError,
          stackTrace: stackTrace,
        );
}

/// Exception thrown when notification controller is not initialized.
class NotificationControllerException extends NotificationException {
  const NotificationControllerException({
    String? message,
    String? code,
    dynamic originalError,
    StackTrace? stackTrace,
  }) : super(
          message ?? 'Notification controller is not initialized',
          code: code ?? 'CONTROLLER_NOT_INITIALIZED',
          originalError: originalError,
          stackTrace: stackTrace,
        );
}

/// Exception thrown when notification widget is not initialized.
class NotificationWidgetException extends NotificationException {
  const NotificationWidgetException({
    String? message,
    String? code,
    dynamic originalError,
    StackTrace? stackTrace,
  }) : super(
          message ?? 'Notification widget is not initialized',
          code: code ?? 'WIDGET_NOT_INITIALIZED',
          originalError: originalError,
          stackTrace: stackTrace,
        );
}

/// Exception thrown when notification provider is not initialized.
class NotificationProviderException extends NotificationException {
  const NotificationProviderException({
    String? message,
    String? code,
    dynamic originalError,
    StackTrace? stackTrace,
  }) : super(
          message ?? 'Notification provider is not initialized',
          code: code ?? 'PROVIDER_NOT_INITIALIZED',
          originalError: originalError,
          stackTrace: stackTrace,
        );
}

/// Exception thrown when notification utils are not initialized.
class NotificationUtilsException extends NotificationException {
  const NotificationUtilsException({
    String? message,
    String? code,
    dynamic originalError,
    StackTrace? stackTrace,
  }) : super(
          message ?? 'Notification utils are not initialized',
          code: code ?? 'UTILS_NOT_INITIALIZED',
          originalError: originalError,
          stackTrace: stackTrace,
        );
}

/// Exception thrown when notification constants are not initialized.
class NotificationConstantsException extends NotificationException {
  const NotificationConstantsException({
    String? message,
    String? code,
    dynamic originalError,
    StackTrace? stackTrace,
  }) : super(
          message ?? 'Notification constants are not initialized',
          code: code ?? 'CONSTANTS_NOT_INITIALIZED',
          originalError: originalError,
          stackTrace: stackTrace,
        );
}

/// Exception thrown when notification models are not initialized.
class NotificationModelsException extends NotificationException {
  const NotificationModelsException({
    String? message,
    String? code,
    dynamic originalError,
    StackTrace? stackTrace,
  }) : super(
          message ?? 'Notification models are not initialized',
          code: code ?? 'MODELS_NOT_INITIALIZED',
          originalError: originalError,
          stackTrace: stackTrace,
        );
} 