/// Constants for notification-related functionality.
class NotificationConstants {
  /// Channel IDs for different types of notifications.
  static const String dailyNotificationChannelId = 'daily_notification';
  static const String progressNotificationChannelId = 'progress_notification';
  static const String streakNotificationChannelId = 'streak_notification';
  static const String achievementNotificationChannelId = 'achievement_notification';

  /// Channel names for different types of notifications.
  static const String dailyNotificationChannelName = 'Daily Notifications';
  static const String progressNotificationChannelName = 'Progress Notifications';
  static const String streakNotificationChannelName = 'Streak Notifications';
  static const String achievementNotificationChannelName = 'Achievement Notifications';

  /// Channel descriptions for different types of notifications.
  static const String dailyNotificationChannelDescription =
      'Daily motivational notifications';
  static const String progressNotificationChannelDescription =
      'Notifications about your daily progress';
  static const String streakNotificationChannelDescription =
      'Notifications about your streak status';
  static const String achievementNotificationChannelDescription =
      'Notifications about unlocked achievements';

  /// Default notification settings.
  static const int defaultNotificationHour = 9;
  static const int defaultNotificationMinute = 0;
  static const bool defaultNotificationsEnabled = false;
  static const bool defaultProgressNotificationsEnabled = true;
  static const bool defaultStreakNotificationsEnabled = true;
  static const bool defaultAchievementNotificationsEnabled = true;

  /// Notification titles.
  static const String dailyNotificationTitle = 'Maxed Out Life';
  static const String progressNotificationTitle = 'Progress Update';
  static const String streakNotificationTitle = 'Streak Update';
  static const String achievementNotificationTitle = 'Achievement Unlocked!';

  /// Notification messages.
  static const String dailyNotificationMessage =
      'Time to check your progress and set new goals!';
  static const String progressNotificationMessage =
      'You\'ve made progress on your goals today!';
  static const String streakNotificationMessage =
      'Keep up your streak! You\'re doing great!';
  static const String achievementNotificationMessage =
      'Congratulations! You\'ve unlocked a new achievement!';

  /// Notification icons.
  static const String dailyNotificationIcon = '@mipmap/mxd_icon';
  static const String progressNotificationIcon = '@mipmap/mxd_icon';
  static const String streakNotificationIcon = '@mipmap/mxd_icon';
  static const String achievementNotificationIcon = '@mipmap/mxd_icon';

  /// Notification colors.
  static const int dailyNotificationColor = 0xFF2196F3; // Blue
  static const int progressNotificationColor = 0xFF4CAF50; // Green
  static const int streakNotificationColor = 0xFFFFC107; // Amber
  static const int achievementNotificationColor = 0xFF9C27B0; // Purple

  /// Notification priorities.
  static const int dailyNotificationPriority = 1;
  static const int progressNotificationPriority = 2;
  static const int streakNotificationPriority = 2;
  static const int achievementNotificationPriority = 3;

  /// Notification importance levels.
  static const String dailyNotificationImportance = 'high';
  static const String progressNotificationImportance = 'default';
  static const String streakNotificationImportance = 'default';
  static const String achievementNotificationImportance = 'high';

  /// Notification sound settings.
  static const bool dailyNotificationSound = true;
  static const bool progressNotificationSound = true;
  static const bool streakNotificationSound = true;
  static const bool achievementNotificationSound = true;

  /// Notification vibration settings.
  static const bool dailyNotificationVibration = true;
  static const bool progressNotificationVibration = true;
  static const bool streakNotificationVibration = true;
  static const bool achievementNotificationVibration = true;

  /// Notification LED settings.
  static const bool dailyNotificationLed = true;
  static const bool progressNotificationLed = true;
  static const bool streakNotificationLed = true;
  static const bool achievementNotificationLed = true;

  /// Notification group settings.
  static const String notificationGroupKey = 'mol_notification_group';
  static const String notificationGroupName = 'Maxed Out Life Notifications';
  static const String notificationGroupDescription =
      'Notifications from Maxed Out Life app';

  /// Notification category settings.
  static const String notificationCategory = 'mol_notification_category';
  static const String notificationCategoryName = 'Maxed Out Life';
  static const String notificationCategoryDescription =
      'Notifications from Maxed Out Life app';

  /// Notification action settings.
  static const String notificationActionView = 'VIEW';
  static const String notificationActionDismiss = 'DISMISS';
  static const String notificationActionSnooze = 'SNOOZE';

  /// Notification action labels.
  static const String notificationActionViewLabel = 'View';
  static const String notificationActionDismissLabel = 'Dismiss';
  static const String notificationActionSnoozeLabel = 'Snooze';

  /// Notification action icons.
  static const String notificationActionViewIcon = 'ic_view';
  static const String notificationActionDismissIcon = 'ic_dismiss';
  static const String notificationActionSnoozeIcon = 'ic_snooze';

  /// Notification action colors.
  static const int notificationActionViewColor = 0xFF2196F3; // Blue
  static const int notificationActionDismissColor = 0xFFF44336; // Red
  static const int notificationActionSnoozeColor = 0xFFFFC107; // Amber

  /// Notification action priorities.
  static const int notificationActionViewPriority = 1;
  static const int notificationActionDismissPriority = 2;
  static const int notificationActionSnoozePriority = 3;

  /// Notification action importance levels.
  static const String notificationActionViewImportance = 'high';
  static const String notificationActionDismissImportance = 'default';
  static const String notificationActionSnoozeImportance = 'default';

  /// Notification action sound settings.
  static const bool notificationActionViewSound = true;
  static const bool notificationActionDismissSound = true;
  static const bool notificationActionSnoozeSound = true;

  /// Notification action vibration settings.
  static const bool notificationActionViewVibration = true;
  static const bool notificationActionDismissVibration = true;
  static const bool notificationActionSnoozeVibration = true;

  /// Notification action LED settings.
  static const bool notificationActionViewLed = true;
  static const bool notificationActionDismissLed = true;
  static const bool notificationActionSnoozeLed = true;
} 