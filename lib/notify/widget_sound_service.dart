import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/foundation.dart';

/// Service class to manage sound effects for widgets.
class WidgetSoundService {
  final AudioPlayer _player = AudioPlayer();
  bool _isInitialized = false;

  /// Initialize the sound service.
  Future<void> init() async {
    if (!_isInitialized) {
      try {
        await _player.setReleaseMode(ReleaseMode.stop);
        _isInitialized = true;
      } catch (e) {
        debugPrint('Error initializing sound service: $e');
      }
    }
  }

  /// Play a lightning sound effect.
  Future<void> playLightningSound() async {
    try {
      if (!_isInitialized) await init();
      await _player.play(AssetSource('sounds/lightning.mp3'));
    } catch (e) {
      debugPrint('Error playing lightning sound: $e');
    }
  }

  /// Play a level up sound effect.
  Future<void> playLevelUpSound() async {
    try {
      if (!_isInitialized) await init();
      await _player.play(AssetSource('sounds/level_up.mp3'));
    } catch (e) {
      debugPrint('Error playing level up sound: $e');
    }
  }

  /// Play a notification sound effect.
  Future<void> playNotificationSound() async {
    try {
      if (!_isInitialized) await init();
      await _player.play(AssetSource('sounds/notification.mp3'));
    } catch (e) {
      debugPrint('Error playing notification sound: $e');
    }
  }

  /// Clean up resources.
  Future<void> dispose() async {
    try {
      await _player.dispose();
      _isInitialized = false;
    } catch (e) {
      debugPrint('Error disposing sound service: $e');
    }
  }
} 