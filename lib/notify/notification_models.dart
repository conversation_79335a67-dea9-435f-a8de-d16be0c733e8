import 'dart:convert';
import 'package:flutter/foundation.dart' show mapEquals;
import 'notification_constants.dart';

/// Model class representing a notification payload.
class NotificationPayload {
  final String type;
  final String title;
  final String body;
  final Map<String, dynamic> data;
  final DateTime timestamp;

  const NotificationPayload({
    required this.type,
    required this.title,
    required this.body,
    required this.data,
    required this.timestamp,
  });

  /// Create a NotificationPayload from a JSON map.
  factory NotificationPayload.fromJson(Map<String, dynamic> json) {
    return NotificationPayload(
      type: json['type'] as String,
      title: json['title'] as String,
      body: json['body'] as String,
      data: Map<String, dynamic>.from(json['data'] as Map),
      timestamp: DateTime.parse(json['timestamp'] as String),
    );
  }

  /// Convert the NotificationPayload to a JSON map.
  Map<String, dynamic> toJson() {
    return {
      'type': type,
      'title': title,
      'body': body,
      'data': data,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  /// Create a NotificationPayload from a JSON string.
  factory NotificationPayload.fromJsonString(String jsonString) {
    return NotificationPayload.fromJson(jsonDecode(jsonString));
  }

  /// Convert the NotificationPayload to a JSON string.
  String toJsonString() {
    return jsonEncode(toJson());
  }

  /// Create a daily notification payload.
  factory NotificationPayload.daily() {
    return NotificationPayload(
      type: 'daily',
      title: NotificationConstants.dailyNotificationTitle,
      body: NotificationConstants.dailyNotificationMessage,
      data: {
        'channelId': NotificationConstants.dailyNotificationChannelId,
        'priority': NotificationConstants.dailyNotificationPriority,
        'importance': NotificationConstants.dailyNotificationImportance,
        'sound': NotificationConstants.dailyNotificationSound,
        'vibration': NotificationConstants.dailyNotificationVibration,
        'led': NotificationConstants.dailyNotificationLed,
      },
      timestamp: DateTime.now(),
    );
  }

  /// Create a progress notification payload.
  factory NotificationPayload.progress(String message) {
    return NotificationPayload(
      type: 'progress',
      title: NotificationConstants.progressNotificationTitle,
      body: message,
      data: {
        'channelId': NotificationConstants.progressNotificationChannelId,
        'priority': NotificationConstants.progressNotificationPriority,
        'importance': NotificationConstants.progressNotificationImportance,
        'sound': NotificationConstants.progressNotificationSound,
        'vibration': NotificationConstants.progressNotificationVibration,
        'led': NotificationConstants.progressNotificationLed,
      },
      timestamp: DateTime.now(),
    );
  }

  /// Create a streak notification payload.
  factory NotificationPayload.streak(String message) {
    return NotificationPayload(
      type: 'streak',
      title: NotificationConstants.streakNotificationTitle,
      body: message,
      data: {
        'channelId': NotificationConstants.streakNotificationChannelId,
        'priority': NotificationConstants.streakNotificationPriority,
        'importance': NotificationConstants.streakNotificationImportance,
        'sound': NotificationConstants.streakNotificationSound,
        'vibration': NotificationConstants.streakNotificationVibration,
        'led': NotificationConstants.streakNotificationLed,
      },
      timestamp: DateTime.now(),
    );
  }

  /// Create an achievement notification payload.
  factory NotificationPayload.achievement(String message) {
    return NotificationPayload(
      type: 'achievement',
      title: NotificationConstants.achievementNotificationTitle,
      body: message,
      data: {
        'channelId': NotificationConstants.achievementNotificationChannelId,
        'priority': NotificationConstants.achievementNotificationPriority,
        'importance': NotificationConstants.achievementNotificationImportance,
        'sound': NotificationConstants.achievementNotificationSound,
        'vibration': NotificationConstants.achievementNotificationVibration,
        'led': NotificationConstants.achievementNotificationLed,
      },
      timestamp: DateTime.now(),
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is NotificationPayload &&
        other.type == type &&
        other.title == title &&
        other.body == body &&
        mapEquals(other.data, data) &&
        other.timestamp == timestamp;
  }

  @override
  int get hashCode {
    return Object.hash(
      type,
      title,
      body,
      Object.hashAll(data.entries),
      timestamp,
    );
  }
}

/// Model class representing a notification action.
class NotificationAction {
  final String type;
  final String label;
  final String icon;
  final int color;
  final int priority;
  final String importance;
  final bool sound;
  final bool vibration;
  final bool led;

  const NotificationAction({
    required this.type,
    required this.label,
    required this.icon,
    required this.color,
    required this.priority,
    required this.importance,
    required this.sound,
    required this.vibration,
    required this.led,
  });

  /// Create a NotificationAction from a JSON map.
  factory NotificationAction.fromJson(Map<String, dynamic> json) {
    return NotificationAction(
      type: json['type'] as String,
      label: json['label'] as String,
      icon: json['icon'] as String,
      color: json['color'] as int,
      priority: json['priority'] as int,
      importance: json['importance'] as String,
      sound: json['sound'] as bool,
      vibration: json['vibration'] as bool,
      led: json['led'] as bool,
    );
  }

  /// Convert the NotificationAction to a JSON map.
  Map<String, dynamic> toJson() {
    return {
      'type': type,
      'label': label,
      'icon': icon,
      'color': color,
      'priority': priority,
      'importance': importance,
      'sound': sound,
      'vibration': vibration,
      'led': led,
    };
  }

  /// Create a view action.
  factory NotificationAction.view() {
    return NotificationAction(
      type: NotificationConstants.notificationActionView,
      label: NotificationConstants.notificationActionViewLabel,
      icon: NotificationConstants.notificationActionViewIcon,
      color: NotificationConstants.notificationActionViewColor,
      priority: NotificationConstants.notificationActionViewPriority,
      importance: NotificationConstants.notificationActionViewImportance,
      sound: NotificationConstants.notificationActionViewSound,
      vibration: NotificationConstants.notificationActionViewVibration,
      led: NotificationConstants.notificationActionViewLed,
    );
  }

  /// Create a dismiss action.
  factory NotificationAction.dismiss() {
    return NotificationAction(
      type: NotificationConstants.notificationActionDismiss,
      label: NotificationConstants.notificationActionDismissLabel,
      icon: NotificationConstants.notificationActionDismissIcon,
      color: NotificationConstants.notificationActionDismissColor,
      priority: NotificationConstants.notificationActionDismissPriority,
      importance: NotificationConstants.notificationActionDismissImportance,
      sound: NotificationConstants.notificationActionDismissSound,
      vibration: NotificationConstants.notificationActionDismissVibration,
      led: NotificationConstants.notificationActionDismissLed,
    );
  }

  /// Create a snooze action.
  factory NotificationAction.snooze() {
    return NotificationAction(
      type: NotificationConstants.notificationActionSnooze,
      label: NotificationConstants.notificationActionSnoozeLabel,
      icon: NotificationConstants.notificationActionSnoozeIcon,
      color: NotificationConstants.notificationActionSnoozeColor,
      priority: NotificationConstants.notificationActionSnoozePriority,
      importance: NotificationConstants.notificationActionSnoozeImportance,
      sound: NotificationConstants.notificationActionSnoozeSound,
      vibration: NotificationConstants.notificationActionSnoozeVibration,
      led: NotificationConstants.notificationActionSnoozeLed,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is NotificationAction &&
        other.type == type &&
        other.label == label &&
        other.icon == icon &&
        other.color == color &&
        other.priority == priority &&
        other.importance == importance &&
        other.sound == sound &&
        other.vibration == vibration &&
        other.led == led;
  }

  @override
  int get hashCode {
    return Object.hash(
      type,
      label,
      icon,
      color,
      priority,
      importance,
      sound,
      vibration,
      led,
    );
  }
} 