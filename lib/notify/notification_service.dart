import 'dart:async';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:timezone/data/latest.dart' as tz;
import '../bulletproof/error_handler.dart';

/// Service for managing notifications in the app.
class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  final FlutterLocalNotificationsPlugin _notifications = FlutterLocalNotificationsPlugin();
  final ErrorHandler _errorHandler = ErrorHandler();
  Timer? _dailyCheckTimer;

  /// Initialize the notification service.
  Future<void> initialize() async {
    try {
      tz.initializeTimeZones();

      const androidSettings = AndroidInitializationSettings('@mipmap/mxd_icon');
      const iosSettings = DarwinInitializationSettings(
        requestAlertPermission: true,
        requestBadgePermission: true,
        requestSoundPermission: true,
      );

      const macosSettings = DarwinInitializationSettings(
        requestAlertPermission: true,
        requestBadgePermission: true,
        requestSoundPermission: true,
      );

      const initSettings = InitializationSettings(
        android: androidSettings,
        iOS: iosSettings,
        macOS: macosSettings,
      );

      await _notifications.initialize(
        initSettings,
        onDidReceiveNotificationResponse: _onNotificationTapped,
      );

      // Request permissions
      await _notifications.resolvePlatformSpecificImplementation<
          IOSFlutterLocalNotificationsPlugin>()?.requestPermissions(
        alert: true,
        badge: true,
        sound: true,
      );

      // Start daily check timer
      _startDailyCheck();
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'Initializing notification service');
    }
  }

  /// Schedule a daily notification at the specified time.
  Future<void> scheduleDailyNotification({
    required int hour,
    required int minute,
    required String title,
    required String body,
  }) async {
    try {
      final now = DateTime.now();
      var scheduledDate = DateTime(
        now.year,
        now.month,
        now.day,
        hour,
        minute,
      );

      if (scheduledDate.isBefore(now)) {
        scheduledDate = scheduledDate.add(const Duration(days: 1));
      }

      await _notifications.zonedSchedule(
        0,
        title,
        body,
        tz.TZDateTime.from(scheduledDate, tz.local),
        NotificationDetails(
          android: AndroidNotificationDetails(
            'daily_notification',
            'Daily Notifications',
            channelDescription: 'Daily motivational notifications',
            importance: Importance.high,
            priority: Priority.high,
          ),
          iOS: const DarwinNotificationDetails(
            presentAlert: true,
            presentBadge: true,
            presentSound: true,
          ),
        ),
        androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
        matchDateTimeComponents: DateTimeComponents.time,
      );
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'Scheduling daily notification');
    }
  }

  /// Show an immediate notification.
  Future<void> showNotification({
    required String title,
    required String body,
    String? payload,
  }) async {
    try {
      await _notifications.show(
        0,
        title,
        body,
        NotificationDetails(
          android: AndroidNotificationDetails(
            'immediate_notification',
            'Immediate Notifications',
            channelDescription: 'Immediate notifications',
            importance: Importance.high,
            priority: Priority.high,
          ),
          iOS: const DarwinNotificationDetails(
            presentAlert: true,
            presentBadge: true,
            presentSound: true,
          ),
        ),
        payload: payload,
      );
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'Showing immediate notification');
    }
  }

  /// Cancel all scheduled notifications.
  Future<void> cancelAllNotifications() async {
    try {
      await _notifications.cancelAll();
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'Canceling all notifications');
    }
  }

  /// Start the daily check timer.
  void _startDailyCheck() {
    _dailyCheckTimer?.cancel();
    _dailyCheckTimer = Timer.periodic(
      const Duration(minutes: 15),
      (_) => _checkAndUpdateNotifications(),
    );
  }

  /// Check and update notifications based on user state.
  Future<void> _checkAndUpdateNotifications() async {
    try {
      // Note: Implement notification check logic based on user state
      // This should check user progress, streaks, and other relevant data
      // to determine if notifications should be sent
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'Checking and updating notifications');
    }
  }

  /// Handle notification tap events.
  void _onNotificationTapped(NotificationResponse response) {
    // Note: Implement notification tap handling
    // This should navigate to the appropriate screen based on the notification
  }

  /// Dispose the notification service.
  void dispose() {
    _dailyCheckTimer?.cancel();
  }
} 