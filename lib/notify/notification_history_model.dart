import 'dart:convert';
//import 'package:flutter/material.dart';
//import 'quote_model.dart';

/// Model class for notification history.
class NotificationHistory {
  final List<String> quoteIds;
  final List<NotificationEntry> entries;
  final DateTime lastUpdated;

  NotificationHistory({
    required this.quoteIds,
    required this.entries,
    required this.lastUpdated,
  });

  /// Create an empty instance.
  factory NotificationHistory.empty() {
    return NotificationHistory(
      quoteIds: [],
      entries: [],
      lastUpdated: DateTime.now(),
    );
  }

  /// Add a new entry to the history.
  void addEntry(String quoteId) {
    entries.add(NotificationEntry(
      id: quoteId,
      title: '',
      body: '',
      timestamp: DateTime.now(),
    ));
  }

  /// Check if a quote was used recently.
  bool wasQuoteUsedRecently(String quoteId, int days) {
    final cutoffDate = DateTime.now().subtract(Duration(days: days));
    return entries.any((entry) {
      return entry.id == quoteId && entry.timestamp.isAfter(cutoffDate);
    });
  }

  /// Convert the history to a JSON map.
  Map<String, dynamic> toJson() {
    return {
      'quoteIds': quoteIds,
      'entries': entries.map((e) => e.toJson()).toList(),
      'lastUpdated': lastUpdated.toIso8601String(),
    };
  }

  /// Convert the history to a JSON string.
  String toJsonString() => jsonEncode(toJson());

  /// Create a NotificationHistory instance from a JSON string.
  factory NotificationHistory.fromJsonString(String jsonString) {
    return NotificationHistory.fromJson(jsonDecode(jsonString));
  }

  /// Creates a NotificationHistory from a JSON map.
  factory NotificationHistory.fromJson(Map<String, dynamic> json) {
    return NotificationHistory(
      quoteIds: List<String>.from(json['quoteIds'] ?? []),
      entries: (json['entries'] as List<dynamic>?)
              ?.map((e) => NotificationEntry.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
    );
  }
}

/// Model class for notification entries.
class NotificationEntry {
  final String id;
  final String title;
  final String body;
  final DateTime timestamp;

  NotificationEntry({
    required this.id,
    required this.title,
    required this.body,
    required this.timestamp,
  });

  /// Convert the entry to a JSON map.
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'body': body,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  /// Create a NotificationEntry instance from a JSON map.
  factory NotificationEntry.fromJson(Map<String, dynamic> json) {
    return NotificationEntry(
      id: json['id'] as String,
      title: json['title'] as String,
      body: json['body'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
    );
  }
} 