import 'dart:convert';

/// Model class representing widget preferences.
class WidgetPreferences {
  final bool showHomeLevelWidget;
  final bool showNorthStarWidget;
  final bool showLockScreenWidget;
  final DateTime lastUpdated;
  final String lastNotificationQuote;
  final double dailyGoalProgress;
  final int streakDays;
  final int level;
  final int rank;
  final int rankProgress;
  final bool showExpWidget;
  final bool showHabitsWidget;

  const WidgetPreferences({
    required this.showHomeLevelWidget,
    required this.showNorthStarWidget,
    required this.showLockScreenWidget,
    required this.lastUpdated,
    required this.lastNotificationQuote,
    required this.dailyGoalProgress,
    required this.streakDays,
    required this.level,
    required this.rank,
    required this.rankProgress,
    required this.showExpWidget,
    required this.showHabitsWidget,
  });

  /// Create an empty instance with default values.
  factory WidgetPreferences.empty() {
    return WidgetPreferences(
      showHomeLevelWidget: false,
      showNorthStarWidget: false,
      showLockScreenWidget: false,
      lastUpdated: DateTime.now(),
      lastNotificationQuote: '',
      dailyGoalProgress: 0.0,
      streakDays: 0,
      level: 1,
      rank: 1,
      rankProgress: 0,
      showExpWidget: false,
      showHabitsWidget: false,
    );
  }

  /// Create a copy of this instance with the given fields replaced with new values.
  WidgetPreferences copyWith({
    bool? showHomeLevelWidget,
    bool? showNorthStarWidget,
    bool? showLockScreenWidget,
    DateTime? lastUpdated,
    String? lastNotificationQuote,
    double? dailyGoalProgress,
    int? streakDays,
    int? level,
    int? rank,
    int? rankProgress,
    bool? showExpWidget,
    bool? showHabitsWidget,
  }) {
    return WidgetPreferences(
      showHomeLevelWidget: showHomeLevelWidget ?? this.showHomeLevelWidget,
      showNorthStarWidget: showNorthStarWidget ?? this.showNorthStarWidget,
      showLockScreenWidget: showLockScreenWidget ?? this.showLockScreenWidget,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      lastNotificationQuote: lastNotificationQuote ?? this.lastNotificationQuote,
      dailyGoalProgress: dailyGoalProgress ?? this.dailyGoalProgress,
      streakDays: streakDays ?? this.streakDays,
      level: level ?? this.level,
      rank: rank ?? this.rank,
      rankProgress: rankProgress ?? this.rankProgress,
      showExpWidget: showExpWidget ?? this.showExpWidget,
      showHabitsWidget: showHabitsWidget ?? this.showHabitsWidget,
    );
  }

  /// Create a WidgetPreferences from a JSON map.
  factory WidgetPreferences.fromJson(Map<String, dynamic> json) {
    return WidgetPreferences(
      showHomeLevelWidget: json['showHomeLevelWidget'] ?? false,
      showNorthStarWidget: json['showNorthStarWidget'] ?? false,
      showLockScreenWidget: json['showLockScreenWidget'] ?? false,
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
      lastNotificationQuote: json['lastNotificationQuote'] ?? '',
      dailyGoalProgress: json['dailyGoalProgress'] ?? 0.0,
      streakDays: json['streakDays'] ?? 0,
      level: json['level'] ?? 1,
      rank: json['rank'] ?? 1,
      rankProgress: json['rankProgress'] ?? 0,
      showExpWidget: json['showExpWidget'] ?? false,
      showHabitsWidget: json['showHabitsWidget'] ?? false,
    );
  }

  /// Convert the WidgetPreferences to a JSON map.
  Map<String, dynamic> toJson() {
    return {
      'showHomeLevelWidget': showHomeLevelWidget,
      'showNorthStarWidget': showNorthStarWidget,
      'showLockScreenWidget': showLockScreenWidget,
      'lastUpdated': lastUpdated.toIso8601String(),
      'lastNotificationQuote': lastNotificationQuote,
      'dailyGoalProgress': dailyGoalProgress,
      'streakDays': streakDays,
      'level': level,
      'rank': rank,
      'rankProgress': rankProgress,
      'showExpWidget': showExpWidget,
      'showHabitsWidget': showHabitsWidget,
    };
  }

  /// Convert the WidgetPreferences to a JSON string.
  String toJsonString() {
    return jsonEncode(toJson());
  }

  /// Create a WidgetPreferences from a JSON string.
  factory WidgetPreferences.fromJsonString(String jsonString) {
    return WidgetPreferences.fromJson(jsonDecode(jsonString));
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is WidgetPreferences &&
        other.showHomeLevelWidget == showHomeLevelWidget &&
        other.showNorthStarWidget == showNorthStarWidget &&
        other.showLockScreenWidget == showLockScreenWidget &&
        other.lastUpdated == lastUpdated &&
        other.lastNotificationQuote == lastNotificationQuote &&
        other.dailyGoalProgress == dailyGoalProgress &&
        other.streakDays == streakDays &&
        other.level == level &&
        other.rank == rank &&
        other.rankProgress == rankProgress &&
        other.showExpWidget == showExpWidget &&
        other.showHabitsWidget == showHabitsWidget;
  }

  @override
  int get hashCode {
    return Object.hash(
      showHomeLevelWidget,
      showNorthStarWidget,
      showLockScreenWidget,
      lastUpdated,
      lastNotificationQuote,
      dailyGoalProgress,
      streakDays,
      level,
      rank,
      rankProgress,
      showExpWidget,
      showHabitsWidget,
    );
  }
} 