import 'dart:math' as math;
import 'package:flutter/material.dart';

/// Utility class for widget animations.
class WidgetAnimationUtils {
  /// Draw lightning bolts on a progress bar.
  static void drawLightningBolts(
    Canvas canvas,
    Size size,
    double progress,
    Paint paint,
  ) {
    try {
      final boltCount = 7;
      final boltSpacing = size.width / (boltCount + 1);
      final random = math.Random();

      for (var i = 1; i <= boltCount; i++) {
        final x = boltSpacing * i;
        if (x <= size.width * progress) {
          final path = Path();
          path.moveTo(x, 0);
          path.lineTo(x - 2 + random.nextDouble() * 4, size.height * 0.3);
          path.lineTo(x + 2 - random.nextDouble() * 4, size.height * 0.5);
          path.lineTo(x - 1 + random.nextDouble() * 2, size.height * 0.7);
          path.lineTo(x, size.height);

          final lightningPaint = Paint()
            ..color = paint.color
            ..style = PaintingStyle.stroke
            ..strokeWidth = 2.0;

          canvas.drawPath(path, lightningPaint);
        }
      }
    } catch (e) {
      debugPrint('Error drawing lightning bolts: $e');
    }
  }

  /// Draw a particle burst effect.
  static void drawParticleBurst(
    Canvas canvas,
    Offset center,
    double radius,
    Paint paint,
  ) {
    try {
      final particleCount = 12;
      final angleStep = 2 * math.pi / particleCount;
      final random = math.Random();

      for (var i = 0; i < particleCount; i++) {
        final angle = i * angleStep;
        final x = center.dx + radius * math.cos(angle) + random.nextDouble() * 4 - 2;
        final y = center.dy + radius * math.sin(angle) + random.nextDouble() * 4 - 2;

        final particlePaint = Paint()
          ..color = paint.color
          ..style = PaintingStyle.fill;

        canvas.drawCircle(Offset(x, y), 3.0, particlePaint);
      }
    } catch (e) {
      debugPrint('Error drawing particle burst: $e');
    }
  }

  /// Draw a neon glow effect.
  static void drawNeonGlow(
    Canvas canvas,
    Path path,
    Color color,
    double blurRadius,
  ) {
    try {
      final paint = Paint()
        ..color = color.withAlpha((0.5 * 255).toInt())
        ..maskFilter = MaskFilter.blur(BlurStyle.normal, blurRadius * 1.5);

      canvas.drawPath(path, paint);
    } catch (e) {
      debugPrint('Error drawing neon glow: $e');
    }
  }

  /// Draw a progress bar with lightning effect.
  static void drawProgressBar(
    Canvas canvas,
    Size size,
    double progress,
    Color fillColor,
    Color glowColor,
    double glowScale,
    double lightningValue,
  ) {
    try {
      final backgroundPaint = Paint()
        ..color = Colors.grey.withAlpha((0.2 * 255).toInt())
        ..style = PaintingStyle.fill;

      final fillPaint = Paint()
        ..color = fillColor
        ..style = PaintingStyle.fill;

      final glowPaint = Paint()
        ..color = glowColor.withAlpha((0.5 * 255).toInt())
        ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 6.0);

      final lightningPaint = Paint()
        ..color = Colors.white.withAlpha((0.7 * 255).toInt())
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2.0;

      // Draw background
      final backgroundRect = RRect.fromRectAndRadius(
        Rect.fromLTWH(0, 0, size.width, size.height),
        const Radius.circular(4.0),
      );
      canvas.drawRRect(backgroundRect, backgroundPaint);

      // Draw filled portion
      final fillRect = RRect.fromRectAndRadius(
        Rect.fromLTWH(0, 0, size.width * progress, size.height),
        const Radius.circular(4.0),
      );
      canvas.drawRRect(fillRect, fillPaint);

      // Draw glow effect
      final glowRect = RRect.fromRectAndRadius(
        Rect.fromLTWH(
          0,
          0,
          size.width * progress * glowScale,
          size.height,
        ),
        const Radius.circular(4.0),
      );
      canvas.drawRRect(glowRect, glowPaint);

      // Draw lightning bolts
      drawLightningBolts(canvas, size, progress, lightningPaint);
    } catch (e) {
      debugPrint('Error drawing progress bar: $e');
    }
  }
} 