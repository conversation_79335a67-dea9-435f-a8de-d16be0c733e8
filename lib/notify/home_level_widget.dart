import 'package:flutter/material.dart';
import 'package:home_widget/home_widget.dart';
import 'progress_indicator_widget.dart';
import 'widget_animation_utils.dart';

/// A widget for displaying the user's level and progress on the home screen.
class HomeLevelWidget extends StatefulWidget {
  const HomeLevelWidget({super.key});

  @override
  State<HomeLevelWidget> createState() => _HomeLevelWidgetState();
}

class _HomeLevelWidgetState extends State<HomeLevelWidget>
    with SingleTickerProviderStateMixin {
  int _level = 1;
  double _progress = 0.0;
  int _streak = 0;
  DateTime? _lastUpdated;
  bool _isLoading = true;
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 2000),
    )..repeat();

    _loadData();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    try {
      final data = await HomeWidget.getWidgetData<Map<String, dynamic>>('home_level_widget');
      if (data != null) {
        setState(() {
          _level = data['level'] as int? ?? 1;
          _progress = data['progress'] as double? ?? 0.0;
          _streak = data['streak'] as int? ?? 0;
          _lastUpdated = data['lastUpdated'] != null ? DateTime.parse(data['lastUpdated'] as String) : null;
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('Error loading home level widget data: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }
    return Stack(
      alignment: Alignment.center,
      children: [
        Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Level $_level',
              style: TextStyle(
                color: Colors.amberAccent.shade200,
                fontSize: 28.0,
                fontWeight: FontWeight.bold,
                shadows: [
                  Shadow(
                    blurRadius: 16.0,
                    color: Colors.amberAccent.withAlpha(180),
                    offset: Offset(0, 0),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 8.0),
            ProgressIndicatorWidget(
              progress: _progress,
              fillColor: Colors.amberAccent,
              glowColor: Colors.yellowAccent,
              height: 12.0,
              borderRadius: 6.0,
              showLightning: true,
              showGlow: true,
            ),
            const SizedBox(height: 8.0),
            Text(
              'Streak: $_streak days',
              style: TextStyle(
                color: Colors.lightBlueAccent.shade100,
                fontSize: 16.0,
                fontWeight: FontWeight.w500,
              ),
            ),
            if (_lastUpdated != null)
              Text(
                'Last updated: ${_formatLastUpdated(_lastUpdated!)}',
                style: TextStyle(
                  color: Colors.grey.shade400,
                  fontSize: 12.0,
                ),
              ),
          ],
        ),
        if (_progress >= 1.0) ...[
          // Particle burst overlay
          Positioned.fill(
            child: IgnorePointer(
              child: CustomPaint(
                painter: _LevelUpBurstPainter(),
              ),
            ),
          ),
        ],
      ],
    );
  }

  String _formatLastUpdated(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inDays}d ago';
    }
  }
}

class _LevelUpBurstPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final burstPaint = Paint()
      ..color = Colors.amberAccent
      ..style = PaintingStyle.fill;
    WidgetAnimationUtils.drawParticleBurst(
      canvas,
      Offset(size.width / 2, size.height / 2),
      size.height * 0.7,
      burstPaint,
    );
  }
  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
} 