//import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';

class AndroidNotificationHelper {
  static const String channelId = 'coach_follow_up';
  static const String channelName = 'Coach Follow-up';
  static const String channelDescription = 'Notifications for coach follow-up messages';

  static Future<void> createNotificationChannel() async {
    const AndroidNotificationChannel channel = AndroidNotificationChannel(
      channelId,
      channelName,
      description: channelDescription,
      importance: Importance.high,
    );

    final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();
    await flutterLocalNotificationsPlugin.resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()?.createNotificationChannel(channel);
  }

  static Future<bool> requestNotificationPermission() async {
    // No requestPermission method for Android; permissions are granted at install (except Android 13+)
    return true;
  }
} 