//import 'package:flutter/material.dart';

class ChatMessage {
  final String sender;
  final String content;
  final DateTime timestamp;

  ChatMessage({
    required this.sender,
    required this.content,
    required this.timestamp,
  }) {
    // Validate input
    if (sender.isEmpty) throw ArgumentError('Sender cannot be empty');
    if (content.isEmpty) throw ArgumentError('Content cannot be empty');
  }

  Map<String, dynamic> toJson() {
    return {
      'sender': sender,
      'content': content,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  factory ChatMessage.fromJson(Map<String, dynamic> json) {
    return ChatMessage(
      sender: json['sender'] ?? 'Unknown',
      content: json['content'] ?? '',
      timestamp: DateTime.tryParse(json['timestamp'] ?? '') ?? DateTime.now(),
    );
  }
}

class ChatHistory {
  final String coachId;
  final List<ChatMessage> messages;
  DateTime lastFollowUpDate;

  ChatHistory({
    required this.coachId,
    required this.messages,
    required this.lastFollowUpDate,
  }) {
    // Validate input
    if (coachId.isEmpty) throw ArgumentError('Coach ID cannot be empty');
  }

  Map<String, dynamic> toJson() {
    return {
      'coachId': coachId,
      'messages': messages.map((m) => m.toJson()).toList(),
      'lastFollowUpDate': lastFollowUpDate.toIso8601String(),
    };
  }

  factory ChatHistory.fromJson(Map<String, dynamic> json) {
    return ChatHistory(
      coachId: json['coachId'] ?? 'Unknown',
      messages: (json['messages'] as List? ?? []).map((m) => ChatMessage.fromJson(m)).toList(),
      lastFollowUpDate: DateTime.tryParse(json['lastFollowUpDate'] ?? '') ?? DateTime.now(),
    );
  }
} 