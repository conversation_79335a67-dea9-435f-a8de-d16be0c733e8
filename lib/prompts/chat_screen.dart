import 'package:flutter/material.dart';
import 'chat_history.dart';

class ChatScreen extends StatelessWidget {
  final String coachId;
  final ChatHistory chatHistory;

  const ChatScreen({super.key, required this.coachId, required this.chatHistory});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Chat with $coachId')),
      body: chatHistory.messages.isEmpty
          ? const Center(child: Text('No messages yet'))
          : ListView.builder(
              itemCount: chatHistory.messages.length,
              itemBuilder: (context, index) {
                final message = chatHistory.messages[index];
                return ListTile(
                  title: Text(message.sender),
                  subtitle: Text(message.content),
                  trailing: Text(message.timestamp.toString()),
                );
              },
            ),
    );
  }
} 