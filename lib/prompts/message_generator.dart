//import 'package:flutter/material.dart';
import 'chat_history.dart';

class MessageGenerator {
  static String generateFollowUpMessage(ChatHistory chatHistory) {
    if (chatHistory.messages.isEmpty) {
      return 'Hey! How are you doing today?';
    }

    final lastMessage = chatHistory.messages.last;
    final lastContent = lastMessage.content.toLowerCase();

    // Edge case: very long messages
    if (lastContent.length > 100) {
      return 'Last time you shared a lot. How are things going now?';
    }

    if (lastContent.contains('struggling') || lastContent.contains('difficult')) {
      return 'Last time you mentioned struggling with something. How is that going?';
    } else if (lastContent.contains('goal') || lastContent.contains('target')) {
      return 'How are you progressing toward your goal?';
    } else if (lastContent.contains('happy') || lastContent.contains('excited')) {
      return 'Great to hear you were happy last time! What\'s new?';
    } else {
      return 'Hey! Just checking in. How are things going?';
    }
  }
} 