# MXD Life Coach Follow-Up Notifications

This feature allows coaches to send personalized follow-up messages to players every other day, based on their last conversation.

## How It Works

- **Chat History:** Each coach has a chat history with messages and a last follow-up date.
- **Message Generation:** Follow-up messages are generated based on the last conversation.
- **Notifications:** Notifications are scheduled at 07:00, 12:45, and 19:45 every other day.
- **Tapping Notifications:** Opens the chat with the relevant coach and displays the new message.

## Files

- `chat_history.dart`: Data model for chat messages and history.
- `message_generator.dart`: Logic to generate follow-up messages.
- `notification_scheduler.dart`: Schedules notifications for follow-up messages.
- `notification_handler.dart`: Handles notification taps and navigation.
- `chat_screen.dart`: UI for displaying chat messages.
- `main.dart`: Entry point for the app, initializes notifications and sets up routes.

## Usage

1. **Initialize Notifications:**
   - Call `NotificationHandler.initialize(context)` in your app's entry point.

2. **Schedule Notifications:**
   - Call `NotificationScheduler.scheduleFollowUpNotifications(chatHistories)` to schedule notifications for each coach.

3. **Handle Notification Taps:**
   - When a notification is tapped, the app navigates to the chat screen with the relevant coach.

## Testing

- Test the app in different states (background, foreground, terminated).
- Ensure notifications are scheduled correctly and messages are displayed as expected. 