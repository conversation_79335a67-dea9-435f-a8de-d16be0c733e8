import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
//import 'chat_history.dart';

class NotificationHandler {
  static final FlutterLocalNotificationsPlugin _notifications = FlutterLocalNotificationsPlugin();

  static Future<void> initialize(BuildContext context) async {
    const AndroidInitializationSettings androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');
    const DarwinInitializationSettings iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );
    const InitializationSettings initSettings = InitializationSettings(android: androidSettings, iOS: iosSettings);
    await _notifications.initialize(initSettings, onDidReceiveNotificationResponse: (NotificationResponse response) {
      handleNotificationTap(response.payload, context);
    });
  }

  static void handleNotificationTap(String? payload, BuildContext context) {
    if (payload != null && payload.isNotEmpty) {
      // Navigate to the chat with the coach
      Navigator.pushNamed(context, '/chat', arguments: payload);
    } else {
      // Handle invalid payload
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Invalid notification payload')),
      );
    }
  }
} 