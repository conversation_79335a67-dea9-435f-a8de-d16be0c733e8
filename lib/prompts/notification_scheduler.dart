//import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:timezone/timezone.dart' as tz;
import 'chat_history.dart';
import 'message_generator.dart';

class NotificationScheduler {
  static final FlutterLocalNotificationsPlugin _notifications = FlutterLocalNotificationsPlugin();
  static final Set<int> _scheduledNotificationIds = {};

  static Future<void> initialize() async {
    const AndroidInitializationSettings androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');
    const DarwinInitializationSettings iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );
    const InitializationSettings initSettings = InitializationSettings(android: androidSettings, iOS: iosSettings);
    await _notifications.initialize(initSettings);
  }

  static Future<void> scheduleFollowUpNotifications(List<ChatHistory> chatHistories) async {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);

    for (var chatHistory in chatHistories) {
      final lastFollowUp = chatHistory.lastFollowUpDate;
      final daysSinceLastFollowUp = today.difference(lastFollowUp).inDays;

      if (daysSinceLastFollowUp >= 2) {
        final followUpMessage = MessageGenerator.generateFollowUpMessage(chatHistory);
        final notificationTimes = [
          tz.TZDateTime.from(DateTime(today.year, today.month, today.day, 7, 0), tz.local),
          tz.TZDateTime.from(DateTime(today.year, today.month, today.day, 12, 45), tz.local),
          tz.TZDateTime.from(DateTime(today.year, today.month, today.day, 19, 45), tz.local),
        ];

        for (var time in notificationTimes) {
          if (time.isAfter(now)) {
            final notificationId = chatHistory.coachId.hashCode + time.hour;
            if (!_scheduledNotificationIds.contains(notificationId)) {
              await _notifications.zonedSchedule(
                notificationId,
                'Follow-up from ${chatHistory.coachId}',
                followUpMessage,
                time,
                const NotificationDetails(
                  android: AndroidNotificationDetails(
                    'coach_follow_up',
                    'Coach Follow-up',
                    importance: Importance.high,
                    priority: Priority.high,
                  ),
                  iOS: DarwinNotificationDetails(
                    presentAlert: true,
                    presentBadge: true,
                    presentSound: true,
                  ),
                ),
                androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
                payload: chatHistory.coachId,
              );
              _scheduledNotificationIds.add(notificationId);
            }
          }
        }
        chatHistory.lastFollowUpDate = today;
      }
    }
  }
} 