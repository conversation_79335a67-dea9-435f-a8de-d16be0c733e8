// 📁 lib/models/exp_data.dart

import 'package:uuid/uuid.dart';
import 'services/user_service.dart';
import 'models/user_model.dart';
import 'bulletproof/error_handler.dart';

/// EXP constants
const int levelExp = 100;
const int expFor30Min = 10;
const int expFor1Hour = 20;

/// Default EXP categories
const List<String> baseCategories = [
  'Health',
  'Wealth',
  'Purpose',
  'Connection',
];

// Note: Color mapping is now handled by the centralized getCategoryColor() function
// in lib/theme/colors.dart to ensure consistency across the app.

/// EXP system utilities
class ExpData {
  /// Multiplier based on streak length
  static int getStreakMultiplier(User user) {
    final streak = user.streakDays;
    return (1 + (streak ~/ 7)).clamp(1, 3); // max 3x
  }

  /// Load user or create a blank one
  static Future<User> loadOrCreateUser(String username) async {
    final userService = UserService(ErrorHandler());
    final existing = await userService.loadUser();
    if (existing != null && existing.username == username) {
      return existing;
    } else {
      final newUser = User.blank(id: const Uuid().v4(), username: username);
      await saveUser(newUser);
      return newUser;
    }
  }

  /// Persist user
  static Future<void> saveUser(User user) async {
    final userService = UserService(ErrorHandler());
    await userService.saveUser(user);
  }

  /// Finish a journal entry and award EXP
  static Future<User> finishEntry(User user, String category, int expAmount, String note) async {
    final updated = user.copyWithAddedExp(category, expAmount, note);
    await saveUser(updated);
    return updated;
  }

}
