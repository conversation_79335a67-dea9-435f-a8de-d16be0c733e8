import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/user_model.dart';
import '../controller/user_controller2.dart';
//import '../cstcat/custom_category_service.dart';
import '../cstcat/custom_category_persistence.dart';
import '../theme/colors.dart';
import '../services/music_service.dart';

class CustomCategorySetupWidget extends StatefulWidget {
  final User user;
  final void Function(User) onUserUpdated;
  final bool isInitialSetup;

  const CustomCategorySetupWidget({
    super.key,
    required this.user,
    required this.onUserUpdated,
    this.isInitialSetup = false,
  });

  @override
  State<CustomCategorySetupWidget> createState() => _CustomCategorySetupWidgetState();
}

class _CustomCategorySetupWidgetState extends State<CustomCategorySetupWidget> {
  final TextEditingController _cat1Controller = TextEditingController();
  final TextEditingController _cat2Controller = TextEditingController();
  bool _isSubmitting = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    // Pre-fill existing categories if any
    final existingCats = widget.user.customCategories;
    if (existingCats.isNotEmpty) {
      _cat1Controller.text = existingCats[0];
      if (existingCats.length > 1) {
        _cat2Controller.text = existingCats[1];
      }
    }

    // Add listeners to update button state
    _cat1Controller.addListener(_updateButtonState);
    _cat2Controller.addListener(_updateButtonState);
  }

  void _updateButtonState() {
    setState(() {
      // This will trigger a rebuild and update the button state
    });
  }

  bool _canSave() {
    final cat1 = _cat1Controller.text.trim();
    final cat2 = _cat2Controller.text.trim();
    return cat1.isNotEmpty && cat2.isNotEmpty && !_isSubmitting;
  }

  @override
  void dispose() {
    _cat1Controller.removeListener(_updateButtonState);
    _cat2Controller.removeListener(_updateButtonState);
    _cat1Controller.dispose();
    _cat2Controller.dispose();
    super.dispose();
  }

  bool _validateCategories() {
    final cat1 = _cat1Controller.text.trim();
    final cat2 = _cat2Controller.text.trim();

    if (cat1.isEmpty) {
      setState(() => _errorMessage = "First category cannot be empty");
      return false;
    }

    if (cat2.isEmpty) {
      setState(() => _errorMessage = "Second category cannot be empty");
      return false;
    }

    if (cat1.length > 20) {
      setState(() => _errorMessage = "Category names must be 20 characters or less");
      return false;
    }

    if (cat2.length > 20) {
      setState(() => _errorMessage = "Category names must be 20 characters or less");
      return false;
    }

    if (cat1.toLowerCase() == cat2.toLowerCase()) {
      setState(() => _errorMessage = "Categories cannot be the same");
      return false;
    }

    // Check against built-in categories
    const builtIn = ['Health', 'Wealth', 'Purpose', 'Connection'];
    if (builtIn.contains(cat1) || builtIn.contains(cat2)) {
      setState(() => _errorMessage = "Cannot use built-in category names");
      return false;
    }

    setState(() => _errorMessage = null);
    return true;
  }

  Future<void> _saveCategories() async {
    print('🔘 _saveCategories called, _isSubmitting: $_isSubmitting');

    if (_isSubmitting) {
      print('⚠️ Already submitting, ignoring call');
      return;
    }

    if (!_validateCategories()) return;

    print('🔄 Setting _isSubmitting = true');
    setState(() => _isSubmitting = true);

    try {
      final userController = Provider.of<UserController2>(context, listen: false);
      final cat1 = _cat1Controller.text.trim();
      final cat2 = _cat2Controller.text.trim();

      print('🔍 Saving categories: cat1="$cat1", cat2="$cat2"');
      print('🔍 User existing categories: ${widget.user.customCategories}');
      print('🔍 User categories map: ${widget.user.categories.keys.toList()}');

      // Use the persistence service to ensure reliable saving
      final updatedUser = await CustomCategoryPersistence().saveCategories(
        widget.user,
        [cat1, if (cat2.isNotEmpty) cat2],
      );

      if (updatedUser != null) {
        print('✅ Categories saved successfully, updating UI...');
        userController.user = updatedUser;
        print('🔄 Calling widget.onUserUpdated...');
        widget.onUserUpdated(updatedUser);
        print('✅ widget.onUserUpdated called');

        MusicService.playEffect('assets/sounds/lightning.mp3');

        // Don't manage navigation here - let the parent handle it
        print('✅ Categories saved, letting parent handle navigation');
      } else {
        print('❌ updatedUser is null');
        setState(() => _errorMessage = "Failed to save categories. Please try again.");
      }
    } catch (e) {
      print('❌ Exception in _saveCategories: $e');
      setState(() => _errorMessage = e.toString());
    } finally {
      print('🔄 Setting _isSubmitting = false');
      setState(() => _isSubmitting = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: Colors.black,
      title: Text(
        widget.isInitialSetup ? "Set Your Custom Categories" : "Enter Custom Categories",
        style: const TextStyle(
          color: Colors.cyanAccent,
          fontFamily: 'Pirulen',
          fontSize: 18,
          fontWeight: FontWeight.bold,
        ),
      ),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (_errorMessage != null)
              Padding(
                padding: const EdgeInsets.only(bottom: 16),
                child: Text(
                  _errorMessage!,
                  style: const TextStyle(
                    color: Colors.redAccent,
                    fontSize: 14,
                  ),
                ),
              ),
            const Text(
              "Category 1",
              style: TextStyle(
                color: Colors.greenAccent,
                fontFamily: 'Pirulen',
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: _cat1Controller,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontFamily: 'Bitsumishi',
              ),
              decoration: const InputDecoration(
                hintText: "Enter first category name",
                hintStyle: TextStyle(
                  color: Colors.grey,
                  fontSize: 16,
                  fontFamily: 'Bitsumishi',
                ),
                enabledBorder: UnderlineInputBorder(
                  borderSide: BorderSide(color: Colors.white54),
                ),
                focusedBorder: UnderlineInputBorder(
                  borderSide: BorderSide(color: Colors.greenAccent),
                ),
              ),
            ),
            const SizedBox(height: 24),
            const Text(
              "Category 2",
              style: TextStyle(
                color: Colors.greenAccent,
                fontFamily: 'Pirulen',
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: _cat2Controller,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontFamily: 'Bitsumishi',
              ),
              decoration: const InputDecoration(
                hintText: "Enter second category name",
                hintStyle: TextStyle(
                  color: Colors.grey,
                  fontSize: 16,
                  fontFamily: 'Bitsumishi',
                ),
                enabledBorder: UnderlineInputBorder(
                  borderSide: BorderSide(color: Colors.white54),
                ),
                focusedBorder: UnderlineInputBorder(
                  borderSide: BorderSide(color: Colors.greenAccent),
                ),
              ),
            ),
          ],
        ),
      ),
      actions: [
        if (!widget.isInitialSetup)
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text(
              "Cancel",
              style: TextStyle(
                color: Colors.grey,
                fontSize: 16,
                fontFamily: 'Pirulen',
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        TextButton(
          onPressed: _canSave() ? _saveCategories : null,
          child: Text(
            widget.isInitialSetup ? "Continue" : "Save",
            style: TextStyle(
              color: _canSave() ? MolColors.green : Colors.grey,
              fontSize: 16,
              fontFamily: 'Pirulen',
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ],
    );
  }
} 