import 'package:flutter/material.dart';
import '../models/user_model.dart';
import './first_time_user_manager.dart';
import './critical_path_validator.dart';
import './custom_category_setup_widget.dart';
import './first_time_recovery_manager.dart';
import './category_connection_manager.dart';

class FirstTimeUserFlow extends StatefulWidget {
  final User user;
  final Function(User) onComplete;

  const FirstTimeUserFlow({
    super.key,
    required this.user,
    required this.onComplete,
  });

  @override
  State<FirstTimeUserFlow> createState() => _FirstTimeUserFlowState();
}

class _FirstTimeUserFlowState extends State<FirstTimeUserFlow> {
  final _firstTimeManager = FirstTimeUserManager();
  final _criticalPathValidator = CriticalPathValidator();
  final _recoveryManager = FirstTimeRecoveryManager();
  final _connectionManager = CategoryConnectionManager();
  bool _isLoading = false;
  String? _error;

  @override
  void initState() {
    super.initState();
    _validateAndProceed();
  }

  Future<void> _validateAndProceed() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // 1. Ensure initial setup
      final setupComplete = await _firstTimeManager.ensureInitialSetup(widget.user);
      if (!setupComplete) {
        await _showCategorySetup();
      }

      // 2. Validate critical path
      final isValid = await _criticalPathValidator.validateCriticalPath(widget.user);
      if (!isValid) {
        // 3. Attempt recovery if validation fails
        final recoverySuccess = await _recoveryManager.attemptRecovery(widget.user);
        if (!recoverySuccess) {
          final errors = _criticalPathValidator.getValidationErrors(widget.user);
          setState(() {
            _error = errors.join('\n');
          });
          return;
        }
      }

      // 4. Initialize connections
      final connectionsInitialized = await _connectionManager.initializeConnections(widget.user);
      if (!connectionsInitialized) {
        setState(() {
          _error = 'Failed to initialize category connections. Please try again.';
        });
        return;
      }

      // 5. Complete flow
      widget.onComplete(widget.user);
    } catch (e) {
      setState(() {
        _error = 'An error occurred. Please try again.';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _showCategorySetup() async {
    final result = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) => CustomCategorySetupWidget(
        user: widget.user,
        onUserUpdated: (updatedUser) {
          Navigator.of(context).pop(true);
        },
        isInitialSetup: true,
      ),
    );

    if (result == true) {
      _validateAndProceed();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: _isLoading
            ? const CircularProgressIndicator()
            : _error != null
                ? Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        _error!,
                        style: const TextStyle(color: Colors.red),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _validateAndProceed,
                        child: const Text('Try Again'),
                      ),
                    ],
                  )
                : const CircularProgressIndicator(),
      ),
    );
  }
} 