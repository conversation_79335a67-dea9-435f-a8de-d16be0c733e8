import 'dart:async';
import '../models/user_model.dart';
import '../services/user_service.dart';
import '../controller/user_controller2.dart';
import 'custom_category_service.dart';
import 'custom_category_validator.dart';
import '../bulletproof/error_handler.dart';

class CustomCategoryPersistence {
  static final CustomCategoryPersistence _instance = CustomCategoryPersistence._internal();
  factory CustomCategoryPersistence() => _instance;

  late final ErrorHandler _errorHandler;
  late final UserService _userService;
  late final UserController2 _userController;
  late final CustomCategoryService _categoryService;
  
  // Cache for backup with timestamps
  final Map<String, Map<String, dynamic>> _categoryBackup = {};

  // Prevent duplicate saves
  final Set<String> _activeSaves = {};
  
  // Configuration
  static const int _maxRetries = 1;
  static const Duration _retryDelay = Duration(milliseconds: 500);
  static const Duration _backupExpiry = Duration(hours: 24);

  CustomCategoryPersistence._internal() {
    _errorHandler = ErrorHandler();
    _userService = UserService(_errorHandler);
    _userController = UserController2();
    _categoryService = CustomCategoryService();
  }

  /// Saves categories with multiple failsafe mechanisms
  Future<User?> saveCategories(User user, List<String> categories) async {
    if (categories.isEmpty) return null;

    // Prevent duplicate saves
    final saveKey = '${user.id}_${categories.join(',')}';
    if (_activeSaves.contains(saveKey)) {
      print('⚠️ Save already in progress for $saveKey, skipping duplicate');
      return null;
    }

    _activeSaves.add(saveKey);

    try {
      // Validate categories using the validator (ONLY validate against original user)
      CustomCategoryValidator.validateCategories(categories);
      CustomCategoryValidator.validateAgainstUserCategories(categories, user);

      print('✅ Initial validation passed for categories: $categories');

      // Create backup before making changes
      await _createBackup(user.id, user.customCategories);

      User? updatedUser;
      int retryCount = 0;

      while (retryCount < _maxRetries) {
        try {
          print('🔄 Starting save attempt ${retryCount + 1}/$_maxRetries');

          // 1. Update user model
          print('📝 Step 1: Updating user model...');
          updatedUser = _updateUserModel(user, categories);
          if (updatedUser == null) throw Exception('Failed to update user model');
          print('✅ Step 1: User model updated');

          // 2. Update UserController
          print('📝 Step 2: Updating UserController...');
          await _updateUserController(updatedUser);
          print('✅ Step 2: UserController updated');

          // 3. Update UserService
          print('📝 Step 3: Updating UserService...');
          await _updateUserService(updatedUser);
          print('✅ Step 3: UserService updated');

          // 4. Update CustomCategoryService
          print('📝 Step 4: Updating CustomCategoryService...');
          await _categoryService.updateCategories(updatedUser, categories);
          print('✅ Step 4: CustomCategoryService updated');

          // 5. Verify persistence
          print('📝 Step 5: Verifying persistence...');
          final verified = await _verifyPersistence(updatedUser);
          if (!verified) {
            throw Exception('Persistence verification failed');
          }
          print('✅ Step 5: Persistence verified');

          // 6. Success
          print('✅ Category save completed successfully - returning user');
          _categoryBackup.remove(user.id);
          _activeSaves.remove(saveKey);
          return updatedUser;
        } catch (e, stackTrace) {
          retryCount++;
          if (retryCount == _maxRetries) {
            // Restore from backup on final failure
            await _restoreFromBackup(user.id);
            await _errorHandler.handleError(e, stackTrace, context: 'saveCategories');
            _activeSaves.remove(saveKey);
            rethrow;
          }
          await Future.delayed(_retryDelay);
        }
      }

      return null;
    } catch (e, stackTrace) {
      _activeSaves.remove(saveKey);
      await _errorHandler.handleError(e, stackTrace, context: 'saveCategories');
      rethrow;
    }
  }

  /// Updates the user model with new categories
  User? _updateUserModel(User user, List<String> categories) {
    try {
      // Create new categories map
      final newCategories = Map<String, int>.from(user.categories);
      final newLastWeekExp = Map<String, int>.from(user.lastWeekExp);

      // Remove old custom categories
      for (final oldCat in user.customCategories) {
        newCategories.remove(oldCat);
        newLastWeekExp.remove(oldCat);
      }

      // Add new categories
      for (final category in categories) {
        newCategories[category] = 0;
        newLastWeekExp[category] = 0;
      }

      return user.copyWith(
        categories: newCategories,
        lastWeekExp: newLastWeekExp,
        customCategories: categories,
      );
    } catch (e, stackTrace) {
      _errorHandler.handleError(e, stackTrace, context: 'updateUserModel');
      return null;
    }
  }

  /// Updates the UserController
  Future<void> _updateUserController(User user) async {
    try {
      _userController.user = user;
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'updateUserController');
      throw Exception('Failed to update UserController: $e');
    }
  }

  /// Updates the UserService
  Future<void> _updateUserService(User user) async {
    try {
      await _userService.saveUser(user);
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'updateUserService');
      throw Exception('Failed to update UserService: $e');
    }
  }

  /// Verifies that categories were properly persisted
  Future<bool> _verifyPersistence(User user) async {
    try {
      print('🔍 Verifying persistence for user: ${user.username}');
      final persistedUser = await _userService.loadUserByUsername(user.username);

      if (persistedUser == null) {
        print('❌ Verification failed: No persisted user found');
        return false;
      }

      // Verify categories match
      final persistedCategories = persistedUser.customCategories;
      final expectedCategories = user.customCategories;

      print('🔍 Expected categories: $expectedCategories');
      print('🔍 Persisted categories: $persistedCategories');

      if (persistedCategories.length != expectedCategories.length) {
        print('❌ Verification failed: Category count mismatch');
        return false;
      }

      for (final category in expectedCategories) {
        if (!persistedCategories.contains(category)) {
          print('❌ Verification failed: Missing category $category');
          return false;
        }

        // Skip detailed verification for now - just check if categories exist
        print('✅ Category $category found in persisted data');
      }

      print('✅ Persistence verification passed');
      return true;
    } catch (e, stackTrace) {
      print('❌ Verification error: $e');
      await _errorHandler.handleError(e, stackTrace, context: 'verifyPersistence');
      return false;
    }
  }

  /// Creates a backup of current categories
  Future<void> _createBackup(String userId, List<String> categories) async {
    _categoryBackup[userId] = {
      'categories': List<String>.from(categories),
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };
  }

  /// Restores categories from backup
  Future<void> _restoreFromBackup(String userId) async {
    final backup = _categoryBackup[userId];
    if (backup == null) return;

    try {
      // Check if backup is expired
      final timestamp = backup['timestamp'] as int;
      if (DateTime.now().millisecondsSinceEpoch - timestamp > _backupExpiry.inMilliseconds) {
        _categoryBackup.remove(userId);
        return;
      }

      final categories = backup['categories'] as List<String>;
      final user = await _userService.loadUserByUsername(userId);
      if (user == null) return;

      final updatedUser = _updateUserModel(user, categories);
      if (updatedUser != null) {
        await _updateUserController(updatedUser);
        await _updateUserService(updatedUser);
        await _categoryService.updateCategories(updatedUser, categories);
      }
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'restoreFromBackup');
    }
  }

  /// Loads categories with verification
  Future<List<String>> loadCategories(String userId) async {
    try {
      final user = await _userService.loadUserByUsername(userId);
      
      if (user == null) return [];

      // Verify categories are valid
      try {
        CustomCategoryValidator.validateCategories(user.customCategories);
        return user.customCategories;
      } catch (e) {
        // Attempt to restore from backup if available
        await _restoreFromBackup(userId);
        final restoredUser = await _userService.loadUserByUsername(userId);
        return restoredUser?.customCategories ?? [];
      }
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'loadCategories');
      // Attempt to restore from backup on error
      await _restoreFromBackup(userId);
      return [];
    }
  }

  /// Cleans up expired backups
  void cleanupBackups() {
    final now = DateTime.now().millisecondsSinceEpoch;
    _categoryBackup.removeWhere((_, backup) {
      final timestamp = backup['timestamp'] as int;
      return now - timestamp > _backupExpiry.inMilliseconds;
    });
  }
} 