import '../models/user_model.dart';
import '../cstcat/custom_category_state_manager.dart';
import '../cstcat/custom_category_validator.dart';
import '../bulletproof/error_handler.dart';

class CategoryConnectionManager {
  static final CategoryConnectionManager _instance = CategoryConnectionManager._internal();
  factory CategoryConnectionManager() => _instance;
  
  final _stateManager = CustomCategoryStateManager();
  final _errorHandler = ErrorHandler();
  bool _isInitialized = false;

  CategoryConnectionManager._internal();

  /// Initializes the connection between categories and coaching screens
  Future<bool> initializeConnections(User user) async {
    if (_isInitialized) return true;

    try {
      _stateManager.beginTransaction();

      // 1. Create recovery point
      await _stateManager.createRecoveryPoint('init_${DateTime.now().millisecondsSinceEpoch}', user);

      // 2. Ensure categories are properly set up
      await _ensureCategorySetup(user);

      // 3. Initialize EXP tracking
      await _initializeExpTracking(user);

      // 4. Link to coaching screens
      await _linkToCoachingScreens(user);

      // 5. Verify connections
      final isValid = await _verifyConnections(user);
      if (!isValid) {
        await _stateManager.rollbackTransaction();
        return false;
      }

      await _stateManager.commitTransaction();
      _isInitialized = true;
      return true;
    } catch (e) {
      await _stateManager.rollbackTransaction();
      return false;
    }
  }

  /// Ensures categories are properly set up
  Future<void> _ensureCategorySetup(User user) async {
    try {
      // 1. Validate categories
      CustomCategoryValidator.validateUserCategorySetup(user);

      // 2. Ensure each category has EXP tracking
      final updatedCategories = Map<String, int>.from(user.categories);
      final updatedLastWeekExp = Map<String, int>.from(user.lastWeekExp);

      for (final category in user.customCategories) {
        if (!updatedCategories.containsKey(category)) {
          updatedCategories[category] = 0;
        }
        if (!updatedLastWeekExp.containsKey(category)) {
          updatedLastWeekExp[category] = 0;
        }
      }

      // 3. Update user with complete category setup
      await _stateManager.updateCategories(
        user,
        user.customCategories,
        createRecoveryPoint: false,
      );
    } catch (e) {
      await _errorHandler.handleError(e, StackTrace.current, context: 'ensureCategorySetup');
      rethrow;
    }
  }

  /// Initializes EXP tracking for all categories
  Future<void> _initializeExpTracking(User user) async {
    try {
      final updatedCategories = Map<String, int>.from(user.categories);
      final updatedLastWeekExp = Map<String, int>.from(user.lastWeekExp);

      // Ensure all categories have EXP tracking initialized
      for (final category in user.customCategories) {
        if (!updatedCategories.containsKey(category)) {
          updatedCategories[category] = 0;
        }
        if (!updatedLastWeekExp.containsKey(category)) {
          updatedLastWeekExp[category] = 0;
        }
      }

      await _stateManager.updateCategories(
        user,
        user.customCategories,
        createRecoveryPoint: false,
      );
    } catch (e) {
      await _errorHandler.handleError(e, StackTrace.current, context: 'initializeExpTracking');
      rethrow;
    }
  }

  /// Links categories to coaching screens
  Future<void> _linkToCoachingScreens(User user) async {
    try {
      // 1. Ensure coaching data is initialized in diary
      final diary = Map<String, dynamic>.from(user.diary);
      final coachingData = diary['coaching'] as Map<String, dynamic>? ?? {};
      
      // 2. Add category tracking to coaching data
      for (final category in user.customCategories) {
        if (!coachingData.containsKey(category)) {
          coachingData[category] = {
            'lastDiscussed': DateTime.now().toIso8601String(),
            'progress': 0,
            'goals': [],
          };
        }
      }

      // 3. Update user with coaching data in diary
      final updatedUser = user.copyWith(
        diary: {
          ...diary,
          'coaching': coachingData,
        },
      );

      await _stateManager.updateCategories(
        updatedUser,
        updatedUser.customCategories,
        createRecoveryPoint: false,
      );
    } catch (e) {
      await _errorHandler.handleError(e, StackTrace.current, context: 'linkToCoachingScreens');
      rethrow;
    }
  }

  /// Verifies all connections are properly established
  Future<bool> _verifyConnections(User user) async {
    try {
      // 1. Check category setup
      CustomCategoryValidator.validateUserCategorySetup(user);

      // 2. Verify EXP tracking
      for (final category in user.customCategories) {
        if (!user.categories.containsKey(category) || 
            !user.lastWeekExp.containsKey(category)) {
          return false;
        }
      }

      // 3. Verify coaching screen connections
      final diary = user.diary;
      final coachingData = diary['coaching'] as Map<String, dynamic>?;
      if (coachingData == null) return false;
      
      for (final category in user.customCategories) {
        if (!coachingData.containsKey(category)) {
          return false;
        }
      }

      return true;
    } catch (e) {
      await _errorHandler.handleError(e, StackTrace.current, context: 'verifyConnections');
      return false;
    }
  }

  /// Updates EXP for a category
  Future<bool> updateCategoryExp(User user, String category, int exp) async {
    try {
      _stateManager.beginTransaction();

      // 1. Validate category
      if (!user.customCategories.contains(category)) {
        await _stateManager.rollbackTransaction();
        return false;
      }

      // 2. Update EXP
      final updatedCategories = Map<String, int>.from(user.categories);
      updatedCategories[category] = (updatedCategories[category] ?? 0) + exp;

      // 3. Update last week EXP
      final updatedLastWeekExp = Map<String, int>.from(user.lastWeekExp);
      updatedLastWeekExp[category] = (updatedLastWeekExp[category] ?? 0) + exp;

      // 4. Save changes
      final updatedUser = user.copyWith(
        categories: updatedCategories,
        lastWeekExp: updatedLastWeekExp,
      );

      await _stateManager.updateCategories(
        updatedUser,
        updatedUser.customCategories,
        createRecoveryPoint: true,
      );

      await _stateManager.commitTransaction();
      return true;
    } catch (e) {
      await _stateManager.rollbackTransaction();
      await _errorHandler.handleError(e, StackTrace.current, context: 'updateCategoryExp');
      return false;
    }
  }

  /// Gets the current initialization status
  bool get isInitialized => _isInitialized;
} 