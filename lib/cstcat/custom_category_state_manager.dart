import 'dart:async';
import 'dart:math';
import '../models/user_model.dart';
import '../controller/user_controller2.dart';
import '../services/user_service.dart';
import '../cstcat/custom_category_validator.dart';
//import '../cstcat/custom_category_persistence.dart';
import '../bulletproof/error_handler.dart';

class CustomCategoryStateError implements Exception {
  final String message;
  final String code;
  final Map<String, dynamic>? details;

  CustomCategoryStateError(this.message, this.code, [this.details]);

  @override
  String toString() => 'CustomCategoryStateError: $message (Code: $code)';
}

class CustomCategoryStateManager {
  static final CustomCategoryStateManager _instance = CustomCategoryStateManager._internal();
  factory CustomCategoryStateManager() => _instance;
  
  final UserService _userService;
  final UserController2 _userController;
  final ErrorHandler _errorHandler;
  final _stateController = StreamController<Map<String, dynamic>>.broadcast();
  final _recoveryPoints = <String, Map<String, dynamic>>{};
  final _transactionStack = <Map<String, dynamic>>[];
  bool _isInTransaction = false;
  bool _isDisposed = false;

  CustomCategoryStateManager._internal()
      : _userService = UserService(ErrorHandler()),
        _userController = UserController2(),
        _errorHandler = ErrorHandler();

  // Getters
  Stream<Map<String, dynamic>> get stateStream => _stateController.stream;
  bool get isInTransaction => _isInTransaction;

  /// Starts a new transaction
  void beginTransaction() {
    if (_isDisposed) {
      throw CustomCategoryStateError(
        'State manager has been disposed',
        'DISPOSED',
      );
    }
    if (_isInTransaction) {
      throw CustomCategoryStateError(
        'Transaction already in progress',
        'TRANSACTION_IN_PROGRESS',
      );
    }
    _isInTransaction = true;
    _transactionStack.add({});
  }

  /// Commits the current transaction
  Future<void> commitTransaction() async {
    if (_isDisposed) {
      throw CustomCategoryStateError(
        'State manager has been disposed',
        'DISPOSED',
      );
    }
    if (!_isInTransaction) {
      throw CustomCategoryStateError(
        'No transaction in progress',
        'NO_TRANSACTION',
      );
    }

    try {
      final changes = _transactionStack.last;
      await _applyChanges(changes);
      _transactionStack.removeLast();
      _isInTransaction = _transactionStack.isNotEmpty;
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'commitTransaction');
      await _rollbackTransaction();
      rethrow;
    }
  }

  /// Rolls back the current transaction
  Future<void> rollbackTransaction() async {
    if (_isDisposed) {
      throw CustomCategoryStateError(
        'State manager has been disposed',
        'DISPOSED',
      );
    }
    if (!_isInTransaction) {
      throw CustomCategoryStateError(
        'No transaction in progress',
        'NO_TRANSACTION',
      );
    }

    await _rollbackTransaction();
  }

  /// Creates a recovery point
  Future<void> createRecoveryPoint(String pointId, User user) async {
    if (_isDisposed) {
      throw CustomCategoryStateError(
        'State manager has been disposed',
        'DISPOSED',
      );
    }
    _recoveryPoints[pointId] = {
      'categories': Map<String, int>.from(user.categories),
      'lastWeekExp': Map<String, int>.from(user.lastWeekExp),
      'customCategories': List<String>.from(user.customCategories),
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  /// Recovers from a recovery point
  Future<User> recoverFromPoint(String pointId, User user) async {
    if (_isDisposed) {
      throw CustomCategoryStateError(
        'State manager has been disposed',
        'DISPOSED',
      );
    }
    final point = _recoveryPoints[pointId];
    if (point == null) {
      throw CustomCategoryStateError(
        'Recovery point not found',
        'POINT_NOT_FOUND',
        {'pointId': pointId},
      );
    }

    try {
      final recoveredUser = user.copyWith(
        categories: Map<String, int>.from(point['categories']),
        lastWeekExp: Map<String, int>.from(point['lastWeekExp']),
        customCategories: List<String>.from(point['customCategories']),
      );

      await _validateAndSaveState(recoveredUser);
      return recoveredUser;
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'recoverFromPoint');
      rethrow;
    }
  }

  /// Updates categories with atomic operation
  Future<User> updateCategories(
    User user,
    List<String> newCategories, {
    bool createRecoveryPoint = true,
  }) async {
    if (_isDisposed) {
      throw CustomCategoryStateError(
        'State manager has been disposed',
        'DISPOSED',
      );
    }
    try {
      // Validate new categories
      CustomCategoryValidator.validateCategories(newCategories);
      CustomCategoryValidator.validateAgainstUserCategories(newCategories, user);

      if (createRecoveryPoint) {
        await this.createRecoveryPoint('pre_update_${DateTime.now().millisecondsSinceEpoch}', user);
      }

      // Create new maps
      final newCategoriesMap = Map<String, int>.from(user.categories);
      final newLastWeekExpMap = Map<String, int>.from(user.lastWeekExp);

      // Remove old custom categories
      for (final oldCat in user.customCategories) {
        newCategoriesMap.remove(oldCat);
        newLastWeekExpMap.remove(oldCat);
      }

      // Add new categories
      for (final category in newCategories) {
        newCategoriesMap[category] = 0;
        newLastWeekExpMap[category] = 0;
      }

      // Update assignedCoaches for custom categories
      Map<String, String>? updatedAssignedCoaches = user.assignedCoaches != null
          ? Map<String, String>.from(user.assignedCoaches!)
          : null;

      // Remove old custom category assignments
      if (updatedAssignedCoaches != null) {
        updatedAssignedCoaches.removeWhere((key, value) => key.startsWith('Custom Category'));
      }

      // Add new custom category assignments
      for (int i = 0; i < newCategories.length; i++) {
        final categoryKey = 'Custom Category ${i + 1}';

        if (user.gender.toLowerCase() == 'non-gender') {
          updatedAssignedCoaches ??= {};
          // For non-gender users, preserve existing assignments or create new random ones
          if (!updatedAssignedCoaches.containsKey(categoryKey)) {
            final random = Random();
            updatedAssignedCoaches[categoryKey] = random.nextBool() ? 'Male' : 'Female';
          }
        } else {
          // For gendered users, ensure assignedCoaches exists for consistency
          updatedAssignedCoaches ??= {};
          updatedAssignedCoaches[categoryKey] = user.gender;
        }
      }

      // Create updated user
      final updatedUser = user.copyWith(
        categories: newCategoriesMap,
        lastWeekExp: newLastWeekExpMap,
        customCategories: newCategories,
        assignedCoaches: updatedAssignedCoaches,
      );

      // Validate and save
      await _validateAndSaveState(updatedUser);
      return updatedUser;
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'updateCategories');
      if (createRecoveryPoint) {
        await _attemptRecovery(user);
      }
      rethrow;
    }
  }

  /// Adds a single category
  Future<User> addCategory(
    User user,
    String category, {
    bool createRecoveryPoint = true,
  }) async {
    if (_isDisposed) {
      throw CustomCategoryStateError(
        'State manager has been disposed',
        'DISPOSED',
      );
    }
    try {
      // Validate category
      CustomCategoryValidator.validateCategoryName(category);
      CustomCategoryValidator.validateAgainstUserCategories([category], user);

      if (createRecoveryPoint) {
        await this.createRecoveryPoint('pre_add_${DateTime.now().millisecondsSinceEpoch}', user);
      }

      // Create new maps
      final newCategoriesMap = Map<String, int>.from(user.categories);
      final newLastWeekExpMap = Map<String, int>.from(user.lastWeekExp);

      // Add category
      newCategoriesMap[category] = 0;
      newLastWeekExpMap[category] = 0;

      // Update assignedCoaches for the new custom category
      final updatedCustomCategories = [...user.customCategories, category];
      Map<String, String>? updatedAssignedCoaches = user.assignedCoaches != null
          ? Map<String, String>.from(user.assignedCoaches!)
          : null;

      // Determine the custom category index (1 or 2)
      final customCategoryIndex = updatedCustomCategories.length;
      final categoryKey = 'Custom Category $customCategoryIndex';

      // For non-gender users, assign random coach; for gendered users, ensure proper assignment
      if (user.gender.toLowerCase() == 'non-gender') {
        updatedAssignedCoaches ??= {};
        final random = Random();
        updatedAssignedCoaches[categoryKey] = random.nextBool() ? 'Male' : 'Female';
      } else {
        // For gendered users, ensure assignedCoaches exists for consistency
        updatedAssignedCoaches ??= {};
        updatedAssignedCoaches[categoryKey] = user.gender;
      }

      // Create updated user
      final updatedUser = user.copyWith(
        categories: newCategoriesMap,
        lastWeekExp: newLastWeekExpMap,
        customCategories: updatedCustomCategories,
        assignedCoaches: updatedAssignedCoaches,
      );

      // Validate and save
      await _validateAndSaveState(updatedUser);
      return updatedUser;
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'addCategory');
      if (createRecoveryPoint) {
        await _attemptRecovery(user);
      }
      rethrow;
    }
  }

  /// Removes a single category
  Future<User> removeCategory(
    User user,
    String category, {
    bool createRecoveryPoint = true,
  }) async {
    if (_isDisposed) {
      throw CustomCategoryStateError(
        'State manager has been disposed',
        'DISPOSED',
      );
    }
    try {
      if (!user.customCategories.contains(category)) {
        throw CustomCategoryStateError(
          'Category not found',
          'CATEGORY_NOT_FOUND',
          {'category': category},
        );
      }

      if (createRecoveryPoint) {
        await this.createRecoveryPoint('pre_remove_${DateTime.now().millisecondsSinceEpoch}', user);
      }

      // Create new maps
      final newCategoriesMap = Map<String, int>.from(user.categories);
      final newLastWeekExpMap = Map<String, int>.from(user.lastWeekExp);

      // Remove category
      newCategoriesMap.remove(category);
      newLastWeekExpMap.remove(category);

      // Update assignedCoaches to remove custom category assignments and reindex
      final updatedCustomCategories = user.customCategories.where((c) => c != category).toList();
      Map<String, String>? updatedAssignedCoaches = user.assignedCoaches != null
          ? Map<String, String>.from(user.assignedCoaches!)
          : null;

      if (updatedAssignedCoaches != null) {
        // Remove all custom category assignments
        updatedAssignedCoaches.removeWhere((key, value) => key.startsWith('Custom Category'));

        // Re-add assignments for remaining custom categories with correct indices
        for (int i = 0; i < updatedCustomCategories.length; i++) {
          final categoryKey = 'Custom Category ${i + 1}';

          if (user.gender.toLowerCase() == 'non-gender') {
            final random = Random();
            updatedAssignedCoaches[categoryKey] = random.nextBool() ? 'Male' : 'Female';
          } else {
            updatedAssignedCoaches[categoryKey] = user.gender;
          }
        }
      }

      // Create updated user
      final updatedUser = user.copyWith(
        categories: newCategoriesMap,
        lastWeekExp: newLastWeekExpMap,
        customCategories: updatedCustomCategories,
        assignedCoaches: updatedAssignedCoaches,
      );

      // Validate and save
      await _validateAndSaveState(updatedUser);
      return updatedUser;
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'removeCategory');
      if (createRecoveryPoint) {
        await _attemptRecovery(user);
      }
      rethrow;
    }
  }

  /// Disposes the state manager
  Future<void> dispose() async {
    if (_isDisposed) return;

    _isDisposed = true;
    _isInTransaction = false;
    _transactionStack.clear();
    _recoveryPoints.clear();

    try {
      await _stateController.close();
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'dispose');
    }
  }

  /// Applies changes from a transaction
  Future<void> _applyChanges(Map<String, dynamic> changes) async {
    try {
      if (changes.containsKey('user')) {
        final user = changes['user'] as User;
        await _validateAndSaveState(user);
      }
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: '_applyChanges');
      rethrow;
    }
  }

  /// Rolls back the current transaction
  Future<void> _rollbackTransaction() async {
    if (_transactionStack.isEmpty) return;

    try {
      _transactionStack.removeLast();
      _isInTransaction = _transactionStack.isNotEmpty;
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: '_rollbackTransaction');
      rethrow;
    }
  }

  /// Attempts to recover from an error
  Future<void> _attemptRecovery(User user) async {
    try {
      // Find the most recent recovery point
      final points = _recoveryPoints.entries.toList()
        ..sort((a, b) => (b.value['timestamp'] as String)
            .compareTo(a.value['timestamp'] as String));

      if (points.isNotEmpty) {
        final pointId = points.first.key;
        await recoverFromPoint(pointId, user);
      }
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: '_attemptRecovery');
      // Don't rethrow - this is a recovery attempt
    }
  }

  /// Validates and saves the user state
  Future<void> _validateAndSaveState(User user) async {
    try {
      // Validate user data
      CustomCategoryValidator.validateCategories(user.customCategories);
      CustomCategoryValidator.validateAgainstUserCategories(user.customCategories, user);

      // Save to persistence
      await _userService.saveUser(user);
      _userController.user = user;

      // Broadcast state change
      _stateController.add({
        'categories': user.categories,
        'lastWeekExp': user.lastWeekExp,
        'customCategories': user.customCategories,
      });
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: '_validateAndSaveState');
      rethrow;
    }
  }
} 