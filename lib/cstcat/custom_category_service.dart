import 'dart:math';
import '../models/user_model.dart';
import '../services/user_service.dart';
import 'custom_category_validator.dart';
import '../bulletproof/error_handler.dart';

class CustomCategoryService {
  final UserService _userService;

  CustomCategoryService()
      : _userService = UserService(ErrorHandler());

  /// Add a custom category and persist the user.
  Future<User> addCustomCategory(User user, String category) async {
    try {
      // Validate category name
      CustomCategoryValidator.validateCategoryName(category);
      
      // Validate against existing categories
      CustomCategoryValidator.validateAgainstUserCategories([category], user);
      
      if (user.customCategories.length >= 2) {
        throw Exception('You can only have 2 custom categories.');
      }
      if (user.categories.containsKey(category)) {
        throw Exception('Category already exists.');
      }
      if (category.trim().isEmpty) {
        throw Exception('Category name cannot be empty.');
      }

      // Update both categories and lastWeekExp maps
      final updatedCategories = {...user.categories, category: 0};
      final updatedLastWeekExp = {...user.lastWeekExp, category: 0};
      final updatedCustomCategories = [...user.customCategories, category];

      // Update assignedCoaches for custom categories to ensure proper coach assignment
      Map<String, String>? updatedAssignedCoaches = user.assignedCoaches != null
          ? Map<String, String>.from(user.assignedCoaches!)
          : null;

      // Determine the custom category index (1 or 2)
      final customCategoryIndex = updatedCustomCategories.length;
      final categoryKey = 'Custom Category $customCategoryIndex';

      // For non-gender users, assign random coach; for gendered users, ensure proper assignment
      if (user.gender.toLowerCase() == 'non-gender') {
        updatedAssignedCoaches ??= {};
        final random = Random();
        updatedAssignedCoaches[categoryKey] = random.nextBool() ? 'Male' : 'Female';
      } else {
        // For gendered users, ensure assignedCoaches exists for consistency
        // This helps with coach assignment logic across all services
        updatedAssignedCoaches ??= {};
        updatedAssignedCoaches[categoryKey] = user.gender;
      }

      final updated = user.copyWith(
        categories: updatedCategories,
        lastWeekExp: updatedLastWeekExp,
        customCategories: updatedCustomCategories,
        assignedCoaches: updatedAssignedCoaches,
      );

      await _userService.saveUser(updated);
      final latest = await _userService.loadUserByUsername(user.username);
      if (latest == null) {
        throw Exception('Failed to verify category addition. Please try again.');
      }
      return latest;
    } catch (e) {
      throw Exception('Failed to add custom category: $e');
    }
  }

  /// Remove a custom category and persist the user.
  Future<User> removeCustomCategory(User user, String category) async {
    try {
      if (!user.customCategories.contains(category)) {
        throw Exception('Category does not exist.');
      }

      // Update both categories and lastWeekExp maps
      final updatedCategories = Map<String, int>.from(user.categories)..remove(category);
      final updatedLastWeekExp = Map<String, int>.from(user.lastWeekExp)..remove(category);
      final updatedCustomCategories = List<String>.from(user.customCategories)..remove(category);

      // Update assignedCoaches to remove custom category assignments and reindex
      Map<String, String>? updatedAssignedCoaches = user.assignedCoaches != null
          ? Map<String, String>.from(user.assignedCoaches!)
          : null;

      if (updatedAssignedCoaches != null) {
        // Remove all custom category assignments
        updatedAssignedCoaches.removeWhere((key, value) => key.startsWith('Custom Category'));

        // Re-add assignments for remaining custom categories with correct indices
        for (int i = 0; i < updatedCustomCategories.length; i++) {
          final categoryKey = 'Custom Category ${i + 1}';

          if (user.gender.toLowerCase() == 'non-gender') {
            final random = Random();
            updatedAssignedCoaches[categoryKey] = random.nextBool() ? 'Male' : 'Female';
          } else {
            updatedAssignedCoaches[categoryKey] = user.gender;
          }
        }
      }

      final updated = user.copyWith(
        categories: updatedCategories,
        lastWeekExp: updatedLastWeekExp,
        customCategories: updatedCustomCategories,
        assignedCoaches: updatedAssignedCoaches,
      );

      await _userService.saveUser(updated);
      final latest = await _userService.loadUserByUsername(user.username);
      if (latest == null) {
        throw Exception('Failed to verify category removal. Please try again.');
      }
      return latest;
    } catch (e) {
      throw Exception('Failed to remove custom category: $e');
    }
  }

  /// Load the latest user from storage.
  Future<User?> loadUser(String username) async {
    try {
      final user = await _userService.loadUserByUsername(username);
      if (user == null) {
        throw Exception('User not found');
      }
      return user;
    } catch (e) {
      throw Exception('Failed to load user data: $e');
    }
  }

  /// Update categories for a user
  Future<User> updateCategories(User user, List<String> categories) async {
    try {
      // Skip validation here - it should be done before calling this method
      print('🔄 CustomCategoryService: Updating categories without validation (already validated)');

      // Create new maps
      final newCategories = Map<String, int>.from(user.categories);
      final newLastWeekExp = Map<String, int>.from(user.lastWeekExp);

      // Remove old custom categories
      for (final oldCat in user.customCategories) {
        newCategories.remove(oldCat);
        newLastWeekExp.remove(oldCat);
      }

      // Add new categories
      for (final category in categories) {
        newCategories[category] = 0;
        newLastWeekExp[category] = 0;
      }

      // Update assignedCoaches for custom categories
      Map<String, String>? updatedAssignedCoaches = user.assignedCoaches != null
          ? Map<String, String>.from(user.assignedCoaches!)
          : null;

      // Remove old custom category assignments
      if (updatedAssignedCoaches != null) {
        updatedAssignedCoaches.removeWhere((key, value) => key.startsWith('Custom Category'));
      }

      // Add new custom category assignments
      for (int i = 0; i < categories.length; i++) {
        final categoryKey = 'Custom Category ${i + 1}';

        if (user.gender.toLowerCase() == 'non-gender') {
          updatedAssignedCoaches ??= {};
          final random = Random();
          updatedAssignedCoaches[categoryKey] = random.nextBool() ? 'Male' : 'Female';
        } else {
          // For gendered users, ensure assignedCoaches exists for consistency
          updatedAssignedCoaches ??= {};
          updatedAssignedCoaches[categoryKey] = user.gender;
        }
      }

      final updated = user.copyWith(
        categories: newCategories,
        lastWeekExp: newLastWeekExp,
        customCategories: categories,
        assignedCoaches: updatedAssignedCoaches,
      );

      await _userService.saveUser(updated);
      final latest = await _userService.loadUserByUsername(user.username);
      if (latest == null) {
        throw Exception('Failed to verify category update. Please try again.');
      }
      return latest;
    } catch (e) {
      throw Exception('Failed to update categories: $e');
    }
  }
} 