import 'dart:async';
import '../models/user_model.dart';
import '../controller/user_controller2.dart';
import '../services/user_service.dart';
import './custom_category_validator.dart';
import './custom_category_state_manager.dart';
import './custom_category_persistence.dart';
import '../bulletproof/error_handler.dart';

class CustomCategoryIntegrityError implements Exception {
  final String message;
  final String code;
  final Map<String, dynamic>? details;

  CustomCategoryIntegrityError(this.message, this.code, [this.details]);

  @override
  String toString() => 'CustomCategoryIntegrityError: $message (Code: $code)';
}

class CustomCategoryIntegrityChecker {
  static final CustomCategoryIntegrityChecker _instance = CustomCategoryIntegrityChecker._internal();
  factory CustomCategoryIntegrityChecker() => _instance;
  
  late final ErrorHandler _errorHandler;
  late final UserService _userService;
  final _stateManager = CustomCategoryStateManager();
  final _repairAttempts = <String, int>{};
  final _lastCheckResults = <String, Map<String, dynamic>>{};
  Timer? _periodicCheckTimer;

  // Configuration
  static const Duration _checkInterval = Duration(minutes: 5);
  static const int _maxRepairAttempts = 3;
  static const Duration _repairDelay = Duration(seconds: 1);

  CustomCategoryIntegrityChecker._internal() {
    _errorHandler = ErrorHandler();
    _userService = UserService(_errorHandler);
  }

  /// Starts periodic integrity checks
  void startPeriodicChecks() {
    _periodicCheckTimer?.cancel();
    _periodicCheckTimer = Timer.periodic(_checkInterval, (_) {
      _performIntegrityCheck();
    });
  }

  /// Stops periodic integrity checks
  void stopPeriodicChecks() {
    _periodicCheckTimer?.cancel();
    _periodicCheckTimer = null;
  }

  /// Performs a manual integrity check
  Future<Map<String, dynamic>> performManualCheck() async {
    return await _performIntegrityCheck();
  }

  /// Performs the integrity check
  Future<Map<String, dynamic>> _performIntegrityCheck() async {
    final results = <String, dynamic>{
      'timestamp': DateTime.now().toIso8601String(),
      'checks': <String, bool>{},
      'errors': <String, dynamic>{},
      'repairs': <String, dynamic>{},
    };

    try {
      // Get current user
      final userController = UserController2();
      final user = userController.user;
      
      if (user == null) {
        results['errors']['general'] = 'No user found';
        return results;
      }

      _stateManager.beginTransaction();

      // Check 1: Validate category setup
      try {
        CustomCategoryValidator.validateUserCategorySetup(user);
        results['checks']['category_setup'] = true;
      } catch (e) {
        results['checks']['category_setup'] = false;
        results['errors']['category_setup'] = e.toString();
        await _attemptRepair('category_setup', user);
      }

      // Check 2: Verify persistence
      try {
        final persistedUser = await _userService.loadUserByUsername(user.username);
        if (persistedUser == null) {
          throw Exception('User not found in persistence');
        }
        if (!_compareCategoryMaps(user.categories, persistedUser.categories) ||
            !_compareCategoryMaps(user.lastWeekExp, persistedUser.lastWeekExp)) {
          throw Exception('Category maps mismatch');
        }
        results['checks']['persistence'] = true;
      } catch (e) {
        results['checks']['persistence'] = false;
        results['errors']['persistence'] = e.toString();
        await _attemptRepair('persistence', user);
      }

      // Check 3: Verify state consistency
      try {
        final currentState = await _getCurrentState(user);
        if (!_verifyStateConsistency(currentState)) {
          throw Exception('State inconsistency detected');
        }
        results['checks']['state_consistency'] = true;
      } catch (e) {
        results['checks']['state_consistency'] = false;
        results['errors']['state_consistency'] = e.toString();
        await _attemptRepair('state_consistency', user);
      }

      // Check 4: Verify category maps
      try {
        if (!_verifyCategoryMaps(user)) {
          throw Exception('Category maps verification failed');
        }
        results['checks']['category_maps'] = true;
      } catch (e) {
        results['checks']['category_maps'] = false;
        results['errors']['category_maps'] = e.toString();
        await _attemptRepair('category_maps', user);
      }

      await _stateManager.commitTransaction();

      // Store results
      _lastCheckResults[DateTime.now().toIso8601String()] = results;

      return results;
    } catch (e, stackTrace) {
      await _stateManager.rollbackTransaction();
      await _errorHandler.handleError(e, stackTrace, context: 'performIntegrityCheck');
      results['errors']['general'] = e.toString();
      return results;
    }
  }

  /// Attempts to repair a specific issue
  Future<void> _attemptRepair(String issue, User user) async {
    final attemptKey = '${user.id}_$issue';
    final attempts = _repairAttempts[attemptKey] ?? 0;

    if (attempts >= _maxRepairAttempts) {
      throw CustomCategoryIntegrityError(
        'Max repair attempts reached for $issue',
        'MAX_REPAIR_ATTEMPTS',
        {'issue': issue, 'attempts': attempts},
      );
    }

    _repairAttempts[attemptKey] = attempts + 1;

    try {
      switch (issue) {
        case 'category_setup':
          await _repairCategorySetup(user);
          break;
        case 'persistence':
          await _repairPersistence(user);
          break;
        case 'state_consistency':
          await _repairStateConsistency(user);
          break;
        case 'category_maps':
          await _repairCategoryMaps(user);
          break;
        default:
          throw CustomCategoryIntegrityError(
            'Unknown repair issue',
            'UNKNOWN_REPAIR_ISSUE',
            {'issue': issue},
          );
      }

      await Future.delayed(_repairDelay);
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'attemptRepair');
      rethrow;
    }
  }

  /// Repairs category setup issues
  Future<void> _repairCategorySetup(User user) async {
    try {
      // Create backup
      await _stateManager.createRecoveryPoint('pre_repair_${DateTime.now().millisecondsSinceEpoch}', user);

      // Validate and fix categories
      final validCategories = user.customCategories.where((cat) {
        try {
          CustomCategoryValidator.validateCategoryName(cat);
          return true;
        } catch (_) {
          return false;
        }
      }).toList();

      // Update user with valid categories
      await _stateManager.updateCategories(user, validCategories, createRecoveryPoint: false);
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'repairCategorySetup');
      rethrow;
    }
  }

  /// Repairs persistence issues
  Future<void> _repairPersistence(User user) async {
    try {
      // Create backup
      await _stateManager.createRecoveryPoint('pre_persistence_repair_${DateTime.now().millisecondsSinceEpoch}', user);

      // Force save to persistence
      await CustomCategoryPersistence().saveCategories(user, user.customCategories);
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'repairPersistence');
      rethrow;
    }
  }

  /// Repairs state consistency issues
  Future<void> _repairStateConsistency(User user) async {
    try {
      // Create backup
      await _stateManager.createRecoveryPoint('pre_state_repair_${DateTime.now().millisecondsSinceEpoch}', user);

      // Force state update
      await _stateManager.updateCategories(user, user.customCategories, createRecoveryPoint: false);
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'repairStateConsistency');
      rethrow;
    }
  }

  /// Repairs category maps issues
  Future<void> _repairCategoryMaps(User user) async {
    try {
      // Create backup
      await _stateManager.createRecoveryPoint('pre_maps_repair_${DateTime.now().millisecondsSinceEpoch}', user);

      // Fix category maps
      final newCategories = Map<String, int>.from(user.categories);
      final newLastWeekExp = Map<String, int>.from(user.lastWeekExp);

      // Remove invalid categories
      newCategories.removeWhere((key, _) => !user.customCategories.contains(key));
      newLastWeekExp.removeWhere((key, _) => !user.customCategories.contains(key));

      // Add missing categories
      for (final category in user.customCategories) {
        newCategories.putIfAbsent(category, () => 0);
        newLastWeekExp.putIfAbsent(category, () => 0);
      }

      // Update user
      final updatedUser = user.copyWith(
        categories: newCategories,
        lastWeekExp: newLastWeekExp,
      );

      await _stateManager.updateCategories(updatedUser, user.customCategories, createRecoveryPoint: false);
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'repairCategoryMaps');
      rethrow;
    }
  }

  /// Gets the current state of the user's categories
  Future<Map<String, dynamic>> _getCurrentState(User user) async {
    try {
      final persistedUser = await _userService.loadUserByUsername(user.username);
      if (persistedUser == null) {
        throw Exception('User not found in persistence');
      }

      return {
        'categories': user.categories,
        'lastWeekExp': user.lastWeekExp,
        'persistedCategories': persistedUser.categories,
        'persistedLastWeekExp': persistedUser.lastWeekExp,
      };
    } catch (e, stackTrace) {
      await _errorHandler.handleError(e, stackTrace, context: 'getCurrentState');
      rethrow;
    }
  }

  /// Verifies state consistency
  bool _verifyStateConsistency(Map<String, dynamic> state) {
    try {
      final categories = state['categories'] as Map<String, int>;
      final lastWeekExp = state['lastWeekExp'] as Map<String, int>;
      final persistedCategories = state['persistedCategories'] as Map<String, int>;
      final persistedLastWeekExp = state['persistedLastWeekExp'] as Map<String, int>;

      return _compareCategoryMaps(categories, persistedCategories) &&
          _compareCategoryMaps(lastWeekExp, persistedLastWeekExp);
    } catch (e) {
      return false;
    }
  }

  /// Verifies category maps
  bool _verifyCategoryMaps(User user) {
    try {
      // Check if all custom categories have entries in both maps
      for (final category in user.customCategories) {
        if (!user.categories.containsKey(category) || !user.lastWeekExp.containsKey(category)) {
          return false;
        }
      }

      // Check if there are no extra categories in the maps
      for (final category in user.categories.keys) {
        if (!user.customCategories.contains(category)) {
          return false;
        }
      }

      for (final category in user.lastWeekExp.keys) {
        if (!user.customCategories.contains(category)) {
          return false;
        }
      }

      return true;
    } catch (e) {
      return false;
    }
  }

  /// Compares two category maps
  bool _compareCategoryMaps(Map<String, int> map1, Map<String, int> map2) {
    if (map1.length != map2.length) return false;

    for (final key in map1.keys) {
      if (!map2.containsKey(key) || map1[key] != map2[key]) {
        return false;
      }
    }

    return true;
  }

  /// Gets the last check results
  Map<String, Map<String, dynamic>> get lastCheckResults => Map.unmodifiable(_lastCheckResults);
} 