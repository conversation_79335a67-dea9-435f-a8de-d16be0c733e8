import '../models/user_model.dart';
import './custom_category_state_manager.dart';
import './custom_category_validator.dart';
import '../services/user_service.dart';
import '../bulletproof/error_handler.dart';

class FirstTimeRecoveryManager {
  static final FirstTimeRecoveryManager _instance = FirstTimeRecoveryManager._internal();
  factory FirstTimeRecoveryManager() => _instance;
  FirstTimeRecoveryManager._internal();

  final _stateManager = CustomCategoryStateManager();
  bool _isRecovering = false;

  /// Attempts to recover from a failed first-time setup
  Future<bool> attemptRecovery(User user) async {
    if (_isRecovering) return false;
    _isRecovering = true;

    try {
      // 1. Create recovery point
      await _stateManager.createRecoveryPoint('recovery_${DateTime.now().millisecondsSinceEpoch}', user);

      // 2. Validate and clean categories
      final cleanedCategories = _cleanCategories(user.customCategories);
      
      // 3. Update user with cleaned categories
      await _stateManager.updateCategories(user, cleanedCategories);

      // 4. Verify recovery
      final isValid = await _verifyRecovery(user);
      if (!isValid) {
        await _stateManager.rollbackTransaction();
        return false;
      }

      return true;
    } catch (e) {
      await _stateManager.rollbackTransaction();
      return false;
    } finally {
      _isRecovering = false;
    }
  }

  /// Cleans and validates categories
  List<String> _cleanCategories(List<String> categories) {
    return categories.where((category) {
      try {
        CustomCategoryValidator.validateCategoryName(category);
        return true;
      } catch (_) {
        return false;
      }
    }).toList();
  }

  /// Verifies that recovery was successful
  Future<bool> _verifyRecovery(User user) async {
    try {
      // 1. Check if categories are valid
      CustomCategoryValidator.validateUserCategorySetup(user);

      // 2. Check if user has at least one category
      if (user.customCategories.isEmpty) {
        return false;
      }

      // 3. Check if categories are properly saved
      final userService = UserService(ErrorHandler());
      final persistedUser = await userService.loadUserByUsername(user.username);
      if (persistedUser == null || persistedUser.customCategories.length != user.customCategories.length) {
        return false;
      }

      return true;
    } catch (e) {
      return false;
    }
  }

  /// Gets the current recovery status
  bool get isRecovering => _isRecovering;
} 