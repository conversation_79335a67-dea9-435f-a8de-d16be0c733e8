import '../models/user_model.dart';
import './custom_category_validator.dart';
import './custom_category_state_manager.dart';
import './custom_category_integrity_checker.dart';

class FirstTimeUserManager {
  static final FirstTimeUserManager _instance = FirstTimeUserManager._internal();
  factory FirstTimeUserManager() => _instance;
  FirstTimeUserManager._internal();

  final _stateManager = CustomCategoryStateManager();
  final _integrityChecker = CustomCategoryIntegrityChecker();
  bool _hasCompletedSetup = false;

  /// Ensures the user has completed the initial setup
  Future<bool> ensureInitialSetup(User user) async {
    if (_hasCompletedSetup) return true;

    try {
      // 1. Validate current state
      CustomCategoryValidator.validateUserCategorySetup(user);

      // 2. Check integrity
      final integrityResults = await _integrityChecker.performManualCheck();
      if (!_isSetupComplete(integrityResults)) {
        // 3. Attempt repair if needed
        await _repairSetup(user);
        
        // 4. Verify repair
        final verifyResults = await _integrityChecker.performManualCheck();
        if (!_isSetupComplete(verifyResults)) {
          return false;
        }
      }

      _hasCompletedSetup = true;
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Checks if the setup is complete based on integrity results
  bool _isSetupComplete(Map<String, dynamic> results) {
    final checks = results['checks'] as Map<String, bool>;
    return checks.values.every((value) => value == true);
  }

  /// Attempts to repair the setup
  Future<void> _repairSetup(User user) async {
    // 1. Create recovery point
    await _stateManager.createRecoveryPoint('pre_repair_${DateTime.now().millisecondsSinceEpoch}', user);

    try {
      // 2. Ensure categories are valid
      final validCategories = user.customCategories.where((cat) {
        try {
          CustomCategoryValidator.validateCategoryName(cat);
          return true;
        } catch (_) {
          return false;
        }
      }).toList();

      // 3. Update categories if needed
      if (validCategories.length != user.customCategories.length) {
        await _stateManager.updateCategories(user, validCategories);
      }

      // 4. Force integrity check
      await _integrityChecker.performManualCheck();
    } catch (e) {
      // 5. Rollback on failure
      await _stateManager.rollbackTransaction();
      rethrow;
    }
  }

  /// Resets the setup completion status
  void resetSetupStatus() {
    _hasCompletedSetup = false;
  }

  /// Gets the current setup status
  bool get isSetupComplete => _hasCompletedSetup;
} 