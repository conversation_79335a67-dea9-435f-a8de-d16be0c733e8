import '../models/user_model.dart';

class CustomCategoryValidationError implements Exception {
  final String message;
  final String code;
  final Map<String, dynamic>? details;

  CustomCategoryValidationError(this.message, this.code, [this.details]);

  @override
  String toString() => 'CustomCategoryValidationError: $message (Code: $code)';
}

class CustomCategoryValidator {
  static const int _maxCategoryLength = 20;
  static const int _maxCustomCategories = 2;
  static const List<String> _builtInCategories = [
    'Health',
    'Wealth',
    'Purpose',
    'Connection',
  ];

  // Validation error codes
  static const String _errorEmpty = 'EMPTY_CATEGORY';
  static const String _errorTooLong = 'CATEGORY_TOO_LONG';
  static const String _errorInvalidChars = 'INVALID_CHARACTERS';
  static const String _errorDuplicate = 'DUPLICATE_CATEGORY';
  static const String _errorBuiltIn = 'BUILT_IN_CATEGORY';
  static const String _errorTooMany = 'TOO_MANY_CATEGORIES';
  static const String _errorWhitespace = 'WHITESPACE_ONLY';

  /// Validates a single category name
  static void validateCategoryName(String category) {
    if (category.isEmpty) {
      throw CustomCategoryValidationError(
        'Category name cannot be empty',
        _errorEmpty,
      );
    }

    if (category.length > _maxCategoryLength) {
      throw CustomCategoryValidationError(
        'Category name must be $_maxCategoryLength characters or less',
        _errorTooLong,
        {'maxLength': _maxCategoryLength, 'actualLength': category.length},
      );
    }

    if (category.trim().isEmpty) {
      throw CustomCategoryValidationError(
        'Category name cannot be whitespace only',
        _errorWhitespace,
      );
    }

    // Check for invalid characters
    final invalidChars = RegExp(r'[<>{}[\]\\]');
    if (invalidChars.hasMatch(category)) {
      throw CustomCategoryValidationError(
        'Category name contains invalid characters',
        _errorInvalidChars,
        {'invalidChars': invalidChars.allMatches(category).map((m) => m.group(0)).toList()},
      );
    }

    // Check against built-in categories
    if (_builtInCategories.any((builtIn) => 
        builtIn.toLowerCase() == category.toLowerCase())) {
      throw CustomCategoryValidationError(
        'Cannot use built-in category name',
        _errorBuiltIn,
        {'builtInCategory': category},
      );
    }
  }

  /// Validates a list of categories
  static void validateCategories(List<String> categories) {
    if (categories.isEmpty) {
      throw CustomCategoryValidationError(
        'At least one category is required',
        _errorEmpty,
      );
    }

    if (categories.length > _maxCustomCategories) {
      throw CustomCategoryValidationError(
        'Maximum $_maxCustomCategories custom categories allowed',
        _errorTooMany,
        {'maxCategories': _maxCustomCategories, 'actualCount': categories.length},
      );
    }

    // Check for duplicates (case-insensitive)
    final lowerCaseCategories = categories.map((c) => c.toLowerCase()).toList();
    final uniqueCategories = lowerCaseCategories.toSet();
    if (uniqueCategories.length != categories.length) {
      throw CustomCategoryValidationError(
        'Duplicate categories are not allowed',
        _errorDuplicate,
        {'categories': categories},
      );
    }

    // Validate each category
    for (final category in categories) {
      validateCategoryName(category);
    }
  }

  /// Validates categories against existing user categories
  static void validateAgainstUserCategories(
    List<String> newCategories,
    User user,
  ) {
    // Get all existing categories (built-in + custom)
    final existingCategories = [
      ..._builtInCategories,
      ...user.customCategories,
    ];

    print('🔍 Validator: newCategories = $newCategories');
    print('🔍 Validator: existingCategories = $existingCategories');
    print('🔍 Validator: user.customCategories = ${user.customCategories}');

    // Check for conflicts with existing categories
    for (final newCategory in newCategories) {
      final conflictingCategory = existingCategories.firstWhere(
        (existing) => existing.toLowerCase() == newCategory.toLowerCase(),
        orElse: () => '',
      );

      if (conflictingCategory.isNotEmpty) {
        print('❌ Validator: Found conflict - "$newCategory" matches existing "$conflictingCategory"');
        throw CustomCategoryValidationError(
          'Category "$newCategory" already exists',
          _errorDuplicate,
          {
            'newCategory': newCategory,
            'conflictingCategory': conflictingCategory,
            'existingCategories': existingCategories,
          },
        );
      }
    }
    print('✅ Validator: No conflicts found');
  }

  /// Validates the entire category setup for a user
  static void validateUserCategorySetup(User user) {
    // Validate custom categories
    validateCategories(user.customCategories);

    // Validate against built-in categories
    for (final category in user.customCategories) {
      validateCategoryName(category);
    }

    // Validate category maps
    _validateCategoryMaps(user);
  }

  /// Validates the category and lastWeekExp maps
  static void _validateCategoryMaps(User user) {
    // Check if all custom categories exist in both maps
    for (final category in user.customCategories) {
      if (!user.categories.containsKey(category)) {
        throw CustomCategoryValidationError(
          'Category "$category" missing from categories map',
          'MISSING_CATEGORY_MAP',
          {'category': category, 'map': 'categories'},
        );
      }

      if (!user.lastWeekExp.containsKey(category)) {
        throw CustomCategoryValidationError(
          'Category "$category" missing from lastWeekExp map',
          'MISSING_LAST_WEEK_MAP',
          {'category': category, 'map': 'lastWeekExp'},
        );
      }
    }

    // Check if maps contain only valid categories
    for (final category in user.categories.keys) {
      if (!_builtInCategories.contains(category) &&
          !user.customCategories.contains(category)) {
        throw CustomCategoryValidationError(
          'Invalid category "$category" in categories map',
          'INVALID_CATEGORY_MAP',
          {'category': category, 'map': 'categories'},
        );
      }
    }

    for (final category in user.lastWeekExp.keys) {
      if (!_builtInCategories.contains(category) &&
          !user.customCategories.contains(category)) {
        throw CustomCategoryValidationError(
          'Invalid category "$category" in lastWeekExp map',
          'INVALID_LAST_WEEK_MAP',
          {'category': category, 'map': 'lastWeekExp'},
        );
      }
    }
  }
} 