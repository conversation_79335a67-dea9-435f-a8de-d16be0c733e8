// 📁 lib/cstcat/critical_path_validator.dart

import '../models/user_model.dart';
import '../cstcat/custom_category_validator.dart';

class CriticalPathValidator {
  static final CriticalPathValidator _instance = CriticalPathValidator._internal();
  factory CriticalPathValidator() => _instance;
  CriticalPathValidator._internal();

  /// Validates the critical path for first-time users
  Future<bool> validateCriticalPath(User user) async {
    try {
      // 1. Check if user has completed initial setup
      if (!_hasCompletedInitialSetup(user)) {
        return false;
      }

      // 2. Validate custom categories
      if (!_hasValidCustomCategories(user)) {
        return false;
      }

      // 3. Check if user has selected at least one category
      if (!_hasSelectedCategories(user)) {
        return false;
      }

      return true;
    } catch (e) {
      return false;
    }
  }

  /// Checks if user has completed initial setup
  bool _hasCompletedInitialSetup(User user) {
    return user.customCategories.isNotEmpty;
  }

  /// Validates custom categories
  bool _hasValidCustomCategories(User user) {
    try {
      CustomCategoryValidator.validateUserCategorySetup(user);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Checks if user has selected at least one category
  bool _hasSelectedCategories(User user) {
    return user.categories.isNotEmpty;
  }

  /// Gets validation errors if any
  List<String> getValidationErrors(User user) {
    final errors = <String>[];

    if (!_hasCompletedInitialSetup(user)) {
      errors.add('Initial setup not completed');
    }

    if (!_hasValidCustomCategories(user)) {
      errors.add('Invalid custom categories');
    }

    if (!_hasSelectedCategories(user)) {
      errors.add('No categories selected');
    }

    return errors;
  }
} 