import WidgetKit
import SwiftUI

struct Provider: TimelineProvider {
    func placeholder(in context: Context) -> SimpleEntry {
        SimpleEntry(date: Date(), level: 1, levelProgress: 0.0, rank: 1, rankProgress: 0.0)
    }

    func getSnapshot(in context: Context, completion: @escaping (SimpleEntry) -> ()) {
        let entry = SimpleEntry(date: Date(), level: 1, levelProgress: 0.0, rank: 1, rankProgress: 0.0)
        completion(entry)
    }

    func getTimeline(in context: Context, completion: @escaping (Timeline<Entry>) -> ()) {
        let userDefaults = UserDefaults(suiteName: "group.com.guardiantape.maxedoutlife")
        
        let level = userDefaults?.integer(forKey: "level") ?? 1
        let levelProgress = userDefaults?.double(forKey: "level_progress") ?? 0.0
        let rank = userDefaults?.integer(forKey: "rank") ?? 1
        let rankProgress = userDefaults?.double(forKey: "rank_progress") ?? 0.0
        
        let entry = SimpleEntry(
            date: Date(),
            level: level,
            levelProgress: levelProgress,
            rank: rank,
            rankProgress: rankProgress
        )
        
        let timeline = Timeline(entries: [entry], policy: .atEnd)
        completion(timeline)
    }
}

struct SimpleEntry: TimelineEntry {
    let date: Date
    let level: Int
    let levelProgress: Double
    let rank: Int
    let rankProgress: Double
}

struct WidgetExtensionEntryView : View {
    var entry: Provider.Entry

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Maxed Out Life")
                .font(.headline)
                .foregroundColor(.white)
            
            // Total Level Progress
            VStack(alignment: .leading, spacing: 4) {
                Text("Total Level")
                    .font(.subheadline)
                    .foregroundColor(.gray)
                
                Text("Level \(entry.level)")
                    .font(.title3)
                    .foregroundColor(.white)
                
                ProgressView(value: entry.levelProgress)
                    .progressViewStyle(LinearProgressViewStyle(tint: .blue))
            }
            
            // North Star Rank
            VStack(alignment: .leading, spacing: 4) {
                Text("North Star Rank")
                    .font(.subheadline)
                    .foregroundColor(.gray)
                
                Text("Rank \(entry.rank)")
                    .font(.title3)
                    .foregroundColor(.white)
                
                ZStack {
                    // Background
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.white.opacity(0.1))
                        .frame(height: 24)
                    
                    // Progress bar
                    GeometryReader { geometry in
                        RoundedRectangle(cornerRadius: 12)
                            .fill(
                                LinearGradient(
                                    gradient: Gradient(colors: [
                                        Color(red: 0, green: 1, blue: 1), // Cyan
                                        Color(red: 0.61, green: 0.15, blue: 0.69), // Purple
                                        Color(red: 1, green: 0.84, blue: 0) // Gold
                                    ]),
                                    startPoint: .leading,
                                    endPoint: .trailing
                                )
                            )
                            .frame(width: geometry.size.width * entry.rankProgress)
                            .shadow(color: Color(red: 0, green: 1, blue: 1).opacity(0.5), radius: 8, x: 0, y: 0)
                    }
                }
                .frame(height: 24)
            }
        }
        .padding()
        .background(Color.black.opacity(0.8))
    }
}

@main
struct WidgetExtension: Widget {
    let kind: String = "WidgetExtension"

    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: Provider()) { entry in
            WidgetExtensionEntryView(entry: entry)
        }
        .configurationDisplayName("Maxed Out Life")
        .description("Track your level and rank progress.")
        .supportedFamilies([.systemSmall, .systemMedium])
    }
} 