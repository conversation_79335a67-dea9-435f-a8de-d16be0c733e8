import Flutter
import UIKit
import Firebase
import AVFoundation

@main
@objc class AppDelegate: FlutterAppDelegate {
  private var deepLinkChannel: FlutterMethodChannel?

  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {
    // Configure audio session to respect hardware volume buttons
    do {
      try AVAudioSession.sharedInstance().setCategory(.playback, mode: .default, options: [])
      try AVAudioSession.sharedInstance().setActive(true)
    } catch {
      print("Failed to configure audio session: \(error)")
    }

    // Initialize Firebase
    FirebaseApp.configure()

    GeneratedPluginRegistrant.register(with: self)

    // Set up deep link method channel
    if let controller = window?.rootViewController as? FlutterViewController {
      deepLinkChannel = FlutterMethodChannel(name: "mxd.app/deep_links", binaryMessenger: controller.binaryMessenger)

      deepLinkChannel?.setMethodCallHandler { [weak self] (call: FlutterMethodCall, result: @escaping FlutterResult) in
        switch call.method {
        case "getInitialLink":
          // Check if app was launched via URL scheme
          if let url = launchOptions?[UIApplication.LaunchOptionsKey.url] as? URL {
            result(url.absoluteString)
          } else {
            result(nil)
          }
        default:
          result(FlutterMethodNotImplemented)
        }
      }
    }

    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }

  // Handle URL schemes when app is already running
  override func application(_ app: UIApplication, open url: URL, options: [UIApplication.OpenURLOptionsKey : Any] = [:]) -> Bool {
    deepLinkChannel?.invokeMethod("onDeepLink", arguments: ["url": url.absoluteString])
    return true
  }
}
