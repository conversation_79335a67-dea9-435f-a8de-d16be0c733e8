#!/bin/bash

# Flutter Build Wrapper Script
# This script ensures all required environment variables are set before calling the Flutter build script

# Set default values for missing environment variables
export FLUTTER_BUILD_DIR="${FLUTTER_BUILD_DIR:-build}"
export FLUTTER_APPLICATION_PATH="${FLUTTER_APPLICATION_PATH:-$(pwd)/..}"
export SOURCE_ROOT="${SOURCE_ROOT:-$(pwd)}"

# Debug output (optional)
if [ "${VERBOSE_SCRIPT_LOGGING}" = "true" ]; then
    echo "Flutter Build Wrapper - Environment Variables:"
    echo "FLUTTER_ROOT: ${FLUTTER_ROOT}"
    echo "FLUTTER_BUILD_DIR: ${FLUTTER_BUILD_DIR}"
    echo "FLUTTER_APPLICATION_PATH: ${FLUTTER_APPLICATION_PATH}"
    echo "SOURCE_ROOT: ${SOURCE_ROOT}"
fi

# Call the original Flutter script with all arguments
exec /bin/sh "${FLUTTER_ROOT}/packages/flutter_tools/bin/xcode_backend.sh" "$@"
