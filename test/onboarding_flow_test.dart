// test/onboarding_flow_test.dart

import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:maxed_out_life/services/firebase_auth_service.dart';
import 'package:maxed_out_life/controller/firebase_auth_controller.dart';
import 'package:maxed_out_life/services/bulletproof_storage_service.dart';
import 'package:maxed_out_life/widgets/awaiting_email_verification_modal.dart';
import 'package:maxed_out_life/onboarding/welcome_start_03.dart';
import 'package:maxed_out_life/models/user_model.dart';

/// 🧪 COMPREHENSIVE ONBOARDING FLOW TESTS
/// 
/// These tests validate the bulletproof email verification and onboarding system
/// to ensure all connections work properly and failsafes are in place.
void main() {
  group('🛡️ Bulletproof Onboarding Flow Tests', () {
    
    testWidgets('🔍 Firebase Service Connection Test', (WidgetTester tester) async {
      // Test Firebase service initialization
      expect(FirebaseAuthService.initialize, isA<Function>());
      expect(FirebaseAuthService.currentUser, isA<dynamic>());
      expect(FirebaseAuthService.isSignedIn, isA<bool>());
      expect(FirebaseAuthService.isEmailVerified, isA<bool>());
      expect(FirebaseAuthService.reloadUser, isA<Function>());
      expect(FirebaseAuthService.createAccount, isA<Function>());
      expect(FirebaseAuthService.sendEmailVerification, isA<Function>());
    });

    testWidgets('🔍 Storage Service Connection Test', (WidgetTester tester) async {
      final storage = BulletproofStorageService();
      
      // Test storage methods exist
      expect(storage.write, isA<Function>());
      expect(storage.read, isA<Function>());
      expect(storage.delete, isA<Function>());
    });

    testWidgets('🔍 Awaiting Email Verification Modal Creation Test', (WidgetTester tester) async {
      // Test modal can be created with all required parameters
      final modal = AwaitingEmailVerificationModal(
        email: '<EMAIL>',
        password: 'testPassword123',
        username: 'testUser',
        gender: 'Male',
        onSuccess: () {},
        onError: (error) {},
      );
      
      expect(modal, isA<AwaitingEmailVerificationModal>());
      expect(modal.email, equals('<EMAIL>'));
      expect(modal.password, equals('testPassword123'));
      expect(modal.username, equals('testUser'));
      expect(modal.gender, equals('Male'));
    });

    testWidgets('🔍 Welcome Start Page Creation Test', (WidgetTester tester) async {
      final testUser = User.blank(id: 'test', username: 'test');
      
      // Test page can be created
      final page = WelcomeStartPage(
        user: testUser,
        onContinue: (user) {},
      );
      
      expect(page, isA<WelcomeStartPage>());
      expect(page.user, equals(testUser));
    });

    testWidgets('🔍 Firebase Controller Integration Test', (WidgetTester tester) async {
      // Create a test widget with provider
      await tester.pumpWidget(
        MaterialApp(
          home: ChangeNotifierProvider(
            create: (context) => FirebaseAuthController(),
            child: Builder(
              builder: (context) {
                final controller = Provider.of<FirebaseAuthController>(context, listen: false);
                
                // Test controller methods exist
                expect(controller.initialize, isA<Function>());
                expect(controller.createAccount, isA<Function>());
                expect(controller.signIn, isA<Function>());
                expect(controller.reloadUser, isA<Function>());
                expect(controller.isSignedIn, isA<bool>());
                expect(controller.isEmailVerified, isA<bool>());
                
                return const Scaffold(
                  body: Text('Test'),
                );
              },
            ),
          ),
        ),
      );
      
      await tester.pumpAndSettle();
      expect(find.text('Test'), findsOneWidget);
    });

    testWidgets('🔍 Email Verification Modal Widget Test', (WidgetTester tester) async {
      // Create modal widget
      await tester.pumpWidget(
        MaterialApp(
          home: ChangeNotifierProvider(
            create: (context) => FirebaseAuthController(),
            child: AwaitingEmailVerificationModal(
              email: '<EMAIL>',
              password: 'testPassword123',
              username: 'testUser',
              gender: 'Male',
              onSuccess: () {
                // Success callback for testing
              },
              onError: (error) {
                // Error callback for testing
              },
            ),
          ),
        ),
      );

      // Just pump once to avoid timeout in test environment
      await tester.pump();

      // Check if modal renders
      expect(find.text('Awaiting Email Verification'), findsOneWidget);
      expect(find.text('<EMAIL>'), findsOneWidget);
    });

    testWidgets('🔍 Welcome Start Page Widget Test', (WidgetTester tester) async {
      final testUser = User.blank(id: 'test', username: 'test');

      // Create page widget
      await tester.pumpWidget(
        MaterialApp(
          home: MultiProvider(
            providers: [
              ChangeNotifierProvider(create: (context) => FirebaseAuthController()),
            ],
            child: WelcomeStartPage(
              user: testUser,
              onContinue: (user) {
                // Continue callback for testing
              },
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Check if page renders with correct text
      expect(find.text('Welcome to your'), findsOneWidget);
    });

    test('🔍 Input Validation Tests', () {
      // Test email validation
      expect('<EMAIL>'.contains('@'), isTrue);
      expect('invalid-email'.contains('@'), isFalse);
      
      // Test username validation
      expect(RegExp(r'^[a-zA-Z0-9_]+$').hasMatch('validUser123'), isTrue);
      expect(RegExp(r'^[a-zA-Z0-9_]+$').hasMatch('invalid user!'), isFalse);
      
      // Test password validation
      expect('password123'.length >= 8, isTrue);
      expect('short'.length >= 8, isFalse);
      
      // Test gender validation
      expect(['Male', 'Female', 'Non-Gender'].contains('Male'), isTrue);
      expect(['Male', 'Female', 'Non-Gender'].contains('Invalid'), isFalse);
    });

    test('🔍 Session ID Generation Test', () {
      final sessionId1 = DateTime.now().millisecondsSinceEpoch.toString();
      final sessionId2 = DateTime.now().millisecondsSinceEpoch.toString();
      
      // Session IDs should be strings
      expect(sessionId1, isA<String>());
      expect(sessionId2, isA<String>());
      
      // Session IDs should be non-empty
      expect(sessionId1.isNotEmpty, isTrue);
      expect(sessionId2.isNotEmpty, isTrue);
    });

    test('🔍 Debug Log Format Test', () {
      final timestamp = DateTime.now().toIso8601String();
      final message = 'Test message';
      final logEntry = '[$timestamp] $message';
      
      expect(logEntry.contains(timestamp), isTrue);
      expect(logEntry.contains(message), isTrue);
      expect(logEntry.startsWith('['), isTrue);
      expect(logEntry.contains('] '), isTrue);
    });

    test('🔍 State Snapshot Structure Test', () {
      final stateSnapshot = {
        'timestamp': DateTime.now().toIso8601String(),
        'disposed': false,
        'email_sent': false,
        'is_verified': false,
        'current_level': 1,
        'verification_attempts': 0,
        'firebase_connection': true,
        'controller_connection': true,
      };
      
      expect(stateSnapshot['timestamp'], isA<String>());
      expect(stateSnapshot['disposed'], isA<bool>());
      expect(stateSnapshot['email_sent'], isA<bool>());
      expect(stateSnapshot['is_verified'], isA<bool>());
      expect(stateSnapshot['current_level'], isA<int>());
      expect(stateSnapshot['verification_attempts'], isA<int>());
      expect(stateSnapshot['firebase_connection'], isA<bool>());
      expect(stateSnapshot['controller_connection'], isA<bool>());
    });
  });
}
