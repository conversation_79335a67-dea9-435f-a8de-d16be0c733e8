import 'package:flutter_test/flutter_test.dart';
import 'package:maxed_out_life/models/user_model.dart';
import 'package:maxed_out_life/models/bounty_model.dart';
import 'package:maxed_out_life/data/bounty_data.dart';
import 'package:maxed_out_life/widgets/cashed_bounties_modal.dart';

void main() {
  group('Bounty System Tests', () {
    late User testUser;
    late BountyModel testBounty;

    setUpAll(() {
      TestWidgetsFlutterBinding.ensureInitialized();
    });

    setUp(() {
      testUser = User.blank(id: 'test-user', username: 'TestUser');
      testBounty = BountyModel(
        id: 'test-bounty',
        description: 'Complete a 30-minute workout',
        categories: ['Health'],
        expPerCategory: {'Health': 50},
        difficulty: 'medium',
        isEpic: false,
      );
    });

    test('Bounty data structure is valid', () {
      expect(allBounties, isNotEmpty);
      expect(allBounties.length, greaterThan(5));
      
      // Check first bounty structure
      final firstBounty = allBounties.first;
      expect(firstBounty.id, isNotEmpty);
      expect(firstBounty.description, isNotEmpty);
      expect(firstBounty.categories, isNotEmpty);
      expect(firstBounty.expPerCategory, isNotEmpty);
      expect(firstBounty.difficulty, isIn(['easy', 'medium', 'hard', 'epic']));
      expect(firstBounty.isEpic, isA<bool>());
    });

    test('Bounty completion awards correct EXP', () {
      // Complete bounty and check EXP award
      var updatedUser = testUser;
      
      for (final entry in testBounty.expPerCategory.entries) {
        updatedUser = updatedUser.copyWithAddedExp(
          entry.key, 
          entry.value, 
          'Bounty: ${testBounty.description}'
        );
      }
      
      expect(updatedUser.exp, equals(50)); // Total EXP from bounty
      expect(updatedUser.categories['Health'], equals(50));
      expect(updatedUser.dailyExpTotal, equals(50)); // Should contribute to daily total
    });

    test('Epic bounty has higher EXP rewards', () {
      final epicBounty = BountyModel(
        id: 'epic-test',
        description: 'Epic challenge',
        categories: ['Health', 'Purpose'],
        expPerCategory: {'Health': 100, 'Purpose': 100},
        difficulty: 'epic',
        isEpic: true,
      );
      
      expect(epicBounty.isEpic, isTrue);
      expect(epicBounty.difficulty, equals('epic'));
      
      final totalExp = epicBounty.expPerCategory.values.fold(0, (sum, exp) => sum + exp);
      expect(totalExp, greaterThan(100)); // Epic bounties should have high EXP
    });

    test('Bounty completion triggers spinner unlock check', () {
      // Start with user below spinner threshold
      var userBelow = testUser.copyWith(dailyExpTotal: 30);
      expect(userBelow.availableSpinnerPlays, equals(0));
      
      // Complete bounty that pushes over 40 EXP threshold
      var userAfter = userBelow.copyWithAddedExp('Health', 20, 'Bounty completion');
      expect(userAfter.dailyExpTotal, equals(50));
      expect(userAfter.availableSpinnerPlays, equals(1)); // Should unlock spinner
    });

    test('CashedBounty structure is valid', () {
      final cashedBounty = CashedBounty(
        bounty: testBounty,
        photoPath: '/test/photo/path.jpg',
        completedAt: DateTime.now(),
      );
      
      expect(cashedBounty.bounty, equals(testBounty));
      expect(cashedBounty.photoPath, isNotEmpty);
      expect(cashedBounty.completedAt, isA<DateTime>());
    });

    test('Multiple category bounties award EXP correctly', () {
      final multiCategoryBounty = BountyModel(
        id: 'multi-test',
        description: 'Multi-category challenge',
        categories: ['Health', 'Purpose', 'Connection'],
        expPerCategory: {'Health': 30, 'Purpose': 30, 'Connection': 30},
        difficulty: 'hard',
        isEpic: false,
      );
      
      var updatedUser = testUser;
      
      // Award EXP for each category
      for (final entry in multiCategoryBounty.expPerCategory.entries) {
        updatedUser = updatedUser.copyWithAddedExp(
          entry.key, 
          entry.value, 
          'Bounty: ${multiCategoryBounty.description}'
        );
      }
      
      expect(updatedUser.exp, equals(90)); // Total EXP
      expect(updatedUser.categories['Health'], equals(30));
      expect(updatedUser.categories['Purpose'], equals(30));
      expect(updatedUser.categories['Connection'], equals(30));
      expect(updatedUser.dailyExpTotal, equals(90));
    });

    test('Bounty difficulty levels are properly distributed', () {
      final difficulties = allBounties.map((b) => b.difficulty).toSet();
      
      expect(difficulties, contains('easy'));
      expect(difficulties, contains('medium'));
      expect(difficulties, contains('hard'));
      
      // Check that epic bounties exist
      final epicBounties = allBounties.where((b) => b.isEpic).toList();
      expect(epicBounties, isNotEmpty);
      
      // Epic bounties should have 'epic' difficulty or high EXP
      for (final epic in epicBounties) {
        final totalExp = epic.expPerCategory.values.fold(0, (sum, exp) => sum + exp);
        expect(totalExp, greaterThan(80)); // Epic bounties should have high rewards
      }
    });

    test('Bounty categories match expected categories', () {
      final expectedCategories = {'Health', 'Wealth', 'Purpose', 'Connection'};
      
      for (final bounty in allBounties) {
        for (final category in bounty.categories) {
          expect(expectedCategories, contains(category));
        }
        
        // Each bounty should have at least one category
        expect(bounty.categories, isNotEmpty);
        
        // EXP per category should match categories
        for (final category in bounty.categories) {
          expect(bounty.expPerCategory, containsPair(category, isA<int>()));
          expect(bounty.expPerCategory[category]!, greaterThan(0));
        }
      }
    });

    test('Camera service file path generation', () {
      // Test file path format
      final bountyId = 'test-bounty-123';
      
      // Mock the expected path format
      final expectedPattern = RegExp(r'bounty_test-bounty-123_\d+\.jpg$');
      
      // Since we can't easily test the actual camera service without mocking,
      // we'll test the path generation logic conceptually
      final timestamp = DateTime.now().millisecondsSinceEpoch.toString();
      final fileName = 'bounty_${bountyId}_$timestamp.jpg';
      
      expect(fileName, matches(expectedPattern));
      expect(fileName, contains(bountyId));
      expect(fileName, endsWith('.jpg'));
    });

    test('Bounty completion with bonus categories', () {
      // Create user with active bonus category
      var userWithBonus = testUser.copyWith(
        activeBonusCategories: ['Health'],
      );
      
      // Complete Health bounty - should have chance for bonus EXP
      var updatedUser = userWithBonus.copyWithAddedExp('Health', 50, 'Bounty completion');
      
      // Should have at least base EXP (might have bonus)
      expect(updatedUser.exp, greaterThanOrEqualTo(50));
      expect(updatedUser.categories['Health'], greaterThanOrEqualTo(50));
      
      // Complete non-bonus category bounty - should not get bonus
      updatedUser = userWithBonus.copyWithAddedExp('Wealth', 30, 'Wealth bounty');
      expect(updatedUser.categories['Wealth'], equals(30)); // Exact amount, no bonus
    });
  });
}
