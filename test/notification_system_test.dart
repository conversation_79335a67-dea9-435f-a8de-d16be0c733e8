import 'package:flutter_test/flutter_test.dart';
import 'package:maxed_out_life/models/user_model.dart';
import 'package:maxed_out_life/models/bounty_model.dart';
import 'package:maxed_out_life/services/notification_manager.dart';
import 'package:maxed_out_life/services/bounty_notification_service.dart';

void main() {
  group('Notification System Tests', () {
    late User testUser;
    late BountyModel testBounty;
    late NotificationManager notificationManager;

    setUpAll(() {
      TestWidgetsFlutterBinding.ensureInitialized();
    });

    setUp(() {
      testUser = User.blank(id: 'test-user', username: 'TestUser')
          .copyWith(
            exp: 1500,
            categories: {
              'Health': 600,
              'Wealth': 300,
              'Purpose': 400,
              'Connection': 200,
            },
            gender: 'male',
            createdAt: DateTime.now().subtract(Duration(days: 30)),
          );

      testBounty = BountyModel(
        id: 'test-bounty',
        description: 'Complete a 30-minute workout',
        categories: ['Health'],
        expPerCategory: {'Health': 50},
        difficulty: 'medium',
        isEpic: false,
      );

      notificationManager = NotificationManager();
    });

    group('Notification Manager Tests', () {
      test('Notification manager initializes correctly', () {
        expect(notificationManager, isNotNull);
        
        final status = notificationManager.getStatus();
        expect(status, isA<Map<String, dynamic>>());
        expect(status.containsKey('is_initialized'), isTrue);
        expect(status.containsKey('auto_schedule_enabled'), isTrue);
        expect(status.containsKey('notification_interval_hours'), isTrue);
      });

      test('Auto-schedule settings can be modified', () async {
        // Test enabling/disabling auto-schedule
        await notificationManager.setAutoScheduleEnabled(false);
        var status = notificationManager.getStatus();
        expect(status['auto_schedule_enabled'], isFalse);

        await notificationManager.setAutoScheduleEnabled(true);
        status = notificationManager.getStatus();
        expect(status['auto_schedule_enabled'], isTrue);
      });

      test('Notification interval can be set within valid range', () async {
        // Test valid intervals
        await notificationManager.setNotificationInterval(24); // 1 day
        var status = notificationManager.getStatus();
        expect(status['notification_interval_hours'], equals(24));

        await notificationManager.setNotificationInterval(72); // 3 days
        status = notificationManager.getStatus();
        expect(status['notification_interval_hours'], equals(72));

        // Test clamping - too low
        await notificationManager.setNotificationInterval(0);
        status = notificationManager.getStatus();
        expect(status['notification_interval_hours'], equals(1)); // Should clamp to 1

        // Test clamping - too high
        await notificationManager.setNotificationInterval(200);
        status = notificationManager.getStatus();
        expect(status['notification_interval_hours'], equals(168)); // Should clamp to 168 (1 week)
      });

      test('Manager disposes cleanly', () {
        expect(() => notificationManager.dispose(), returnsNormally);
      });
    });

    group('Bounty Notification Service Tests', () {
      test('Should send notification based on business rules', () {
        final shouldSend = BountyNotificationService.shouldSendNotification(testUser);
        expect(shouldSend, isA<bool>());
      });

      test('Generates personalized bounty for user', () {
        final bounty = BountyNotificationService.generatePersonalizedBounty(testUser);
        
        if (bounty != null) {
          expect(bounty.id, isNotEmpty);
          expect(bounty.description, isNotEmpty);
          expect(bounty.categories, isNotEmpty);
          expect(bounty.expPerCategory, isNotEmpty);
          
          // Should be appropriate for user level
          expect(['easy', 'medium', 'hard', 'epic'], contains(bounty.difficulty));
        }
      });

      test('Generates appropriate notification messages', () {
        final message = BountyNotificationService.generateNotificationMessage(testUser, testBounty);
        
        expect(message, isNotEmpty);
        expect(message, contains(testUser.username));
        expect(message, contains(testBounty.description));
        
        // Should contain coach name
        final coachNames = ['Marcus', 'Luna', 'Aether', 'Chronos', 'Elysia'];
        final containsCoach = coachNames.any((coach) => message.contains(coach));
        expect(containsCoach, isTrue);
      });

      test('Schedules next notification correctly', () {
        final nextNotification = BountyNotificationService.scheduleNextNotification();
        final now = DateTime.now();
        
        expect(nextNotification.isAfter(now), isTrue);
        
        // Should be at least 2 days in the future
        expect(nextNotification.difference(now).inDays, greaterThanOrEqualTo(2));
      });

      test('Epic bounty alerts work for qualified users', () {
        // High-level user should be eligible for epic alerts
        final highLevelUser = testUser.copyWith(exp: 5000);
        final shouldAlert = BountyNotificationService.shouldSendEpicBountyAlert(highLevelUser);
        expect(shouldAlert, isA<bool>());

        // Low-level user should not get epic alerts
        final lowLevelUser = testUser.copyWith(exp: 100);
        final shouldNotAlert = BountyNotificationService.shouldSendEpicBountyAlert(lowLevelUser);
        expect(shouldNotAlert, isFalse);
      });

      test('Generates epic bounty alert messages', () {
        final alertMessage = BountyNotificationService.generateEpicBountyAlert(testUser);
        
        expect(alertMessage, isNotEmpty);
        expect(alertMessage, contains(testUser.username));
        expect(alertMessage.toUpperCase(), contains('EPIC'));
        
        // Should contain excitement indicators
        final excitementIndicators = ['🔥', '⚡', '🌟', '💎'];
        final containsExcitement = excitementIndicators.any((indicator) => alertMessage.contains(indicator));
        expect(containsExcitement, isTrue);
      });

      test('Coach assignment works correctly', () {
        // Male user should get male coach or random
        final maleUser = testUser.copyWith(gender: 'male');
        final maleMessage = BountyNotificationService.generateNotificationMessage(maleUser, testBounty);
        expect(maleMessage, isNotEmpty);

        // Female user should get female coach or random
        final femaleUser = testUser.copyWith(gender: 'female');
        final femaleMessage = BountyNotificationService.generateNotificationMessage(femaleUser, testBounty);
        expect(femaleMessage, isNotEmpty);

        // Non-gender user should get random coach
        final nonGenderUser = testUser.copyWith(gender: 'non-binary');
        final nonGenderMessage = BountyNotificationService.generateNotificationMessage(nonGenderUser, testBounty);
        expect(nonGenderMessage, isNotEmpty);
      });
    });

    group('Notification Timing Tests', () {
      test('Time window validation works correctly', () {
        // Test various times to ensure they fall within 5:45am - 8:45pm window
        final earlyMorning = DateTime(2024, 1, 1, 6, 0); // 6:00am - should be valid
        final midDay = DateTime(2024, 1, 1, 12, 0); // 12:00pm - should be valid
        final evening = DateTime(2024, 1, 1, 20, 0); // 8:00pm - should be valid
        final tooEarly = DateTime(2024, 1, 1, 4, 0); // 4:00am - should be invalid
        final tooLate = DateTime(2024, 1, 1, 22, 0); // 10:00pm - should be invalid

        // We can't directly test the private method, but we can test the scheduling logic
        expect(earlyMorning.hour, greaterThanOrEqualTo(5));
        expect(midDay.hour, lessThanOrEqualTo(20));
        expect(evening.hour, lessThanOrEqualTo(20));
        expect(tooEarly.hour, lessThan(5));
        expect(tooLate.hour, greaterThan(20));
      });

      test('Notification frequency limits are respected', () {
        // Test that the system respects the 3-day interval
        final now = DateTime.now();
        final threeDaysAgo = now.subtract(Duration(days: 3));
        final twoDaysAgo = now.subtract(Duration(days: 2));
        
        expect(now.difference(threeDaysAgo).inDays, equals(3));
        expect(now.difference(twoDaysAgo).inDays, equals(2));
        
        // Should allow notification after 3 days
        expect(now.difference(threeDaysAgo).inDays >= 3, isTrue);
        
        // Should not allow notification after only 2 days
        expect(now.difference(twoDaysAgo).inDays >= 3, isFalse);
      });
    });

    group('Error Handling Tests', () {
      test('Handles null user gracefully', () {
        expect(() => BountyNotificationService.generateNotificationMessage(testUser, testBounty), 
          returnsNormally);
      });

      test('Handles empty bounty list gracefully', () {
        final bounty = BountyNotificationService.generatePersonalizedBounty(testUser);
        // Should either return a bounty or null, but not throw
        expect(bounty, anyOf(isNull, isA<BountyModel>()));
      });

      test('Handles invalid user data gracefully', () {
        final invalidUser = User.blank(id: '', username: '');
        
        expect(() => BountyNotificationService.generateNotificationMessage(invalidUser, testBounty), 
          returnsNormally);
        
        expect(() => BountyNotificationService.shouldSendNotification(invalidUser), 
          returnsNormally);
      });
    });

    group('Integration Tests', () {
      test('Notification manager integrates with bounty service', () {
        final status = notificationManager.getStatus();
        expect(status, isNotNull);
        
        // Should have all required status fields
        expect(status.containsKey('is_initialized'), isTrue);
        expect(status.containsKey('auto_schedule_enabled'), isTrue);
        expect(status.containsKey('notification_interval_hours'), isTrue);
        expect(status.containsKey('auto_schedule_timer_active'), isTrue);
      });

      test('Epic bounty system integrates correctly', () {
        final highLevelUser = testUser.copyWith(exp: 5000);
        
        // Should be able to generate epic alert
        final alertMessage = BountyNotificationService.generateEpicBountyAlert(highLevelUser);
        expect(alertMessage, isNotEmpty);
        
        // Should qualify for epic bounty alerts
        final shouldAlert = BountyNotificationService.shouldSendEpicBountyAlert(highLevelUser);
        expect(shouldAlert, isA<bool>());
      });

      test('Personalized bounty generation works with notification system', () {
        final personalizedBounty = BountyNotificationService.generatePersonalizedBounty(testUser);
        
        if (personalizedBounty != null) {
          final message = BountyNotificationService.generateNotificationMessage(testUser, personalizedBounty);
          expect(message, contains(personalizedBounty.description));
          expect(message, contains(testUser.username));
        }
      });
    });

    tearDown(() {
      // Clean up after each test
      notificationManager.dispose();
    });
  });
}
