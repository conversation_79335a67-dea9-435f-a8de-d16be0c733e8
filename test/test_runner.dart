// test/test_runner.dart

import 'dart:io';

/// Simplified test runner for MXD app
///
/// This validates that the app builds successfully and basic functionality works
/// Usage: dart test/test_runner.dart
void main() async {
  print('🧪 MXD App Build Validation Starting...\n');

  final testRunner = MXDTestRunner();
  final success = await testRunner.runBasicValidation();

  if (success) {
    print('\n✅ Build validation passed! App is ready for testing.');
    exit(0);
  } else {
    print('\n❌ Build validation failed. Please review and fix issues.');
    exit(1);
  }
}

class MXDTestRunner {
  int _passedTests = 0;
  int _failedTests = 0;
  final List<String> _failureReasons = [];

  /// Run basic validation tests
  Future<bool> runBasicValidation() async {
    print('📋 Running basic validation tests...\n');

    // Basic validation tests
    await _runTest('Build System', _testBuildSystem);
    await _runTest('File Structure', _testFileStructure);
    await _runTest('Configuration', _testConfiguration);

    _printSummary();
    return _failedTests == 0;
  }

  /// Run individual test with error handling
  Future<void> _runTest(String testName, Future<bool> Function() testFunction) async {
    try {
      print('🔍 Testing: $testName...');
      final success = await testFunction();

      if (success) {
        print('✅ $testName: PASSED\n');
        _passedTests++;
      } else {
        print('❌ $testName: FAILED\n');
        _failedTests++;
        _failureReasons.add(testName);
      }
    } catch (e) {
      print('💥 $testName: ERROR - $e\n');
      _failedTests++;
      _failureReasons.add('$testName (Error: $e)');
    }
  }

  /// Test build system
  Future<bool> _testBuildSystem() async {
    try {
      // Check if the app was built successfully
      final buildDir = Directory('build');
      if (!buildDir.existsSync()) {
        print('   ❌ Build directory not found');
        return false;
      }

      // Check for iOS build artifacts
      final iosDir = Directory('build/ios');
      if (!iosDir.existsSync()) {
        print('   ❌ iOS build directory not found');
        return false;
      }

      print('   ✓ Build directory exists');
      print('   ✓ iOS build artifacts present');

      return true;
    } catch (e) {
      print('   ❌ Build system test error: $e');
      return false;
    }
  }

  /// Test file structure
  Future<bool> _testFileStructure() async {
    try {
      // Check critical files exist
      final criticalFiles = [
        'lib/main.dart',
        'lib/services/enhanced_coach_service.dart',
        'lib/services/coach_checkin_coordinator.dart',
        'lib/services/error_handler_service.dart',
        'lib/services/offline_mode_service.dart',
        'lib/services/app_health_service.dart',
        'lib/services/data_migration_service.dart',
        'lib/services/app_monitoring_service.dart',
        'lib/services/performance_optimization_service.dart',
        'lib/services/feature_flag_service.dart',
        'lib/services/user_feedback_service.dart',
        'pubspec.yaml',
      ];

      for (final filePath in criticalFiles) {
        final file = File(filePath);
        if (!file.existsSync()) {
          print('   ❌ Critical file missing: $filePath');
          return false;
        }
      }

      print('   ✓ All critical files present');
      print('   ✓ Service architecture complete');

      return true;
    } catch (e) {
      print('   ❌ File structure test error: $e');
      return false;
    }
  }

  /// Test configuration
  Future<bool> _testConfiguration() async {
    try {
      // Check pubspec.yaml
      final pubspecFile = File('pubspec.yaml');
      if (!pubspecFile.existsSync()) {
        print('   ❌ pubspec.yaml not found');
        return false;
      }

      final pubspecContent = await pubspecFile.readAsString();

      // Check for critical dependencies
      final criticalDeps = [
        'flutter_secure_storage',
        'http',
        'flutter_dotenv',
        'flutter_local_notifications',
      ];

      for (final dep in criticalDeps) {
        if (!pubspecContent.contains(dep)) {
          print('   ❌ Missing critical dependency: $dep');
          return false;
        }
      }

      // Check .env file exists
      final envFile = File('.env');
      if (!envFile.existsSync()) {
        print('   ⚠️ .env file not found (expected for production)');
      }

      print('   ✓ pubspec.yaml configuration valid');
      print('   ✓ Critical dependencies present');

      return true;
    } catch (e) {
      print('   ❌ Configuration test error: $e');
      return false;
    }
  }

  /// Print test summary
  void _printSummary() {
    print('📊 Test Summary:');
    print('   ✅ Passed: $_passedTests');
    print('   ❌ Failed: $_failedTests');
    print('   📈 Success Rate: ${((_passedTests / (_passedTests + _failedTests)) * 100).toStringAsFixed(1)}%');

    if (_failureReasons.isNotEmpty) {
      print('\n💥 Failed Tests:');
      for (final reason in _failureReasons) {
        print('   • $reason');
      }
    }
  }
}
