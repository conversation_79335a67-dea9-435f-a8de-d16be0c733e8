// 📁 test/transcript_integration_test.dart

import 'package:flutter_test/flutter_test.dart';
import 'package:maxed_out_life/services/transcript_service.dart';
import 'package:maxed_out_life/prompts/coachp.dart';

void main() {
  group('Enhanced AI Coach Integration Tests', () {
    
    test('Transcript Service - Load Health Category Insights', () async {
      print('🧪 Testing transcript service for health category...');
      
      try {
        final healthInsights = await TranscriptService.getInsightsForCategory('health');
        
        // Should contain insights from <PERSON> transcript
        expect(healthInsights.isNotEmpty, true);
        expect(healthInsights.contains('40%'), true, reason: 'Should contain Goggins 40% rule');
        expect(healthInsights.contains('Mental Toughness'), true, reason: 'Should contain mental toughness concepts');
        
        print('✅ Health insights loaded successfully!');
        print('Sample insight: ${healthInsights.substring(0, 200)}...');
        
      } catch (e) {
        print('❌ Failed to load health insights: $e');
        rethrow;
      }
    });
    
    test('Enhanced Coach Prompts - Health Coach with Creator Knowledge', () async {
      print('🧪 Testing enhanced health coach prompt...');
      
      try {
        final healthPrompt = await CoachPrompts.health;
        
        // Should contain base personality
        expect(healthPrompt.contains('Maxed Out Life Coach for Health'), true);
        expect(healthPrompt.contains('high-performance longevity'), true);
        
        // Should contain creator insights
        expect(healthPrompt.contains('EXPERT INSIGHTS'), true);
        expect(healthPrompt.contains('40%'), true, reason: 'Should include Goggins insights');
        
        print('✅ Enhanced health coach prompt working!');
        print('Prompt length: ${healthPrompt.length} characters');
        
      } catch (e) {
        print('❌ Failed to generate enhanced prompt: $e');
        rethrow;
      }
    });
    
    test('Training Data Summary', () async {
      print('🧪 Testing training data summary...');
      
      try {
        final summary = await TranscriptService.getTrainingDataSummary();
        
        expect(summary.containsKey('health'), true);
        expect(summary['health']['transcriptCount'], greaterThan(0));
        expect(summary['health']['creators'], isNotEmpty);
        
        print('✅ Training data summary working!');
        print('Health category: ${summary['health']['transcriptCount']} transcripts');
        print('Creators: ${summary['health']['creators']}');
        
      } catch (e) {
        print('❌ Failed to get training data summary: $e');
        rethrow;
      }
    });
    
    test('Creator Knowledge Extraction', () async {
      print('🧪 Testing creator knowledge extraction...');
      
      try {
        final creators = await TranscriptService.getCreatorNamesForCategory('health');
        
        expect(creators.contains('David Goggins'), true, reason: 'Should find David Goggins');
        
        print('✅ Creator extraction working!');
        print('Health creators: $creators');
        
      } catch (e) {
        print('❌ Failed to extract creator names: $e');
        rethrow;
      }
    });
  });
}
