// test/spiritual_safety_test.dart

import 'package:flutter_test/flutter_test.dart';
import 'package:maxed_out_life/services/user_spiritual_profile_service.dart';
import 'package:maxed_out_life/services/spiritual_safety_validator.dart';

/// CRITICAL: Comprehensive tests for spiritual safety system
///
/// These tests ensure we NEVER make religious mistakes.
/// ZERO TOLERANCE for religious content errors.
void main() {
  setUpAll(() {
    // Initialize Flutter binding for secure storage
    TestWidgetsFlutterBinding.ensureInitialized();
  });

  group('🛡️ Spiritual Safety System Tests', () {

    setUp(() {
      // Clear cache before each test
      UserSpiritualProfileService.clearCache();
    });
    
    group('User Spiritual Profile Service', () {
      
      test('Should create safe default profile when none exists', () async {
        final profile = await UserSpiritualProfileService.getUserSpiritualProfile('test_user');
        
        expect(profile.isProfileSet, false);
        expect(profile.denomination, SpiritualDenomination.preferNotToSay);
        expect(profile.comfortLevel, ComfortLevel.notSet);
      });
      
      test('Should save and retrieve Christian profile correctly', () async {
        final christianProfile = SpiritualProfile(
          userId: 'christian_user',
          denomination: SpiritualDenomination.christian,
          comfortLevel: ComfortLevel.veryComfortable,
          isProfileSet: true,
          lastUpdated: DateTime.now(),
        );
        
        final saved = await UserSpiritualProfileService.saveSpiritualProfile(christianProfile);
        expect(saved, true);
        
        final retrieved = await UserSpiritualProfileService.getUserSpiritualProfile('christian_user');
        expect(retrieved.denomination, SpiritualDenomination.christian);
        expect(retrieved.comfortLevel, ComfortLevel.veryComfortable);
        expect(retrieved.isProfileSet, true);
      });
      
      test('Should save and retrieve secular profile correctly', () async {
        final secularProfile = SpiritualProfile(
          userId: 'secular_user',
          denomination: SpiritualDenomination.secular,
          comfortLevel: ComfortLevel.comfortable,
          isProfileSet: true,
          lastUpdated: DateTime.now(),
        );
        
        final saved = await UserSpiritualProfileService.saveSpiritualProfile(secularProfile);
        expect(saved, true);
        
        final retrieved = await UserSpiritualProfileService.getUserSpiritualProfile('secular_user');
        expect(retrieved.denomination, SpiritualDenomination.secular);
        expect(retrieved.isProfileSet, true);
      });
      
      test('Should block religious content for unset profiles', () async {
        final isAppropriate = await UserSpiritualProfileService.isReligiousContentAppropriate(
          'unset_user',
          ReligiousContentType.christianBibleVerse,
        );
        
        expect(isAppropriate, false);
      });
      
      test('Should block religious content for secular users', () async {
        final secularProfile = SpiritualProfile(
          userId: 'secular_user_2',
          denomination: SpiritualDenomination.secular,
          comfortLevel: ComfortLevel.comfortable,
          isProfileSet: true,
          lastUpdated: DateTime.now(),
        );
        
        await UserSpiritualProfileService.saveSpiritualProfile(secularProfile);
        
        final isAppropriate = await UserSpiritualProfileService.isReligiousContentAppropriate(
          'secular_user_2',
          ReligiousContentType.christianBibleVerse,
        );
        
        expect(isAppropriate, false);
      });
      
      test('Should allow appropriate religious content for Christian users', () async {
        final christianProfile = SpiritualProfile(
          userId: 'christian_user_2',
          denomination: SpiritualDenomination.christian,
          comfortLevel: ComfortLevel.veryComfortable,
          isProfileSet: true,
          lastUpdated: DateTime.now(),
        );
        
        await UserSpiritualProfileService.saveSpiritualProfile(christianProfile);
        
        final isAppropriate = await UserSpiritualProfileService.isReligiousContentAppropriate(
          'christian_user_2',
          ReligiousContentType.christianBibleVerse,
        );
        
        expect(isAppropriate, true);
      });
      
      test('Should block inappropriate cross-religious content', () async {
        final christianProfile = SpiritualProfile(
          userId: 'christian_user_3',
          denomination: SpiritualDenomination.christian,
          comfortLevel: ComfortLevel.veryComfortable,
          isProfileSet: true,
          lastUpdated: DateTime.now(),
        );
        
        await UserSpiritualProfileService.saveSpiritualProfile(christianProfile);
        
        final isAppropriate = await UserSpiritualProfileService.isReligiousContentAppropriate(
          'christian_user_3',
          ReligiousContentType.islamicWisdom,
        );
        
        expect(isAppropriate, false);
      });
    });
    
    group('Spiritual Safety Validator', () {
      
      test('Should approve content with no religious references', () async {
        final result = await SpiritualSafetyValidator.validateSpiritualContent(
          userId: 'any_user',
          content: 'Great job on your workout today! Keep up the momentum.',
          coachName: 'Kai-Tholo',
          category: 'Health',
        );
        
        expect(result.isApproved, true);
      });
      
      test('Should block Christian content for unset profile', () async {
        final result = await SpiritualSafetyValidator.validateSpiritualContent(
          userId: 'unset_user_validator',
          content: 'As Jesus said in Matthew 5:16, let your light shine before others.',
          coachName: 'Zen',
          category: 'Connection',
        );
        
        expect(result.isBlocked, true);
        expect(result.reason, contains('User spiritual profile not set'));
      });
      
      test('Should block Christian content for secular user', () async {
        final secularProfile = SpiritualProfile(
          userId: 'secular_validator',
          denomination: SpiritualDenomination.secular,
          comfortLevel: ComfortLevel.comfortable,
          isProfileSet: true,
          lastUpdated: DateTime.now(),
        );
        
        await UserSpiritualProfileService.saveSpiritualProfile(secularProfile);
        
        final result = await SpiritualSafetyValidator.validateSpiritualContent(
          userId: 'secular_validator',
          content: 'Remember what the Bible says about perseverance.',
          coachName: 'Zen',
          category: 'Connection',
        );
        
        expect(result.isBlocked, true);
        expect(result.reason, contains('User prefers secular content'));
      });
      
      test('Should approve Christian content for Christian user', () async {
        final christianProfile = SpiritualProfile(
          userId: 'christian_validator',
          denomination: SpiritualDenomination.christian,
          comfortLevel: ComfortLevel.veryComfortable,
          isProfileSet: true,
          lastUpdated: DateTime.now(),
        );
        
        await UserSpiritualProfileService.saveSpiritualProfile(christianProfile);
        
        final result = await SpiritualSafetyValidator.validateSpiritualContent(
          userId: 'christian_validator',
          content: 'As it says in Philippians 4:13, I can do all things through Christ who strengthens me.',
          coachName: 'Zen',
          category: 'Connection',
        );
        
        expect(result.isApproved, true);
      });
      
      test('Should block problematic cross-religious phrases', () async {
        final christianProfile = SpiritualProfile(
          userId: 'christian_validator_2',
          denomination: SpiritualDenomination.christian,
          comfortLevel: ComfortLevel.veryComfortable,
          isProfileSet: true,
          lastUpdated: DateTime.now(),
        );
        
        await UserSpiritualProfileService.saveSpiritualProfile(christianProfile);
        
        final result = await SpiritualSafetyValidator.validateSpiritualContent(
          userId: 'christian_validator_2',
          content: 'Unlike those false prophets, Jesus is the only true way.',
          coachName: 'Zen',
          category: 'Connection',
        );
        
        expect(result.isBlocked, true);
        expect(result.reason, contains('Problematic religious phrases detected'));
      });
      
      test('Should approve stoic wisdom for secular users', () async {
        final secularProfile = SpiritualProfile(
          userId: 'secular_stoic',
          denomination: SpiritualDenomination.secular,
          comfortLevel: ComfortLevel.comfortable,
          isProfileSet: true,
          lastUpdated: DateTime.now(),
        );
        
        await UserSpiritualProfileService.saveSpiritualProfile(secularProfile);
        
        final result = await SpiritualSafetyValidator.validateSpiritualContent(
          userId: 'secular_stoic',
          content: 'As Marcus Aurelius taught, focus on what you can control and accept what you cannot.',
          coachName: 'Zen',
          category: 'Connection',
        );
        
        expect(result.isApproved, true);
      });
    });
    
    group('Spiritual Wisdom Guidance', () {
      
      test('Should provide Christian guidance for Christian users', () async {
        final christianProfile = SpiritualProfile(
          userId: 'christian_guidance',
          denomination: SpiritualDenomination.christian,
          comfortLevel: ComfortLevel.veryComfortable,
          isProfileSet: true,
          lastUpdated: DateTime.now(),
        );
        
        await UserSpiritualProfileService.saveSpiritualProfile(christianProfile);
        
        final guidance = await UserSpiritualProfileService.getSpiritualWisdomGuidance('christian_guidance');
        
        expect(guidance.canShareReligiousContent, true);
        expect(guidance.allowBibleVerses, true);
        expect(guidance.allowPrayer, true);
        expect(guidance.appropriateWisdomTypes, contains('christian'));
        expect(guidance.appropriateWisdomTypes, contains('biblical'));
      });
      
      test('Should provide secular guidance for secular users', () async {
        final secularProfile = SpiritualProfile(
          userId: 'secular_guidance',
          denomination: SpiritualDenomination.secular,
          comfortLevel: ComfortLevel.comfortable,
          isProfileSet: true,
          lastUpdated: DateTime.now(),
        );
        
        await UserSpiritualProfileService.saveSpiritualProfile(secularProfile);
        
        final guidance = await UserSpiritualProfileService.getSpiritualWisdomGuidance('secular_guidance');
        
        expect(guidance.canShareReligiousContent, false);
        expect(guidance.allowBibleVerses, false);
        expect(guidance.allowPrayer, false);
        expect(guidance.appropriateWisdomTypes, contains('stoic'));
        expect(guidance.fallbackWisdomType, 'stoic');
      });
      
      test('Should provide universal guidance for unset profiles', () async {
        final guidance = await UserSpiritualProfileService.getSpiritualWisdomGuidance('unset_guidance');
        
        expect(guidance.canShareReligiousContent, false);
        expect(guidance.allowBibleVerses, false);
        expect(guidance.allowPrayer, false);
        expect(guidance.appropriateWisdomTypes, contains('universal'));
        expect(guidance.fallbackWisdomType, 'stoic');
      });
    });
    
    group('Edge Cases and Error Handling', () {
      
      test('Should handle validation errors gracefully', () async {
        final result = await SpiritualSafetyValidator.validateSpiritualContent(
          userId: '', // Invalid user ID
          content: 'Test content',
          coachName: 'TestCoach',
          category: 'Test',
        );
        
        expect(result.isBlocked, true);
        expect(result.reason, contains('Validation error'));
      });
      
      test('Should handle profile loading errors gracefully', () async {
        final profile = await UserSpiritualProfileService.getUserSpiritualProfile('');
        
        expect(profile.isProfileSet, false);
        expect(profile.denomination, SpiritualDenomination.preferNotToSay);
      });
    });
  });
}
