// test/integration/app_integration_test.dart

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:maxed_out_life/main.dart' as app;
import 'package:maxed_out_life/models/user_model.dart';
import 'package:maxed_out_life/services/app_health_service.dart';
import 'package:maxed_out_life/services/data_migration_service.dart';
import 'package:maxed_out_life/services/error_handler_service.dart';
import 'package:maxed_out_life/services/offline_mode_service.dart';
import 'package:maxed_out_life/services/coach_checkin_coordinator.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('MXD App Integration Tests', () {
    
    group('App Startup & Health Checks', () {
      testWidgets('App starts successfully with health checks', (WidgetTester tester) async {
        // Start the app
        app.main();
        await tester.pumpAndSettle(const Duration(seconds: 5));

        // Verify app loaded
        expect(find.byType(MaterialApp), findsOneWidget);
        
        // Check health status
        final healthStats = await ErrorHandlerService.getErrorStatistics();
        expect(healthStats['networkAvailable'], isA<bool>());
        expect(healthStats['apiConfigValid'], isA<bool>());
        
        print('✅ App startup health check passed');
      });

      testWidgets('Health service validates API configuration', (WidgetTester tester) async {
        final healthResult = await AppHealthService.performStartupHealthCheck();
        
        expect(healthResult.storageAccessible, isTrue);
        expect(healthResult.assetsValid, isTrue);
        expect(healthResult.overallHealth, isNot(HealthStatus.critical));
        
        print('✅ Health service validation passed');
      });
    });

    group('Data Migration System', () {
      testWidgets('Data migration runs without errors', (WidgetTester tester) async {
        // Clear any existing migration data
        await DataMigrationService.clearMigrationData();
        
        // Perform migration
        final migrationResult = await DataMigrationService.performMigrationIfNeeded();
        
        expect(migrationResult.success, isTrue);
        expect(migrationResult.toVersion, DataMigrationService.currentDataVersion);
        
        // Validate data integrity
        final integrityValid = await DataMigrationService.validateDataIntegrity();
        expect(integrityValid, isTrue);
        
        print('✅ Data migration system passed');
      });

      testWidgets('Migration status provides accurate information', (WidgetTester tester) async {
        final status = await DataMigrationService.getMigrationStatus();
        
        expect(status['currentVersion'], isA<int>());
        expect(status['targetVersion'], isA<int>());
        expect(status['needsMigration'], isA<bool>());
        expect(status['dataIntegrityValid'], isA<bool>());
        
        print('✅ Migration status check passed');
      });
    });

    group('Error Handling & Offline Mode', () {
      testWidgets('Error handler provides fallback responses', (WidgetTester tester) async {
        // Test error handling with a failing operation
        final result = await ErrorHandlerService.executeWithRetry<String>(
          operation: () async {
            throw Exception('Test error');
          },
          fallback: () => 'Fallback response',
          operationName: 'test_operation',
          maxRetries: 1,
        );
        
        expect(result, equals('Fallback response'));
        
        // Check error statistics
        final stats = await ErrorHandlerService.getErrorStatistics();
        expect(stats['totalErrors'], greaterThan(0));
        
        print('✅ Error handling system passed');
      });

      testWidgets('Offline mode provides intelligent responses', (WidgetTester tester) async {
        // Test offline response generation
        final offlineResponse = await OfflineModeService.getOfflineCoachResponse(
          category: 'Health',
          userPrompt: 'I need help with my fitness goals',
          userGender: 'male',
          username: 'TestUser',
        );
        
        expect(offlineResponse, isNotEmpty);
        expect(offlineResponse.toLowerCase(), contains('testuser'));
        expect(offlineResponse.toLowerCase(), anyOf([
          contains('health'),
          contains('fitness'),
          contains('offline'),
        ]));
        
        print('✅ Offline mode system passed');
      });
    });

    group('Authentication Flow', () {
      testWidgets('User model handles all required fields', (WidgetTester tester) async {
        // Create test user
        final testUser = User.blank(
          id: 'test_user_id',
          username: 'testuser123',
        ).copyWith(
          gender: 'male',
          customCategories: ['Fitness', 'Learning'],
          email: '<EMAIL>',
          passwordHash: 'hashed_password',
          isEmailVerified: true,
          klaviyoSubscribed: false,
        );
        
        // Verify user model integrity
        expect(testUser.username, equals('testuser123'));
        expect(testUser.gender, equals('male'));
        expect(testUser.customCategories.length, equals(2));
        expect(testUser.email, equals('<EMAIL>'));
        expect(testUser.isEmailVerified, isTrue);
        
        print('✅ User model validation passed');
      });

      testWidgets('Gender assignment works for non-gender users', (WidgetTester tester) async {
        final testUser = User.blank(
          id: 'nonbinary_user_id',
          username: 'nonbinaryuser',
        ).copyWith(
          gender: 'non-gender',
          customCategories: ['Health', 'Wealth'],
          assignedCoaches: {
            'Health': 'male',
            'Wealth': 'female',
            'Custom Category 1': 'male',
            'Custom Category 2': 'female',
          },
        );
        
        expect(testUser.gender, equals('non-gender'));
        expect(testUser.assignedCoaches, isNotNull);
        expect(testUser.assignedCoaches!['Health'], equals('male'));
        expect(testUser.assignedCoaches!['Wealth'], equals('female'));
        
        print('✅ Non-gender user assignment passed');
      });
    });

    group('Coach Check-in System', () {
      testWidgets('Check-in coordinator initializes properly', (WidgetTester tester) async {
        final testUser = User.blank(
          id: 'checkin_test_id',
          username: 'checkintest',
        ).copyWith(
          gender: 'female',
          customCategories: ['Mindfulness', 'Career'],
        );
        
        // Initialize coordinator
        await CoachCheckinCoordinator.initialize(testUser);
        
        // Check status
        final status = CoachCheckinCoordinator.getStatus();
        expect(status['isInitialized'], isTrue);
        expect(status['hasUser'], isTrue);
        expect(status['username'], equals('checkintest'));
        
        // Stop coordinator
        CoachCheckinCoordinator.stop();
        
        print('✅ Coach check-in coordinator passed');
      });

      testWidgets('Check-in eligibility system works correctly', (WidgetTester tester) async {
        final testUser = User.blank(
          id: 'eligibility_test_id',
          username: 'eligibilitytest',
        ).copyWith(
          gender: 'male',
          customCategories: ['Health', 'Wealth'],
        );
        
        await CoachCheckinCoordinator.initialize(testUser);
        
        // Test eligibility (should be true for new user)
        final status = CoachCheckinCoordinator.getStatus();
        expect(status['isInitialized'], isTrue);
        
        // Test notification window
        expect(status['withinNotificationWindow'], isA<bool>());
        
        CoachCheckinCoordinator.stop();
        
        print('✅ Check-in eligibility system passed');
      });
    });

    group('Performance & Memory', () {
      testWidgets('App memory usage stays within reasonable bounds', (WidgetTester tester) async {
        // Start the app
        app.main();
        await tester.pumpAndSettle(const Duration(seconds: 3));
        
        // Simulate user interactions
        for (int i = 0; i < 5; i++) {
          await tester.tap(find.byType(MaterialApp));
          await tester.pumpAndSettle(const Duration(milliseconds: 500));
        }
        
        // Memory usage test would require platform-specific implementation
        // For now, we'll just verify the app is still responsive
        expect(find.byType(MaterialApp), findsOneWidget);
        
        print('✅ Memory usage test passed');
      });

      testWidgets('Error handling doesn\'t cause memory leaks', (WidgetTester tester) async {
        // Generate multiple errors to test cleanup
        for (int i = 0; i < 10; i++) {
          await ErrorHandlerService.executeWithRetry<String>(
            operation: () async {
              throw Exception('Test error $i');
            },
            fallback: () => 'Fallback $i',
            operationName: 'memory_test_$i',
            maxRetries: 1,
          );
        }
        
        // Verify error statistics are reasonable
        final stats = await ErrorHandlerService.getErrorStatistics();
        expect(stats['totalErrors'], greaterThan(0));
        expect(stats['totalErrors'], lessThan(1000)); // Reasonable upper bound
        
        print('✅ Error handling memory test passed');
      });
    });

    group('Network & Connectivity', () {
      testWidgets('Network connectivity check works', (WidgetTester tester) async {
        final isConnected = await ErrorHandlerService.isNetworkAvailable();
        expect(isConnected, isA<bool>());
        
        print('✅ Network connectivity check passed: $isConnected');
      });

      testWidgets('Offline mode detection works', (WidgetTester tester) async {
        final shouldUseOffline = await OfflineModeService.shouldUseOfflineMode();
        expect(shouldUseOffline, isA<bool>());
        
        print('✅ Offline mode detection passed: $shouldUseOffline');
      });
    });

    group('Data Persistence', () {
      testWidgets('Secure storage operations work correctly', (WidgetTester tester) async {
        // Test through error handler service (which uses secure storage)
        final stats1 = await ErrorHandlerService.getErrorStatistics();
        expect(stats1, isA<Map<String, dynamic>>());
        
        // Generate an error to test storage
        await ErrorHandlerService.executeWithRetry<String>(
          operation: () async {
            throw Exception('Storage test error');
          },
          fallback: () => 'Storage test fallback',
          operationName: 'storage_test',
          maxRetries: 1,
        );
        
        // Verify error was stored
        final stats2 = await ErrorHandlerService.getErrorStatistics();
        expect(stats2['totalErrors'], greaterThanOrEqualTo(stats1['totalErrors']));
        
        print('✅ Data persistence test passed');
      });
    });

    group('User Experience', () {
      testWidgets('App responds to user interactions', (WidgetTester tester) async {
        app.main();
        await tester.pumpAndSettle(const Duration(seconds: 3));
        
        // Find any interactive elements
        final materialApp = find.byType(MaterialApp);
        expect(materialApp, findsOneWidget);
        
        // Test basic interaction
        await tester.tap(materialApp);
        await tester.pumpAndSettle();
        
        // App should still be responsive
        expect(find.byType(MaterialApp), findsOneWidget);
        
        print('✅ User interaction test passed');
      });
    });
  });
}

/// Helper function to create test user
User createTestUser({
  String username = 'testuser',
  String gender = 'male',
  List<String>? customCategories,
  Map<String, String>? assignedCoaches,
}) {
  return User.blank(
    id: 'test_${username}_id',
    username: username,
  ).copyWith(
    gender: gender,
    customCategories: customCategories ?? ['Health', 'Wealth'],
    assignedCoaches: assignedCoaches,
  );
}

/// Helper function to wait for async operations
Future<void> waitForAsyncOperations() async {
  await Future.delayed(const Duration(milliseconds: 100));
}

/// Test runner for CI/CD integration
class TestRunner {
  static Future<bool> runAllTests() async {
    try {
      print('🧪 Starting comprehensive test suite...');

      // Health checks
      final healthResult = await AppHealthService.performStartupHealthCheck();
      if (healthResult.overallHealth == HealthStatus.critical) {
        print('❌ Health check failed - aborting tests');
        return false;
      }

      // Data migration
      final migrationResult = await DataMigrationService.performMigrationIfNeeded();
      if (!migrationResult.success) {
        print('❌ Data migration failed - aborting tests');
        return false;
      }

      // Error handling
      final errorTest = await _testErrorHandling();
      if (!errorTest) {
        print('❌ Error handling test failed');
        return false;
      }

      // Offline mode
      final offlineTest = await _testOfflineMode();
      if (!offlineTest) {
        print('❌ Offline mode test failed');
        return false;
      }

      print('✅ All critical tests passed');
      return true;
    } catch (e) {
      print('❌ Test suite failed: $e');
      return false;
    }
  }

  static Future<bool> _testErrorHandling() async {
    try {
      final result = await ErrorHandlerService.executeWithRetry<String>(
        operation: () async => throw Exception('Test'),
        fallback: () => 'Success',
        operationName: 'test',
        maxRetries: 1,
      );
      return result == 'Success';
    } catch (e) {
      return false;
    }
  }

  static Future<bool> _testOfflineMode() async {
    try {
      final response = await OfflineModeService.getOfflineCoachResponse(
        category: 'Health',
        userPrompt: 'Test prompt',
        userGender: 'male',
        username: 'test',
      );
      return response.isNotEmpty;
    } catch (e) {
      return false;
    }
  }
}
