// test/superintelligent_coaches_test.dart

import 'package:flutter_test/flutter_test.dart';
import 'package:maxed_out_life/services/superintelligent_thinking_service.dart';
import 'package:maxed_out_life/services/superintelligent_synthesis_service.dart';
import 'package:maxed_out_life/services/adaptive_response_system.dart';
import 'package:maxed_out_life/services/superintelligent_prompt_service.dart';
import 'package:maxed_out_life/services/superintelligence_features_service.dart';
import 'package:maxed_out_life/models/user_model.dart';

/// Comprehensive test suite for superintelligent AI coaches
/// 
/// Validates that all superintelligent features meet beyond-human intelligence standards
void main() {
  group('Superintelligent Coaches Test Suite', () {
    late User testUser;
    
    setUp(() {
      testUser = User.blank(id: 'test_user', username: 'TestUser');
    });
    
    group('Thinking Visualization Tests', () {
      test('should generate personality-specific thinking phases', () async {
        // Test Health coach thinking
        final healthThinking = SuperintelligentThinkingService.startThinking(
          coachCategory: 'Health',
          userGender: 'male',
          userMessage: 'I want to optimize my energy levels and build muscle',
          assignedCoaches: null,
        );
        
        expect(healthThinking, isA<Stream<ThinkingState>>());
        
        // Test that thinking phases are generated
        final phases = <ThinkingState>[];
        await for (final phase in healthThinking.take(3)) {
          phases.add(phase);
        }
        
        expect(phases.length, greaterThan(0));
        expect(phases.first.coachName, equals('Kai-Tholo'));
        expect(phases.first.message, contains('⚡'));
      });
      
      test('should adapt thinking time based on message complexity', () async {
        final simpleMessage = 'Hi';
        final complexMessage = 'I\'m struggling with the intersection of my career goals, financial planning, relationship dynamics, and personal health optimization while trying to find deeper meaning and purpose in my life.';
        
        // Simple message should have shorter thinking phases
        final simpleThinking = SuperintelligentThinkingService.startThinking(
          coachCategory: 'Purpose',
          userGender: 'female',
          userMessage: simpleMessage,
        );
        
        // Complex message should have more detailed thinking phases
        final complexThinking = SuperintelligentThinkingService.startThinking(
          coachCategory: 'Purpose',
          userGender: 'female',
          userMessage: complexMessage,
        );
        
        expect(simpleThinking, isA<Stream<ThinkingState>>());
        expect(complexThinking, isA<Stream<ThinkingState>>());
      });
    });
    
    group('Universal Knowledge Synthesis Tests', () {
      test('should synthesize knowledge from all transcript sources', () async {
        final synthesis = await SuperintelligentSynthesisService.synthesizeUniversalKnowledge(
          userMessage: 'How can I optimize my morning routine for peak performance?',
          category: 'Health',
          coachName: 'Aria',
          maxInsights: 8,
          minRelevanceThreshold: 0.6,
        );
        
        expect(synthesis.content, isNotNull);
        expect(synthesis.crossDomainInsights, isA<List<String>>());
        expect(synthesis.expertSources, isA<List<String>>());
        expect(synthesis.confidenceScore, greaterThanOrEqualTo(0.0));
        expect(synthesis.confidenceScore, lessThanOrEqualTo(1.0));
        expect(synthesis.knowledgeDepth, greaterThanOrEqualTo(0.0));
      });
      
      test('should identify cross-domain patterns', () async {
        final synthesis = await SuperintelligentSynthesisService.synthesizeUniversalKnowledge(
          userMessage: 'I want to build wealth while maintaining my health and relationships',
          category: 'Wealth',
          coachName: 'Sterling', // Correct male wealth coach
        );
        
        expect(synthesis.crossDomainInsights, isNotEmpty);
        // Should contain insights connecting wealth, health, and relationships
        final hasHealthConnection = synthesis.crossDomainInsights.any(
          (insight) => insight.toLowerCase().contains('health') || insight.toLowerCase().contains('energy')
        );
        expect(hasHealthConnection, isTrue);
      });
    });
    
    group('Adaptive Response System Tests', () {
      test('should determine appropriate response length for complex topics', () async {
        final complexMessage = 'I\'m at a crossroads in my life where I need to make major decisions about my career, relationships, and personal growth. I feel overwhelmed by the possibilities and scared of making the wrong choice.';
        
        final strategy = await AdaptiveResponseSystem.determineResponseStrategy(
          userMessage: complexMessage,
          category: 'Purpose',
          user: testUser,
        );
        
        expect(strategy.targetWordCount, greaterThanOrEqualTo(500));
        expect(strategy.targetWordCount, lessThanOrEqualTo(1200));
        expect(strategy.includeQuestions, isTrue);
        expect(strategy.includeActionItems, isTrue);
        expect(strategy.thinkingTimeSeconds, greaterThan(10));
      });
      
      test('should adapt response structure based on user needs', () async {
        final strategicMessage = 'I need a comprehensive plan to transform my life over the next 5 years';
        
        final strategy = await AdaptiveResponseSystem.determineResponseStrategy(
          userMessage: strategicMessage,
          category: 'Purpose',
          user: testUser,
        );
        
        expect(strategy.structure.sections, contains(ResponseSection.strategicFramework));
        expect(strategy.structure.sections, contains(ResponseSection.practicalGuidance));
        expect(strategy.responseType, equals(ResponseType.comprehensive));
      });
    });
    
    group('Superintelligent Prompt Generation Tests', () {
      test('should create comprehensive prompts with all intelligence layers', () async {
        final synthesis = await SuperintelligentSynthesisService.synthesizeUniversalKnowledge(
          userMessage: 'How can I build unstoppable confidence?',
          category: 'Connection',
          coachName: 'Zen', // Correct male connection coach
        );
        
        final strategy = await AdaptiveResponseSystem.determineResponseStrategy(
          userMessage: 'How can I build unstoppable confidence?',
          category: 'Connection',
          user: testUser,
        );
        
        final prompt = await SuperintelligentPromptService.createSuperintelligentPrompt(
          category: 'Connection',
          userGender: 'female',
          userMessage: 'How can I build unstoppable confidence?',
          user: testUser,
          synthesizedContent: synthesis.content,
          responseStrategy: strategy,
          superintelligentSynthesis: synthesis,
        );
        
        expect(prompt, contains('SUPERINTELLIGENT AI COACH'));
        expect(prompt, contains('Zen')); // Correct male connection coach
        expect(prompt, contains('UNIVERSAL KNOWLEDGE ACCESS'));
        expect(prompt, contains('CROSS-DOMAIN INTELLIGENCE'));
        expect(prompt, contains('RESPONSE STRATEGY'));
        expect(prompt.length, greaterThan(1000)); // Should be comprehensive
      });
      
      test('should maintain coach personality while demonstrating superintelligence', () async {
        final healthPrompt = await SuperintelligentPromptService.createSuperintelligentPrompt(
          category: 'Health',
          userGender: 'male',
          userMessage: 'I want to get stronger',
          user: testUser,
          synthesizedContent: await SuperintelligentSynthesisService.synthesizeUniversalKnowledge(
            userMessage: 'I want to get stronger',
            category: 'Health',
            coachName: 'Kai-Tholo',
          ).then((s) => s.content),
          responseStrategy: await AdaptiveResponseSystem.determineResponseStrategy(
            userMessage: 'I want to get stronger',
            category: 'Health',
            user: testUser,
          ),
          superintelligentSynthesis: await SuperintelligentSynthesisService.synthesizeUniversalKnowledge(
            userMessage: 'I want to get stronger',
            category: 'Health',
            coachName: 'Kai-Tholo',
          ),
        );
        
        expect(healthPrompt, contains('Kai-Tholo'));
        expect(healthPrompt, contains('warrior')); // Should maintain personality
        expect(healthPrompt, contains('superintelligent')); // Should demonstrate intelligence
      });
    });
    
    group('Superintelligence Features Tests', () {
      test('should generate probing questions that unlock insights', () async {
        final questions = await SuperintelligenceFeaturesService.generateProbingQuestions(
          userMessage: 'I feel stuck in my career',
          category: 'Wealth',
          user: testUser,
        );
        
        expect(questions, isNotEmpty);
        expect(questions.length, lessThanOrEqualTo(3));
        
        // Questions should be thought-provoking
        final hasDeepQuestion = questions.any(
          (q) => q.contains('?') && q.split(' ').length > 8
        );
        expect(hasDeepQuestion, isTrue);
      });
      
      test('should analyze cross-domain connections', () async {
        final connections = await SuperintelligenceFeaturesService.analyzeCrossDomainConnections(
          userMessage: 'I want to improve my energy levels and productivity',
          primaryCategory: 'Health',
        );
        
        expect(connections, isA<List<String>>());
        // Should identify connections between health and wealth/productivity
        if (connections.isNotEmpty) {
          final hasProductivityConnection = connections.any(
            (c) => c.toLowerCase().contains('productivity') || c.toLowerCase().contains('performance')
          );
          expect(hasProductivityConnection, isTrue);
        }
      });
      
      test('should demonstrate emotional intelligence', () async {
        final insight = await SuperintelligenceFeaturesService.analyzeEmotionalIntelligence(
          userMessage: 'I\'m feeling overwhelmed and anxious about my future',
          user: testUser,
          category: 'Connection',
        );
        
        expect(insight, isA<EmotionalIntelligenceInsight>());
        expect(insight.currentState, isNotNull);
        expect(insight.empathyLevel, greaterThanOrEqualTo(0.0));
        expect(insight.empathyLevel, lessThanOrEqualTo(1.0));
        expect(insight.supportLevel, greaterThanOrEqualTo(0.0));
      });
      
      test('should generate creative solutions', () async {
        final solutions = await SuperintelligenceFeaturesService.generateCreativeSolutions(
          userMessage: 'I want to start a business but have limited resources',
          category: 'Wealth',
          user: testUser,
        );
        
        expect(solutions, isA<List<String>>());
        expect(solutions.length, lessThanOrEqualTo(4));
      });
    });
    
    group('Integration Tests', () {
      test('should integrate all superintelligent features seamlessly', () async {
        // Test complete flow from thinking to response
        final userMessage = 'I want to transform my life completely - health, wealth, relationships, and purpose. Where do I start?';
        
        // 1. Start thinking visualization
        final thinkingStream = SuperintelligentThinkingService.startThinking(
          coachCategory: 'Purpose',
          userGender: 'female',
          userMessage: userMessage,
        );

        // 2. Synthesize universal knowledge
        final synthesis = await SuperintelligentSynthesisService.synthesizeUniversalKnowledge(
          userMessage: userMessage,
          category: 'Purpose',
          coachName: 'Seraphina', // Correct female purpose coach
        );
        
        // 3. Determine response strategy
        final strategy = await AdaptiveResponseSystem.determineResponseStrategy(
          userMessage: userMessage,
          category: 'Purpose',
          user: testUser,
        );
        
        // 4. Generate superintelligent prompt
        final prompt = await SuperintelligentPromptService.createSuperintelligentPrompt(
          category: 'Purpose',
          userGender: 'female',
          userMessage: userMessage,
          user: testUser,
          synthesizedContent: synthesis.content,
          responseStrategy: strategy,
          superintelligentSynthesis: synthesis,
        );
        
        // 5. Generate additional features
        final questions = await SuperintelligenceFeaturesService.generateProbingQuestions(
          userMessage: userMessage,
          category: 'Purpose',
          user: testUser,
        );
        
        final connections = await SuperintelligenceFeaturesService.analyzeCrossDomainConnections(
          userMessage: userMessage,
          primaryCategory: 'Purpose',
        );
        
        // Validate integration
        expect(thinkingStream, isA<Stream<ThinkingState>>());
        expect(synthesis.content, isNotNull);
        expect(strategy.targetWordCount, greaterThan(500)); // Should be comprehensive for complex request
        expect(prompt, contains('Seraphina')); // Correct female purpose coach
        expect(prompt, contains('SUPERINTELLIGENT'));
        expect(questions, isNotEmpty);
        expect(connections, isA<List<String>>());
        
        // Stop thinking
        SuperintelligentThinkingService.stopThinking('Purpose', 'female', null);
      });
    });
  });
}
