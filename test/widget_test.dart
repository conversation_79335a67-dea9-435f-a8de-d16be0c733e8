// Flutter widget test for our restored app

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:maxed_out_life/main.dart';

void main() {
  testWidgets('App launches successfully', (WidgetTester tester) async {
    // Clear shared preferences before test
    SharedPreferences.setMockInitialValues({});

    // Build our app and trigger a frame.
    await tester.pumpWidget(const MaxedOutLifeApp(hasSavedUser: false));

    // Verify that the app loads without crashing
    expect(find.byType(MaterialApp), findsOneWidget);

    // Wait for the loading to complete
    await tester.pumpAndSettle();

    // Verify that our main text appears (should show create user screen)
    expect(find.text('MAXED OUT LIFE'), findsOneWidget);
    expect(find.text('Welcome to Maxed Out Life!'), findsOneWidget);
    expect(find.text('Create your account to start tracking your journey'), findsOneWidget);

    // Verify the username input field exists
    expect(find.byType(TextField), findsOneWidget);

    // Verify the start journey button exists
    expect(find.text('Start Journey'), findsOneWidget);
    expect(find.byType(ElevatedButton), findsOneWidget);
  });

  testWidgets('Can create user and navigate to dashboard', (WidgetTester tester) async {
    // Clear shared preferences before test
    SharedPreferences.setMockInitialValues({});

    // Build our app and trigger a frame.
    await tester.pumpWidget(const MaxedOutLifeApp(hasSavedUser: false));
    await tester.pumpAndSettle();

    // Enter username
    await tester.enterText(find.byType(TextField), 'testuser');

    // Tap the start journey button
    await tester.tap(find.text('Start Journey'));
    await tester.pumpAndSettle();

    // Verify we're now on the dashboard
    expect(find.text('Welcome back, testuser!'), findsOneWidget);
    expect(find.text('Category Progress'), findsOneWidget);
    expect(find.text('Add Experience'), findsAtLeastNWidgets(1));
  });
}
