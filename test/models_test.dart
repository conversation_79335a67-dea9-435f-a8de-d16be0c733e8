import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:maxed_out_life/models/user_model.dart';
import 'package:maxed_out_life/models/habit_model.dart';
import 'package:maxed_out_life/models/diary_entry_model.dart';
import 'package:maxed_out_life/models/onboarding_progress.dart';
import 'package:maxed_out_life/quests/north_star_model.dart';

void main() {
  group('Core Models Tests', () {
    test('OnboardingProgress model works correctly', () {
      final progress = OnboardingProgress();
      expect(progress.isComplete, false);
      expect(progress.progressPercentage, 0.0);
      
      final updatedProgress = progress.copyWith(hasCompletedWelcome: true);
      expect(updatedProgress.hasCompletedWelcome, true);
      expect(updatedProgress.progressPercentage, 1/6);
    });

    test('Habit model works correctly', () {
      final habit = Habit.create(
        name: 'Morning Workout',
        description: '30 minutes of exercise',
        color: Colors.green,
      );
      
      expect(habit.name, 'Morning Workout');
      expect(habit.description, '30 minutes of exercise');
      expect(habit.color, Colors.green);
      expect(habit.streak, 0);
      expect(habit.validate(), true);
      expect(habit.isCompletedToday(), false);
    });

    test('DiaryEntry model works correctly', () {
      final entry = DiaryEntry.create(
        category: 'Health',
        note: 'Completed morning workout',
        exp: 50,
      );
      
      expect(entry.category, 'Health');
      expect(entry.note, 'Completed morning workout');
      expect(entry.exp, 50);
      expect(entry.id.isNotEmpty, true);
    });

    test('NorthStarQuest model works correctly', () {
      final quest = NorthStarQuest.create(
        title: 'Learn Flutter',
        summary: 'Become proficient in Flutter development',
        glowColor: Colors.blue,
        icon: '🚀',
        coreValues: ['Growth', 'Learning'],
        category: 'Purpose',
      );
      
      expect(quest.title, 'Learn Flutter');
      expect(quest.summary, 'Become proficient in Flutter development');
      expect(quest.glowColor, Colors.blue);
      expect(quest.icon, '🚀');
      expect(quest.coreValues, ['Growth', 'Learning']);
      expect(quest.category, 'Purpose');
      expect(quest.totalExp, 0);
    });

    test('User model works correctly', () {
      final user = User.blank(id: 'test123', username: 'testuser');
      
      expect(user.id, 'test123');
      expect(user.username, 'testuser');
      expect(user.exp, 0);
      expect(user.level, 1);
      expect(user.rank, 'Novice');
      expect(user.streak, 0);
      expect(user.diaryEntries.isEmpty, true);
      expect(user.dailyHabits.isEmpty, true);
      
      // Test adding experience
      final updatedUser = user.copyWithAddedExp('Health', 100, 'Workout completed');
      expect(updatedUser.categories['Health'], 100);
      expect(updatedUser.diaryEntries.length, 1);
      expect(updatedUser.diaryEntries.first.category, 'Health');
      expect(updatedUser.diaryEntries.first.exp, 100);
    });

    test('User model JSON serialization works', () {
      final user = User.blank(id: 'test123', username: 'testuser');
      final json = user.toJson();
      final deserializedUser = User.fromJson(json);
      
      expect(deserializedUser.id, user.id);
      expect(deserializedUser.username, user.username);
      expect(deserializedUser.exp, user.exp);
      expect(deserializedUser.level, user.level);
    });
  });
}
