// test/unit/services_test.dart

import 'package:flutter_test/flutter_test.dart';
import 'package:maxed_out_life/services/error_handler_service.dart';
import 'package:maxed_out_life/services/offline_mode_service.dart';
import 'package:maxed_out_life/services/app_health_service.dart';
import 'package:maxed_out_life/services/data_migration_service.dart';
import 'package:maxed_out_life/models/user_model.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  // Set up test environment to handle plugin exceptions gracefully
  setUp(() {
    // Tests will handle MissingPluginException gracefully
    // This is expected behavior in unit test environment
  });
  group('Error Handler Service Tests', () {
    test('executeWithRetry returns result on success', () async {
      final result = await ErrorHandlerService.executeWithRetry<String>(
        operation: () async => 'success',
        fallback: () => 'fallback',
        operationName: 'test_success',
        maxRetries: 3,
      );
      
      expect(result, equals('success'));
    });

    test('executeWithRetry uses fallback on failure', () async {
      final result = await ErrorHandlerService.executeWithRetry<String>(
        operation: () async => throw Exception('test error'),
        fallback: () => 'fallback_used',
        operationName: 'test_failure',
        maxRetries: 2,
      );
      
      expect(result, equals('fallback_used'));
    });

    test('getUserFriendlyMessage returns appropriate messages', () {
      final networkError = ErrorHandlerService.getUserFriendlyMessage(
        'network connection failed',
        'coach_response',
      );
      expect(networkError, contains('internet connection'));

      final apiError = ErrorHandlerService.getUserFriendlyMessage(
        'unauthorized access',
        'coach_response',
      );
      expect(apiError, contains('configuration issue'));

      final timeoutError = ErrorHandlerService.getUserFriendlyMessage(
        'request timeout',
        'coach_response',
      );
      expect(timeoutError, contains('took too long'));
    });

    test('network connectivity check works', () async {
      final isConnected = await ErrorHandlerService.isNetworkAvailable();
      expect(isConnected, isA<bool>());
    });

    test('API configuration validation works', () async {
      final isValid = await ErrorHandlerService.validateApiConfiguration();
      expect(isValid, isA<bool>());
    });
  });

  group('Offline Mode Service Tests', () {
    test('shouldUseOfflineMode returns boolean', () async {
      final shouldUse = await OfflineModeService.shouldUseOfflineMode();
      expect(shouldUse, isA<bool>());
    });

    test('getOfflineCoachResponse returns non-empty response', () async {
      final response = await OfflineModeService.getOfflineCoachResponse(
        category: 'Health',
        userPrompt: 'I need help with my fitness goals',
        userGender: 'female',
        username: 'TestUser',
      );
      
      expect(response, isNotEmpty);
      expect(response, contains('TestUser'));
    });

    test('offline responses are category-specific', () async {
      final healthResponse = await OfflineModeService.getOfflineCoachResponse(
        category: 'Health',
        userPrompt: 'fitness question',
        userGender: 'male',
        username: 'User1',
      );
      
      final wealthResponse = await OfflineModeService.getOfflineCoachResponse(
        category: 'Wealth',
        userPrompt: 'money question',
        userGender: 'male',
        username: 'User1',
      );
      
      expect(healthResponse, isNot(equals(wealthResponse)));
      expect(healthResponse.toLowerCase(), anyOf([
        contains('health'),
        contains('fitness'),
        contains('wellness'),
      ]));
      expect(wealthResponse.toLowerCase(), anyOf([
        contains('wealth'),
        contains('financial'),
        contains('money'),
      ]));
    });

    test('offline responses handle different genders', () async {
      final maleResponse = await OfflineModeService.getOfflineCoachResponse(
        category: 'Purpose',
        userPrompt: 'life purpose question',
        userGender: 'male',
        username: 'MaleUser',
      );
      
      final femaleResponse = await OfflineModeService.getOfflineCoachResponse(
        category: 'Purpose',
        userPrompt: 'life purpose question',
        userGender: 'female',
        username: 'FemaleUser',
      );
      
      expect(maleResponse, contains('MaleUser'));
      expect(femaleResponse, contains('FemaleUser'));
    });

    test('offline mode status tracking works', () {
      final isOffline = OfflineModeService.isOfflineMode;
      expect(isOffline, isA<bool>());
    });
  });

  group('App Health Service Tests', () {
    test('health check returns valid result', () async {
      final result = await AppHealthService.performStartupHealthCheck();
      
      expect(result.overallHealth, isA<HealthStatus>());
      expect(result.apiKeyValid, isA<bool>());
      expect(result.networkAvailable, isA<bool>());
      expect(result.storageAccessible, isA<bool>());
      expect(result.assetsValid, isA<bool>());
      expect(result.checkDuration, isA<Duration>());
    });

    test('health check summary is informative', () async {
      final result = await AppHealthService.performStartupHealthCheck();
      final summary = result.toSummaryString();
      
      expect(summary, contains('API:'));
      expect(summary, contains('Network:'));
      expect(summary, contains('Storage:'));
      expect(summary, contains('Assets:'));
    });

    test('health check determines if needed', () async {
      final isNeeded = await AppHealthService.isHealthCheckNeeded();
      expect(isNeeded, isA<bool>());
    });

    test('health summary provides status', () async {
      final summary = await AppHealthService.getHealthSummary();
      expect(summary, isA<Map<String, dynamic>>());
      expect(summary.containsKey('needsHealthCheck'), isTrue);
    });
  });

  group('Data Migration Service Tests', () {
    test('migration performs without errors', () async {
      final result = await DataMigrationService.performMigrationIfNeeded();
      
      expect(result.success, isA<bool>());
      expect(result.fromVersion, isA<int>());
      expect(result.toVersion, isA<int>());
      expect(result.migrations, isA<List<String>>());
    });

    test('data integrity validation works', () async {
      final isValid = await DataMigrationService.validateDataIntegrity();
      expect(isValid, isA<bool>());
    });

    test('migration status provides information', () async {
      final status = await DataMigrationService.getMigrationStatus();
      
      expect(status['currentVersion'], isA<int>());
      expect(status['targetVersion'], isA<int>());
      expect(status['needsMigration'], isA<bool>());
      expect(status['dataIntegrityValid'], isA<bool>());
    });

    test('backup functionality works', () async {
      final backupSuccess = await DataMigrationService.backupUserData();
      expect(backupSuccess, isA<bool>());
    });
  });

  group('User Model Tests', () {
    test('user model creates with required fields', () {
      final user = User.blank(
        id: 'test_user_id',
        username: 'testuser',
      ).copyWith(
        gender: 'male',
        customCategories: ['Health', 'Wealth'],
      );
      
      expect(user.username, equals('testuser'));
      expect(user.gender, equals('male'));
      expect(user.customCategories.length, equals(2));
      expect(user.customCategories[0], equals('Health'));
      expect(user.customCategories[1], equals('Wealth'));
    });

    test('user model handles optional fields', () {
      final user = User.blank(
        id: 'test_user2_id',
        username: 'testuser2',
      ).copyWith(
        gender: 'female',
        customCategories: ['Purpose', 'Connection'],
        email: '<EMAIL>',
        passwordHash: 'hashed_password',
        isEmailVerified: true,
        klaviyoSubscribed: false,
      );
      
      expect(user.email, equals('<EMAIL>'));
      expect(user.passwordHash, equals('hashed_password'));
      expect(user.isEmailVerified, isTrue);
      expect(user.klaviyoSubscribed, isFalse);
    });

    test('user model handles non-gender assignment', () {
      final user = User.blank(
        id: 'nonbinary_user_id',
        username: 'nonbinaryuser',
      ).copyWith(
        gender: 'non-gender',
        customCategories: ['Health', 'Wealth'],
        assignedCoaches: {
          'Health': 'male',
          'Wealth': 'female',
          'Custom Category 1': 'male',
          'Custom Category 2': 'female',
        },
      );
      
      expect(user.gender, equals('non-gender'));
      expect(user.assignedCoaches, isNotNull);
      expect(user.assignedCoaches!['Health'], equals('male'));
      expect(user.assignedCoaches!['Wealth'], equals('female'));
    });

    test('user model validates username constraints', () {
      // Valid usernames
      expect(() => User.blank(id: 'test1', username: 'validuser123').copyWith(gender: 'male', customCategories: []), returnsNormally);
      expect(() => User.blank(id: 'test2', username: 'user_with_underscore').copyWith(gender: 'male', customCategories: []), returnsNormally);
      expect(() => User.blank(id: 'test3', username: 'user-with-dash').copyWith(gender: 'male', customCategories: []), returnsNormally);
      expect(() => User.blank(id: 'test4', username: 'user!').copyWith(gender: 'male', customCategories: []), returnsNormally);
      expect(() => User.blank(id: 'test5', username: 'user*').copyWith(gender: 'male', customCategories: []), returnsNormally);

      // Test username length (should be <= 15 chars based on requirements)
      final longUsername = 'a' * 16;
      expect(() => User.blank(id: 'test6', username: longUsername).copyWith(gender: 'male', customCategories: []), returnsNormally);
      // Note: Actual validation would be in the UI layer, not the model
    });
  });

  group('Integration Tests', () {
    test('services work together correctly', () async {
      // Test the interaction between health check and error handling
      final healthResult = await AppHealthService.performStartupHealthCheck();
      
      if (healthResult.overallHealth == HealthStatus.critical) {
        // Should trigger offline mode
        final shouldUseOffline = await OfflineModeService.shouldUseOfflineMode();
        expect(shouldUseOffline, isTrue);
      }
      
      // Test error handling with health status
      final errorStats = await ErrorHandlerService.getErrorStatistics();
      expect(errorStats['networkAvailable'], isA<bool>());
      expect(errorStats['apiConfigValid'], isA<bool>());
    });

    test('migration and health check integration', () async {
      // Perform migration
      final migrationResult = await DataMigrationService.performMigrationIfNeeded();
      expect(migrationResult.success, isTrue);
      
      // Health check should pass after successful migration
      final healthResult = await AppHealthService.performStartupHealthCheck();
      expect(healthResult.storageAccessible, isTrue);
    });

    test('error handling and offline mode integration', () async {
      // Test that offline mode activates when error handling detects issues
      final networkAvailable = await ErrorHandlerService.isNetworkAvailable();
      final shouldUseOffline = await OfflineModeService.shouldUseOfflineMode();
      
      // If network is unavailable, should use offline mode
      if (!networkAvailable) {
        expect(shouldUseOffline, isTrue);
      }
    });
  });
}

/// Test utilities
class TestUtils {
  static User createTestUser({
    String username = 'testuser',
    String gender = 'male',
    List<String>? customCategories,
    Map<String, String>? assignedCoaches,
  }) {
    return User.blank(
      id: 'test_${username}_id',
      username: username,
    ).copyWith(
      gender: gender,
      customCategories: customCategories ?? ['Health', 'Wealth'],
      assignedCoaches: assignedCoaches,
    );
  }

  static Future<void> waitForAsync([int milliseconds = 100]) async {
    await Future.delayed(Duration(milliseconds: milliseconds));
  }

  static void expectValidResponse(String response) {
    expect(response, isNotEmpty);
    expect(response.length, greaterThan(10));
    expect(response, isNot(contains('ERROR')));
    expect(response, isNot(contains('FAILED')));
  }

  static void expectOfflineResponse(String response, String username) {
    expectValidResponse(response);
    expect(response, contains(username));
    expect(response.toLowerCase(), anyOf([
      contains('offline'),
      contains('technical'),
      contains('difficulty'),
      contains('issue'),
    ]));
  }
}
