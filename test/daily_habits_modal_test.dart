// 📁 test/daily_habits_modal_test.dart

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:maxed_out_life/widgets/daily_habits_intro_modal.dart';

void main() {
  group('Daily Habits Intro Modal Tests', () {
    testWidgets('Modal displays all required content', (WidgetTester tester) async {
      bool continuePressed = false;
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DailyHabitsIntroModal(
              onContinue: () {
                continuePressed = true;
              },
            ),
          ),
        ),
      );

      // Test header
      expect(find.text('DAILY HABITS ONBOARDING PAGE'), findsOneWidget);
      
      // Test key content
      expect(find.textContaining('compounding discipline'), findsOneWidget);
      expect(find.textContaining('1% per day'), findsOneWidget);
      expect(find.textContaining('37.78x'), findsOneWidget);
      
      // Test quotes
      expect(find.textContaining('<PERSON>'), findsOneWidget);
      expect(find.textContaining('<PERSON>'), findsOneWidget);
      expect(find.textContaining('<PERSON>'), findsOneWidget);
      expect(find.textContaining('Socrates'), findsOneWidget);
      
      // Test key concepts
      expect(find.textContaining('10 core habits'), findsOneWidget);
      expect(find.textContaining('non-negotiables'), findsAtLeastNWidgets(1));
      expect(find.textContaining('MXD Out Life'), findsOneWidget);
      
      // Test button
      expect(find.text('LOCK IN MY 10 HABITS'), findsOneWidget);

      // Scroll to the button to make it visible
      await tester.scrollUntilVisible(
        find.text('LOCK IN MY 10 HABITS'),
        500.0,
      );
      await tester.pump();

      // Test button functionality
      await tester.tap(find.text('LOCK IN MY 10 HABITS'));
      await tester.pump();

      expect(continuePressed, true);
    });

    testWidgets('Modal has proper styling and layout', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DailyHabitsIntroModal(
              onContinue: () {},
            ),
          ),
        ),
      );

      // Check that the modal container exists
      expect(find.byType(Dialog), findsOneWidget);
      expect(find.byType(SingleChildScrollView), findsOneWidget);
      
      // Check that progress placeholders exist
      expect(find.text('[PROGRESS.PNG]'), findsOneWidget);
      expect(find.text('[PROGRESS2.PNG]'), findsOneWidget);
      expect(find.text('[PROGRESS3.PNG]'), findsOneWidget);
      
      // Check that the button is styled correctly
      final buttonFinder = find.byType(ElevatedButton);
      expect(buttonFinder, findsOneWidget);
      
      final button = tester.widget<ElevatedButton>(buttonFinder);
      expect(button.style?.backgroundColor?.resolve({}), Colors.cyanAccent);
    });

    testWidgets('Modal content is scrollable', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DailyHabitsIntroModal(
              onContinue: () {},
            ),
          ),
        ),
      );

      // The modal should be scrollable due to its length
      expect(find.byType(SingleChildScrollView), findsOneWidget);
      
      // Test that we can scroll to see content at the bottom
      expect(find.text('LOCK IN MY 10 HABITS'), findsOneWidget);
      
      // Scroll to ensure all content is accessible
      await tester.drag(find.byType(SingleChildScrollView), const Offset(0, -500));
      await tester.pump();
      
      // Should still find the button after scrolling
      expect(find.text('LOCK IN MY 10 HABITS'), findsOneWidget);
    });
  });
}

/// Manual test helper - run this to see the modal in action
class DailyHabitsModalTestApp extends StatelessWidget {
  const DailyHabitsModalTestApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Daily Habits Modal Test',
      theme: ThemeData.dark(),
      home: Scaffold(
        backgroundColor: Colors.black,
        appBar: AppBar(
          title: const Text('Daily Habits Modal Test'),
          backgroundColor: Colors.grey[900],
        ),
        body: Center(
          child: ElevatedButton(
            onPressed: () {
              showDialog(
                context: context,
                barrierDismissible: false,
                builder: (context) => DailyHabitsIntroModal(
                  onContinue: () {
                    Navigator.of(context).pop();
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Modal completed successfully!'),
                        backgroundColor: Colors.green,
                      ),
                    );
                  },
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.cyanAccent,
              foregroundColor: Colors.black,
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
            ),
            child: const Text(
              'SHOW DAILY HABITS MODAL',
              style: TextStyle(
                fontFamily: 'Pirulen',
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
