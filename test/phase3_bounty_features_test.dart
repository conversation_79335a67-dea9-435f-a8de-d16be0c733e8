import 'package:flutter_test/flutter_test.dart';
import 'package:maxed_out_life/models/user_model.dart';
import 'package:maxed_out_life/models/bounty_model.dart';
import 'package:maxed_out_life/widgets/enhanced_cashed_bounties_modal.dart';
import 'package:maxed_out_life/services/bounty_notification_service.dart';
import 'package:maxed_out_life/services/advanced_bounty_generator.dart';
import 'package:maxed_out_life/services/bounty_analytics_service.dart';

void main() {
  group('Phase 3: Enhanced Bounty Features Tests', () {
    late User testUser;
    late List<CashedBounty> testCashedBounties;

    setUpAll(() {
      TestWidgetsFlutterBinding.ensureInitialized();
    });

    setUp(() {
      testUser = User.blank(id: 'test-user', username: 'TestUser')
          .copyWith(
            exp: 1500,
            categories: {
              'Health': 600,
              'Wealth': 300,
              'Purpose': 400,
              'Connection': 200,
            },
            gender: 'male',
            createdAt: DateTime.now().subtract(Duration(days: 30)),
          );

      // Create test cashed bounties
      testCashedBounties = [
        CashedBounty(
          bounty: BountyModel(
            id: 'test-1',
            description: 'Complete a 30-minute workout',
            categories: ['Health'],
            expPerCategory: {'Health': 50},
            difficulty: 'medium',
            isEpic: false,
          ),
          photoPath: '/test/photo1.jpg',
          completedAt: DateTime.now().subtract(Duration(days: 1)),
        ),
        CashedBounty(
          bounty: BountyModel(
            id: 'test-2',
            description: 'Read a book for 1 hour',
            categories: ['Purpose'],
            expPerCategory: {'Purpose': 40},
            difficulty: 'easy',
            isEpic: false,
          ),
          photoPath: '/test/photo2.jpg',
          completedAt: DateTime.now().subtract(Duration(days: 2)),
        ),
        CashedBounty(
          bounty: BountyModel(
            id: 'test-3',
            description: 'Epic challenge: Run a marathon',
            categories: ['Health', 'Purpose'],
            expPerCategory: {'Health': 100, 'Purpose': 100},
            difficulty: 'epic',
            isEpic: true,
          ),
          photoPath: '/test/photo3.jpg',
          completedAt: DateTime.now().subtract(Duration(days: 3)),
        ),
      ];
    });

    group('Bounty Notification Service Tests', () {
      test('Should send notification based on timing rules', () {
        // Test notification timing
        expect(BountyNotificationService.shouldSendNotification(testUser), isA<bool>());
      });

      test('Generates personalized bounty suggestions', () {
        final bounty = BountyNotificationService.generatePersonalizedBounty(testUser);
        
        if (bounty != null) {
          expect(bounty.id, isNotEmpty);
          expect(bounty.description, isNotEmpty);
          expect(bounty.categories, isNotEmpty);
          expect(bounty.expPerCategory, isNotEmpty);
        }
      });

      test('Generates appropriate notification messages', () {
        final testBounty = BountyModel(
          id: 'msg-test',
          description: 'Test bounty for messaging',
          categories: ['Health'],
          expPerCategory: {'Health': 30},
          difficulty: 'medium',
          isEpic: false,
        );

        final message = BountyNotificationService.generateNotificationMessage(testUser, testBounty);
        
        expect(message, isNotEmpty);
        expect(message, contains(testUser.username));
        expect(message, contains(testBounty.description));
      });

      test('Schedules next notification correctly', () {
        final nextNotification = BountyNotificationService.scheduleNextNotification();
        final now = DateTime.now();

        expect(nextNotification.isAfter(now), isTrue);
        expect(nextNotification.difference(now).inDays, greaterThanOrEqualTo(2));
      });

      test('Epic bounty alerts work for qualified users', () {
        // High-level user should be eligible for epic alerts
        final highLevelUser = testUser.copyWith(exp: 5000);
        final shouldAlert = BountyNotificationService.shouldSendEpicBountyAlert(highLevelUser);
        expect(shouldAlert, isA<bool>());

        // Low-level user should not get epic alerts
        final lowLevelUser = testUser.copyWith(exp: 100);
        final shouldNotAlert = BountyNotificationService.shouldSendEpicBountyAlert(lowLevelUser);
        expect(shouldNotAlert, isFalse);
      });
    });

    group('Advanced Bounty Generator Tests', () {
      test('Generates appropriate daily bounty for user level', () {
        final completedIds = ['completed-1', 'completed-2'];
        final bounty = AdvancedBountyGenerator.generateDailyBounty(testUser, completedIds);
        
        if (bounty != null) {
          expect(bounty.id, isNotEmpty);
          expect(completedIds, isNot(contains(bounty.id)));
          
          // Should be appropriate difficulty for user level
          expect(['easy', 'medium', 'hard', 'epic'], contains(bounty.difficulty));
        }
      });

      test('Generates themed bounties correctly', () {
        final newYearBounties = AdvancedBountyGenerator.generateThemedBounties('new_year');
        expect(newYearBounties, isNotEmpty);
        
        for (final bounty in newYearBounties) {
          expect(bounty.id, contains('ny_'));
          expect(bounty.description, isNotEmpty);
          expect(bounty.categories, isNotEmpty);
        }

        final summerBounties = AdvancedBountyGenerator.generateThemedBounties('summer');
        expect(summerBounties, isNotEmpty);
        
        final wellnessBounties = AdvancedBountyGenerator.generateThemedBounties('wellness');
        expect(wellnessBounties, isNotEmpty);
      });

      test('Handles empty available bounties gracefully', () {
        final allCompletedIds = ['b1', 'b2', 'b3', 'b4', 'b5']; // Assume all bounties completed
        final bounty = AdvancedBountyGenerator.generateDailyBounty(testUser, allCompletedIds);
        
        // Should either return null or a bounty (depending on implementation)
        if (bounty != null) {
          expect(bounty.id, isNotEmpty);
        }
      });
    });

    group('Bounty Analytics Service Tests', () {
      test('Calculates comprehensive bounty statistics', () {
        final stats = BountyAnalyticsService.calculateBountyStats(testUser, testCashedBounties);
        
        expect(stats.totalBountiesCompleted, equals(3));
        expect(stats.totalExpFromBounties, equals(290)); // 50 + 40 + 200
        expect(stats.categoryBreakdown, isNotEmpty);
        expect(stats.difficultyBreakdown, isNotEmpty);
        expect(stats.epicBountiesCompleted, equals(1));
        expect(stats.favoriteCategory, isNotEmpty);
        expect(stats.personalRecords, isNotEmpty);
      });

      test('Calculates streaks correctly', () {
        // Create consecutive bounties for streak testing
        final consecutiveBounties = [
          CashedBounty(
            bounty: BountyModel(
              id: 'streak-1',
              description: 'Day 1',
              categories: ['Health'],
              expPerCategory: {'Health': 30},
              difficulty: 'easy',
              isEpic: false,
            ),
            photoPath: '/test/streak1.jpg',
            completedAt: DateTime.now().subtract(Duration(days: 2)),
          ),
          CashedBounty(
            bounty: BountyModel(
              id: 'streak-2',
              description: 'Day 2',
              categories: ['Health'],
              expPerCategory: {'Health': 30},
              difficulty: 'easy',
              isEpic: false,
            ),
            photoPath: '/test/streak2.jpg',
            completedAt: DateTime.now().subtract(Duration(days: 1)),
          ),
          CashedBounty(
            bounty: BountyModel(
              id: 'streak-3',
              description: 'Day 3',
              categories: ['Health'],
              expPerCategory: {'Health': 30},
              difficulty: 'easy',
              isEpic: false,
            ),
            photoPath: '/test/streak3.jpg',
            completedAt: DateTime.now(),
          ),
        ];

        final stats = BountyAnalyticsService.calculateBountyStats(testUser, consecutiveBounties);
        expect(stats.currentStreak, greaterThanOrEqualTo(1));
        expect(stats.longestStreak, greaterThanOrEqualTo(1));
      });

      test('Generates meaningful insights', () {
        final stats = BountyAnalyticsService.calculateBountyStats(testUser, testCashedBounties);
        final insights = BountyAnalyticsService.generateInsights(stats);
        
        expect(insights, isNotEmpty);
        
        for (final insight in insights) {
          expect(insight, isNotEmpty);
          // Should contain emoji and meaningful text
          expect(insight.length, greaterThan(10));
        }
      });

      test('Handles empty bounty list gracefully', () {
        final stats = BountyAnalyticsService.calculateBountyStats(testUser, []);
        
        expect(stats.totalBountiesCompleted, equals(0));
        expect(stats.totalExpFromBounties, equals(0));
        expect(stats.currentStreak, equals(0));
        expect(stats.longestStreak, equals(0));
        expect(stats.epicBountiesCompleted, equals(0));
      });

      test('Category breakdown is accurate', () {
        final stats = BountyAnalyticsService.calculateBountyStats(testUser, testCashedBounties);
        
        expect(stats.categoryBreakdown['Health'], equals(2)); // 2 bounties with Health
        expect(stats.categoryBreakdown['Purpose'], equals(2)); // 2 bounties with Purpose
        expect(stats.categoryBreakdown['Wealth'], isNull); // No Wealth bounties
      });

      test('Difficulty breakdown is accurate', () {
        final stats = BountyAnalyticsService.calculateBountyStats(testUser, testCashedBounties);
        
        expect(stats.difficultyBreakdown['easy'], equals(1));
        expect(stats.difficultyBreakdown['medium'], equals(1));
        expect(stats.difficultyBreakdown['epic'], equals(1));
        expect(stats.difficultyBreakdown['hard'], isNull);
      });
    });

    group('Enhanced Cashed Bounties Modal Tests', () {
      test('CashedBounty model works correctly', () {
        final cashedBounty = testCashedBounties.first;
        
        expect(cashedBounty.bounty, isA<BountyModel>());
        expect(cashedBounty.photoPath, isNotEmpty);
        expect(cashedBounty.completedAt, isA<DateTime>());
      });

      test('Multiple cashed bounties can be created', () {
        expect(testCashedBounties.length, equals(3));
        
        for (final bounty in testCashedBounties) {
          expect(bounty.bounty.id, isNotEmpty);
          expect(bounty.photoPath, isNotEmpty);
          expect(bounty.completedAt, isA<DateTime>());
        }
      });
    });

    group('Integration Tests', () {
      test('Notification service integrates with bounty generator', () {
        final personalizedBounty = BountyNotificationService.generatePersonalizedBounty(testUser);
        
        if (personalizedBounty != null) {
          final message = BountyNotificationService.generateNotificationMessage(testUser, personalizedBounty);
          expect(message, contains(personalizedBounty.description));
        }
      });

      test('Analytics service works with generated bounties', () {
        final generatedBounty = AdvancedBountyGenerator.generateDailyBounty(testUser, []);
        
        if (generatedBounty != null) {
          final newCashedBounty = CashedBounty(
            bounty: generatedBounty,
            photoPath: '/test/generated.jpg',
            completedAt: DateTime.now(),
          );
          
          final updatedBounties = [...testCashedBounties, newCashedBounty];
          final stats = BountyAnalyticsService.calculateBountyStats(testUser, updatedBounties);
          
          expect(stats.totalBountiesCompleted, equals(4));
        }
      });
    });
  });
}
