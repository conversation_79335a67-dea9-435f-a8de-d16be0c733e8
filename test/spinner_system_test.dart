import 'package:flutter_test/flutter_test.dart';
import 'package:maxed_out_life/models/user_model.dart';
import 'package:maxed_out_life/services/reward_engine.dart';

void main() {
  group('Spinner System Tests', () {
    late User testUser;

    setUpAll(() {
      TestWidgetsFlutterBinding.ensureInitialized();
    });

    setUp(() {
      testUser = User.blank(id: 'test-user', username: 'TestUser');
    });

    test('User starts with zero daily EXP and no spinner plays', () {
      expect(testUser.dailyExpTotal, equals(0));
      expect(testUser.availableSpinnerPlays, equals(0));
      expect(testUser.activeBonusCategories, isEmpty);
    });

    test('Adding EXP updates daily total and unlocks spinner at 40 EXP', () {
      // Add 30 EXP - should not unlock spinner
      var updatedUser = testUser.copyWithAddedExp('Health', 30, 'Workout');
      expect(updatedUser.dailyExpTotal, equals(30));
      expect(updatedUser.availableSpinnerPlays, equals(0));

      // Add 15 more EXP - should unlock first spinner play (45 total)
      updatedUser = updatedUser.copyWithAddedExp('Health', 15, 'Run');
      expect(updatedUser.dailyExpTotal, equals(45));
      expect(updatedUser.availableSpinnerPlays, equals(1));

      // Add 20 more EXP - should unlock second spinner play (65 total)
      updatedUser = updatedUser.copyWithAddedExp('Wealth', 20, 'Work');
      expect(updatedUser.dailyExpTotal, equals(65));
      expect(updatedUser.availableSpinnerPlays, equals(2));
    });

    test('Daily reset clears EXP and spinner plays', () {
      // Add EXP and unlock spinner
      var updatedUser = testUser.copyWithAddedExp('Health', 50, 'Workout');
      expect(updatedUser.dailyExpTotal, equals(50));
      expect(updatedUser.availableSpinnerPlays, equals(1));

      // Simulate daily reset
      var resetUser = RewardEngine.instance.resetDailyExp(updatedUser);
      expect(resetUser.dailyExpTotal, equals(0));
      expect(resetUser.availableSpinnerPlays, equals(0));
      expect(resetUser.activeBonusCategories, isEmpty);
    });

    test('Spinner mechanics work correctly', () {
      // Create user with spinner plays
      var userWithSpins = testUser.copyWith(
        dailyExpTotal: 50,
        availableSpinnerPlays: 2,
      );

      // Test can spin
      expect(RewardEngine.instance.canSpin(userWithSpins), isTrue);

      // Test spinner result structure
      var spinResult = RewardEngine.instance.spinWheel(userWithSpins);
      expect(spinResult, isA<SpinResult>());
      expect(spinResult.message, isNotEmpty);

      // Test using spinner play
      var updatedUser = RewardEngine.instance.useSpinnerPlay(userWithSpins, spinResult);
      expect(updatedUser.availableSpinnerPlays, equals(1)); // One less play

      if (spinResult.success && spinResult.wonCategory != null) {
        expect(updatedUser.activeBonusCategories, contains(spinResult.wonCategory));
      }
    });

    test('EXP to next spinner calculation', () {
      expect(RewardEngine.instance.getExpToNextSpinner(0), equals(40)); // Need 40 to unlock first
      expect(RewardEngine.instance.getExpToNextSpinner(30), equals(10)); // Need 10 more
      expect(RewardEngine.instance.getExpToNextSpinner(40), equals(20)); // Need 20 for next (60 total)
      expect(RewardEngine.instance.getExpToNextSpinner(50), equals(10)); // Need 10 more for 60
      expect(RewardEngine.instance.getExpToNextSpinner(60), equals(20)); // Need 20 for next (80 total)
    });

    test('Spinner progress calculation', () {
      expect(RewardEngine.instance.getSpinnerProgress(0), equals(0.0));
      expect(RewardEngine.instance.getSpinnerProgress(20), equals(0.5)); // 20/40 = 0.5
      expect(RewardEngine.instance.getSpinnerProgress(40), equals(0.0)); // Reset at unlock
      expect(RewardEngine.instance.getSpinnerProgress(50), equals(0.5)); // 10/20 = 0.5
    });

    test('Bonus EXP system works with active categories', () {
      // Create user with active bonus category
      var userWithBonus = testUser.copyWith(
        activeBonusCategories: ['Health'],
      );

      // Add EXP to Health category - might get bonus
      var updatedUser = userWithBonus.copyWithAddedExp('Health', 10, 'Workout');

      // Check if bonus was applied (note: bonus is random, so we check structure)
      expect(updatedUser.exp, greaterThanOrEqualTo(10)); // At least base EXP
      expect(updatedUser.categories['Health'], greaterThanOrEqualTo(10));

      // Add EXP to non-bonus category - should not get bonus
      updatedUser = userWithBonus.copyWithAddedExp('Wealth', 10, 'Work');
      expect(updatedUser.categories['Wealth'], equals(10)); // Exact amount, no bonus
    });

    test('CRITICAL: Spinner bonus EXP is added to user total EXP', () {
      // Create user with spinner plays
      var userWithSpins = testUser.copyWith(
        dailyExpTotal: 50,
        availableSpinnerPlays: 1,
        exp: 100, // Starting total EXP
        categories: {'Health': 50, 'Wealth': 30, 'Purpose': 20, 'Connection': 0},
      );

      final initialTotalExp = userWithSpins.exp;
      final initialHealthExp = userWithSpins.categories['Health'] ?? 0;

      // Spin the wheel
      var spinResult = RewardEngine.instance.spinWheel(userWithSpins);
      var updatedUser = RewardEngine.instance.useSpinnerPlay(userWithSpins, spinResult);

      // Verify spinner play was consumed
      expect(updatedUser.availableSpinnerPlays, equals(0));

      if (spinResult.success && spinResult.bonusExp > 0) {
        // CRITICAL: Verify bonus EXP was added to total EXP
        expect(updatedUser.exp, equals(initialTotalExp + spinResult.bonusExp));

        // CRITICAL: Verify bonus EXP was added to category EXP
        final categoryExp = updatedUser.categories[spinResult.wonCategory!] ?? 0;
        if (spinResult.wonCategory == 'Health') {
          expect(categoryExp, equals(initialHealthExp + spinResult.bonusExp));
        }

        // CRITICAL: Verify bonus EXP was added to daily total
        expect(updatedUser.dailyExpTotal, equals(50 + spinResult.bonusExp));

        // Verify bonus category was added
        expect(updatedUser.activeBonusCategories, contains(spinResult.wonCategory));

        print('✅ SPINNER BONUS EXP VERIFICATION PASSED:');
        print('   Won Category: ${spinResult.wonCategory}');
        print('   Bonus EXP: ${spinResult.bonusExp}');
        print('   Total EXP: $initialTotalExp -> ${updatedUser.exp}');
        print('   Daily EXP: 50 -> ${updatedUser.dailyExpTotal}');
      } else {
        // Loss case - no EXP should be added
        expect(updatedUser.exp, equals(initialTotalExp));
        expect(updatedUser.dailyExpTotal, equals(50));
        print('✅ SPINNER LOSS VERIFICATION PASSED: No EXP added');
      }
    });

    test('Multiple bonus categories stack correctly', () {
      var userWithMultipleBonus = testUser.copyWith(
        activeBonusCategories: ['Health', 'Wealth', 'Purpose'],
      );

      expect(userWithMultipleBonus.activeBonusCategories.length, equals(3));
      expect(RewardEngine.instance.hasBonusChance(userWithMultipleBonus, 'Health'), isTrue);
      expect(RewardEngine.instance.hasBonusChance(userWithMultipleBonus, 'Wealth'), isTrue);
      expect(RewardEngine.instance.hasBonusChance(userWithMultipleBonus, 'Purpose'), isTrue);
      expect(RewardEngine.instance.hasBonusChance(userWithMultipleBonus, 'Connection'), isFalse);
    });
  });
}
