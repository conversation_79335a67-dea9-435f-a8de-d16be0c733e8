// test/spiritual_validation_logic_test.dart

import 'package:flutter_test/flutter_test.dart';
import 'package:maxed_out_life/services/spiritual_safety_validator.dart';
import 'package:maxed_out_life/services/user_spiritual_profile_service.dart';

/// CRITICAL: Tests for spiritual content validation logic
/// 
/// These tests ensure our content analysis works correctly.
/// ZERO TOLERANCE for religious content errors.
void main() {
  group('🛡️ Spiritual Content Analysis Tests', () {
    
    test('Should detect Christian content correctly', () {
      const content = 'As Jesus said in Matthew 5:16, let your light shine before others.';
      final analysis = SpiritualSafetyValidator.analyzeContentForReligiousReferences(content);
      
      expect(analysis.hasReligiousContent, true);
      expect(analysis.detectedReferences, contains(ReligiousContentType.christianBibleVerse));
      expect(analysis.riskLevel, RiskLevel.low);
    });
    
    test('Should detect prayer references correctly', () {
      const content = 'Let us pray for guidance in this difficult time.';
      final analysis = SpiritualSafetyValidator.analyzeContentForReligiousReferences(content);
      
      expect(analysis.hasReligiousContent, true);
      expect(analysis.detectedReferences, contains(ReligiousContentType.christianPrayer));
    });
    
    test('Should detect Catholic content correctly', () {
      const content = '<PERSON> <PERSON> once wrote about the importance of faith.';
      final analysis = SpiritualSafetyValidator.analyzeContentForReligiousReferences(content);
      
      expect(analysis.hasReligiousContent, true);
      expect(analysis.detectedReferences, contains(ReligiousContentType.catholicSaintWisdom));
    });
    
    test('Should detect Jewish content correctly', () {
      const content = 'The Torah teaches us about wisdom and understanding.';
      final analysis = SpiritualSafetyValidator.analyzeContentForReligiousReferences(content);
      
      expect(analysis.hasReligiousContent, true);
      expect(analysis.detectedReferences, contains(ReligiousContentType.jewishWisdom));
    });
    
    test('Should detect Islamic content correctly', () {
      const content = 'The Quran speaks of compassion and mercy.';
      final analysis = SpiritualSafetyValidator.analyzeContentForReligiousReferences(content);
      
      expect(analysis.hasReligiousContent, true);
      expect(analysis.detectedReferences, contains(ReligiousContentType.islamicWisdom));
    });
    
    test('Should detect Buddhist content correctly', () {
      const content = 'Buddha taught about the path to enlightenment.';
      final analysis = SpiritualSafetyValidator.analyzeContentForReligiousReferences(content);
      
      expect(analysis.hasReligiousContent, true);
      expect(analysis.detectedReferences, contains(ReligiousContentType.buddhistWisdom));
    });
    
    test('Should detect Hindu content correctly', () {
      const content = 'The Bhagavad Gita offers profound wisdom about dharma.';
      final analysis = SpiritualSafetyValidator.analyzeContentForReligiousReferences(content);
      
      expect(analysis.hasReligiousContent, true);
      expect(analysis.detectedReferences, contains(ReligiousContentType.hinduWisdom));
    });
    
    test('Should NOT detect religious content in secular text', () {
      const content = 'Great job on your workout today! Keep up the momentum and stay focused on your goals.';
      final analysis = SpiritualSafetyValidator.analyzeContentForReligiousReferences(content);

      expect(analysis.hasReligiousContent, false);
      expect(analysis.detectedReferences, isEmpty);
      expect(analysis.riskLevel, RiskLevel.none);
    });
    
    test('Should NOT detect religious content in stoic wisdom', () {
      const content = 'As Marcus Aurelius taught, focus on what you can control and accept what you cannot.';
      final analysis = SpiritualSafetyValidator.analyzeContentForReligiousReferences(content);
      
      expect(analysis.hasReligiousContent, false);
      expect(analysis.detectedReferences, isEmpty);
      expect(analysis.riskLevel, RiskLevel.none);
    });
    
    test('Should detect problematic cross-religious phrases', () {
      const content = 'Unlike those false prophets, Jesus is the only true way.';
      final analysis = SpiritualSafetyValidator.analyzeContentForReligiousReferences(content);
      
      expect(analysis.hasReligiousContent, true);
      expect(analysis.problematicPhrases, contains('false prophet'));
      expect(analysis.riskLevel, RiskLevel.high);
    });
    
    test('Should handle multiple religious references', () {
      const content = 'Jesus taught love, Buddha taught compassion, and the Torah speaks of wisdom.';
      final analysis = SpiritualSafetyValidator.analyzeContentForReligiousReferences(content);
      
      expect(analysis.hasReligiousContent, true);
      expect(analysis.detectedReferences.length, greaterThan(2));
      expect(analysis.riskLevel, RiskLevel.medium);
    });
    
    test('Should be case insensitive', () {
      const content = 'JESUS said to love your neighbor as yourself.';
      final analysis = SpiritualSafetyValidator.analyzeContentForReligiousReferences(content);
      
      expect(analysis.hasReligiousContent, true);
      expect(analysis.detectedReferences, contains(ReligiousContentType.christianBibleVerse));
    });
    
    test('Should detect partial word matches correctly', () {
      const content = 'The biblical principles guide our understanding.';
      final analysis = SpiritualSafetyValidator.analyzeContentForReligiousReferences(content);
      
      expect(analysis.hasReligiousContent, true);
      expect(analysis.detectedReferences, contains(ReligiousContentType.christianBibleVerse));
    });
    
    test('Should handle empty content safely', () {
      const content = '';
      final analysis = SpiritualSafetyValidator.analyzeContentForReligiousReferences(content);
      
      expect(analysis.hasReligiousContent, false);
      expect(analysis.detectedReferences, isEmpty);
      expect(analysis.riskLevel, RiskLevel.none);
    });
    
    test('Should handle content with only whitespace', () {
      const content = '   \n\t  ';
      final analysis = SpiritualSafetyValidator.analyzeContentForReligiousReferences(content);
      
      expect(analysis.hasReligiousContent, false);
      expect(analysis.detectedReferences, isEmpty);
      expect(analysis.riskLevel, RiskLevel.none);
    });
  });
  
  group('🛡️ Risk Level Calculation Tests', () {
    
    test('Should assign no risk to content without religious references', () {
      const content = 'Focus on your health and fitness goals today.';
      final analysis = SpiritualSafetyValidator.analyzeContentForReligiousReferences(content);
      
      expect(analysis.riskLevel, RiskLevel.none);
    });
    
    test('Should assign low risk to single religious reference', () {
      const content = 'Have faith in your journey.';
      final analysis = SpiritualSafetyValidator.analyzeContentForReligiousReferences(content);
      
      expect(analysis.riskLevel, RiskLevel.low);
    });
    
    test('Should assign medium risk to multiple religious references', () {
      const content = 'Jesus, Buddha, and Moses all taught about compassion.';
      final analysis = SpiritualSafetyValidator.analyzeContentForReligiousReferences(content);
      
      expect(analysis.riskLevel, RiskLevel.medium);
    });
    
    test('Should assign high risk to problematic phrases', () {
      const content = 'Those false prophets lead people astray.';
      final analysis = SpiritualSafetyValidator.analyzeContentForReligiousReferences(content);
      
      expect(analysis.riskLevel, RiskLevel.high);
    });
  });
  
  group('🛡️ Content Type Appropriateness Tests', () {
    
    test('Should allow Christian content for Christian denomination', () {
      final isAppropriate = SpiritualSafetyValidator.isReferenceAppropriate(
        ReligiousContentType.christianBibleVerse,
        SpiritualProfile(
          userId: 'test',
          denomination: SpiritualDenomination.christian,
          comfortLevel: ComfortLevel.comfortable,
          isProfileSet: true,
          lastUpdated: DateTime.now(),
        ),
        SpiritualWisdomGuidance.christian(
          allowBibleVerses: true,
          allowPrayer: true,
          allowSaintWisdom: false,
        ),
      );
      
      expect(isAppropriate, true);
    });
    
    test('Should block Christian content for Jewish denomination', () {
      final isAppropriate = SpiritualSafetyValidator.isReferenceAppropriate(
        ReligiousContentType.christianBibleVerse,
        SpiritualProfile(
          userId: 'test',
          denomination: SpiritualDenomination.jewish,
          comfortLevel: ComfortLevel.comfortable,
          isProfileSet: true,
          lastUpdated: DateTime.now(),
        ),
        SpiritualWisdomGuidance.jewish(),
      );
      
      expect(isAppropriate, false);
    });
    
    test('Should allow universal spiritual content for all denominations', () {
      final isAppropriate = SpiritualSafetyValidator.isReferenceAppropriate(
        ReligiousContentType.universalSpiritual,
        SpiritualProfile(
          userId: 'test',
          denomination: SpiritualDenomination.secular,
          comfortLevel: ComfortLevel.comfortable,
          isProfileSet: true,
          lastUpdated: DateTime.now(),
        ),
        SpiritualWisdomGuidance.secular(),
      );
      
      expect(isAppropriate, true);
    });
    
    test('Should allow stoic wisdom for all denominations', () {
      final isAppropriate = SpiritualSafetyValidator.isReferenceAppropriate(
        ReligiousContentType.stoicWisdom,
        SpiritualProfile(
          userId: 'test',
          denomination: SpiritualDenomination.muslim,
          comfortLevel: ComfortLevel.comfortable,
          isProfileSet: true,
          lastUpdated: DateTime.now(),
        ),
        SpiritualWisdomGuidance.muslim(),
      );
      
      expect(isAppropriate, true);
    });
  });
}
