name: maxed_out_life
description: "MXD - Transform your life with AI-powered coaching across Health, Wealth, Purpose, and Connection. Gamified progress tracking with superintelligent coaches."
publish_to: 'none'
version: 1.0.0+2

environment:
  sdk: ^3.8.1

dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.8
  shared_preferences: ^2.2.2
  provider: ^6.1.1
  uuid: ^4.2.1
  intl: ^0.19.0
  http: ^1.1.0
  flutter_dotenv: ^5.1.0
  audioplayers: ^6.5.0
  flutter_local_notifications: ^19.3.0
  timezone: ^0.10.1
  flutter_secure_storage: ^9.0.0
  json_annotation: ^4.8.1
  path_provider: ^2.1.1
  path: ^1.8.3
  url_launcher: ^6.2.2
  speech_to_text: ^7.1.0
  home_widget: ^0.6.0
  package_info_plus: ^8.0.0
  connectivity_plus: ^6.0.5
  # Additional dependencies found in temp_disabled_files
  meta: ^1.16.0
  equatable: ^2.0.7
  file_picker: ^8.1.4
  image_picker: ^1.1.2
  fl_chart: ^0.70.1
  crypto: ^3.0.6
  firebase_core: ^3.6.0
  firebase_crashlytics: ^4.1.3
  firebase_analytics: ^11.3.3
  permission_handler: ^11.3.1
  share_plus: ^7.2.2
  firebase_auth: ^5.6.2
  
dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  flutter_launcher_icons: ^0.14.3
  mockito: ^5.4.2
  integration_test:
    sdk: flutter
  test: ^1.24.0
  build_runner: ^2.4.7
  fake_async: ^1.3.1
  golden_toolkit: ^0.15.0
  patrol: ^3.0.0
  json_serializable: ^6.7.1

flutter:
  uses-material-design: true

  assets:
    - assets/sounds/
    - assets/sounds/Heimanu_Tracks/
    - assets/sounds/Gamera_Tracks/
    - assets/images/
    - assets/fonts/
    - assets/notification_icons/
    - assets/legal/
    - .env

  fonts:
    - family: Pirulen
      fonts:
        - asset: assets/fonts/Pirulen.otf
    - family: Bitsumishi
      fonts:
        - asset: assets/fonts/BITSUMISHI.TTF
    - family: Orbitron
      fonts:
        - asset: assets/fonts/Orbitron.ttf
    - family: SF-Pro
      fonts:
        - asset: assets/fonts/SF-Pro.ttf
    - family: Digital-7
      fonts:
        - asset: assets/fonts/digital-7.ttf

# App Icon Configuration
flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/images/mxd_logo_3_icon.jpg"
  min_sdk_android: 21 # android min sdk min:16, default 21
  web:
    generate: true
    image_path: "assets/images/mxd_logo_3_icon.jpg"
    background_color: "#hexcode"
    theme_color: "#hexcode"
  windows:
    generate: true
    image_path: "assets/images/mxd_logo_3_icon.jpg"
    icon_size: 48 # min:48, max:256, default: 48
  macos:
    generate: true
    image_path: "assets/images/mxd_logo_3_icon.jpg"

