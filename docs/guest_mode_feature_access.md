# Guest Mode Feature Access Plan

## Overview
This document defines which features are accessible to guest users vs requiring account creation, ensuring compliance with Apple App Store guidelines while maintaining app value.

## Feature Access Levels

### 🟢 **Guest Accessible Features** (No Account Required)
These features allow users to explore the app's value proposition without registration:

#### **App Exploration & Demo**
- **App Interface Browsing**: View the main home screen layout and design
- **Feature Overview**: See what the app offers without functional access
- **Tutorial/Onboarding**: Learn how the app works
- **Music Player**: Listen to MXD tracks (entertainment feature)
- **Static Content**: View app information, about sections

#### **Limited Demo Functionality**
- **Demo AI Coach Interaction**: Single sample conversation per category (no persistence)
- **Sample Progress Visualization**: Show example EXP bars, progress charts with demo data
- **Feature Previews**: Show what habits, bounty hunter, training tracker look like

### 🔴 **Account Required Features** (Registration Mandatory)
These features require user accounts due to data persistence and personalization:

#### **AI Coaches & Personalization**
- **Full AI Coach Access**: Personalized conversations, memory, context
- **Coach Memory**: Coaches remembering previous conversations
- **Personalized Responses**: AI responses based on user history and data

#### **Progress Tracking & Data Persistence**
- **EXP Tracking**: Earning and tracking experience points
- **Habit Management**: Creating, tracking, and completing daily habits
- **Diary Entries**: Creating and saving personal diary entries
- **North Star Quest**: Setting and tracking long-term goals
- **Streak Tracking**: Daily habit streaks and progress
- **7-Day Progress**: Personal progress analytics

#### **Gamification & Rewards**
- **Bounty Hunter**: Earning and spinning for rewards
- **Training Tracker**: Logging workouts and training sessions
- **Level System**: User levels, ranks, and progression
- **Achievement System**: Unlocking achievements and milestones

#### **Data & Synchronization**
- **Cloud Sync**: Syncing data across devices
- **Backup & Restore**: Data backup and recovery
- **Profile Management**: User settings and preferences
- **Email Verification**: Account security features

## Implementation Strategy

### **Guest User Model**
- Create a temporary `GuestUser` that mimics `User` structure
- Use session-only storage (no persistence)
- Clear data when app closes or user signs up

### **Feature Gating**
- Add `isGuest` checks throughout the app
- Show "Sign Up to Continue" prompts for account-required features
- Provide seamless upgrade path from guest to full user

### **UI/UX Considerations**
- Clear indicators of guest vs authenticated state
- Prominent but non-intrusive sign-up prompts
- Preserve guest session data during account creation
- Smooth transition between guest and authenticated experiences

## Apple App Store Compliance

### **Browsable Content**
✅ Users can explore the app interface and see what it offers
✅ Music player provides entertainment value without account
✅ Demo interactions show app capabilities
✅ Tutorial explains how the app works

### **Account Requirements**
✅ Only features requiring data persistence need accounts
✅ AI coaches require accounts for personalization and memory
✅ Progress tracking requires accounts for data storage
✅ Clear value proposition before requiring registration

## Next Steps
1. Create `GuestUser` model and session management
2. Implement guest mode navigation in `AuthScreen`
3. Add feature gating throughout the app
4. Create guest-to-user conversion flow
5. Test compliance with Apple guidelines
