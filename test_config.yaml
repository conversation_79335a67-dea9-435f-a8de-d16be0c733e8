# Test Configuration for Maxed Out Life App
# 
# This file defines test settings, coverage requirements,
# and automation configurations for the testing framework.

# Test Coverage Requirements
coverage:
  minimum_coverage: 80.0
  exclude_patterns:
    - "**/*.g.dart"
    - "**/*.freezed.dart"
    - "**/*.mocks.dart"
    - "**/generated/**"
    - "**/l10n/**"
  
  # Coverage targets by component
  targets:
    services: 90.0
    models: 85.0
    widgets: 75.0
    screens: 70.0
    utils: 95.0

# Test Execution Settings
execution:
  timeout: 30s
  retry_count: 3
  parallel_execution: true
  max_workers: 4
  
  # Test environment settings
  environment:
    FLUTTER_TEST: true
    TEST_ENV: testing
    LOG_LEVEL: error

# Test Categories
categories:
  unit:
    pattern: "test/unit/**/*_test.dart"
    timeout: 10s
    coverage_required: true
    
  widget:
    pattern: "test/widget/**/*_test.dart"
    timeout: 20s
    coverage_required: true
    
  integration:
    pattern: "integration_test/**/*_test.dart"
    timeout: 60s
    coverage_required: false
    
  performance:
    pattern: "test/performance/**/*_test.dart"
    timeout: 30s
    coverage_required: false

# Test Data Configuration
test_data:
  mock_users:
    - username: "testuser1"
      email: "<EMAIL>"
      level: 5
      total_exp: 1250
    - username: "testuser2"
      email: "<EMAIL>"
      level: 10
      total_exp: 5000
      
  mock_habits:
    - name: "Morning Exercise"
      category: "Health"
      streak: 7
    - name: "Read Daily"
      category: "Purpose"
      streak: 14
      
  mock_bounties:
    - description: "Complete a workout"
      difficulty: "medium"
      categories: ["Health"]
      exp_reward: 50

# Performance Test Thresholds
performance:
  build_time_ms: 500
  scroll_time_ms: 1000
  animation_frame_ms: 20
  memory_usage_mb: 200
  cache_hit_rate: 0.8

# Quality Gates
quality_gates:
  # Code quality requirements
  code_quality:
    max_complexity: 10
    max_lines_per_function: 50
    max_parameters: 5
    
  # Performance requirements
  performance:
    max_build_time: 500ms
    max_scroll_time: 1000ms
    min_fps: 55
    max_memory: 200MB
    
  # Test requirements
  testing:
    min_coverage: 80%
    max_test_time: 30s
    required_test_types:
      - unit
      - widget
      - integration

# Reporting Configuration
reporting:
  formats:
    - html
    - json
    - lcov
    
  output_directory: "test_reports"
  
  # Report sections
  sections:
    - summary
    - coverage
    - performance
    - quality_metrics
    - failed_tests
    
  # Notification settings
  notifications:
    on_failure: true
    on_success: false
    channels:
      - console
      - file

# CI/CD Integration
ci_cd:
  # GitHub Actions
  github_actions:
    workflow_file: ".github/workflows/test.yml"
    trigger_events:
      - push
      - pull_request
    
  # Test stages
  stages:
    - name: "Unit Tests"
      command: "flutter test test/unit"
      required: true
      
    - name: "Widget Tests"
      command: "flutter test test/widget"
      required: true
      
    - name: "Integration Tests"
      command: "flutter test integration_test"
      required: false
      
    - name: "Performance Tests"
      command: "flutter test test/performance"
      required: false
      
    - name: "Coverage Report"
      command: "flutter test --coverage"
      required: true

# Test Utilities Configuration
utilities:
  # Mock data generation
  mock_generation:
    enabled: true
    seed: 12345
    
  # Test fixtures
  fixtures:
    directory: "test/fixtures"
    auto_load: true
    
  # Golden file testing
  golden_files:
    enabled: true
    directory: "test/goldens"
    threshold: 0.01

# Platform-specific Settings
platforms:
  android:
    min_sdk: 21
    target_sdk: 34
    test_devices:
      - "Pixel_4_API_30"
      - "Pixel_6_API_33"
      
  ios:
    min_version: "12.0"
    test_devices:
      - "iPhone_12"
      - "iPhone_14_Pro"
      
  web:
    browsers:
      - chrome
      - firefox
      
  desktop:
    platforms:
      - windows
      - macos
      - linux

# Test Automation Rules
automation:
  # Auto-retry flaky tests
  retry_flaky_tests: true
  max_retries: 3
  
  # Parallel execution
  parallel_tests: true
  max_parallel: 4
  
  # Test selection
  smart_selection:
    enabled: true
    changed_files_only: false
    
  # Cleanup
  auto_cleanup: true
  cleanup_after_run: true

# Debug and Development
debug:
  verbose_output: false
  save_screenshots: true
  save_logs: true
  
  # Test debugging
  debug_mode: false
  break_on_failure: false
  
  # Performance profiling
  profile_tests: false
  memory_profiling: false

# Custom Test Commands
commands:
  # Quick test suite
  quick:
    description: "Run quick test suite"
    command: "flutter test test/unit test/widget --reporter=compact"
    
  # Full test suite
  full:
    description: "Run full test suite with coverage"
    command: "flutter test --coverage && genhtml coverage/lcov.info -o coverage/html"
    
  # Performance tests only
  performance:
    description: "Run performance tests"
    command: "flutter test test/performance --reporter=json"
    
  # Integration tests only
  integration:
    description: "Run integration tests"
    command: "flutter test integration_test"

# Test Maintenance
maintenance:
  # Automatic test updates
  auto_update_mocks: true
  auto_update_goldens: false
  
  # Test cleanup
  remove_obsolete_tests: false
  archive_old_reports: true
  max_report_age_days: 30
  
  # Test optimization
  optimize_test_order: true
  cache_test_results: true
