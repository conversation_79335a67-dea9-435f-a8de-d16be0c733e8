import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'package:maxed_out_life/main.dart' as app;
import 'package:maxed_out_life/design_system/components/app_button.dart';

/// Integration tests for the Maxed Out Life app.
/// 
/// These tests verify the complete user flows and interactions
/// across the entire application, ensuring all components work
/// together correctly.
/// 
/// Run with:
/// ```bash
/// flutter test integration_test/app_test.dart
/// ```
void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('App Integration Tests', () {
    setUp(() async {
      // Clear any existing data before each test
      SharedPreferences.setMockInitialValues({});
    });

    testWidgets('app should launch successfully', (tester) async {
      // Arrange & Act
      app.main();
      await tester.pumpAndSettle(const Duration(seconds: 5));
      
      // Assert
      expect(find.byType(MaterialApp), findsOneWidget);
    });

    testWidgets('should navigate through onboarding flow', (tester) async {
      // Arrange
      app.main();
      await tester.pumpAndSettle(const Duration(seconds: 5));
      
      // Act & Assert - Check if auth screen is displayed
      expect(find.text('Welcome'), findsWidgets);
      
      // Navigate through onboarding if present
      if (find.text('Get Started').evaluate().isNotEmpty) {
        await tester.tap(find.text('Get Started'));
        await tester.pumpAndSettle();
      }
    });

    testWidgets('should complete user registration flow', (tester) async {
      // Arrange
      app.main();
      await tester.pumpAndSettle(const Duration(seconds: 5));
      
      // Navigate to registration if not already there
      if (find.text('Sign Up').evaluate().isNotEmpty) {
        await tester.tap(find.text('Sign Up'));
        await tester.pumpAndSettle();
      }
      
      // Act - Fill registration form
      if (find.byType(TextFormField).evaluate().isNotEmpty) {
        final textFields = find.byType(TextFormField);
        
        // Enter email
        await tester.enterText(textFields.at(0), '<EMAIL>');
        await tester.pump();
        
        // Enter username
        await tester.enterText(textFields.at(1), 'testuser');
        await tester.pump();
        
        // Enter password
        await tester.enterText(textFields.at(2), 'SecurePassword123!');
        await tester.pump();
        
        // Select gender if dropdown exists
        if (find.byType(DropdownButtonFormField).evaluate().isNotEmpty) {
          await tester.tap(find.byType(DropdownButtonFormField));
          await tester.pumpAndSettle();
          
          if (find.text('Male').evaluate().isNotEmpty) {
            await tester.tap(find.text('Male'));
            await tester.pumpAndSettle();
          }
        }
        
        // Submit form
        if (find.text('Create Account').evaluate().isNotEmpty) {
          await tester.tap(find.text('Create Account'));
          await tester.pumpAndSettle(const Duration(seconds: 3));
        }
      }
      
      // Assert - Should navigate to main app or next step
      // This depends on the actual app flow
    });

    testWidgets('should complete user login flow', (tester) async {
      // Arrange - First create a user
      app.main();
      await tester.pumpAndSettle(const Duration(seconds: 5));
      
      // Navigate to login if not already there
      if (find.text('Sign In').evaluate().isNotEmpty) {
        await tester.tap(find.text('Sign In'));
        await tester.pumpAndSettle();
      }
      
      // Act - Fill login form
      if (find.byType(TextFormField).evaluate().isNotEmpty) {
        final textFields = find.byType(TextFormField);
        
        // Enter username
        await tester.enterText(textFields.at(0), 'testuser');
        await tester.pump();
        
        // Enter password
        await tester.enterText(textFields.at(1), 'SecurePassword123!');
        await tester.pump();
        
        // Submit form
        if (find.text('Sign In').evaluate().isNotEmpty) {
          await tester.tap(find.text('Sign In').last);
          await tester.pumpAndSettle(const Duration(seconds: 3));
        }
      }
      
      // Assert - Should navigate to main app
      // This depends on the actual app flow
    });

    testWidgets('should navigate through main app sections', (tester) async {
      // Arrange - Assume user is logged in
      app.main();
      await tester.pumpAndSettle(const Duration(seconds: 5));
      
      // Skip auth if possible (this would need app-specific logic)
      
      // Act & Assert - Navigate through main sections
      if (find.byType(BottomNavigationBar).evaluate().isNotEmpty) {
        final bottomNav = find.byType(BottomNavigationBar);
        
        // Test navigation to different tabs
        final navItems = find.descendant(
          of: bottomNav,
          matching: find.byType(InkWell),
        );
        
        if (navItems.evaluate().length > 1) {
          // Navigate to second tab
          await tester.tap(navItems.at(1));
          await tester.pumpAndSettle();
          
          // Navigate to third tab if exists
          if (navItems.evaluate().length > 2) {
            await tester.tap(navItems.at(2));
            await tester.pumpAndSettle();
          }
          
          // Navigate back to first tab
          await tester.tap(navItems.at(0));
          await tester.pumpAndSettle();
        }
      }
    });

    testWidgets('should handle habit creation flow', (tester) async {
      // Arrange
      app.main();
      await tester.pumpAndSettle(const Duration(seconds: 5));
      
      // Navigate to habits section
      if (find.text('Habits').evaluate().isNotEmpty) {
        await tester.tap(find.text('Habits'));
        await tester.pumpAndSettle();
      }
      
      // Act - Create new habit
      if (find.byIcon(Icons.add).evaluate().isNotEmpty) {
        await tester.tap(find.byIcon(Icons.add));
        await tester.pumpAndSettle();
        
        // Fill habit form if present
        if (find.byType(TextFormField).evaluate().isNotEmpty) {
          final textFields = find.byType(TextFormField);
          
          // Enter habit name
          await tester.enterText(textFields.at(0), 'Test Habit');
          await tester.pump();
          
          // Enter description if field exists
          if (textFields.evaluate().length > 1) {
            await tester.enterText(textFields.at(1), 'A test habit for integration testing');
            await tester.pump();
          }
          
          // Save habit
          if (find.text('Save').evaluate().isNotEmpty) {
            await tester.tap(find.text('Save'));
            await tester.pumpAndSettle();
          }
        }
      }
      
      // Assert - Habit should appear in list
      expect(find.text('Test Habit'), findsWidgets);
    });

    testWidgets('should handle habit completion flow', (tester) async {
      // Arrange - Assume habit exists
      app.main();
      await tester.pumpAndSettle(const Duration(seconds: 5));
      
      // Navigate to habits section
      if (find.text('Habits').evaluate().isNotEmpty) {
        await tester.tap(find.text('Habits'));
        await tester.pumpAndSettle();
      }
      
      // Act - Complete a habit
      if (find.byType(Checkbox).evaluate().isNotEmpty) {
        await tester.tap(find.byType(Checkbox).first);
        await tester.pumpAndSettle();
      }
      
      // Assert - Habit should be marked as completed
      // This would depend on the UI implementation
    });

    testWidgets('should handle settings navigation', (tester) async {
      // Arrange
      app.main();
      await tester.pumpAndSettle(const Duration(seconds: 5));
      
      // Act - Navigate to settings
      if (find.byIcon(Icons.settings).evaluate().isNotEmpty) {
        await tester.tap(find.byIcon(Icons.settings));
        await tester.pumpAndSettle();
      } else if (find.text('Settings').evaluate().isNotEmpty) {
        await tester.tap(find.text('Settings'));
        await tester.pumpAndSettle();
      }
      
      // Assert - Settings screen should be displayed
      expect(find.text('Settings'), findsWidgets);
    });

    testWidgets('should handle theme switching', (tester) async {
      // Arrange
      app.main();
      await tester.pumpAndSettle(const Duration(seconds: 5));
      
      // Navigate to settings
      if (find.byIcon(Icons.settings).evaluate().isNotEmpty) {
        await tester.tap(find.byIcon(Icons.settings));
        await tester.pumpAndSettle();
      }
      
      // Act - Toggle theme if switch exists
      if (find.byType(Switch).evaluate().isNotEmpty) {
        await tester.tap(find.byType(Switch).first);
        await tester.pumpAndSettle();
      }
      
      // Assert - Theme should change
      // This would require checking the actual theme colors
    });

    testWidgets('should handle app lifecycle events', (tester) async {
      // Arrange
      app.main();
      await tester.pumpAndSettle(const Duration(seconds: 5));
      
      // Act - Simulate app going to background and returning
      await tester.binding.defaultBinaryMessenger.handlePlatformMessage(
        'flutter/lifecycle',
        const StandardMethodCodec().encodeMethodCall(
          const MethodCall('AppLifecycleState.paused'),
        ),
        (data) {},
      );
      await tester.pump();
      
      await tester.binding.defaultBinaryMessenger.handlePlatformMessage(
        'flutter/lifecycle',
        const StandardMethodCodec().encodeMethodCall(
          const MethodCall('AppLifecycleState.resumed'),
        ),
        (data) {},
      );
      await tester.pumpAndSettle();
      
      // Assert - App should still be functional
      expect(find.byType(MaterialApp), findsOneWidget);
    });

    testWidgets('should handle error states gracefully', (tester) async {
      // Arrange
      app.main();
      await tester.pumpAndSettle(const Duration(seconds: 5));
      
      // Act - Trigger error conditions (this would be app-specific)
      // For example, try to access a feature without proper data
      
      // Assert - Error should be handled gracefully
      // No crashes should occur
      expect(find.byType(MaterialApp), findsOneWidget);
    });

    testWidgets('should maintain performance under load', (tester) async {
      // Arrange
      app.main();
      await tester.pumpAndSettle(const Duration(seconds: 5));
      
      // Act - Perform multiple rapid interactions
      for (int i = 0; i < 10; i++) {
        if (find.byType(AppButton).evaluate().isNotEmpty) {
          await tester.tap(find.byType(AppButton).first);
          await tester.pump(const Duration(milliseconds: 100));
        }
      }
      
      await tester.pumpAndSettle();
      
      // Assert - App should remain responsive
      expect(find.byType(MaterialApp), findsOneWidget);
    });
  });
}
