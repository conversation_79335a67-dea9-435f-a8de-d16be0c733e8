# Contributing to Maxed Out Life

Thank you for your interest in contributing to Maxed Out Life! This document provides guidelines and information for contributors.

## 🤝 Code of Conduct

We are committed to providing a welcoming and inclusive environment for all contributors. Please read and follow our [Code of Conduct](CODE_OF_CONDUCT.md).

## 🚀 Getting Started

### Prerequisites

- Flutter SDK 3.24.5 or higher
- Dart SDK 3.5.4 or higher
- Git for version control
- A GitHub account

### Development Setup

1. **Fork the repository**
   ```bash
   git clone https://github.com/YOUR_USERNAME/maxed_out_life.git
   cd maxed_out_life
   ```

2. **Set up upstream remote**
   ```bash
   git remote add upstream https://github.com/maxedoutlife/maxed_out_life.git
   ```

3. **Install dependencies**
   ```bash
   flutter pub get
   ```

4. **Verify setup**
   ```bash
   flutter test
   flutter analyze
   ```

## 📋 How to Contribute

### Reporting Issues

Before creating an issue, please:

1. **Search existing issues** to avoid duplicates
2. **Use the issue templates** provided
3. **Provide detailed information** including:
   - Flutter/Dart version
   - Platform (iOS, Android, Web, Desktop)
   - Steps to reproduce
   - Expected vs actual behavior
   - Screenshots/logs if applicable

### Suggesting Features

For feature requests:

1. **Check existing discussions** and issues
2. **Use the feature request template**
3. **Explain the use case** and benefits
4. **Consider implementation complexity**
5. **Be open to feedback** and discussion

### Contributing Code

#### 1. Choose an Issue

- Look for issues labeled `good first issue` for beginners
- Check `help wanted` for areas needing assistance
- Comment on the issue to indicate you're working on it

#### 2. Create a Branch

```bash
git checkout -b feature/your-feature-name
# or
git checkout -b fix/issue-number-description
```

#### 3. Make Changes

Follow our coding standards:

- **Code Style**: Follow [Dart Style Guide](https://dart.dev/guides/language/effective-dart/style)
- **Documentation**: Document all public APIs with dartdoc comments
- **Testing**: Add tests for new functionality
- **Accessibility**: Ensure WCAG 2.1 AA compliance

#### 4. Test Your Changes

```bash
# Run all tests
flutter test

# Check code analysis
flutter analyze

# Test on multiple platforms
flutter run -d chrome
flutter run -d android
flutter run -d ios
```

#### 5. Commit Changes

Use conventional commit messages:

```bash
git commit -m "feat: add new habit tracking feature"
git commit -m "fix: resolve authentication issue"
git commit -m "docs: update API documentation"
git commit -m "test: add unit tests for user service"
```

#### 6. Push and Create PR

```bash
git push origin your-branch-name
```

Then create a Pull Request on GitHub.

## 📝 Coding Standards

### Dart/Flutter Guidelines

- Follow [Effective Dart](https://dart.dev/guides/language/effective-dart) guidelines
- Use `dart format` for consistent formatting
- Prefer composition over inheritance
- Use meaningful variable and function names
- Keep functions small and focused

### Code Organization

```
lib/
├── models/              # Data models
├── services/            # Business logic
├── screens/             # UI screens
├── widgets/             # Reusable components
├── utils/               # Helper functions
└── constants/           # App constants
```

### Documentation

- Document all public APIs
- Use dartdoc comments (`///`)
- Include examples for complex functions
- Keep documentation up to date

### Testing

- **Unit Tests**: Test business logic and utilities
- **Widget Tests**: Test UI components
- **Integration Tests**: Test complete user flows
- **Performance Tests**: Test app performance

Maintain minimum test coverage:
- Services: 90%
- Models: 85%
- Widgets: 75%
- Overall: 80%

## 🔍 Code Review Process

### For Contributors

- Ensure all tests pass
- Update documentation if needed
- Respond to feedback promptly
- Keep PRs focused and small

### Review Criteria

- **Functionality**: Does it work as intended?
- **Code Quality**: Is it readable and maintainable?
- **Testing**: Are there adequate tests?
- **Performance**: Does it impact app performance?
- **Accessibility**: Is it accessible to all users?
- **Security**: Are there any security concerns?

## 🏷️ Pull Request Guidelines

### PR Title

Use conventional commit format:
- `feat: add new feature`
- `fix: resolve bug`
- `docs: update documentation`
- `style: formatting changes`
- `refactor: code restructuring`
- `test: add or update tests`
- `chore: maintenance tasks`

### PR Description

Include:
- **Summary** of changes
- **Issue reference** (if applicable)
- **Testing** performed
- **Screenshots** (for UI changes)
- **Breaking changes** (if any)

### PR Checklist

- [ ] Tests pass locally
- [ ] Code follows style guidelines
- [ ] Documentation updated
- [ ] No breaking changes (or documented)
- [ ] Accessibility tested
- [ ] Performance impact considered

## 🐛 Bug Fixes

### Priority Levels

- **Critical**: Security issues, data loss, app crashes
- **High**: Major functionality broken
- **Medium**: Minor functionality issues
- **Low**: Cosmetic issues, enhancements

### Bug Fix Process

1. **Reproduce** the issue
2. **Write a test** that fails
3. **Fix** the issue
4. **Verify** the test passes
5. **Test** related functionality

## ✨ Feature Development

### Feature Lifecycle

1. **Discussion**: GitHub Discussions or Issues
2. **Design**: Technical design document
3. **Implementation**: Code development
4. **Testing**: Comprehensive testing
5. **Documentation**: Update relevant docs
6. **Review**: Code review process
7. **Deployment**: Merge and release

### Design Considerations

- **User Experience**: Intuitive and accessible
- **Performance**: Minimal impact on app performance
- **Maintainability**: Clean, documented code
- **Compatibility**: Works across all platforms
- **Security**: No security vulnerabilities

## 📚 Resources

### Documentation

- [Flutter Documentation](https://docs.flutter.dev/)
- [Dart Language Guide](https://dart.dev/guides)
- [Material Design Guidelines](https://material.io/design)
- [Accessibility Guidelines](https://www.w3.org/WAI/WCAG21/quickref/)

### Tools

- [Flutter Inspector](https://docs.flutter.dev/development/tools/flutter-inspector)
- [Dart DevTools](https://dart.dev/tools/dart-devtools)
- [VS Code Flutter Extension](https://marketplace.visualstudio.com/items?itemName=Dart-Code.flutter)
- [Android Studio Flutter Plugin](https://plugins.jetbrains.com/plugin/9212-flutter)

## 🎉 Recognition

Contributors are recognized in:
- **README.md** acknowledgments
- **CONTRIBUTORS.md** file
- **Release notes** for significant contributions
- **GitHub contributor graphs**

## 📞 Getting Help

- **GitHub Discussions**: For questions and ideas
- **GitHub Issues**: For bug reports and feature requests
- **Code Review**: For implementation feedback

## 📄 License

By contributing to Maxed Out Life, you agree that your contributions will be licensed under the MIT License.

---

Thank you for contributing to Maxed Out Life! Together, we're building something amazing. 🚀
